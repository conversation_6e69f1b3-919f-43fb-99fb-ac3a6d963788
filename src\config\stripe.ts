/**
 * Stripe configuration for HouseGoing
 */

// Stripe API configuration
export const STRIPE_CONFIG = {
  // Stripe API keys
  publishableKey: (typeof import.meta !== 'undefined' ? import.meta.env?.VITE_STRIPE_PUBLISHABLE_KEY : undefined) ||
                  process.env.STRIPE_PUBLISHABLE_KEY,
  secretKey: process.env.STRIPE_SECRET_KEY, // Secret key must be set on server-side only
  apiVersion: '2023-10-16',

  // Payment options
  currency: 'aud',
  paymentMethods: ['card', 'apple_pay', 'google_pay'],

  // Fee structure
  fees: {
    platformFee: 0.10, // 10% platform fee
    paymentProcessingFee: 0.029, // 2.9% payment processing fee
    fixedFee: 0.30, // $0.30 fixed fee per transaction
  },

  // Progressive pricing tiers (hours)
  progressivePricing: {
    tier1: { hours: 3, multiplier: 1.0 },   // 1-3 hours: standard rate
    tier2: { hours: 5, multiplier: 0.95 },  // 4-5 hours: 5% discount
    tier3: { hours: 8, multiplier: 0.90 },  // 6-8 hours: 10% discount
    tier4: { multiplier: 0.85 }             // 9+ hours: 15% discount
  },

  // Surcharges
  surcharges: {
    weekendRate: 1.2,  // 20% surcharge for weekend bookings
    holidayRate: 1.5,  // 50% surcharge for holiday bookings
    lateNightRate: 1.3 // 30% surcharge for bookings after 10pm
  }
};

export default STRIPE_CONFIG;
