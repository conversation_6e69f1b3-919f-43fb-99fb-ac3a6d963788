/**
 * Test script for NSW Party Planning Tool
 * 
 * This script tests the zoning and council detection for 182 Power Street Glendenning NSW 2761
 */

import { extractLGA<PERSON>romAddress } from './addressUtils';

// The address to test
const testAddress = '182 Power Street Glendenning NSW 2761';

// Step 1: Extract LGA from address
const extractedLGA = extractLGAFromAddress(testAddress);
console.log('Extracted LGA:', extractedLGA);

// Step 2: Geocode the address using Nominatim
async function geocodeAddress(address: string) {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(address)}&format=json&addressdetails=1&limit=1`
    );
    
    if (response.ok) {
      const data = await response.json();
      if (data && data.length > 0) {
        const coords = {
          lat: parseFloat(data[0].lat),
          lng: parseFloat(data[0].lon)
        };
        console.log('Geocoded coordinates:', coords);
        console.log('Display name:', data[0].display_name);
        
        // Extract additional information from the response
        if (data[0].address) {
          console.log('Address details:');
          console.log('  City:', data[0].address.city);
          console.log('  County:', data[0].address.county);
          console.log('  State:', data[0].address.state);
          console.log('  Postcode:', data[0].address.postcode);
        }
        
        return coords;
      }
    }
    return null;
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

// Step 3: Query NSW Planning Portal for zoning information
async function getZoningInfo(lat: number, lng: number) {
  try {
    // NSW Planning Portal API endpoint for zoning
    const url = `https://maps.six.nsw.gov.au/arcgis/rest/services/public/PlanningInformation/MapServer/10/query`;
    
    // Parameters for the API request
    const params = new URLSearchParams({
      geometry: `${lng},${lat}`, // Note: longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: 'ZONE_CODE,ZONE_NAME',
      f: 'json'
    });
    
    const response = await fetch(`${url}?${params.toString()}`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        const zoning = {
          code: data.features[0].attributes.ZONE_CODE,
          name: data.features[0].attributes.ZONE_NAME
        };
        console.log('Zoning information:', zoning);
        return zoning;
      } else {
        console.log('No zoning information found');
      }
    } else {
      console.error('Error fetching zoning information:', await response.text());
    }
    return null;
  } catch (error) {
    console.error('Error getting zoning info:', error);
    return null;
  }
}

// Step 4: Query NSW Spatial Services for LGA information
async function getLGAInfo(lat: number, lng: number) {
  try {
    // NSW Spatial Services API endpoint for LGA
    const url = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
    
    // Parameters for the API request
    const params = new URLSearchParams({
      geometry: `${lng},${lat}`, // Note: longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: 'NAME',
      f: 'json'
    });
    
    const response = await fetch(`${url}?${params.toString()}`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        const lga = data.features[0].attributes.NAME;
        console.log('LGA information:', lga);
        return lga;
      } else {
        console.log('No LGA information found');
      }
    } else {
      console.error('Error fetching LGA information:', await response.text());
    }
    return null;
  } catch (error) {
    console.error('Error getting LGA info:', error);
    return null;
  }
}

// Run the test
async function runTest() {
  console.log('Testing address:', testAddress);
  
  // Get coordinates
  const coords = await geocodeAddress(testAddress);
  
  if (coords) {
    // Get zoning information
    const zoning = await getZoningInfo(coords.lat, coords.lng);
    
    // Get LGA information
    const lga = await getLGAInfo(coords.lat, coords.lng);
    
    // Print final results
    console.log('\nFinal results:');
    console.log('Address:', testAddress);
    console.log('Coordinates:', coords);
    console.log('LGA:', lga || extractedLGA || 'Unknown');
    console.log('Zoning:', zoning ? `${zoning.code} - ${zoning.name}` : 'Unknown');
  }
}

runTest();
