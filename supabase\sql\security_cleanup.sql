-- HouseGoing Security Cleanup Script
-- This script removes all security vulnerabilities and implements secure alternatives
-- Run this in the Supabase SQL Editor

-- Step 1: Drop all insecure functions that allow arbitrary SQL execution
DROP FUNCTION IF EXISTS public.exec_sql(text);
DROP FUNCTION IF EXISTS public.pg_query(text);
DROP FUNCTION IF EXISTS public.execute_sql(text);
DROP FUNCTION IF EXISTS public.exec_sql_query(text);

-- Step 2: Revoke dangerous permissions
REVOKE ALL ON SCHEMA public FROM anon;
REVOKE ALL ON SCHEMA public FROM authenticated;

-- Step 3: Grant only necessary permissions
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Grant table permissions
GRANT SELECT ON public.venues TO anon, authenticated;
GRANT SELECT ON public.reviews TO anon, authenticated;
GRANT SELECT ON public.user_profiles TO anon, authenticated;

-- Authenticated users can modify their own data
GRANT INSERT, UPDATE, DELETE ON public.bookings TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.messages TO authenticated;
GRANT INSERT, UPDATE ON public.user_profiles TO authenticated;

-- Step 4: Fix auth configuration (skip if auth.config doesn't exist)
DO $$
BEGIN
  -- Check if auth.config table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'config') THEN
    UPDATE auth.config SET
      otp_expiry = 600,  -- 10 minutes instead of 1 hour
      password_reset_expiry = 3600,  -- 1 hour instead of 24 hours
      email_confirmation_expiry = 86400,  -- 24 hours
      enable_password_breach_detection = true,
      password_min_length = 8
    WHERE true;
    RAISE NOTICE 'Auth configuration updated successfully';
  ELSE
    RAISE NOTICE 'Auth configuration table not found - skipping auth config updates';
  END IF;
END;
$$;

-- Step 5: Create secure replacement functions with proper search paths

-- Secure notification creation
CREATE OR REPLACE FUNCTION public.create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  notification_id UUID;
BEGIN
  IF p_user_id IS NULL OR p_title IS NULL OR p_message IS NULL THEN
    RAISE EXCEPTION 'Missing required parameters';
  END IF;
  
  INSERT INTO notifications (user_id, title, message, type)
  VALUES (p_user_id, p_title, p_message, p_type)
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$;

-- Secure message count function
CREATE OR REPLACE FUNCTION public.get_unread_message_count(
  p_user_id UUID
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM messages 
    WHERE recipient_id = p_user_id 
    AND read_at IS NULL
  );
END;
$$;

-- Secure rating calculation
CREATE OR REPLACE FUNCTION public.calculate_confidence_score(
  p_venue_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  avg_rating DECIMAL(3,2);
  review_count INTEGER;
BEGIN
  SELECT 
    COALESCE(AVG(rating), 0),
    COUNT(*)
  INTO avg_rating, review_count
  FROM reviews 
  WHERE venue_id = p_venue_id;
  
  IF review_count = 0 THEN
    RETURN 0.00;
  END IF;
  
  RETURN ROUND(avg_rating * (review_count::DECIMAL / (review_count + 10)), 2);
END;
$$;

-- Secure user rating function
CREATE OR REPLACE FUNCTION public.get_user_average_rating(
  p_user_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN (
    SELECT COALESCE(AVG(r.rating), 0.00)
    FROM reviews r
    JOIN venues v ON r.venue_id = v.id
    WHERE v.host_id = p_user_id
  );
END;
$$;

-- Secure property type detection
CREATE OR REPLACE FUNCTION public.detect_property_type(
  p_venue_id UUID
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  venue_info RECORD;
BEGIN
  SELECT title, description INTO venue_info
  FROM venues WHERE id = p_venue_id;
  
  IF venue_info.title ILIKE '%house%' OR venue_info.description ILIKE '%house%' THEN
    RETURN 'house';
  ELSIF venue_info.title ILIKE '%apartment%' OR venue_info.description ILIKE '%apartment%' THEN
    RETURN 'apartment';
  ELSIF venue_info.title ILIKE '%villa%' OR venue_info.description ILIKE '%villa%' THEN
    RETURN 'villa';
  ELSIF venue_info.title ILIKE '%studio%' OR venue_info.description ILIKE '%studio%' THEN
    RETURN 'studio';
  ELSE
    RETURN 'other';
  END IF;
END;
$$;

-- Secure curfew info function
CREATE OR REPLACE FUNCTION public.get_curfew_info(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  venue_rules TEXT;
BEGIN
  SELECT house_rules INTO venue_rules
  FROM venues WHERE id = p_venue_id;
  
  RETURN jsonb_build_object(
    'has_curfew', 
    CASE WHEN venue_rules ILIKE '%quiet%' OR venue_rules ILIKE '%curfew%' THEN true ELSE false END,
    'curfew_time', 
    CASE 
      WHEN venue_rules ILIKE '%10%pm%' OR venue_rules ILIKE '%22:%' THEN '22:00'
      WHEN venue_rules ILIKE '%11%pm%' OR venue_rules ILIKE '%23:%' THEN '23:00'
      ELSE '22:00'
    END
  );
END;
$$;

-- Secure venue availability check
CREATE OR REPLACE FUNCTION public.check_venue_availability(
  p_venue_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM bookings
    WHERE venue_id = p_venue_id
    AND status IN ('confirmed', 'pending')
    AND (
      (start_date <= p_start_date AND end_date >= p_start_date) OR
      (start_date <= p_end_date AND end_date >= p_end_date) OR
      (start_date >= p_start_date AND end_date <= p_end_date)
    )
  );
END;
$$;

-- Secure booking conflict check
CREATE OR REPLACE FUNCTION public.check_booking_conflicts(
  p_venue_id UUID,
  p_start_date TIMESTAMP,
  p_end_date TIMESTAMP,
  p_exclude_booking_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM bookings
    WHERE venue_id = p_venue_id
    AND status IN ('confirmed', 'pending')
    AND (p_exclude_booking_id IS NULL OR id != p_exclude_booking_id)
    AND (
      (start_date <= p_start_date AND end_date >= p_start_date) OR
      (start_date <= p_end_date AND end_date >= p_end_date) OR
      (start_date >= p_start_date AND end_date <= p_end_date)
    )
  );
END;
$$;

-- Step 6: Grant minimal necessary permissions
GRANT EXECUTE ON FUNCTION public.create_notification TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_unread_message_count TO authenticated;
GRANT EXECUTE ON FUNCTION public.calculate_confidence_score TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_user_average_rating TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.detect_property_type TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_curfew_info TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.check_venue_availability TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.check_booking_conflicts TO authenticated;

-- Step 7: Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Step 8: Apply security settings
ALTER DATABASE postgres SET log_statement = 'all';
ALTER DATABASE postgres SET log_min_duration_statement = 1000;

-- Step 9: Create security audit log
CREATE TABLE IF NOT EXISTS security_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL,
  user_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS security_audit_log_created_at_idx ON security_audit_log(created_at);
CREATE INDEX IF NOT EXISTS security_audit_log_user_id_idx ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS security_audit_log_event_type_idx ON security_audit_log(event_type);

-- Enable RLS on audit log
ALTER TABLE security_audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs (if admin_users table exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'admin_users') THEN
    EXECUTE 'CREATE POLICY "Only admins can view audit logs" ON security_audit_log
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM admin_users au
          WHERE au.user_id = auth.uid()
        )
      )';
    RAISE NOTICE 'Admin-only audit log policy created';
  ELSE
    -- Allow authenticated users to view audit logs if no admin table exists
    EXECUTE 'CREATE POLICY "Authenticated users can view audit logs" ON security_audit_log
      FOR SELECT USING (auth.uid() IS NOT NULL)';
    RAISE NOTICE 'General audit log policy created (no admin table found)';
  END IF;
END;
$$;

GRANT SELECT ON security_audit_log TO authenticated;

-- Final step: Log this security cleanup
INSERT INTO security_audit_log (event_type, details)
VALUES ('security_cleanup', '{"action": "removed_insecure_functions", "timestamp": "' || NOW() || '"}');

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Security cleanup completed successfully. All insecure functions removed and replaced with secure alternatives.';
END;
$$;
