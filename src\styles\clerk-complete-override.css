/* Complete overrides for <PERSON> UI to remove all branding and social buttons */

/* Hide all Clerk branding and development mode elements */
.cl-footer,
.cl-footer-item,
.cl-footer-text,
.cl-footer-link,
.cl-development-mode-badge,
.cl-developmentModeText,
.cl-developmentModeBox,
.cl-internal-b3fm6y,
.cl-auth-form-wrapper::before,
.cl-auth-form-wrapper::after,
[data-clerk-ui],
[data-clerk-ui="developmentBadge"],
.cl-rootBox > div > div:last-child,
.cl-rootBox > div > div:last-child > div,
.cl-rootBox > div > div:last-child > div > div,
.cl-rootBox > div > div:last-child > div > div > div,
.cl-rootBox > div > div:last-child > div > div > div > div {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Hide all social buttons and related elements */
.cl-socialButtonsBlockButton,
.cl-socialButtonsIconButton,
.cl-socialButtons,
.cl-socialButtonsBlockButtonText,
.cl-socialButtonsBlockButtonArrow,
.cl-socialButtonsProviderIcon,
.cl-socialButtonsIconButton__google,
.cl-socialButtonsIconButton__facebook,
.cl-socialButtonsIconButton__tiktok,
.cl-socialButtonsProviderIcon__google,
.cl-socialButtonsProviderIcon__facebook,
.cl-socialButtonsProviderIcon__tiktok,
.cl-socialButtonsBlockButton__google,
.cl-socialButtonsBlockButton__facebook,
.cl-socialButtonsBlockButton__tiktok,
.cl-socialButtonsBlockButtonText__google,
.cl-socialButtonsBlockButtonText__facebook,
.cl-socialButtonsBlockButtonText__tiktok,
.cl-socialButtonsBlockButtonArrow__google,
.cl-socialButtonsBlockButtonArrow__facebook,
.cl-socialButtonsBlockButtonArrow__tiktok,
.cl-socialButtonsIconButton__github,
.cl-socialButtonsIconButton__apple,
.cl-socialButtonsProviderIcon__github,
.cl-socialButtonsProviderIcon__apple,
.cl-socialButtonsBlockButton__github,
.cl-socialButtonsBlockButton__apple,
.cl-socialButtonsBlockButtonText__github,
.cl-socialButtonsBlockButtonText__apple,
.cl-socialButtonsBlockButtonArrow__github,
.cl-socialButtonsBlockButtonArrow__apple,
[data-localization-key="socialButtonsBlockButton"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Hide the entire social buttons container */
.cl-socialButtonsBlock,
.cl-socialButtonsIconRow,
.cl-socialButtonsBlockButtonRow,
[data-localization-key="formSocialButtonsBlockButton"],
[data-localization-key="formSocialButtonsIconButton"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Hide the divider text */
.cl-dividerText,
.cl-divider {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Hide any elements with "Clerk" in the text */
*:contains("Clerk"),
*:contains("clerk") {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Improve the form styling */
.cl-formButtonPrimary {
  background: linear-gradient(to right, #7C3AED, #EC4899) !important;
  border-radius: 9999px !important; /* Full rounded */
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease-in-out !important;
  border: none !important;
  width: 100% !important;
  max-width: 300px !important;
  margin: 0 auto !important;
  display: block !important;
}

.cl-formButtonPrimary:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px) !important;
}

.cl-input {
  border-radius: 0.5rem !important;
  border: 1px solid #E5E7EB !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.2s ease-in-out !important;
  width: 100% !important;
  max-width: 300px !important;
  margin: 0 auto !important;
}

.cl-input:focus {
  border-color: #7C3AED !important;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1) !important;
}

.cl-formFieldLabel {
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
  text-align: center !important;
}

.cl-formField {
  margin-bottom: 1.25rem !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Center the form elements */
.cl-form {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  width: 100% !important;
}

/* Hide any remaining Clerk branding */
[class*="cl-internal"],
[class*="cl-auth"],
[class*="cl-branded"],
[class*="cl-social"],
.cl-socialButtons,
.cl-socialButtonsBlock,
.cl-socialButtonsBlockButtonRow,
.cl-socialButtonsIconRow,
.cl-socialButtonsBlockButton,
.cl-socialButtonsIconButton,
.cl-socialButtonsProviderIcon,
.cl-socialButtonsBlockButtonText,
.cl-socialButtonsBlockButtonArrow,
.cl-socialButtonsIconButton__google,
.cl-socialButtonsIconButton__facebook,
.cl-socialButtonsIconButton__tiktok,
.cl-socialButtonsProviderIcon__google,
.cl-socialButtonsProviderIcon__facebook,
.cl-socialButtonsProviderIcon__tiktok,
.cl-socialButtonsBlockButton__google,
.cl-socialButtonsBlockButton__facebook,
.cl-socialButtonsBlockButton__tiktok,
.cl-socialButtonsBlockButtonText__google,
.cl-socialButtonsBlockButtonText__facebook,
.cl-socialButtonsBlockButtonText__tiktok,
.cl-socialButtonsBlockButtonArrow__google,
.cl-socialButtonsBlockButtonArrow__facebook,
.cl-socialButtonsBlockButtonArrow__tiktok,
.cl-socialButtonsIconButton__github,
.cl-socialButtonsIconButton__apple,
.cl-socialButtonsProviderIcon__github,
.cl-socialButtonsProviderIcon__apple,
.cl-socialButtonsBlockButton__github,
.cl-socialButtonsBlockButton__apple,
.cl-socialButtonsBlockButtonText__github,
.cl-socialButtonsBlockButtonText__apple,
.cl-socialButtonsBlockButtonArrow__github,
.cl-socialButtonsBlockButtonArrow__apple,
[data-localization-key="socialButtonsBlockButton"],
[data-localization-key="formSocialButtonsBlockButton"],
[data-localization-key="formSocialButtonsIconButton"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  overflow: hidden !important;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
}
