/**
 * IMPORTANT: This file is deprecated and should not be used directly.
 * Import from src/lib/supabase-client.ts instead to prevent multiple GoTrueClient instances.
 */

import { supabase as centralizedClient } from '../../lib/supabase-client';

// Log a warning when this file is imported
console.warn('WARNING: src/integrations/supabase/client.ts is deprecated. Import from src/lib/supabase-client.ts instead.');

// Re-export the centralized client
export const supabase = centralizedClient;