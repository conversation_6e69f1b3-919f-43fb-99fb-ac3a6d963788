/**
 * Utility functions for working with session storage
 */

// Save chat messages to session storage
export const saveMessagesToSession = (messages) => {
  try {
    sessionStorage.setItem('housegoing_chat_messages', JSON.stringify(messages));
  } catch (error) {
    console.error('Error saving messages to session storage:', error);
  }
};

// Load chat messages from session storage
export const loadMessagesFromSession = () => {
  try {
    const savedMessages = sessionStorage.getItem('housegoing_chat_messages');
    return savedMessages ? JSON.parse(savedMessages) : null;
  } catch (error) {
    console.error('Error loading messages from session storage:', error);
    return null;
  }
};

// Save conversation context to session storage
export const saveContextToSession = (context) => {
  try {
    sessionStorage.setItem('housegoing_chat_context', JSON.stringify(context));
  } catch (error) {
    console.error('Error saving context to session storage:', error);
  }
};

// Load conversation context from session storage
export const loadContextFromSession = () => {
  try {
    const savedContext = sessionStorage.getItem('housegoing_chat_context');
    return savedContext ? JSON.parse(savedContext) : null;
  } catch (error) {
    console.error('Error loading context from session storage:', error);
    return null;
  }
};

// Clear chat session data
export const clearChatSession = () => {
  try {
    sessionStorage.removeItem('housegoing_chat_messages');
    sessionStorage.removeItem('housegoing_chat_context');
  } catch (error) {
    console.error('Error clearing chat session:', error);
  }
};
