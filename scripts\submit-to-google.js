#!/usr/bin/env node

/**
 * Google Search Console URL Submission Script
 * Helps submit important URLs to Google for faster indexing
 */

const https = require('https');
const fs = require('fs');

// Important URLs to submit to Google
const importantUrls = [
  'https://housegoing.com.au/',
  'https://housegoing.com.au/find-venues',
  'https://housegoing.com.au/host/signup',
  'https://housegoing.com.au/nsw-party-planning',
  'https://housegoing.com.au/terms',
  'https://housegoing.com.au/privacy',
  'https://housegoing.com.au/faq'
];

console.log('🔍 Google Search Console URL Submission Guide');
console.log('='.repeat(50));
console.log('');
console.log('To submit URLs to Google Search Console:');
console.log('');
console.log('1. Go to: https://search.google.com/search-console');
console.log('2. Select your HouseGoing property');
console.log('3. Go to "URL Inspection" tool');
console.log('4. Submit these important URLs:');
console.log('');

importantUrls.forEach((url, index) => {
  console.log(`   ${index + 1}. ${url}`);
});

console.log('');
console.log('5. For each URL:');
console.log('   - Paste the URL');
console.log('   - Click "Test Live URL"');
console.log('   - Click "Request Indexing"');
console.log('');
console.log('📊 Also submit your sitemap:');
console.log('   - Go to "Sitemaps" section');
console.log('   - Add: sitemap.xml');
console.log('   - Add: sitemap_main.xml');
console.log('');
console.log('✅ This will help Google find and index your pages faster!');
