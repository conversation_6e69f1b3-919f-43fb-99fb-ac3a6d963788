/**
 * Owner Property List Component
 * 
 * Shows owner's property submissions and approved venues
 */

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { Building, Eye, Edit, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { 
  getOwnerPropertySubmissions, 
  getOwnerLiveVenues,
  PropertySubmission,
  LiveVenue 
} from '../../services/propertyDataService';

export default function OwnerPropertyList() {
  const { user } = useUser();
  const [submissions, setSubmissions] = useState<PropertySubmission[]>([]);
  const [liveVenues, setLiveVenues] = useState<LiveVenue[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'submissions' | 'live'>('submissions');

  useEffect(() => {
    if (user?.id) {
      loadOwnerProperties();
    }
  }, [user?.id]);

  const loadOwnerProperties = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const [submissionsData, venuesData] = await Promise.all([
        getOwnerPropertySubmissions(user.id),
        getOwnerLiveVenues(user.id)
      ]);

      setSubmissions(submissionsData);
      setLiveVenues(venuesData);
      console.log('✅ Loaded owner properties:', { submissions: submissionsData.length, venues: venuesData.length });
    } catch (error) {
      console.error('Error loading owner properties:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          <p className="ml-3 text-purple-600">Loading your properties...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">My Properties</h2>
          <button
            onClick={loadOwnerProperties}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm"
          >
            Refresh
          </button>
        </div>

        {/* Tabs */}
        <div className="flex mt-4 border-b">
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === 'submissions'
                ? 'text-purple-600 border-b-2 border-purple-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => setActiveTab('submissions')}
          >
            Submissions ({submissions.length})
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === 'live'
                ? 'text-purple-600 border-b-2 border-purple-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => setActiveTab('live')}
          >
            Live Venues ({liveVenues.length})
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Property Submissions Tab */}
        {activeTab === 'submissions' && (
          <div>
            {submissions.length === 0 ? (
              <div className="text-center py-8">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No property submissions found</p>
                <p className="text-sm text-gray-400 mt-2">
                  Submit your first property to get started
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{submission.name}</h3>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(submission.status)}
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>
                              {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-600 mb-2">{submission.address}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Type: {submission.type}</span>
                          <span>Capacity: {submission.maxGuests} guests</span>
                          <span>Price: ${submission.price}/hour</span>
                        </div>
                        {submission.status === 'rejected' && submission.rejection_reason && (
                          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-800">
                              <strong>Rejection Reason:</strong> {submission.rejection_reason}
                            </p>
                          </div>
                        )}
                        {submission.admin_notes && (
                          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                            <p className="text-sm text-blue-800">
                              <strong>Admin Notes:</strong> {submission.admin_notes}
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-md">
                          <Eye className="h-4 w-4" />
                        </button>
                        {submission.status === 'rejected' && (
                          <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-md">
                            <Edit className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Live Venues Tab */}
        {activeTab === 'live' && (
          <div>
            {liveVenues.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No live venues yet</p>
                <p className="text-sm text-gray-400 mt-2">
                  Your approved properties will appear here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {liveVenues.map((venue) => (
                  <div key={venue.id} className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{venue.title}</h3>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Live
                          </span>
                        </div>
                        <p className="text-gray-600 mb-2">{venue.address}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Capacity: {venue.capacity} guests</span>
                          <span>Price: ${venue.price_per_hour}/hour</span>
                          <span>Published: {venue.is_published ? 'Yes' : 'No'}</span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-md">
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-md">
                          <Edit className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
