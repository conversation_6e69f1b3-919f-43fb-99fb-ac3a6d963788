import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  isDarkMode: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize theme from localStorage or default to 'system'
  const [theme, setThemeState] = useState<Theme>(() => {
    const savedTheme = localStorage.getItem('theme') as Theme;
    return savedTheme || 'system';
  });
  
  // Track whether dark mode is active
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  
  // Update localStorage and apply theme when it changes
  useEffect(() => {
    localStorage.setItem('theme', theme);
    
    // Apply the theme to the document
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    
    // Determine if dark mode should be active
    let darkModeActive = false;
    
    if (theme === 'system') {
      darkModeActive = window.matchMedia('(prefers-color-scheme: dark)').matches;
    } else {
      darkModeActive = theme === 'dark';
    }
    
    // Apply the appropriate class
    if (darkModeActive) {
      root.classList.add('dark');
    } else {
      root.classList.add('light');
    }
    
    setIsDarkMode(darkModeActive);
  }, [theme]);
  
  // Listen for system theme changes
  useEffect(() => {
    if (theme !== 'system') return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      const root = window.document.documentElement;
      const isDark = mediaQuery.matches;
      
      root.classList.remove('light', 'dark');
      root.classList.add(isDark ? 'dark' : 'light');
      
      setIsDarkMode(isDark);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);
  
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme, isDarkMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
