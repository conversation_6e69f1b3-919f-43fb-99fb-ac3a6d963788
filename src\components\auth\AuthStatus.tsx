import React from 'react';
import { useSupabase } from '../../providers/SupabaseProvider';

/**
 * Component to display the current authentication status
 */
export default function AuthStatus() {
  const { userProfile, isLoading, isAuthenticated, isHost, signOut } = useSupabase();

  if (isLoading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
        <p>Loading user data...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
        <p>Not authenticated</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
      <div className="space-y-2">
        <p><strong>Authenticated:</strong> Yes</p>
        <p><strong>User ID:</strong> {userProfile?.id}</p>
        <p><strong>Email:</strong> {userProfile?.email}</p>
        <p><strong>Role:</strong> {userProfile?.role}</p>
        <p><strong>Is Host:</strong> {isHost ? 'Yes' : 'No'}</p>
        <p><strong>First Name:</strong> {userProfile?.first_name || 'Not set'}</p>
        <p><strong>Last Name:</strong> {userProfile?.last_name || 'Not set'}</p>
        <button
          onClick={signOut}
          className="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Sign Out
        </button>
      </div>
    </div>
  );
}
