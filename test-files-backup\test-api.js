const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testZoning() {
  try {
    const testData = {
      address: '10 Darvall Road, Eastwood, NSW 2122',
      lat: -33.791,
      lng: 151.080
    };
    
    console.log('Sending:', testData);
    const response = await fetch('http://localhost:5177/api/nsw-zoning', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    });
    
    console.log('Status:', response.status);
    const text = await response.text();
    console.log('Raw response:', text);
    
    try {
      const data = JSON.parse(text);
      console.log('Parsed JSON:', data);
    } catch (parseError) {
      console.error('Failed to parse JSON:', parseError);
    }
  } catch (error) {
    console.error('Request failed:', error);
  }
}

testZoning();
