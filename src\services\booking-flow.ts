/**
 * Comprehensive Booking Flow Service
 * 
 * This service manages the complete booking flow from venue selection
 * to payment confirmation and email notifications.
 */

import { createBooking, getBookingById, updateBookingStatus } from '../api/bookings';
import { stripePaymentService, PriceCalculation } from './stripe-payment';
import { sendBookingConfirmationEmail, sendHostNotificationEmail } from './email';
import { getSupabaseClient } from './api';
import { BookingData } from '../components/booking/BookingForm';
import { Venue } from '../types/venue';
import { trackBookingEvent } from '../api/analytics';

export interface BookingFlowData {
  venue: Venue;
  bookingData: BookingData;
  user: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
  priceCalculation: PriceCalculation;
}

export interface BookingFlowResult {
  success: boolean;
  bookingId?: string;
  paymentId?: string;
  error?: string;
  step: 'validation' | 'booking_creation' | 'payment_processing' | 'confirmation' | 'completed' | 'failed';
}

export class BookingFlowService {
  /**
   * Execute the complete booking flow
   */
  async executeBookingFlow(flowData: BookingFlowData): Promise<BookingFlowResult> {
    const { venue, bookingData, user, priceCalculation } = flowData;
    
    try {
      // Step 1: Validate booking data
      console.log('🔍 Step 1: Validating booking data...');
      const validationResult = await this.validateBookingData(flowData);
      if (!validationResult.success) {
        return {
          success: false,
          error: validationResult.error,
          step: 'validation'
        };
      }

      // Step 2: Create booking in database (pending status)
      console.log('📝 Step 2: Creating booking in database...');
      const booking = await createBooking(bookingData, user.id, venue.id);
      
      if (!booking || !booking.id) {
        return {
          success: false,
          error: 'Failed to create booking in database',
          step: 'booking_creation'
        };
      }

      console.log('✅ Booking created successfully:', booking.id);

      // Step 3: Return booking ID for payment processing
      // Payment will be handled by the BookingPayment component
      // and webhook will update the booking status upon successful payment
      return {
        success: true,
        bookingId: booking.id,
        step: 'payment_processing'
      };

    } catch (error: any) {
      console.error('❌ Booking flow failed:', error);
      return {
        success: false,
        error: error.message || 'Booking flow failed',
        step: 'failed'
      };
    }
  }

  /**
   * Validate booking data before processing
   */
  private async validateBookingData(flowData: BookingFlowData): Promise<{ success: boolean; error?: string }> {
    const { venue, bookingData, user, priceCalculation } = flowData;

    // Validate user
    if (!user || !user.id || !user.email) {
      return { success: false, error: 'User information is required' };
    }

    // Validate venue
    if (!venue || !venue.id) {
      return { success: false, error: 'Venue information is required' };
    }

    // Validate booking data
    if (!bookingData.startDate || !bookingData.startTime || !bookingData.endTime) {
      return { success: false, error: 'Booking date and time are required' };
    }

    if (!bookingData.guests || bookingData.guests <= 0) {
      return { success: false, error: 'Number of guests is required' };
    }

    if (bookingData.guests > venue.capacity) {
      return { success: false, error: `Number of guests (${bookingData.guests}) exceeds venue capacity (${venue.capacity})` };
    }

    // Validate price calculation
    if (!priceCalculation || !priceCalculation.total || priceCalculation.total <= 0) {
      return { success: false, error: 'Invalid price calculation' };
    }

    // Validate dates are not in the past
    const startDateTime = new Date(`${bookingData.startDate}T${bookingData.startTime}`);
    const now = new Date();
    
    if (startDateTime <= now) {
      return { success: false, error: 'Booking date and time must be in the future' };
    }

    return { success: true };
  }

  /**
   * Handle successful payment (called by webhook or payment component)
   */
  async handlePaymentSuccess(bookingId: string, paymentId: string, paymentAmount: number): Promise<BookingFlowResult> {
    try {
      console.log('💳 Processing successful payment for booking:', bookingId);

      // Update booking status to confirmed
      await updateBookingStatus(bookingId, 'confirmed', {
        payment_id: paymentId,
        payment_status: 'paid',
        payment_amount: paymentAmount,
        payment_date: new Date().toISOString(),
        payment_method: 'Stripe'
      });

      // Get updated booking details
      const booking = await getBookingById(bookingId);
      if (!booking) {
        throw new Error('Booking not found after payment');
      }

      // Track the booking event for analytics
      try {
        await trackBookingEvent({
          booking_id: booking.id,
          user_id: booking.guest_id,
          venue_id: booking.venue_id,
          venue_suburb: booking.venue?.location || 'Unknown',
          venue_state: 'NSW', // Default to NSW for now
          booking_date: booking.start_date.split('T')[0],
          booking_amount: booking.total_price,
          guest_count: booking.guests_count,
          booking_status: 'confirmed'
        });
        console.log('✅ Booking analytics tracked successfully');
      } catch (analyticsError) {
        console.error('⚠️ Failed to track booking analytics:', analyticsError);
        // Don't fail the booking if analytics fails
      }

      // Send confirmation emails
      await this.sendConfirmationEmails(booking);

      console.log('✅ Payment processed successfully for booking:', bookingId);

      return {
        success: true,
        bookingId,
        paymentId,
        step: 'completed'
      };

    } catch (error: any) {
      console.error('❌ Payment processing failed:', error);
      return {
        success: false,
        error: error.message || 'Payment processing failed',
        step: 'failed'
      };
    }
  }

  /**
   * Handle failed payment
   */
  async handlePaymentFailure(bookingId: string, error: string): Promise<void> {
    try {
      console.log('❌ Processing failed payment for booking:', bookingId);

      // Update booking status to payment_failed
      await updateBookingStatus(bookingId, 'payment_failed', {
        payment_status: 'failed',
        payment_error: error,
        updated_at: new Date().toISOString()
      });

      // TODO: Send payment failure notification email
      console.log('📧 Payment failure notification sent for booking:', bookingId);

    } catch (updateError: any) {
      console.error('Failed to update booking after payment failure:', updateError);
    }
  }

  /**
   * Send confirmation emails to guest and host
   */
  private async sendConfirmationEmails(booking: any): Promise<void> {
    try {
      console.log('📧 Sending confirmation emails for booking:', booking.id);

      // Get guest and venue details
      const supabase = getSupabaseClient();
      
      // Get guest details
      const { data: guest } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', booking.guest_id)
        .single();

      // Get venue and host details
      const { data: venue } = await supabase
        .from('venues')
        .select(`
          *,
          host:host_id (
            id,
            email,
            first_name,
            last_name
          )
        `)
        .eq('id', booking.venue_id)
        .single();

      if (guest && venue) {
        // Send confirmation email to guest
        await sendBookingConfirmationEmail(booking, guest, venue);

        // Send notification email to host
        if (venue.host) {
          await sendHostNotificationEmail(booking, venue.host, venue, guest);
        }

        console.log('✅ Confirmation emails sent successfully');
      } else {
        console.warn('⚠️ Missing guest or venue data for email notifications');
      }

    } catch (error) {
      console.error('❌ Failed to send confirmation emails:', error);
      // Don't throw error as booking is already successful
    }
  }

  /**
   * Get booking status and details
   */
  async getBookingStatus(bookingId: string): Promise<any> {
    try {
      const booking = await getBookingById(bookingId);
      return booking;
    } catch (error) {
      console.error('Failed to get booking status:', error);
      return null;
    }
  }

  /**
   * Cancel booking (if allowed)
   */
  async cancelBooking(bookingId: string, reason?: string): Promise<BookingFlowResult> {
    try {
      console.log('🚫 Cancelling booking:', bookingId);

      // Update booking status to cancelled
      await updateBookingStatus(bookingId, 'cancelled', {
        cancellation_reason: reason,
        cancelled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      // TODO: Process refund if applicable
      // TODO: Send cancellation emails

      return {
        success: true,
        bookingId,
        step: 'completed'
      };

    } catch (error: any) {
      console.error('❌ Booking cancellation failed:', error);
      return {
        success: false,
        error: error.message || 'Booking cancellation failed',
        step: 'failed'
      };
    }
  }
}

// Export singleton instance
export const bookingFlowService = new BookingFlowService();
export default bookingFlowService;
