/**
 * Test script for enhanced address utilities
 * 
 * This script tests the address parsing, LGA detection, and zoning detection
 * for specific addresses.
 */

import { 
  extractAddressComponents, 
  extractLGAFromAddress, 
  detectZoning,
  determinePropertyType,
  analyzeNSWAddress
} from './enhancedAddressUtils';

// Test addresses
const testAddresses = [
  {
    address: '15-19 Robert Street, Holroyd, NSW 2142',
    lat: -33.8302,
    lng: 150.9893
  },
  {
    address: '5 Tiptrees Ave, Carlingford, NSW 2118',
    lat: -33.7828,
    lng: 151.0463
  },
  {
    address: '24 BERRY STREET, Clyde, NSW 2142',
    lat: -33.8336,
    lng: 151.0249
  }
];

// Run tests
function runTests() {
  console.log('=== NSW Address Analysis Test ===\n');
  
  testAddresses.forEach((test, index) => {
    console.log(`Test ${index + 1}: ${test.address}`);
    console.log('----------------------------');
    
    // Test address components extraction
    const components = extractAddressComponents(test.address);
    console.log('Address Components:');
    console.log(JSON.stringify(components, null, 2));
    
    // Test LGA detection
    const lga = extractLGAFromAddress(test.address);
    console.log(`\nLGA: ${lga || 'Unknown'}`);
    
    // Test zoning detection
    const zoning = detectZoning(test.address, test.lat, test.lng);
    console.log(`Zoning: ${zoning}`);
    
    // Test property type detection
    const propertyType = determinePropertyType(test.address, zoning);
    console.log(`Property Type: ${propertyType}`);
    
    // Test comprehensive analysis
    const analysis = analyzeNSWAddress(test.address, test.lat, test.lng);
    console.log('\nComprehensive Analysis:');
    console.log(JSON.stringify(analysis, null, 2));
    
    console.log('\n');
  });
}

// Run the tests
runTests();
