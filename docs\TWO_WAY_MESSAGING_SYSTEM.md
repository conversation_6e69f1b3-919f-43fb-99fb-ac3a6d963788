# Two-Way Messaging System - HouseGoing

## Overview

The HouseGoing platform now features a **comprehensive two-way messaging system** that enables seamless communication between guests and hosts throughout the booking journey.

## Key Features

### ✅ **Real-Time Messaging**
- Instant message delivery using Supabase real-time subscriptions
- Live typing indicators and message status updates
- Automatic message read receipts and status tracking
- Mobile-responsive chat interface with smooth animations

### ✅ **Booking-Integrated Communication**
- Pre-booking inquiries directly from venue pages
- Post-booking coordination and event planning
- Booking-specific conversation threads
- Context-aware messaging with venue and booking details

### ✅ **Email Notifications**
- Instant email alerts for new messages
- Personalized email templates with sender names
- Booking context included in notifications
- Integration with Brevo email service

### ✅ **Smart Conversation Management**
- Automatic conversation grouping by user and booking
- Unread message counts and notifications
- Search functionality across conversations
- Message history and persistence

## Architecture

### Components

#### **Core Messaging Components**
- `EnhancedMessaging.tsx` - Main messaging interface with full chat functionality
- `BookingMessagingIntegration.tsx` - Booking-specific messaging integration
- `MessageNotifications.tsx` - Header notifications and unread count display
- `BookingMessages.tsx` - Legacy booking-specific message display

#### **API Services**
- `userAccount.ts` - Enhanced messaging API functions
- `emailNotifications.ts` - Email notification service
- `booking-flow.ts` - Integration with booking workflow

#### **Database Schema**
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sender_id TEXT NOT NULL REFERENCES profiles(id),
  receiver_id TEXT NOT NULL REFERENCES profiles(id),
  booking_id UUID REFERENCES bookings(id),
  content TEXT NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Implementation Details

### Real-Time Messaging Flow

```typescript
// 1. Send Message
const messageData = await sendMessage(
  senderId,
  receiverId,
  content,
  bookingId,
  senderName,
  venueName
);

// 2. Real-Time Subscription
const subscription = supabase
  .channel('messages-realtime')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'messages'
  }, (payload) => {
    // Update UI with new message
    setMessages(prev => [...prev, payload.new]);
  })
  .subscribe();

// 3. Email Notification
await sendMessageNotification(
  senderId,
  receiverId,
  content,
  senderName,
  bookingId,
  venueName
);
```

### Conversation Management

```typescript
// Get conversations for a user
const conversations = await getConversations(userId);

// Group by other user + booking
const conversationKey = `${otherUserId}_${bookingId || 'general'}`;

// Sort by last message timestamp
conversations.sort((a, b) => 
  new Date(b.lastMessage.created_at).getTime() - 
  new Date(a.lastMessage.created_at).getTime()
);
```

## Integration Points

### Venue Detail Pages
- **Contact Host Section**: Pre-booking inquiry form with quick message templates
- **Compact Messaging**: Streamlined interface for initial contact
- **Quick Templates**: Pre-written messages for common inquiries

### Booking Flow
- **Enhanced Booking Flow**: Integrated messaging during booking process
- **Post-Booking Communication**: Automatic conversation creation after booking
- **Event Coordination**: Host-guest communication for event planning

### Header Notifications
- **Unread Count Badge**: Real-time unread message indicator
- **Notification Dropdown**: Quick access to recent conversations
- **Navigation Integration**: Direct links to full messaging interface

## User Experience Features

### Guest Experience
1. **Venue Inquiry**: Send messages to hosts before booking
2. **Quick Templates**: Use pre-written inquiry messages
3. **Booking Coordination**: Communicate about event details
4. **Real-Time Updates**: Instant message delivery and notifications

### Host Experience
1. **Inquiry Management**: Respond to guest questions quickly
2. **Booking Support**: Help guests with event planning
3. **Notification System**: Email alerts for new messages
4. **Conversation History**: Access to all guest communications

### Mobile Experience
- **Responsive Design**: Optimized for mobile devices
- **Touch-Friendly Interface**: Easy navigation and message input
- **Conversation List**: Collapsible sidebar for mobile screens
- **Real-Time Sync**: Seamless experience across devices

## Email Integration

### Message Notification Templates
```typescript
const EMAIL_TEMPLATES = {
  BOOKING_MESSAGE: ({
    recipientName,
    senderName,
    messageContent,
    venueName
  }) => ({
    subject: `${senderName} sent you a message about ${venueName}`,
    html: `
      <h2>New Message from ${senderName}</h2>
      <p>Hi ${recipientName},</p>
      <p>You have a new message regarding your booking at ${venueName}:</p>
      <blockquote>${messageContent}</blockquote>
      <a href="https://housegoing.com.au/messages">Reply on HouseGoing</a>
    `
  })
};
```

### Brevo Integration
- **Transactional Emails**: Automated message notifications
- **Template Management**: Dynamic content with booking context
- **Delivery Tracking**: Monitor email delivery and engagement
- **Personalization**: Sender names and venue details

## Security & Privacy

### Message Security
- **User Authentication**: Only authenticated users can send messages
- **Conversation Privacy**: Users only see their own conversations
- **Content Filtering**: Basic content validation and sanitization
- **Rate Limiting**: Prevent spam and abuse

### Data Protection
- **Encrypted Storage**: Messages stored securely in Supabase
- **Access Control**: Row-level security policies
- **Data Retention**: Configurable message retention policies
- **GDPR Compliance**: User data deletion and export capabilities

## Performance Optimization

### Real-Time Efficiency
- **Selective Subscriptions**: Only subscribe to relevant conversations
- **Message Pagination**: Load messages in chunks for performance
- **Connection Management**: Automatic reconnection and error handling
- **Memory Management**: Cleanup subscriptions on component unmount

### Database Optimization
- **Indexed Queries**: Optimized database indexes for fast retrieval
- **Conversation Caching**: Cache frequently accessed conversations
- **Batch Operations**: Efficient bulk message operations
- **Connection Pooling**: Optimized database connection management

## Testing

### Manual Testing Scenarios
1. **Send Message**: Test message sending between users
2. **Real-Time Updates**: Verify instant message delivery
3. **Email Notifications**: Check email delivery and content
4. **Mobile Experience**: Test responsive design and touch interactions
5. **Conversation Management**: Verify conversation grouping and sorting

### Automated Testing
```typescript
// Example test for message sending
describe('Message Sending', () => {
  it('should send message and trigger real-time update', async () => {
    const message = await sendMessage(senderId, receiverId, 'Test message');
    expect(message.content).toBe('Test message');
    expect(message.read).toBe(false);
  });
});
```

## Monitoring & Analytics

### Key Metrics
- **Message Volume**: Total messages sent per day/week/month
- **Response Rate**: Percentage of messages that receive replies
- **Response Time**: Average time between message and reply
- **User Engagement**: Active messaging users and conversation frequency

### Error Monitoring
- **Message Delivery Failures**: Track failed message sends
- **Email Notification Failures**: Monitor email delivery issues
- **Real-Time Connection Issues**: Track WebSocket connection problems
- **Database Performance**: Monitor query performance and timeouts

## Future Enhancements

### Planned Features
- **Message Attachments**: Support for images and documents
- **Voice Messages**: Audio message recording and playback
- **Message Reactions**: Emoji reactions and message interactions
- **Typing Indicators**: Show when other user is typing
- **Message Threading**: Reply to specific messages in conversations

### Advanced Features
- **AI-Powered Responses**: Smart reply suggestions
- **Translation Support**: Multi-language message translation
- **Message Scheduling**: Schedule messages for later delivery
- **Conversation Analytics**: Detailed conversation insights
- **Integration APIs**: Third-party messaging platform integration

## Deployment

### Production Checklist
1. **Environment Variables**: Configure Supabase and email service keys
2. **Database Setup**: Ensure messages table and indexes exist
3. **Email Service**: Configure Brevo API keys and templates
4. **Real-Time Setup**: Verify Supabase real-time configuration
5. **Monitoring**: Set up error tracking and performance monitoring

### Scaling Considerations
- **Message Volume**: Plan for high-volume message handling
- **Real-Time Connections**: Scale WebSocket connections
- **Email Delivery**: Monitor email service limits and quotas
- **Database Performance**: Optimize for large message volumes

The two-way messaging system provides a complete communication solution that enhances the booking experience and builds trust between guests and hosts on the HouseGoing platform.
