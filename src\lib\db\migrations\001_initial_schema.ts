import { supabase } from '../../supabase';
import { Migration } from './index';

/**
 * Initial database schema migration
 */
const migration: Migration = {
  id: 1,
  name: 'initial_schema',
  
  up: async () => {
    // Create profiles table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS profiles (
          id UUID PRIMARY KEY REFERENCES auth.users(id),
          username TEXT UNIQUE,
          full_name TEXT,
          avatar_url TEXT,
          bio TEXT,
          is_host BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
        );
        
        CREATE INDEX IF NOT EXISTS profiles_username_idx ON profiles(username);
      `
    });
    
    // Create venues table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS venues (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          location TEXT NOT NULL,
          coordinates JSONB,
          price DECIMAL NOT NULL,
          capacity INTEGER NOT NULL,
          amenities TEXT[] DEFAULT '{}',
          images TEXT[] DEFAULT '{}',
          host_id UUID NOT NULL REFERENCES profiles(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
        );
        
        CREATE INDEX IF NOT EXISTS venues_host_id_idx ON venues(host_id);
        CREATE INDEX IF NOT EXISTS venues_location_idx ON venues(location);
      `
    });
    
    // Create bookings table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS bookings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          venue_id UUID NOT NULL REFERENCES venues(id),
          guest_id UUID NOT NULL REFERENCES profiles(id),
          start_date TIMESTAMP WITH TIME ZONE NOT NULL,
          end_date TIMESTAMP WITH TIME ZONE NOT NULL,
          guests_count INTEGER NOT NULL,
          total_price DECIMAL NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          
          CONSTRAINT valid_date_range CHECK (end_date > start_date),
          CONSTRAINT valid_status CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed'))
        );
        
        CREATE INDEX IF NOT EXISTS bookings_venue_id_idx ON bookings(venue_id);
        CREATE INDEX IF NOT EXISTS bookings_guest_id_idx ON bookings(guest_id);
        CREATE INDEX IF NOT EXISTS bookings_status_idx ON bookings(status);
      `
    });
    
    // Create reviews table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS reviews (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          venue_id UUID NOT NULL REFERENCES venues(id),
          user_id UUID NOT NULL REFERENCES profiles(id),
          rating INTEGER NOT NULL,
          comment TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          
          CONSTRAINT valid_rating CHECK (rating BETWEEN 1 AND 5)
        );
        
        CREATE INDEX IF NOT EXISTS reviews_venue_id_idx ON reviews(venue_id);
        CREATE INDEX IF NOT EXISTS reviews_user_id_idx ON reviews(user_id);
      `
    });
    
    // Create messages table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS messages (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          booking_id UUID REFERENCES bookings(id),
          sender_id UUID NOT NULL REFERENCES profiles(id),
          receiver_id UUID NOT NULL REFERENCES profiles(id),
          content TEXT NOT NULL,
          read BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
        );
        
        CREATE INDEX IF NOT EXISTS messages_booking_id_idx ON messages(booking_id);
        CREATE INDEX IF NOT EXISTS messages_sender_id_idx ON messages(sender_id);
        CREATE INDEX IF NOT EXISTS messages_receiver_id_idx ON messages(receiver_id);
      `
    });
    
    // Create favorites table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS favorites (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES profiles(id),
          venue_id UUID NOT NULL REFERENCES venues(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          
          CONSTRAINT unique_favorite UNIQUE (user_id, venue_id)
        );
        
        CREATE INDEX IF NOT EXISTS favorites_user_id_idx ON favorites(user_id);
        CREATE INDEX IF NOT EXISTS favorites_venue_id_idx ON favorites(venue_id);
      `
    });
  },
  
  down: async () => {
    // Drop tables in reverse order
    await supabase.rpc('execute_sql', {
      sql: `
        DROP TABLE IF EXISTS favorites;
        DROP TABLE IF EXISTS messages;
        DROP TABLE IF EXISTS reviews;
        DROP TABLE IF EXISTS bookings;
        DROP TABLE IF EXISTS venues;
        DROP TABLE IF EXISTS profiles;
      `
    });
  }
};

export default migration;
