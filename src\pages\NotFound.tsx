import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import SEO from '../components/seo/SEO';

export default function NotFound() {
  // Set proper HTTP status code for SEO
  useEffect(() => {
    // This helps with server-side rendering and crawlers
    if (typeof window !== 'undefined' && window.history) {
      // Signal to any SSR or prerendering that this is a 404
      document.documentElement.setAttribute('data-status-code', '404');
    }
  }, []);

  return (
    <>
      <SEO
        title="Page Not Found (404) | HouseGoing"
        description="The page you're looking for doesn't exist. Find party venues and event spaces on HouseGoing."
        noindex={false}
      />
      <div className="min-h-screen pt-32 px-4 flex flex-col items-center">
        <div className="max-w-md mx-auto text-center">
          <h1 className="text-3xl font-bold mb-4">404 - Page Not Found</h1>
          <p className="text-gray-600 mb-8">
            The page you're looking for doesn't exist or has been moved.
          </p>
          <div className="flex flex-col space-y-4">
            <Link
              to="/"
              className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-md hover:from-purple-700 hover:to-pink-700 transition-colors"
            >
              Return to Home
            </Link>
            <Link
              to="/find-venues"
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Browse Venues
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
