-- HouseGoing Secure Database Functions
-- This script replaces insecure functions with secure alternatives
-- Run this in the Supabase SQL Editor

-- First, drop all insecure functions
DROP FUNCTION IF EXISTS public.exec_sql(text);
DROP FUNCTION IF EXISTS public.pg_query(text);
DROP FUNCTION IF EXISTS public.execute_sql(text);

-- Create secure search path for all functions
SET search_path = public, extensions;

-- Create secure notification function
CREATE OR REPLACE FUNCTION public.create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  notification_id UUID;
BEGIN
  -- Validate input
  IF p_user_id IS NULL OR p_title IS NULL OR p_message IS NULL THEN
    RAISE EXCEPTION 'Missing required parameters';
  END IF;
  
  -- Insert notification
  INSERT INTO notifications (user_id, title, message, type)
  VALUES (p_user_id, p_title, p_message, p_type)
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$;

-- Create secure user rating calculation
CREATE OR REPLACE FUNCTION public.calculate_confidence_score(
  p_venue_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  avg_rating DECIMAL(3,2);
  review_count INTEGER;
  confidence_score DECIMAL(3,2);
BEGIN
  -- Get average rating and count
  SELECT 
    COALESCE(AVG(rating), 0),
    COUNT(*)
  INTO avg_rating, review_count
  FROM reviews 
  WHERE venue_id = p_venue_id;
  
  -- Calculate confidence score (Wilson score interval)
  IF review_count = 0 THEN
    RETURN 0.00;
  END IF;
  
  -- Simple confidence calculation
  confidence_score := avg_rating * (review_count::DECIMAL / (review_count + 10));
  
  RETURN ROUND(confidence_score, 2);
END;
$$;

-- Create secure message count function
CREATE OR REPLACE FUNCTION public.get_unread_message_count(
  p_user_id UUID
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  unread_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO unread_count
  FROM messages 
  WHERE recipient_id = p_user_id 
  AND read_at IS NULL;
  
  RETURN COALESCE(unread_count, 0);
END;
$$;

-- Create secure user rating function
CREATE OR REPLACE FUNCTION public.get_user_average_rating(
  p_user_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  avg_rating DECIMAL(3,2);
BEGIN
  SELECT AVG(rating)
  INTO avg_rating
  FROM reviews r
  JOIN venues v ON r.venue_id = v.id
  WHERE v.host_id = p_user_id;
  
  RETURN COALESCE(avg_rating, 0.00);
END;
$$;

-- Create secure property type detection
CREATE OR REPLACE FUNCTION public.detect_property_type(
  p_venue_id UUID
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  property_type TEXT;
  venue_title TEXT;
  venue_description TEXT;
BEGIN
  -- Get venue details
  SELECT title, description
  INTO venue_title, venue_description
  FROM venues
  WHERE id = p_venue_id;
  
  -- Simple property type detection based on keywords
  IF venue_title ILIKE '%house%' OR venue_description ILIKE '%house%' THEN
    property_type := 'house';
  ELSIF venue_title ILIKE '%apartment%' OR venue_description ILIKE '%apartment%' THEN
    property_type := 'apartment';
  ELSIF venue_title ILIKE '%villa%' OR venue_description ILIKE '%villa%' THEN
    property_type := 'villa';
  ELSIF venue_title ILIKE '%studio%' OR venue_description ILIKE '%studio%' THEN
    property_type := 'studio';
  ELSE
    property_type := 'other';
  END IF;
  
  RETURN property_type;
END;
$$;

-- Create secure curfew info function
CREATE OR REPLACE FUNCTION public.get_curfew_info(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  curfew_info JSONB;
BEGIN
  -- Get curfew information from venue rules
  SELECT jsonb_build_object(
    'has_curfew', CASE WHEN house_rules ILIKE '%quiet%' OR house_rules ILIKE '%curfew%' THEN true ELSE false END,
    'curfew_time', CASE 
      WHEN house_rules ILIKE '%10%pm%' OR house_rules ILIKE '%22:%' THEN '22:00'
      WHEN house_rules ILIKE '%11%pm%' OR house_rules ILIKE '%23:%' THEN '23:00'
      ELSE '22:00'
    END
  )
  INTO curfew_info
  FROM venues
  WHERE id = p_venue_id;
  
  RETURN COALESCE(curfew_info, '{"has_curfew": false}'::jsonb);
END;
$$;

-- Create secure venue availability functions
CREATE OR REPLACE FUNCTION public.check_venue_availability(
  p_venue_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  booking_count INTEGER;
BEGIN
  -- Check for conflicting bookings
  SELECT COUNT(*)
  INTO booking_count
  FROM bookings
  WHERE venue_id = p_venue_id
  AND status IN ('confirmed', 'pending')
  AND (
    (start_date <= p_start_date AND end_date >= p_start_date) OR
    (start_date <= p_end_date AND end_date >= p_end_date) OR
    (start_date >= p_start_date AND end_date <= p_end_date)
  );

  RETURN booking_count = 0;
END;
$$;

-- Create secure venue search function
CREATE OR REPLACE FUNCTION public.get_available_venues(
  p_location TEXT DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL,
  p_max_guests INTEGER DEFAULT NULL
)
RETURNS TABLE (
  venue_id UUID,
  title TEXT,
  location TEXT,
  price_per_hour DECIMAL,
  max_guests INTEGER,
  rating DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
  RETURN QUERY
  SELECT
    v.id,
    v.title,
    v.location,
    v.price_per_hour,
    v.max_guests,
    COALESCE(AVG(r.rating), 0.0) as rating
  FROM venues v
  LEFT JOIN reviews r ON v.id = r.venue_id
  WHERE v.status = 'approved'
  AND (p_location IS NULL OR v.location ILIKE '%' || p_location || '%')
  AND (p_max_guests IS NULL OR v.max_guests >= p_max_guests)
  AND (
    p_start_date IS NULL OR p_end_date IS NULL OR
    check_venue_availability(v.id, p_start_date, p_end_date)
  )
  GROUP BY v.id, v.title, v.location, v.price_per_hour, v.max_guests
  ORDER BY rating DESC, v.created_at DESC;
END;
$$;

-- Create secure booking conflict check
CREATE OR REPLACE FUNCTION public.check_booking_conflicts(
  p_venue_id UUID,
  p_start_date TIMESTAMP,
  p_end_date TIMESTAMP,
  p_exclude_booking_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  conflict_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO conflict_count
  FROM bookings
  WHERE venue_id = p_venue_id
  AND status IN ('confirmed', 'pending')
  AND (p_exclude_booking_id IS NULL OR id != p_exclude_booking_id)
  AND (
    (start_date <= p_start_date AND end_date >= p_start_date) OR
    (start_date <= p_end_date AND end_date >= p_end_date) OR
    (start_date >= p_start_date AND end_date <= p_end_date)
  );

  RETURN conflict_count > 0;
END;
$$;

-- Create secure venue day availability setter
CREATE OR REPLACE FUNCTION public.set_venue_day_availability(
  p_venue_id UUID,
  p_date DATE,
  p_available BOOLEAN,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  is_owner BOOLEAN;
BEGIN
  -- Check if user owns the venue
  SELECT EXISTS(
    SELECT 1 FROM venues
    WHERE id = p_venue_id AND host_id = p_user_id
  ) INTO is_owner;

  IF NOT is_owner THEN
    RAISE EXCEPTION 'Unauthorized: User does not own this venue';
  END IF;

  -- Update or insert availability
  INSERT INTO venue_availability (venue_id, date, available)
  VALUES (p_venue_id, p_date, p_available)
  ON CONFLICT (venue_id, date)
  DO UPDATE SET available = p_available, updated_at = NOW();

  RETURN TRUE;
END;
$$;

-- Create secure operating hours update
CREATE OR REPLACE FUNCTION public.update_venue_operating_hours(
  p_venue_id UUID,
  p_operating_hours JSONB,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  is_owner BOOLEAN;
BEGIN
  -- Check if user owns the venue
  SELECT EXISTS(
    SELECT 1 FROM venues
    WHERE id = p_venue_id AND host_id = p_user_id
  ) INTO is_owner;

  IF NOT is_owner THEN
    RAISE EXCEPTION 'Unauthorized: User does not own this venue';
  END IF;

  -- Update operating hours
  UPDATE venues
  SET operating_hours = p_operating_hours, updated_at = NOW()
  WHERE id = p_venue_id;

  RETURN TRUE;
END;
$$;

-- Grant minimal necessary permissions
GRANT EXECUTE ON FUNCTION public.create_notification TO authenticated;
GRANT EXECUTE ON FUNCTION public.calculate_confidence_score TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_unread_message_count TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_average_rating TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.detect_property_type TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_curfew_info TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.check_venue_availability TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_available_venues TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.check_booking_conflicts TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_venue_day_availability TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_venue_operating_hours TO authenticated;
