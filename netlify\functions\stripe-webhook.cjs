/**
 * Netlify Function: Stripe Webhook Handler
 *
 * This function handles Stripe webhook events to update booking status
 * and send confirmation emails when payments are successful.
 */

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Stripe-Signature',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  const sig = event.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let stripeEvent;

  try {
    // Verify webhook signature
    stripeEvent = stripe.webhooks.constructEvent(event.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'Webhook signature verification failed' }),
    };
  }

  try {
    // Handle the event
    switch (stripeEvent.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(stripeEvent.data.object);
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(stripeEvent.data.object);
        break;
      case 'payment_intent.canceled':
        await handlePaymentIntentCanceled(stripeEvent.data.object);
        break;
      default:
        console.log(`Unhandled event type ${stripeEvent.type}`);
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ received: true }),
    };
  } catch (error) {
    console.error('Error processing webhook:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to process webhook' }),
    };
  }
};

// Handle successful payment
async function handlePaymentIntentSucceeded(paymentIntent) {
  console.log('Payment succeeded:', paymentIntent.id);

  // Extract booking ID from metadata
  const bookingId = paymentIntent.metadata?.booking_id;

  if (bookingId) {
    try {
      // Update booking status in Supabase
      const supabaseUrl = process.env.VITE_SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        console.error('Missing Supabase configuration');
        return;
      }

      // Update booking with payment information
      const response = await fetch(`${supabaseUrl}/rest/v1/bookings?id=eq.${bookingId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseKey}`,
          'apikey': supabaseKey,
        },
        body: JSON.stringify({
          status: 'confirmed',
          payment_status: 'paid',
          payment_id: paymentIntent.id,
          payment_amount: paymentIntent.amount / 100, // Convert from cents
          payment_date: new Date().toISOString(),
          payment_method: 'Stripe',
          updated_at: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update booking: ${response.statusText}`);
      }

      console.log(`Successfully updated booking ${bookingId} to confirmed status`);

      // TODO: Send confirmation emails here
      // await sendBookingConfirmationEmails(bookingId, paymentIntent);

    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  }
}

// Handle failed payment
async function handlePaymentIntentFailed(paymentIntent) {
  console.log('Payment failed:', paymentIntent.id);

  const bookingId = paymentIntent.metadata?.booking_id;

  if (bookingId) {
    try {
      console.log(`Updating booking ${bookingId} to payment_failed status`);
      // await updateBookingStatus(bookingId, 'payment_failed', paymentIntent);
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  }
}

// Handle canceled payment
async function handlePaymentIntentCanceled(paymentIntent) {
  console.log('Payment canceled:', paymentIntent.id);

  const bookingId = paymentIntent.metadata?.booking_id;

  if (bookingId) {
    try {
      console.log(`Updating booking ${bookingId} to canceled status`);
      // await updateBookingStatus(bookingId, 'canceled', paymentIntent);
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  }
}
