import React, { useState } from 'react';
import { Upload, Camera, Shield, CheckCircle, AlertCircle, User, FileText, Phone } from 'lucide-react';

interface VerificationStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  required: boolean;
  completed: boolean;
}

interface CustomerVerificationProps {
  onComplete: (verificationData: any) => void;
  onSkip?: () => void;
  isRequired?: boolean;
}

export default function CustomerVerification({ onComplete, onSkip, isRequired = false }: CustomerVerificationProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [verificationData, setVerificationData] = useState({
    personalInfo: {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      address: '',
      phone: ''
    },
    documents: {
      governmentId: null as File | null,
      selfieWithId: null as File | null,
      governmentIdUrl: '',
      selfieWithIdUrl: ''
    },
    verificationLevel: 'basic' // basic, standard, enhanced
  });

  const verificationSteps: VerificationStep[] = [
    {
      id: 'personal-info',
      title: 'Personal Information',
      description: 'Verify your legal name and contact details',
      icon: <User className="h-5 w-5" />,
      required: true,
      completed: false
    },
    {
      id: 'government-id',
      title: 'Government ID',
      description: 'Upload your Australian driver\'s license or passport',
      icon: <FileText className="h-5 w-5" />,
      required: true,
      completed: false
    },
    {
      id: 'selfie-with-id',
      title: 'Selfie with ID',
      description: 'Take a selfie holding your government ID',
      icon: <Camera className="h-5 w-5" />,
      required: true,
      completed: false
    },
    {
      id: 'phone-verification',
      title: 'Phone Verification',
      description: 'Verify your phone number with SMS code',
      icon: <Phone className="h-5 w-5" />,
      required: true,
      completed: false
    }
  ];

  // Cloudinary upload function
  const uploadToCloudinary = async (file: File, folder: string): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', 'housegoing_verification'); // You'll need to create this preset
    formData.append('folder', `housegoing/verification/${folder}`);

    try {
      const response = await fetch(
        'https://api.cloudinary.com/v1_1/your-cloud-name/image/upload', // Replace with your cloud name
        {
          method: 'POST',
          body: formData,
        }
      );

      const data = await response.json();
      return data.secure_url;
    } catch (error) {
      console.error('Error uploading to Cloudinary:', error);
      throw error;
    }
  };

  const handleFileUpload = async (type: 'governmentId' | 'selfieWithId', file: File) => {
    try {
      // Upload to Cloudinary
      const folder = type === 'governmentId' ? 'government_ids' : 'selfies_with_id';
      const url = await uploadToCloudinary(file, folder);

      setVerificationData(prev => ({
        ...prev,
        documents: {
          ...prev.documents,
          [type]: file,
          [`${type}Url`]: url
        }
      }));
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Error uploading file. Please try again.');
    }
  };

  const handlePersonalInfoChange = (field: string, value: string) => {
    setVerificationData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value
      }
    }));
  };

  const renderPersonalInfoStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shield className="h-12 w-12 text-purple-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Personal Information</h3>
        <p className="text-gray-600">Help us verify your identity with your legal name and contact details</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Legal First Name</label>
          <input
            type="text"
            value={verificationData.personalInfo.firstName}
            onChange={(e) => handlePersonalInfoChange('firstName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
            placeholder="As shown on your ID"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Legal Last Name</label>
          <input
            type="text"
            value={verificationData.personalInfo.lastName}
            onChange={(e) => handlePersonalInfoChange('lastName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
            placeholder="As shown on your ID"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
        <input
          type="date"
          value={verificationData.personalInfo.dateOfBirth}
          onChange={(e) => handlePersonalInfoChange('dateOfBirth', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
        <input
          type="text"
          value={verificationData.personalInfo.address}
          onChange={(e) => handlePersonalInfoChange('address', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
          placeholder="Your current residential address"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
        <input
          type="tel"
          value={verificationData.personalInfo.phone}
          onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
          placeholder="+61 4XX XXX XXX"
        />
      </div>
    </div>
  );

  const renderDocumentUpload = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FileText className="h-12 w-12 text-purple-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Government ID</h3>
        <p className="text-gray-600">Upload a clear photo of your Australian government-issued ID</p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Accepted Documents:</h4>
        <ul className="text-blue-800 text-sm space-y-1">
          <li>• Australian Driver's License</li>
          <li>• Australian Passport</li>
          <li>• Proof of Age Card</li>
          <li>• Foreign Passport (with valid Australian visa)</li>
        </ul>
      </div>

      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
        <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600 mb-2">Click to upload or drag and drop</p>
        <p className="text-sm text-gray-500">PNG, JPG up to 10MB</p>
        <input
          type="file"
          accept="image/*"
          onChange={(e) => e.target.files?.[0] && handleFileUpload('governmentId', e.target.files[0])}
          className="hidden"
          id="government-id-upload"
        />
        <label
          htmlFor="government-id-upload"
          className="mt-4 inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 cursor-pointer"
        >
          Choose File
        </label>
      </div>

      {verificationData.documents.governmentId && (
        <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-green-800">ID uploaded successfully</span>
        </div>
      )}

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2" />
          <div className="text-yellow-800 text-sm">
            <p className="font-medium mb-1">Photo Tips:</p>
            <ul className="space-y-1">
              <li>• Ensure all text is clearly readable</li>
              <li>• Take photo in good lighting</li>
              <li>• Include all four corners of the document</li>
              <li>• Avoid glare and shadows</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSelfieWithIdStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Camera className="h-12 w-12 text-purple-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Selfie with ID</h3>
        <p className="text-gray-600">Take a selfie holding your government ID next to your face</p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Selfie Requirements:</h4>
        <ul className="text-blue-800 text-sm space-y-1">
          <li>• Hold your ID next to your face</li>
          <li>• Ensure both your face and ID are clearly visible</li>
          <li>• Face the camera directly</li>
          <li>• Remove sunglasses and hats</li>
          <li>• Ensure good lighting on both your face and ID</li>
          <li>• Religious head coverings are acceptable</li>
        </ul>
      </div>

      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
        <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600 mb-2">Take a selfie holding your ID</p>
        <p className="text-sm text-gray-500">This photo will be used for verification only</p>
        <input
          type="file"
          accept="image/*"
          capture="user"
          onChange={(e) => e.target.files?.[0] && handleFileUpload('selfieWithId', e.target.files[0])}
          className="hidden"
          id="selfie-with-id-upload"
        />
        <label
          htmlFor="selfie-with-id-upload"
          className="mt-4 inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 cursor-pointer"
        >
          Take Selfie with ID
        </label>
      </div>

      {verificationData.documents.selfieWithId && (
        <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-green-800">Selfie with ID captured successfully</span>
        </div>
      )}

      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-2" />
          <div className="text-red-800 text-sm">
            <p className="font-medium mb-1">Important:</p>
            <p>This photo is used to verify that you are the person shown on the ID. Make sure both your face and the ID are clearly visible and match.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPhoneVerification = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Phone className="h-12 w-12 text-purple-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Phone Verification</h3>
        <p className="text-gray-600">We'll send a verification code to your phone</p>
      </div>

      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <p className="text-gray-700">
          <strong>Phone:</strong> {verificationData.personalInfo.phone || 'Not provided'}
        </p>
      </div>

      <button className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
        Send Verification Code
      </button>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Verification Code</label>
        <input
          type="text"
          placeholder="Enter 6-digit code"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
        />
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0: return renderPersonalInfoStep();
      case 1: return renderDocumentUpload();
      case 2: return renderSelfieWithIdStep();
      case 3: return renderPhoneVerification();
      default: return null;
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return verificationData.personalInfo.firstName && 
               verificationData.personalInfo.lastName && 
               verificationData.personalInfo.dateOfBirth;
      case 1:
        return verificationData.documents.governmentId;
      case 2:
        return verificationData.documents.selfieWithId;
      case 3:
        return true; // Phone verification logic would go here
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < verificationSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(verificationData);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {verificationSteps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-center ${index < verificationSteps.length - 1 ? 'flex-1' : ''}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index <= currentStep
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index < currentStep ? <CheckCircle className="h-4 w-4" /> : index + 1}
              </div>
              {index < verificationSteps.length - 1 && (
                <div
                  className={`flex-1 h-1 mx-2 ${
                    index < currentStep ? 'bg-purple-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <p className="text-sm text-gray-600 text-center">
          Step {currentStep + 1} of {verificationSteps.length}: {verificationSteps[currentStep].title}
        </p>
      </div>

      {/* Current Step Content */}
      <div className="mb-8">
        {renderCurrentStep()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <button
          onClick={handleBack}
          disabled={currentStep === 0}
          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Back
        </button>

        <div className="flex space-x-3">
          {!isRequired && (
            <button
              onClick={onSkip}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Skip for Now
            </button>
          )}
          <button
            onClick={handleNext}
            disabled={!canProceed()}
            className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {currentStep === verificationSteps.length - 1 ? 'Complete Verification' : 'Next'}
          </button>
        </div>
      </div>

      {/* Security Notice */}
      <div className="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="flex items-start">
          <Shield className="h-5 w-5 text-gray-600 mt-0.5 mr-2" />
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-1">Your privacy is protected</p>
            <p>
              Your verification information is encrypted and stored securely on Cloudinary. We only use this information 
              for identity verification and fraud prevention. Your documents are never shared with hosts or other users.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
