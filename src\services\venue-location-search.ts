import { searchNSWSuburbs, NSWSuburb } from './nsw-suburbs-search';
import { MockVenue } from '../data/mockVenues';

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param lat1 Latitude of first point
 * @param lng1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lng2 Longitude of second point
 * @returns Distance in kilometers
 */
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Enhanced venue with distance information
 */
export interface VenueWithDistance extends MockVenue {
  distanceKm: number;
  isWithinRadius: boolean;
  radiusCategory: 'exact' | 'nearby' | 'extended' | 'distant';
}

/**
 * Search venues by location with distance-based filtering and sorting
 */
export async function searchVenuesByLocation(
  venues: MockVenue[],
  locationQuery: string,
  maxRadiusKm: number = 20
): Promise<{
  searchedLocation: NSWSuburb | null;
  exactMatches: VenueWithDistance[];
  nearbyVenues: VenueWithDistance[];
  extendedResults: VenueWithDistance[];
  allResults: VenueWithDistance[];
}> {

  // Step 1: Resolve location query to coordinates using NSW suburbs API
  let searchedLocation: NSWSuburb | null = null;
  let searchLat: number;
  let searchLng: number;

  console.log(`🔍 Starting location search for: "${locationQuery}"`);
  console.log(`📊 Total venues to search: ${venues.length}`);

  try {
    console.log(`🔍 CALLING searchNSWSuburbs with query: "${locationQuery}"`);
    const suburbResults = await searchNSWSuburbs(locationQuery, 1);
    console.log(`🔍 searchNSWSuburbs returned ${suburbResults.length} results:`, suburbResults);

    if (suburbResults.length > 0) {
      searchedLocation = suburbResults[0];
      console.log(`📍 Full location data:`, searchedLocation);

      // Check if coordinates are available
      if (searchedLocation.lat && searchedLocation.lng) {
        searchLat = searchedLocation.lat;
        searchLng = searchedLocation.lng;
        console.log(`✅ NSW API found coordinates for ${locationQuery}: ${searchLat}, ${searchLng}`);
      } else {
        console.warn(`⚠️ NSW API result missing coordinates for ${locationQuery}, using fallback`);
        const fallbackCoords = getFallbackCoordinates(locationQuery);
        if (fallbackCoords) {
          searchLat = fallbackCoords.lat;
          searchLng = fallbackCoords.lng;
          console.log(`🔄 Using fallback coordinates for ${locationQuery}: ${searchLat}, ${searchLng}`);
        } else {
          console.warn(`❌ No fallback coordinates found for location: ${locationQuery}`);
          return {
            searchedLocation: null,
            exactMatches: [],
            nearbyVenues: [],
            extendedResults: [],
            allResults: []
          };
        }
      }
    } else {
      // Fallback to manual coordinate lookup for common areas
      const fallbackCoords = getFallbackCoordinates(locationQuery);
      if (fallbackCoords) {
        searchLat = fallbackCoords.lat;
        searchLng = fallbackCoords.lng;
        console.log(`🔄 Using fallback coordinates for ${locationQuery}: ${searchLat}, ${searchLng}`);
      } else {
        console.warn(`❌ No coordinates found for location: ${locationQuery}`);
        return {
          searchedLocation: null,
          exactMatches: [],
          nearbyVenues: [],
          extendedResults: [],
          allResults: []
        };
      }
    }
  } catch (error) {
    console.error('❌ Error searching NSW suburbs:', error);
    return {
      searchedLocation: null,
      exactMatches: [],
      nearbyVenues: [],
      extendedResults: [],
      allResults: []
    };
  }

  // Step 2: Calculate distances and categorize venues
  console.log(`📏 Calculating distances from search point (${searchLat}, ${searchLng})`);
  const venuesWithDistance: VenueWithDistance[] = venues.map(venue => {
    const distance = calculateDistance(
      searchLat,
      searchLng,
      venue.location.latitude,
      venue.location.longitude
    );

    let radiusCategory: 'exact' | 'nearby' | 'extended' | 'distant';
    if (distance <= 2) {
      radiusCategory = 'exact';  // Same suburb or very close
    } else if (distance <= 10) {
      radiusCategory = 'nearby'; // Within default search radius
    } else if (distance <= 20) {
      radiusCategory = 'extended'; // Outside search radius but reasonable
    } else {
      radiusCategory = 'distant'; // Far away
    }

    console.log(`📍 ${venue.name} (${venue.location.suburb}): ${distance.toFixed(6)}km → ${radiusCategory}`);
    console.log(`   Venue coords: ${venue.location.latitude}, ${venue.location.longitude}`);
    console.log(`   Search coords: ${searchLat}, ${searchLng}`);

    // Special debug for Parramatta
    if (venue.location.suburb === 'Parramatta') {
      console.log(`🚨 PARRAMATTA VENUE DEBUG:`);
      console.log(`   Distance: ${distance.toFixed(6)}km`);
      console.log(`   Category: ${radiusCategory}`);
      console.log(`   Within radius (${maxRadiusKm}km): ${distance <= maxRadiusKm}`);
      console.log(`   Exact match (≤2km): ${distance <= 2}`);
    }

    return {
      ...venue,
      distanceKm: distance,
      isWithinRadius: distance <= maxRadiusKm,
      radiusCategory
    };
  });

  // Step 3: Sort by distance
  venuesWithDistance.sort((a, b) => a.distanceKm - b.distanceKm);
  console.log(`🏆 Closest venue after sorting: ${venuesWithDistance[0]?.name} at ${venuesWithDistance[0]?.distanceKm.toFixed(2)}km`);

  // Step 4: Categorize results
  const exactMatches = venuesWithDistance.filter(v => v.radiusCategory === 'exact');
  const nearbyVenues = venuesWithDistance.filter(v => v.radiusCategory === 'nearby');
  const extendedResults = venuesWithDistance.filter(v => v.radiusCategory === 'extended');

  console.log(`📊 Location search results for "${locationQuery}":`);
  console.log(`- Exact matches (≤2km): ${exactMatches.length}`);
  if (exactMatches.length > 0) {
    exactMatches.forEach(v => console.log(`  ✅ ${v.name}: ${v.distanceKm.toFixed(2)}km`));
  }
  console.log(`- Nearby venues (2-10km): ${nearbyVenues.length}`);
  if (nearbyVenues.length > 0) {
    nearbyVenues.forEach(v => console.log(`  📍 ${v.name}: ${v.distanceKm.toFixed(2)}km`));
  }
  console.log(`- Extended results (10-20km): ${extendedResults.length}`);
  console.log(`- Total within 10km radius: ${exactMatches.length + nearbyVenues.length}`);

  return {
    searchedLocation,
    exactMatches,
    nearbyVenues,
    extendedResults,
    allResults: venuesWithDistance
  };
}

/**
 * Fallback coordinates for common Sydney locations
 */
function getFallbackCoordinates(locationQuery: string): { lat: number; lng: number } | null {
  const fallbackMap: Record<string, { lat: number; lng: number }> = {
    'sydney': { lat: -33.8688, lng: 151.2093 },
    'newtown': { lat: -33.8988, lng: 151.1810 },
    'bondi': { lat: -33.8915, lng: 151.2767 },
    'bondi beach': { lat: -33.8915, lng: 151.2767 },
    'parramatta': { lat: -33.8150, lng: 151.0011 },
    'chatswood': { lat: -33.7967, lng: 151.1800 },
    'ryde': { lat: -33.8149, lng: 151.1056 },
    'manly': { lat: -33.7969, lng: 151.2502 },
    'surry hills': { lat: -33.8886, lng: 151.2094 },
    'cronulla': { lat: -34.0581, lng: 151.1543 },
    'penrith': { lat: -33.7506, lng: 150.6944 }
  };

  const key = locationQuery.toLowerCase().trim();
  return fallbackMap[key] || null;
}

/**
 * Format distance for display
 */
export function formatDistance(distanceKm: number): string {
  if (distanceKm < 1) {
    return `${Math.round(distanceKm * 1000)}m away`;
  } else if (distanceKm < 10) {
    return `${distanceKm.toFixed(1)}km away`;
  } else {
    return `${Math.round(distanceKm)}km away`;
  }
}

/**
 * Get radius category display text
 */
export function getRadiusCategoryText(category: 'exact' | 'nearby' | 'extended' | 'distant'): string {
  switch (category) {
    case 'exact':
      return 'In your area';
    case 'nearby':
      return 'Nearby areas';
    case 'extended':
      return 'Extended search';
    case 'distant':
      return 'Further away';
    default:
      return 'Other areas';
  }
}

/**
 * Test function for debugging Parramatta search
 * Call this from browser console: window.testParramattaSearch()
 */
export async function testParramattaSearch() {
  console.log('🧪 Testing Parramatta search...');

  // Test NSW suburbs search
  const suburbResults = await searchNSWSuburbs('Parramatta', 1);
  console.log('NSW Suburbs results:', suburbResults);

  // Test fallback coordinates
  const fallbackCoords = getFallbackCoordinates('parramatta');
  console.log('Fallback coordinates:', fallbackCoords);

  // Test distance calculation
  if (suburbResults.length > 0 && suburbResults[0].lat && suburbResults[0].lng) {
    const parramattaVenueCoords = { lat: -33.8150, lng: 151.0011 };
    const distance = calculateDistance(
      suburbResults[0].lat,
      suburbResults[0].lng,
      parramattaVenueCoords.lat,
      parramattaVenueCoords.lng
    );
    console.log(`Distance between search result and venue: ${distance.toFixed(4)}km`);

    // Test the full venue search
    const { mockVenues } = await import('../data/mockVenues');
    const parramattaVenue = mockVenues.find(v => v.location.suburb === 'Parramatta');
    if (parramattaVenue) {
      console.log('Found Parramatta venue:', parramattaVenue.name);
      console.log('Venue coordinates:', parramattaVenue.location.latitude, parramattaVenueCoords.lng);

      const venueDistance = calculateDistance(
        suburbResults[0].lat,
        suburbResults[0].lng,
        parramattaVenue.location.latitude,
        parramattaVenue.location.longitude
      );
      console.log(`Distance to actual venue: ${venueDistance.toFixed(4)}km`);

      // Test full location search
      const locationResults = await searchVenuesByLocation(mockVenues, 'Parramatta', 10);
      console.log('Location search results:', locationResults);
    }
  }
}

// Make test function available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testParramattaSearch = testParramattaSearch;

  // Quick NSW suburbs test
  (window as any).testNSWSuburbs = async () => {
    console.log('🧪 Testing NSW suburbs search for Parramatta...');
    try {
      const { searchNSWSuburbs } = await import('./nsw-suburbs-search');
      const results = await searchNSWSuburbs('Parramatta', 1);

      console.log('📊 NSW suburbs search results:', results);
      if (results.length > 0) {
        const first = results[0];
        console.log('✅ First result:', first);
        console.log('📍 Coordinates:', first.lat, first.lng);

        // Compare with venue coordinates
        const venueCoords = { lat: -33.8150, lng: 151.0011 };
        console.log('🏢 Venue coordinates:', venueCoords.lat, venueCoords.lng);

        if (first.lat && first.lng) {
          const distance = calculateDistance(first.lat, first.lng, venueCoords.lat, venueCoords.lng);
          console.log(`📏 Distance between search result and venue: ${distance.toFixed(6)}km`);

          if (distance < 0.1) {
            console.log('✅ Coordinates match (within 100m)');
          } else {
            console.log('❌ Coordinates do not match');
          }
        }
      } else {
        console.log('❌ No results found');
      }
    } catch (error) {
      console.error('Test failed:', error);
    }
  };

  // Quick coordinate test
  (window as any).testCoordinates = () => {
    const parramattaSearch = { lat: -33.8150, lng: 151.0011 };
    const parramattaVenue = { lat: -33.8150, lng: 151.0011 };
    const distance = calculateDistance(
      parramattaSearch.lat, parramattaSearch.lng,
      parramattaVenue.lat, parramattaVenue.lng
    );
    console.log(`Distance between identical coordinates: ${distance}km`);
    console.log(`Should be 0km for exact match`);
  };

  // Quick debug function to test the exact issue
  (window as any).debugParramattaIssue = async () => {
    console.log('🚨 DEBUGGING PARRAMATTA ISSUE...');

    try {
      // Step 1: Check NSW suburbs search
      console.log('\n1️⃣ Testing NSW suburbs search...');
      const { searchNSWSuburbs } = await import('./nsw-suburbs-search');
      const suburbResults = await searchNSWSuburbs('Parramatta', 1);
      console.log('NSW suburbs results:', suburbResults);

      if (suburbResults.length === 0) {
        console.log('❌ ISSUE: NSW suburbs search returns no results for Parramatta');
        return;
      }

      const searchCoords = suburbResults[0];
      console.log('✅ NSW suburbs found:', searchCoords);

      // Step 2: Check mock venues
      console.log('\n2️⃣ Testing mock venues...');
      const { mockVenues } = await import('../data/mockVenues');
      const parramattaVenue = mockVenues.find(v => v.location.suburb === 'Parramatta');

      if (!parramattaVenue) {
        console.log('❌ ISSUE: No Parramatta venue in mock data');
        return;
      }

      console.log('✅ Parramatta venue found:', parramattaVenue.name);
      console.log('Venue coordinates:', parramattaVenue.location.latitude, parramattaVenue.location.longitude);

      // Step 3: Test distance calculation
      console.log('\n3️⃣ Testing distance calculation...');
      if (searchCoords.lat && searchCoords.lng) {
        const distance = calculateDistance(
          searchCoords.lat, searchCoords.lng,
          parramattaVenue.location.latitude, parramattaVenue.location.longitude
        );
        console.log(`Distance: ${distance.toFixed(6)}km`);

        if (distance <= 2) {
          console.log('✅ Distance is within exact match range (≤2km)');
        } else {
          console.log('❌ ISSUE: Distance is too large for exact match');
        }
      }

      // Step 4: Test full search
      console.log('\n4️⃣ Testing full venue search...');
      const { searchVenues } = await import('../data/mockVenues');
      const result = await searchVenues(mockVenues, { location: 'Parramatta' });

      console.log('Search result:', result);
      console.log(`Exact matches: ${result.exactMatches.length}`);
      console.log(`Suggestions: ${result.suggestions.length}`);
      console.log(`Search type: ${result.searchType}`);

      if (result.exactMatches.length === 0) {
        console.log('❌ ISSUE: Full search returns no exact matches');
        console.log('This is the problem we need to fix!');
      } else {
        console.log('✅ Full search working correctly');
      }

    } catch (error) {
      console.error('❌ Error during debugging:', error);
    }
  };

  // Quick venue search test
  (window as any).testVenueSearch = async () => {
    console.log('🧪 Testing venue search for Parramatta...');
    try {
      const { searchVenues } = await import('../data/mockVenues');
      const { mockVenues } = await import('../data/mockVenues');

      console.log('📊 Total mock venues:', mockVenues.length);

      // Find Parramatta venue in mock data
      const parramattaVenue = mockVenues.find(v => v.location.suburb === 'Parramatta');
      if (parramattaVenue) {
        console.log('✅ Found Parramatta venue in mock data:', parramattaVenue.name);
        console.log('📍 Venue coordinates:', parramattaVenue.location.latitude, parramattaVenue.location.longitude);
      } else {
        console.log('❌ No Parramatta venue found in mock data');
        return;
      }

      const result = await searchVenues(mockVenues, { location: 'Parramatta' });
      console.log('🔍 Search result:', result);
      console.log(`📈 Exact matches: ${result.exactMatches.length}`);
      console.log(`📈 Suggestions: ${result.suggestions.length}`);
      console.log(`📈 Search type: ${result.searchType}`);

      if (result.exactMatches.length > 0) {
        console.log('✅ Found exact matches:');
        result.exactMatches.forEach((venue, index) => {
          console.log(`  ${index + 1}. ${venue.name} (${venue.location.suburb})`);
          if ((venue as any).distanceKm !== undefined) {
            console.log(`     Distance: ${(venue as any).distanceKm.toFixed(4)}km`);
          }
        });
      } else {
        console.log('❌ No exact matches found');
        if (result.suggestions.length > 0) {
          console.log('💡 Suggestions found:');
          result.suggestions.slice(0, 3).forEach((venue, index) => {
            console.log(`  ${index + 1}. ${venue.name} (${venue.location.suburb})`);
            if ((venue as any).distanceKm !== undefined) {
              console.log(`     Distance: ${(venue as any).distanceKm.toFixed(2)}km`);
            }
          });
        }
      }
    } catch (error) {
      console.error('Test failed:', error);
    }
  };

  // Test all suburbs
  (window as any).testAllSuburbs = async () => {
    console.log('🧪 Testing all suburb searches...');
    const suburbs = ['Sydney', 'Bondi Beach', 'Newtown', 'Parramatta', 'Chatswood', 'Cronulla', 'Ryde', 'Penrith'];

    try {
      const { searchVenues } = await import('../data/mockVenues');
      const { mockVenues } = await import('../data/mockVenues');

      for (const suburb of suburbs) {
        console.log(`\n🔍 Testing ${suburb}...`);
        const result = await searchVenues(mockVenues, { location: suburb });

        const venueInSuburb = mockVenues.find(v => v.location.suburb === suburb);
        if (venueInSuburb) {
          const foundInExact = result.exactMatches.find(v => v.id === venueInSuburb.id);
          if (foundInExact) {
            console.log(`✅ ${suburb}: ${venueInSuburb.name} found in exact matches`);
            if ((foundInExact as any).distanceKm !== undefined) {
              console.log(`   Distance: ${(foundInExact as any).distanceKm.toFixed(2)}km`);
            }
          } else {
            console.log(`❌ ${suburb}: ${venueInSuburb.name} NOT found in exact matches`);
            console.log(`   Total exact matches: ${result.exactMatches.length}`);
            console.log(`   Total suggestions: ${result.suggestions.length}`);
          }
        } else {
          console.log(`ℹ️ ${suburb}: No venue in mock data for this suburb`);
        }
      }
    } catch (error) {
      console.error('Test failed:', error);
    }
  };

  // Test the new coordinate system
  (window as any).testNewCoordinateSystem = async () => {
    console.log('🧪 Testing new coordinate system...');

    try {
      const { assignVenueCoordinates } = await import('./venue-coordinates');
      const { auditMockVenues } = await import('../scripts/fix-venue-coordinates');

      console.log('\n1. Testing coordinate assignment...');
      const testCoords = await assignVenueCoordinates({
        address: '123 Test Street, Parramatta NSW 2150',
        suburb: 'Parramatta',
        postcode: '2150',
        state: 'NSW'
      });

      console.log(`✅ Parramatta coordinates: ${testCoords.latitude}, ${testCoords.longitude}`);
      console.log(`   Source: ${testCoords.source}, Accuracy: ${testCoords.accuracy}`);

      console.log('\n2. Auditing mock venues...');
      const auditResults = await auditMockVenues();

      console.log('\n3. Testing venue search with new system...');
      await (window as any).testAllSuburbs();

      console.log('\n✅ New coordinate system test complete!');
      console.log(`- Coordinate assignment: Working`);
      console.log(`- Mock venue audit: ${auditResults.length} issues found`);
      console.log(`- Location search: Tested all suburbs`);

    } catch (error) {
      console.error('❌ New coordinate system test failed:', error);
    }
  };
}
