import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import axios from 'axios';
import { FiSend } from 'react-icons/fi';
import { BsRobot } from 'react-icons/bs';
import { FaUser } from 'react-icons/fa';
import { aiService } from '../../services/aiService';

export const HostAIAssistant = forwardRef(({ context = 'general', isMaximized = false }, ref) => {
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'G\'day! 🏠✨ I\'m <PERSON><PERSON>, your friendly HouseGoing Host Assistant! I\'m here to help make your venue absolutely amazing. I can help with venue optimization, Party Score calculations, pricing strategies, and heaps more! How can I help you shine today? 🌟' }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const messagesEndRef = useRef(null);

  const loadingMessages = [
    "Checking local venue regulations...",
    "Calculating optimal pricing...",
    "Analyzing similar venues in your area...",
    "Crunching the numbers...",
    "Checking Party Score factors...",
    "Looking up booking trends...",
    "Finding optimization opportunities..."
  ];

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    handleSuggestion: (suggestion) => {
      if (suggestion) {
        setInput(suggestion);
        sendMessage(suggestion);
      }
    }
  }));

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const sendMessage = async (customMessage) => {
    const messageToSend = customMessage || input;
    if (!messageToSend.trim()) return;

    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: messageToSend }]);
    setLoading(true);

    // Show random loading message
    const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
    setLoadingMessage(randomMessage);

    try {
      // Prepare context for host assistance
      const hostContext = {
        propertyCount: 2, // Could be dynamic from props
        recentBookings: 5, // Could be dynamic from props
        earnings: '$1,200', // Could be dynamic from props
        currentTask: context
      };

      // Call AI service for host assistance
      const response = await aiService.getHostAssistance(messageToSend, hostContext);

      if (response.success && response.message) {
        // Add assistant response
        setMessages(prev => [...prev, { role: 'assistant', content: response.message }]);
      } else {
        // Fallback response
        setMessages(prev => [...prev, { role: 'assistant', content: 'I\'m here to help with your hosting needs! Could you tell me more about what you\'d like assistance with?' }]);
      }

      // Save session ID (for future use)
      const sessionId = localStorage.getItem('hostAssistantSessionId') || Date.now().toString();
      localStorage.setItem('hostAssistantSessionId', sessionId);

    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => [...prev, { role: 'assistant', content: 'Sorry, I ran into a problem. Please try again later.' }]);
    } finally {
      setLoading(false);
      setLoadingMessage('');
      setInput('');
    }
  };

  return (
    <div className={`flex flex-col ${isMaximized ? 'h-full' : 'h-[600px]'} border rounded-lg overflow-hidden shadow-md`}>
      <div className="bg-purple-700 text-white p-4 flex items-center">
        <BsRobot className="text-xl mr-2" />
        <div>
          <h2 className="font-bold">Host Assistant</h2>
          <p className="text-xs opacity-80">Powered by AI</p>
        </div>
      </div>

      <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
        {messages.map((message, index) => (
          <div key={index} className={`mb-4 ${message.role === 'user' ? 'text-right' : ''}`}>
            <div className={`inline-block max-w-[80%] p-3 rounded-lg ${
              message.role === 'user'
                ? 'bg-purple-600 text-white rounded-tr-none'
                : 'bg-white border border-gray-200 rounded-tl-none'
            }`}>
              {message.content}
            </div>
          </div>
        ))}

        {loading && (
          <div className="mb-4">
            <div className="inline-block max-w-[80%] p-3 rounded-lg bg-white border border-gray-200 rounded-tl-none">
              <p className="text-gray-500 mb-1">{loadingMessage}</p>
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-purple-300 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce delay-100"></div>
                <div className="w-2 h-2 bg-purple-700 rounded-full animate-bounce delay-200"></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="border-t p-4 bg-white">
        <div className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            placeholder="Ask about venue optimization, Party Score, pricing..."
            className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
          <button
            onClick={sendMessage}
            disabled={loading}
            className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
          >
            <FiSend size={20} />
          </button>
        </div>
      </div>
    </div>
  );
});
