/**
 * Mobile-specific analytics tracking utilities
 * Tracks mobile user interactions and performance metrics
 */

interface MobileAnalyticsEvent {
  event: string;
  properties: {
    device_type: 'mobile' | 'tablet' | 'desktop';
    screen_size: string;
    touch_enabled: boolean;
    connection_type?: string;
    viewport_width: number;
    viewport_height: number;
    user_agent: string;
    timestamp: number;
    [key: string]: any;
  };
}

class MobileAnalytics {
  private static instance: MobileAnalytics;
  private isInitialized = false;

  static getInstance(): MobileAnalytics {
    if (!MobileAnalytics.instance) {
      MobileAnalytics.instance = new MobileAnalytics();
    }
    return MobileAnalytics.instance;
  }

  initialize() {
    if (this.isInitialized) return;
    
    this.setupMobileEventListeners();
    this.trackDeviceInfo();
    this.isInitialized = true;
  }

  private getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  private getConnectionType(): string {
    // @ts-ignore - navigator.connection is experimental
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    return connection?.effectiveType || 'unknown';
  }

  private setupMobileEventListeners() {
    // Track touch interactions
    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
    document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    
    // Track orientation changes
    window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
    
    // Track scroll performance
    let scrollTimeout: NodeJS.Timeout;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.trackEvent('mobile_scroll_end', {
          scroll_position: window.scrollY,
          page_height: document.body.scrollHeight
        });
      }, 150);
    }, { passive: true });
  }

  private handleTouchStart(event: TouchEvent) {
    const target = event.target as HTMLElement;
    const touchCount = event.touches.length;
    
    this.trackEvent('mobile_touch_start', {
      element_type: target.tagName.toLowerCase(),
      element_class: target.className,
      touch_count: touchCount,
      target_size: {
        width: target.offsetWidth,
        height: target.offsetHeight
      }
    });
  }

  private handleTouchEnd(event: TouchEvent) {
    const target = event.target as HTMLElement;
    
    this.trackEvent('mobile_touch_end', {
      element_type: target.tagName.toLowerCase(),
      element_class: target.className
    });
  }

  private handleOrientationChange() {
    setTimeout(() => {
      this.trackEvent('mobile_orientation_change', {
        orientation: window.orientation,
        new_width: window.innerWidth,
        new_height: window.innerHeight
      });
    }, 100); // Delay to get accurate dimensions
  }

  private trackDeviceInfo() {
    this.trackEvent('mobile_device_info', {
      screen_resolution: `${screen.width}x${screen.height}`,
      pixel_ratio: window.devicePixelRatio,
      platform: navigator.platform,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    });
  }

  trackEvent(eventName: string, additionalProperties: Record<string, any> = {}) {
    const event: MobileAnalyticsEvent = {
      event: eventName,
      properties: {
        device_type: this.getDeviceType(),
        screen_size: `${window.innerWidth}x${window.innerHeight}`,
        touch_enabled: 'ontouchstart' in window,
        connection_type: this.getConnectionType(),
        viewport_width: window.innerWidth,
        viewport_height: window.innerHeight,
        user_agent: navigator.userAgent,
        timestamp: Date.now(),
        ...additionalProperties
      }
    };

    // Send to analytics service (replace with your analytics provider)
    this.sendToAnalytics(event);
  }

  trackMobileSearchInteraction(searchType: 'expand' | 'collapse' | 'search' | 'filter') {
    this.trackEvent('mobile_search_interaction', {
      interaction_type: searchType,
      search_expanded: document.querySelector('.mobile-search-expanded') !== null
    });
  }

  trackMobileMenuInteraction(action: 'open' | 'close' | 'navigate') {
    this.trackEvent('mobile_menu_interaction', {
      action,
      menu_visible: document.querySelector('.mobile-menu') !== null
    });
  }

  trackMobilePerformance() {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.trackEvent('mobile_performance', {
        page_load_time: navigation.loadEventEnd - navigation.loadEventStart,
        dom_content_loaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        first_paint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        first_contentful_paint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      });
    }
  }

  private sendToAnalytics(event: MobileAnalyticsEvent) {
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📱 Mobile Analytics:', event);
    }

    // Send to your analytics service
    // Example: Google Analytics, Mixpanel, etc.
    try {
      // Replace with your analytics implementation
      if (typeof gtag !== 'undefined') {
        gtag('event', event.event, event.properties);
      }
    } catch (error) {
      console.warn('Failed to send mobile analytics event:', error);
    }
  }
}

// Export singleton instance
export const mobileAnalytics = MobileAnalytics.getInstance();

// Auto-initialize on mobile devices
if (typeof window !== 'undefined' && window.innerWidth < 768) {
  mobileAnalytics.initialize();
}

export default mobileAnalytics;
