import { useEffect } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { analytics, AnalyticsEventType, AnalyticsEventProperties } from '../services/analytics';

export function useAnalytics() {
  const { user } = useUser();
  const { isSignedIn, isLoaded } = useAuth();

  // Track sign in and sign out events
  useEffect(() => {
    if (!isLoaded) return;

    if (isSignedIn && user) {
      // User signed in
      analytics.trackSignIn({
        userId: user.id,
        email: user.primaryEmailAddress?.emailAddress,
      });
    }

    // Cleanup function to track sign out
    return () => {
      if (isSignedIn && user) {
        analytics.trackSignOut({
          userId: user.id,
          email: user.primaryEmailAddress?.emailAddress,
        });
      }
    };
  }, [isSignedIn, isLoaded, user]);

  // Return the analytics service
  return {
    track: (eventType: AnalyticsEventType, properties: AnalyticsEventProperties = {}) => {
      // Add user information to properties if available
      if (user) {
        properties.userId = properties.userId || user.id;
        properties.email = properties.email || user.primaryEmailAddress?.emailAddress;
      }
      
      analytics.track(eventType, properties);
    },
    trackSignUp: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackSignUp({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
    trackSignIn: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackSignIn({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
    trackSignOut: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackSignOut({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
    trackPasswordReset: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackPasswordReset({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
    trackEmailVerification: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackEmailVerification({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
    trackProfileUpdate: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackProfileUpdate({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
    trackSocialSignIn: (properties: AnalyticsEventProperties = {}) => {
      analytics.trackSocialSignIn({
        ...properties,
        userId: user?.id,
        email: user?.primaryEmailAddress?.emailAddress,
      });
    },
  };
}
