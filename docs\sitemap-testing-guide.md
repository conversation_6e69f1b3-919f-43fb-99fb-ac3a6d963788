# HouseGoing Sitemap Testing and Submission Guide

This guide explains how to test your sitemap index and main sitemap locally and submit them to Google Search Console.

## Testing Your Sitemap Locally

We've added several tools to help you test and validate your sitemap:

### 1. Validate the Sitemap Files

Run the validation script to check if your sitemap index and main sitemap are properly formatted:

```bash
npm run validate-sitemap
```

This will:
- Check the XML structure of both files
- Validate all URLs in the main sitemap
- Validate all sitemap references in the index
- Remove any comments or unnecessary whitespace
- Report any errors found

### 2. Test the Sitemap Locally

To test how the sitemap will be served in production:

```bash
npm run test-sitemap
```

This will:
- Start a local server on port 3000
- Serve both your sitemap_index.xml and sitemap_main.xml files with the correct content type
- Provide a simple web interface to view and test both sitemap files

Visit http://localhost:3000 in your browser to access the test interface with tabs for both sitemap files.

### 3. Check Content Type

When viewing your sitemap in the browser:

1. Open the browser's developer tools (F12 or right-click > Inspect)
2. Go to the Network tab
3. Refresh the page
4. Click on the sitemap_index.xml request
5. Check that the "Content-Type" header is set to "application/xml"
6. Repeat steps 4-5 for sitemap_main.xml

## Fixing Common Sitemap Issues

If you encounter the "Your Sitemap appears to be an HTML page" or "The type must be sitemap index" error in Google Search Console:

### Issue 1: Incorrect Content Type

The server might be serving the sitemap with the wrong content type (e.g., text/html instead of application/xml).

**Solution**: We've added middleware in `src/server/sitemap-middleware.js` that ensures both sitemap files are served with the correct content type.

### Issue 2: HTML in the Sitemap

The sitemap files might contain HTML tags or comments.

**Solution**: The validation script removes comments and ensures proper XML formatting for both files.

### Issue 3: Wrong Sitemap Type

Google expects a sitemap index file when submitting sitemap_index.xml.

**Solution**: We've created a proper sitemap index file that points to the main sitemap file.

### Issue 4: XML Declaration Missing

The sitemap files must start with an XML declaration.

**Solution**: Our sitemap generation script ensures the XML declaration is present in both files.

## Submitting Your Sitemap to Google Search Console

After fixing any issues:

1. Go to [Google Search Console](https://search.google.com/search-console)
2. Select your property
3. Click on "Sitemaps" in the left sidebar
4. Enter "sitemap_index.xml" in the "Add a new sitemap" field
5. Click "Submit"

## Testing in Production

After deploying:

1. Visit https://housegoing.com.au/sitemap_index.xml directly
2. Visit https://housegoing.com.au/sitemap_main.xml directly
2. Check that it displays as XML, not HTML
3. Use a tool like [XML Sitemap Validator](https://www.xml-sitemaps.com/validate-xml-sitemap.html) to verify it

## Troubleshooting

If issues persist:

1. Check the server logs for any errors
2. Verify that both sitemap_index.xml and sitemap_main.xml files exist in the dist directory after building
3. Ensure the middleware is correctly set up to serve both sitemap files with the proper content type
4. Try clearing your browser cache or testing in an incognito window
