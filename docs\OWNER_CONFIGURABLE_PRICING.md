# Owner-Configurable Pricing System - HouseGoing

## Overview

The HouseGoing platform now features a **completely owner-configurable pricing system** that allows venue owners to set their own progressive pricing tiers and surcharges, rather than using hardcoded platform defaults.

## Key Features

### ✅ **Owner-Controlled Progressive Pricing**
- Venue owners can create custom pricing tiers based on booking duration
- Each tier can have different discount percentages and custom labels
- Owners can enable/disable progressive pricing entirely
- Flexible tier configuration (min/max hours, discount %, custom labels)

### ✅ **Customizable Surcharges**
- **Weekend Surcharge**: Owner-defined percentage for Friday-Sunday bookings
- **Holiday Surcharge**: Custom rates for public holidays
- **Late Night Surcharge**: Configurable rates for bookings after 10 PM
- **Overtime Surcharge**: Optional surcharge for bookings over 12 hours

### ✅ **Fallback System**
- If owners haven't configured pricing, system uses sensible defaults
- Seamless transition between owner-configured and default pricing
- No disruption to existing bookings

## Implementation Details

### Data Structure

```typescript
interface VenuePricingConfig {
  progressivePricing?: {
    enabled: boolean;
    tiers: Array<{
      minHours: number;
      maxHours?: number;
      discountPercentage: number;
      label: string;
    }>;
  };
  surcharges?: {
    weekend?: number;    // percentage
    holiday?: number;    // percentage
    lateNight?: number;  // percentage
    overtime?: number;   // percentage
  };
}
```

### Example Owner Configuration

```javascript
// Venue with aggressive progressive pricing
{
  progressivePricing: {
    enabled: true,
    tiers: [
      { minHours: 1, maxHours: 3, discountPercentage: 0, label: "Standard Rate" },
      { minHours: 4, maxHours: 6, discountPercentage: 5, label: "4-6 Hours (5% off)" },
      { minHours: 7, maxHours: 10, discountPercentage: 10, label: "7-10 Hours (10% off)" },
      { minHours: 11, discountPercentage: 15, label: "11+ Hours (15% off)" }
    ]
  },
  surcharges: {
    weekend: 20,   // 20% weekend surcharge
    holiday: 30,   // 30% holiday surcharge
    lateNight: 15, // 15% late night surcharge
    overtime: 25   // 25% overtime surcharge
  }
}

// Beach venue with no progressive pricing, just premium surcharges
{
  progressivePricing: {
    enabled: false,
    tiers: []
  },
  surcharges: {
    weekend: 25,   // Higher weekend premium for beach location
    holiday: 40,   // Premium holiday pricing
    lateNight: 20  // Beach house late night premium
  }
}
```

## Owner Portal Integration

### PricingConfiguration Component

The `PricingConfiguration` component provides a comprehensive interface for venue owners to:

1. **Enable/Disable Progressive Pricing**
2. **Add/Remove Pricing Tiers** with:
   - Minimum hours
   - Maximum hours (optional)
   - Discount percentage
   - Custom label
3. **Configure Surcharges** for:
   - Weekend bookings
   - Holiday bookings
   - Late night bookings
   - Overtime bookings

### Usage in Owner Portal

```tsx
import PricingConfiguration from '../components/host/PricingConfiguration';

<PricingConfiguration
  initialConfig={venue.pricing}
  onSave={(config) => updateVenuePricing(venue.id, config)}
  onCancel={() => setShowPricingConfig(false)}
/>
```

## Pricing Calculation Flow

### 1. **Progressive Pricing Application**
```typescript
// Check if venue has custom progressive pricing
if (venuePricingConfig?.progressivePricing?.enabled) {
  // Use venue-specific tiers
  const applicableTier = venuePricingConfig.progressivePricing.tiers
    .filter(tier => hours >= tier.minHours && (!tier.maxHours || hours <= tier.maxHours))
    .sort((a, b) => b.discountPercentage - a.discountPercentage)[0];
  
  if (applicableTier) {
    progressiveDiscount = applicableTier.discountPercentage;
    progressiveMultiplier = 1 - (progressiveDiscount / 100);
  }
} else {
  // Fallback to default progressive pricing
  progressiveMultiplier = getDefaultProgressiveMultiplier(hours);
}
```

### 2. **Surcharge Application**
```typescript
// Apply venue-specific surcharges or defaults
const surcharges = {
  weekend: isWeekend ? 
    subtotal * ((venuePricingConfig?.surcharges?.weekend ?? 20) / 100) : 0,
  holiday: isHoliday ? 
    subtotal * ((venuePricingConfig?.surcharges?.holiday ?? 30) / 100) : 0,
  lateNight: isLateNight ? 
    subtotal * ((venuePricingConfig?.surcharges?.lateNight ?? 15) / 100) : 0,
  overtime: isOvertime && venuePricingConfig?.surcharges?.overtime ? 
    subtotal * (venuePricingConfig.surcharges.overtime / 100) : 0
};
```

## Database Schema Updates

### Venues Table Enhancement
```sql
-- Add pricing configuration to venues table
ALTER TABLE venues ADD COLUMN pricing_config JSONB;

-- Example pricing_config structure:
{
  "progressivePricing": {
    "enabled": true,
    "tiers": [
      {
        "minHours": 1,
        "maxHours": 3,
        "discountPercentage": 0,
        "label": "Standard Rate"
      },
      {
        "minHours": 4,
        "maxHours": 6,
        "discountPercentage": 5,
        "label": "4-6 Hours (5% off)"
      }
    ]
  },
  "surcharges": {
    "weekend": 20,
    "holiday": 30,
    "lateNight": 15,
    "overtime": 25
  }
}
```

## Benefits for Venue Owners

### 🎯 **Complete Control**
- Set pricing strategy that matches their business model
- Adjust rates based on venue type, location, and market demand
- Create competitive advantages through strategic pricing

### 💰 **Revenue Optimization**
- Encourage longer bookings with progressive discounts
- Capture premium pricing during high-demand periods
- Optimize pricing for different customer segments

### 🔧 **Flexibility**
- Change pricing strategy anytime without platform limitations
- Test different pricing models to find optimal rates
- Respond quickly to market changes

## Benefits for Platform

### 🏆 **Competitive Advantage**
- More flexible than competitors with fixed pricing models
- Attracts venue owners who want pricing control
- Enables diverse venue types with different pricing needs

### 📈 **Higher Conversion**
- Owners can optimize pricing for their specific market
- Better pricing leads to more bookings
- Increased platform revenue through higher booking volumes

## Testing Examples

### Test Venue 1: Harbour View Rooftop (venue-001)
- **Progressive Pricing**: Enabled with 4 tiers (0%, 5%, 10%, 15% discounts)
- **Surcharges**: Weekend 20%, Holiday 30%, Late Night 15%, Overtime 25%
- **Base Rate**: $180/hour

### Test Venue 2: Bondi Beach House (venue-002)
- **Progressive Pricing**: Disabled
- **Surcharges**: Weekend 25%, Holiday 40%, Late Night 20%
- **Base Rate**: $120/hour

### Testing Scenarios
1. **Short Booking (2 hours)**: Standard rate, no progressive discount
2. **Medium Booking (6 hours)**: Progressive discount applied if enabled
3. **Long Booking (12+ hours)**: Maximum progressive discount + overtime surcharge
4. **Weekend Booking**: Weekend surcharge applied
5. **Holiday Booking**: Holiday surcharge applied
6. **Late Night Booking**: Late night surcharge applied

## Migration Strategy

### Phase 1: Backward Compatibility ✅
- All existing venues continue working with default pricing
- New pricing system runs alongside existing system
- No disruption to current bookings

### Phase 2: Owner Onboarding
- Add pricing configuration to owner portal
- Provide guided setup for new pricing features
- Educational content about pricing optimization

### Phase 3: Advanced Features
- Pricing analytics and recommendations
- Seasonal pricing adjustments
- Dynamic pricing based on demand

## Future Enhancements

### 🔮 **Planned Features**
- **Seasonal Pricing**: Different rates for different seasons
- **Dynamic Pricing**: AI-powered pricing recommendations
- **Bulk Discounts**: Special rates for multiple venue bookings
- **Loyalty Pricing**: Discounts for repeat customers
- **Event-Based Pricing**: Special rates for specific event types

### 📊 **Analytics Integration**
- Pricing performance metrics
- Revenue optimization suggestions
- Competitive pricing analysis
- Booking conversion tracking by pricing tier

## Conclusion

The owner-configurable pricing system transforms HouseGoing from a platform with fixed pricing to a flexible marketplace where venue owners have complete control over their pricing strategy. This change:

1. **Empowers venue owners** with pricing flexibility
2. **Increases platform competitiveness** in the market
3. **Enables revenue optimization** for both owners and platform
4. **Maintains backward compatibility** with existing venues
5. **Provides foundation** for advanced pricing features

The system is now **production-ready** and can be immediately deployed to give venue owners the pricing control they need to succeed on the platform.
