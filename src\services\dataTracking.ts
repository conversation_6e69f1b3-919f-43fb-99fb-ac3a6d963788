/**
 * Data Tracking Service - <PERSON> Inspired Event Tracking
 * 
 * Tracks key business events for analytics and growth optimization:
 * - User acquisition and conversion events
 * - Revenue and booking events
 * - Host onboarding and performance events
 * - Customer lifecycle events
 */

import { getSupabaseClient } from '../lib/supabase-client';

// Event Types for Business Analytics
export interface BusinessEvent {
  id?: string;
  event_type: string;
  event_category: 'acquisition' | 'conversion' | 'revenue' | 'retention' | 'churn';
  user_id?: string;
  session_id?: string;
  properties: Record<string, any>;
  revenue_impact?: number;
  created_at?: string;
}

// Key Business Events to Track
export const EVENT_TYPES = {
  // Acquisition Events
  USER_SIGNUP: 'user_signup',
  HOST_SIGNUP: 'host_signup',
  PROPERTY_SUBMISSION: 'property_submission',
  
  // Conversion Events
  FIRST_BOOKING: 'first_booking',
  BOOKING_COMPLETED: 'booking_completed',
  PAYMENT_COMPLETED: 'payment_completed',
  
  // Revenue Events
  COMMISSION_EARNED: 'commission_earned',
  HOST_PAYOUT: 'host_payout',
  REFUND_PROCESSED: 'refund_processed',
  
  // Retention Events
  REPEAT_BOOKING: 'repeat_booking',
  HOST_REACTIVATION: 'host_reactivation',
  CUSTOMER_RETURN: 'customer_return',
  
  // Churn Events
  BOOKING_CANCELLED: 'booking_cancelled',
  HOST_INACTIVE: 'host_inactive',
  CUSTOMER_INACTIVE: 'customer_inactive'
} as const;

/**
 * Track a business event
 */
export async function trackEvent(
  eventType: string,
  category: BusinessEvent['event_category'],
  properties: Record<string, any> = {},
  userId?: string,
  revenueImpact?: number
): Promise<void> {
  try {
    const supabase = getSupabaseClient();
    
    const event: BusinessEvent = {
      event_type: eventType,
      event_category: category,
      user_id: userId,
      session_id: generateSessionId(),
      properties: {
        ...properties,
        timestamp: new Date().toISOString(),
        user_agent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
        url: typeof window !== 'undefined' ? window.location.href : 'server'
      },
      revenue_impact: revenueImpact,
      created_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('business_events')
      .insert(event);

    if (error) {
      console.error('Error tracking event:', error);
    } else {
      console.log(`📊 Event tracked: ${eventType}`, properties);
    }
  } catch (error) {
    console.error('Exception tracking event:', error);
  }
}

/**
 * Track user signup event
 */
export async function trackUserSignup(
  userId: string,
  userType: 'customer' | 'host',
  signupMethod: 'email' | 'google' | 'facebook',
  referralSource?: string
): Promise<void> {
  await trackEvent(
    userType === 'host' ? EVENT_TYPES.HOST_SIGNUP : EVENT_TYPES.USER_SIGNUP,
    'acquisition',
    {
      user_type: userType,
      signup_method: signupMethod,
      referral_source: referralSource,
      onboarding_step: 'completed'
    },
    userId
  );
}

/**
 * Track property submission
 */
export async function trackPropertySubmission(
  hostId: string,
  propertyType: string,
  pricePerHour: number,
  location: string
): Promise<void> {
  await trackEvent(
    EVENT_TYPES.PROPERTY_SUBMISSION,
    'conversion',
    {
      property_type: propertyType,
      price_per_hour: pricePerHour,
      location,
      submission_step: 'completed'
    },
    hostId
  );
}

/**
 * Track booking completion
 */
export async function trackBookingCompleted(
  customerId: string,
  hostId: string,
  bookingValue: number,
  isFirstBooking: boolean,
  venueType: string,
  bookingDuration: number
): Promise<void> {
  const eventType = isFirstBooking ? EVENT_TYPES.FIRST_BOOKING : EVENT_TYPES.BOOKING_COMPLETED;
  const category = isFirstBooking ? 'conversion' : 'retention';
  
  await trackEvent(
    eventType,
    category,
    {
      host_id: hostId,
      booking_value: bookingValue,
      venue_type: venueType,
      booking_duration_hours: bookingDuration,
      is_first_booking: isFirstBooking,
      payment_method: 'stripe' // Default for now
    },
    customerId,
    bookingValue
  );

  // Also track commission earned
  const commission = bookingValue * 0.10; // 10% commission
  await trackEvent(
    EVENT_TYPES.COMMISSION_EARNED,
    'revenue',
    {
      booking_id: `booking_${Date.now()}`,
      commission_rate: 0.10,
      gross_booking_value: bookingValue
    },
    undefined,
    commission
  );
}

/**
 * Track payment completion
 */
export async function trackPaymentCompleted(
  customerId: string,
  amount: number,
  paymentMethod: string,
  bookingId: string
): Promise<void> {
  await trackEvent(
    EVENT_TYPES.PAYMENT_COMPLETED,
    'revenue',
    {
      booking_id: bookingId,
      payment_method: paymentMethod,
      amount,
      currency: 'AUD',
      payment_status: 'completed'
    },
    customerId,
    amount
  );
}

/**
 * Track customer churn indicators
 */
export async function trackCustomerInactivity(
  customerId: string,
  daysSinceLastBooking: number,
  totalBookings: number,
  totalSpent: number
): Promise<void> {
  await trackEvent(
    EVENT_TYPES.CUSTOMER_INACTIVE,
    'churn',
    {
      days_since_last_booking: daysSinceLastBooking,
      total_bookings: totalBookings,
      total_spent: totalSpent,
      churn_risk_level: daysSinceLastBooking > 90 ? 'high' : daysSinceLastBooking > 60 ? 'medium' : 'low'
    },
    customerId
  );
}

/**
 * Track host performance events
 */
export async function trackHostPerformance(
  hostId: string,
  metrics: {
    totalBookings: number;
    totalRevenue: number;
    averageRating: number;
    responseRate: number;
    activeListings: number;
  }
): Promise<void> {
  await trackEvent(
    'host_performance_update',
    'retention',
    {
      total_bookings: metrics.totalBookings,
      total_revenue: metrics.totalRevenue,
      average_rating: metrics.averageRating,
      response_rate: metrics.responseRate,
      active_listings: metrics.activeListings,
      performance_tier: getHostPerformanceTier(metrics)
    },
    hostId
  );
}

/**
 * Get events for analytics
 */
export async function getEventsByType(
  eventType: string,
  startDate?: string,
  endDate?: string
): Promise<BusinessEvent[]> {
  try {
    const supabase = getSupabaseClient();
    
    let query = supabase
      .from('business_events')
      .select('*')
      .eq('event_type', eventType)
      .order('created_at', { ascending: false });

    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching events:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching events:', error);
    return [];
  }
}

/**
 * Get revenue events for a time period
 */
export async function getRevenueEvents(
  startDate: string,
  endDate: string
): Promise<BusinessEvent[]> {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('business_events')
      .select('*')
      .eq('event_category', 'revenue')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching revenue events:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching revenue events:', error);
    return [];
  }
}

/**
 * Calculate conversion funnel metrics
 */
export async function getConversionFunnel(
  startDate: string,
  endDate: string
): Promise<{
  signups: number;
  propertySubmissions: number;
  firstBookings: number;
  repeatBookings: number;
  conversionRates: {
    signupToSubmission: number;
    submissionToBooking: number;
    firstToRepeat: number;
  };
}> {
  try {
    const [signups, submissions, firstBookings, repeatBookings] = await Promise.all([
      getEventsByType(EVENT_TYPES.USER_SIGNUP, startDate, endDate),
      getEventsByType(EVENT_TYPES.PROPERTY_SUBMISSION, startDate, endDate),
      getEventsByType(EVENT_TYPES.FIRST_BOOKING, startDate, endDate),
      getEventsByType(EVENT_TYPES.REPEAT_BOOKING, startDate, endDate)
    ]);

    const signupCount = signups.length;
    const submissionCount = submissions.length;
    const firstBookingCount = firstBookings.length;
    const repeatBookingCount = repeatBookings.length;

    return {
      signups: signupCount,
      propertySubmissions: submissionCount,
      firstBookings: firstBookingCount,
      repeatBookings: repeatBookingCount,
      conversionRates: {
        signupToSubmission: signupCount > 0 ? (submissionCount / signupCount) * 100 : 0,
        submissionToBooking: submissionCount > 0 ? (firstBookingCount / submissionCount) * 100 : 0,
        firstToRepeat: firstBookingCount > 0 ? (repeatBookingCount / firstBookingCount) * 100 : 0
      }
    };
  } catch (error) {
    console.error('Error calculating conversion funnel:', error);
    return {
      signups: 0,
      propertySubmissions: 0,
      firstBookings: 0,
      repeatBookings: 0,
      conversionRates: {
        signupToSubmission: 0,
        submissionToBooking: 0,
        firstToRepeat: 0
      }
    };
  }
}

// Helper functions
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function getHostPerformanceTier(metrics: {
  totalBookings: number;
  totalRevenue: number;
  averageRating: number;
  responseRate: number;
}): 'platinum' | 'gold' | 'silver' | 'bronze' {
  const score = 
    (metrics.totalBookings >= 20 ? 25 : metrics.totalBookings * 1.25) +
    (metrics.totalRevenue >= 5000 ? 25 : metrics.totalRevenue / 200) +
    (metrics.averageRating * 5) +
    (metrics.responseRate / 4);

  if (score >= 80) return 'platinum';
  if (score >= 60) return 'gold';
  if (score >= 40) return 'silver';
  return 'bronze';
}

/**
 * Initialize event tracking for the application
 */
export function initializeEventTracking(): void {
  console.log('📊 Business event tracking initialized');
  
  // Track page views for analytics
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      // Track session end
      trackEvent('session_end', 'retention', {
        session_duration: Date.now() - (window as any).sessionStartTime
      });
    });

    // Set session start time
    (window as any).sessionStartTime = Date.now();
  }
}
