# HouseGoing AI Sales Assistant

This is an AI sales assistant for the HouseGoing platform. It helps potential customers find the perfect venue for their event and guides them through the booking process.

## Features

- Conversational AI interface for helping customers find venues
- Personalized recommendations based on event type, guest count, location, and budget
- Integration with the HouseGoing website through a REST API
- Session-based conversation history
- Australian-friendly language with a warm, helpful tone

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create a `.env` file with your API keys (you can copy from `.env.example`):
   ```
   HUGGINGFACE_API_KEY=your_huggingface_api_key
   LANGSMITH_API_KEY=your_langsmith_api_key
   LANGSMITH_PROJECT=your_langsmith_project
   LANGSMITH_TRACING=true
   LANGSMITH_ENDPOINT=https://api.smith.langchain.com
   ```

## Usage

### Console Interface

Run the sales assistant in the console for testing:

```bash
npm start
```

This starts a simple conversational interface that you can use to interact with the sales assistant.

### API Server

Run the API server for integration with your website:

```bash
npm run api
```

This starts an Express server on port 3002 with the following endpoints:

- `POST /api/chat` - Send a message to the sales assistant
  - Request body: `{ "message": "Your message here", "sessionId": "optional-session-id" }`
  - Response: `{ "response": "Assistant response", "sessionId": "session-id" }`

- `POST /api/reset` - Reset the conversation history
  - Request body: `{ "sessionId": "session-id" }`
  - Response: `{ "success": true, "message": "Conversation reset successfully", "sessionId": "session-id" }`

- `GET /api/health` - Check the health of the API server
  - Response: `{ "status": "ok", "version": "1.0.0" }`

## Integration with HouseGoing Website

To integrate the sales assistant with your website, you'll need to:

1. Update your frontend code to call the API endpoints
2. Store the session ID in localStorage or a cookie
3. Display the assistant's responses in your chat interface

Example frontend code:

```javascript
// Send a message to the sales assistant
async function sendMessage(message, sessionId) {
  const response = await fetch('http://localhost:3002/api/chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      message,
      sessionId
    })
  });
  
  return await response.json();
}

// Reset the conversation
async function resetConversation(sessionId) {
  const response = await fetch('http://localhost:3002/api/reset', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      sessionId
    })
  });
  
  return await response.json();
}
```

## Customization

You can customize the sales assistant by modifying the prompt template in `prompt.js`. This allows you to change the assistant's personality, tone, and behavior.

## Deployment

For production deployment, you should:

1. Set up a proper authentication system
2. Use a database for storing conversation history
3. Deploy the API server to a cloud provider
4. Set up HTTPS for secure communication
5. Configure proper error handling and logging
