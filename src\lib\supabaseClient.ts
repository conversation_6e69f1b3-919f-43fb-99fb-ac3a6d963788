import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Supabase URL and Anon Key must be defined in environment variables.');
}

// Create and export a single, shared Supabase client instance
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Prevent Supabase from using localStorage, as Clerk will be the source of truth for auth state.
    // This is crucial to avoid conflicts between Clerk and Supabase's session management.
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false,
  },
});
