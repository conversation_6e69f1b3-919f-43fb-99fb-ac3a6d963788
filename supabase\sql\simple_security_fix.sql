-- Simple Security Fix for HouseGoing
-- This script safely removes the main security vulnerabilities
-- Run this in the Supabase SQL Editor

-- Step 1: Drop all insecure functions that allow arbitrary SQL execution
DROP FUNCTION IF EXISTS public.exec_sql(text);
DROP FUNCTION IF EXISTS public.pg_query(text);
DROP FUNCTION IF EXISTS public.execute_sql(text);
DROP FUNCTION IF EXISTS public.exec_sql_query(text);

-- Step 2: Create secure replacement functions with proper search paths

-- Secure notification creation
CREATE OR REPLACE FUNCTION public.create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  notification_id UUID;
BEGIN
  IF p_user_id IS NULL OR p_title IS NULL OR p_message IS NULL THEN
    RAISE EXCEPTION 'Missing required parameters';
  END IF;
  
  -- Check if notifications table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notifications') THEN
    INSERT INTO notifications (user_id, title, message, type)
    VALUES (p_user_id, p_title, p_message, p_type)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
  ELSE
    RAISE NOTICE 'Notifications table does not exist';
    RETURN gen_random_uuid();
  END IF;
END;
$$;

-- Secure message count function
CREATE OR REPLACE FUNCTION public.get_unread_message_count(
  p_user_id UUID
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'messages') THEN
    RETURN (
      SELECT COUNT(*)
      FROM messages 
      WHERE recipient_id = p_user_id 
      AND read_at IS NULL
    );
  ELSE
    RETURN 0;
  END IF;
END;
$$;

-- Secure rating calculation
CREATE OR REPLACE FUNCTION public.calculate_confidence_score(
  p_venue_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  avg_rating DECIMAL(3,2);
  review_count INTEGER;
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'reviews') THEN
    SELECT 
      COALESCE(AVG(rating), 0),
      COUNT(*)
    INTO avg_rating, review_count
    FROM reviews 
    WHERE venue_id = p_venue_id;
    
    IF review_count = 0 THEN
      RETURN 0.00;
    END IF;
    
    RETURN ROUND(avg_rating * (review_count::DECIMAL / (review_count + 10)), 2);
  ELSE
    RETURN 0.00;
  END IF;
END;
$$;

-- Secure user rating function
CREATE OR REPLACE FUNCTION public.get_user_average_rating(
  p_user_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'reviews') 
     AND EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'venues') THEN
    RETURN (
      SELECT COALESCE(AVG(r.rating), 0.00)
      FROM reviews r
      JOIN venues v ON r.venue_id = v.id
      WHERE v.host_id = p_user_id
    );
  ELSE
    RETURN 0.00;
  END IF;
END;
$$;

-- Secure property type detection
CREATE OR REPLACE FUNCTION public.detect_property_type(
  p_venue_id UUID
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  venue_info RECORD;
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'venues') THEN
    SELECT title, description INTO venue_info
    FROM venues WHERE id = p_venue_id;
    
    IF venue_info.title ILIKE '%house%' OR venue_info.description ILIKE '%house%' THEN
      RETURN 'house';
    ELSIF venue_info.title ILIKE '%apartment%' OR venue_info.description ILIKE '%apartment%' THEN
      RETURN 'apartment';
    ELSIF venue_info.title ILIKE '%villa%' OR venue_info.description ILIKE '%villa%' THEN
      RETURN 'villa';
    ELSIF venue_info.title ILIKE '%studio%' OR venue_info.description ILIKE '%studio%' THEN
      RETURN 'studio';
    ELSE
      RETURN 'other';
    END IF;
  ELSE
    RETURN 'unknown';
  END IF;
END;
$$;

-- Secure curfew info function
CREATE OR REPLACE FUNCTION public.get_curfew_info(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  venue_rules TEXT;
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'venues') THEN
    SELECT house_rules INTO venue_rules
    FROM venues WHERE id = p_venue_id;
    
    RETURN jsonb_build_object(
      'has_curfew', 
      CASE WHEN venue_rules ILIKE '%quiet%' OR venue_rules ILIKE '%curfew%' THEN true ELSE false END,
      'curfew_time', 
      CASE 
        WHEN venue_rules ILIKE '%10%pm%' OR venue_rules ILIKE '%22:%' THEN '22:00'
        WHEN venue_rules ILIKE '%11%pm%' OR venue_rules ILIKE '%23:%' THEN '23:00'
        ELSE '22:00'
      END
    );
  ELSE
    RETURN '{"has_curfew": false}'::jsonb;
  END IF;
END;
$$;

-- Secure venue availability check
CREATE OR REPLACE FUNCTION public.check_venue_availability(
  p_venue_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'bookings') THEN
    RETURN NOT EXISTS (
      SELECT 1 FROM bookings
      WHERE venue_id = p_venue_id
      AND status IN ('confirmed', 'pending')
      AND (
        (start_date <= p_start_date AND end_date >= p_start_date) OR
        (start_date <= p_end_date AND end_date >= p_end_date) OR
        (start_date >= p_start_date AND end_date <= p_end_date)
      )
    );
  ELSE
    RETURN true;
  END IF;
END;
$$;

-- Secure booking conflict check
CREATE OR REPLACE FUNCTION public.check_booking_conflicts(
  p_venue_id UUID,
  p_start_date TIMESTAMP,
  p_end_date TIMESTAMP,
  p_exclude_booking_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'bookings') THEN
    RETURN EXISTS (
      SELECT 1 FROM bookings
      WHERE venue_id = p_venue_id
      AND status IN ('confirmed', 'pending')
      AND (p_exclude_booking_id IS NULL OR id != p_exclude_booking_id)
      AND (
        (start_date <= p_start_date AND end_date >= p_start_date) OR
        (start_date <= p_end_date AND end_date >= p_end_date) OR
        (start_date >= p_start_date AND end_date <= p_end_date)
      )
    );
  ELSE
    RETURN false;
  END IF;
END;
$$;

-- Step 3: Grant minimal necessary permissions
GRANT EXECUTE ON FUNCTION public.create_notification TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_unread_message_count TO authenticated;
GRANT EXECUTE ON FUNCTION public.calculate_confidence_score TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_user_average_rating TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.detect_property_type TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.get_curfew_info TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.check_venue_availability TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.check_booking_conflicts TO authenticated;

-- Step 4: Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Security cleanup completed successfully. All insecure functions removed and replaced with secure alternatives.';
END;
$$;
