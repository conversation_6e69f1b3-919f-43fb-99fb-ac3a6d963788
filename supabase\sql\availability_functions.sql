-- Comprehensive Availability Check Functions for HouseGoing
-- Run this after setting up the tables

-- Function to check if a venue is available for a specific date and time range
CREATE OR REPLACE FUNCTION check_venue_availability(
  p_venue_id UUID,
  p_start_datetime TIMESTAMP WITH TIME ZONE,
  p_end_datetime TIMESTAMP WITH TIME ZONE
)
RETURNS JSON AS $$
DECLARE
  v_result JSON;
  v_available BOOLEAN := true;
  v_conflicts TEXT[] := '{}';
  v_settings RECORD;
  v_day_of_week INTEGER;
  v_date DATE;
  v_start_time TIME;
  v_end_time TIME;
  v_operating_hours RECORD;
  v_day_availability RECORD;
  v_blocked_count INTEGER;
  v_booking_count INTEGER;
  v_seasonal_availability RECORD;
BEGIN
  -- Validate inputs
  IF p_venue_id IS NULL OR p_start_datetime IS NULL OR p_end_datetime IS NULL THEN
    RETURN json_build_object(
      'available', false,
      'conflicts', ARRAY['Invalid parameters provided']
    );
  END IF;

  IF p_end_datetime <= p_start_datetime THEN
    RETURN json_build_object(
      'available', false,
      'conflicts', ARRAY['End time must be after start time']
    );
  END IF;

  -- Get venue availability settings
  SELECT * INTO v_settings
  FROM venue_availability_settings
  WHERE venue_id = p_venue_id;

  -- If no settings found, use defaults
  IF v_settings IS NULL THEN
    INSERT INTO venue_availability_settings (venue_id)
    VALUES (p_venue_id)
    RETURNING * INTO v_settings;
  END IF;

  -- Extract date and time components
  v_date := p_start_datetime::DATE;
  v_start_time := p_start_datetime::TIME;
  v_end_time := p_end_datetime::TIME;
  v_day_of_week := EXTRACT(DOW FROM p_start_datetime)::INTEGER;

  -- Check 1: Lead time requirement
  IF p_start_datetime < (NOW() + (v_settings.lead_time_hours || ' hours')::INTERVAL) THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 
      'Booking requires at least ' || v_settings.lead_time_hours || ' hours advance notice'
    );
  END IF;

  -- Check 2: Maximum advance booking
  IF p_start_datetime > (NOW() + (v_settings.max_advance_days || ' days')::INTERVAL) THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 
      'Cannot book more than ' || v_settings.max_advance_days || ' days in advance'
    );
  END IF;

  -- Check 3: Booking duration limits
  IF EXTRACT(EPOCH FROM (p_end_datetime - p_start_datetime))/3600 < v_settings.min_booking_hours THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 
      'Minimum booking duration is ' || v_settings.min_booking_hours || ' hours'
    );
  END IF;

  IF EXTRACT(EPOCH FROM (p_end_datetime - p_start_datetime))/3600 > v_settings.max_booking_hours THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 
      'Maximum booking duration is ' || v_settings.max_booking_hours || ' hours'
    );
  END IF;

  -- Check 4: Day of week availability
  IF NOT (v_day_of_week = ANY(v_settings.available_days)) THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 'Venue is not available on this day of the week');
  END IF;

  -- Check 5: Operating hours for this day of week
  SELECT * INTO v_operating_hours
  FROM venue_operating_hours
  WHERE venue_id = p_venue_id AND day_of_week = v_day_of_week;

  IF v_operating_hours IS NOT NULL THEN
    IF NOT v_operating_hours.is_available THEN
      v_available := false;
      v_conflicts := array_append(v_conflicts, 'Venue is closed on this day');
    ELSIF v_start_time < v_operating_hours.start_time OR v_end_time > v_operating_hours.end_time THEN
      v_available := false;
      v_conflicts := array_append(v_conflicts, 
        'Requested time is outside operating hours (' || 
        v_operating_hours.start_time || ' - ' || v_operating_hours.end_time || ')'
      );
    END IF;
  ELSE
    -- Use default hours from settings
    IF v_start_time < v_settings.default_start_time OR v_end_time > v_settings.default_end_time THEN
      v_available := false;
      v_conflicts := array_append(v_conflicts, 
        'Requested time is outside default operating hours (' || 
        v_settings.default_start_time || ' - ' || v_settings.default_end_time || ')'
      );
    END IF;
  END IF;

  -- Check 6: Specific day availability override
  SELECT * INTO v_day_availability
  FROM venue_day_availability
  WHERE venue_id = p_venue_id AND date = v_date;

  IF v_day_availability IS NOT NULL THEN
    IF NOT v_day_availability.is_available THEN
      v_available := false;
      v_conflicts := array_append(v_conflicts, 'Venue is not available on this specific date');
    ELSIF v_day_availability.start_time IS NOT NULL AND v_day_availability.end_time IS NOT NULL THEN
      IF v_start_time < v_day_availability.start_time OR v_end_time > v_day_availability.end_time THEN
        v_available := false;
        v_conflicts := array_append(v_conflicts, 
          'Requested time is outside available hours for this date (' || 
          v_day_availability.start_time || ' - ' || v_day_availability.end_time || ')'
        );
      END IF;
    END IF;
  END IF;

  -- Check 7: Blocked time slots
  SELECT COUNT(*) INTO v_blocked_count
  FROM venue_blocked_slots
  WHERE venue_id = p_venue_id
    AND start_datetime < p_end_datetime
    AND end_datetime > p_start_datetime;

  IF v_blocked_count > 0 THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 'Time slot conflicts with blocked period');
  END IF;

  -- Check 8: Existing bookings
  SELECT COUNT(*) INTO v_booking_count
  FROM bookings
  WHERE venue_id = p_venue_id
    AND status NOT IN ('cancelled', 'rejected')
    AND start_date < p_end_datetime
    AND end_date > p_start_datetime;

  IF v_booking_count > 0 THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 'Time slot conflicts with existing booking');
  END IF;

  -- Check 9: Seasonal availability
  SELECT * INTO v_seasonal_availability
  FROM venue_seasonal_availability
  WHERE venue_id = p_venue_id
    AND start_date <= v_date
    AND end_date >= v_date
  ORDER BY created_at DESC
  LIMIT 1;

  IF v_seasonal_availability IS NOT NULL AND NOT v_seasonal_availability.is_available THEN
    v_available := false;
    v_conflicts := array_append(v_conflicts, 
      'Venue is not available during ' || v_seasonal_availability.season_name
    );
  END IF;

  -- Build result
  v_result := json_build_object(
    'available', v_available,
    'conflicts', v_conflicts,
    'venue_id', p_venue_id,
    'requested_start', p_start_datetime,
    'requested_end', p_end_datetime,
    'settings', json_build_object(
      'min_booking_hours', v_settings.min_booking_hours,
      'max_booking_hours', v_settings.max_booking_hours,
      'lead_time_hours', v_settings.lead_time_hours,
      'instant_booking_enabled', v_settings.instant_booking_enabled
    )
  );

  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get venue availability for a date range (for calendar display)
CREATE OR REPLACE FUNCTION get_venue_availability_calendar(
  p_venue_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS JSON AS $$
DECLARE
  v_result JSON;
  v_calendar JSON[] := '{}';
  v_current_date DATE;
  v_day_info JSON;
  v_day_availability RECORD;
  v_operating_hours RECORD;
  v_blocked_slots JSON[];
  v_bookings JSON[];
  v_settings RECORD;
BEGIN
  -- Get venue settings
  SELECT * INTO v_settings
  FROM venue_availability_settings
  WHERE venue_id = p_venue_id;

  -- Loop through each date in the range
  v_current_date := p_start_date;
  WHILE v_current_date <= p_end_date LOOP
    -- Get day-specific availability
    SELECT * INTO v_day_availability
    FROM venue_day_availability
    WHERE venue_id = p_venue_id AND date = v_current_date;

    -- Get operating hours for this day of week
    SELECT * INTO v_operating_hours
    FROM venue_operating_hours
    WHERE venue_id = p_venue_id AND day_of_week = EXTRACT(DOW FROM v_current_date);

    -- Get blocked slots for this date
    SELECT array_agg(
      json_build_object(
        'start_time', start_datetime::TIME,
        'end_time', end_datetime::TIME,
        'reason', reason,
        'block_type', block_type
      )
    ) INTO v_blocked_slots
    FROM venue_blocked_slots
    WHERE venue_id = p_venue_id
      AND start_datetime::DATE = v_current_date;

    -- Get bookings for this date
    SELECT array_agg(
      json_build_object(
        'start_time', start_date::TIME,
        'end_time', end_date::TIME,
        'status', status
      )
    ) INTO v_bookings
    FROM bookings
    WHERE venue_id = p_venue_id
      AND start_date::DATE = v_current_date
      AND status NOT IN ('cancelled', 'rejected');

    -- Build day info
    v_day_info := json_build_object(
      'date', v_current_date,
      'day_of_week', EXTRACT(DOW FROM v_current_date),
      'is_available', COALESCE(v_day_availability.is_available, 
        CASE WHEN v_settings IS NOT NULL THEN 
          EXTRACT(DOW FROM v_current_date) = ANY(v_settings.available_days)
        ELSE true END
      ),
      'operating_hours', CASE 
        WHEN v_operating_hours IS NOT NULL THEN
          json_build_object(
            'start_time', v_operating_hours.start_time,
            'end_time', v_operating_hours.end_time,
            'is_available', v_operating_hours.is_available
          )
        WHEN v_settings IS NOT NULL THEN
          json_build_object(
            'start_time', v_settings.default_start_time,
            'end_time', v_settings.default_end_time,
            'is_available', true
          )
        ELSE NULL
      END,
      'custom_hours', CASE 
        WHEN v_day_availability IS NOT NULL AND v_day_availability.start_time IS NOT NULL THEN
          json_build_object(
            'start_time', v_day_availability.start_time,
            'end_time', v_day_availability.end_time
          )
        ELSE NULL
      END,
      'blocked_slots', COALESCE(v_blocked_slots, '{}'),
      'bookings', COALESCE(v_bookings, '{}'),
      'special_price', v_day_availability.special_price,
      'notes', v_day_availability.notes
    );

    v_calendar := array_append(v_calendar, v_day_info);
    v_current_date := v_current_date + 1;
  END LOOP;

  v_result := json_build_object(
    'venue_id', p_venue_id,
    'start_date', p_start_date,
    'end_date', p_end_date,
    'calendar', v_calendar
  );

  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
