/**
 * AccountPageFix Component
 *
 * Simplified component that ensures proper authentication state
 * without complex initialization chains that can cause loading issues.
 */

import React, { useEffect, useState } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';

interface AccountPageFixProps {
  children: React.ReactNode;
}

export default function AccountPageFix({ children }: AccountPageFixProps) {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simple timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.log('AccountPageFix: Timeout reached, proceeding with current state');
      setIsReady(true);
    }, 3000);

    // If Clerk is loaded, proceed immediately
    if (isLoaded) {
      console.log('AccountPageFix: Clerk loaded, authentication state:', { isSignedIn, hasUser: !!user });
      clearTimeout(timeout);
      setIsReady(true);
    }

    return () => clearTimeout(timeout);
  }, [isLoaded, isSignedIn, user]);
  // Show loading while Clerk is initializing
  if (!isReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
          <p className="text-gray-600 mb-2">Loading your account...</p>
          <p className="text-gray-400 text-sm">This should only take a moment</p>
        </div>
      </div>
    );
  }
  // If not signed in, show sign in button
  if (!isSignedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
          <h2 className="text-2xl font-bold mb-4">Please Sign In</h2>
          <p className="text-gray-600 mb-6">You need to be signed in to access your account.</p>
          <a href="/login" className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  // Show error if any
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-4">Authentication Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block mr-4"
          >
            Retry
          </button>
          <a href="/" className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-md inline-block">
            Return Home
          </a>
        </div>
      </div>
    );
  }

  // Render children if everything is ready
  return <>{children}</>;
}
