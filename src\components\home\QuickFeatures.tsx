
import { Music2, Shield, CreditCard, HeartHandshake } from "lucide-react";

const features = [
  {
    icon: Music2,
    title: "Entertainment Ready",
    description: "Professional sound systems and DJ setups included"
  },
  {
    icon: Shield,
    title: "Safe & Secure",
    description: "Licensed venues with proper security"
  },
  {
    icon: CreditCard,
    title: "Easy Booking",
    description: "Transparent pricing, instant confirmation"
  },
  {
    icon: HeartHandshake,
    title: "Dedicated Support",
    description: "24/7 assistance for your event"
  }
];

export function QuickFeatures() {
  return (
    <div className="bg-gradient-to-b from-white to-primary-light/20 py-gr4">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div 
                key={index} 
                className="flex items-start space-x-3 p-4 rounded-lg bg-white shadow-lg hover:shadow-xl transition-all duration-300 border border-primary/5"
              >
                <Icon className="w-6 h-6 text-primary shrink-0" />
                <div>
                  <h3 className="text-h3 font-semibold mb-1 text-primary-darker">{feature.title}</h3>
                  <p className="text-sm text-secondary-darker">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
