# rebuild-with-indexing-fixes.ps1
# PowerShell script to rebuild the site with all indexing fixes applied

Write-Host "HouseGoing Indexing Fix - Rebuild" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Step 1: Update sitemap generation
Write-Host "Step 1: Regenerating sitemaps with brand pages..." -ForegroundColor Green

try {
    & npm run generate-sitemap
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error generating sitemap. Check the script output for details." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✓ Sitemaps successfully regenerated" -ForegroundColor Green
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Validate the sitemap
Write-Host "Step 2: Validating sitemap..." -ForegroundColor Green

try {
    & npm run validate-sitemap
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error validating sitemap. Check the script output for details." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✓ Sitemaps successfully validated" -ForegroundColor Green
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Run a full production build
Write-Host "Step 3: Running production build..." -ForegroundColor Green

try {
    & npm run build:prod
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error during production build. Check the build output for details." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✓ Production build completed successfully" -ForegroundColor Green
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Verify the build output contains our brand pages
Write-Host "Step 4: Verifying build output..." -ForegroundColor Green

$distDir = ".\dist"
$brandFiles = @(
    "$distDir\housegoing-brand.html",
    "$distDir\housegoing-faq.html",
    "$distDir\sitemap.xml",
    "$distDir\sitemap_comprehensive.xml"
)

$allFilesExist = $true
foreach ($file in $brandFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "Warning: File not found in build output: $file" -ForegroundColor Red
        $allFilesExist = $false
    }
    else {
        Write-Host "✓ File found in build output: $file" -ForegroundColor Green
    }
}

if (-not $allFilesExist) {
    Write-Host "Warning: Some expected files are missing from the build output." -ForegroundColor Yellow
}
else {
    Write-Host "✓ All expected files found in build output" -ForegroundColor Green
}

# Step 5: Create special indexing verification file
Write-Host "Step 5: Creating special indexing verification page..." -ForegroundColor Green

$verificationContent = @'
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing Indexing Verification - Google Please Index This Site</title>
  <meta name="description" content="This page helps verify that HouseGoing is properly indexed by Google and other search engines.">
  <link rel="canonical" href="https://housegoing.com.au/indexing-verification.html">
</head>
<body>
  <h1>HouseGoing Indexing Verification</h1>
  <p>This page confirms that HouseGoing should be indexed by Google and other search engines.</p>
  <p>HouseGoing is Australia's premier venue booking platform for party and event spaces.</p>
  <p>Our website is located at: <a href="https://housegoing.com.au">https://housegoing.com.au</a></p>
  <p>We provide services for:</p>
  <ul>
    <li>Finding venues for parties and events</li>
    <li>Checking noise restrictions for venues</li>
    <li>Booking event spaces in Australia</li>
    <li>Managing venue listings for hosts</li>
  </ul>
  <p>Please ensure our site is properly indexed for searches like:</p>
  <ul>
    <li>"housegoing"</li>
    <li>"housegoing au"</li>
    <li>"venue booking australia"</li>
  </ul>
  <p>Verification timestamp: 
'@ + (Get-Date -Format "yyyy-MM-dd HH:mm:ss") + @'
  </p>
  <p><a href="https://housegoing.com.au">Return to HouseGoing Homepage</a></p>
</body>
</html>
'@

$verificationPath = "$distDir\indexing-verification.html"
Set-Content -Path $verificationPath -Value $verificationContent
Write-Host "✓ Created indexing verification page at: $verificationPath" -ForegroundColor Green

# Step 6: Update sitemap to include verification page
Write-Host "Step 6: Adding verification page to sitemap..." -ForegroundColor Green

$sitemapPath = "$distDir\sitemap_comprehensive.xml"
if (Test-Path $sitemapPath) {
    $sitemapContent = Get-Content -Path $sitemapPath -Raw

    # Check if the sitemap already contains our verification page
    if ($sitemapContent -notmatch "indexing-verification.html") {
        # Find the last </url> tag
        $lastUrlPos = $sitemapContent.LastIndexOf("</url>")
        
        if ($lastUrlPos -ne -1) {
            $verificationEntry = @'
  <url>
    <loc>https://housegoing.com.au/indexing-verification.html</loc>
    <lastmod>
'@ + (Get-Date -Format "yyyy-MM-dd") + @'
</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
'@
            # Insert the new entry after the last </url> tag
            $newContent = $sitemapContent.Substring(0, $lastUrlPos + 6) + "`n" + $verificationEntry + $sitemapContent.Substring($lastUrlPos + 6)
            Set-Content -Path $sitemapPath -Value $newContent
            
            Write-Host "✓ Added verification page to sitemap" -ForegroundColor Green
        }
        else {
            Write-Host "Warning: Could not find position to insert verification page in sitemap" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "✓ Verification page already in sitemap" -ForegroundColor Green
    }
}
else {
    Write-Host "Warning: Sitemap file not found at $sitemapPath" -ForegroundColor Red
}

# Final output
Write-Host "`n====================================" -ForegroundColor Cyan
Write-Host "Build with Indexing Fixes Complete!" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "`nNext steps:"
Write-Host "1. Deploy the site to Netlify" -ForegroundColor Yellow
Write-Host "2. Submit the updated sitemap to Google Search Console" -ForegroundColor Yellow
Write-Host "3. Request indexing for the following URLs:" -ForegroundColor Yellow
Write-Host "   - https://housegoing.com.au/" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/about-housegoing" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/housegoing-brand.html" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/housegoing-faq.html" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/indexing-verification.html" -ForegroundColor White
Write-Host "4. Check if the site has been indexed by searching:" -ForegroundColor Yellow
Write-Host "   site:housegoing.com.au" -ForegroundColor White
Write-Host "`nFor detailed guidance, see docs\brand-search-status-update.md" -ForegroundColor Green

# Step 4: Verify the build output contains our brand pages
Write-Host "Step 4: Verifying build output..." -ForegroundColor Green

$distDir = ".\dist"
$brandFiles = @(
    "$distDir\housegoing-brand.html",
    "$distDir\housegoing-faq.html",
    "$distDir\sitemap.xml",
    "$distDir\sitemap_comprehensive.xml"
)

$allFilesExist = $true
foreach ($file in $brandFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "Warning: File not found in build output: $file" -ForegroundColor Red
        $allFilesExist = $false
    } 
    else {
        Write-Host "✓ File found in build output: $file" -ForegroundColor Green
    }
}

if (-not $allFilesExist) {
    Write-Host "Warning: Some expected files are missing from the build output." -ForegroundColor Yellow
} 
else {
    Write-Host "✓ All expected files found in build output" -ForegroundColor Green
}

# Step 5: Create special indexing verification file
Write-Host "Step 5: Creating special indexing verification page..." -ForegroundColor Green

$verificationContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing Indexing Verification - Google Please Index This Site</title>
  <meta name="description" content="This page helps verify that HouseGoing is properly indexed by Google and other search engines.">
  <link rel="canonical" href="https://housegoing.com.au/indexing-verification.html">
</head>
<body>
  <h1>HouseGoing Indexing Verification</h1>
  <p>This page confirms that HouseGoing should be indexed by Google and other search engines.</p>
  <p>HouseGoing is Australia's premier venue booking platform for party and event spaces.</p>
  <p>Our website is located at: <a href="https://housegoing.com.au">https://housegoing.com.au</a></p>
  <p>We provide services for:</p>
  <ul>
    <li>Finding venues for parties and events</li>
    <li>Checking noise restrictions for venues</li>
    <li>Booking event spaces in Australia</li>
    <li>Managing venue listings for hosts</li>
  </ul>
  <p>Please ensure our site is properly indexed for searches like:</p>
  <ul>
    <li>"housegoing"</li>
    <li>"housegoing au"</li>
    <li>"venue booking australia"</li>
  </ul>
  <p>Verification timestamp: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
  <p><a href="https://housegoing.com.au">Return to HouseGoing Homepage</a></p>
</body>
</html>
"@

$verificationPath = "$distDir\indexing-verification.html"
Set-Content -Path $verificationPath -Value $verificationContent
Write-Host "✓ Created indexing verification page at: $verificationPath" -ForegroundColor Green

# Step 6: Update sitemap to include verification page
Write-Host "Step 6: Adding verification page to sitemap..." -ForegroundColor Green

$sitemapPath = "$distDir\sitemap_comprehensive.xml"
$sitemapContent = Get-Content -Path $sitemapPath -Raw

# Check if the sitemap already contains our verification page
if ($sitemapContent -notmatch "indexing-verification.html") {
    # Find the last </url> tag
    $lastUrlPos = $sitemapContent.LastIndexOf("</url>")
    
    if ($lastUrlPos -ne -1) {
        $verificationEntry = @"
  <url>
    <loc>https://housegoing.com.au/indexing-verification.html</loc>
    <lastmod>$(Get-Date -Format "yyyy-MM-dd")</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
"@
        # Insert the new entry after the last </url> tag
        $newContent = $sitemapContent.Substring(0, $lastUrlPos + 6) + "`n" + $verificationEntry + $sitemapContent.Substring($lastUrlPos + 6)
        Set-Content -Path $sitemapPath -Value $newContent
        
        Write-Host "✓ Added verification page to sitemap" -ForegroundColor Green
    } 
    else {
        Write-Host "Warning: Could not find position to insert verification page in sitemap" -ForegroundColor Yellow
    }
} 
else {
    Write-Host "✓ Verification page already in sitemap" -ForegroundColor Green
}

# Final output
Write-Host "`n====================================" -ForegroundColor Cyan
Write-Host "Build with Indexing Fixes Complete!" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "`nNext steps:"
Write-Host "1. Deploy the site to Netlify" -ForegroundColor Yellow
Write-Host "2. Submit the updated sitemap to Google Search Console" -ForegroundColor Yellow
Write-Host "3. Request indexing for the following URLs:" -ForegroundColor Yellow
Write-Host "   - https://housegoing.com.au/" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/about-housegoing" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/housegoing-brand.html" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/housegoing-faq.html" -ForegroundColor White
Write-Host "   - https://housegoing.com.au/indexing-verification.html" -ForegroundColor White
Write-Host "4. Check if the site has been indexed by searching:" -ForegroundColor Yellow
Write-Host "   site:housegoing.com.au" -ForegroundColor White
Write-Host "`nFor detailed guidance, see docs\brand-search-status-update.md" -ForegroundColor Green
