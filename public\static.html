<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing - Static Version</title>
  <style>
    /* Basic styles */
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f9fafb;
      color: #111827;
      line-height: 1.5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    header {
      margin-bottom: 2rem;
    }
    
    h1 {
      font-size: 2.5rem;
      color: #7c3aed;
      margin-bottom: 0.5rem;
    }
    
    .subtitle {
      font-size: 1.25rem;
      color: #4b5563;
    }
    
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      padding: 2rem;
      margin-bottom: 2rem;
    }
    
    h2 {
      font-size: 1.875rem;
      color: #111827;
      margin-top: 0;
      margin-bottom: 1rem;
    }
    
    .features {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }
    
    .feature {
      background-color: #f9fafb;
      border-radius: 0.5rem;
      padding: 1.5rem;
    }
    
    h3 {
      font-size: 1.25rem;
      color: #111827;
      margin-top: 0;
      margin-bottom: 0.75rem;
    }
    
    .button {
      display: inline-block;
      background-color: #7c3aed;
      color: white;
      font-weight: 500;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      text-decoration: none;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #6d28d9;
    }
    
    .text-center {
      text-align: center;
    }
    
    .mt-8 {
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>HouseGoing</h1>
      <p class="subtitle">Find your perfect venue</p>
    </header>
    
    <main>
      <div class="card">
        <h2>Welcome to HouseGoing</h2>
        <p>Discover and book fully verified venues for your next event. We connect venue owners with people seeking legitimate spaces for private events.</p>
        
        <div class="features">
          <div class="feature">
            <h3>Verified Properties</h3>
            <p>Venues are verified for listing accuracy. Hosts maintain their own safety standards and insurance.</p>
          </div>
          
          <div class="feature">
            <h3>Trusted Reviews</h3>
            <p>Read real reviews from verified guests to help you make the right choice for your gathering.</p>
          </div>
          
          <div class="feature">
            <h3>Quick Response Time</h3>
            <p>Most hosts respond within 2 hours, making planning your event quick and easy.</p>
          </div>
        </div>
        
        <div class="text-center mt-8">
          <a href="/find-venues" class="button">Explore Venues</a>
        </div>
      </div>
    </main>
  </div>
</body>
</html>
