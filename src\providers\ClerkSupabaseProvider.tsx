import React, { useEffect, ReactNode, useState } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { createClient } from '@supabase/supabase-js';

interface ClerkSupabaseProviderProps {
  children: ReactNode;
}

/**
 * ClerkSupabaseProvider
 *
 * This component creates a Supabase client with Clerk session token
 * following the official Clerk-Supabase integration pattern.
 * It should be placed inside the ClerkProvider.
 */
export function ClerkSupabaseProvider({ children }: ClerkSupabaseProviderProps) {
  const { getToken, isLoaded, userId } = useAuth();
  const { user } = useUser();
  const [customerData, setCustomerData] = useState<any | null>(null);

  // Create a custom Supabase client that injects the Clerk session token (following official docs)
  function createClerkSupabaseClient() {
    return createClient(
      import.meta.env.VITE_SUPABASE_URL!,
      import.meta.env.VITE_SUPABASE_ANON_KEY!,
      {
        async accessToken() {
          return await getToken() ?? null;
        },
      },
    );
  }

  // Create a client object for accessing Supabase data using the Clerk token
  const client = createClerkSupabaseClient();

  // This useEffect will wait for the User object to be loaded before requesting
  // the user data for the signed in user
  useEffect(() => {
    if (!user) return;

    async function syncUserToSupabase() {
      if (user && user.primaryEmailAddress?.emailAddress) {
        try {
          console.log('🔄 Syncing user to Supabase:', user.primaryEmailAddress.emailAddress);

          // Check if user exists in user_profiles table
          const { data: existingUser, error: fetchError } = await client
            .from('user_profiles')
            .select('*')
            .eq('email', user.primaryEmailAddress.emailAddress)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('❌ Error checking existing user:', fetchError);
            return;
          }

          if (!existingUser) {
            // Create new user profile record
            const { data: newUser, error: insertError } = await client
              .from('user_profiles')
              .insert({
                id: user.id,
                email: user.primaryEmailAddress.emailAddress,
                first_name: user.firstName || '',
                last_name: user.lastName || '',
                role: 'customer',
                is_host: false,
              })
              .select()
              .single();

            if (insertError) {
              console.error('❌ Error creating user profile:', insertError);
            } else {
              setCustomerData(newUser);
              console.log('✅ New user profile created:', newUser.email);
            }
          } else {
            setCustomerData(existingUser);
            console.log('✅ Existing user profile loaded:', existingUser.email);
          }
        } catch (error) {
          console.error('❌ Error syncing user to Supabase:', error);
        }
      } else {
        setCustomerData(null);
      }
    }

    syncUserToSupabase();
  }, [user, client]);

  return <>{children}</>;
}

export default ClerkSupabaseProvider;
