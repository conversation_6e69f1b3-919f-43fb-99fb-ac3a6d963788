import React from 'react';
import { Calendar as CalendarIcon } from 'lucide-react';

interface DatePickerProps {
  onSelect: (date: string) => void;
  isOpen: boolean;
}

export default function DatePicker({ onSelect, isOpen }: DatePickerProps) {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 mt-2 bg-white rounded-lg shadow-lg p-4 z-50">
      <input
        type="date"
        onChange={(e) => onSelect(e.target.value)}
        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        min={new Date().toISOString().split('T')[0]}
      />
    </div>
  );
}