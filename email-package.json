{"name": "housegoing-email-server", "version": "1.0.0", "description": "Email notification server for HouseGoing platform", "main": "email-server.js", "type": "module", "scripts": {"start": "node email-server.js", "dev": "node email-server.js"}, "dependencies": {"express": "^4.18.2", "nodemailer": "^6.9.7", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["email", "notifications", "nodemailer", "housegoing"], "author": "HouseGoing", "license": "MIT"}