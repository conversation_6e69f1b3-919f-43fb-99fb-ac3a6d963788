# HouseGoing AI Agents

This directory contains AI agents for the HouseGoing platform. Each agent is designed to solve specific problems and enhance the user experience.

## Available Agents

### Host Acquisition Agent

The Host Acquisition Agent helps potential hosts list their venues on the HouseGoing platform. It provides personalized guidance, venue analysis, and recommendations to make the hosting process as smooth as possible.

Key features:
- Conversational AI interface for guiding hosts through the listing process
- Venue analysis and earning potential estimation
- Party Score Calculator for evaluating venues based on local regulations
- Pricing optimization and competitor analysis
- Booking calendar management assistance
- Host rules generator

[Learn more about the Host Acquisition Agent](./host-acquisition/README.md)

### Sales Assistant

The Sales Assistant helps potential customers find the perfect venue for their event and guides them through the booking process. It's designed to be integrated with the HouseGoing website as a chatbox.

Key features:
- Conversational AI interface for helping customers find venues
- Personalized recommendations based on event type, guest count, location, and budget
- Australian-friendly language with a warm, helpful tone
- Session-based conversation history
- Integration with the HouseGoing website through a REST API

[Learn more about the Sales Assistant](./sales-assistant/README.md)

## Getting Started

To install all agents:

```bash
npm run install-all
```

To run the Host Acquisition Agent:

```bash
npm run host-agent
```

To run the advanced version with additional tools:

```bash
npm run host-agent:advanced
```

To run the API server for the Host Acquisition Agent:

```bash
npm run host-agent:api
```

To run the Sales Assistant:

```bash
npm run sales-assistant
```

To run the API server for the Sales Assistant (for website integration):

```bash
npm run sales-assistant:api
```

## Authentication

All agents are designed to be accessible only to authenticated users with appropriate permissions. The Host Acquisition Agent, for example, is only accessible to users with host privileges.

## Integration with HouseGoing Platform

These agents are designed to be integrated with the HouseGoing platform through API endpoints. Each agent provides a set of endpoints that can be called from the main application.

## Technology Stack

- LangChain for agent framework
- Hugging Face for language models
- Node.js for runtime environment
- Express for API endpoints (when running as a service)

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
