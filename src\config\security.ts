/**
 * Production Security Configuration
 * Implements robust security measures for the HouseGoing platform
 */

// Security constants
export const SECURITY_CONFIG = {
  // Content Security Policy
  CSP_DIRECTIVES: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for Vite in development
      'https://js.stripe.com',
      'https://clerk.housegoing.com.au',
      'https://*.clerk.accounts.dev',
      'https://api.mapbox.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for styled components
      'https://fonts.googleapis.com'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https://*.supabase.co',
      'https://res.cloudinary.com',
      'https://api.mapbox.com'
    ],
    'connect-src': [
      "'self'",
      'https://*.supabase.co',
      'https://api.stripe.com',
      'https://clerk.housegoing.com.au',
      'https://*.clerk.accounts.dev',
      'https://api.mapbox.com',
      'https://housegoing.onrender.com',
      'https://api.ipify.org'
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com'
    ],
    'frame-src': [
      'https://js.stripe.com',
      'https://hooks.stripe.com'
    ]
  },

  // Rate limiting
  RATE_LIMITS: {
    SEARCH_PER_MINUTE: 30,
    BOOKING_PER_HOUR: 5,
    LOGIN_ATTEMPTS: 5,
    API_CALLS_PER_MINUTE: 100
  },

  // Admin emails - should be loaded from environment variables in production
  ADMIN_EMAILS: (typeof process !== 'undefined' && process.env.ADMIN_EMAILS)
    ? process.env.ADMIN_EMAILS.split(',').map(email => email.trim())
    : [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ],

  // Allowed domains for CORS
  ALLOWED_ORIGINS: [
    'https://housegoing.com.au',
    'https://www.housegoing.com.au',
    'https://clerk.housegoing.com.au'
  ],

  // Security headers
  SECURITY_HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self)',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
  }
};

// Environment validation
export function validateEnvironment(): boolean {
  const requiredEnvVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_CLERK_PUBLISHABLE_KEY',
    'VITE_STRIPE_PUBLISHABLE_KEY'
  ];

  const missing = requiredEnvVars.filter(envVar => !import.meta.env[envVar]);

  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    return false;
  }

  // Validate API key formats
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  const clerkKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;
  const stripeKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;

  if (supabaseKey && !supabaseKey.startsWith('eyJ')) {
    console.error('Invalid Supabase key format');
    return false;
  }

  if (clerkKey && !clerkKey.startsWith('pk_')) {
    console.error('Invalid Clerk key format');
    return false;
  }

  if (stripeKey && !stripeKey.startsWith('pk_')) {
    console.error('Invalid Stripe key format');
    return false;
  }

  return true;
}

// Check if we're in production
export function isProduction(): boolean {
  return import.meta.env.PROD && 
         typeof window !== 'undefined' && 
         !window.location.hostname.includes('localhost') &&
         !window.location.hostname.includes('127.0.0.1');
}

// Sanitize user input
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Check if user is admin
export function isAdminUser(email: string): boolean {
  if (!email || !isValidEmail(email)) return false;
  return SECURITY_CONFIG.ADMIN_EMAILS.includes(email.toLowerCase());
}

// Generate secure session ID
export function generateSecureSessionId(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

// Rate limiting check
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(key: string, limit: number, windowMs: number): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}

// Log security events
export function logSecurityEvent(event: string, details: any = {}): void {
  if (isProduction()) {
    // In production, send to monitoring service
    console.warn('Security Event:', event, details);
    // TODO: Integrate with monitoring service (e.g., Sentry)
  } else {
    console.log('Security Event:', event, details);
  }
}

// Initialize security measures
export function initializeSecurity(): void {
  if (!validateEnvironment()) {
    throw new Error('Environment validation failed');
  }

  // Set security headers (if running in a service worker context)
  if (typeof window !== 'undefined') {
    // Client-side security measures
    
    // Disable right-click in production
    if (isProduction()) {
      document.addEventListener('contextmenu', (e) => e.preventDefault());
      document.addEventListener('selectstart', (e) => e.preventDefault());
      document.addEventListener('dragstart', (e) => e.preventDefault());
    }

    // Monitor for suspicious activity
    let suspiciousActivity = 0;
    
    // Detect rapid clicking
    document.addEventListener('click', () => {
      suspiciousActivity++;
      setTimeout(() => suspiciousActivity--, 1000);
      
      if (suspiciousActivity > 20) {
        logSecurityEvent('Suspicious clicking detected', { count: suspiciousActivity });
      }
    });

    // Detect console usage in production
    if (isProduction()) {
      const originalLog = console.log;
      console.log = (...args) => {
        logSecurityEvent('Console usage detected in production', { args });
        originalLog.apply(console, args);
      };
    }
  }

  console.log('🔒 Security measures initialized');
}

// API key validation
export function validateApiKey(key: string, type: 'stripe' | 'clerk' | 'supabase'): boolean {
  const patterns = {
    stripe: /^pk_live_[a-zA-Z0-9]+$/,
    clerk: /^pk_live_[a-zA-Z0-9]+$/,
    supabase: /^[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+$/
  };

  return patterns[type]?.test(key) || false;
}

// Secure data storage
export function secureStore(key: string, value: any): void {
  try {
    const encrypted = btoa(JSON.stringify(value));
    sessionStorage.setItem(key, encrypted);
  } catch (error) {
    logSecurityEvent('Secure storage failed', { key, error });
  }
}

export function secureRetrieve(key: string): any {
  try {
    const encrypted = sessionStorage.getItem(key);
    if (!encrypted) return null;
    return JSON.parse(atob(encrypted));
  } catch (error) {
    logSecurityEvent('Secure retrieval failed', { key, error });
    return null;
  }
}
