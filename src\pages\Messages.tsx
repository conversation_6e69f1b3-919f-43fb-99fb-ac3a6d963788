import React from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { useLocation } from 'react-router-dom';
import EnhancedMessaging from '../components/messaging/EnhancedMessaging';
import SEO from '../components/seo/SEO';

export default function Messages() {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const location = useLocation();

  // Get any pre-selected conversation from navigation state
  const selectedConversation = location.state?.selectedConversation;

  // Show loading state while Clerk is loading
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Check if user is signed in
  if (!isSignedIn || !user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-6">Please sign in to view your messages.</p>
          <a href="/login" className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEO
        title="Messages | HouseGoing"
        description="Communicate with hosts and guests on HouseGoing. Send messages, coordinate bookings, and get answers to your questions."
        url="https://housegoing.com.au/messages"
      />

      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
            <p className="mt-1 text-gray-600">Communicate with hosts and guests</p>
          </div>

          <EnhancedMessaging
            className="h-[70vh]"
            otherUserId={selectedConversation?.otherUser?.id}
            bookingId={selectedConversation?.booking?.id}
          />
        </div>
      </div>
    </>
  );
}
