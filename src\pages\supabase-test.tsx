'use client'
import { useEffect, useState } from 'react'
import { useSession, useUser } from '@clerk/clerk-react'
import { useClerkSupabaseClient } from '../lib/clerk-supabase-client'

/**
 * Official Clerk-Supabase Integration Test Page
 * This follows the exact pattern from the official Clerk documentation
 */
export default function SupabaseTest() {
  const [tasks, setTasks] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [name, setName] = useState('')
  const [error, setError] = useState<string | null>(null)
  
  // The `useUser()` hook is used to ensure that <PERSON> has loaded data about the signed in user
  const { user } = useUser()
  // The `useSession()` hook is used to get the Clerk session object
  // The session object is used to get the Clerk session token
  const { session } = useSession()

  // Create a custom Supabase client that injects the Clerk session token into the request headers
  const client = useClerkSupabaseClient()

  // This `useEffect` will wait for the User object to be loaded before requesting
  // the tasks for the signed in user
  useEffect(() => {
    if (!user) return

    async function loadTasks() {
      setLoading(true)
      setError(null)
      
      try {
        const { data, error } = await client.from('user_profiles').select('*').limit(5)
        
        if (error) {
          setError(`Supabase Error: ${error.message}`)
          console.error('Supabase error:', error)
        } else {
          setTasks(data || [])
          console.log('Successfully loaded data:', data)
        }
      } catch (err) {
        setError(`Network Error: ${err instanceof Error ? err.message : 'Unknown error'}`)
        console.error('Network error:', err)
      }
      
      setLoading(false)
    }

    loadTasks()
  }, [user, session])

  async function createTask(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    setError(null)
    
    try {
      // Insert task into the "user_profiles" table (or create a tasks table)
      const { data, error } = await client.from('user_profiles').insert({
        display_name: name,
        // Add other required fields as needed
      })
      
      if (error) {
        setError(`Insert Error: ${error.message}`)
        console.error('Insert error:', error)
      } else {
        console.log('Successfully inserted:', data)
        setName('')
        // Reload the page to see the new task
        window.location.reload()
      }
    } catch (err) {
      setError(`Network Error: ${err instanceof Error ? err.message : 'Unknown error'}`)
      console.error('Network error:', err)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
            <p>Please sign in to test the Supabase integration.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Official Clerk-Supabase Integration Test
        </h1>

        <div className="space-y-6">
          {/* User Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">User Information</h2>
            <div className="space-y-2">
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Email:</strong> {user.primaryEmailAddress?.emailAddress}</p>
              <p><strong>External ID:</strong> {user.externalId || 'Not set'}</p>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <p><strong>Error:</strong> {error}</p>
            </div>
          )}

          {/* Data Display */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">User Profiles Data</h2>
            
            {loading && <p className="text-gray-600">Loading...</p>}

            {!loading && tasks.length > 0 && (
              <div className="space-y-2">
                {tasks.map((task: any) => (
                  <div key={task.id} className="p-3 bg-gray-50 rounded">
                    <p><strong>ID:</strong> {task.id}</p>
                    <p><strong>Name:</strong> {task.display_name || 'No name'}</p>
                    <p><strong>User ID:</strong> {task.user_id || 'No user ID'}</p>
                  </div>
                ))}
              </div>
            )}

            {!loading && tasks.length === 0 && !error && (
              <p className="text-gray-600">No data found</p>
            )}
          </div>

          {/* Test Form */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Test Data Insertion</h2>
            <form onSubmit={createTask} className="space-y-4">
              <input
                autoFocus
                type="text"
                name="name"
                placeholder="Enter test name"
                onChange={(e) => setName(e.target.value)}
                value={name}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button 
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
                disabled={!name.trim()}
              >
                Add Test Entry
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
