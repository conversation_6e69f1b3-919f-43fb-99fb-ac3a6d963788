import React, { useState } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Building,
  Calendar,
  CalendarCheck,
  MessageSquare,
  DollarSign,
  Settings,
  Menu,
  X,
  LogOut,
  HelpCircle,
  Bell,
  Bot
} from 'lucide-react';
import { useSupabase } from '../providers/SupabaseProvider';
import { useUser, useClerk } from '@clerk/clerk-react';
import { AIAssistantButton } from '../components/host/AIAssistantButton';
import HeaderAuthButtons from '../components/auth/HeaderAuthButtons';

export default function OwnerPortalLayout() {
  const { userProfile, signOut } = useSupabase();
  const { user } = useUser();
  const { signOut: clerkSignOut } = useClerk();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/host/dashboard', icon: LayoutDashboard },
    { name: 'Properties', href: '/host/properties', icon: Building },
    { name: 'Availability', href: '/host/availability', icon: Calendar },
    { name: 'Bookings', href: '/host/bookings', icon: CalendarCheck },
    { name: 'Messages', href: '/host/messages', icon: MessageSquare },
    { name: 'AI Assistant', href: '/host/ai-assistant', icon: Bot },
    { name: 'Earnings', href: '/host/earnings', icon: DollarSign },
    { name: 'Settings', href: '/host/settings', icon: Settings },
    { name: 'Help', href: '/host/help', icon: HelpCircle },
  ];

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSignOut = () => {
    signOut();
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar toggle */}
      <div className="fixed top-4 left-4 z-50 lg:hidden">
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md bg-white shadow-md text-gray-600 hover:text-purple-600"
        >
          {sidebarOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 transition-transform duration-300 ease-in-out owner-portal-sidebar`}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b">
            <Link to="/" className="flex items-center">
              <img src="/images/housegoing-logo.svg" alt="HouseGoing" className="h-8 w-8" />
              <span className="ml-2 text-xl font-bold text-purple-600">HouseGoing</span>
            </Link>
          </div>

          {/* User info */}
          <div className="p-4 border-b">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <img
                  className="h-10 w-10 rounded-full"
                  src={user?.imageUrl || userProfile?.avatar_url || 'https://via.placeholder.com/40'}
                  alt={user?.fullName || `${userProfile?.first_name || ''} ${userProfile?.last_name || ''}` || 'User'}
                />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {user?.fullName || `${userProfile?.first_name || ''} ${userProfile?.last_name || ''}` || 'Property Owner'}
                </p>
                <p className="text-xs text-gray-500">Property Owner</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 overflow-y-auto">
            <div className="space-y-1">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-md ${
                      isActive
                        ? 'bg-purple-50 text-purple-600'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-purple-600'
                    }`}
                  >
                    <item.icon
                      className={`mr-3 h-5 w-5 ${
                        isActive ? 'text-purple-600' : 'text-gray-500'
                      }`}
                    />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </nav>

          {/* Sign out button */}
          <div className="p-4 border-t">
            <button
              onClick={handleSignOut}
              className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-red-600"
            >
              <LogOut className="mr-3 h-5 w-5 text-gray-500" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className={`lg:pl-64 min-h-screen flex flex-col`}>
        {/* Top navigation */}
        <header className="sticky top-0 z-30 bg-white shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Logo for mobile */}
              <div className="lg:hidden flex items-center">
                <Link to="/" className="flex items-center">
                  <img src="/images/housegoing-logo.svg" alt="HouseGoing" className="h-6 w-6" />
                  <span className="ml-2 text-lg font-bold text-purple-600">HouseGoing</span>
                </Link>
              </div>

              {/* Right side items */}
              <div className="flex items-center space-x-4">
                {/* Auth buttons - only show when not signed in */}
                {!userProfile && !user && (
                  <div className="hidden md:block">
                    <HeaderAuthButtons variant="owner" />
                  </div>
                )}

                {/* Only show these when signed in */}
                {(userProfile || user) && (
                  <>
                    {/* Notification bell */}
                    <button className="p-2 text-gray-500 rounded-full hover:text-purple-600 hover:bg-gray-100">
                      <Bell className="h-5 w-5" />
                    </button>

                    {/* User dropdown - simplified for now */}
                    <div className="relative">
                      <div className="flex items-center">
                        <img
                          className="h-8 w-8 rounded-full"
                          src={user?.imageUrl || userProfile?.avatar_url || 'https://via.placeholder.com/32'}
                          alt={user?.fullName || `${userProfile?.first_name || ''} ${userProfile?.last_name || ''}` || 'User'}
                        />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <Outlet />
          </div>
        </main>

        {/* AI Assistant Button - available on all host portal pages */}
        <AIAssistantButton
          context={location.pathname.split('/').pop() || 'dashboard'}
          position="bottom-right"
        />
      </div>
    </div>
  );
}
