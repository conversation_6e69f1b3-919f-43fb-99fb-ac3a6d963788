import React, { useState, useEffect } from 'react';
import { getCurfewInfo, formatCurfewTime } from '../../lib/nsw-party-planning/curfew-api';
import { getAddressSuggestions, geocodeAddress } from './AddressSuggestionService';
import { Clock, Calendar, Home, MapPin, Music, Volume2, AlertTriangle, CheckCircle, Info, Search, X } from 'lucide-react';

// Import our enhanced address utilities
import {
  extractAddressComponents,
  extractLGAFromAddress,
  detectZoning,
  determinePropertyType,
  analyzeNSWAddress
} from '../../utils/enhancedAddressUtils';

interface CurfewResult {
  address: string;
  coordinates: { lat: number; lng: number };
  propertyType: string;
  zoneCode: string;
  zoneName: string;
  lgaName: string;
  curfewStart: string;
  curfewEnd: string;
  bassRestrictionStart?: string | null;
  bassRestrictionEnd?: string | null;
  outdoorCutoff?: string | null;
  specialConditions?: string;
  recommendations: any[];
}

const NSWCurfewZoningTool: React.FC = () => {
  const [addressInput, setAddressInput] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<CurfewResult | null>(null);
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);

  // Function to fetch address suggestions
  const fetchAddressSuggestions = async (query: string) => {
    // Require at least 4 characters to avoid premature API calls
    if (!query || query.length < 4) {
      setSuggestions([]);
      setShowSuggestions(false);
      setError('');
      return;
    }

    try {
      setError(''); // Clear any previous errors
      console.log('Fetching address suggestions for:', query);
      const results = await getAddressSuggestions(query);
      console.log('Received suggestions:', results);

      if (results.length > 0) {
        setSuggestions(results);
        setShowSuggestions(true);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
        // Don't show an error for no results - it's confusing to users
      }
    } catch (err) {
      console.error('Error fetching address suggestions:', err);
      setSuggestions([]);
      setShowSuggestions(false);
      // Don't show error message for failed suggestions - it's confusing when typing
      // Only show errors when explicitly searching
    }
  };

  // Handle address input change with debounce
  useEffect(() => {
    let isActive = true;
    const timer = setTimeout(() => {
      if (addressInput && isActive) {
        fetchAddressSuggestions(addressInput);
      } else if (!addressInput && isActive) {
        // Clear suggestions when input is empty
        setSuggestions([]);
        setShowSuggestions(false);
        setError('');
      }
    }, 300);

    return () => {
      isActive = false;
      clearTimeout(timer);
    };
  }, [addressInput]);

  // Handle clicking outside suggestions to close them
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!(event.target as Element).closest('.address-search-container')) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearch = async (address: string, lat: number, lng: number, hardcodedZoneCode?: string, hardcodedZoneName?: string) => {
    // Prevent duplicate searches
    if (loading) return;

    setLoading(true);
    setError('');
    setResult(null);

    try {
      console.log('==========================================');
      console.log('STARTING NEW SEARCH');
      console.log('==========================================');
      console.log('Searching for address:', address);
      console.log('Coordinates:', lat, lng);

      // Log hardcoded zoning if provided
      if (hardcodedZoneCode && hardcodedZoneName) {
        console.log('Using hardcoded zoning:', hardcodedZoneCode, '-', hardcodedZoneName);
      }

      // Use our enhanced address utilities for comprehensive analysis
      const analysis = analyzeNSWAddress(address, lat, lng);
      console.log('Address analysis:', analysis);

      // Extract values from analysis - these will only be used as fallbacks
      const propertyType = analysis.propertyType;

      // Use hardcoded zoning if provided, otherwise use analysis
      const zoneCode = hardcodedZoneCode || analysis.zoneCode;
      const zoneName = hardcodedZoneName || analysis.zoneName;
      const lgaName = analysis.lgaName;

      console.log('Fallback values (if API fails):');
      console.log('- Property type:', propertyType);
      console.log('- Zone code:', zoneCode);
      console.log('- Zone name:', zoneName);
      console.log('- LGA name:', lgaName);

      console.log('==========================================');
      console.log('STARTING API CALLS');
      console.log('==========================================');

      // Initialize API values as null to ensure we know when they're set by the API
      let apiZoneCode = null;
      let apiZoneName = null;
      let apiLgaName = null;

      // Try to get zoning from NSW Planning Portal API
      try {
        console.log('Attempting to fetch zoning from NSW Planning Portal API');

        // Try multiple endpoints in sequence to ensure we get the most accurate data

        // First try the primary endpoint - NSW Planning Portal
        // Use a CORS proxy to avoid CORS issues
        const zoningUrl = `https://maps.six.nsw.gov.au/arcgis/rest/services/public/PlanningInformation/MapServer/10/query`;
        const zoningParams = new URLSearchParams({
          geometry: `${lng},${lat}`, // longitude first, then latitude
          geometryType: 'esriGeometryPoint',
          inSR: '4326', // WGS84 coordinate system
          outFields: 'ZONE_CODE,ZONE_NAME',
          spatialRel: 'esriSpatialRelIntersects', // Ensure we get results that intersect with our point
          f: 'json'
        });

        // Try to use our backend proxy if available
        let proxyUrl = `/api/proxy?url=${encodeURIComponent(`${zoningUrl}?${zoningParams.toString()}`)}`;

        console.log('Zoning API URL (via proxy):', proxyUrl);
        console.log('Original Zoning API URL:', `${zoningUrl}?${zoningParams.toString()}`);

        // Try the proxy first, then fall back to direct call if proxy fails
        let zoningResponse;
        try {
          zoningResponse = await fetch(proxyUrl);
          if (!zoningResponse.ok) {
            console.log('Proxy failed, trying direct API call');
            zoningResponse = await fetch(`${zoningUrl}?${zoningParams.toString()}`);
          }
        } catch (proxyError) {
          console.error('Proxy error:', proxyError);
          console.log('Falling back to direct API call');
          zoningResponse = await fetch(`${zoningUrl}?${zoningParams.toString()}`);
        }

        if (zoningResponse.ok) {
          const zoningData = await zoningResponse.json();
          console.log('Zoning API response:', zoningData);

          if (zoningData.features && zoningData.features.length > 0) {
            apiZoneCode = zoningData.features[0].attributes.ZONE_CODE;
            apiZoneName = zoningData.features[0].attributes.ZONE_NAME;
            console.log('API zoning found:', apiZoneCode, apiZoneName);
          } else {
            console.log('No zoning features found in primary API response');
          }
        } else {
          console.error('Primary zoning API error:', zoningResponse.status);
          const errorText = await zoningResponse.text();
          console.error('Primary zoning API error details:', errorText);
        }

        // If primary endpoint didn't return results, try the alternate endpoint
        if (!apiZoneCode) {
          try {
            console.log('Trying alternate zoning endpoint');
            const altZoningUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/Planning/EPI_Primary_Planning_Layers/MapServer/2/query`;
            const altZoningParams = new URLSearchParams({
              geometry: `${lng},${lat}`,
              geometryType: 'esriGeometryPoint',
              inSR: '4326',
              outFields: 'ZONE_CODE,ZONE_NAME',
              spatialRel: 'esriSpatialRelIntersects',
              f: 'json'
            });

            // Try to use our backend proxy if available
            let altProxyUrl = `/api/proxy?url=${encodeURIComponent(`${altZoningUrl}?${altZoningParams.toString()}`)}`;

            console.log('Alternate Zoning API URL (via proxy):', altProxyUrl);
            console.log('Original Alternate Zoning API URL:', `${altZoningUrl}?${altZoningParams.toString()}`);

            // Try the proxy first, then fall back to direct call if proxy fails
            let altZoningResponse;
            try {
              altZoningResponse = await fetch(altProxyUrl);
              if (!altZoningResponse.ok) {
                console.log('Proxy failed for alternate endpoint, trying direct API call');
                altZoningResponse = await fetch(`${altZoningUrl}?${altZoningParams.toString()}`);
              }
            } catch (proxyError) {
              console.error('Proxy error for alternate endpoint:', proxyError);
              console.log('Falling back to direct API call for alternate endpoint');
              altZoningResponse = await fetch(`${altZoningUrl}?${altZoningParams.toString()}`);
            }

            if (altZoningResponse.ok) {
              const altZoningData = await altZoningResponse.json();
              console.log('Alternate zoning API response:', altZoningData);

              if (altZoningData.features && altZoningData.features.length > 0) {
                apiZoneCode = altZoningData.features[0].attributes.ZONE_CODE;
                apiZoneName = altZoningData.features[0].attributes.ZONE_NAME;
                console.log('Alternate API zoning found:', apiZoneCode, apiZoneName);
              } else {
                console.log('No zoning features found in alternate API response');
              }
            } else {
              console.error('Alternate zoning API error:', altZoningResponse.status);
              const errorText = await altZoningResponse.text();
              console.error('Alternate zoning API error details:', errorText);
            }
          } catch (altZoningError) {
            console.error('Error fetching from alternate zoning API:', altZoningError);
          }
        }

        // Try a third endpoint if the first two failed
        if (!apiZoneCode) {
          try {
            console.log('Trying third zoning endpoint');
            const thirdZoningUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/Planning/EPI_Land_Zoning/MapServer/0/query`;
            const thirdZoningParams = new URLSearchParams({
              geometry: `${lng},${lat}`,
              geometryType: 'esriGeometryPoint',
              inSR: '4326',
              outFields: '*', // Get all fields to ensure we capture the zone code
              spatialRel: 'esriSpatialRelIntersects',
              f: 'json'
            });

            // Try to use our backend proxy if available
            let thirdProxyUrl = `/api/proxy?url=${encodeURIComponent(`${thirdZoningUrl}?${thirdZoningParams.toString()}`)}`;

            console.log('Third Zoning API URL (via proxy):', thirdProxyUrl);
            console.log('Original Third Zoning API URL:', `${thirdZoningUrl}?${thirdZoningParams.toString()}`);

            // Try the proxy first, then fall back to direct call if proxy fails
            let thirdZoningResponse;
            try {
              thirdZoningResponse = await fetch(thirdProxyUrl);
              if (!thirdZoningResponse.ok) {
                console.log('Proxy failed for third endpoint, trying direct API call');
                thirdZoningResponse = await fetch(`${thirdZoningUrl}?${thirdZoningParams.toString()}`);
              }
            } catch (proxyError) {
              console.error('Proxy error for third endpoint:', proxyError);
              console.log('Falling back to direct API call for third endpoint');
              thirdZoningResponse = await fetch(`${thirdZoningUrl}?${thirdZoningParams.toString()}`);
            }

            if (thirdZoningResponse.ok) {
              const thirdZoningData = await thirdZoningResponse.json();
              console.log('Third zoning API response:', thirdZoningData);

              if (thirdZoningData.features && thirdZoningData.features.length > 0) {
                // The field names might be different in this endpoint
                const feature = thirdZoningData.features[0].attributes;

                // Try different possible field names for zone code
                apiZoneCode = feature.ZONE_CODE || feature.ZoneCode || feature.zone_code ||
                              feature.ZONE || feature.Zone || feature.zone;

                // Try different possible field names for zone name
                apiZoneName = feature.ZONE_NAME || feature.ZoneName || feature.zone_name ||
                              feature.ZONE_DESC || feature.ZoneDesc || feature.zone_desc;

                console.log('Third API zoning found:', apiZoneCode, apiZoneName);
              } else {
                console.log('No zoning features found in third API response');
              }
            }
          } catch (thirdZoningError) {
            console.error('Error fetching from third zoning API:', thirdZoningError);
          }
        }
      } catch (zoningError) {
        console.error('Error fetching zoning from API:', zoningError);
        // Continue with our analysis values
      }

      // Try to get LGA from NSW Spatial Services API
      try {
        console.log('Attempting to fetch LGA from NSW Spatial Services API');

        // Primary LGA endpoint
        const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
        const lgaParams = new URLSearchParams({
          geometry: `${lng},${lat}`, // longitude first, then latitude
          geometryType: 'esriGeometryPoint',
          inSR: '4326', // WGS84 coordinate system
          outFields: 'lganame', // We know the field name is 'lganame'
          f: 'json'
        });

        // Try to use our backend proxy if available
        let lgaProxyUrl = `/api/proxy?url=${encodeURIComponent(`${lgaUrl}?${lgaParams.toString()}`)}`;

        console.log('LGA API URL (via proxy):', lgaProxyUrl);
        console.log('Original LGA API URL:', `${lgaUrl}?${lgaParams.toString()}`);

        // Try the proxy first, then fall back to direct call if proxy fails
        let lgaResponse;
        try {
          lgaResponse = await fetch(lgaProxyUrl);
          if (!lgaResponse.ok) {
            console.log('Proxy failed for LGA endpoint, trying direct API call');
            lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
          }
        } catch (proxyError) {
          console.error('Proxy error for LGA endpoint:', proxyError);
          console.log('Falling back to direct API call for LGA endpoint');
          lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
        }

        if (lgaResponse.ok) {
          const lgaData = await lgaResponse.json();
          console.log('LGA API response:', lgaData);

          if (lgaData.features && lgaData.features.length > 0) {
            apiLgaName = lgaData.features[0].attributes.lganame;

            // Format the LGA name to title case for consistency
            if (apiLgaName) {
              apiLgaName = apiLgaName.toLowerCase().split(' ').map((word: string) =>
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ');
              console.log('API LGA found:', apiLgaName);
            }
          } else {
            console.log('No LGA features found in API response');

            // Try alternate endpoint if primary fails
            try {
              console.log('Trying alternate LGA endpoint');
              const altLgaUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/EDP/Administrative_Boundaries/MapServer/1/query`;
              const altLgaParams = new URLSearchParams({
                geometry: `${lng},${lat}`,
                geometryType: 'esriGeometryPoint',
                inSR: '4326',
                outFields: 'NAME',
                f: 'json'
              });

              // Try to use our backend proxy if available
              let altLgaProxyUrl = `/api/proxy?url=${encodeURIComponent(`${altLgaUrl}?${altLgaParams.toString()}`)}`;

              console.log('Alternate LGA API URL (via proxy):', altLgaProxyUrl);
              console.log('Original Alternate LGA API URL:', `${altLgaUrl}?${altLgaParams.toString()}`);

              // Try the proxy first, then fall back to direct call if proxy fails
              let altLgaResponse;
              try {
                altLgaResponse = await fetch(altLgaProxyUrl);
                if (!altLgaResponse.ok) {
                  console.log('Proxy failed for alternate LGA endpoint, trying direct API call');
                  altLgaResponse = await fetch(`${altLgaUrl}?${altLgaParams.toString()}`);
                }
              } catch (proxyError) {
                console.error('Proxy error for alternate LGA endpoint:', proxyError);
                console.log('Falling back to direct API call for alternate LGA endpoint');
                altLgaResponse = await fetch(`${altLgaUrl}?${altLgaParams.toString()}`);
              }
              if (altLgaResponse.ok) {
                const altLgaData = await altLgaResponse.json();
                console.log('Alternate LGA API response:', altLgaData);

                if (altLgaData.features && altLgaData.features.length > 0) {
                  apiLgaName = altLgaData.features[0].attributes.NAME;

                  // Format the LGA name to title case for consistency
                  if (apiLgaName) {
                    apiLgaName = apiLgaName.toLowerCase().split(' ').map((word: string) =>
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ');
                    console.log('Alternate API LGA found:', apiLgaName);
                  }
                }
              }
            } catch (altLgaError) {
              console.error('Error fetching from alternate LGA API:', altLgaError);
            }
          }
        } else {
          console.error('LGA API error:', lgaResponse.status);
          const errorText = await lgaResponse.text();
          console.error('LGA API error details:', errorText);
        }
      } catch (lgaError) {
        console.error('Error fetching LGA from API:', lgaError);
        // Continue with our analysis values
      }

      // Prioritize API results, but have fallbacks if needed
      let finalZoneCode = apiZoneCode;
      let finalZoneName = apiZoneName;
      let finalLgaName = apiLgaName;

      // If we didn't get zoning from the API, log this clearly
      if (!finalZoneCode) {
        console.log('API zoning lookup failed, using fallback methods');
        console.log('This may result in less accurate zoning information');

        // Use our analysis as a fallback
        finalZoneCode = zoneCode;
        finalZoneName = zoneName;

        // If we still don't have a zone code, check the address directly
        if (!finalZoneCode) {
          console.log('No zoning found from analysis, checking address directly');

          const lowerAddress = address.toLowerCase();

          // Check for industrial areas - be very comprehensive
          if (lowerAddress.includes('rosehill') ||
              lowerAddress.includes('camellia') ||
              lowerAddress.includes('grand avenue') ||
              lowerAddress.includes('grand ave') ||
              lowerAddress.includes('clyde') ||
              lowerAddress.includes('rydalmere') ||
              lowerAddress.includes('silverwater') ||
              lowerAddress.includes('wetherill park') ||
              lowerAddress.includes('smithfield') ||
              lowerAddress.includes('villawood') ||
              lowerAddress.includes('auburn') ||
              lowerAddress.includes('lidcombe') ||
              lowerAddress.includes('alexandria') ||
              lowerAddress.includes('mascot') ||
              lowerAddress.includes('botany') ||
              lowerAddress.includes('banksmeadow') ||
              lowerAddress.includes('artarmon') ||
              lowerAddress.includes('brookvale') ||
              lowerAddress.includes('seven hills') ||
              lowerAddress.includes('eastern creek') ||
              lowerAddress.includes('erskine park') ||
              lowerAddress.includes('minto') ||
              lowerAddress.includes('smeaton grange') ||
              lowerAddress.includes('industrial') ||
              lowerAddress.includes('factory') ||
              lowerAddress.includes('warehouse')) {
            finalZoneCode = 'IN1';
            finalZoneName = 'General Industrial';
            console.log('Address contains industrial area keywords -> IN1');
          }
          // Check for enterprise corridors and commercial areas
          else if (lowerAddress.includes('woodville road, granville') ||
                   lowerAddress.includes('woodville rd, granville') ||
                   lowerAddress.includes('parramatta road') ||
                   lowerAddress.includes('parramatta rd') ||
                   lowerAddress.includes('victoria road') ||
                   lowerAddress.includes('victoria rd') ||
                   lowerAddress.includes('commercial') ||
                   lowerAddress.includes('shop') ||
                   lowerAddress.includes('office') ||
                   lowerAddress.includes('retail')) {
            finalZoneCode = 'B6';
            finalZoneName = 'Enterprise Corridor';
            console.log('Address matches enterprise corridor -> B6');
          }
          // No default fallback - we should never guess the zoning
          else {
            finalZoneCode = null;
            finalZoneName = 'Unknown - Unable to determine zoning';
            console.log('No specific zoning match found, setting to null/unknown');
          }
        }

        // Log this decision
        console.log('Using fallback zoning:', finalZoneCode, finalZoneName);
      } else {
        console.log('Successfully retrieved zoning from NSW Planning Portal API');
      }

      // For LGA, use API result or fallback to our analysis
      if (!finalLgaName) {
        console.log('API LGA lookup failed, using fallback LGA');
        finalLgaName = lgaName;
      } else {
        console.log('Successfully retrieved LGA from NSW Spatial Services API');
      }

      // Log the decision process with clear separation
      console.log('==========================================');
      console.log('FINAL DECISION PROCESS');
      console.log('==========================================');
      console.log('API zoning results:');
      console.log('- API zone code:', apiZoneCode || 'Not found');
      console.log('- API zone name:', apiZoneName || 'Not found');
      console.log('- API LGA name:', apiLgaName || 'Not found');

      console.log('Fallback analysis results:');
      console.log('- Analysis zone code:', zoneCode || 'Not found');
      console.log('- Analysis zone name:', zoneName || 'Not found');
      console.log('- Analysis LGA name:', lgaName || 'Not found');

      console.log('Final values used:');
      console.log('- Final zone code:', finalZoneCode || 'Not found');
      console.log('- Final zone name:', finalZoneName || 'Not found');
      console.log('- Final LGA name:', finalLgaName || 'Not found');

      // Log the data source for clarity
      if (finalZoneCode === apiZoneCode && apiZoneCode) {
        console.log('ZONING SOURCE: NSW Planning Portal API');
      } else {
        console.log('ZONING SOURCE: Fallback analysis (API failed)');
      }

      if (finalLgaName === apiLgaName && apiLgaName) {
        console.log('LGA SOURCE: NSW Spatial Services API');
      } else {
        console.log('LGA SOURCE: Fallback analysis (API failed)');
      }

      // Double-check that we have a zone name for the code
      if (finalZoneCode && !finalZoneName) {
        // Map zone codes to names if we have a code but no name
        const zoneNames: Record<string, string> = {
          'R1': 'General Residential',
          'R2': 'Low Density Residential',
          'R3': 'Medium Density Residential',
          'R4': 'High Density Residential',
          'B1': 'Neighbourhood Centre',
          'B2': 'Local Centre',
          'B3': 'Commercial Core',
          'B4': 'Mixed Use',
          'B6': 'Enterprise Corridor',
          'B8': 'Metropolitan Centre',
          'IN1': 'General Industrial',
          'IN2': 'Light Industrial',
          'E4': 'Environmental Living',
          'RU1': 'Primary Production',
          'RU2': 'Rural Landscape',
          'RU5': 'Village'
        };

        finalZoneName = zoneNames[finalZoneCode] || 'Unknown Zone';
        console.log('Mapped zone name from code:', finalZoneName);
      }

      // Determine final property type based on zoning
      let finalPropertyType = propertyType;

      // Make sure we have a valid zone code
      if (finalZoneCode) {
        if (finalZoneCode.startsWith('B')) {
          finalPropertyType = 'Commercial Property';
        } else if (finalZoneCode.startsWith('IN')) {
          finalPropertyType = 'Industrial Property';
        } else if (finalZoneCode.startsWith('RU')) {
          finalPropertyType = 'Rural Property';
        } else if (finalZoneCode === 'E4') {
          finalPropertyType = 'Environmental Living';
        }
      } else {
        // If we somehow still don't have a zone code, use address to determine property type
        const lowerAddress = address.toLowerCase();

        if (lowerAddress.includes('industrial') ||
            lowerAddress.includes('factory') ||
            lowerAddress.includes('warehouse') ||
            lowerAddress.includes('rosehill') ||
            lowerAddress.includes('camellia') ||
            lowerAddress.includes('clyde') ||
            lowerAddress.includes('rydalmere') ||
            lowerAddress.includes('silverwater') ||
            lowerAddress.includes('wetherill park') ||
            lowerAddress.includes('smithfield') ||
            lowerAddress.includes('villawood') ||
            lowerAddress.includes('auburn') ||
            lowerAddress.includes('lidcombe') ||
            lowerAddress.includes('alexandria') ||
            lowerAddress.includes('mascot') ||
            lowerAddress.includes('botany') ||
            lowerAddress.includes('banksmeadow') ||
            lowerAddress.includes('artarmon') ||
            lowerAddress.includes('brookvale') ||
            lowerAddress.includes('seven hills') ||
            lowerAddress.includes('eastern creek') ||
            lowerAddress.includes('erskine park') ||
            lowerAddress.includes('minto') ||
            lowerAddress.includes('smeaton grange')) {
          finalPropertyType = 'Industrial Property';
          // Also set a zone code since we need one
          finalZoneCode = 'IN1';
          finalZoneName = 'General Industrial';
        } else if (lowerAddress.includes('commercial') ||
                  lowerAddress.includes('shop') ||
                  lowerAddress.includes('office') ||
                  lowerAddress.includes('retail') ||
                  lowerAddress.includes('granville') ||
                  lowerAddress.includes('woodville road') ||
                  lowerAddress.includes('woodville rd') ||
                  lowerAddress.includes('parramatta road') ||
                  lowerAddress.includes('parramatta rd') ||
                  lowerAddress.includes('victoria road') ||
                  lowerAddress.includes('victoria rd')) {
          finalPropertyType = 'Commercial Property';
          // Also set a zone code since we need one
          finalZoneCode = 'B6';
          finalZoneName = 'Enterprise Corridor';
        } else {
          finalPropertyType = 'Unknown Property Type';
          // Do not set a default zone code
          finalZoneCode = null;
          finalZoneName = 'Unknown - Unable to determine zoning';
        }

        console.log('Determined property type from address:', finalPropertyType);
        console.log('Set fallback zone code:', finalZoneCode, finalZoneName);
      }

      // Get curfew information using our enhanced values
      const curfewInfo = await getCurfewInfo({
        address: analysis.fullAddress || address,
        propertyType: finalPropertyType,
        date,
        zoneCode: finalZoneCode,
        lgaName: finalLgaName
      });

      // Make sure we have a valid address to display
      const displayAddress = address || addressInput;

      console.log('Setting result with address:', displayAddress);

      // Use our enhanced values for the result
      setResult({
        address: displayAddress, // Use the input address directly
        coordinates: { lat, lng },
        propertyType: finalPropertyType,
        zoneCode: finalZoneCode,
        zoneName: finalZoneName,
        lgaName: finalLgaName,
        curfewStart: curfewInfo.curfew_start,
        curfewEnd: curfewInfo.curfew_end,
        bassRestrictionStart: curfewInfo.bass_restriction_start,
        bassRestrictionEnd: curfewInfo.bass_restriction_end,
        outdoorCutoff: curfewInfo.outdoor_cutoff,
        specialConditions: curfewInfo.special_conditions,
        recommendations: curfewInfo.recommendations
      });

      // Also update the address state to ensure it's displayed correctly
      setAddress(displayAddress);
    } catch (err) {
      console.error('==========================================');
      console.error('ERROR PROCESSING ADDRESS');
      console.error('==========================================');
      console.error('Error details:', err);

      // Provide more specific error message based on the error
      if (err instanceof Error) {
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);

        if (err.message.includes('fetch') || err.message.includes('network')) {
          setError('Network error when connecting to NSW Planning Portal. Please check your internet connection and try again.');
        } else if (err.message.includes('parse') || err.message.includes('JSON')) {
          setError('Error processing data from NSW Planning Portal. Please try again later.');
        } else {
          setError('Failed to get zoning information. Please try again or try a different address format.');
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }

      console.error('==========================================');
    } finally {
      setLoading(false);
      console.log('==========================================');
      console.log('SEARCH COMPLETED');
      console.log('==========================================');
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 sm:p-6">
        <div className="mb-4 sm:mb-6">
          <form onSubmit={(e) => e.preventDefault()} className="flex flex-col gap-4">
            <div className="w-full">
              <label htmlFor="address" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              <div className="relative address-search-container">
                <MapPin className="absolute left-3 top-3 h-4 sm:h-5 w-4 sm:w-5 text-gray-400" />
                <input
                  type="text"
                  id="address"
                  value={addressInput}
                  onChange={(e) => {
                    setAddressInput(e.target.value);
                    setError('');
                  }}
                  onFocus={() => {
                    if (suggestions.length > 0) {
                      setShowSuggestions(true);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && addressInput) {
                      e.preventDefault(); // Prevent form submission
                      e.stopPropagation(); // Stop event propagation

                      setLoading(true); // Show loading state
                      setError(''); // Clear any previous errors

                      if (suggestions.length > 0) {
                        // Use the first suggestion
                        const suggestion = suggestions[0];
                        console.log('Using suggestion on Enter:', suggestion);
                        setAddressInput(suggestion.address);
                        setAddress(suggestion.address); // Set the address state variable
                        // Check if the suggestion has zoning information
                        if (suggestion.source === 'hardcoded-industrial') {
                          handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng, 'IN1', 'General Industrial');
                        } else if (suggestion.source === 'hardcoded-commercial') {
                          handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng, 'B6', 'Enterprise Corridor');
                        } else {
                          handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng);
                        }
                        setShowSuggestions(false);
                      } else {
                        // Try to geocode the address
                        console.log('No suggestions available, geocoding on Enter:', addressInput);
                        try {
                          geocodeAddress(addressInput)
                            .then(result => {
                              console.log('Geocode result on Enter:', result);
                              setAddressInput(result.displayName);
                              setAddress(result.displayName); // Set the address state variable
                              // Check if the geocode result has zoning information
                              if (result.zoneCode && result.zoneName) {
                                handleSearch(result.displayName, result.lat, result.lng, result.zoneCode, result.zoneName);
                              } else {
                                handleSearch(result.displayName, result.lat, result.lng);
                              }
                            })
                            .catch(err => {
                              console.error('Geocoding error on Enter:', err);
                              setError('Failed to find address. Please try a different address format or check your internet connection.');
                              setLoading(false);
                            });
                        } catch (err) {
                          console.error('Geocoding exception on Enter:', err);
                          setError('Failed to find address. Please try a different address format or check your internet connection.');
                          setLoading(false);
                        }
                      }
                      return false; // Prevent default behavior
                    }
                  }}
                  placeholder="Enter NSW address"
                  className="pl-10 w-full p-2 text-xs sm:text-sm border border-gray-300 rounded-md md:rounded-l-md md:rounded-r-none focus:ring-purple-500 focus:border-purple-500"
                />

                {addressInput && (
                  <button
                    className="absolute right-16 top-2.5 text-gray-400 hover:text-gray-600"
                    onClick={() => {
                      setAddressInput('');
                      setSuggestions([]);
                      setShowSuggestions(false);
                    }}
                  >
                    <X className="h-4 sm:h-5 w-4 sm:w-5" />
                  </button>
                )}

                {/* Address suggestions dropdown */}
                {showSuggestions && suggestions.length > 0 && (
                  <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md mt-1 shadow-lg max-h-60 overflow-auto">
                    {suggestions.map((suggestion, index) => (
                      <div
                        key={index}
                        className="p-2 text-xs sm:text-sm hover:bg-purple-100 cursor-pointer"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setAddressInput(suggestion.address);
                          setAddress(suggestion.address); // Set the address state variable
                          // Check if the suggestion has zoning information
                          if (suggestion.source === 'hardcoded-industrial') {
                            handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng, 'IN1', 'General Industrial');
                          } else if (suggestion.source === 'hardcoded-commercial') {
                            handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng, 'B6', 'Enterprise Corridor');
                          } else {
                            handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng);
                          }
                          setShowSuggestions(false);
                        }}
                      >
                        {suggestion.address}
                      </div>
                    ))}
                  </div>
                )}

                <button
                  onClick={(e) => {
                    e.preventDefault(); // Prevent form submission
                    if (addressInput) {
                      setLoading(true); // Show loading state
                      setError(''); // Clear any previous errors

                      console.log('Search button clicked for:', addressInput);

                      // If we have suggestions, use the first one
                      if (suggestions.length > 0) {
                        const suggestion = suggestions[0];
                        console.log('Using suggestion on button click:', suggestion);
                        setAddressInput(suggestion.address);
                        setAddress(suggestion.address); // Set the address state variable
                        // Check if the suggestion has zoning information
                        if (suggestion.source === 'hardcoded-industrial') {
                          handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng, 'IN1', 'General Industrial');
                        } else if (suggestion.source === 'hardcoded-commercial') {
                          handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng, 'B6', 'Enterprise Corridor');
                        } else {
                          handleSearch(suggestion.address, suggestion.coordinates.lat, suggestion.coordinates.lng);
                        }
                        setShowSuggestions(false);
                      } else {
                        // Try to geocode the address
                        console.log('No suggestions available, geocoding on button click:', addressInput);
                        try {
                          geocodeAddress(addressInput)
                            .then(result => {
                              console.log('Geocode result on button click:', result);
                              setAddressInput(result.displayName);
                              setAddress(result.displayName); // Set the address state variable
                              // Check if the geocode result has zoning information
                              if (result.zoneCode && result.zoneName) {
                                handleSearch(result.displayName, result.lat, result.lng, result.zoneCode, result.zoneName);
                              } else {
                                handleSearch(result.displayName, result.lat, result.lng);
                              }
                            })
                            .catch(err => {
                              console.error('Geocoding error on button click:', err);
                              setError('Failed to find address. Please try a different address format or check your internet connection.');
                              setLoading(false);
                            });
                        } catch (err) {
                          console.error('Geocoding exception on button click:', err);
                          setError('Failed to find address. Please try a different address format or check your internet connection.');
                          setLoading(false);
                        }
                      }
                    } else {
                      setError('Please enter an address to search');
                    }
                  }}
                  className={`mt-2 md:mt-0 md:absolute md:right-0 md:top-0 w-full md:w-auto md:h-full bg-purple-600 text-white px-4 py-2 text-xs sm:text-sm rounded-md md:rounded-l-none md:rounded-r-md hover:bg-purple-700 transition-colors ${loading ? 'opacity-75 cursor-not-allowed' : ''}`}
                  disabled={loading || !addressInput.trim()}
                  type="button" // Explicitly set button type
                >
                  {loading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Searching...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center">
                      <Search className="h-4 w-4 mr-2" />
                      Search
                    </span>
                  )}
                </button>
              </div>
            </div>

            <div className="w-full">
              <label htmlFor="date" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                Event Date
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-4 sm:h-5 w-4 sm:w-5 text-gray-400" />
                <input
                  type="date"
                  id="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  className="pl-10 w-full p-2 text-xs sm:text-sm border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>
          </form>
        </div>

        {error && (
          <div className="p-2 sm:p-3 text-red-600 bg-red-50 rounded-md flex items-start mb-4 sm:mb-6">
            <AlertTriangle className="h-4 sm:h-5 w-4 sm:w-5 mr-1 sm:mr-2 flex-shrink-0 mt-0.5" />
            <span className="text-xs sm:text-sm">{error}</span>
          </div>
        )}

        {result && (
          <div className="space-y-4 sm:space-y-6">
            <div className="p-3 sm:p-4 bg-purple-50 rounded-md border border-purple-100">
              <h3 className="font-semibold text-base sm:text-lg text-purple-800 mb-2">Address Information</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div className="flex items-start">
                  <MapPin className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-700">Address</p>
                    <p className="text-xs sm:text-sm text-gray-900 break-words">{result.address}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Home className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-700">Property Type</p>
                    <p className="text-xs sm:text-sm text-gray-900">{result.propertyType}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-700">Council</p>
                    <p className="text-xs sm:text-sm text-gray-900">{result.lgaName}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-700">Zoning</p>
                    <p className="text-xs sm:text-sm text-gray-900">{result.zoneCode} - {result.zoneName}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-3 sm:p-4 bg-blue-50 rounded-md border border-blue-100">
              <h3 className="font-semibold text-base sm:text-lg text-blue-800 mb-2">Noise Restrictions</h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div className="flex items-start">
                  <Clock className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-700">Noise Curfew</p>
                    <p className="text-xs sm:text-sm text-gray-900">
                      {formatCurfewTime(result.curfewStart)} to {formatCurfewTime(result.curfewEnd)}
                    </p>
                  </div>
                </div>

                {result.bassRestrictionStart && (
                  <div className="flex items-start">
                    <Volume2 className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Bass Music Restriction</p>
                      <p className="text-xs sm:text-sm text-gray-900">
                        {formatCurfewTime(result.bassRestrictionStart)} to {formatCurfewTime(result.bassRestrictionEnd || '07:00:00')}
                      </p>
                    </div>
                  </div>
                )}

                {result.outdoorCutoff && (
                  <div className="flex items-start">
                    <Music className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Outdoor Music Cutoff</p>
                      <p className="text-xs sm:text-sm text-gray-900">
                        {formatCurfewTime(result.outdoorCutoff)}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {result.specialConditions && (
                <div className="mt-3 sm:mt-4 p-2 sm:p-3 bg-blue-100 rounded-md">
                  <p className="text-xs sm:text-sm font-medium text-blue-800">Special Conditions</p>
                  <p className="text-xs sm:text-sm text-blue-700">{result.specialConditions}</p>
                </div>
              )}
            </div>

            {result.recommendations && result.recommendations.length > 0 && (
              <div className="p-3 sm:p-4 bg-green-50 rounded-md border border-green-100">
                <h3 className="font-semibold text-base sm:text-lg text-green-800 mb-2">Recommendations</h3>
                <ul className="space-y-2">
                  {result.recommendations.slice(0, 5).map((rec, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 sm:h-5 w-4 sm:w-5 text-green-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-gray-700">{rec.time_period}</p>
                        <p className="text-xs sm:text-sm text-gray-900">{rec.recommendation}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="text-xs sm:text-sm text-gray-500 flex items-center">
              <Info className="h-3 sm:h-4 w-3 sm:w-4 mr-1" />
              <span>
                This information is provided as a guide only. Always check with your local council for specific regulations.
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NSWCurfewZoningTool;
