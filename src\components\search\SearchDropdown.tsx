import React, { useRef } from 'react';
import { useClickOutside } from '../../hooks/useClickOutside';

interface SearchDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
  width?: string;
}

export default function SearchDropdown({
  isOpen,
  onClose,
  children,
  className = '',
  width = 'auto'
}: SearchDropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null);
  useClickOutside(dropdownRef, onClose);

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className={`absolute bg-white rounded-xl shadow-lg border border-gray-100 ${className}`}
      onClick={(e) => e.stopPropagation()}
      style={{
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1)',
        zIndex: 10000,
        top: '100%',
        left: 0,
        marginTop: '12px',
        width: width === 'auto' ? 'max-content' : width,
        minWidth: '280px',
        maxWidth: '400px'
      }}
    >
      {children}
    </div>
  );
}