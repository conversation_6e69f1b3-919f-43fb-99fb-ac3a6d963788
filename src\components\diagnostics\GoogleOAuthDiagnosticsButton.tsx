import React, { useState } from 'react';
import { logGoogleOAuthDiagnostics, createGoogleOAuthReport, attemptGoogleOAuthFix, isLikelyGoogleOAuthUser } from '../../utils/google-oauth-debug';

/**
 * Google OAuth Diagnostics Button
 * 
 * This component provides a UI for users to run diagnostics on Google OAuth issues.
 * It's specifically designed to help troubleshoot Google OAuth login problems.
 */
const GoogleOAuthDiagnosticsButton = () => {
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [diagnosticsResult, setDiagnosticsResult] = useState<any>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [fixAttempted, setFixAttempted] = useState(false);
  const [fixResult, setFixResult] = useState<any>(null);
  
  // Only show this for likely Google OAuth users
  const isGoogleUser = isLikelyGoogleOAuthUser();
  
  if (!isGoogleUser) {
    return null;
  }

  const runDiagnostics = () => {
    const results = logGoogleOAuthDiagnostics();
    setDiagnosticsResult(results);
    setShowDiagnostics(true);
  };

  const createReport = () => {
    const report = createGoogleOAuthReport();
    setDiagnosticsResult(report);
    setShowDiagnostics(true);
  };

  const copyToClipboard = () => {
    if (diagnosticsResult) {
      navigator.clipboard.writeText(JSON.stringify(diagnosticsResult, null, 2))
        .then(() => {
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy diagnostics:', err);
        });
    }
  };

  const attemptFix = () => {
    const result = attemptGoogleOAuthFix();
    setFixResult(result);
    setFixAttempted(true);
  };

  if (!showDiagnostics) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={runDiagnostics}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-full shadow-lg flex items-center text-sm"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          Google Sign-In Issues?
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl p-4 w-80 max-h-[80vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Google Sign-In Diagnostics</h3>
        <button 
          onClick={() => setShowDiagnostics(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">
          Having trouble with Google Sign-In? This tool helps diagnose authentication issues.
        </p>
        
        <div className="flex space-x-2 mb-4">
          <button
            onClick={runDiagnostics}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
          >
            Run Diagnostics
          </button>
          <button
            onClick={createReport}
            className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm"
          >
            Create Report
          </button>
          <button
            onClick={attemptFix}
            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
          >
            Try Fix
          </button>
        </div>
        
        {fixAttempted && (
          <div className={`text-sm p-2 rounded mb-3 ${fixResult?.fixed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
            {fixResult?.fixed ? 
              `Fix attempted: ${fixResult.type}. Please try signing in again.` : 
              'No automatic fixes available. Please try signing in again or contact support.'}
          </div>
        )}
      </div>

      {diagnosticsResult && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-medium text-sm">Diagnostics Results</h4>
            <button
              onClick={copyToClipboard}
              className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
            >
              {copySuccess ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <div className="bg-gray-100 p-2 rounded-md text-xs overflow-auto max-h-40">
            <pre>{JSON.stringify(diagnosticsResult, null, 2)}</pre>
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500">
        <p>
          If issues persist, please copy the diagnostics and contact support.
        </p>
        <div className="mt-2 pt-2 border-t border-gray-200">
          <button
            onClick={() => {
              localStorage.removeItem('google_oauth_flow');
              localStorage.removeItem('auth_attempts');
              window.location.href = '/login';
            }}
            className="text-red-600 hover:text-red-800"
          >
            Reset & Try Again
          </button>
        </div>
      </div>
    </div>
  );
};

export default GoogleOAuthDiagnosticsButton;
