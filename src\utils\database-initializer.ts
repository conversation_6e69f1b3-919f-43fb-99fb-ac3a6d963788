/**
 * Database Initializer
 * 
 * This utility handles the initialization of the Supabase database,
 * creating necessary tables and functions if they don't exist.
 */

import { supabase } from '../lib/supabase-client';

// Flag to track if initialization has been attempted
let initializationAttempted = false;
let initializationSuccessful = false;

/**
 * Initialize the database by creating necessary tables and functions
 */
export async function initializeDatabase(): Promise<boolean> {
  // Only attempt initialization once
  if (initializationAttempted) {
    return initializationSuccessful;
  }
  
  initializationAttempted = true;
  console.log('Initializing database...');
  
  try {
    // Step 1: Create the exec_sql function
    const execSqlCreated = await createExecSqlFunction();
    
    // Step 2: Create the user_profiles table
    const userProfilesCreated = await createUserProfilesTable();
    
    // Step 3: Create the admin_users table
    const adminUsersCreated = await createAdminUsersTable();
    
    // Log results
    console.log('Database initialization results:', {
      execSqlCreated,
      userProfilesCreated,
      adminUsersCreated
    });
    
    // Consider initialization successful if at least the user_profiles table was created
    initializationSuccessful = userProfilesCreated;
    return initializationSuccessful;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
}

/**
 * Create the exec_sql function in Supabase
 */
async function createExecSqlFunction(): Promise<boolean> {
  try {
    console.log('Creating exec_sql function...');
    
    // Try to create the function using direct SQL
    const response = await fetch(`${supabase.supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabase.supabaseKey,
        'Authorization': `Bearer ${supabase.supabaseKey}`
      },
      body: JSON.stringify({
        sql_query: `
          CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
          RETURNS void AS $$
          BEGIN
            EXECUTE sql_query;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `
      })
    });
    
    // Check if the function already exists
    if (response.status === 404) {
      console.log('exec_sql function does not exist, creating directly...');
      
      // Create the function using direct SQL
      const { error } = await supabase.rpc('exec_sql', {
        sql_query: `
          CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
          RETURNS void AS $$
          BEGIN
            EXECUTE sql_query;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
          
          -- Grant execute permission
          GRANT EXECUTE ON FUNCTION exec_sql TO authenticated;
          GRANT EXECUTE ON FUNCTION exec_sql TO anon;
          GRANT EXECUTE ON FUNCTION exec_sql TO service_role;
        `
      });
      
      if (error) {
        console.error('Error creating exec_sql function:', error);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Exception creating exec_sql function:', error);
    return false;
  }
}

/**
 * Create the user_profiles table in Supabase
 */
async function createUserProfilesTable(): Promise<boolean> {
  try {
    console.log('Creating user_profiles table...');
    
    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('user_profiles')
      .select('count(*)', { count: 'exact', head: true });
    
    // If the table doesn't exist
    if (checkError && checkError.code === '42P01') {
      console.log('user_profiles table does not exist, creating...');
      
      // Create the table using direct SQL
      const { error } = await supabase.rpc('exec_sql', {
        sql_query: `
          CREATE TABLE IF NOT EXISTS user_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            clerk_id TEXT UNIQUE NOT NULL,
            email TEXT NOT NULL,
            role TEXT DEFAULT 'guest',
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            bio TEXT,
            phone TEXT,
            is_host BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );
          
          -- Set up RLS
          ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
          
          -- Create policies
          CREATE POLICY "Users can view their own profile"
            ON user_profiles
            FOR SELECT
            USING (auth.uid() = id);
            
          CREATE POLICY "Users can update their own profile"
            ON user_profiles
            FOR UPDATE
            USING (auth.uid() = id);
            
          -- Allow public read access for basic user info
          CREATE POLICY "Public read access for basic user info"
            ON user_profiles
            FOR SELECT
            USING (true);
        `
      });
      
      if (error) {
        console.error('Error creating user_profiles table:', error);
        return false;
      }
    } else {
      console.log('user_profiles table already exists');
    }
    
    return true;
  } catch (error) {
    console.error('Exception creating user_profiles table:', error);
    return false;
  }
}

/**
 * Create the admin_users table in Supabase
 */
async function createAdminUsersTable(): Promise<boolean> {
  try {
    console.log('Creating admin_users table...');
    
    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('admin_users')
      .select('count(*)', { count: 'exact', head: true });
    
    // If the table doesn't exist
    if (checkError && checkError.code === '42P01') {
      console.log('admin_users table does not exist, creating...');
      
      // Create the table using direct SQL
      const { error } = await supabase.rpc('exec_sql', {
        sql_query: `
          CREATE TABLE IF NOT EXISTS admin_users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email TEXT UNIQUE NOT NULL,
            clerk_id TEXT UNIQUE,
            is_super_admin BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );
          
          -- Set up RLS
          ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
          
          -- Create policies
          CREATE POLICY "Admins can view all admin users"
            ON admin_users
            FOR SELECT
            USING (
              auth.uid() IN (SELECT id FROM admin_users)
            );
            
          CREATE POLICY "Only super admins can insert/update admin users"
            ON admin_users
            FOR ALL
            USING (
              auth.uid() IN (SELECT id FROM admin_users WHERE is_super_admin = true)
            );
        `
      });
      
      if (error) {
        console.error('Error creating admin_users table:', error);
        return false;
      }
    } else {
      console.log('admin_users table already exists');
    }
    
    return true;
  } catch (error) {
    console.error('Exception creating admin_users table:', error);
    return false;
  }
}
