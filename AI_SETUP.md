# AI Integration Setup for HouseGoing

This document provides instructions for setting up the AI integration for the HouseGoing platform.

## Hugging Face API Setup

The HouseGoing platform uses Hugging Face's API to power its AI assistants. Follow these steps to set up your Hugging Face API key:

1. Create a Hugging Face account at [huggingface.co](https://huggingface.co/join)
2. Generate an API key at [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
3. Copy your API key and add it to the `.env.local` file:

```
NEXT_PUBLIC_HUGGINGFACE_API_KEY=your_huggingface_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
```

## Models Used

The HouseGoing AI integration uses the following models:

### Primary Model
- **DeepSeek V3**: `deepseek-ai/DeepSeek-V3-0324`
  - A powerful model for generating high-quality responses
  - Used for both host and sales assistants

### Fallback Models
- **Qwen 2.5 Omni**: `Qwen/Qwen2.5-Omni-7B`
  - Used as a first fallback if <PERSON><PERSON><PERSON> is unavailable
- **Mistral 7B Instruct**: `mistralai/Mistral-7B-Instruct-v0.3`
  - Used as a second fallback if both DeepSeek and <PERSON>wen are unavailable

## Implementation Details

The AI integration is implemented in the following files:

- `src/lib/ai/huggingface.js`: Contains the core API integration code
- `src/pages/api/ai-chat.js`: API endpoint for AI chat functionality
- `src/components/host/HostAIAssistant.jsx`: Host AI assistant component
- `src/components/chat/SalesAssistantChat.jsx`: Sales AI assistant component

## Troubleshooting

If you encounter issues with the AI integration:

1. Check that your Hugging Face API key is correctly set in the `.env.local` file
2. Ensure you have access to the models (you may need to accept the model terms on Hugging Face)
3. Check the browser console for error messages
4. Try restarting the development server

## LangSmith Integration (Optional)

For advanced monitoring and debugging of AI interactions, LangSmith is integrated:

1. Create a LangSmith account at [smith.langchain.com](https://smith.langchain.com/)
2. Set up your LangSmith API key and organization ID in the `.env.local` file
3. Enable tracing by setting `LANGSMITH_TRACING=true`

This will allow you to monitor and debug AI interactions through the LangSmith dashboard.
