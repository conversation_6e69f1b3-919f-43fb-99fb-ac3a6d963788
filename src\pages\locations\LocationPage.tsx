import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import SEO from '../../components/seo/SEO';
import JsonLd from '../../components/seo/JsonLd';
import { MapPin, Users, Calendar, Star, ArrowRight, Phone, Mail } from 'lucide-react';

interface LocationData {
  name: string;
  description: string;
  population: string;
  popularVenues: string[];
  averagePrice: string;
  bestFor: string[];
  nearbyAttractions: string[];
  transportLinks: string[];
  noiseRestrictions: string;
  councilInfo: {
    name: string;
    phone: string;
    website: string;
  };
}

const locationData: Record<string, LocationData> = {
  'sydney-cbd': {
    name: 'Sydney CBD',
    description: 'The heart of Sydney offers premium party venues with stunning harbor views, rooftop terraces, and sophisticated event spaces perfect for corporate events and upscale celebrations.',
    population: '17,252',
    popularVenues: ['Rooftop Bars', 'Hotel Function Rooms', 'Waterfront Venues', 'Corporate Event Spaces'],
    averagePrice: '$150-400/hour',
    bestFor: ['Corporate Events', 'Cocktail Parties', 'Product Launches', 'Networking Events'],
    nearbyAttractions: ['Sydney Opera House', 'Harbour Bridge', 'Royal Botanic Gardens', 'Circular Quay'],
    transportLinks: ['Central Station', 'Circular Quay', 'Wynyard Station', 'Multiple Bus Routes'],
    noiseRestrictions: 'Strict noise limits after 10 PM on weekdays, midnight on weekends',
    councilInfo: {
      name: 'City of Sydney Council',
      phone: '(02) 9265 9333',
      website: 'https://www.cityofsydney.nsw.gov.au'
    }
  },
  'bondi-beach': {
    name: 'Bondi Beach',
    description: 'Australia\'s most famous beach destination offers beachfront venues, surf clubs, and coastal event spaces with ocean views and relaxed beach vibes.',
    population: '11,513',
    popularVenues: ['Beach Clubs', 'Surf Life Saving Clubs', 'Beachfront Restaurants', 'Coastal Function Centres'],
    averagePrice: '$100-250/hour',
    bestFor: ['Beach Parties', 'Summer Celebrations', 'Casual Gatherings', 'Surf-themed Events'],
    nearbyAttractions: ['Bondi Beach', 'Bondi to Coogee Walk', 'Bondi Markets', 'Icebergs Pool'],
    transportLinks: ['Bondi Junction Station', 'Bus Routes 380, 381, 382', 'Bondi Beach Bus Terminal'],
    noiseRestrictions: 'Beach area restrictions apply, 9 PM limit for outdoor events',
    councilInfo: {
      name: 'Waverley Council',
      phone: '(02) 9083 8000',
      website: 'https://www.waverley.nsw.gov.au'
    }
  },
  'parramatta': {
    name: 'Parramatta',
    description: 'Western Sydney\'s business hub features modern venues, cultural centers, and riverside locations perfect for diverse celebrations and corporate events.',
    population: '25,398',
    popularVenues: ['Riverside Venues', 'Cultural Centres', 'Modern Function Rooms', 'Park Pavilions'],
    averagePrice: '$80-200/hour',
    bestFor: ['Cultural Events', 'Family Celebrations', 'Community Gatherings', 'Business Functions'],
    nearbyAttractions: ['Parramatta Park', 'Riverside Theatres', 'Old Government House', 'Westfield Parramatta'],
    transportLinks: ['Parramatta Station', 'Light Rail', 'Multiple Bus Routes', 'Ferry Services'],
    noiseRestrictions: 'Standard residential limits, special permits available for events',
    councilInfo: {
      name: 'City of Parramatta Council',
      phone: '(02) 9806 5050',
      website: 'https://www.cityofparramatta.nsw.gov.au'
    }
  },
  // Sydney sublocations
  'sydney/inner-west': {
    name: 'Inner West Sydney',
    description: 'Trendy venues in Sydney\'s creative inner west suburbs including Newtown, Marrickville, and Leichhardt',
    population: '180,000+',
    popularVenues: ['Warehouse venues', 'Art galleries', 'Rooftop bars', 'Creative spaces'],
    averagePrice: '$250-600/hour',
    bestFor: ['Creative Events', 'Art Exhibitions', 'Alternative Weddings', 'Corporate Functions'],
    nearbyAttractions: ['King Street Newtown', 'Carriageworks', 'Sydney Park', 'Enmore Theatre'],
    transportLinks: ['Multiple Train Stations', 'Light Rail', 'Bus Networks', 'Cycling Paths'],
    noiseRestrictions: 'Mixed residential/commercial zones, check with individual venues',
    councilInfo: {
      name: 'Inner West Council',
      phone: '(02) 9392 5000',
      website: 'https://www.innerwest.nsw.gov.au'
    }
  },
  'sydney/eastern-suburbs': {
    name: 'Eastern Suburbs Sydney',
    description: 'Upscale venues in Sydney\'s prestigious eastern suburbs with harbor and ocean views',
    population: '250,000+',
    popularVenues: ['Beachside venues', 'Luxury function rooms', 'Harbor venues', 'Golf clubs'],
    averagePrice: '$400-800/hour',
    bestFor: ['Luxury Events', 'Corporate Functions', 'Weddings', 'High-end Celebrations'],
    nearbyAttractions: ['Bondi Beach', 'Coogee Beach', 'Royal Botanic Gardens', 'Double Bay'],
    transportLinks: ['Eastern Suburbs Railway', 'Bus Networks', 'Ferry Services', 'Taxi/Uber'],
    noiseRestrictions: 'Strict residential noise limits, early finish times common',
    councilInfo: {
      name: 'Waverley Council / Randwick Council',
      phone: '(02) 9083 8000',
      website: 'https://www.waverley.nsw.gov.au'
    }
  },
  'sydney/north-shore': {
    name: 'North Shore Sydney',
    description: 'Family-friendly venues across Sydney\'s leafy north shore suburbs',
    population: '300,000+',
    popularVenues: ['Community halls', 'Garden venues', 'Function centers', 'Sports clubs'],
    averagePrice: '$300-650/hour',
    bestFor: ['Family Events', 'Community Gatherings', 'Corporate Functions', 'Celebrations'],
    nearbyAttractions: ['Lane Cove National Park', 'Chatswood', 'Macquarie Centre', 'North Sydney'],
    transportLinks: ['North Shore Railway', 'Metro Northwest', 'Bus Networks', 'Ferry Services'],
    noiseRestrictions: 'Family-friendly areas with standard residential limits',
    councilInfo: {
      name: 'North Sydney Council',
      phone: '(02) 9936 8100',
      website: 'https://www.northsydney.nsw.gov.au'
    }
  },
  'sydney/western-sydney': {
    name: 'Western Sydney',
    description: 'Affordable venues in Sydney\'s growing western suburbs with great value and accessibility',
    population: '800,000+',
    popularVenues: ['Function centers', 'Community venues', 'Sports clubs', 'Cultural centers'],
    averagePrice: '$200-450/hour',
    bestFor: ['Large Events', 'Community Celebrations', 'Corporate Functions', 'Cultural Events'],
    nearbyAttractions: ['Penrith Panthers', 'Blacktown Arts Centre', 'Western Sydney Parklands', 'Auburn Botanic Gardens'],
    transportLinks: ['Western Railway', 'Metro Southwest', 'Extensive Bus Networks', 'M4/M7 Motorways'],
    noiseRestrictions: 'Generally more flexible, check with local councils',
    councilInfo: {
      name: 'Various Western Sydney Councils',
      phone: '(02) 4732 7777',
      website: 'https://www.penrithcity.nsw.gov.au'
    }
  }
};

export default function LocationPage() {
  const { location, sublocation } = useParams<{ location: string; sublocation?: string }>();

  // Handle sublocation routing (e.g., /locations/sydney/inner-west)
  const locationKey = sublocation ? `${location}/${sublocation}` : location;
  const locationInfo = locationKey ? locationData[locationKey] || locationData[location || ''] : null;

  if (!locationInfo) {
    return (
      <div className="pt-32 px-4 text-center">
        <h1 className="text-2xl font-bold mb-4">Location Not Found</h1>
        <Link to="/find-venues" className="text-purple-600 hover:text-purple-700">
          Browse All Venues
        </Link>
      </div>
    );
  }

  const structuredData = {
    name: `Party Venues in ${locationInfo.name}`,
    description: locationInfo.description,
    url: `https://housegoing.com.au/locations/${location}`,
    areaServed: {
      '@type': 'City',
      name: locationInfo.name,
      addressRegion: 'NSW',
      addressCountry: 'AU'
    },
    priceRange: locationInfo.averagePrice,
    serviceType: 'Venue Rental'
  };

  return (
    <>
      <SEO
        title={`Party Venues in ${locationInfo.name} | Book Event Spaces | HouseGoing`}
        description={`Find the perfect party venue in ${locationInfo.name}. ${locationInfo.description} Average prices ${locationInfo.averagePrice}. Book now!`}
        keywords={`party venues ${locationInfo.name}, event spaces ${locationInfo.name}, ${locationInfo.name} venue rental, party planning ${locationInfo.name}`}
        url={`https://housegoing.com.au/locations/${location}`}
      />
      
      <JsonLd type="LocalBusiness" data={structuredData} />

      <div className="pt-32 px-4 sm:px-6 max-w-7xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <MapPin className="h-8 w-8 text-purple-600 mr-2" />
            <h1 className="text-4xl font-bold text-gray-900">
              Party Venues in {locationInfo.name}
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
            {locationInfo.description}
          </p>
          <Link
            to={`/find-venues?location=${locationInfo.name}`}
            className="inline-flex items-center px-8 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Browse Available Venues
            <ArrowRight className="h-5 w-5 ml-2" />
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{locationInfo.population}</div>
            <div className="text-gray-600">Population</div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{locationInfo.averagePrice}</div>
            <div className="text-gray-600">Average Price</div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <Star className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{locationInfo.popularVenues.length}+</div>
            <div className="text-gray-600">Venue Types</div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <MapPin className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{locationInfo.nearbyAttractions.length}</div>
            <div className="text-gray-600">Attractions</div>
          </div>
        </div>

        {/* Content Sections */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {/* Popular Venue Types */}
          <div className="bg-white rounded-lg p-6 shadow-md">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Popular Venue Types</h2>
            <ul className="space-y-2">
              {locationInfo.popularVenues.map((venue, index) => (
                <li key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mr-3"></div>
                  {venue}
                </li>
              ))}
            </ul>
          </div>

          {/* Best For */}
          <div className="bg-white rounded-lg p-6 shadow-md">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Perfect For</h2>
            <ul className="space-y-2">
              {locationInfo.bestFor.map((event, index) => (
                <li key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                  {event}
                </li>
              ))}
            </ul>
          </div>

          {/* Transport Links */}
          <div className="bg-white rounded-lg p-6 shadow-md">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Getting There</h2>
            <ul className="space-y-2">
              {locationInfo.transportLinks.map((transport, index) => (
                <li key={index} className="flex items-center text-gray-700">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  {transport}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Nearby Attractions */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Nearby Attractions</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            {locationInfo.nearbyAttractions.map((attraction, index) => (
              <div key={index} className="bg-white rounded-lg p-4 shadow-sm">
                <div className="font-medium text-gray-900">{attraction}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Important Information */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Noise Restrictions */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-3">Noise Restrictions</h3>
            <p className="text-gray-700">{locationInfo.noiseRestrictions}</p>
          </div>

          {/* Council Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-3">Council Information</h3>
            <div className="space-y-2">
              <div className="flex items-center text-gray-700">
                <Phone className="h-4 w-4 mr-2" />
                {locationInfo.councilInfo.phone}
              </div>
              <div className="flex items-center text-gray-700">
                <Mail className="h-4 w-4 mr-2" />
                <a 
                  href={locationInfo.councilInfo.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-700"
                >
                  {locationInfo.councilInfo.name}
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gray-900 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">
            Ready to Find Your Perfect Venue in {locationInfo.name}?
          </h2>
          <p className="text-gray-300 mb-6">
            Browse our curated selection of party venues and book with confidence
          </p>
          <Link
            to={`/find-venues?location=${locationInfo.name}`}
            className="inline-flex items-center px-8 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Start Your Search
            <ArrowRight className="h-5 w-5 ml-2" />
          </Link>
        </div>
      </div>
    </>
  );
}
