/**
 * API route for AI training feedback
 */

// In-memory storage for training feedback (would be replaced with database in production)
const trainingFeedback = [];
const trainingNotes = [];

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { type, data } = req.body;

    if (!type || !data) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Handle different types of training data
    switch (type) {
      case 'feedback':
        // Validate feedback data
        if (!data.messageId || !data.rating) {
          return res.status(400).json({ error: 'Invalid feedback data' });
        }
        
        // Store feedback
        trainingFeedback.push({
          id: `feedback_${Date.now()}`,
          messageId: data.messageId,
          rating: data.rating,
          notes: data.notes || '',
          timestamp: new Date().toISOString(),
          userId: data.userId || 'anonymous'
        });
        
        // In a real implementation, this would update the AI model or training data
        console.log(`Feedback received for message ${data.messageId}: ${data.rating}`);
        
        break;
        
      case 'notes':
        // Validate notes data
        if (!data.content || typeof data.content !== 'string') {
          return res.status(400).json({ error: 'Invalid notes data' });
        }
        
        // Store training notes
        trainingNotes.push({
          id: `note_${Date.now()}`,
          content: data.content,
          timestamp: new Date().toISOString(),
          userId: data.userId || 'anonymous'
        });
        
        // In a real implementation, this would update the AI model or training data
        console.log(`Training note received: ${data.content.substring(0, 50)}...`);
        
        break;
        
      default:
        return res.status(400).json({ error: 'Invalid training data type' });
    }

    return res.status(200).json({ 
      success: true,
      message: `${type} saved successfully`,
      id: type === 'feedback' ? trainingFeedback[trainingFeedback.length - 1].id : trainingNotes[trainingNotes.length - 1].id
    });
  } catch (error) {
    console.error('Error in AI training API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
