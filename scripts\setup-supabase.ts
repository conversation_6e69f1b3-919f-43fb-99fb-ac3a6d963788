// This script sets up the Supabase database schema
import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://fxqoowlruissctsgbljk.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk0MzI1NCwiZXhwIjoyMDU5NTE5MjU0fQ.9qjOAPoSojpa-91l9jlAI79Rkw3Och4gCjuTWM6KBLI';

// Create Supabase client with service role key for full access
const supabase = createClient(supabaseUrl, supabaseKey);

interface UserProfile {
  id?: string;
  clerk_id: string;
  email: string;
  role: string;
  created_at?: string;
  updated_at?: string;
}

async function setupDatabase() {
  console.log('Setting up Supabase database...');

  try {
    // Create user_profiles table using SQL query
    console.log('Creating user_profiles table...');
    const { error: createTableError } = await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS user_profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          clerk_id TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          role TEXT NOT NULL DEFAULT 'guest',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (createTableError) {
      console.error('Error creating user_profiles table:', createTableError);
      // Try alternative method
      console.log('Trying alternative method to create table...');
      await createUserProfilesTable();
    }

    // Insert pre-registered hosts
    console.log('Inserting pre-registered hosts...');
    const { error: insertError } = await supabase
      .from('user_profiles')
      .upsert([
        {
          clerk_id: 'pre-registered-host-1',
          email: '<EMAIL>',
          role: 'host',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ] as UserProfile[], { onConflict: 'email' });

    if (insertError) {
      console.error('Error inserting pre-registered hosts:', insertError);
    } else {
      console.log('Pre-registered hosts inserted successfully');
    }

    // Verify the table was created
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*');

    if (error) {
      console.error('Error verifying table creation:', error);
    } else {
      console.log('User profiles in database:', data);
      console.log('Database setup completed successfully!');
    }

  } catch (error) {
    console.error('Error setting up database:', error);
  }
}

// Alternative method to create the user_profiles table
async function createUserProfilesTable() {
  try {
    // Create the table using the from API
    const { error } = await supabase
      .from('user_profiles')
      .insert([
        {
          clerk_id: 'test-clerk-id',
          email: '<EMAIL>',
          role: 'guest',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ] as UserProfile[]);

    if (error) {
      if (error.code === '42P01') { // Table doesn't exist
        console.error('Table does not exist and could not be created automatically');
      } else {
        console.error('Error creating table:', error);
      }
    } else {
      console.log('Table created successfully via insert method');
    }
  } catch (error) {
    console.error('Error in createUserProfilesTable:', error);
  }
}

// Run the setup
setupDatabase();
