import React from 'react';
import { Helmet } from 'react-helmet-async';
import PreciseNSWAddressLookup from '../components/nsw-curfew/PreciseNSWAddressLookup';
import { MapPin, Clock, Volume2, AlertTriangle, CheckCircle, Info, Home, Calendar } from 'lucide-react';

/**
 * NSW Party Planning Tool Page
 *
 * This page provides a comprehensive tool for checking noise regulations,
 * curfew times, and party planning recommendations for any address in NSW.
 */
export default function NSWPartyPlanningToolPage() {
  return (
    <>
      <Helmet>
        <title>NSW Party Planning & Noise Guide | Curfew Times & Regulations | HouseGoing</title>
        <meta
          name="description"
          content="Complete NSW party planning guide with noise curfew times, council regulations, and legal requirements. Check any NSW address for party-friendly zones and restrictions."
        />
        <meta
          name="keywords"
          content="NSW party planning, noise curfew NSW, party regulations NSW, noise restrictions Sydney, council noise rules, party permits NSW, noise complaints NSW"
        />
        <meta property="og:title" content="NSW Party Planning & Noise Guide | HouseGoing" />
        <meta
          property="og:description"
          content="Find noise curfew times and party regulations for any NSW address. Complete guide to planning legal, neighbor-friendly events."
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://housegoing.com.au/nsw-party-planning" />
        <link rel="canonical" href="https://housegoing.com.au/nsw-party-planning" />
      </Helmet>

      <div className="container mx-auto px-4 py-8 pt-16">
        <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-2 text-center pt-20">NSW Party Planning & Noise Guide</h1>
        <p className="text-gray-600 mb-8 text-center">
          Find out noise curfew times, restrictions, and recommendations for any address in NSW
          to help plan your event and stay compliant with local regulations.
        </p>

        <div className="mb-10">
          <PreciseNSWAddressLookup />
        </div>

        {/* Comprehensive Guide Content */}
        <div className="space-y-12">
          {/* Understanding NSW Noise Laws */}
          <section className="bg-white rounded-lg shadow-sm p-8">
            <div className="flex items-center mb-6">
              <Volume2 className="h-6 w-6 text-purple-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">Understanding NSW Noise Laws</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">General Noise Restrictions</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-purple-500 pl-4">
                    <h4 className="font-medium text-gray-900">Weekdays (Monday-Thursday)</h4>
                    <p className="text-gray-600">Quiet hours: 10:00 PM - 7:00 AM</p>
                  </div>
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-medium text-gray-900">Friday & Saturday</h4>
                    <p className="text-gray-600">Quiet hours: 11:00 PM - 8:00 AM</p>
                  </div>
                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-medium text-gray-900">Sunday & Public Holidays</h4>
                    <p className="text-gray-600">Quiet hours: 10:00 PM - 8:00 AM</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Factors Affecting Noise Limits</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span><strong>Property Type:</strong> Apartments have stricter rules than houses</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span><strong>Zoning:</strong> Residential zones have lower noise thresholds</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span><strong>Council Area:</strong> Each LGA may have specific regulations</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span><strong>Proximity to Schools/Hospitals:</strong> Extra restrictions may apply</span>
                  </li>
                </ul>
              </div>
            </div>
          </section>

          {/* Party Planning Best Practices */}
          <section className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-8">
            <div className="flex items-center mb-6">
              <Calendar className="h-6 w-6 text-purple-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">Party Planning Best Practices</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <Clock className="h-5 w-5 text-blue-600 mr-2" />
                  <h3 className="font-semibold">Timing Your Event</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Start earlier to end before curfew</li>
                  <li>• Consider afternoon parties for families</li>
                  <li>• Weekend events have later curfews</li>
                  <li>• Check for public holidays</li>
                </ul>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <Home className="h-5 w-5 text-green-600 mr-2" />
                  <h3 className="font-semibold">Venue Selection</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Choose detached houses over apartments</li>
                  <li>• Look for properties with outdoor space</li>
                  <li>• Avoid venues near schools/hospitals</li>
                  <li>• Check zoning restrictions</li>
                </ul>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mr-2" />
                  <h3 className="font-semibold">Noise Management</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Keep music indoors after 10 PM</li>
                  <li>• Use sound limiters if available</li>
                  <li>• Inform neighbors in advance</li>
                  <li>• Have a noise management plan</li>
                </ul>
              </div>
            </div>
          </section>
        </div>

        {/* Quick Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                <Clock className="w-5 h-5 text-white" />
              </div>
              <h3 className="font-semibold text-blue-900">Curfew Times</h3>
            </div>
            <p className="text-blue-700 text-sm">
              Get exact noise curfew times for any NSW address
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                <Home className="w-5 h-5 text-white" />
              </div>
              <h3 className="font-semibold text-green-900">Property Rules</h3>
            </div>
            <p className="text-green-700 text-sm">
              Understand rules based on your property type & zoning
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <h3 className="font-semibold text-purple-900">Party Score</h3>
            </div>
            <p className="text-purple-700 text-sm">
              Get a party-friendliness score for your location
            </p>
          </div>
        </div>

        {/* Key Points */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-10">
          <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
            <h2 className="text-xl font-bold mb-2">Key Things to Know</h2>
            <p className="text-purple-100">
              Quick facts about NSW noise regulations
            </p>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <MapPin className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Location Matters</h4>
                    <p className="text-gray-600 text-sm">Different areas have different rules - apartments vs houses, residential vs commercial zones</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <Calendar className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Timing is Key</h4>
                    <p className="text-gray-600 text-sm">Weekends usually have later curfews than weekdays</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Penalties Apply</h4>
                    <p className="text-gray-600 text-sm">Fines range from $200-$3,000 for noise violations</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 mt-1">
                    <Volume2 className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Be Considerate</h4>
                    <p className="text-gray-600 text-sm">Inform neighbors in advance and monitor noise levels</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick FAQ */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
            <h2 className="text-xl font-bold mb-2">Quick Answers</h2>
            <p className="text-purple-100">Common questions about NSW noise rules</p>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Info className="w-5 h-5 text-blue-500 mr-2" />
                    <h3 className="font-medium text-gray-900">What's a noise curfew?</h3>
                  </div>
                  <p className="text-gray-600 text-sm">Time when stricter noise rules apply to avoid disturbing neighbors</p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Home className="w-5 h-5 text-green-500 mr-2" />
                    <h3 className="font-medium text-gray-900">Apartments vs Houses?</h3>
                  </div>
                  <p className="text-gray-600 text-sm">Apartments have stricter rules due to shared walls and closer neighbors</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Calendar className="w-5 h-5 text-purple-500 mr-2" />
                    <h3 className="font-medium text-gray-900">Special event exemptions?</h3>
                  </div>
                  <p className="text-gray-600 text-sm">Apply 28+ days in advance - limited to few events per year</p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                    <h3 className="font-medium text-gray-900">How accurate is this?</h3>
                  </div>
                  <p className="text-gray-600 text-sm">Good guidance - always check with your local council for specifics</p>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center">
                <AlertTriangle className="w-5 h-5 text-blue-600 mr-2" />
                <p className="text-blue-800 text-sm font-medium">
                  For noise complaints: Contact your council during business hours or Police Assistance Line (131 444) after hours
                </p>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </>
  );
}
