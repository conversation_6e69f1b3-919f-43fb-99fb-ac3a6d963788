import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { SignIn } from "@clerk/clerk-react";
import { Card } from "@/components/ui/card";
import { useAuth } from "@clerk/clerk-react";

const ListVenueAuth = () => {
  const navigate = useNavigate();
  const { isSignedIn } = useAuth();

  useEffect(() => {
    if (isSignedIn) {
      navigate('/list-venue/photos');
    }
  }, [isSignedIn, navigate]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-4">Sign in to continue</h1>
          <p className="text-gray-600">
            Create an account or sign in to list your venue
          </p>
        </div>

        <Card className="p-6">
          <SignIn
            appearance={{
              elements: {
                formButtonPrimary: 'bg-purple-600 hover:bg-purple-700 text-sm normal-case',
              },
            }}
            redirectUrl="/list-venue/photos"
          />
        </Card>
      </div>
    </div>
  );
};

export default ListVenueAuth;