import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import HeroSection from '../components/home/<USER>';
import FeaturedVenues from '../components/FeaturedVenues';
import BrowsePartySpacesSection from '../components/home/<USER>';
import HostCTASection from '../components/home/<USER>';
import DollarBookingOffer from '../components/offers/DollarBookingOffer';
import SEO from '../components/seo/SEO';
import { OrganizationSchema, WebsiteSchema } from '../components/seo/JsonLd';

export default function HomePage() {
  const location = useLocation();
  const [showAuthSuccess, setShowAuthSuccess] = useState(false);

  // Check if we're coming from a successful authentication
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const authStatus = params.get('auth');

    if (authStatus === 'success') {
      setShowAuthSuccess(true);

      // Hide the success message after 5 seconds
      const timer = setTimeout(() => {
        setShowAuthSuccess(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [location]);

  return (
    <>
      <SEO
        title="Find Your Perfect Party Venue in NSW | HouseGoing"
        description="Discover and book party-ready venues across NSW. Find the perfect space for your next celebration with verified properties and trusted reviews."
        url="https://housegoing.com.au/"
      />
      <OrganizationSchema />
      <WebsiteSchema />

      {showAuthSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 mx-auto max-w-7xl mt-4" role="alert">
          <strong className="font-bold">Success! </strong>
          <span className="block sm:inline">You have successfully signed in.</span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setShowAuthSuccess(false)}
          >
            <span className="sr-only">Close</span>
            <svg className="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>Close</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </button>
        </div>
      )}
      <HeroSection />

      {/* $1 Party Booking Offer - Prominent placement after hero */}
      <section className="py-8 px-4 sm:px-6 bg-white">
        <div className="container-width">
          <DollarBookingOffer showProgress={false} className="max-w-4xl mx-auto" />
        </div>
      </section>

      {/* Featured Venues - Right after search to show immediate value */}
      <section className="pt-6 pb-8 px-4 sm:px-6 bg-gray-50">
        <div className="container-width">
          <div className="text-center mb-6">
            <h2 className="mb-3">Popular Party Venues</h2>
            <p className="text-large text-gray-600 max-w-3xl mx-auto">
              See what's available right now - these venues are loved by party hosts across NSW
            </p>
          </div>
          <FeaturedVenues />
        </div>
      </section>
      {/* <BrowsePartySpacesSection /> */}
      <HostCTASection />
    </>
  );
}
