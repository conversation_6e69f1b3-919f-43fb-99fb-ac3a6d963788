/**
 * ClerkSupabaseDebugger Component
 * 
 * A debugging tool to help identify issues with Clerk-Supabase integration.
 * This component displays the current state of authentication and provides
 * tools to diagnose and fix common issues.
 */

import { useEffect, useState } from 'react';
import { useAuth, useSession, useUser } from '@clerk/clerk-react';
import { getDiagnosticInfo, fixStuckLoadingState } from './ClerkSupabaseDebug';

export default function ClerkSupabaseDebugger() {
  const { isLoaded, userId, isSignedIn } = useAuth();
  const { user } = useUser();
  const { session } = useSession();
  const [diagnosticInfo, setDiagnosticInfo] = useState<any>(null);
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const [fixAttempted, setFixAttempted] = useState(false);

  useEffect(() => {
    // Run initial diagnostics
    runDiagnostics();
  }, [isLoaded, userId, user, session]);

  const runDiagnostics = async () => {
    setIsRunningDiagnostics(true);
    try {
      const info = await getDiagnosticInfo();
      setDiagnosticInfo(info);
      console.log('Clerk-Supabase Diagnostic Info:', info);
    } catch (error) {
      console.error('Error running diagnostics:', error);
    } finally {
      setIsRunningDiagnostics(false);
    }
  };

  const attemptFix = async () => {
    try {
      const success = fixStuckLoadingState();
      setFixAttempted(true);
      
      // Re-run diagnostics after fix attempt
      await runDiagnostics();
      
      // If we've fixed the issue, reload the page
      if (success) {
        alert('Fix attempt successful. Page will reload.');
        window.location.reload();
      }
    } catch (error) {
      console.error('Error attempting fix:', error);
    }
  };

  // If not mounted in DOM yet
  if (typeof window === 'undefined') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
      <div className="bg-indigo-600 text-white px-4 py-2 flex justify-between items-center">
        <h3 className="font-medium">Clerk-Supabase Debugger</h3>
        <button onClick={runDiagnostics} disabled={isRunningDiagnostics} className="text-xs bg-white bg-opacity-20 hover:bg-opacity-30 rounded px-2 py-1">
          {isRunningDiagnostics ? 'Running...' : 'Refresh'}
        </button>
      </div>
      
      <div className="p-4 max-h-96 overflow-y-auto text-sm">
        <div className="mb-4">
          <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Auth Status</h4>
          <ul className="space-y-1 text-gray-600 dark:text-gray-400">
            <li><span className="font-medium">Clerk Loaded:</span> {isLoaded ? '✅' : '❌'}</li>
            <li><span className="font-medium">Signed In:</span> {isSignedIn ? '✅' : '❌'}</li>
            <li><span className="font-medium">User ID:</span> {userId || 'None'}</li>
            <li><span className="font-medium">Session Available:</span> {session ? '✅' : '❌'}</li>
          </ul>
        </div>
        
        {diagnosticInfo && (
          <>
            <div className="mb-4">
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Supabase Status</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li><span className="font-medium">Client Exists:</span> {diagnosticInfo.supabase.clientExists ? '✅' : '❌'}</li>
                <li><span className="font-medium">Client Count:</span> {diagnosticInfo.supabase.clientCount}</li>
              </ul>
            </div>
            
            <div className="mb-4">
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Clerk Token</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li><span className="font-medium">Token Available:</span> {diagnosticInfo.clerk.tokenAvailable ? '✅' : '❌'}</li>
                {diagnosticInfo.clerk.tokenError && (
                  <li className="text-red-500">{diagnosticInfo.clerk.tokenError}</li>
                )}
              </ul>
            </div>
            
            <div className="mb-4">
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">LocalStorage</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                {Object.entries(diagnosticInfo.localStorage).map(([key, value]) => (
                  <li key={key}><span className="font-medium">{key}:</span> {value?.toString() || 'null'}</li>
                ))}
              </ul>
            </div>
          </>
        )}
      </div>
      
      <div className="bg-gray-100 dark:bg-gray-700 px-4 py-3 flex justify-between">
        <button
          onClick={attemptFix}
          disabled={fixAttempted}
          className="text-xs bg-indigo-600 hover:bg-indigo-700 text-white rounded px-3 py-1"
        >
          {fixAttempted ? 'Fix Attempted' : 'Attempt Fix'}
        </button>
        
        <button
          onClick={() => console.log('Debug info logged')}
          className="text-xs bg-gray-600 hover:bg-gray-700 text-white rounded px-3 py-1"
        >
          Log Debug
        </button>
      </div>
    </div>
  );
}
