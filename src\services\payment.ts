/**
 * Payment service for HouseGoing
 * Handles integration with Pin Payments API
 */
import axios from 'axios';

// Pin Payments API configuration
const PIN_PAYMENTS_CONFIG = {
  // Test API keys - will be replaced with production keys in production environment
  secretKey: 'Y_TVhfdAUlcrvQ6G_6jyMw',
  publishableKey: 'pk_8EymY6f5N1cMUazT0Gtv-A',
  apiUrl: 'https://test-api.pin.net.au/1',
};

// Payment service interface
export interface PaymentService {
  createToken(cardDetails: CardDetails): Promise<TokenResponse>;
  createCharge(chargeDetails: ChargeDetails): Promise<ChargeResponse>;
  createCustomer(customerDetails: CustomerDetails): Promise<CustomerResponse>;
  getCharge(chargeToken: string): Promise<ChargeResponse>;
  refundCharge(chargeToken: string, amount?: number): Promise<RefundResponse>;
}

// Card details interface
export interface CardDetails {
  number: string;
  expiry_month: string;
  expiry_year: string;
  cvc: string;
  name: string;
  address_line1?: string;
  address_line2?: string;
  address_city?: string;
  address_postcode?: string;
  address_state?: string;
  address_country?: string;
}

// Charge details interface
export interface ChargeDetails {
  email: string;
  description: string;
  amount: number; // Amount in cents
  currency: string;
  ip_address?: string;
  card_token?: string;
  customer_token?: string;
  capture?: boolean;
  metadata?: Record<string, string>;
}

// Customer details interface
export interface CustomerDetails {
  email: string;
  card?: CardDetails;
  card_token?: string;
}

// Token response interface
export interface TokenResponse {
  token: string;
  card: {
    token: string;
    scheme: string;
    display_number: string;
    expiry_month: number;
    expiry_year: number;
    name: string;
    address_line1: string;
    address_line2: string;
    address_city: string;
    address_postcode: string;
    address_state: string;
    address_country: string;
    customer_token: string;
    primary: boolean;
  };
}

// Charge response interface
export interface ChargeResponse {
  token: string;
  success: boolean;
  amount: number;
  currency: string;
  description: string;
  email: string;
  ip_address: string;
  created_at: string;
  status_message: string;
  error_message?: string;
  card: {
    token: string;
    scheme: string;
    display_number: string;
    expiry_month: number;
    expiry_year: number;
    name: string;
    address_line1: string;
    address_line2: string;
    address_city: string;
    address_postcode: string;
    address_state: string;
    address_country: string;
    customer_token: string;
    primary: boolean;
  };
  transfer?: any[];
  amount_refunded: number;
  total_fees: number;
  merchant_entitlement: number;
  refund_pending: boolean;
  authorisation_expired: boolean;
  captured: boolean;
  settlement_currency: string;
  metadata: Record<string, string>;
}

// Customer response interface
export interface CustomerResponse {
  token: string;
  email: string;
  created_at: string;
  card: {
    token: string;
    scheme: string;
    display_number: string;
    expiry_month: number;
    expiry_year: number;
    name: string;
    address_line1: string;
    address_line2: string;
    address_city: string;
    address_postcode: string;
    address_state: string;
    address_country: string;
    customer_token: string;
    primary: boolean;
  };
}

// Refund response interface
export interface RefundResponse {
  token: string;
  success: boolean;
  amount: number;
  currency: string;
  charge: string;
  created_at: string;
  error_message?: string;
  status_message: string;
}

/**
 * Pin Payments service implementation
 */
class PinPaymentsService implements PaymentService {
  private apiUrl: string;
  private secretKey: string;
  private publishableKey: string;

  constructor() {
    this.apiUrl = PIN_PAYMENTS_CONFIG.apiUrl;
    this.secretKey = PIN_PAYMENTS_CONFIG.secretKey;
    this.publishableKey = PIN_PAYMENTS_CONFIG.publishableKey;
  }

  /**
   * Create a card token
   * @param cardDetails Card details
   * @returns Token response
   */
  async createToken(cardDetails: CardDetails): Promise<TokenResponse> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/cards`,
        { card: cardDetails },
        {
          auth: {
            username: this.secretKey,
            password: '',
          },
        }
      );
      return response.data.response;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Create a charge
   * @param chargeDetails Charge details
   * @returns Charge response
   */
  async createCharge(chargeDetails: ChargeDetails): Promise<ChargeResponse> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/charges`,
        chargeDetails,
        {
          auth: {
            username: this.secretKey,
            password: '',
          },
        }
      );
      return response.data.response;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Create a customer
   * @param customerDetails Customer details
   * @returns Customer response
   */
  async createCustomer(customerDetails: CustomerDetails): Promise<CustomerResponse> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/customers`,
        customerDetails,
        {
          auth: {
            username: this.secretKey,
            password: '',
          },
        }
      );
      return response.data.response;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Get a charge
   * @param chargeToken Charge token
   * @returns Charge response
   */
  async getCharge(chargeToken: string): Promise<ChargeResponse> {
    try {
      const response = await axios.get(
        `${this.apiUrl}/charges/${chargeToken}`,
        {
          auth: {
            username: this.secretKey,
            password: '',
          },
        }
      );
      return response.data.response;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Refund a charge
   * @param chargeToken Charge token
   * @param amount Amount to refund (in cents)
   * @returns Refund response
   */
  async refundCharge(chargeToken: string, amount?: number): Promise<RefundResponse> {
    try {
      const data = amount ? { amount } : {};
      const response = await axios.post(
        `${this.apiUrl}/charges/${chargeToken}/refunds`,
        data,
        {
          auth: {
            username: this.secretKey,
            password: '',
          },
        }
      );
      return response.data.response;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Handle API errors
   * @param error Error object
   */
  private handleError(error: any): void {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Pin Payments API error:', error.response.data);
      console.error('Status:', error.response.status);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Pin Payments API no response:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Pin Payments API error:', error.message);
    }
  }
}

// Export a singleton instance of the payment service
export const paymentService = new PinPaymentsService();

// Export test card numbers for development
export const TEST_CARDS = {
  VISA_SUCCESS: '****************', // Always succeeds
  VISA_INSUFFICIENT_FUNDS: '****************', // Fails with insufficient_funds
  VISA_INVALID_CVC: '****************', // Fails with invalid_cvc
  VISA_EXPIRED_CARD: '****************', // Fails with expired_card
  VISA_PROCESSING_ERROR: '****************', // Fails with processing_error
  VISA_SUSPECTED_FRAUD: '****************', // Fails with suspected_fraud
  MASTERCARD_SUCCESS: '****************', // Always succeeds
};

export default paymentService;
