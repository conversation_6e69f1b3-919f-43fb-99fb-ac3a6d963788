import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  CardElement,
  Elements,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { STRIPE_CONFIG } from '../../config/stripe';
import { stripePaymentService, PriceCalculation, STRIPE_TEST_CARDS } from '../../services/stripe-payment';
import Button from '../ui/Button';
import { CreditCard, Check, AlertCircle } from 'lucide-react';

// Load Stripe outside of component to avoid recreating it on every render
const stripePromise = loadStripe(STRIPE_CONFIG.publishableKey);

interface StripePaymentFormProps {
  amount: number;
  description: string;
  customerEmail?: string;
  metadata?: Record<string, string>;
  priceCalculation?: PriceCalculation;
  onSuccess: (paymentId: string) => void;
  onError: (error: string) => void;
  showTestCards?: boolean;
}

// Card element styles
const cardElementOptions = {
  style: {
    base: {
      color: '#32325d',
      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
      fontSmoothing: 'antialiased',
      fontSize: '16px',
      '::placeholder': {
        color: '#aab7c4',
      },
    },
    invalid: {
      color: '#fa755a',
      iconColor: '#fa755a',
    },
  },
  hidePostalCode: true,
};

// Payment form component
const PaymentForm = ({
  amount,
  description,
  customerEmail,
  metadata,
  priceCalculation,
  onSuccess,
  onError,
  showTestCards = true, // Enable test cards by default in development
}: StripePaymentFormProps) => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [clientSecret, setClientSecret] = useState('');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Create payment intent when component mounts
  useEffect(() => {
    const createPaymentIntent = async () => {
      try {
        // Validate amount
        if (!amount || amount <= 0) {
          throw new Error('Invalid payment amount');
        }

        // Clear previous errors
        setError(null);

        // Set loading state
        setLoading(true);

        // Log for debugging
        console.log('Creating payment intent for amount:', amount);

        // Add booking ID to metadata if available
        const enhancedMetadata = {
          ...metadata,
          date: new Date().toISOString(), // Add timestamp for tracking
          amount_display: amount.toFixed(2), // Add formatted amount for verification
          description: description.substring(0, 100), // Add truncated description
        };

        const paymentIntent = await stripePaymentService.createPaymentIntent({
          amount: Math.round(amount * 100), // Convert to cents
          currency: STRIPE_CONFIG.currency,
          description,
          customer_email: customerEmail,
          metadata: enhancedMetadata,
        });

        if (!paymentIntent || !paymentIntent.client_secret) {
          throw new Error('Invalid payment intent response');
        }

        console.log('Payment intent created successfully');
        setClientSecret(paymentIntent.client_secret);
      } catch (err: any) {
        console.error('Payment intent creation failed:', err);

        // Format error message for better user experience
        let errorMessage = 'Failed to create payment intent';
        let errorDetails = '';

        // Handle specific error codes
        if (err.code === 'card_declined') {
          errorMessage = 'Your card was declined. Please try another payment method.';
        } else if (err.code === 'expired_card') {
          errorMessage = 'Your card has expired. Please try another card.';
        } else if (err.code === 'processing_error') {
          errorMessage = 'An error occurred while processing your card. Please try again.';
        } else if (err.code === 'rate_limit') {
          errorMessage = 'Too many requests. Please try again in a few minutes.';
        } else if (err.code === 'invalid_request_error') {
          errorMessage = 'Invalid payment request. Please check your payment details.';
        } else if (err.code === 'authentication_required') {
          errorMessage = 'Your card requires authentication. Please try again and follow the authentication steps.';
        } else if (err.message) {
          errorMessage = err.message;

          // Extract more details if available
          if (err.response?.data?.error?.message) {
            errorDetails = err.response.data.error.message;
          }
        }

        const fullErrorMessage = errorDetails ? `${errorMessage} (${errorDetails})` : errorMessage;

        setError(fullErrorMessage);
        onError(fullErrorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (amount > 0) {
      createPaymentIntent();
    } else {
      setError('Invalid payment amount');
      onError('Invalid payment amount');
    }
  }, [amount, description, customerEmail, metadata, onError]);

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      setError('Payment system is still loading. Please try again in a moment.');
      return;
    }

    if (!clientSecret) {
      setError('Payment system is not ready. Please refresh the page and try again.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const cardElement = elements.getElement(CardElement);

      if (!cardElement) {
        throw new Error('Card element not found. Please refresh the page and try again.');
      }

      // Log payment attempt for debugging
      console.log('Attempting to confirm card payment...');

      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            email: customerEmail,
          },
        },
      });

      if (error) {
        console.error('Payment confirmation error:', error);

        // Format error message for better user experience
        let errorMessage = 'Payment failed';

        if (error.type === 'card_error') {
          switch (error.code) {
            case 'card_declined':
              errorMessage = 'Your card was declined. Please try another payment method.';
              break;
            case 'expired_card':
              errorMessage = 'Your card has expired. Please try another card.';
              break;
            case 'incorrect_cvc':
              errorMessage = 'The CVC number is incorrect. Please check and try again.';
              break;
            case 'processing_error':
              errorMessage = 'An error occurred while processing your card. Please try again.';
              break;
            case 'incorrect_number':
              errorMessage = 'The card number is incorrect. Please check and try again.';
              break;
            case 'incomplete_number':
              errorMessage = 'The card number is incomplete. Please check and try again.';
              break;
            case 'incomplete_cvc':
              errorMessage = 'The CVC is incomplete. Please check and try again.';
              break;
            case 'incomplete_expiry':
              errorMessage = 'The expiration date is incomplete. Please check and try again.';
              break;
            default:
              errorMessage = error.message || 'Payment failed';
          }
        } else if (error.type === 'validation_error') {
          errorMessage = 'Validation error: ' + (error.message || 'Please check your card details');
        } else {
          errorMessage = error.message || 'Payment failed';
        }

        throw new Error(errorMessage);
      }

      if (!paymentIntent) {
        throw new Error('No payment intent returned. Please try again.');
      }

      console.log('Payment intent status:', paymentIntent.status);

      if (paymentIntent.status === 'succeeded') {
        console.log('Payment succeeded:', paymentIntent.id);
        setSuccess(true);
        onSuccess(paymentIntent.id);
      } else if (paymentIntent.status === 'requires_action') {
        // Handle 3D Secure authentication
        console.log('Payment requires additional authentication');
        const { error: actionError } = await stripe.confirmCardPayment(clientSecret);

        if (actionError) {
          throw new Error(actionError.message || 'Authentication failed');
        } else {
          setSuccess(true);
          onSuccess(paymentIntent.id);
        }
      } else {
        throw new Error(`Payment status: ${paymentIntent.status}. Please try again.`);
      }
    } catch (err: any) {
      console.error('Payment submission error:', err);
      setError(err.message || 'Payment failed. Please try again.');
      onError(err.message || 'Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment summary */}
      <div className="bg-gray-50 p-4 rounded-lg mb-4">
        <h3 className="text-lg font-semibold mb-2">Payment Summary</h3>
        <p className="text-gray-600 mb-4">{description}</p>

        {priceCalculation && (
          <div className="space-y-2 mb-4">
            {Object.entries(priceCalculation.breakdown).map(([label, value]) => (
              <div key={label} className="flex justify-between text-sm">
                <span>{label}</span>
                <span>{formatCurrency(value)}</span>
              </div>
            ))}
            <div className="border-t border-gray-200 pt-2 mt-2 flex justify-between font-semibold">
              <span>Total</span>
              <span>{formatCurrency(amount)}</span>
            </div>
          </div>
        )}
      </div>

      {/* Card element */}
      <div className="border border-gray-300 p-4 rounded-md">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Card Details
        </label>
        <CardElement options={cardElementOptions} />
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div className="flex flex-col">
            <span className="font-medium">Payment Error</span>
            <span className="text-sm">{error}</span>
            <span className="text-xs mt-1 text-red-600">
              Please try again or contact customer support if the problem persists.
            </span>
          </div>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 p-3 rounded-md flex items-start">
          <Check className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div className="flex flex-col">
            <span className="font-medium">Payment Successful!</span>
            <span className="text-sm">Your booking has been confirmed.</span>
            <span className="text-xs mt-1 text-green-600">
              A confirmation email will be sent to your email address shortly.
            </span>
          </div>
        </div>
      )}

      {/* Submit button */}
      <Button
        type="submit"
        disabled={!stripe || loading || success}
        className="w-full"
        loading={loading}
      >
        <CreditCard className="h-5 w-5 mr-2" />
        {loading ? 'Processing...' : success ? 'Payment Successful' : `Pay ${formatCurrency(amount)}`}
      </Button>

      {/* Secure payment message */}
      <p className="text-xs text-center text-gray-500 mt-4">
        Secure payment powered by Stripe. Your card details are encrypted and never stored on our servers.
      </p>

      {/* Test cards section */}
      {showTestCards && process.env.NODE_ENV !== 'production' && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-semibold text-blue-800 mb-2">Test Cards (For Development Only)</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="font-medium">Success:</span>
              <code className="bg-white px-2 py-1 rounded">{STRIPE_TEST_CARDS.VISA_SUCCESS}</code>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Authentication Required:</span>
              <code className="bg-white px-2 py-1 rounded">{STRIPE_TEST_CARDS.VISA_AUTHENTICATION_REQUIRED}</code>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Declined:</span>
              <code className="bg-white px-2 py-1 rounded">{STRIPE_TEST_CARDS.VISA_DECLINED}</code>
            </div>
            <p className="text-blue-600 mt-1">
              Use any future expiry date, any 3-digit CVC, and any postal code.
            </p>
          </div>
        </div>
      )}
    </form>
  );
};

// Wrapper component with Stripe Elements provider
export default function StripePaymentForm(props: StripePaymentFormProps) {
  // Show test cards by default in development environment
  const showTestCards = props.showTestCards ?? (process.env.NODE_ENV !== 'production');

  return (
    <Elements stripe={stripePromise}>
      <PaymentForm {...props} showTestCards={showTestCards} />
    </Elements>
  );
}
