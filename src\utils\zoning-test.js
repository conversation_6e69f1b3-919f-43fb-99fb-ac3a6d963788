/**
 * Test utility for zoning detection
 * This file contains functions to test zoning detection for specific addresses
 */

import { getAddressSuggestions, geocodeAddress } from '../components/nsw-curfew/AddressSuggestionService';

/**
 * Test zoning detection for a specific address
 * @param {string} address - The address to test
 * @returns {Promise<Object>} - The zoning information
 */
export async function testZoningDetection(address) {
  console.log(`Testing zoning detection for: ${address}`);
  
  try {
    // Step 1: Geocode the address to get coordinates
    const geocodeResult = await geocodeAddress(address);
    console.log('Geocode result:', geocodeResult);
    
    if (!geocodeResult || !geocodeResult.lat || !geocodeResult.lng) {
      console.error('Failed to geocode address');
      return { error: 'Failed to geocode address' };
    }
    
    const { lat, lng } = geocodeResult;
    
    // Step 2: Query NSW Planning Portal API for zoning information
    let zoneCode = '';
    let zoneName = '';
    
    try {
      // NSW Planning Portal API endpoint for zoning
      const zoningUrl = `https://maps.six.nsw.gov.au/arcgis/rest/services/public/PlanningInformation/MapServer/10/query`;
      
      // Parameters for the API request
      const zoningParams = new URLSearchParams({
        geometry: `${lng},${lat}`, // longitude first, then latitude
        geometryType: 'esriGeometryPoint',
        inSR: '4326', // WGS84 coordinate system
        outFields: 'ZONE_CODE,ZONE_NAME',
        f: 'json'
      });
      
      const zoningResponse = await fetch(`${zoningUrl}?${zoningParams.toString()}`);
      
      if (zoningResponse.ok) {
        const zoningData = await zoningResponse.json();
        console.log('Zoning API response:', zoningData);
        
        if (zoningData.features && zoningData.features.length > 0) {
          zoneCode = zoningData.features[0].attributes.ZONE_CODE;
          zoneName = zoningData.features[0].attributes.ZONE_NAME;
          console.log('NSW Planning Portal zoning:', zoneCode, zoneName);
        } else {
          console.log('No zoning information found from primary API');
          
          // Try alternative approach with a different endpoint
          try {
            console.log('Attempting alternative zoning lookup approach...');
            
            // Try the NSW Planning Portal EPI Primary Planning Layers endpoint
            const altZoningUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/Planning/EPI_Primary_Planning_Layers/MapServer/2/query`;
            
            const altZoningParams = new URLSearchParams({
              geometry: `${lng},${lat}`,
              geometryType: 'esriGeometryPoint',
              inSR: '4326',
              outFields: 'ZONE_CODE,ZONE_NAME',
              f: 'json'
            });
            
            const altZoningResponse = await fetch(`${altZoningUrl}?${altZoningParams.toString()}`);
            
            if (altZoningResponse.ok) {
              const altZoningData = await altZoningResponse.json();
              console.log('Alternative zoning API response:', altZoningData);
              
              if (altZoningData.features && altZoningData.features.length > 0) {
                zoneCode = altZoningData.features[0].attributes.ZONE_CODE;
                zoneName = altZoningData.features[0].attributes.ZONE_NAME;
                console.log('Alternative API zoning:', zoneCode, zoneName);
              } else {
                console.log('No zoning information found from alternative API');
                
                // Try a third API endpoint as a last resort
                try {
                  console.log('Attempting third zoning lookup approach...');
                  
                  // Try the NSW Planning Portal Planning Information endpoint
                  const thirdZoningUrl = `https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/Planning/Planning_Information/MapServer/10/query`;
                  
                  const thirdZoningParams = new URLSearchParams({
                    geometry: `${lng},${lat}`,
                    geometryType: 'esriGeometryPoint',
                    inSR: '4326',
                    outFields: '*',
                    f: 'json'
                  });
                  
                  const thirdZoningResponse = await fetch(`${thirdZoningUrl}?${thirdZoningParams.toString()}`);
                  
                  if (thirdZoningResponse.ok) {
                    const thirdZoningData = await thirdZoningResponse.json();
                    console.log('Third zoning API response:', thirdZoningData);
                    
                    if (thirdZoningData.features && thirdZoningData.features.length > 0) {
                      // The field names might be different in this API
                      const attributes = thirdZoningData.features[0].attributes;
                      zoneCode = attributes.ZONE_CODE || attributes.ZoneCode || attributes.zone_code || '';
                      zoneName = attributes.ZONE_NAME || attributes.ZoneName || attributes.zone_name || '';
                      console.log('Third API zoning:', zoneCode, zoneName);
                    } else {
                      console.log('No zoning information found from third API');
                    }
                  } else {
                    console.error('Third zoning API response not OK:', thirdZoningResponse.statusText);
                  }
                } catch (thirdError) {
                  console.error('Third zoning lookup failed:', thirdError);
                }
              }
            } else {
              console.error('Alternative zoning API response not OK:', altZoningResponse.statusText);
            }
          } catch (altError) {
            console.error('Alternative zoning lookup failed:', altError);
          }
        }
      } else {
        console.error('NSW Planning Portal API response not OK:', zoningResponse.statusText);
      }
    } catch (zoningError) {
      console.error('Error fetching zoning from NSW Planning Portal:', zoningError);
    }
    
    // Step 3: If we couldn't get zoning from API, use pattern recognition
    if (!zoneCode) {
      console.log('Using pattern recognition for zoning detection');
      
      const lowerAddress = address.toLowerCase();
      
      // Special case for Silverwater Road in Auburn (IN1 - General Industrial)
      if ((lowerAddress.includes('silverwater road') || lowerAddress.includes('silverwater rd')) && 
          lowerAddress.includes('auburn')) {
        zoneCode = 'IN1';
        zoneName = 'General Industrial';
        console.log('Pattern recognition: IN1 - General Industrial for Silverwater Road, Auburn');
      }
      
      // Special case for Phillip Street in Parramatta (B4 - Mixed Use)
      else if ((lowerAddress.includes('phillip street') || lowerAddress.includes('phillip st')) && 
               lowerAddress.includes('parramatta')) {
        zoneCode = 'B4';
        zoneName = 'Mixed Use';
        console.log('Pattern recognition: B4 - Mixed Use for Phillip Street, Parramatta');
      }
      
      // Special case for Harrington Street in The Rocks (B8 - Metropolitan Centre)
      else if ((lowerAddress.includes('harrington street') || lowerAddress.includes('harrington st')) && 
               (lowerAddress.includes('the rocks') || lowerAddress.includes('sydney'))) {
        zoneCode = 'B8';
        zoneName = 'Metropolitan Centre';
        console.log('Pattern recognition: B8 - Metropolitan Centre for Harrington Street, The Rocks');
      }
      
      // Special case for Castlereagh Street in Sydney (B8 - Metropolitan Centre)
      else if ((lowerAddress.includes('castlereagh street') || lowerAddress.includes('castlereagh st')) && 
               lowerAddress.includes('sydney')) {
        zoneCode = 'B8';
        zoneName = 'Metropolitan Centre';
        console.log('Pattern recognition: B8 - Metropolitan Centre for Castlereagh Street, Sydney');
      }
      
      // Check for Sydney CBD
      else if (lowerAddress.includes('sydney') && !lowerAddress.includes('west sydney') && !lowerAddress.includes('north sydney')) {
        zoneCode = 'B8';
        zoneName = 'Metropolitan Centre';
        console.log('Pattern recognition: B8 - Metropolitan Centre for Sydney CBD');
      }
      
      // Check for Auburn (mostly industrial)
      else if (lowerAddress.includes('auburn')) {
        zoneCode = 'IN1';
        zoneName = 'General Industrial';
        console.log('Pattern recognition: IN1 - General Industrial for Auburn');
      }
      
      // Default to R2 if no pattern matched
      else {
        zoneCode = 'R2';
        zoneName = 'Low Density Residential';
        console.log('Pattern recognition: Default R2 - Low Density Residential');
      }
    }
    
    // Step 4: Query NSW Spatial Services for LGA information
    let lgaName = null;
    
    try {
      console.log('Querying NSW Spatial Services API Layer 8 for LGA...');
      const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
      
      const lgaParams = new URLSearchParams({
        geometry: `${lng},${lat}`, // longitude first, then latitude
        geometryType: 'esriGeometryPoint',
        inSR: '4326', // WGS84 coordinate system
        outFields: 'lganame', // We know the field name is 'lganame'
        f: 'json'
      });
      
      const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
      
      if (lgaResponse.ok) {
        const lgaData = await lgaResponse.json();
        
        if (lgaData.features && lgaData.features.length > 0) {
          lgaName = lgaData.features[0].attributes.lganame;
          console.log('LGA from NSW Spatial Services (Layer 8):', lgaName);
          
          // Format the LGA name to title case for consistency
          if (lgaName) {
            // Convert to title case (e.g., "CITY OF SYDNEY" to "City of Sydney")
            lgaName = lgaName.toLowerCase().split(' ').map((word) =>
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');
            
            console.log('Formatted LGA name:', lgaName);
          }
        } else {
          console.log('No LGA information found from Layer 8 API');
        }
      } else {
        console.error('NSW Spatial Services API response not OK:', lgaResponse.statusText);
      }
    } catch (lgaError) {
      console.error('Error fetching LGA information from Layer 8 API:', lgaError);
    }
    
    // Step 5: If we couldn't get LGA from API, use pattern recognition
    if (!lgaName) {
      console.log('Using pattern recognition for LGA detection');
      
      const lowerAddress = address.toLowerCase();
      
      if (lowerAddress.includes('sydney') || lowerAddress.includes('the rocks')) {
        lgaName = 'City of Sydney';
      } else if (lowerAddress.includes('parramatta')) {
        lgaName = 'City of Parramatta';
      } else if (lowerAddress.includes('auburn')) {
        lgaName = 'Cumberland Council';
      } else {
        lgaName = 'Unknown Council';
      }
      
      console.log('Pattern recognition LGA:', lgaName);
    }
    
    // Step 6: Determine property type
    let propertyType = 'House'; // Default
    
    // Check for apartment/unit
    if (/^\d+\//.test(address) || address.toLowerCase().includes('suite')) {
      propertyType = 'Apartment/Unit';
    }
    // Check for commercial property
    else if (zoneCode.startsWith('B')) {
      propertyType = 'Commercial Property';
    }
    // Check for industrial property
    else if (zoneCode.startsWith('IN')) {
      propertyType = 'Industrial Property';
    }
    // Check for rural property
    else if (zoneCode.startsWith('RU')) {
      propertyType = 'Rural Property';
    }
    
    console.log('Determined property type:', propertyType);
    
    return {
      address,
      coordinates: { lat, lng },
      zoneCode,
      zoneName,
      lgaName,
      propertyType
    };
  } catch (error) {
    console.error('Error in testZoningDetection:', error);
    return { error: error.message };
  }
}

/**
 * Test zoning detection for multiple addresses
 * @param {string[]} addresses - The addresses to test
 * @returns {Promise<Object[]>} - The zoning information for each address
 */
export async function testMultipleAddresses(addresses) {
  const results = [];
  
  for (const address of addresses) {
    const result = await testZoningDetection(address);
    results.push(result);
  }
  
  return results;
}
