import React, { useState, useEffect, useRef } from 'react';
import { MapPin, X } from 'lucide-react';
import { getAddressSuggestions, geocodeAddress } from '../../components/nsw-curfew/AddressSuggestionService';

interface AddressLookupProps {
  value: string;
  onChange: (address: string, coordinates: [number, number]) => void;
  placeholder?: string;
  required?: boolean;
  className?: string;
}

const AddressLookup: React.FC<AddressLookupProps> = ({
  value,
  onChange,
  placeholder = 'Enter address',
  required = false,
  className = '',
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Handle outside clicks to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch suggestions when input changes
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (inputValue.length < 3) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setLoading(true);
      setError('');

      try {
        const results = await getAddressSuggestions(inputValue);
        setSuggestions(results);
        setShowSuggestions(results.length > 0);
      } catch (err) {
        console.error('Error fetching address suggestions:', err);
        setError('Failed to fetch address suggestions');
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    };

    const timer = setTimeout(() => {
      fetchSuggestions();
    }, 300);

    return () => clearTimeout(timer);
  }, [inputValue]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (e.target.value === '') {
      onChange('', [-33.8688, 151.2093]); // Default to Sydney
    }
  };

  // Handle suggestion selection
  const handleSelectSuggestion = (suggestion: any) => {
    setInputValue(suggestion.address);
    onChange(suggestion.address, [suggestion.coordinates.lat, suggestion.coordinates.lng]);
    setShowSuggestions(false);
  };

  // Handle manual address entry (when user presses Enter)
  const handleKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue && !showSuggestions) {
      e.preventDefault();
      setLoading(true);
      setError('');

      try {
        const result = await geocodeAddress(inputValue);
        onChange(result.displayName, [result.lat, result.lng]);
        setInputValue(result.displayName);
      } catch (err) {
        console.error('Error geocoding address:', err);
        setError('Failed to find address. Please try a different address format.');
      } finally {
        setLoading(false);
      }
    }
  };

  // Clear input
  const handleClear = () => {
    setInputValue('');
    onChange('', [-33.8688, 151.2093]); // Default to Sydney
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className="relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <MapPin className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => inputValue.length >= 3 && suggestions.length > 0 && setShowSuggestions(true)}
          placeholder={placeholder}
          required={required}
          className={`pl-10 pr-10 w-full p-2 border rounded ${className}`}
          aria-label="Address"
        />
        {inputValue && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute inset-y-0 right-0 flex items-center pr-3"
            aria-label="Clear address"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        )}
      </div>

      {loading && (
        <div className="absolute right-3 top-3">
          <div className="animate-spin h-4 w-4 border-2 border-gray-500 border-t-transparent rounded-full"></div>
        </div>
      )}

      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className="p-2 hover:bg-purple-100 cursor-pointer flex items-start"
              onClick={() => handleSelectSuggestion(suggestion)}
            >
              <MapPin className="h-5 w-5 text-gray-400 mr-2 mt-0.5 flex-shrink-0" />
              <span>{suggestion.address}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AddressLookup;
