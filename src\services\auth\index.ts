/**
 * Unified Authentication Service
 *
 * This service coordinates authentication between Clerk and <PERSON>pa<PERSON>,
 * ensuring that user data is properly synchronized between the two systems.
 */

import { User as ClerkUser } from '@clerk/clerk-react';
import { supabase } from '../../lib/supabase-client';
import { isPreregisteredHost } from '../../data/preregisteredHosts';

// User roles
export type UserRole = 'guest' | 'host' | 'admin';

// User profile interface
export interface UserProfile {
  id: string;
  clerk_id: string;
  email: string;
  role: UserRole;
  created_at?: string;
  updated_at?: string;
}

/**
 * Synchronize a Clerk user with Supabase
 * This should be called after authentication or when user data changes
 */
export async function syncUserWithSupabase(
  clerkUser: ClerkUser,
  role?: UserRole
): Promise<UserProfile | null> {
  try {
    if (!clerkUser) {
      console.error('Cannot sync null user with Supabase');
      return null;
    }

    const clerkId = clerkUser.id;
    const email = clerkUser.primaryEmailAddress?.emailAddress || '';

    if (!email) {
      console.error('User has no email address, cannot sync with Supabase');
      return null;
    }

    console.log(`Syncing user ${email} (${clerkId}) with Supabase`);

    // Check if user already exists in Supabase
    const { data: existingUser, error: fetchError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('clerk_id', clerkId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error fetching user from Supabase:', fetchError);
    }

    // Determine the role
    let userRole: UserRole;

    if (role) {
      // Use the provided role
      userRole = role;
    } else if (existingUser?.role) {
      // Keep existing role if present
      userRole = existingUser.role as UserRole;
    } else if (clerkUser.publicMetadata?.role) {
      // Use role from Clerk metadata
      userRole = clerkUser.publicMetadata.role as UserRole;
    } else if (isPreregisteredHost(email)) {
      // Check if user is a pre-registered host
      userRole = 'host';
    } else {
      // Default to guest
      userRole = 'guest';
    }

    // Prepare user data
    const userData = {
      clerk_id: clerkId,
      email,
      role: userRole,
      updated_at: new Date().toISOString()
    };

    // Upsert user in Supabase
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert(userData, {
        onConflict: 'clerk_id',
        returning: 'representation'
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting user in Supabase:', error);
      return null;
    }

    // If the role in Clerk doesn't match the role in Supabase, update Clerk
    if (clerkUser.publicMetadata?.role !== userRole) {
      try {
        await clerkUser.update({
          publicMetadata: {
            ...clerkUser.publicMetadata,
            role: userRole
          }
        });
        console.log(`Updated Clerk user role to ${userRole}`);
      } catch (updateError) {
        console.error('Error updating Clerk user metadata:', updateError);
      }
    }

    return data as UserProfile;
  } catch (error) {
    console.error('Error in syncUserWithSupabase:', error);
    return null;
  }
}

/**
 * Check if a user is a host
 */
export async function isUserHost(clerkId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('clerk_id', clerkId)
      .single();

    if (error) {
      console.error('Error checking if user is host:', error);
      return false;
    }

    return data?.role === 'host';
  } catch (error) {
    console.error('Error checking if user is host:', error);
    return false;
  }
}

/**
 * Set a user's role in both Supabase and Clerk
 */
export async function setUserRole(
  clerkUser: ClerkUser,
  role: UserRole
): Promise<boolean> {
  try {
    if (!clerkUser) {
      console.error('Cannot set role for null user');
      return false;
    }

    const clerkId = clerkUser.id;
    const email = clerkUser.primaryEmailAddress?.emailAddress || '';

    if (!email) {
      console.error('User has no email address, cannot set role');
      return false;
    }

    console.log(`Setting role for user ${email} (${clerkId}) to ${role}`);

    // Update role in Supabase
    const { error: supabaseError } = await supabase
      .from('user_profiles')
      .upsert({
        clerk_id: clerkId,
        email,
        role,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'clerk_id'
      });

    if (supabaseError) {
      console.error('Error setting user role in Supabase:', supabaseError);
      return false;
    }

    // Update role in Clerk
    try {
      await clerkUser.update({
        publicMetadata: {
          ...clerkUser.publicMetadata,
          role
        }
      });
      console.log(`Updated Clerk user role to ${role}`);
    } catch (updateError) {
      console.error('Error updating Clerk user metadata:', updateError);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in setUserRole:', error);
    return false;
  }
}

/**
 * Get a user's profile from Supabase
 */
export async function getUserProfile(clerkId: string): Promise<UserProfile | null> {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('clerk_id', clerkId)
      .single();

    if (error) {
      console.error('Error getting user profile:', error);
      return null;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}
