import React from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { useAuth as useCustomAuth } from '../../providers/AuthProvider';

export default function AuthDebugger() {
  const { user: clerkUser, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const { getToken } = useAuth();
  const { isAuthenticated, user: customUser, isAdmin } = useCustomAuth();

  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm text-xs z-50">
      <h3 className="font-bold mb-2">Auth Debug Info</h3>
      
      <div className="space-y-1">
        <div><strong>Clerk Loaded:</strong> {clerkLoaded ? '✅' : '❌'}</div>
        <div><strong>Clerk Signed In:</strong> {isSignedIn ? '✅' : '❌'}</div>
        <div><strong>Custom Auth:</strong> {isAuthenticated ? '✅' : '❌'}</div>
        <div><strong>Is Admin:</strong> {isAdmin ? '✅' : '❌'}</div>
        
        {clerkUser && (
          <div className="mt-2 pt-2 border-t">
            <div><strong>Email:</strong> {clerkUser.primaryEmailAddress?.emailAddress}</div>
            <div><strong>Name:</strong> {clerkUser.firstName} {clerkUser.lastName}</div>
            <div><strong>ID:</strong> {clerkUser.id.substring(0, 8)}...</div>
          </div>
        )}
        
        <button
          onClick={async () => {
            try {
              const token = await getToken();
              console.log('Clerk Token:', token ? 'Available' : 'None');
            } catch (error) {
              console.error('Error getting token:', error);
            }
          }}
          className="mt-2 px-2 py-1 bg-blue-500 text-white rounded text-xs"
        >
          Test Token
        </button>
      </div>
    </div>
  );
}
