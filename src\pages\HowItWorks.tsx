import React from 'react';
import { PartyPopper, Search, Calendar, MessageSquare } from 'lucide-react';

export default function HowItWorks() {
  const steps = [
    {
      icon: Search,
      title: "Find the Perfect Venue",
      description: "Browse through our curated list of party-ready venues. Use filters to find the perfect match for your event."
    },
    {
      icon: Calendar,
      title: "Book Your Date",
      description: "Check availability and instantly book your preferred date. Secure payment process with clear pricing."
    },
    {
      icon: MessageSquare,
      title: "Connect with Host",
      description: "Direct messaging with the host to discuss party details and any special requirements."
    },
    {
      icon: PartyPopper,
      title: "Enjoy Your Party",
      description: "Show up and enjoy your event in a venue that's perfect for celebrations!"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 pt-32 pb-16 px-4 sm:px-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-12 text-center">How HouseGoing Works</h1>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {steps.map((step, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                <step.icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">{step.title}</h3>
              <p className="text-gray-600">{step.description}</p>
            </div>
          ))}
        </div>

        {/* Additional content to ensure proper spacing */}
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Ready to Get Started?</h2>
          <p className="text-gray-600 mb-8">Join thousands of party hosts and guests who trust HouseGoing for their celebrations.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/find-venues"
              className="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors font-medium"
            >
              Find Venues
            </a>
            <a
              href="/list-space"
              className="border border-purple-600 text-purple-600 px-8 py-3 rounded-lg hover:bg-purple-50 transition-colors font-medium"
            >
              List Your Space
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}