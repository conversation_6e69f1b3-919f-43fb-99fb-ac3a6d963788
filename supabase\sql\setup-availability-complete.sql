-- Complete Availability System Setup for HouseGoing
-- Run this entire script in Supabase SQL Editor

-- 1. Create venue_availability_settings table
CREATE TABLE IF NOT EXISTS venue_availability_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  
  -- Days of week availability (0 = Sunday, 1 = Monday, etc.)
  available_days INTEGER[] DEFAULT '{1,2,3,4,5,6,0}',
  
  -- Default time slots
  default_start_time TIME DEFAULT '09:00:00',
  default_end_time TIME DEFAULT '23:00:00',
  
  -- Booking constraints
  min_booking_hours INTEGER DEFAULT 4,
  max_booking_hours INTEGER DEFAULT 12,
  lead_time_hours INTEGER DEFAULT 24,
  max_advance_days INTEGER DEFAULT 365,
  instant_booking_enabled BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venue_id)
);

-- 2. Create venue_operating_hours table
CREATE TABLE IF NOT EXISTS venue_operating_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venue_id, day_of_week),
  CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- 3. Create venue_day_availability table
CREATE TABLE IF NOT EXISTS venue_day_availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  is_available BOOLEAN DEFAULT true,
  start_time TIME,
  end_time TIME,
  special_price DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venue_id, date)
);

-- 4. Create venue_blocked_slots table
CREATE TABLE IF NOT EXISTS venue_blocked_slots (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
  end_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
  reason TEXT,
  block_type VARCHAR(20) DEFAULT 'manual',
  is_recurring BOOLEAN DEFAULT false,
  recurrence_pattern VARCHAR(20),
  recurrence_end_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT valid_datetime_range CHECK (end_datetime > start_datetime)
);

-- 5. Create venue_seasonal_availability table
CREATE TABLE IF NOT EXISTS venue_seasonal_availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  season_name VARCHAR(100) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_available BOOLEAN DEFAULT true,
  price_multiplier DECIMAL(3,2) DEFAULT 1.00,
  is_yearly_recurring BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT valid_date_range CHECK (end_date >= start_date),
  CONSTRAINT valid_price_multiplier CHECK (price_multiplier > 0)
);

-- 6. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_venue_availability_settings_venue_id ON venue_availability_settings(venue_id);
CREATE INDEX IF NOT EXISTS idx_venue_day_availability_venue_date ON venue_day_availability(venue_id, date);
CREATE INDEX IF NOT EXISTS idx_venue_blocked_slots_venue_datetime ON venue_blocked_slots(venue_id, start_datetime, end_datetime);
CREATE INDEX IF NOT EXISTS idx_venue_operating_hours_venue_day ON venue_operating_hours(venue_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_venue_seasonal_availability_venue_dates ON venue_seasonal_availability(venue_id, start_date, end_date);

-- 7. Enable Row Level Security
ALTER TABLE venue_availability_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE venue_day_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE venue_blocked_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE venue_operating_hours ENABLE ROW LEVEL SECURITY;
ALTER TABLE venue_seasonal_availability ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS Policies (Public read, owner write)
-- venue_availability_settings policies
DROP POLICY IF EXISTS "Public can view availability settings" ON venue_availability_settings;
CREATE POLICY "Public can view availability settings"
  ON venue_availability_settings FOR SELECT
  USING (true);

DROP POLICY IF EXISTS "Venue owners can manage their availability settings" ON venue_availability_settings;
CREATE POLICY "Venue owners can manage their availability settings"
  ON venue_availability_settings FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

-- venue_operating_hours policies
DROP POLICY IF EXISTS "Public can view operating hours" ON venue_operating_hours;
CREATE POLICY "Public can view operating hours"
  ON venue_operating_hours FOR SELECT
  USING (true);

DROP POLICY IF EXISTS "Venue owners can manage their operating hours" ON venue_operating_hours;
CREATE POLICY "Venue owners can manage their operating hours"
  ON venue_operating_hours FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

-- venue_day_availability policies
DROP POLICY IF EXISTS "Public can view day availability" ON venue_day_availability;
CREATE POLICY "Public can view day availability"
  ON venue_day_availability FOR SELECT
  USING (true);

DROP POLICY IF EXISTS "Venue owners can manage their day availability" ON venue_day_availability;
CREATE POLICY "Venue owners can manage their day availability"
  ON venue_day_availability FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

-- venue_blocked_slots policies
DROP POLICY IF EXISTS "Public can view blocked slots" ON venue_blocked_slots;
CREATE POLICY "Public can view blocked slots"
  ON venue_blocked_slots FOR SELECT
  USING (true);

DROP POLICY IF EXISTS "Venue owners can manage their blocked slots" ON venue_blocked_slots;
CREATE POLICY "Venue owners can manage their blocked slots"
  ON venue_blocked_slots FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

-- venue_seasonal_availability policies
DROP POLICY IF EXISTS "Public can view seasonal availability" ON venue_seasonal_availability;
CREATE POLICY "Public can view seasonal availability"
  ON venue_seasonal_availability FOR SELECT
  USING (true);

DROP POLICY IF EXISTS "Venue owners can manage their seasonal availability" ON venue_seasonal_availability;
CREATE POLICY "Venue owners can manage their seasonal availability"
  ON venue_seasonal_availability FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

-- 9. Insert default settings for existing venues
INSERT INTO venue_availability_settings (venue_id, available_days, default_start_time, default_end_time, min_booking_hours, max_booking_hours, lead_time_hours, max_advance_days, instant_booking_enabled)
SELECT 
  id,
  '{1,2,3,4,5,6,0}',
  '09:00:00',
  '23:00:00',
  4,
  12,
  24,
  365,
  false
FROM venues 
WHERE id NOT IN (SELECT venue_id FROM venue_availability_settings);

-- 10. Insert default operating hours for existing venues
INSERT INTO venue_operating_hours (venue_id, day_of_week, start_time, end_time, is_available)
SELECT 
  v.id,
  d.day_of_week,
  '09:00:00',
  '23:00:00',
  true
FROM venues v
CROSS JOIN (
  SELECT generate_series(0, 6) as day_of_week
) d
WHERE NOT EXISTS (
  SELECT 1 FROM venue_operating_hours voh 
  WHERE voh.venue_id = v.id AND voh.day_of_week = d.day_of_week
);

-- Success message
SELECT 'Availability system setup complete! 🎉' as status;
