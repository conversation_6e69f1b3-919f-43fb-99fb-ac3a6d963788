import { getSupabaseClient, isSupabaseAvailable } from '../services/api';
import { mockVenues as apiMockVenues } from '../services/api';
import { mockVenues as comprehensiveMockVenues, transformedMockVenues, searchVenues, MockVenue } from '../data/mockVenues';
import { Venue } from '../types/venue';
import { checkVenueAvailability, searchAvailableVenues } from './availability';

/**
 * Interface for venue search parameters
 */
export interface VenueSearchParams {
  location?: string;
  lat?: number | null;
  lng?: number | null;
  startDate?: string;
  endDate?: string;
  guests?: number;
  budget?: number;
  amenities?: string[];
  eventTypes?: string[];
  minPrice?: number;
  maxPrice?: number;
  minCapacity?: number;
  maxCapacity?: number;
  priceRange?: string;
  guestRange?: string;
  instantBook?: boolean;
}

/**
 * Filter venues by availability for specific date/time
 */
async function filterVenuesByAvailability(venues: Venue[], startDate?: string, endDate?: string): Promise<Venue[]> {
  if (!startDate || !endDate) {
    return venues; // Return all venues if no date filter
  }

  try {
    // Check availability for each venue
    const availabilityChecks = await Promise.allSettled(
      venues.map(async (venue) => {
        try {
          const startDateTime = `${startDate}T18:00:00Z`; // Default to 6 PM
          const endDateTime = `${endDate}T22:00:00Z`; // Default to 10 PM

          const availability = await checkVenueAvailability(venue.id, startDateTime, endDateTime);

          return {
            venue,
            available: availability.available,
            conflicts: availability.conflicts
          };
        } catch (error) {
          console.error(`Error checking availability for venue ${venue.id}:`, error);
          // If availability check fails, assume venue is available (fallback)
          return {
            venue,
            available: true,
            conflicts: []
          };
        }
      })
    );

    // Filter to only available venues and add availability info
    const availableVenues = availabilityChecks
      .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
      .map(result => result.value)
      .filter(result => result.available)
      .map(result => ({
        ...result.venue,
        availabilityChecked: true,
        isAvailable: true
      }));

    console.log(`🗓️ Availability Filter: ${availableVenues.length}/${venues.length} venues available for ${startDate} to ${endDate}`);

    return availableVenues;
  } catch (error) {
    console.error('Error filtering venues by availability:', error);
    // If availability filtering fails, return all venues
    return venues;
  }
}

/**
 * Convert MockVenue to Venue format
 */
function convertMockVenueToVenue(mockVenue: MockVenue): Venue {
  return {
    id: mockVenue.id,
    title: mockVenue.title,
    description: mockVenue.description,
    location: `${mockVenue.location.suburb}, ${mockVenue.location.state}`,
    address: mockVenue.location.address,
    price: mockVenue.pricing.hourlyRate,
    capacity: mockVenue.capacity.recommended,
    rating: mockVenue.host.rating,
    reviews: mockVenue.host.reviewCount,
    images: mockVenue.images,
    amenities: mockVenue.amenities,
    eventTypes: [mockVenue.venueType],
    instantBook: mockVenue.instantBook,
    coordinates: {
      latitude: mockVenue.location.latitude,
      longitude: mockVenue.location.longitude
    },
    host: {
      id: mockVenue.id + '-host',
      name: mockVenue.host.name,
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200',
      rating: mockVenue.host.rating,
      verified: mockVenue.verified
    },
    partyScore: {
      score: Math.round(mockVenue.partyScore / 10), // Convert to 1-10 scale
      factors: mockVenue.features
    }
  };
}

/**
 * Convert Supabase venue data to Venue format
 */
function convertSupabaseVenueToVenue(supabaseVenue: any): Venue {
  return {
    id: supabaseVenue.id,
    title: supabaseVenue.title || supabaseVenue.name || 'Untitled Venue',
    description: supabaseVenue.description || '',
    location: supabaseVenue.location || 'Unknown Location',
    suburb: supabaseVenue.suburb || 'Unknown',
    state: 'NSW',
    postcode: supabaseVenue.postcode || '2000',
    latitude: supabaseVenue.coordinates?.latitude || supabaseVenue.latitude || -33.8688,
    longitude: supabaseVenue.coordinates?.longitude || supabaseVenue.longitude || 151.2093,
    capacity: supabaseVenue.capacity || 50,
    price: supabaseVenue.price || 100,
    images: supabaseVenue.images || [],
    amenities: supabaseVenue.amenities || [],
    features: supabaseVenue.features || [],
    rules: supabaseVenue.rules || [],
    availability: supabaseVenue.availability || 'Available',
    instantBook: supabaseVenue.instant_book || false,
    verified: supabaseVenue.verified || false,
    rating: supabaseVenue.rating || 4.5,
    reviewCount: supabaseVenue.reviews || 0,
    eventTypes: supabaseVenue.event_types || ['Event'],
    hostId: supabaseVenue.host_id || 'unknown',
    hostName: supabaseVenue.host?.name || 'Host',
    hostImage: supabaseVenue.host?.image || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200',
    hostRating: supabaseVenue.host?.rating || 4.5,
    hostReviews: supabaseVenue.host?.reviews || 0,
    hostResponseTime: supabaseVenue.host?.responseTime || '1 hour',
    isPublished: supabaseVenue.is_published || false,
    createdAt: supabaseVenue.created_at || new Date().toISOString(),
    updatedAt: supabaseVenue.updated_at || new Date().toISOString()
  };
}

/**
 * Get all venues with optional filtering
 */
export async function getVenues(params?: VenueSearchParams): Promise<Venue[]> {
  try {
    // TEMPORARY: Force use of mock data to show all 15 venues
    // Check if Supabase is available
    if (true || !isSupabaseAvailable()) {
      console.log('Supabase not available, using comprehensive mock data');

      // If no search parameters provided, return all venues in NSW
      if (!params || Object.keys(params).length === 0 ||
          (!params.location && !params.startDate && !params.endDate &&
           !params.budget && !params.guests && !params.eventTypes && !params.amenities)) {
        console.log('🏠 No search parameters provided, returning all NSW venues');
        const allVenues = transformedMockVenues.map(convertMockVenueToVenue);
        return allVenues;
      }

      // Use the smart search function from mockVenues with enhanced filters
      const searchFilters = {
        location: params?.location,
        startDate: params?.startDate,
        endDate: params?.endDate,
        maxBudget: params?.budget,
        guestCount: params?.guests,
        venueType: params?.eventTypes?.[0],
        eventTypes: params?.eventTypes,
        amenities: params?.amenities
      };

      const searchResult = await searchVenues(transformedMockVenues, searchFilters);

      // Combine exact matches and suggestions
      const allVenues = [...searchResult.exactMatches, ...searchResult.suggestions];

      // Convert MockVenue[] to Venue[] and add metadata
      let venues = allVenues.map((mockVenue, index) => {
        const venue = convertMockVenueToVenue(mockVenue);

        // Add metadata to distinguish exact matches from suggestions
        // Also pass through distance information if available
        return {
          ...venue,
          isExactMatch: index < searchResult.exactMatches.length,
          isSuggestion: index >= searchResult.exactMatches.length,
          distanceKm: (mockVenue as any).distanceKm,
          radiusCategory: (mockVenue as any).radiusCategory
        };
      });

      // Apply budget filter with hours calculation if specified
      if (params?.budget && params.budget > 0) {
        venues = venues.map(venue => {
          const hoursAvailable = Math.floor(params.budget / venue.price);
          return {
            ...venue,
            hoursAvailable
          };
        }).filter(venue => venue.hoursAvailable >= 1);
      }

      console.log(`Found ${searchResult.exactMatches.length} exact matches and ${searchResult.suggestions.length} suggestions using ${searchResult.searchType} search`);
      if (searchResult.message) {
        console.log(searchResult.message);
      }
      if (searchResult.suggestionMessage) {
        console.log(searchResult.suggestionMessage);
      }

      // Apply availability filter if dates are provided
      if (params?.startDate && params?.endDate) {
        try {
          // Try using the new database-powered availability search first
          const startDateTime = `${params.startDate}T${params.startTime || '18:00'}:00Z`;
          const endDateTime = `${params.endDate}T${params.endTime || '22:00'}:00Z`;

          const availableVenues = await searchVenuesByAvailability(startDateTime, endDateTime, {
            suburb: params.location,
            maxPrice: params.budget
          });

          if (availableVenues.length > 0) {
            console.log(`🎯 Using database availability search: ${availableVenues.length} venues found`);
            return availableVenues;
          }
        } catch (availabilityError) {
          console.warn('Database availability search failed, using fallback:', availabilityError);
        }
      }

      // Fallback to original availability filter
      const finalVenues = await filterVenuesByAvailability(venues, params?.startDate, params?.endDate);
      return finalVenues;
    }

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Try to get venues from Supabase first
    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .eq('is_published', true);

    if (error) {
      console.error('Error fetching venues from Supabase:', error);
      console.log('Falling back to comprehensive mock data');

      // If no search parameters provided, return all venues in NSW
      if (!params || Object.keys(params).length === 0 ||
          (!params.location && !params.startDate && !params.endDate &&
           !params.budget && !params.guests && !params.eventTypes && !params.amenities)) {
        console.log('🏠 No search parameters provided, returning all NSW venues');
        const allVenues = transformedMockVenues.map(convertMockVenueToVenue);
        return allVenues;
      }

      // Use the smart search function from mockVenues with enhanced filters
      const searchFilters = {
        location: params?.location,
        startDate: params?.startDate,
        endDate: params?.endDate,
        maxBudget: params?.budget,
        guestCount: params?.guests,
        venueType: params?.eventTypes?.[0],
        eventTypes: params?.eventTypes,
        amenities: params?.amenities
      };

      const searchResult = await searchVenues(transformedMockVenues, searchFilters);

      // Combine exact matches and suggestions
      const allVenues = [...searchResult.exactMatches, ...searchResult.suggestions];

      // Convert MockVenue[] to Venue[] and add metadata
      let venues = allVenues.map((mockVenue, index) => {
        const venue = convertMockVenueToVenue(mockVenue);

        // Add metadata to distinguish exact matches from suggestions
        return {
          ...venue,
          isExactMatch: index < searchResult.exactMatches.length,
          isSuggestion: index >= searchResult.exactMatches.length,
          distanceKm: (mockVenue as any).distanceKm,
          radiusCategory: (mockVenue as any).radiusCategory
        };
      });

      // Apply budget filter with hours calculation if specified
      if (params?.budget && params.budget > 0) {
        venues = venues.map(venue => {
          const hoursAvailable = Math.floor(params.budget / venue.price);
          return {
            ...venue,
            hoursAvailable
          };
        }).filter(venue => venue.hoursAvailable >= 1);
      }

      console.log(`Found ${searchResult.exactMatches.length} exact matches and ${searchResult.suggestions.length} suggestions using ${searchResult.searchType} search`);

      // Apply availability filter if dates are provided
      if (params?.startDate && params?.endDate) {
        try {
          // Try using the new database-powered availability search first
          const startDateTime = `${params.startDate}T${params.startTime || '18:00'}:00Z`;
          const endDateTime = `${params.endDate}T${params.endTime || '22:00'}:00Z`;

          const availableVenues = await searchVenuesByAvailability(startDateTime, endDateTime, {
            suburb: params.location,
            maxPrice: params.budget
          });

          if (availableVenues.length > 0) {
            console.log(`🎯 Using database availability search: ${availableVenues.length} venues found`);
            return availableVenues;
          }
        } catch (availabilityError) {
          console.warn('Database availability search failed, using fallback:', availabilityError);
        }
      }

      // Fallback to original availability filter
      const finalVenues = await filterVenuesByAvailability(venues, params?.startDate, params?.endDate);
      return finalVenues;
    }

    if (!data || data.length === 0) {
      console.log('No venues found in Supabase, using comprehensive mock data with smart search');

      // If no search parameters provided, return all venues in NSW
      if (!params || Object.keys(params).length === 0 ||
          (!params.location && !params.startDate && !params.endDate &&
           !params.budget && !params.guests && !params.eventTypes && !params.amenities)) {
        console.log('🏠 No search parameters provided, returning all NSW venues');
        console.log('🏠 CACHE DEBUG 2: Total mock venues available:', transformedMockVenues.length);
        const allVenues = transformedMockVenues.map(convertMockVenueToVenue);
        console.log('🏠 CACHE DEBUG 2: Converted venues count:', allVenues.length);
        return allVenues;
      }

      // Use the smart search function from mockVenues with enhanced filters
      const searchFilters = {
        location: params?.location,
        startDate: params?.startDate,
        endDate: params?.endDate,
        maxBudget: params?.budget,
        guestCount: params?.guests,
        venueType: params?.eventTypes?.[0],
        eventTypes: params?.eventTypes,
        amenities: params?.amenities
      };

      const searchResult = await searchVenues(transformedMockVenues, searchFilters);

      // Combine exact matches and suggestions
      const allVenues = [...searchResult.exactMatches, ...searchResult.suggestions];

      // Convert MockVenue[] to Venue[] and add metadata
      let venues = allVenues.map((mockVenue, index) => {
        const venue = convertMockVenueToVenue(mockVenue);

        // Add metadata to distinguish exact matches from suggestions
        return {
          ...venue,
          isExactMatch: index < searchResult.exactMatches.length,
          isSuggestion: index >= searchResult.exactMatches.length,
          distanceKm: (mockVenue as any).distanceKm,
          radiusCategory: (mockVenue as any).radiusCategory
        };
      });

      // Apply budget filter with hours calculation if specified
      if (params?.budget && params.budget > 0) {
        venues = venues.map(venue => {
          const hoursAvailable = Math.floor(params.budget / venue.price);
          return {
            ...venue,
            hoursAvailable
          };
        }).filter(venue => venue.hoursAvailable >= 1);
      }

      console.log(`Found ${searchResult.exactMatches.length} exact matches and ${searchResult.suggestions.length} suggestions using ${searchResult.searchType} search`);

      // Apply availability filter if dates are provided
      const finalVenues = await filterVenuesByAvailability(venues, params?.startDate, params?.endDate);
      return finalVenues;
    }

    // If we have Supabase data, check if we need to apply search logic
    console.log(`Found ${data.length} venues in Supabase`);

    // If no search parameters provided, return all Supabase venues
    if (!params || Object.keys(params).length === 0 ||
        (!params.location && !params.startDate && !params.endDate &&
         !params.budget && !params.guests && !params.eventTypes && !params.amenities)) {
      console.log('🏠 No search parameters provided, returning all Supabase venues');
      // Convert Supabase data directly to Venue format
      const allVenues = data.map(venue => convertSupabaseVenueToVenue(venue));
      return allVenues;
    }

    console.log('Applying smart search logic to Supabase data');

    // Convert Supabase data to MockVenue format for smart search
    const supabaseVenuesAsMock: MockVenue[] = data.map(venue => ({
      id: venue.id,
      title: venue.title || venue.name || 'Untitled Venue',
      location: {
        suburb: venue.location || 'Unknown',
        postcode: '2000',
        state: 'NSW',
        address: venue.address || venue.location || 'Unknown Address',
        latitude: venue.coordinates?.latitude || venue.latitude || -33.8688,
        longitude: venue.coordinates?.longitude || venue.longitude || 151.2093
      },
      pricing: {
        hourlyRate: venue.price || 100,
        minimumHours: 4,
        cleaningFee: 100,
        securityDeposit: 300,
        totalMinimum: venue.price || 100
      },
      capacity: {
        standing: venue.capacity || 50,
        seated: Math.floor((venue.capacity || 50) * 0.7),
        recommended: venue.capacity || 50
      },
      venueType: venue.venue_type || 'Event Space',
      eventTypes: venue.event_types || ['Social'],
      amenities: venue.amenities || [],
      features: [],
      description: venue.description || '',
      images: venue.images || [],
      availability: {
        daysAvailable: ['Friday', 'Saturday', 'Sunday'],
        timeSlots: ['6:00 PM - 12:00 AM'],
        blackoutDates: []
      },
      host: {
        id: venue.host_id || 'host-1',
        name: venue.host?.name || 'Host',
        responseTime: '1 hour',
        rating: venue.rating || 4.5,
        reviewCount: venue.reviews || 50,
        image: venue.host?.image || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=200&q=80'
      },
      rules: [],
      partyScore: 8.5,
      verified: true,
      instantBook: venue.instant_book || false,
      price: venue.price || 100,
      rating: venue.rating || 4.5,
      reviews: venue.reviews || 50
    }));

    // Apply smart search to Supabase venues
    const searchFilters = {
      location: params?.location,
      startDate: params?.startDate,
      endDate: params?.endDate,
      maxBudget: params?.budget,
      guestCount: params?.guests,
      venueType: params?.eventTypes?.[0],
      eventTypes: params?.eventTypes,
      amenities: params?.amenities
    };

    const searchResult = await searchVenues(supabaseVenuesAsMock, searchFilters);

    // Combine exact matches and suggestions
    const allVenues = [...searchResult.exactMatches, ...searchResult.suggestions];

    // Convert back to Venue format and add metadata
    let venues = allVenues.map((mockVenue, index) => {
      const venue = convertMockVenueToVenue(mockVenue);

      // Add metadata to distinguish exact matches from suggestions
      return {
        ...venue,
        isExactMatch: index < searchResult.exactMatches.length,
        isSuggestion: index >= searchResult.exactMatches.length,
        distanceKm: (mockVenue as any).distanceKm,
        radiusCategory: (mockVenue as any).radiusCategory
      };
    });

    // Apply budget filter with hours calculation if specified
    if (params?.budget && params.budget > 0) {
      venues = venues.map(venue => {
        const hoursAvailable = Math.floor(params.budget / venue.price);
        return {
          ...venue,
          hoursAvailable
        };
      }).filter(venue => venue.hoursAvailable >= 1);
    }

    console.log(`Found ${searchResult.exactMatches.length} exact matches and ${searchResult.suggestions.length} suggestions using ${searchResult.searchType} search`);

    // Apply availability filter if dates are provided
    const finalVenues = await filterVenuesByAvailability(venues, params?.startDate, params?.endDate);
    return finalVenues;
  } catch (error) {
    console.error('Error in getVenues:', error);
    console.log('🏠 CACHE DEBUG 3: Error fallback - Total mock venues available:', transformedMockVenues.length);
    const fallbackVenues = transformedMockVenues.map(convertMockVenueToVenue);
    console.log('🏠 CACHE DEBUG 3: Error fallback - Converted venues count:', fallbackVenues.length);
    return fallbackVenues;
  }
}

/**
 * Search venues by availability using database functions
 */
export async function searchVenuesByAvailability(
  startDatetime: string,
  endDatetime: string,
  filters?: {
    suburb?: string;
    minCapacity?: number;
    maxPrice?: number;
  }
): Promise<Venue[]> {
  try {
    // Check if Supabase is available
    if (!isSupabaseAvailable()) {
      console.log('Supabase not available, falling back to mock data with availability filter');
      const allVenues = transformedMockVenues.map(convertMockVenueToVenue);
      return await filterVenuesByAvailability(allVenues, startDatetime.split('T')[0], endDatetime.split('T')[0]);
    }

    // Use the database function to get available venues
    const availableVenues = await searchAvailableVenues(startDatetime, endDatetime, filters);

    // Convert database results to Venue format
    const venues: Venue[] = availableVenues.map(dbVenue => ({
      id: dbVenue.venue_id,
      title: dbVenue.title,
      description: dbVenue.description,
      location: dbVenue.location,
      suburb: dbVenue.suburb,
      capacity: dbVenue.capacity,
      price: dbVenue.price,
      rating: dbVenue.rating,
      images: dbVenue.images || [],
      amenities: dbVenue.amenities || [],
      eventTypes: ['Event'],
      instantBook: true,
      coordinates: {
        latitude: -33.8688,
        longitude: 151.2093
      },
      host: {
        id: 'host-' + dbVenue.venue_id,
        name: 'Host',
        image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200',
        rating: 4.5,
        verified: true
      },
      availabilityChecked: true,
      isAvailable: true
    }));

    console.log(`🎯 Database Availability Search: Found ${venues.length} available venues`);
    return venues;
  } catch (error) {
    console.error('Error in searchVenuesByAvailability:', error);
    // Fallback to regular search with availability filter
    const allVenues = transformedMockVenues.map(convertMockVenueToVenue);
    return await filterVenuesByAvailability(allVenues, startDatetime.split('T')[0], endDatetime.split('T')[0]);
  }
}

/**
 * Get a venue by ID
 */
export async function getVenueById(venueId: string): Promise<Venue | null> {
  try {
    // Check if Supabase is available
    if (!isSupabaseAvailable()) {
      console.log('Supabase not available, using comprehensive mock data');
      const mockVenue = transformedMockVenues.find(v => v.id === venueId);
      if (mockVenue) {
        return convertMockVenueToVenue(mockVenue);
      }

      // Also check smart search venues (they have numeric IDs)
      console.log('🔍 Checking smart search venues for ID:', venueId);
      try {
        // Import smart search venues dynamically
        const smartSearchModule = await import('../components/search/SmartVenueSearch');
        const smartVenues = smartSearchModule.venues || [];
        const smartVenue = smartVenues.find((v: any) => v.id.toString() === venueId);

        if (smartVenue) {
          console.log('✅ Found venue in smart search data:', smartVenue.name);
          // Convert smart venue to Venue format
          const convertedVenue: Venue = {
            id: smartVenue.id.toString(),
            title: smartVenue.name,
            description: smartVenue.description,
            location: smartVenue.location,
            suburb: smartVenue.suburb,
            state: 'NSW',
            postcode: '2000',
            latitude: -33.8688,
            longitude: 151.2093,
            capacity: smartVenue.capacity.max,
            price: smartVenue.pricePerHour,
            images: [
              'https://images.unsplash.com/photo-1566737236500-c8ac43014a67?auto=format&fit=crop&w=800&q=80',
              'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?auto=format&fit=crop&w=800&q=80'
            ],
            amenities: smartVenue.amenities,
            features: smartVenue.features,
            rules: ['Please follow venue guidelines', 'Respect noise restrictions'],
            availability: smartVenue.timeSlots.join(', '),
            instantBook: true,
            verified: true,
            rating: 4.5,
            reviewCount: 25,
            hostId: 'host-smart-' + smartVenue.id,
            hostName: 'Venue Manager',
            hostImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=200&q=80',
            hostRating: 4.5,
            hostReviews: 25,
            hostResponseTime: '2 hours',
            isPublished: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          return convertedVenue;
        }
      } catch (error) {
        console.error('Error importing smart search venues:', error);
      }

      return null;
    }

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .eq('id', venueId)
      .single();

    if (error) {
      console.error('Error fetching venue:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getVenueById:', error);
    return null;
  }
}
