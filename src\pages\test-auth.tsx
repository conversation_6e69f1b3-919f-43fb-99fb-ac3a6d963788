import React, { useState, useEffect } from 'react';
import { useAuth, useUser, useSession } from '@clerk/clerk-react';
import { supabase } from '../lib/supabase-client';
import { createClerkSupabaseClient } from '../lib/clerk-supabase-official';

/**
 * Test page to debug Clerk-Supabase authentication
 */
export default function TestAuth() {
  const { isSignedIn, getToken } = useAuth();
  const { user } = useUser();
  const { session } = useSession();
  const [testResults, setTestResults] = useState<any>(null);
  const [supabaseTest, setSupabaseTest] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [officialClient, setOfficialClient] = useState<any>(null);

  // Initialize the official Clerk-Supabase client when session changes
  useEffect(() => {
    if (session) {
      const initClient = async () => {
        const client = await createClerkSupabaseClient(session);
        setOfficialClient(client);
      };
      initClient();
    } else {
      setOfficialClient(null);
    }
  }, [session]);

  const runTests = async () => {
    setLoading(true);
    const results: any = {};

    try {
      // Test 1: Check if user is signed in
      results.userSignedIn = isSignedIn;
      results.userId = user?.id;
      results.userExternalId = user?.externalId;

      // Test 2: Get token from Clerk (native integration - no template)
      if (isSignedIn) {
        const nativeToken = await getToken();
        results.nativeTokenReceived = !!nativeToken;
        results.nativeTokenPreview = nativeToken ? nativeToken.substring(0, 30) + '...' : null;

        // Also test the old template method for comparison
        const templateToken = await getToken({ template: 'supabase' });
        results.templateTokenReceived = !!templateToken;
        results.templateTokenPreview = templateToken ? templateToken.substring(0, 30) + '...' : null;

        if (nativeToken) {
          // Test 3: Decode the native token
          const nativeDecoded = decodeJWT(nativeToken);
          results.nativeTokenDecoded = nativeDecoded;
        }

        if (templateToken) {
          // Test 4: Decode the template token for comparison
          const templateDecoded = decodeJWT(templateToken);
          results.templateTokenDecoded = templateDecoded;

          // Test 5: Test with utility function (uses template)
          const utilityTest = await testClerkSupabaseToken();
          results.utilityTest = utilityTest;

          // Test 6: Try to make a Supabase request with official native client
          try {
            if (officialClient) {
              // Try to fetch user profiles using the official native integration
              const { data, error } = await officialClient
                .from('user_profiles')
                .select('*')
                .limit(1);

              results.nativeSupabaseTest = {
                success: !error,
                error: error?.message,
                dataReceived: !!data,
                dataCount: data?.length || 0,
                method: 'Official Native Integration'
              };
            } else {
              results.nativeSupabaseTest = {
                success: false,
                error: 'No session available for native client',
                method: 'Official Native Integration'
              };
            }

            // Also test the old template method for comparison
            if (templateToken) {
              await supabase.auth.setSession({
                access_token: templateToken,
                refresh_token: ''
              });

              const { data: oldData, error: oldError } = await supabase
                .from('user_profiles')
                .select('*')
                .limit(1);

              results.legacySupabaseTest = {
                success: !oldError,
                error: oldError?.message,
                dataReceived: !!oldData,
                dataCount: oldData?.length || 0,
                method: 'Legacy Template + setSession Method'
              };
            }
          } catch (supabaseError) {
            results.supabaseTest = {
              success: false,
              error: supabaseError instanceof Error ? supabaseError.message : 'Unknown error'
            };
          }
        }
      }

      setTestResults(results);
    } catch (error) {
      setTestResults({
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testSupabaseConnection = async () => {
    try {
      // Test basic Supabase connection (without auth)
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count')
        .limit(1);

      setSupabaseTest({
        success: !error,
        error: error?.message,
        message: error ? 'Supabase connection failed' : 'Supabase connection successful'
      });
    } catch (error) {
      setSupabaseTest({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Supabase connection failed'
      });
    }
  };

  useEffect(() => {
    if (isSignedIn) {
      runTests();
    }
  }, [isSignedIn]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Clerk-Supabase Authentication Test
        </h1>

        <div className="space-y-6">
          {/* User Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">User Status</h2>
            <div className="space-y-2">
              <p><strong>Signed In:</strong> {isSignedIn ? '✅ Yes' : '❌ No'}</p>
              <p><strong>User ID:</strong> {user?.id || 'N/A'}</p>
              <p><strong>External ID:</strong> {user?.externalId || 'N/A'}</p>
              <p><strong>Email:</strong> {user?.primaryEmailAddress?.emailAddress || 'N/A'}</p>
            </div>
          </div>

          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="space-x-4">
              <button
                onClick={runTests}
                disabled={!isSignedIn || loading}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Running Tests...' : 'Run Auth Tests'}
              </button>
              <button
                onClick={testSupabaseConnection}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Test Supabase Connection
              </button>
            </div>
          </div>

          {/* Supabase Connection Test */}
          {supabaseTest && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Supabase Connection Test</h2>
              <div className={`p-4 rounded ${supabaseTest.success ? 'bg-green-100' : 'bg-red-100'}`}>
                <p className="font-medium">{supabaseTest.message}</p>
                {supabaseTest.error && (
                  <p className="text-red-600 mt-2">Error: {supabaseTest.error}</p>
                )}
              </div>
            </div>
          )}

          {/* Test Results */}
          {testResults && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Test Results</h2>
              <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </div>
          )}

          {!isSignedIn && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
              <p>Please sign in to run authentication tests.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
