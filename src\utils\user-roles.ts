import { User } from '@clerk/clerk-react';

export enum UserRole {
  USER = 'user',
  HOST = 'host',
  ADMIN = 'admin',
}

export const getUserRole = (user: User | null | undefined): UserRole => {
  if (!user) return UserRole.USER;
  
  const role = user.publicMetadata.role as UserRole | undefined;
  return role || UserRole.USER;
};

export const isHost = (user: User | null | undefined): boolean => {
  return getUserRole(user) === UserRole.HOST || getUserRole(user) === UserRole.ADMIN;
};

export const isAdmin = (user: User | null | undefined): boolean => {
  return getUserRole(user) === UserRole.ADMIN;
};
