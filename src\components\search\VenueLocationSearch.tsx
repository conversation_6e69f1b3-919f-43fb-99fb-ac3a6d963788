// File: src/components/search/VenueLocationSearch.tsx
import React, { useState } from 'react';
import { NSWSuburbsSearchBar } from './NSWSuburbsSearchBar';

const radiusOptions = [
  { value: 5, label: '+5km' },
  { value: 10, label: '10km' },
  { value: 15, label: '15km' },
  { value: 20, label: '20km' }
];

export const VenueLocationSearch = () => {
  const [selectedRadius, setSelectedRadius] = useState(10);
  
  const handleSuburbSelect = (suburb) => {
    console.log('Selected suburb:', suburb);
    // Handle suburb selection
  };
  
  return (
    <div className="space-y-4">
      <NSWSuburbsSearchBar onSelect={handleSuburbSelect} />
      <div className="flex gap-2">
        {radiusOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => setSelectedRadius(option.value)}
            className={`px-3 py-1 rounded ${
              selectedRadius === option.value 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-200'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );
};
