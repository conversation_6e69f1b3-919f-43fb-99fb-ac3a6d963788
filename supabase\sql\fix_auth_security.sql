-- Fix Supabase Auth Security Configuration
-- Run this in the Supabase SQL Editor to fix auth security warnings

-- Fix OTP expiry settings (reduce from default to secure values)
UPDATE auth.config 
SET 
  -- Set OTP expiry to 10 minutes (600 seconds) instead of default 1 hour
  otp_expiry = 600,
  -- Set password reset expiry to 1 hour (3600 seconds) instead of default 24 hours  
  password_reset_expiry = 3600,
  -- Set email confirmation expiry to 24 hours (86400 seconds)
  email_confirmation_expiry = 86400
WHERE true;

-- Enable leaked password protection
UPDATE auth.config 
SET 
  -- Enable password breach detection
  enable_password_breach_detection = true,
  -- Set minimum password strength
  password_min_length = 8
WHERE true;

-- Create secure admin user creation function
CREATE OR REPLACE FUNCTION public.create_admin_users_table()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
  -- Create admin_users table if it doesn't exist
  CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clerk_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'admin',
    permissions JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(clerk_id),
    UNIQUE(email)
  );
  
  -- Create index for faster lookups
  CREATE INDEX IF NOT EXISTS admin_users_clerk_id_idx ON admin_users(clerk_id);
  CREATE INDEX IF NOT EXISTS admin_users_email_idx ON admin_users(email);
  
  -- Enable RLS
  ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
  
  -- Create RLS policies
  DROP POLICY IF EXISTS "Admin users can view all admin records" ON admin_users;
  CREATE POLICY "Admin users can view all admin records" ON admin_users
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM admin_users au 
        WHERE au.clerk_id = auth.uid()
      )
    );
  
  DROP POLICY IF EXISTS "Only super admins can modify admin records" ON admin_users;
  CREATE POLICY "Only super admins can modify admin records" ON admin_users
    FOR ALL USING (
      EXISTS (
        SELECT 1 FROM admin_users au 
        WHERE au.clerk_id = auth.uid() 
        AND au.permissions ? 'super_admin'
      )
    );
  
  RETURN TRUE;
END;
$$;

-- Create secure user profiles table function
CREATE OR REPLACE FUNCTION public.create_user_profiles_table()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
  -- Create user_profiles table if it doesn't exist
  CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    full_name TEXT,
    phone TEXT,
    date_of_birth DATE,
    is_host BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    profile_image_url TEXT,
    bio TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id),
    UNIQUE(email)
  );
  
  -- Create indexes
  CREATE INDEX IF NOT EXISTS user_profiles_user_id_idx ON user_profiles(user_id);
  CREATE INDEX IF NOT EXISTS user_profiles_email_idx ON user_profiles(email);
  CREATE INDEX IF NOT EXISTS user_profiles_is_host_idx ON user_profiles(is_host);
  
  -- Enable RLS
  ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
  
  -- Create RLS policies
  DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
  CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);
  
  DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
  CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);
  
  DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
  CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);
  
  DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;
  CREATE POLICY "Admins can view all profiles" ON user_profiles
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM admin_users au 
        WHERE au.clerk_id = auth.uid()
      )
    );
  
  RETURN TRUE;
END;
$$;

-- Create secure function to check if user exists
CREATE OR REPLACE FUNCTION public.exec_sql_exists(table_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
  RETURN EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = table_name
  );
END;
$$;

-- Create secure venue slot blocking function
CREATE OR REPLACE FUNCTION public.block_venue_slot(
  p_venue_id UUID,
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
  is_owner BOOLEAN;
BEGIN
  -- Check if user owns the venue
  SELECT EXISTS(
    SELECT 1 FROM venues 
    WHERE id = p_venue_id AND host_id = p_user_id
  ) INTO is_owner;
  
  IF NOT is_owner THEN
    RAISE EXCEPTION 'Unauthorized: User does not own this venue';
  END IF;
  
  -- Insert blocked slot
  INSERT INTO venue_blocked_slots (venue_id, start_time, end_time, created_by)
  VALUES (p_venue_id, p_start_time, p_end_time, p_user_id);
  
  RETURN TRUE;
END;
$$;

-- Grant execute permissions for secure functions only
GRANT EXECUTE ON FUNCTION public.create_admin_users_table TO service_role;
GRANT EXECUTE ON FUNCTION public.create_user_profiles_table TO service_role;
GRANT EXECUTE ON FUNCTION public.exec_sql_exists TO authenticated;
GRANT EXECUTE ON FUNCTION public.block_venue_slot TO authenticated;

-- Revoke dangerous permissions from insecure functions
REVOKE ALL ON FUNCTION public.exec_sql FROM public, anon, authenticated, service_role;
REVOKE ALL ON FUNCTION public.pg_query FROM public, anon, authenticated, service_role;

-- Create trigger to automatically update updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SET search_path = public, extensions
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Apply updated_at triggers to tables that need them
DO $$
DECLARE
  table_name TEXT;
BEGIN
  FOR table_name IN 
    SELECT t.table_name 
    FROM information_schema.tables t
    JOIN information_schema.columns c ON t.table_name = c.table_name
    WHERE t.table_schema = 'public' 
    AND c.column_name = 'updated_at'
    AND t.table_type = 'BASE TABLE'
  LOOP
    EXECUTE format('
      DROP TRIGGER IF EXISTS update_%s_updated_at ON %s;
      CREATE TRIGGER update_%s_updated_at
        BEFORE UPDATE ON %s
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    ', table_name, table_name, table_name, table_name);
  END LOOP;
END;
$$;
