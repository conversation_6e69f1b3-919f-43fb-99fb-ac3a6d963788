import { handleWebhook } from './webhooks';
import { createPaymentIntent, createCustomer, handleStripeWebhook } from './stripe';

// Define route handlers
const routes = {
  '/api/webhooks/clerk': handleWebhook,
  '/api/stripe/payment-intent': createPaymentIntent,
  '/api/stripe/create-customer': createCustomer,
  '/api/webhooks/stripe': handleStripeWebhook,
};

// Main API router
export async function handleApiRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;

  // Find the appropriate handler for this path
  const handler = routes[path];

  if (handler) {
    return await handler(request);
  } else {
    return new Response('Not found', { status: 404 });
  }
}
