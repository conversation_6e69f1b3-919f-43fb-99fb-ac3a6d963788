/**
 * Admin Email Service
 *
 * Handles email notifications for admin approval workflow
 */

import { generateSampleEmailEnhanced } from './cleanEmailTemplates';
import { trackEvent } from './dataTracking';

const BACKEND_API_URL = process.env.REACT_APP_BACKEND_URL || 'https://housegoing.onrender.com';

// Admin notification emails - Updated to working Gmail addresses
const ADMIN_EMAILS = {
  NEW_HOST_SIGNUP: '<EMAIL>',
  NEW_CUSTOMER_SIGNUP: '<EMAIL>',
  PROPERTY_SUBMISSION: '<EMAIL>',
  ADMIN_GENERAL: '<EMAIL>' // Main admin email for general notifications
};

export interface PropertySubmissionData {
  id: string;
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  ownerName: string;
  ownerEmail: string;
  created_at: string;
}

/**
 * Send email notification when a new property is submitted
 */
export async function sendPropertySubmissionNotification(
  propertyData: PropertySubmissionData
): Promise<boolean> {
  try {
    const template = generateSampleEmailEnhanced('PROPERTY_SUBMISSION_ADMIN', {
      propertyName: propertyData.name,
      propertyAddress: propertyData.address,
      propertyType: propertyData.type,
      ownerName: propertyData.ownerName,
      ownerEmail: propertyData.ownerEmail,
      maxGuests: propertyData.maxGuests,
      pricePerHour: propertyData.price,
      submissionDate: new Date(propertyData.created_at).toLocaleDateString(),
      adminDashboardUrl: `${window.location.origin}/admin/submissions/${propertyData.id}`
    });

    const response = await fetch(`${BACKEND_API_URL}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: ADMIN_EMAILS.PROPERTY_SUBMISSION,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: '<EMAIL>',
        fromName: 'HouseGoing Platform'
      })
    });

    const result = await response.json();
    return result.success;

  } catch (error) {
    console.error('Error sending property submission notification:', error);
    return false;
  }
}

/**
 * Send approval email to property owner
 */
export async function sendPropertyApprovalEmail(
  propertyData: PropertySubmissionData,
  adminNotes?: string
): Promise<boolean> {
  try {
    const template = generateSampleEmailEnhanced('PROPERTY_APPROVAL', {
      recipientName: propertyData.ownerName,
      propertyName: propertyData.name,
      propertyAddress: propertyData.address,
      adminNotes: adminNotes || 'Your property meets all our quality standards.',
      dashboardUrl: `${window.location.origin}/host/dashboard`,
      supportEmail: '<EMAIL>'
    });

    const response = await fetch(`${BACKEND_API_URL}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: propertyData.ownerEmail,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: '<EMAIL>',
        fromName: 'HouseGoing Team'
      })
    });

    const result = await response.json();

    // Track email notification event
    if (result.success) {
      try {
        await trackEvent(
          'approval_email_sent',
          'retention',
          {
            property_id: propertyData.id,
            property_name: propertyData.name,
            recipient_email: propertyData.ownerEmail,
            email_type: 'property_approval',
            admin_notes_included: !!adminNotes
          },
          propertyData.ownerId
        );
      } catch (trackingError) {
        console.error('Error tracking approval email event:', trackingError);
      }
    }

    return result.success;

  } catch (error) {
    console.error('Error sending property approval email:', error);
    return false;
  }
}

/**
 * Send rejection email to property owner
 */
export async function sendPropertyRejectionEmail(
  propertyData: PropertySubmissionData,
  rejectionReason: string,
  adminNotes?: string
): Promise<boolean> {
  try {
    const template = generateSampleEmailEnhanced('PROPERTY_REJECTION', {
      recipientName: propertyData.ownerName,
      propertyName: propertyData.name,
      rejectionReason: rejectionReason,
      adminNotes: adminNotes || 'Please review the feedback and feel free to resubmit.',
      resubmitUrl: `${window.location.origin}/host/submit-property`,
      supportEmail: '<EMAIL>'
    });

    const response = await fetch(`${BACKEND_API_URL}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: propertyData.ownerEmail,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: '<EMAIL>',
        fromName: 'HouseGoing Team'
      })
    });

    const result = await response.json();

    // Track email notification event
    if (result.success) {
      try {
        await trackEvent(
          'rejection_email_sent',
          'churn',
          {
            property_id: propertyData.id,
            property_name: propertyData.name,
            recipient_email: propertyData.ownerEmail,
            email_type: 'property_rejection',
            rejection_reason: rejectionReason,
            admin_notes_included: !!adminNotes
          },
          propertyData.ownerId
        );
      } catch (trackingError) {
        console.error('Error tracking rejection email event:', trackingError);
      }
    }

    return result.success;

  } catch (error) {
    console.error('Error sending property rejection email:', error);
    return false;
  }
}

/**
 * Send notification when new host signs up
 */
export async function sendNewHostSignupNotification(
  hostData: {
    name: string;
    email: string;
    signupDate: string;
  }
): Promise<boolean> {
  try {
    const template = generateSampleEmailEnhanced('NEW_HOST_SIGNUP_ADMIN', {
      hostName: hostData.name,
      hostEmail: hostData.email,
      signupDate: hostData.signupDate,
      adminDashboardUrl: `${window.location.origin}/admin/users`
    });

    const response = await fetch(`${BACKEND_API_URL}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: ADMIN_EMAILS.NEW_HOST_SIGNUP,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: '<EMAIL>',
        fromName: 'HouseGoing Platform'
      })
    });

    const result = await response.json();
    return result.success;

  } catch (error) {
    console.error('Error sending new host signup notification:', error);
    return false;
  }
}

/**
 * Send notification when new customer signs up
 */
export async function sendNewCustomerSignupNotification(
  customerData: {
    name: string;
    email: string;
    signupDate: string;
  }
): Promise<boolean> {
  try {
    const template = generateSampleEmailEnhanced('NEW_CUSTOMER_SIGNUP_ADMIN', {
      customerName: customerData.name,
      customerEmail: customerData.email,
      signupDate: customerData.signupDate,
      adminDashboardUrl: `${window.location.origin}/admin/users`
    });

    const response = await fetch(`${BACKEND_API_URL}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: ADMIN_EMAILS.NEW_CUSTOMER_SIGNUP,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: '<EMAIL>',
        fromName: 'HouseGoing Platform'
      })
    });

    const result = await response.json();
    return result.success;

  } catch (error) {
    console.error('Error sending new customer signup notification:', error);
    return false;
  }
}

/**
 * Send weekly admin summary email
 */
export async function sendWeeklyAdminSummary(
  summaryData: {
    newSubmissions: number;
    pendingReviews: number;
    approvedThisWeek: number;
    rejectedThisWeek: number;
    newHosts: number;
    newCustomers: number;
    totalBookings: number;
  }
): Promise<boolean> {
  try {
    const template = generateSampleEmailEnhanced('WEEKLY_ADMIN_SUMMARY', {
      ...summaryData,
      weekEnding: new Date().toLocaleDateString(),
      adminDashboardUrl: `${window.location.origin}/admin/dashboard`
    });

    const response = await fetch(`${BACKEND_API_URL}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: ADMIN_EMAILS.PROPERTY_SUBMISSION,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: '<EMAIL>',
        fromName: 'HouseGoing Analytics'
      })
    });

    const result = await response.json();
    return result.success;

  } catch (error) {
    console.error('Error sending weekly admin summary:', error);
    return false;
  }
}

export default {
  sendPropertySubmissionNotification,
  sendPropertyApprovalEmail,
  sendPropertyRejectionEmail,
  sendNewHostSignupNotification,
  sendNewCustomerSignupNotification,
  sendWeeklyAdminSummary
};
