import { supabase } from '../supabase';

// Feature flag interface
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  description: string;
  userPercentage?: number;
  userIds?: string[];
  environment?: string[];
}

// Cache for feature flags
let featureFlagsCache: Record<string, FeatureFlag> = {};
let lastFetchTime = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch all feature flags from the database
 */
export async function fetchFeatureFlags(): Promise<Record<string, FeatureFlag>> {
  try {
    const { data, error } = await supabase
      .from('feature_flags')
      .select('*');
    
    if (error) {
      console.error('Error fetching feature flags:', error);
      return {};
    }
    
    // Convert array to record
    const flags: Record<string, FeatureFlag> = {};
    data.forEach(flag => {
      flags[flag.key] = flag;
    });
    
    // Update cache
    featureFlagsCache = flags;
    lastFetchTime = Date.now();
    
    return flags;
  } catch (error) {
    console.error('Error fetching feature flags:', error);
    return {};
  }
}

/**
 * Get all feature flags (from cache or database)
 */
export async function getFeatureFlags(): Promise<Record<string, FeatureFlag>> {
  // Check if cache is valid
  if (Object.keys(featureFlagsCache).length > 0 && Date.now() - lastFetchTime < CACHE_TTL) {
    return featureFlagsCache;
  }
  
  // Fetch from database
  return fetchFeatureFlags();
}

/**
 * Check if a feature flag is enabled
 * @param key Feature flag key
 * @param userId User ID (optional)
 */
export async function isFeatureEnabled(key: string, userId?: string): Promise<boolean> {
  // Get feature flags
  const flags = await getFeatureFlags();
  const flag = flags[key];
  
  // If flag doesn't exist, return false
  if (!flag) {
    return false;
  }
  
  // If flag is disabled, return false
  if (!flag.enabled) {
    return false;
  }
  
  // Check environment
  if (flag.environment && flag.environment.length > 0) {
    const currentEnv = process.env.NODE_ENV || 'development';
    if (!flag.environment.includes(currentEnv)) {
      return false;
    }
  }
  
  // If no user ID, return true (flag is enabled for everyone)
  if (!userId) {
    return true;
  }
  
  // Check user IDs
  if (flag.userIds && flag.userIds.length > 0) {
    return flag.userIds.includes(userId);
  }
  
  // Check user percentage
  if (flag.userPercentage !== undefined && flag.userPercentage < 100) {
    // Generate a hash from the user ID and feature key
    const hash = hashString(`${userId}:${key}`);
    // Convert hash to a number between 0 and 100
    const userValue = hash % 100;
    // Check if user is in the enabled percentage
    return userValue < flag.userPercentage;
  }
  
  // Default to true
  return true;
}

/**
 * Create or update a feature flag
 * @param flag Feature flag
 */
export async function setFeatureFlag(flag: FeatureFlag): Promise<void> {
  try {
    const { error } = await supabase
      .from('feature_flags')
      .upsert({
        key: flag.key,
        enabled: flag.enabled,
        description: flag.description,
        user_percentage: flag.userPercentage,
        user_ids: flag.userIds,
        environment: flag.environment
      });
    
    if (error) {
      console.error('Error setting feature flag:', error);
      return;
    }
    
    // Invalidate cache
    lastFetchTime = 0;
  } catch (error) {
    console.error('Error setting feature flag:', error);
  }
}

/**
 * Delete a feature flag
 * @param key Feature flag key
 */
export async function deleteFeatureFlag(key: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('feature_flags')
      .delete()
      .eq('key', key);
    
    if (error) {
      console.error('Error deleting feature flag:', error);
      return;
    }
    
    // Invalidate cache
    lastFetchTime = 0;
  } catch (error) {
    console.error('Error deleting feature flag:', error);
  }
}

/**
 * Generate a hash from a string
 * @param str String to hash
 */
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}
