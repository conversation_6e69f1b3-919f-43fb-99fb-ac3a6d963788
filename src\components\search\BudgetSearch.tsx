import React, { useState, useEffect } from 'react';
import { DollarSign } from 'lucide-react';
import SearchDropdown from './SearchDropdown';

interface BudgetSearchProps {
  value: number;
  onChange: (value: number) => void;
  onClose: () => void;
  isOpen: boolean;
}

export default function BudgetSearch({ value, onChange, onClose, isOpen }: BudgetSearchProps) {
  const [budget, setBudget] = useState<string>(value > 0 ? value.toString() : '');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (isOpen) {
      // Reset error when dropdown opens
      setError('');
    }
  }, [isOpen]);

  const handleBudgetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Allow empty input or numbers only
    if (inputValue === '' || /^\d+$/.test(inputValue)) {
      setBudget(inputValue);
      setError('');
    }
  };

  const handleApply = () => {
    if (budget === '') {
      // If budget is empty, set it to 0 (no budget filter)
      onChange(0);
      onClose();
      return;
    }

    const budgetValue = parseInt(budget, 10);

    if (isNaN(budgetValue)) {
      setError('Please enter a valid number');
      return;
    }

    if (budgetValue < 100) {
      setError('Minimum budget is $100');
      return;
    }

    onChange(budgetValue);
    onClose();
  };

  const handleClear = () => {
    setBudget('');
    onChange(0);
    onClose();
  };

  return (
    <SearchDropdown isOpen={isOpen} onClose={onClose} width="320px">
      <div className="p-6">
        <div className="mb-6">
          <label htmlFor="budget" className="block text-sm font-semibold text-gray-800 mb-2">
            Your Budget
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <DollarSign className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="budget"
              placeholder="Enter your total budget"
              className={`block w-full pl-10 pr-3 py-3 border ${
                error ? 'border-red-500' : 'border-gray-300'
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200`}
              value={budget}
              onChange={handleBudgetChange}
              autoFocus
            />
          </div>
          {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
          <p className="mt-3 text-sm text-gray-600">
            We'll show you venues and how many hours you can book with your budget.
          </p>
        </div>

        <div className="flex justify-between items-center">
          <button
            onClick={handleClear}
            className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors duration-200"
          >
            Clear
          </button>
          <button
            onClick={handleApply}
            className="bg-purple-600 text-white px-6 py-2.5 rounded-lg hover:bg-purple-700 text-sm font-medium transition-all duration-200 hover:shadow-lg"
          >
            Apply
          </button>
        </div>
      </div>
    </SearchDropdown>
  );
}
