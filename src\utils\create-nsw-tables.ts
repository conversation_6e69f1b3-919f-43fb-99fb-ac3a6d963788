import { supabase } from '../lib/supabase-client';

/**
 * Create the necessary tables for the NSW Party Planning tool
 */
export async function createNSWTables() {
  try {
    console.log('Creating NSW Party Planning tables...');

    // Create nsw_curfew_zones table
    const { error: curfewZonesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS nsw_curfew_zones (
          id SERIAL PRIMARY KEY,
          address TEXT NOT NULL,
          property_type TEXT,
          zone_code TEXT,
          zone_name TEXT,
          lga_name TEXT,
          weekday_curfew_start TIME,
          weekday_curfew_end TIME,
          weekend_curfew_start TIME,
          weekend_curfew_end TIME,
          weekday_bass_restriction TIME,
          weekend_bass_restriction TIME,
          weekday_outdoor_cutoff TIME,
          weekend_outdoor_cutoff TIME,
          special_condition TEXT,
          is_holiday BOOLEAN DEFAULT FALSE,
          holiday_name TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_nsw_curfew_zones_address ON nsw_curfew_zones(address);
        CREATE INDEX IF NOT EXISTS idx_nsw_curfew_zones_lga ON nsw_curfew_zones(lga_name);
        CREATE INDEX IF NOT EXISTS idx_nsw_curfew_zones_zone ON nsw_curfew_zones(zone_code);
      `
    });

    if (curfewZonesError) {
      console.error('Error creating nsw_curfew_zones table:', curfewZonesError);
    } else {
      console.log('✅ nsw_curfew_zones table created successfully');
    }

    // Create curfew_requests table
    const { error: curfewRequestsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS curfew_requests (
          id SERIAL PRIMARY KEY,
          address TEXT NOT NULL,
          date DATE NOT NULL,
          status TEXT DEFAULT 'pending',
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_curfew_requests_address ON curfew_requests(address);
        CREATE INDEX IF NOT EXISTS idx_curfew_requests_status ON curfew_requests(status);
        CREATE INDEX IF NOT EXISTS idx_curfew_requests_date ON curfew_requests(date);
      `
    });

    if (curfewRequestsError) {
      console.error('Error creating curfew_requests table:', curfewRequestsError);
    } else {
      console.log('✅ curfew_requests table created successfully');
    }

    // Create zoning_rules table (for reference data)
    const { error: zoningRulesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS zoning_rules (
          id SERIAL PRIMARY KEY,
          zone_code TEXT UNIQUE NOT NULL,
          zone_name TEXT NOT NULL,
          weekday_curfew_start TIME,
          weekday_curfew_end TIME,
          weekend_curfew_start TIME,
          weekend_curfew_end TIME,
          description TEXT,
          special_conditions TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_zoning_rules_code ON zoning_rules(zone_code);
      `
    });

    if (zoningRulesError) {
      console.error('Error creating zoning_rules table:', zoningRulesError);
    } else {
      console.log('✅ zoning_rules table created successfully');
    }

    console.log('NSW Party Planning tables setup complete!');
    return true;

  } catch (error) {
    console.error('Error creating NSW tables:', error);
    return false;
  }
}

/**
 * Insert default zoning rules data
 */
export async function insertDefaultZoningRules() {
  try {
    console.log('Inserting default zoning rules...');

    const defaultRules = [
      {
        zone_code: 'R1',
        zone_name: 'General Residential',
        weekday_curfew_start: '22:00:00',
        weekday_curfew_end: '07:00:00',
        weekend_curfew_start: '22:00:00',
        weekend_curfew_end: '08:00:00',
        description: 'Standard residential zoning',
        special_conditions: 'Music noise must be inaudible in neighboring premises during curfew hours'
      },
      {
        zone_code: 'R2',
        zone_name: 'Low Density Residential',
        weekday_curfew_start: '22:00:00',
        weekday_curfew_end: '07:00:00',
        weekend_curfew_start: '22:00:00',
        weekend_curfew_end: '08:00:00',
        description: 'Residential with larger lots',
        special_conditions: 'Stricter enforcement in purely residential areas'
      },
      {
        zone_code: 'R3',
        zone_name: 'Medium Density Residential',
        weekday_curfew_start: '22:00:00',
        weekday_curfew_end: '07:00:00',
        weekend_curfew_start: '22:00:00',
        weekend_curfew_end: '08:00:00',
        description: 'Higher density housing',
        special_conditions: 'Higher sensitivity due to closer proximity of dwellings'
      },
      {
        zone_code: 'B1',
        zone_name: 'Neighbourhood Centre',
        weekday_curfew_start: '00:00:00',
        weekday_curfew_end: '07:00:00',
        weekend_curfew_start: '00:00:00',
        weekend_curfew_end: '08:00:00',
        description: 'Small commercial areas',
        special_conditions: '11:00 PM cutoff for outdoor activities'
      },
      {
        zone_code: 'B2',
        zone_name: 'Local Centre',
        weekday_curfew_start: '00:00:00',
        weekday_curfew_end: '07:00:00',
        weekend_curfew_start: '00:00:00',
        weekend_curfew_end: '08:00:00',
        description: 'Local commercial areas',
        special_conditions: 'Extended hours may apply for licensed venues'
      },
      {
        zone_code: 'B8',
        zone_name: 'Metropolitan Centre',
        weekday_curfew_start: '00:00:00',
        weekday_curfew_end: '07:00:00',
        weekend_curfew_start: '00:00:00',
        weekend_curfew_end: '08:00:00',
        description: 'Major commercial centres like Sydney CBD',
        special_conditions: 'Minimal restrictions, subject to venue-specific conditions'
      }
    ];

    for (const rule of defaultRules) {
      const { error } = await supabase
        .from('zoning_rules')
        .upsert(rule, { onConflict: 'zone_code' });

      if (error) {
        console.error(`Error inserting rule for ${rule.zone_code}:`, error);
      }
    }

    console.log('✅ Default zoning rules inserted successfully');
    return true;

  } catch (error) {
    console.error('Error inserting default zoning rules:', error);
    return false;
  }
}
