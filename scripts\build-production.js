/**
 * Production Build Script
 *
 * This script handles the production build process with specific optimizations
 * and configurations for the production environment.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');
const distDir = path.join(rootDir, 'dist');

console.log('Starting production build process...');

try {
  // Step 1: Run the standard build
  console.log('Running Vite build...');
  execSync('vite build', { stdio: 'inherit', cwd: rootDir });

  // Step 2: Create the _redirects file
  console.log('Creating _redirects file...');
  const redirectsContent = `
/nsw-curfew-zoning /index.html 200
/nsw-party-planning /index.html 200
/nsw-party-planning-updated /index.html 200
/nsw-address-v2 /index.html 200
/precise-party-planning /index.html 200
/precise-address /index.html 200
/* /index.html 200
`.trim();

  fs.writeFileSync(path.join(distDir, '_redirects'), redirectsContent);

  // Step 3: Create a netlify.toml file in the dist directory
  console.log('Creating netlify.toml in dist directory...');
  const netlifyTomlContent = `
# Handle client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# NSW Party Planning Tool redirects
[[redirects]]
  from = "/nsw-curfew-zoning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning-updated"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-address-v2"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-address"
  to = "/index.html"
  status = 200
  force = true
`.trim();

  fs.writeFileSync(path.join(distDir, 'netlify.toml'), netlifyTomlContent);

  // Step 4: Copy the public data directory to the dist directory
  console.log('Copying data directory to dist...');
  const publicDataDir = path.join(rootDir, 'public', 'data');
  const distDataDir = path.join(distDir, 'data');

  if (!fs.existsSync(distDataDir)) {
    fs.mkdirSync(distDataDir, { recursive: true });
  }

  // Copy all files from public/data to dist/data
  if (fs.existsSync(publicDataDir)) {
    const files = fs.readdirSync(publicDataDir);
    for (const file of files) {
      const srcFile = path.join(publicDataDir, file);
      const destFile = path.join(distDataDir, file);
      fs.copyFileSync(srcFile, destFile);
    }
  }

  // Step 5: Copy SEO files (robots.txt and sitemap files) to the dist directory
  console.log('Copying SEO files to dist...');
  const robotsTxtSrc = path.join(rootDir, 'public', 'robots.txt');
  const sitemapIndexSrc = path.join(rootDir, 'public', 'sitemap_index.xml');
  const sitemapMainSrc = path.join(rootDir, 'public', 'sitemap_main.xml');

  if (fs.existsSync(robotsTxtSrc)) {
    fs.copyFileSync(robotsTxtSrc, path.join(distDir, 'robots.txt'));
    console.log('robots.txt copied to dist directory');
  } else {
    console.warn('Warning: robots.txt not found in public directory');
  }

  if (fs.existsSync(sitemapIndexSrc)) {
    fs.copyFileSync(sitemapIndexSrc, path.join(distDir, 'sitemap_index.xml'));
    console.log('sitemap_index.xml copied to dist directory');
  } else {
    console.warn('Warning: sitemap_index.xml not found in public directory');
  }

  if (fs.existsSync(sitemapMainSrc)) {
    fs.copyFileSync(sitemapMainSrc, path.join(distDir, 'sitemap_main.xml'));
    console.log('sitemap_main.xml copied to dist directory');
  } else {
    console.warn('Warning: sitemap_main.xml not found in public directory');
  }

  console.log('Production build completed successfully!');
} catch (error) {
  console.error('Error during production build:', error);
  process.exit(1);
}
