# HouseGoing Email Server

This is a simple Express server that handles email notifications for the HouseGoing platform.

## Features

- Send test emails
- Send property submission notifications
- Uses Nodemailer with Gmail SMTP

## Setup

1. Make sure you have Node.js installed
2. Install dependencies:

```bash
cd server
npm install
```

3. Start the email server:

```bash
npm run email
```

The server will start on port 3001 by default.

## API Endpoints

### Test Email

Send a test email to verify the email service is working.

```
POST /api/test-email
```

### Property Submission Email

Send a notification email when a new property is submitted.

```
POST /api/property-submission-email
```

Request body:

```json
{
  "name": "Property Name",
  "id": "property-id",
  "address": "123 Main St, Sydney NSW 2000",
  "type": "house",
  "ownerId": "owner-id"
}
```

## Gmail App Password

The server uses a Gmail app password for authentication. This is a 16-character password that allows the server to send emails through Gmail.

In a production environment, this password should be stored in environment variables, not hardcoded in the source code.

## Client Integration

The client-side code uses the `emailService.js` module to communicate with this server. If the server is not running, the client will use mock functions to simulate sending emails.

## Troubleshooting

If you encounter issues with the email server:

1. Make sure the server is running on port 3001
2. Check that the Gmail app password is correct
3. Verify that the Gmail account has "Less secure app access" enabled
4. Check the server logs for error messages
