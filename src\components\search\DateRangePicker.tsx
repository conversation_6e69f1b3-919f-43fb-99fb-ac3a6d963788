import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import SearchDropdown from './SearchDropdown';

interface DateRangePickerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (dates: { start: string; end: string }) => void;
}

export default function DateRangePicker({ isOpen, onClose, onSelect }: DateRangePickerProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date()); // Current month
  const [selectedRange, setSelectedRange] = useState<{ start?: Date; end?: Date }>({});
  const [today] = useState(new Date()); // Store today's date

  // Ensure we start from current date when opening
  useEffect(() => {
    if (isOpen) {
      // Reset to current month when opening
      setCurrentMonth(new Date());
    }
  }, [isOpen]);

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDay = new Date(year, month, 1).getDay();
    return { daysInMonth, firstDay };
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  const isDateDisabled = (date: Date) => {
    // Reset hours to compare just the dates
    const compareDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const compareToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    return compareDate < compareToday;
  };

  const handleDateClick = (day: number) => {
    const clickedDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );

    if (isDateDisabled(clickedDate)) return;

    if (!selectedRange.start || (selectedRange.start && selectedRange.end)) {
      setSelectedRange({ start: clickedDate });
    } else {
      if (clickedDate < selectedRange.start) {
        setSelectedRange({ start: clickedDate, end: selectedRange.start });
      } else {
        setSelectedRange({ start: selectedRange.start, end: clickedDate });
        onSelect({
          start: selectedRange.start.toISOString().split('T')[0],
          end: clickedDate.toISOString().split('T')[0]
        });
        onClose();
      }
    }
  };

  const { daysInMonth, firstDay } = getDaysInMonth(currentMonth);
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  const blanks = Array.from({ length: firstDay }, (_, i) => i);

  const canGoToPreviousMonth = () => {
    const firstDayOfCurrentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const firstDayOfCurrentMonthToday = new Date(today.getFullYear(), today.getMonth(), 1);
    return firstDayOfCurrentMonth > firstDayOfCurrentMonthToday;
  };

  return (
    <SearchDropdown isOpen={isOpen} onClose={onClose} width="400px">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() - 1)))}
            className={`p-2 rounded-full transition-colors duration-200 ${
              canGoToPreviousMonth()
                ? 'hover:bg-gray-100 text-gray-600'
                : 'text-gray-300 cursor-not-allowed'
            }`}
            disabled={!canGoToPreviousMonth()}
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <h3 className="text-lg font-semibold text-gray-800">{formatDate(currentMonth)}</h3>
          <button
            onClick={() => setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() + 1)))}
            className="p-2 hover:bg-gray-100 rounded-full text-gray-600 transition-colors duration-200"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>

        <div className="grid grid-cols-7 gap-1 mb-3">
          {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
            <div key={day} className="text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {blanks.map((_, i) => (
            <div key={`blank-${i}`} className="h-10" />
          ))}
          {days.map(day => {
            const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
            const isDisabled = isDateDisabled(date);
            const isSelected = selectedRange.start?.getTime() === date.getTime() ||
                             selectedRange.end?.getTime() === date.getTime();
            const isInRange = selectedRange.start && selectedRange.end &&
                            date > selectedRange.start && date < selectedRange.end;

            return (
              <button
                key={day}
                onClick={() => !isDisabled && handleDateClick(day)}
                disabled={isDisabled}
                className={`
                  h-10 rounded-full flex items-center justify-center
                  ${isDisabled ? 'text-gray-300 cursor-not-allowed bg-gray-50 line-through' : 'hover:bg-purple-50'}
                  ${isSelected ? 'bg-purple-600 text-white hover:bg-purple-700' : ''}
                  ${isInRange ? 'bg-purple-100' : ''}
                `}
                title={isDisabled ? 'Past dates cannot be selected' : ''}
              >
                {day}
              </button>
            );
          })}
        </div>

        <div className="mt-6 text-sm">
          {selectedRange.start && !selectedRange.end ? (
            <p className="text-purple-600 font-medium">
              Select end date
            </p>
          ) : (
            <p className="text-gray-500">
              <span className="text-gray-400">•</span> Past dates are not available for booking
            </p>
          )}
        </div>
      </div>
    </SearchDropdown>
  );
}