/**
 * Admin Submission Detail Sections
 *
 * Displays property submission details in the exact same format as the property submission form
 */

import React, { useState } from 'react';
import {
  MapPin,
  Users,
  DollarSign,
  Phone,
  Home,
  Star,
  Clock,
  Shield,
  FileText,
  CreditCard,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MessageSquare,
  Plus,
  Save
} from 'lucide-react';

interface PropertySubmission {
  id: string;

  // Basic Information
  name: string;
  address: string;
  type: string;
  location?: [number, number];
  phoneNumber?: string;
  partyAcknowledgment?: boolean;

  // Venue Details
  description: string;
  size?: number;
  functionRooms?: number;
  eventSpaces?: number;
  maxGuests: number;
  price: number;

  // Amenities & Features
  amenities?: string[];
  parkingDetails?: string;
  transportDetails?: string;
  nearbyLandmarks?: string;
  byoPolicy?: string;

  // House Rules
  noiseRestrictions?: string;
  endTime?: { weekday: string; weekend: string; };
  decorationsPolicy?: string;
  smokingPolicy?: string;
  petPolicy?: string;
  additionalFees?: string;
  curfew?: {
    weekday: { start: string; end: string; };
    weekend: { start: string; end: string; };
  };
  bassRestriction?: { weekday: string; weekend: string; };
  outdoorCutoff?: { weekday: string; weekend: string; };
  specialCondition?: string;

  // Insurance & Compliance
  hasInsurance?: boolean;
  insuranceProvider?: string;
  policyNumber?: string;
  coverageAmount?: string;
  expiryDate?: string;
  insuranceCertificate?: string;

  // Licenses & Permits
  hasLiquorLicense?: boolean;
  liquorLicenseNumber?: string;
  liquorLicenseExpiry?: string;
  hasFoodPermit?: boolean;
  foodPermitNumber?: string;
  hasEntertainmentLicense?: boolean;
  entertainmentLicenseNumber?: string;
  capacityCertificate?: string;
  fireSafetyCompliance?: boolean;

  // Additional Information
  accessibilityFeatures?: string[];
  cateringOptions?: string;
  equipmentProvided?: string;
  staffAvailable?: boolean;
  setupTime?: number;
  cleanupTime?: number;

  // Photos
  images?: string[];

  // Identity Verification Documents
  identityVerification?: {
    driverLicenseFront?: string;
    driverLicenseBack?: string;
    selfiePhoto?: string;
    proofOfAddress?: string;
    additionalDocuments?: string[];
  };

  // Bank Details
  bankDetails?: {
    accountName: string;
    bsb: string;
    accountNumber: string;
    bankName: string;
  };

  // System fields
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  ownerId: string;
  ownerEmail: string;
  ownerName: string;
  approved_by?: string;
  approved_at?: string;
  rejection_reason?: string;
  admin_notes?: string;
}

interface AdminSubmissionDetailSectionsProps {
  submission: PropertySubmission;
}

interface SectionComment {
  id: string;
  section: string;
  comment: string;
  timestamp: string;
  adminEmail: string;
}

export default function AdminSubmissionDetailSections({ submission }: AdminSubmissionDetailSectionsProps) {
  const [sectionComments, setSectionComments] = useState<Record<string, SectionComment[]>>({});
  const [activeCommentSection, setActiveCommentSection] = useState<string | null>(null);
  const [newComment, setNewComment] = useState('');

  // Add comment to section
  const addSectionComment = (sectionId: string) => {
    if (!newComment.trim()) return;

    const comment: SectionComment = {
      id: Date.now().toString(),
      section: sectionId,
      comment: newComment.trim(),
      timestamp: new Date().toISOString(),
      adminEmail: '<EMAIL>'
    };

    setSectionComments(prev => ({
      ...prev,
      [sectionId]: [...(prev[sectionId] || []), comment]
    }));

    setNewComment('');
    setActiveCommentSection(null);
  };

  const SectionCard = ({ title, sectionId, icon: Icon, children }: { title: string; sectionId: string; icon: any; children: React.ReactNode }) => {
    const comments = sectionComments[sectionId] || [];
    const isCommenting = activeCommentSection === sectionId;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Icon className="w-5 h-5 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>

          <div className="flex items-center gap-2">
            {comments.length > 0 && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                <MessageSquare className="w-3 h-3" />
                {comments.length}
              </span>
            )}
            <button
              onClick={() => setActiveCommentSection(isCommenting ? null : sectionId)}
              className="inline-flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
            >
              <Plus className="w-4 h-4" />
              Comment
            </button>
          </div>
        </div>

        {children}

        {/* Existing Comments */}
        {comments.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Admin Comments</h4>
            <div className="space-y-3">
              {comments.map((comment) => (
                <div key={comment.id} className="bg-blue-50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-900">{comment.adminEmail}</span>
                    <span className="text-xs text-blue-600">
                      {new Date(comment.timestamp).toLocaleDateString()} {new Date(comment.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm text-blue-800">{comment.comment}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add Comment Form */}
        {isCommenting && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Add Comment</h4>
            <div className="space-y-3">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add your comment about this section..."
                className="w-full p-3 border border-gray-300 rounded-lg resize-none h-20 text-sm"
              />
              <div className="flex gap-2">
                <button
                  onClick={() => addSectionComment(sectionId)}
                  disabled={!newComment.trim()}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white text-sm rounded-md transition-colors"
                >
                  <Save className="w-3 h-3" />
                  Save Comment
                </button>
                <button
                  onClick={() => {
                    setActiveCommentSection(null);
                    setNewComment('');
                  }}
                  className="px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm rounded-md transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const InfoRow = ({ label, value, type = 'text' }: { label: string; value: any; type?: 'text' | 'boolean' | 'currency' | 'list' }) => {
    if (value === undefined || value === null || value === '') return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-3 border-b border-gray-100 last:border-b-0">
        <dt className="text-sm font-medium text-gray-500">{label}</dt>
        <dd className="md:col-span-2 text-sm text-gray-900">
          {type === 'boolean' ? (
            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
              value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {value ? <CheckCircle className="w-3 h-3" /> : <XCircle className="w-3 h-3" />}
              {value ? 'Yes' : 'No'}
            </span>
          ) : type === 'currency' ? (
            `$${value} AUD`
          ) : type === 'list' && Array.isArray(value) ? (
            <div className="flex flex-wrap gap-2">
              {value.map((item, index) => (
                <span key={index} className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-xs">
                  <Star className="w-3 h-3" />
                  {item}
                </span>
              ))}
            </div>
          ) : (
            value
          )}
        </dd>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <SectionCard title="Basic Information" sectionId="basic-info" icon={Home}>
        <dl className="space-y-0">
          <InfoRow label="Property Name" value={submission.name} />
          <InfoRow label="Address" value={submission.address} />
          <InfoRow label="Property Type" value={submission.type} />
          <InfoRow label="Phone Number" value={submission.phoneNumber} />
          <InfoRow label="Party Acknowledgment" value={submission.partyAcknowledgment} type="boolean" />
          {submission.location && (
            <InfoRow label="Location Coordinates" value={`${submission.location[0]}, ${submission.location[1]}`} />
          )}
        </dl>
      </SectionCard>

      {/* Venue Details */}
      <SectionCard title="Venue Details" sectionId="venue-details" icon={Users}>
        <dl className="space-y-0">
          <InfoRow label="Description" value={submission.description} />
          <InfoRow label="Size (sqm)" value={submission.size} />
          <InfoRow label="Function Rooms" value={submission.functionRooms} />
          <InfoRow label="Event Spaces" value={submission.eventSpaces} />
          <InfoRow label="Maximum Guests" value={submission.maxGuests} />
          <InfoRow label="Hourly Rate" value={submission.price} type="currency" />
        </dl>
      </SectionCard>

      {/* Amenities & Features */}
      <SectionCard title="Amenities & Features" sectionId="amenities" icon={Star}>
        <dl className="space-y-0">
          <InfoRow label="Amenities" value={submission.amenities} type="list" />
          <InfoRow label="Parking Details" value={submission.parkingDetails} />
          <InfoRow label="Transport Details" value={submission.transportDetails} />
          <InfoRow label="Nearby Landmarks" value={submission.nearbyLandmarks} />
          <InfoRow label="BYO Policy" value={submission.byoPolicy} />
        </dl>
      </SectionCard>

      {/* House Rules */}
      <SectionCard title="House Rules" sectionId="house-rules" icon={FileText}>
        <dl className="space-y-0">
          <InfoRow label="Noise Restrictions" value={submission.noiseRestrictions} />
          {submission.endTime && (
            <>
              <InfoRow label="End Time (Weekday)" value={submission.endTime.weekday} />
              <InfoRow label="End Time (Weekend)" value={submission.endTime.weekend} />
            </>
          )}
          <InfoRow label="Decorations Policy" value={submission.decorationsPolicy} />
          <InfoRow label="Smoking Policy" value={submission.smokingPolicy} />
          <InfoRow label="Pet Policy" value={submission.petPolicy} />
          <InfoRow label="Additional Fees" value={submission.additionalFees} />
          {submission.curfew && (
            <>
              <InfoRow label="Curfew (Weekday)" value={`${submission.curfew.weekday.start} - ${submission.curfew.weekday.end}`} />
              <InfoRow label="Curfew (Weekend)" value={`${submission.curfew.weekend.start} - ${submission.curfew.weekend.end}`} />
            </>
          )}
          {submission.bassRestriction && (
            <>
              <InfoRow label="Bass Restriction (Weekday)" value={submission.bassRestriction.weekday} />
              <InfoRow label="Bass Restriction (Weekend)" value={submission.bassRestriction.weekend} />
            </>
          )}
          {submission.outdoorCutoff && (
            <>
              <InfoRow label="Outdoor Cutoff (Weekday)" value={submission.outdoorCutoff.weekday} />
              <InfoRow label="Outdoor Cutoff (Weekend)" value={submission.outdoorCutoff.weekend} />
            </>
          )}
          <InfoRow label="Special Conditions" value={submission.specialCondition} />
        </dl>
      </SectionCard>

      {/* Insurance & Compliance */}
      <SectionCard title="Insurance & Compliance" sectionId="insurance" icon={Shield}>
        <dl className="space-y-0">
          <InfoRow label="Has Insurance" value={submission.hasInsurance} type="boolean" />
          <InfoRow label="Insurance Provider" value={submission.insuranceProvider} />
          <InfoRow label="Policy Number" value={submission.policyNumber} />
          <InfoRow label="Coverage Amount" value={submission.coverageAmount} />
          <InfoRow label="Expiry Date" value={submission.expiryDate} />
          <InfoRow label="Insurance Certificate" value={submission.insuranceCertificate} />
        </dl>
      </SectionCard>

      {/* Licenses & Permits */}
      <SectionCard title="Licenses & Permits" sectionId="licenses" icon={FileText}>
        <dl className="space-y-0">
          <InfoRow label="Has Liquor License" value={submission.hasLiquorLicense} type="boolean" />
          <InfoRow label="Liquor License Number" value={submission.liquorLicenseNumber} />
          <InfoRow label="Liquor License Expiry" value={submission.liquorLicenseExpiry} />
          <InfoRow label="Has Food Permit" value={submission.hasFoodPermit} type="boolean" />
          <InfoRow label="Food Permit Number" value={submission.foodPermitNumber} />
          <InfoRow label="Has Entertainment License" value={submission.hasEntertainmentLicense} type="boolean" />
          <InfoRow label="Entertainment License Number" value={submission.entertainmentLicenseNumber} />
          <InfoRow label="Capacity Certificate" value={submission.capacityCertificate} />
          <InfoRow label="Fire Safety Compliance" value={submission.fireSafetyCompliance} type="boolean" />
        </dl>
      </SectionCard>

      {/* Additional Information */}
      <SectionCard title="Additional Information" sectionId="additional-info" icon={AlertTriangle}>
        <dl className="space-y-0">
          <InfoRow label="Accessibility Features" value={submission.accessibilityFeatures} type="list" />
          <InfoRow label="Catering Options" value={submission.cateringOptions} />
          <InfoRow label="Equipment Provided" value={submission.equipmentProvided} />
          <InfoRow label="Staff Available" value={submission.staffAvailable} type="boolean" />
          <InfoRow label="Setup Time (hours)" value={submission.setupTime} />
          <InfoRow label="Cleanup Time (hours)" value={submission.cleanupTime} />
        </dl>
      </SectionCard>

      {/* Identity Verification Documents */}
      {submission.identityVerification && (
        <SectionCard title="Identity Verification Documents" sectionId="identity-verification" icon={Shield}>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Driver License Front */}
              {submission.identityVerification.driverLicenseFront && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Driver License (Front)</h4>
                  <div className="aspect-[3/2] rounded-lg overflow-hidden border border-gray-200">
                    <img
                      src={submission.identityVerification.driverLicenseFront}
                      alt="Driver License Front"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}

              {/* Driver License Back */}
              {submission.identityVerification.driverLicenseBack && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Driver License (Back)</h4>
                  <div className="aspect-[3/2] rounded-lg overflow-hidden border border-gray-200">
                    <img
                      src={submission.identityVerification.driverLicenseBack}
                      alt="Driver License Back"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}

              {/* Selfie Photo */}
              {submission.identityVerification.selfiePhoto && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Selfie Verification</h4>
                  <div className="aspect-square rounded-lg overflow-hidden border border-gray-200 max-w-48">
                    <img
                      src={submission.identityVerification.selfiePhoto}
                      alt="Selfie Verification"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}

              {/* Proof of Address */}
              {submission.identityVerification.proofOfAddress && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Proof of Address</h4>
                  <div className="aspect-[3/2] rounded-lg overflow-hidden border border-gray-200">
                    <img
                      src={submission.identityVerification.proofOfAddress}
                      alt="Proof of Address"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Additional Documents */}
            {submission.identityVerification.additionalDocuments && submission.identityVerification.additionalDocuments.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Additional Documents</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {submission.identityVerification.additionalDocuments.map((doc, index) => (
                    <div key={index} className="aspect-[3/2] rounded-lg overflow-hidden border border-gray-200">
                      <img
                        src={doc}
                        alt={`Additional Document ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </SectionCard>
      )}

      {/* Bank Details */}
      {submission.bankDetails && (
        <SectionCard title="Bank Details" sectionId="bank-details" icon={CreditCard}>
          <dl className="space-y-0">
            <InfoRow label="Account Name" value={submission.bankDetails.accountName} />
            <InfoRow label="BSB" value={submission.bankDetails.bsb} />
            <InfoRow label="Account Number" value={submission.bankDetails.accountNumber} />
            <InfoRow label="Bank Name" value={submission.bankDetails.bankName} />
          </dl>
        </SectionCard>
      )}

      {/* Photos */}
      {submission.images && submission.images.length > 0 && (
        <SectionCard title="Property Photos" sectionId="property-photos" icon={Star}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {submission.images.map((image, index) => (
              <div key={index} className="aspect-video rounded-lg overflow-hidden">
                <img
                  src={image}
                  alt={`${submission.name} - Photo ${index + 1}`}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                />
              </div>
            ))}
          </div>
        </SectionCard>
      )}
    </div>
  );
}
