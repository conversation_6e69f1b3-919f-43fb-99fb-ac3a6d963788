-- Create bookings table for HouseGoing
-- Run this in the Supabase SQL Editor

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  guest_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  guests_count INTEGER NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  payment_id VARCHAR(255),
  payment_status VARCHAR(20),
  special_requests TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_date_range CHECK (end_date > start_date),
  CONSTRAINT valid_status CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed', 'rejected')),
  CONSTRAINT valid_guests_count CHECK (guests_count > 0),
  CONSTRAINT valid_total_price CHECK (total_price >= 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_venue_id ON bookings(venue_id);
CREATE INDEX IF NOT EXISTS idx_bookings_guest_id ON bookings(guest_id);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_dates ON bookings(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON bookings(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_bookings_updated_at ON bookings;
CREATE TRIGGER update_bookings_updated_at
BEFORE UPDATE ON bookings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create function to check for booking conflicts
CREATE OR REPLACE FUNCTION check_booking_conflicts()
RETURNS TRIGGER AS $$
DECLARE
  conflicts INTEGER;
BEGIN
  -- Check for overlapping bookings for the same venue
  SELECT COUNT(*) INTO conflicts
  FROM bookings
  WHERE venue_id = NEW.venue_id
    AND status NOT IN ('cancelled', 'rejected')
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000')
    AND (
      (NEW.start_date < end_date AND NEW.end_date > start_date)
    );
  
  -- If there are conflicts, raise an exception
  IF conflicts > 0 THEN
    RAISE EXCEPTION 'Booking conflict: The venue is already booked during this time period';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent booking conflicts
DROP TRIGGER IF EXISTS prevent_booking_conflicts ON bookings;
CREATE TRIGGER prevent_booking_conflicts
BEFORE INSERT OR UPDATE ON bookings
FOR EACH ROW
EXECUTE FUNCTION check_booking_conflicts();

-- Create function for creating bookings with availability check
CREATE OR REPLACE FUNCTION create_booking_with_availability_check(
  p_venue_id UUID,
  p_guest_id TEXT,
  p_start_date TIMESTAMP WITH TIME ZONE,
  p_end_date TIMESTAMP WITH TIME ZONE,
  p_guests_count INTEGER,
  p_total_price DECIMAL,
  p_status TEXT DEFAULT 'pending'
)
RETURNS UUID AS $$
DECLARE
  v_booking_id UUID;
  v_availability_result JSON;
BEGIN
  -- Check venue availability first
  SELECT check_venue_availability(p_venue_id, p_start_date, p_end_date) INTO v_availability_result;
  
  -- If not available, raise exception
  IF NOT (v_availability_result->>'available')::BOOLEAN THEN
    RAISE EXCEPTION 'Venue not available: %', v_availability_result->>'conflicts';
  END IF;
  
  -- Create the booking
  INSERT INTO bookings (
    venue_id, guest_id, start_date, end_date, 
    guests_count, total_price, status
  ) VALUES (
    p_venue_id, p_guest_id, p_start_date, p_end_date,
    p_guests_count, p_total_price, p_status
  ) RETURNING id INTO v_booking_id;
  
  RETURN v_booking_id;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT ALL ON bookings TO authenticated;
GRANT ALL ON bookings TO anon;
GRANT EXECUTE ON FUNCTION create_booking_with_availability_check TO authenticated;
GRANT EXECUTE ON FUNCTION create_booking_with_availability_check TO anon;

-- Test the setup
SELECT 'Bookings table created successfully!' as result;
