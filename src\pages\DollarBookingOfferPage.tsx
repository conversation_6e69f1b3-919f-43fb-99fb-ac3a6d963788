/**
 * Dedicated page for the $1 Party Booking Offer
 * Explains the offer in detail and shows user progress
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { 
  DollarSign, 
  Star, 
  Gift, 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  Shield, 
  Users,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import DollarBookingOffer from '../components/offers/DollarBookingOffer';
import { useBookingOffer } from '../hooks/useBookingOffer';
import SEO from '../components/seo/SEO';

export default function DollarBookingOfferPage() {
  const { user } = useUser();
  const { bookingStatus, getOfferSummary } = useBookingOffer();
  
  const offerSummary = getOfferSummary(200); // Example venue cost for calculations

  return (
    <>
      <SEO
        title="$1 Party Booking Offer - Sustainable Pricing | HouseGoing"
        description="Get your first booking for just $1 booking fee, then every 5th booking! Our abuse-proof system rewards loyal customers with sustainable pricing."
        keywords="$1 booking fee, party venue discount, sustainable pricing, booking offer, venue rental deals"
        url="https://housegoing.com.au/dollar-booking-offer"
      />

      <div className="pt-32 pb-16 px-4 sm:px-6">
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
              <Sparkles className="h-6 w-6 mr-2" />
              THE ABUSE-PROOF FRONT-END OFFER
            </div>
            
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              💰 The $1 Party Booking
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 to-orange-600">
                Sustainable Version
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              A revolutionary pricing system that rewards loyal customers while maintaining sustainability. 
              Get your first booking for just $1, then every 5th booking thereafter!
            </p>

            {user ? (
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  to="/find-venues"
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 font-semibold text-lg flex items-center justify-center"
                >
                  Start Booking Now
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Link>
                <Link 
                  to="/profile"
                  className="border-2 border-yellow-500 text-yellow-600 px-8 py-4 rounded-lg hover:bg-yellow-50 transition-colors font-semibold text-lg"
                >
                  View My Progress
                </Link>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  to="/sign-up"
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-4 rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 font-semibold text-lg flex items-center justify-center"
                >
                  Sign Up to Get Started
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Link>
                <Link 
                  to="/sign-in"
                  className="border-2 border-yellow-500 text-yellow-600 px-8 py-4 rounded-lg hover:bg-yellow-50 transition-colors font-semibold text-lg"
                >
                  Sign In
                </Link>
              </div>
            )}
          </div>

          {/* Main Offer Component */}
          <div className="mb-16">
            <DollarBookingOffer showProgress={true} className="max-w-4xl mx-auto" />
          </div>

          {/* How It Works */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">How It Works</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <Star className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">First Booking</h3>
                <p className="text-gray-600">
                  Your very first booking gets the special $1 booking fee instead of the normal 5% of venue cost. 
                  Welcome to HouseGoing!
                </p>
              </div>

              <div className="text-center">
                <div className="bg-gradient-to-r from-blue-400 to-purple-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Build Your Progress</h3>
                <p className="text-gray-600">
                  Complete bookings to build toward your next discount. Our system automatically tracks 
                  your progress - no need to remember anything!
                </p>
              </div>

              <div className="text-center">
                <div className="bg-gradient-to-r from-green-400 to-teal-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <Gift className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Every 5th Booking</h3>
                <p className="text-gray-600">
                  Every 5th completed booking gets the $1 booking fee. It's our way of saying thanks 
                  for being a loyal customer!
                </p>
              </div>
            </div>
          </div>

          {/* Why This System Works */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Why This System Works</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <Shield className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-xl font-semibold text-gray-900">Abuse-Proof Design</h3>
                </div>
                <ul className="text-gray-600 space-y-2">
                  <li>• Requires completed bookings, not just sign-ups</li>
                  <li>• Cancelled bookings don't count toward progress</li>
                  <li>• System automatically tracks everything</li>
                  <li>• No way to game or manipulate the system</li>
                </ul>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <Users className="h-6 w-6 text-blue-600 mr-3" />
                  <h3 className="text-xl font-semibold text-gray-900">Sustainable for Everyone</h3>
                </div>
                <ul className="text-gray-600 space-y-2">
                  <li>• Venue costs always paid in full</li>
                  <li>• Platform remains financially viable</li>
                  <li>• Hosts get their full earnings</li>
                  <li>• Rewards genuine, loyal customers</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Savings Calculator */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Potential Savings</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { venuePrice: 100, title: "Small Venue" },
                { venuePrice: 300, title: "Medium Venue" },
                { venuePrice: 500, title: "Large Venue" }
              ].map((example, index) => {
                const normalFee = example.venuePrice * 0.05;
                const savings = normalFee - 1;
                
                return (
                  <div key={index} className="text-center p-6 bg-gradient-to-b from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{example.title}</h3>
                    <p className="text-gray-600 mb-4">${example.venuePrice} venue cost</p>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Normal fee (5%):</span>
                        <span className="font-medium">${normalFee.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">With offer:</span>
                        <span className="font-medium text-green-600">$1.00</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-bold">
                        <span className="text-green-600">You save:</span>
                        <span className="text-green-600">${savings.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Important Terms */}
          <div className="bg-gray-50 rounded-2xl p-8 mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Clock className="h-6 w-6 mr-3" />
              Important Terms & Conditions
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">What's Included:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• First booking: $1 booking fee</li>
                  <li>• Every 5th booking: $1 booking fee</li>
                  <li>• Automatic progress tracking</li>
                  <li>• No expiration on progress</li>
                  <li>• Applies to all venue types</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Important Notes:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Venue cost always paid in full</li>
                  <li>• Cancelled bookings don't count</li>
                  <li>• Only completed bookings count toward progress</li>
                  <li>• Offer applies to booking fee only</li>
                  <li>• Subject to terms and conditions</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                Full terms and conditions available in our{' '}
                <Link to="/terms" className="text-purple-600 hover:text-purple-700 underline">
                  Terms of Service
                </Link>
                . This offer is designed to be sustainable and abuse-proof while rewarding genuine customers.
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-yellow-400 to-orange-400 rounded-2xl p-8 text-white">
              <h2 className="text-3xl font-bold mb-4">Ready to Start Saving?</h2>
              <p className="text-xl mb-6 opacity-90">
                Join thousands of party hosts who are already enjoying the $1 booking offer!
              </p>
              
              {user ? (
                <Link 
                  to="/find-venues"
                  className="inline-flex items-center bg-white text-yellow-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-semibold text-lg"
                >
                  Browse Venues Now
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Link>
              ) : (
                <Link 
                  to="/sign-up"
                  className="inline-flex items-center bg-white text-yellow-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-semibold text-lg"
                >
                  Sign Up & Get Started
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
