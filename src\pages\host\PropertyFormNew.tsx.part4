  // Render Photos section
  const renderPhotos = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Venue Photos</h2>
      <p className="text-sm text-gray-600 mb-4">
        Upload high-quality photos of your venue to attract more bookings.
        You can upload multiple photos to showcase different areas of your venue.
      </p>
      <PhotoUpload
        onImagesUploaded={handleImagesUploaded}
        existingImages={formData.images}
      />
    </div>
  );
  
  // Render Bank Details section
  const renderBankDetails = () => (
    <div className="border-t pt-6 mt-6">
      <h2 className="text-xl font-bold mb-4">Australian Bank Account Details for Payouts*</h2>
      <p className="text-sm text-gray-600 mb-4">
        This information is required for processing your venue booking payments.
        Your details are securely stored and handled according to Australian privacy laws.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block mb-2 font-medium">Account Holder Name*</label>
          <input
            type="text"
            value={formData.bankDetails.accountName}
            onChange={(e) => setFormData({
              ...formData,
              bankDetails: {
                ...formData.bankDetails,
                accountName: e.target.value
              }
            })}
            className="w-full p-2 border rounded"
            placeholder="e.g. John Smith"
          />
          <p className="text-xs text-gray-500 mt-1">Enter the name exactly as it appears on your bank account</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">BSB* (6 digits)</label>
          <input
            type="text"
            value={formData.bankDetails.bsb}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              // Limit to 6 digits
              const bsb = value.slice(0, 6);
              setFormData({
                ...formData,
                bankDetails: {
                  ...formData.bankDetails,
                  bsb
                }
              });
            }}
            pattern="\d{6}"
            maxLength={6}
            className="w-full p-2 border rounded"
            placeholder="e.g. 062-000"
          />
          <p className="text-xs text-gray-500 mt-1">Australian BSB number (6 digits)</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Account Number* (6-10 digits)</label>
          <input
            type="text"
            value={formData.bankDetails.accountNumber}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              // Limit to 10 digits
              const accountNumber = value.slice(0, 10);
              setFormData({
                ...formData,
                bankDetails: {
                  ...formData.bankDetails,
                  accountNumber
                }
              });
            }}
            maxLength={10}
            className="w-full p-2 border rounded"
            placeholder="e.g. ********"
          />
          <p className="text-xs text-gray-500 mt-1">Enter your account number (6-10 digits)</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Bank Name*</label>
          <input
            type="text"
            value={formData.bankDetails.bankName}
            onChange={(e) => setFormData({
              ...formData,
              bankDetails: {
                ...formData.bankDetails,
                bankName: e.target.value
              }
            })}
            className="w-full p-2 border rounded"
            placeholder="e.g. Commonwealth Bank"
          />
          <p className="text-xs text-gray-500 mt-1">Enter the name of your bank</p>
        </div>
      </div>
    </div>
  );
  
  // Render Review section
  const renderReview = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Review & Submit</h2>
      
      <p className="text-sm text-gray-600 mb-6">
        Please review your venue details before submitting. Once submitted, our team will review your listing
        and you'll receive a notification when it's approved.
      </p>
      
      <div className="bg-gray-50 p-6 rounded-lg space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Basic Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Venue Name</p>
              <p>{formData.name || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Phone Number</p>
              <p>{formData.phoneNumber || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Venue Type</p>
              <p>{formData.type || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Address</p>
              <p>{formData.address || 'Not provided'}</p>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Venue Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Size</p>
              <p>{formData.size ? `${formData.size} m²` : 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Maximum Guests</p>
              <p>{formData.maxGuests || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Hourly Rate</p>
              <p>{formData.price ? `$${formData.price}` : 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Function Rooms</p>
              <p>{formData.functionRooms || 'None'}</p>
            </div>
          </div>
          
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-500">Description</p>
            <p className="mt-1">{formData.description || 'Not provided'}</p>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Insurance & Compliance</h3>
          <p>{formData.hasInsurance ? 'Has public liability insurance' : 'No public liability insurance'}</p>
          {formData.hasInsurance && (
            <div className="grid grid-cols-2 gap-4 mt-2">
              <div>
                <p className="text-sm font-medium text-gray-500">Provider</p>
                <p>{formData.insuranceProvider || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Coverage</p>
                <p>{formData.coverageAmount || 'Not provided'}</p>
              </div>
            </div>
          )}
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Photos</h3>
          <div className="grid grid-cols-4 gap-2">
            {formData.images.length > 0 ? (
              formData.images.map((image, index) => (
                <img 
                  key={index} 
                  src={image} 
                  alt={`Venue ${index + 1}`} 
                  className="h-20 w-full object-cover rounded"
                />
              ))
            ) : (
              <p className="col-span-4 text-gray-500">No photos uploaded</p>
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <div className="flex items-center">
          <input
            id="terms-agreement"
            type="checkbox"
            required
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="terms-agreement" className="ml-2 block text-sm text-gray-700">
            I agree to HouseGoing's <a href="/terms" className="text-purple-600 hover:text-purple-500">Terms of Service</a> and <a href="/privacy" className="text-purple-600 hover:text-purple-500">Privacy Policy</a>
          </label>
        </div>
      </div>
    </div>
  );
  
  // Render step navigation
  const renderStepNavigation = () => (
    <div className="mt-8 flex justify-between">
      {formData.currentStep > 0 ? (
        <button
          type="button"
          onClick={prevStep}
          className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
        >
          Previous
        </button>
      ) : (
        <div></div>
      )}
      
      <div className="flex space-x-2">
        <button
          type="button"
          onClick={saveDraft}
          className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
        >
          Save Draft
        </button>
        
        {formData.currentStep < formSteps.length - 1 ? (
          <button
            type="button"
            onClick={nextStep}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Next
          </button>
        ) : (
          <button
            type="submit"
            disabled={submitting}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:bg-purple-300"
          >
            {submitting ? 'Submitting...' : 'Submit Venue'}
          </button>
        )}
      </div>
    </div>
  );
