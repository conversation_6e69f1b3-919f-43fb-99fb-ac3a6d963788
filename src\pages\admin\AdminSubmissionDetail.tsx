/**
 * Admin Submission Detail Page
 *
 * Displays detailed view of property submissions for admin review
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  CheckCircle,
  XCircle,
  ArrowLeft,
  Mail,
  Calendar
} from 'lucide-react';
import AdminSubmissionDetailSections from './AdminSubmissionDetailSections';

// Email service configuration
const EMAIL_SERVICE_URL = 'https://housegoing-email-service.onrender.com';

/**
 * Send approval email to property owner using EmailJS
 */
async function sendApprovalEmail(submission: any): Promise<void> {
  try {
    console.log('🔄 Sending approval email to:', submission.ownerEmail);

    // Create email content
    const emailData = {
      to_email: submission.ownerEmail,
      to_name: submission.ownerName,
      from_name: 'HouseGoing Team',
      from_email: '<EMAIL>',
      subject: `🎉 Your Property "${submission.name}" has been Approved!`,
      property_name: submission.name,
      property_address: submission.address,
      property_type: submission.type,
      max_guests: submission.maxGuests,
      price_per_hour: submission.price,
      approved_date: new Date().toLocaleDateString(),
      dashboard_url: 'https://housegoing.com.au/owner/dashboard',
      manage_url: `https://housegoing.com.au/owner/property/${submission.id}/manage`,
      message: generateApprovalEmailText(submission)
    };

    // Try EmailJS first (requires setup)
    try {
      // EmailJS would go here if configured
      // For now, we'll use a direct email approach
      console.log('📧 EmailJS not configured, using direct email approach');
    } catch (emailJSError) {
      console.warn('EmailJS failed:', emailJSError);
    }

    // Check if we're on localhost
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

    if (isLocalhost) {
      console.log('🏠 Running on localhost - email service may not work due to CORS restrictions');
      console.log('📧 For localhost testing, using fallback email method');
    } else {
      // Try direct email service (only on production)
      try {
        const response = await fetch(`${EMAIL_SERVICE_URL}/send-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: submission.ownerEmail,
            subject: emailData.subject,
            html: generateApprovalEmailHTML(submission),
            from: 'HouseGoing <<EMAIL>>'
          }),
        });

        if (response.ok) {
          console.log('✅ Approval email sent successfully via email service to:', submission.ownerEmail);
          return;
        } else {
          console.warn('⚠️ Email service failed with status:', response.status);
          const errorText = await response.text();
          console.warn('Error details:', errorText);
        }
      } catch (serviceError) {
        console.warn('⚠️ Email service unavailable:', serviceError);
      }
    }

    // Final fallback: Create a detailed log and mailto link
    const mailtoLink = `mailto:${submission.ownerEmail}?subject=${encodeURIComponent(emailData.subject)}&body=${encodeURIComponent(generateApprovalEmailText(submission))}`;

    console.log('📧 Email service unavailable. Manual email required:');
    console.log('📧 To:', submission.ownerEmail);
    console.log('📧 Subject:', emailData.subject);
    console.log('📧 Mailto link:', mailtoLink);

    // Store email for manual sending
    const emailLog = {
      timestamp: new Date().toISOString(),
      type: 'approval',
      to: submission.ownerEmail,
      subject: emailData.subject,
      content: generateApprovalEmailText(submission),
      property: submission.name,
      status: 'pending_manual_send'
    };

    // Save to localStorage for admin reference
    const existingLogs = JSON.parse(localStorage.getItem('pendingEmails') || '[]');
    existingLogs.push(emailLog);
    localStorage.setItem('pendingEmails', JSON.stringify(existingLogs));

    console.log('📧 Email logged for manual sending. Check localStorage "pendingEmails"');

    // Also log the current localStorage state for debugging
    const currentEmails = JSON.parse(localStorage.getItem('pendingEmails') || '[]');
    console.log('📧 Current pending emails count:', currentEmails.length);
    console.log('📧 Latest email:', currentEmails[currentEmails.length - 1]);

  } catch (error) {
    console.error('❌ Failed to send approval email:', error);
    throw error;
  }
}

/**
 * Generate HTML email content for approval
 */
function generateApprovalEmailHTML(submission: any): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Property Approved - HouseGoing</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #ddd; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .property-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Congratulations!</h1>
          <p>Your property has been approved and is now live on HouseGoing!</p>
        </div>

        <div class="content">
          <h2>Hello ${submission.ownerName},</h2>

          <p>Great news! Your property submission has been approved by our admin team and is now live on the HouseGoing platform.</p>

          <div class="property-details">
            <h3>Property Details:</h3>
            <p><strong>Name:</strong> ${submission.name}</p>
            <p><strong>Address:</strong> ${submission.address}</p>
            <p><strong>Type:</strong> ${submission.type}</p>
            <p><strong>Capacity:</strong> Up to ${submission.maxGuests} guests</p>
            <p><strong>Price:</strong> $${submission.price} per hour</p>
            <p><strong>Approved Date:</strong> ${new Date().toLocaleDateString()}</p>
          </div>

          <h3>What's Next?</h3>
          <ul>
            <li>Your property is now visible to customers on HouseGoing</li>
            <li>You can manage your property settings and availability</li>
            <li>Start receiving bookings from party hosts</li>
            <li>Track your earnings and reviews</li>
          </ul>

          <div style="text-align: center; margin: 30px 0;">
            <a href="https://housegoing.com.au/owner/dashboard" class="button">View My Dashboard</a>
            <a href="https://housegoing.com.au/owner/property/${submission.id}/manage" class="button">Manage Property</a>
          </div>

          <p>If you have any questions, please don't hesitate to contact our support team.</p>

          <p>Welcome to the HouseGoing community!</p>

          <p>Best regards,<br>
          The HouseGoing Team<br>
          <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>

        <div class="footer">
          <p>© 2024 HouseGoing. All rights reserved.</p>
          <p>This email was sent to ${submission.ownerEmail}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate plain text email content for approval
 */
function generateApprovalEmailText(submission: any): string {
  return `
🎉 Congratulations! Your Property "${submission.name}" has been Approved!

Hello ${submission.ownerName},

Great news! Your property submission has been approved by our admin team and is now live on the HouseGoing platform.

Property Details:
- Name: ${submission.name}
- Address: ${submission.address}
- Type: ${submission.type}
- Capacity: Up to ${submission.maxGuests} guests
- Price: $${submission.price} per hour
- Approved Date: ${new Date().toLocaleDateString()}

What's Next?
• Your property is now visible to customers on HouseGoing
• You can manage your property settings and availability
• Start receiving bookings from party hosts
• Track your earnings and reviews

Manage your property: https://housegoing.com.au/owner/dashboard

If you have any questions, please contact our support <NAME_EMAIL>.

Welcome to the HouseGoing community!

Best regards,
The HouseGoing Team
  `.trim();
}

/**
 * Send rejection email to property owner
 */
async function sendRejectionEmail(submission: any, rejectionReason: string, adminComments?: string): Promise<void> {
  try {
    console.log('🔄 sendRejectionEmail function started');
    console.log('📧 Sending rejection email to:', submission.ownerEmail);
    console.log('📝 Rejection reason:', rejectionReason);
    console.log('📝 Admin comments:', adminComments);

    // Create email content
    const emailContent = {
      to: submission.ownerEmail,
      subject: `Property Submission Update - "${submission.name}"`,
      html: generateRejectionEmailHTML(submission, rejectionReason, adminComments)
    };
    console.log('✅ Email content created:', emailContent.subject);

    // Check if we're on localhost
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

    if (isLocalhost) {
      console.log('🏠 Running on localhost - email service may not work due to CORS restrictions');
      console.log('📧 For localhost testing, using fallback email method');
    } else {
      // Try to send via email service (only on production)
      try {
        const response = await fetch(`${EMAIL_SERVICE_URL}/send-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: submission.ownerEmail,
            subject: emailContent.subject,
            html: emailContent.html,
            from: 'HouseGoing <<EMAIL>>'
          }),
        });

        if (response.ok) {
          console.log('✅ Rejection email sent successfully via email service to:', submission.ownerEmail);
          return;
        } else {
          console.warn('⚠️ Email service failed with status:', response.status);
          const errorText = await response.text();
          console.warn('Error details:', errorText);
        }
      } catch (serviceError) {
        console.warn('⚠️ Email service unavailable:', serviceError);
      }
    }

    // Fallback: Create a detailed log and mailto link
    const mailtoLink = `mailto:${submission.ownerEmail}?subject=${encodeURIComponent(emailContent.subject)}&body=${encodeURIComponent(generateRejectionEmailText(submission, rejectionReason, adminComments))}`;

    console.log('📧 Email service unavailable. Manual email required:');
    console.log('📧 To:', submission.ownerEmail);
    console.log('📧 Subject:', emailContent.subject);
    console.log('📧 Mailto link:', mailtoLink);

    // Store email for manual sending
    const emailLog = {
      timestamp: new Date().toISOString(),
      type: 'rejection',
      to: submission.ownerEmail,
      subject: emailContent.subject,
      content: generateRejectionEmailText(submission, rejectionReason, adminComments),
      property: submission.name,
      status: 'pending_manual_send'
    };

    // Save to localStorage for admin reference
    const existingLogs = JSON.parse(localStorage.getItem('pendingEmails') || '[]');
    existingLogs.push(emailLog);
    localStorage.setItem('pendingEmails', JSON.stringify(existingLogs));

    console.log('📧 Email logged for manual sending. Check localStorage "pendingEmails"');

    // Also log the current localStorage state for debugging
    const currentEmails = JSON.parse(localStorage.getItem('pendingEmails') || '[]');
    console.log('📧 Current pending emails count:', currentEmails.length);
    console.log('📧 Latest email:', currentEmails[currentEmails.length - 1]);

  } catch (error) {
    console.error('❌ Failed to send rejection email:', error);
    throw error;
  }
}

/**
 * Generate HTML email content for rejection
 */
function generateRejectionEmailHTML(submission: any, rejectionReason: string, adminComments?: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Property Submission Update - HouseGoing</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #ddd; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .feedback-box { background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .property-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📋 Property Submission Update</h1>
          <p>We've reviewed your property submission</p>
        </div>

        <div class="content">
          <h2>Hello ${submission.ownerName},</h2>

          <p>Thank you for submitting your property "${submission.name}" to HouseGoing. After careful review, we need some improvements before we can approve your listing.</p>

          <div class="feedback-box">
            <h3>📝 Admin Feedback:</h3>
            <p><strong>Reason:</strong> ${rejectionReason}</p>
            ${adminComments ? `<p><strong>Additional Notes:</strong> ${adminComments}</p>` : ''}
          </div>

          <div class="property-details">
            <h3>Property Details:</h3>
            <p><strong>Name:</strong> ${submission.name}</p>
            <p><strong>Address:</strong> ${submission.address}</p>
            <p><strong>Type:</strong> ${submission.type}</p>
            <p><strong>Reviewed Date:</strong> ${new Date().toLocaleDateString()}</p>
          </div>

          <h3>What's Next?</h3>
          <ul>
            <li>Review the feedback provided above</li>
            <li>Make the necessary improvements to your listing</li>
            <li>Resubmit your property for review</li>
            <li>Our team will review your updated submission</li>
          </ul>

          <div style="text-align: center; margin: 30px 0;">
            <a href="https://housegoing.com.au/owner/property/${submission.id}/edit" class="button">Edit & Resubmit Property</a>
            <a href="https://housegoing.com.au/owner/dashboard" class="button">View My Dashboard</a>
          </div>

          <p>Don't worry - most properties are approved after addressing the feedback. We're here to help you succeed!</p>

          <p>If you have any questions about the feedback, please don't hesitate to contact our support team.</p>

          <p>Best regards,<br>
          The HouseGoing Team<br>
          <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>

        <div class="footer">
          <p>© 2024 HouseGoing. All rights reserved.</p>
          <p>This email was sent to ${submission.ownerEmail}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate plain text email content for rejection
 */
function generateRejectionEmailText(submission: any, rejectionReason: string, adminComments?: string): string {
  return `
📋 Property Submission Update - "${submission.name}"

Hello ${submission.ownerName},

Thank you for submitting your property "${submission.name}" to HouseGoing. After careful review, we need some improvements before we can approve your listing.

Admin Feedback:
Reason: ${rejectionReason}
${adminComments ? `Additional Notes: ${adminComments}` : ''}

Property Details:
- Name: ${submission.name}
- Address: ${submission.address}
- Type: ${submission.type}
- Reviewed Date: ${new Date().toLocaleDateString()}

What's Next?
• Review the feedback provided above
• Make the necessary improvements to your listing
• Resubmit your property for review
• Our team will review your updated submission

Edit your property: https://housegoing.com.au/owner/property/${submission.id}/edit
View dashboard: https://housegoing.com.au/owner/dashboard

Don't worry - most properties are approved after addressing the feedback. We're here to help you succeed!

If you have any questions about the feedback, please contact our support <NAME_EMAIL>.

Best regards,
The HouseGoing Team
  `.trim();
}

interface PropertySubmission {
  id: string;

  // Basic Information
  name: string;
  address: string;
  type: string;
  location?: [number, number];
  phoneNumber?: string;
  partyAcknowledgment?: boolean;

  // Venue Details
  description: string;
  size?: number; // Size in square metres
  functionRooms?: number; // Number of function rooms
  eventSpaces?: number; // Number of event spaces
  maxGuests: number;
  price: number;

  // Amenities & Features
  amenities?: string[];
  parkingDetails?: string;
  transportDetails?: string;
  nearbyLandmarks?: string;
  byoPolicy?: string;

  // House Rules
  noiseRestrictions?: string;
  endTime?: {
    weekday: string;
    weekend: string;
  };
  decorationsPolicy?: string;
  smokingPolicy?: string;
  petPolicy?: string;
  additionalFees?: string;
  curfew?: {
    weekday: { start: string; end: string; };
    weekend: { start: string; end: string; };
  };
  bassRestriction?: {
    weekday: string;
    weekend: string;
  };
  outdoorCutoff?: {
    weekday: string;
    weekend: string;
  };
  specialCondition?: string;

  // Insurance & Compliance
  hasInsurance?: boolean;
  insuranceProvider?: string;
  policyNumber?: string;
  coverageAmount?: string;
  expiryDate?: string;
  insuranceCertificate?: string;

  // Licenses & Permits
  hasLiquorLicense?: boolean;
  liquorLicenseNumber?: string;
  liquorLicenseExpiry?: string;
  hasFoodPermit?: boolean;
  foodPermitNumber?: string;
  hasEntertainmentLicense?: boolean;
  entertainmentLicenseNumber?: string;
  capacityCertificate?: string;
  fireSafetyCompliance?: boolean;

  // Additional Information
  accessibilityFeatures?: string[];
  cateringOptions?: string;
  equipmentProvided?: string;
  staffAvailable?: boolean;
  setupTime?: number;
  cleanupTime?: number;

  // Photos
  images?: string[];

  // Identity Verification Documents
  identityVerification?: {
    driverLicenseFront?: string;
    driverLicenseBack?: string;
    selfiePhoto?: string;
    proofOfAddress?: string;
    additionalDocuments?: string[];
  };

  // Bank Details
  bankDetails?: {
    accountName: string;
    bsb: string;
    accountNumber: string;
    bankName: string;
  };

  // System fields
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  ownerId: string;
  ownerEmail: string;
  ownerName: string;
  approved_by?: string;
  approved_at?: string;
  rejection_reason?: string;
  admin_notes?: string;
}

export default function AdminSubmissionDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [submission, setSubmission] = useState<PropertySubmission | null>(null);
  const [loading, setLoading] = useState(true);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [adminComments, setAdminComments] = useState('');

  // Mock data for testing
  const mockSubmissions: Record<string, PropertySubmission> = {
    'mock-1': {
      id: 'mock-1',

      // Basic Information
      name: 'Harbour View Rooftop',
      address: '123 Circular Quay, Sydney NSW 2000',
      type: 'rooftop',
      location: [-33.8688, 151.2093],
      phoneNumber: '0412 345 678',
      partyAcknowledgment: true,

      // Venue Details
      description: 'Beautiful rooftop venue with stunning harbour views, perfect for parties and events. Features modern amenities and professional lighting. This space offers an unparalleled experience with panoramic views of Sydney Harbour, making it ideal for memorable celebrations.',
      size: 200,
      functionRooms: 1,
      eventSpaces: 2,
      maxGuests: 50,
      price: 150,

      // Amenities & Features
      amenities: ['Sound System', 'Lighting', 'Bar Area', 'Harbour Views', 'Parking', 'Catering Kitchen', 'WiFi', 'Air Conditioning'],
      parkingDetails: '10 dedicated parking spaces available on-site',
      transportDetails: 'Circular Quay train station 2 minutes walk, multiple bus routes',
      nearbyLandmarks: 'Sydney Opera House, Harbour Bridge, Royal Botanic Gardens',
      byoPolicy: 'BYO alcohol permitted with responsible service',

      // House Rules
      noiseRestrictions: 'Music must be kept at reasonable levels after 10 PM',
      endTime: {
        weekday: '11:00 PM',
        weekend: '12:00 AM'
      },
      decorationsPolicy: 'Decorations allowed, no permanent fixtures',
      smokingPolicy: 'Smoking permitted in designated outdoor areas only',
      petPolicy: 'No pets allowed',
      additionalFees: 'Security deposit: $500, Cleaning fee: $100',
      curfew: {
        weekday: { start: '10:00 PM', end: '11:00 PM' },
        weekend: { start: '11:00 PM', end: '12:00 AM' }
      },
      bassRestriction: {
        weekday: '10:00 PM',
        weekend: '11:00 PM'
      },
      outdoorCutoff: {
        weekday: '10:00 PM',
        weekend: '11:00 PM'
      },
      specialCondition: 'Weather dependent - indoor backup available',

      // Insurance & Compliance
      hasInsurance: true,
      insuranceProvider: 'QBE Insurance',
      policyNumber: 'QBE********9',
      coverageAmount: '$10,000,000',
      expiryDate: '2025-12-31',
      insuranceCertificate: 'certificate-url-1.pdf',

      // Licenses & Permits
      hasLiquorLicense: true,
      liquorLicenseNumber: 'LIQP770016544',
      liquorLicenseExpiry: '2025-06-30',
      hasFoodPermit: true,
      foodPermitNumber: '*********',
      hasEntertainmentLicense: true,
      entertainmentLicenseNumber: 'ENT2024001',
      capacityCertificate: 'capacity-cert-1.pdf',
      fireSafetyCompliance: true,

      // Additional Information
      accessibilityFeatures: ['Wheelchair accessible', 'Accessible bathroom', 'Elevator access'],
      cateringOptions: 'Full catering kitchen available, preferred caterers list provided',
      equipmentProvided: 'Sound system, microphones, basic lighting, tables and chairs',
      staffAvailable: true,
      setupTime: 2,
      cleanupTime: 1,

      // Photos
      images: [
        'https://images.unsplash.com/photo-1566737236500-c8ac43014a67?w=800',
        'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?w=800',
        'https://images.unsplash.com/photo-1571902943202-507ec2618e8f?w=800',
        'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800'
      ],

      // Identity Verification Documents
      identityVerification: {
        driverLicenseFront: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=600&h=400',
        driverLicenseBack: 'https://images.unsplash.com/photo-*************-b95a79798f07?w=600&h=400',
        selfiePhoto: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=400&h=400',
        proofOfAddress: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=600&h=400',
        additionalDocuments: [
          'https://images.unsplash.com/photo-*************-b95a79798f07?w=600&h=400'
        ]
      },

      // Bank Details
      bankDetails: {
        accountName: 'Tom Chen',
        bsb: '062000',
        accountNumber: '********',
        bankName: 'Commonwealth Bank'
      },

      // System fields
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ownerId: 'dev_host_001',
      ownerEmail: '<EMAIL>',
      ownerName: 'Tom Chen'
    },
    'mock-2': {
      id: 'mock-2',

      // Basic Information
      name: 'Industrial Warehouse Space',
      address: '456 Industrial Lane, Marrickville NSW 2204',
      type: 'warehouse',
      location: [-33.9106, 151.1656],
      phoneNumber: '0423 456 789',
      partyAcknowledgment: true,

      // Venue Details
      description: 'Spacious industrial warehouse with exposed brick walls and high ceilings. Perfect for large events and creative parties. The raw industrial aesthetic provides a unique backdrop for any celebration.',
      size: 800,
      functionRooms: 2,
      eventSpaces: 3,
      maxGuests: 200,
      price: 200,

      // Amenities & Features
      amenities: ['Sound System', 'High Ceilings', 'Loading Dock', 'Parking', 'Kitchen', 'Stage Area', 'Security System'],
      parkingDetails: '50 parking spaces in adjacent lot',
      transportDetails: 'Marrickville train station 5 minutes walk',
      nearbyLandmarks: 'Marrickville Metro, Enmore Theatre, Sydney Park',
      byoPolicy: 'BYO alcohol and catering permitted',

      // House Rules
      noiseRestrictions: 'Industrial area - minimal noise restrictions',
      endTime: {
        weekday: '12:00 AM',
        weekend: '2:00 AM'
      },
      decorationsPolicy: 'Full decoration freedom, rigging points available',
      smokingPolicy: 'Smoking permitted in designated areas',
      petPolicy: 'Pets allowed with prior approval',
      additionalFees: 'Security deposit: $1000, Equipment fee: $200',
      curfew: {
        weekday: { start: '11:00 PM', end: '12:00 AM' },
        weekend: { start: '1:00 AM', end: '2:00 AM' }
      },
      bassRestriction: {
        weekday: '12:00 AM',
        weekend: '2:00 AM'
      },
      outdoorCutoff: {
        weekday: '11:00 PM',
        weekend: '1:00 AM'
      },
      specialCondition: 'Loading dock access for equipment',

      // Insurance & Compliance
      hasInsurance: true,
      insuranceProvider: 'Allianz Australia',
      policyNumber: 'ALZ987654321',
      coverageAmount: '$20,000,000',
      expiryDate: '2025-08-15',
      insuranceCertificate: 'certificate-url-2.pdf',

      // Licenses & Permits
      hasLiquorLicense: false,
      hasFoodPermit: false,
      hasEntertainmentLicense: true,
      entertainmentLicenseNumber: 'ENT2024002',
      capacityCertificate: 'capacity-cert-2.pdf',
      fireSafetyCompliance: true,

      // Additional Information
      accessibilityFeatures: ['Ground level access', 'Wide doorways'],
      cateringOptions: 'Basic kitchen facilities, external catering recommended',
      equipmentProvided: 'Professional sound system, stage, lighting rig, security',
      staffAvailable: true,
      setupTime: 4,
      cleanupTime: 2,

      // Photos
      images: [
        'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800',
        'https://images.unsplash.com/photo-1571902943202-507ec2618e8f?w=800'
      ],

      // Identity Verification Documents
      identityVerification: {
        driverLicenseFront: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=600&h=400',
        driverLicenseBack: 'https://images.unsplash.com/photo-*************-b95a79798f07?w=600&h=400',
        selfiePhoto: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=400&h=400',
        proofOfAddress: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=600&h=400'
      },

      // Bank Details
      bankDetails: {
        accountName: 'Tom Chen',
        bsb: '062000',
        accountNumber: '********',
        bankName: 'Commonwealth Bank'
      },

      // System fields
      status: 'pending',
      created_at: new Date(Date.now() - ********).toISOString(),
      updated_at: new Date(Date.now() - ********).toISOString(),
      ownerId: 'dev_host_001',
      ownerEmail: '<EMAIL>',
      ownerName: 'Tom Chen'
    },
    'mock-3': {
      id: 'mock-3',
      name: 'Beachside Garden Villa',
      address: '789 Beach Road, Bondi NSW 2026',
      type: 'Villa',
      description: 'Elegant beachside villa with beautiful gardens and ocean views. Ideal for intimate gatherings and celebrations.',
      maxGuests: 30,
      price: 120,
      status: 'approved',
      created_at: new Date(Date.now() - *********).toISOString(),
      updated_at: new Date(Date.now() - ********).toISOString(),
      ownerId: 'dev_customer_001',
      ownerEmail: '<EMAIL>',
      ownerName: 'Sarah Johnson',
      images: [
        'https://images.unsplash.com/photo-*************-ab600027ffc6?w=800',
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800'
      ],
      amenities: ['Garden', 'Ocean Views', 'Pool', 'BBQ Area', 'Parking'],
      approved_by: '<EMAIL>',
      approved_at: new Date(Date.now() - ********).toISOString(),
      admin_notes: 'Excellent property with great amenities'
    },
    'mock-4': {
      id: 'mock-4',
      name: 'Urban Loft Space',
      address: '321 King Street, Newtown NSW 2042',
      type: 'Loft',
      description: 'Modern urban loft with contemporary design and city views. Perfect for stylish parties and corporate events.',
      maxGuests: 80,
      price: 180,
      status: 'rejected',
      created_at: new Date(Date.now() - 259200000).toISOString(),
      updated_at: new Date(Date.now() - *********).toISOString(),
      ownerId: 'dev_host_001',
      ownerEmail: '<EMAIL>',
      ownerName: 'Tom Chen',
      images: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800'
      ],
      amenities: ['City Views', 'Modern Kitchen', 'Sound System', 'Elevator', 'Parking'],
      approved_by: '<EMAIL>',
      approved_at: new Date(Date.now() - *********).toISOString(),
      rejection_reason: 'Property needs better photos and more detailed description',
      admin_notes: 'Good potential but requires improvements'
    },
    'mock-5': {
      id: 'mock-5',
      name: 'Backyard Pool Paradise',
      address: '654 Suburban Street, Manly NSW 2095',
      type: 'Backyard',
      description: 'Beautiful backyard with swimming pool and entertainment area. Great for pool parties and summer celebrations.',
      maxGuests: 40,
      price: 100,
      status: 'pending',
      created_at: new Date(Date.now() - 345600000).toISOString(),
      updated_at: new Date(Date.now() - 345600000).toISOString(),
      ownerId: 'dev_customer_001',
      ownerEmail: '<EMAIL>',
      ownerName: 'Sarah Johnson',
      images: [
        'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800'
      ],
      amenities: ['Swimming Pool', 'BBQ Area', 'Outdoor Seating', 'Garden', 'Parking']
    }
  };

  useEffect(() => {
    if (id) {
      setLoading(true);
      // Simulate loading delay
      setTimeout(() => {
        const foundSubmission = mockSubmissions[id];
        setSubmission(foundSubmission || null);
        setLoading(false);
      }, 500);
    }
  }, [id]);

  const handleApprove = async () => {
    if (submission) {
      try {
        // Update submission status
        const updatedSubmission = {
          ...submission,
          status: 'approved' as const,
          approved_by: '<EMAIL>',
          approved_at: new Date().toISOString(),
          admin_notes: 'Approved from detail view'
        };
        setSubmission(updatedSubmission);

        // Send approval email to owner
        await sendApprovalEmail(submission);

        // Check if we're on localhost for appropriate message
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        if (isLocalhost) {
          alert('✅ Property approved successfully!\n\n📧 Email notification prepared for ' + submission.ownerEmail + '\n\nNote: On localhost, emails are logged for manual sending. Check browser console for details.');
        } else {
          alert('✅ Property approved successfully! Email notification sent to ' + submission.ownerEmail);
        }
      } catch (error) {
        console.error('Error sending approval email:', error);
        alert('✅ Property approved successfully! (Note: Email notification failed to send)');
      }
    }
  };

  const handleReject = async () => {
    console.log('🔄 handleReject called');
    console.log('📝 Rejection reason:', rejectReason);
    console.log('📝 Admin comments:', adminComments);
    console.log('🏠 Submission:', submission);

    if (submission && rejectReason.trim()) {
      try {
        console.log('✅ Starting rejection process...');

        // Update submission status
        const updatedSubmission = {
          ...submission,
          status: 'rejected' as const,
          approved_by: '<EMAIL>',
          approved_at: new Date().toISOString(),
          rejection_reason: rejectReason,
          admin_notes: adminComments.trim() || 'Rejected from detail view'
        };
        setSubmission(updatedSubmission);
        console.log('✅ Submission status updated');

        // Send rejection email to owner
        console.log('📧 About to send rejection email...');
        await sendRejectionEmail(submission, rejectReason, adminComments.trim());
        console.log('✅ Rejection email function completed');

        setShowRejectModal(false);
        setRejectReason('');
        setAdminComments('');

        // Check if we're on localhost for appropriate message
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        console.log('🏠 Is localhost:', isLocalhost);

        if (isLocalhost) {
          alert('❌ Property rejected successfully!\n\n📧 Email notification prepared for ' + submission.ownerEmail + '\n\nNote: On localhost, emails are logged for manual sending. Check browser console for details.');
        } else {
          alert('❌ Property rejected successfully! Email notification sent to ' + submission.ownerEmail);
        }
      } catch (error) {
        console.error('❌ Error in rejection process:', error);
        setShowRejectModal(false);
        setRejectReason('');
        setAdminComments('');
        alert('❌ Property rejected successfully! (Note: Email notification failed to send - check console for details)');
      }
    } else {
      console.warn('⚠️ Rejection not processed - missing submission or reason');
      console.log('Submission exists:', !!submission);
      console.log('Reason provided:', !!rejectReason.trim());
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            <p className="ml-3 text-gray-600">Loading submission details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Submission Not Found</h2>
            <p className="text-gray-600 mb-6">The property submission you're looking for doesn't exist.</p>
            <button
              onClick={() => navigate('/admin/approval')}
              className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Approval Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate('/admin/approval')}
            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Approval Dashboard
          </button>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{submission.name}</h1>
              <p className="text-gray-600 mt-1">Property Submission Details</p>
              {submission.updated_at && submission.updated_at !== submission.created_at && (
                <p className="text-sm text-blue-600 mt-1">
                  ✨ Resubmitted {new Date(submission.updated_at).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className="flex items-center gap-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(submission.status)}`}>
                {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
              </span>
              {submission.updated_at && submission.updated_at !== submission.created_at && (
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  Resubmitted
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Left Column - All Property Details */}
          <div className="lg:col-span-3">
            <AdminSubmissionDetailSections submission={submission} />
          </div>

          {/* Right Column - Admin Actions and Host Info */}
          <div className="space-y-6">
            {/* Host Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Host Information</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">{submission.ownerName}</p>
                    <p className="text-sm text-gray-600">{submission.ownerEmail}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Submitted</p>
                    <p className="font-medium text-gray-900">
                      {new Date(submission.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Admin Actions */}
            {submission.status === 'pending' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Actions</h3>
                <div className="space-y-3">
                  <button
                    onClick={handleApprove}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                  >
                    <CheckCircle className="w-4 h-4" />
                    Approve Property
                  </button>

                  <button
                    onClick={() => setShowRejectModal(true)}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
                  >
                    <XCircle className="w-4 h-4" />
                    Reject Property
                  </button>
                </div>
              </div>
            )}

            {/* Status Information */}
            {submission.status !== 'pending' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Information</h3>
                <div className="space-y-3">
                  {submission.approved_by && (
                    <div>
                      <p className="text-sm text-gray-500">Reviewed by</p>
                      <p className="font-medium text-gray-900">{submission.approved_by}</p>
                    </div>
                  )}

                  {submission.approved_at && (
                    <div>
                      <p className="text-sm text-gray-500">Date</p>
                      <p className="font-medium text-gray-900">
                        {new Date(submission.approved_at).toLocaleDateString()}
                      </p>
                    </div>
                  )}

                  {submission.rejection_reason && (
                    <div>
                      <p className="text-sm text-gray-500">Rejection Reason</p>
                      <p className="font-medium text-red-700">{submission.rejection_reason}</p>
                    </div>
                  )}

                  {submission.admin_notes && (
                    <div>
                      <p className="text-sm text-gray-500">Admin Notes</p>
                      <p className="font-medium text-gray-900">{submission.admin_notes}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Reject Property Submission</h3>
              <p className="text-gray-600 mb-4">Please provide detailed feedback for the property owner:</p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rejection Reason <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={rejectReason}
                    onChange={(e) => setRejectReason(e.target.value)}
                    placeholder="e.g., Property photos are unclear, missing required documents, etc."
                    className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Comments (Optional)
                  </label>
                  <textarea
                    value={adminComments}
                    onChange={(e) => setAdminComments(e.target.value)}
                    placeholder="Additional feedback or suggestions for improvement..."
                    className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    These comments will be included in the email to help the host improve their submission.
                  </p>
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleReject}
                  disabled={!rejectReason.trim()}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg font-medium transition-colors"
                >
                  Send Rejection Email
                </button>
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectReason('');
                    setAdminComments('');
                  }}
                  className="flex-1 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
