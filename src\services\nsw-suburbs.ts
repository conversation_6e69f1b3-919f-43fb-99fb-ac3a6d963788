// File: src/services/nsw-suburbs.ts
export interface NSWSuburb {
  name: string;
  postcode: string;
  state: string;
  latitude: number;
  longitude: number;
}

// Comprehensive NSW suburbs database with major suburbs and towns
const NSW_SUBURBS_DATABASE: NSWSuburb[] = [
  // Sydney CBD and Inner City
  { name: 'Sydney', postcode: '2000', state: 'NSW', latitude: -33.8688, longitude: 151.2093 },
  { name: 'Circular Quay', postcode: '2000', state: 'NSW', latitude: -33.8616, longitude: 151.2111 },
  { name: 'The Rocks', postcode: '2000', state: 'NSW', latitude: -33.8587, longitude: 151.2089 },
  { name: 'Haymarket', postcode: '2000', state: 'NSW', latitude: -33.8796, longitude: 151.2023 },
  { name: 'Darlinghurst', postcode: '2010', state: 'NSW', latitude: -33.8792, longitude: 151.2197 },
  { name: 'Potts Point', postcode: '2011', state: 'NSW', latitude: -33.8697, longitude: 151.2274 },
  { name: 'Woolloomooloo', postcode: '2011', state: 'NSW', latitude: -33.8708, longitude: 151.2194 },
  { name: 'Surry Hills', postcode: '2010', state: 'NSW', latitude: -33.8886, longitude: 151.2094 },
  { name: 'Redfern', postcode: '2016', state: 'NSW', latitude: -33.8934, longitude: 151.2044 },
  { name: 'Chippendale', postcode: '2008', state: 'NSW', latitude: -33.8878, longitude: 151.1989 },
  { name: 'Ultimo', postcode: '2007', state: 'NSW', latitude: -33.8785, longitude: 151.1982 },
  { name: 'Pyrmont', postcode: '2009', state: 'NSW', latitude: -33.8689, longitude: 151.1958 },
  { name: 'Glebe', postcode: '2037', state: 'NSW', latitude: -33.8814, longitude: 151.1822 },
  { name: 'Newtown', postcode: '2042', state: 'NSW', latitude: -33.8978, longitude: 151.1795 },
  { name: 'Enmore', postcode: '2042', state: 'NSW', latitude: -33.8989, longitude: 151.1722 },
  { name: 'Marrickville', postcode: '2204', state: 'NSW', latitude: -33.9111, longitude: 151.1556 },
  { name: 'Dulwich Hill', postcode: '2203', state: 'NSW', latitude: -33.9056, longitude: 151.1394 },
  { name: 'Leichhardt', postcode: '2040', state: 'NSW', latitude: -33.8833, longitude: 151.1569 },
  { name: 'Balmain', postcode: '2041', state: 'NSW', latitude: -33.8611, longitude: 151.1806 },
  { name: 'Rozelle', postcode: '2039', state: 'NSW', latitude: -33.8611, longitude: 151.1711 },

  // Eastern Suburbs
  { name: 'Bondi Beach', postcode: '2026', state: 'NSW', latitude: -33.8915, longitude: 151.2767 },
  { name: 'Bondi Junction', postcode: '2022', state: 'NSW', latitude: -33.8947, longitude: 151.2472 },
  { name: 'Coogee', postcode: '2034', state: 'NSW', latitude: -33.9208, longitude: 151.2556 },
  { name: 'Maroubra', postcode: '2035', state: 'NSW', latitude: -33.9506, longitude: 151.2394 },
  { name: 'Randwick', postcode: '2031', state: 'NSW', latitude: -33.9159, longitude: 151.2415 },
  { name: 'Kensington', postcode: '2033', state: 'NSW', latitude: -33.9122, longitude: 151.2236 },
  { name: 'Kingsford', postcode: '2032', state: 'NSW', latitude: -33.9239, longitude: 151.2331 },
  { name: 'Paddington', postcode: '2021', state: 'NSW', latitude: -33.8847, longitude: 151.2306 },
  { name: 'Woollahra', postcode: '2025', state: 'NSW', latitude: -33.8847, longitude: 151.2444 },
  { name: 'Double Bay', postcode: '2028', state: 'NSW', latitude: -33.8778, longitude: 151.2444 },
  { name: 'Rose Bay', postcode: '2029', state: 'NSW', latitude: -33.8689, longitude: 151.2722 },
  { name: 'Vaucluse', postcode: '2030', state: 'NSW', latitude: -33.8597, longitude: 151.2778 },
  { name: 'Watsons Bay', postcode: '2030', state: 'NSW', latitude: -33.8431, longitude: 151.2806 },

  // Northern Beaches
  { name: 'Manly', postcode: '2095', state: 'NSW', latitude: -33.7969, longitude: 151.2502 },
  { name: 'Dee Why', postcode: '2099', state: 'NSW', latitude: -33.7531, longitude: 151.2889 },
  { name: 'Brookvale', postcode: '2100', state: 'NSW', latitude: -33.7647, longitude: 151.2719 },
  { name: 'Freshwater', postcode: '2096', state: 'NSW', latitude: -33.7806, longitude: 151.2889 },
  { name: 'Curl Curl', postcode: '2096', state: 'NSW', latitude: -33.7689, longitude: 151.2944 },
  { name: 'Narrabeen', postcode: '2101', state: 'NSW', latitude: -33.7194, longitude: 151.3000 },
  { name: 'Mona Vale', postcode: '2103', state: 'NSW', latitude: -33.6778, longitude: 151.3056 },
  { name: 'Newport', postcode: '2106', state: 'NSW', latitude: -33.6581, longitude: 151.3139 },
  { name: 'Avalon Beach', postcode: '2107', state: 'NSW', latitude: -33.6356, longitude: 151.3222 },
  { name: 'Palm Beach', postcode: '2108', state: 'NSW', latitude: -33.5944, longitude: 151.3222 },

  // North Shore
  { name: 'North Sydney', postcode: '2060', state: 'NSW', latitude: -33.8389, longitude: 151.2071 },
  { name: 'Neutral Bay', postcode: '2089', state: 'NSW', latitude: -33.8306, longitude: 151.2194 },
  { name: 'Mosman', postcode: '2088', state: 'NSW', latitude: -33.8281, longitude: 151.2444 },
  { name: 'Cremorne', postcode: '2090', state: 'NSW', latitude: -33.8278, longitude: 151.2278 },
  { name: 'Cammeray', postcode: '2062', state: 'NSW', latitude: -33.8194, longitude: 151.2111 },
  { name: 'Crows Nest', postcode: '2065', state: 'NSW', latitude: -33.8256, longitude: 151.2028 },
  { name: 'St Leonards', postcode: '2065', state: 'NSW', latitude: -33.8222, longitude: 151.1944 },
  { name: 'Chatswood', postcode: '2067', state: 'NSW', latitude: -33.7967, longitude: 151.1800 },
  { name: 'Willoughby', postcode: '2068', state: 'NSW', latitude: -33.8056, longitude: 151.1972 },
  { name: 'Artarmon', postcode: '2064', state: 'NSW', latitude: -33.8111, longitude: 151.1889 },
  { name: 'Lane Cove', postcode: '2066', state: 'NSW', latitude: -33.8167, longitude: 151.1667 },
  { name: 'Roseville', postcode: '2069', state: 'NSW', latitude: -33.7833, longitude: 151.1833 },
  { name: 'Lindfield', postcode: '2070', state: 'NSW', latitude: -33.7778, longitude: 151.1667 },
  { name: 'Killara', postcode: '2071', state: 'NSW', latitude: -33.7667, longitude: 151.1667 },
  { name: 'Gordon', postcode: '2072', state: 'NSW', latitude: -33.7556, longitude: 151.1500 },
  { name: 'Pymble', postcode: '2073', state: 'NSW', latitude: -33.7444, longitude: 151.1389 },
  { name: 'Turramurra', postcode: '2074', state: 'NSW', latitude: -33.7333, longitude: 151.1278 },
  { name: 'Wahroonga', postcode: '2076', state: 'NSW', latitude: -33.7167, longitude: 151.1167 },
  { name: 'Hornsby', postcode: '2077', state: 'NSW', latitude: -33.7047, longitude: 151.0983 },

  // Inner West
  { name: 'Ashfield', postcode: '2131', state: 'NSW', latitude: -33.8889, longitude: 151.1264 },
  { name: 'Burwood', postcode: '2134', state: 'NSW', latitude: -33.8772, longitude: 151.1044 },
  { name: 'Strathfield', postcode: '2135', state: 'NSW', latitude: -33.8708, longitude: 151.0944 },
  { name: 'Homebush', postcode: '2140', state: 'NSW', latitude: -33.8667, longitude: 151.0833 },
  { name: 'Rhodes', postcode: '2138', state: 'NSW', latitude: -33.8306, longitude: 151.0889 },
  { name: 'Concord', postcode: '2137', state: 'NSW', latitude: -33.8556, longitude: 151.1000 },
  { name: 'Drummoyne', postcode: '2047', state: 'NSW', latitude: -33.8500, longitude: 151.1556 },
  { name: 'Five Dock', postcode: '2046', state: 'NSW', latitude: -33.8667, longitude: 151.1333 },
  { name: 'Canada Bay', postcode: '2046', state: 'NSW', latitude: -33.8556, longitude: 151.1222 },
  { name: 'Ryde', postcode: '2112', state: 'NSW', latitude: -33.8149, longitude: 151.1056 },
  { name: 'Meadowbank', postcode: '2114', state: 'NSW', latitude: -33.8167, longitude: 151.0889 },
  { name: 'West Ryde', postcode: '2114', state: 'NSW', latitude: -33.8056, longitude: 151.0889 },
  { name: 'Eastwood', postcode: '2122', state: 'NSW', latitude: -33.7889, longitude: 151.0833 },
  { name: 'Epping', postcode: '2121', state: 'NSW', latitude: -33.7722, longitude: 151.0833 },
  { name: 'Carlingford', postcode: '2118', state: 'NSW', latitude: -33.7833, longitude: 151.0500 },

  // Southern Suburbs
  { name: 'Hurstville', postcode: '2220', state: 'NSW', latitude: -33.9676, longitude: 151.1030 },
  { name: 'Kogarah', postcode: '2217', state: 'NSW', latitude: -33.9639, longitude: 151.1333 },
  { name: 'Rockdale', postcode: '2216', state: 'NSW', latitude: -33.9528, longitude: 151.1389 },
  { name: 'Brighton-Le-Sands', postcode: '2216', state: 'NSW', latitude: -33.9583, longitude: 151.1556 },
  { name: 'Cronulla', postcode: '2230', state: 'NSW', latitude: -34.0581, longitude: 151.1543 },
  { name: 'Sutherland', postcode: '2232', state: 'NSW', latitude: -34.0311, longitude: 151.0569 },
  { name: 'Miranda', postcode: '2228', state: 'NSW', latitude: -34.0347, longitude: 151.1000 },
  { name: 'Caringbah', postcode: '2229', state: 'NSW', latitude: -34.0417, longitude: 151.1222 },
  { name: 'Engadine', postcode: '2233', state: 'NSW', latitude: -34.0667, longitude: 151.0167 },

  // Western Sydney
  { name: 'Parramatta', postcode: '2150', state: 'NSW', latitude: -33.8150, longitude: 151.0011 },
  { name: 'Westmead', postcode: '2145', state: 'NSW', latitude: -33.8056, longitude: 150.9889 },
  { name: 'Harris Park', postcode: '2150', state: 'NSW', latitude: -33.8222, longitude: 151.0056 },
  { name: 'Granville', postcode: '2142', state: 'NSW', latitude: -33.8333, longitude: 151.0167 },
  { name: 'Auburn', postcode: '2144', state: 'NSW', latitude: -33.8489, longitude: 151.0322 },
  { name: 'Lidcombe', postcode: '2141', state: 'NSW', latitude: -33.8667, longitude: 151.0444 },
  { name: 'Bankstown', postcode: '2200', state: 'NSW', latitude: -33.9181, longitude: 151.0350 },
  { name: 'Liverpool', postcode: '2170', state: 'NSW', latitude: -33.9249, longitude: 150.9136 },
  { name: 'Fairfield', postcode: '2165', state: 'NSW', latitude: -33.8697, longitude: 150.9539 },
  { name: 'Cabramatta', postcode: '2166', state: 'NSW', latitude: -33.8944, longitude: 150.9361 },
  { name: 'Blacktown', postcode: '2148', state: 'NSW', latitude: -33.7688, longitude: 150.9062 },
  { name: 'Mount Druitt', postcode: '2770', state: 'NSW', latitude: -33.7667, longitude: 150.8167 },
  { name: 'Penrith', postcode: '2750', state: 'NSW', latitude: -33.7506, longitude: 150.6944 },
  { name: 'St Marys', postcode: '2760', state: 'NSW', latitude: -33.7667, longitude: 150.7833 },
  { name: 'Campbelltown', postcode: '2560', state: 'NSW', latitude: -34.0639, longitude: 150.8150 },
  { name: 'Camden', postcode: '2570', state: 'NSW', latitude: -34.0556, longitude: 150.6944 },

  // Hills District
  { name: 'Castle Hill', postcode: '2154', state: 'NSW', latitude: -33.7333, longitude: 151.0000 },
  { name: 'Baulkham Hills', postcode: '2153', state: 'NSW', latitude: -33.7608, longitude: 150.9889 },
  { name: 'Kellyville', postcode: '2155', state: 'NSW', latitude: -33.7167, longitude: 150.9500 },
  { name: 'Bella Vista', postcode: '2153', state: 'NSW', latitude: -33.7333, longitude: 150.9500 },
  { name: 'Norwest', postcode: '2153', state: 'NSW', latitude: -33.7333, longitude: 150.9667 },
  { name: 'Rouse Hill', postcode: '2155', state: 'NSW', latitude: -33.6833, longitude: 150.9167 },

  // Major Regional Cities
  { name: 'Newcastle', postcode: '2300', state: 'NSW', latitude: -32.9267, longitude: 151.7789 },
  { name: 'Wollongong', postcode: '2500', state: 'NSW', latitude: -34.4278, longitude: 150.8931 },
  { name: 'Central Coast', postcode: '2250', state: 'NSW', latitude: -33.4269, longitude: 151.3428 },
  { name: 'Gosford', postcode: '2250', state: 'NSW', latitude: -33.4269, longitude: 151.3428 },
  { name: 'Wyong', postcode: '2259', state: 'NSW', latitude: -33.2833, longitude: 151.4167 },
  { name: 'Blue Mountains', postcode: '2780', state: 'NSW', latitude: -33.7122, longitude: 150.3111 },
  { name: 'Katoomba', postcode: '2780', state: 'NSW', latitude: -33.7122, longitude: 150.3111 },
  { name: 'Leura', postcode: '2780', state: 'NSW', latitude: -33.7167, longitude: 150.3333 },
  { name: 'Springwood', postcode: '2777', state: 'NSW', latitude: -33.6944, longitude: 150.5667 },
  { name: 'Byron Bay', postcode: '2481', state: 'NSW', latitude: -28.6474, longitude: 153.6020 },
  { name: 'Ballina', postcode: '2478', state: 'NSW', latitude: -28.8667, longitude: 153.5667 },
  { name: 'Lismore', postcode: '2480', state: 'NSW', latitude: -28.8167, longitude: 153.2833 },
  { name: 'Tweed Heads', postcode: '2485', state: 'NSW', latitude: -28.1833, longitude: 153.5500 },
  { name: 'Port Macquarie', postcode: '2444', state: 'NSW', latitude: -31.4333, longitude: 152.9000 },
  { name: 'Coffs Harbour', postcode: '2450', state: 'NSW', latitude: -30.3000, longitude: 153.1167 },
  { name: 'Tamworth', postcode: '2340', state: 'NSW', latitude: -31.0833, longitude: 150.9167 },
  { name: 'Armidale', postcode: '2350', state: 'NSW', latitude: -30.5167, longitude: 151.6667 },
  { name: 'Orange', postcode: '2800', state: 'NSW', latitude: -33.2833, longitude: 149.1000 },
  { name: 'Bathurst', postcode: '2795', state: 'NSW', latitude: -33.4167, longitude: 149.5833 },
  { name: 'Dubbo', postcode: '2830', state: 'NSW', latitude: -32.2500, longitude: 148.6167 },
  { name: 'Wagga Wagga', postcode: '2650', state: 'NSW', latitude: -35.1167, longitude: 147.3667 },
  { name: 'Albury', postcode: '2640', state: 'NSW', latitude: -36.0833, longitude: 146.9167 }
];

export const searchNSWSuburbs = async (query: string): Promise<NSWSuburb[]> => {
  if (!query || query.length < 1) {
    return [];
  }

  try {
    // Try API endpoint first (running on port 3001)
    const response = await fetch(`http://localhost:3001/api/suburbs/search?q=${encodeURIComponent(query)}`);
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ NSW Suburbs API: Found ${data.length} results for "${query}"`);
      return data;
    }
  } catch (error) {
    console.warn('NSW Suburbs API failed, using local database:', error);
  }

  // Fallback to local search
  return searchLocalSuburbs(query);
};

// Local search function with comprehensive matching
function searchLocalSuburbs(query: string): NSWSuburb[] {
  const lowerQuery = query.toLowerCase().trim();

  // Exact matches first
  const exactMatches = NSW_SUBURBS_DATABASE.filter(suburb =>
    suburb.name.toLowerCase() === lowerQuery ||
    suburb.postcode === query
  );

  // Starts with matches
  const startsWithMatches = NSW_SUBURBS_DATABASE.filter(suburb =>
    suburb.name.toLowerCase().startsWith(lowerQuery) &&
    !exactMatches.some(exact => exact.name === suburb.name && exact.postcode === suburb.postcode)
  );

  // Contains matches
  const containsMatches = NSW_SUBURBS_DATABASE.filter(suburb =>
    suburb.name.toLowerCase().includes(lowerQuery) &&
    !exactMatches.some(exact => exact.name === suburb.name && exact.postcode === suburb.postcode) &&
    !startsWithMatches.some(starts => starts.name === suburb.name && starts.postcode === suburb.postcode)
  );

  // Combine results in order of relevance and limit to 10
  const allResults = [...exactMatches, ...startsWithMatches, ...containsMatches];
  return allResults.slice(0, 10);
}


