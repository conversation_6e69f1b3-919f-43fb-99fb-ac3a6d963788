import React, { useEffect, useState } from 'react';
import { supabase } from '../../lib/supabase-client';

interface SupabaseAuthOverlayProps {
  children: React.ReactNode;
}

/**
 * This component creates an overlay for the Supabase authentication UI
 * to hide the Supabase branding and URL.
 */
export default function SupabaseAuthOverlay({ children }: SupabaseAuthOverlayProps) {
  const [showOverlay, setShowOverlay] = useState(false);

  // Listen for auth state changes to detect when Supabase auth UI is shown
  useEffect(() => {
    // Check if we're in an auth flow
    const isAuthPage = window.location.pathname.includes('/login') ||
                       window.location.pathname.includes('/signup') ||
                       window.location.pathname.includes('/auth/');

    if (isAuthPage) {
      // Set up a mutation observer to detect when Supabase injects its UI
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            // Check if any added nodes contain Supabase branding
            const addedNodes = Array.from(mutation.addedNodes);
            for (const node of addedNodes) {
              if (node instanceof HTMLElement) {
                // Look for elements with Supabase URL text
                if (node.innerText && node.innerText.includes('supabase.co')) {
                  setShowOverlay(true);
                  break;
                }

                // Also check for iframes that might be from Supabase
                const iframes = node.querySelectorAll('iframe');
                if (iframes.length > 0) {
                  setShowOverlay(true);
                  break;
                }
              }
            }
          }
        }
      });

      // Start observing the document body for changes
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return () => {
        observer.disconnect();
      };
    }
  }, []);

  // If we detect Supabase UI, show our overlay
  if (showOverlay) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex flex-col items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="text-center mb-8">
            <img
              src="/images/housegoing-logo.svg"
              alt="HouseGoing"
              className="h-12 mx-auto mb-4"
            />
            <h2 className="text-2xl font-bold text-gray-900">
              Authentication in Progress
            </h2>
            <p className="text-gray-600 mt-2">
              Please wait while we securely sign you in...
            </p>
          </div>

          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        </div>
      </div>
    );
  }

  // Otherwise, render children normally
  return <>{children}</>;
}
