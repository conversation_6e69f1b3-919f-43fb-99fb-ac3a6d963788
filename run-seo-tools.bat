@echo off
echo ===== HouseGoing SEO Fix Tools =====
echo.

echo Step 1: Installing required packages...
cd scripts
call npm install
cd ..
echo Dependencies installed.
echo.

echo Step 2: Running SEO analysis and fix scripts...
echo.
echo 2.1: Fixing canonical tag issues...
node scripts/canonical-fix.js
echo.
echo 2.2: Identifying soft 404 pages...
node scripts/soft-404-fixer.js
echo.
echo 2.3: Analyzing redirect chains...
node scripts/redirect-chain-analyzer.js
echo.

echo ===== Analysis Complete =====
echo.
echo Three reports have been generated:
echo - canonical-fix-report.md
echo - soft-404-fix-report.md
echo - redirect-chain-report.md
echo.
echo Please review these reports and implement the suggested changes.
echo.
echo Next Steps:
echo 1. Fix identified issues
echo 2. Build your site with: npm run build:prod
echo 3. Deploy the updated site
echo 4. Submit your sitemap in Google Search Console
echo.
echo Press any key to exit...
pause > nul
