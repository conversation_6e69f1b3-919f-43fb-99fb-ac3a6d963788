/**
 * Test script for NSW Party Planning Tool
 * 
 * This script tests the zoning and council detection for 408-410 King Street Newtown NSW 2042
 */

// The address to test
const testAddress = '408-410 King Street Newtown NSW 2042';

// Step 1: Extract LGA from address
function extractLGAFromAddress(address) {
  // Common LGA patterns in addresses
  const lgaPatterns = [
    /City of ([A-Za-z\s]+)(?:,|$)/i,       // City of Sydney
    /([A-Za-z\s]+) Council(?:,|$)/i,       // Randwick Council
    /([A-Za-z\s]+) Shire(?:,|$)/i,         // Sutherland Shire
    /([A-Za-z\s]+) Municipal(?:,|$)/i      // Woollahra Municipal
  ];

  // Check for specific suburbs and their corresponding LGAs
  const suburbToLGA = {
    'marrickville': 'Inner West Council',
    'newtown': 'Inner West Council',
    'leichhardt': 'Inner West Council',
    'sydney': 'City of Sydney',
    'surry hills': 'City of Sydney',
    'bondi': 'Waverley Council',
    'randwick': 'Randwick City Council'
  };

  // First try to extract LGA directly from the address
  for (const pattern of lgaPatterns) {
    const match = address.match(pattern);
    if (match && match[1]) {
      if (pattern.toString().includes('City of')) {
        return `City of ${match[1].trim()}`;
      } else if (pattern.toString().includes('Council')) {
        return `${match[1].trim()} Council`;
      } else if (pattern.toString().includes('Shire')) {
        return `${match[1].trim()} Shire`;
      } else if (pattern.toString().includes('Municipal')) {
        return `${match[1].trim()} Municipal Council`;
      }
    }
  }

  // If no direct LGA found, try to match by suburb
  const lowerAddress = address.toLowerCase();
  for (const [suburb, lga] of Object.entries(suburbToLGA)) {
    if (lowerAddress.includes(suburb)) {
      return lga;
    }
  }

  return null;
}

const extractedLGA = extractLGAFromAddress(testAddress);
console.log('Extracted LGA:', extractedLGA);

// Step 2: Geocode the address using Mapbox API
async function geocodeAddressMapbox(address) {
  try {
    const mapboxToken = 'pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA';
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=${mapboxToken}&country=AU&limit=1`
    );
    
    if (response.ok) {
      const data = await response.json();
      if (data && data.features && data.features.length > 0) {
        const feature = data.features[0];
        const coords = {
          lat: feature.center[1],
          lng: feature.center[0]
        };
        console.log('Mapbox Geocoded coordinates:', coords);
        console.log('Mapbox Display name:', feature.place_name);
        
        // Extract additional information from the response
        if (feature.context) {
          console.log('Mapbox Address details:');
          feature.context.forEach(ctx => {
            console.log(`  ${ctx.id.split('.')[0]}: ${ctx.text}`);
          });
        }
        
        return coords;
      }
    }
    return null;
  } catch (error) {
    console.error('Mapbox Geocoding error:', error);
    return null;
  }
}

// Step 3: Query NSW Planning Portal for zoning information
async function getZoningInfo(lat, lng) {
  try {
    // NSW Planning Portal API endpoint for zoning
    const url = `https://maps.six.nsw.gov.au/arcgis/rest/services/public/PlanningInformation/MapServer/10/query`;
    
    // Parameters for the API request
    const params = new URLSearchParams({
      geometry: `${lng},${lat}`, // Note: longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: 'ZONE_CODE,ZONE_NAME',
      f: 'json'
    });
    
    const response = await fetch(`${url}?${params.toString()}`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        const zoning = {
          code: data.features[0].attributes.ZONE_CODE,
          name: data.features[0].attributes.ZONE_NAME
        };
        console.log('Zoning information:', zoning);
        return zoning;
      } else {
        console.log('No zoning information found');
      }
    } else {
      console.error('Error fetching zoning information:', await response.text());
    }
    return null;
  } catch (error) {
    console.error('Error getting zoning info:', error);
    return null;
  }
}

// Step 4: Query NSW Spatial Services for LGA information
async function getLGAInfo(lat, lng) {
  try {
    // NSW Spatial Services API endpoint for LGA
    const url = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
    
    // Parameters for the API request
    const params = new URLSearchParams({
      geometry: `${lng},${lat}`, // Note: longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: 'NAME',
      f: 'json'
    });
    
    const response = await fetch(`${url}?${params.toString()}`);
    
    if (response.ok) {
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        const lga = data.features[0].attributes.NAME;
        console.log('LGA information:', lga);
        return lga;
      } else {
        console.log('No LGA information found');
      }
    } else {
      console.error('Error fetching LGA information:', await response.text());
    }
    return null;
  } catch (error) {
    console.error('Error getting LGA info:', error);
    return null;
  }
}

// Run the test
async function runTest() {
  console.log('Testing address:', testAddress);
  
  // Get coordinates from Mapbox
  const coords = await geocodeAddressMapbox(testAddress);
  
  if (coords) {
    // Get zoning information
    const zoning = await getZoningInfo(coords.lat, coords.lng);
    
    // Get LGA information
    const lga = await getLGAInfo(coords.lat, coords.lng);
    
    // Print final results
    console.log('\nFinal results:');
    console.log('Address:', testAddress);
    console.log('Coordinates:', coords);
    console.log('LGA:', lga || extractedLGA || 'Unknown');
    console.log('Zoning:', zoning ? `${zoning.code} - ${zoning.name}` : 'Unknown');
  }
}

// Add fetch polyfill for Node.js
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

runTest();
