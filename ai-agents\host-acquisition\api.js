/**
 * API integration for the Host Acquisition Agent
 * Provides endpoints for interacting with the agent from the main application
 */

import express from 'express';
import cors from 'cors';
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { BufferMemory } from "langchain/memory";
import { initializeAgentExecutorWithOptions } from "langchain/agents";
import dotenv from 'dotenv';
import { hostTools } from './tools.js';
import { hostAuthMiddleware } from './auth.js';

// Load environment variables
dotenv.config();

// Configure LangSmith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;

// Initialize Express app
const app = express();
app.use(cors());
app.use(express.json());

// Store agent instances for each user
const agentInstances = new Map();

// Initialize the Hugging Face model
const model = new HuggingFaceInference({
  model: "mistralai/Mistral-7B-Instruct-v0.3", // Updated to the latest version
  apiKey: process.env.HUGGINGFACE_API_KEY,
  temperature: 0.7,
  maxTokens: 1024,
});

/**
 * Create or get an agent instance for a user
 * @param {string} userId - User ID to create agent for
 * @returns {Promise<Object>} Agent executor
 */
async function getAgentForUser(userId) {
  // Check if agent already exists for this user
  if (agentInstances.has(userId)) {
    return agentInstances.get(userId);
  }

  // Create a new agent
  const memory = new BufferMemory({
    memoryKey: "chat_history",
    returnMessages: true,
  });

  const executor = await initializeAgentExecutorWithOptions(hostTools, model, {
    agentType: "structured-chat-zero-shot-react-description",
    verbose: true,
    memory: memory,
  });

  // Store the agent
  agentInstances.set(userId, executor);

  return executor;
}

// Apply authentication middleware to all routes
app.use(hostAuthMiddleware);

/**
 * Chat endpoint - Send a message to the agent
 */
app.post('/api/host-agent/chat', async (req, res) => {
  try {
    const { message } = req.body;
    const userId = req.user.id;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Get or create agent for this user
    const agent = await getAgentForUser(userId);

    // Process the message
    console.log(`Processing message from user ${userId}: ${message}`);
    const response = await agent.call({
      input: message,
      // Include user context
      userId: userId,
      userName: req.user.name,
      userRole: req.user.role
    });

    res.json({
      response: response.output,
      userId: userId
    });
  } catch (error) {
    console.error("Error processing message:", error);
    res.status(500).json({
      error: "Failed to process message",
      details: error.message
    });
  }
});

/**
 * Reset conversation endpoint - Clear conversation history
 */
app.post('/api/host-agent/reset', async (req, res) => {
  try {
    const userId = req.user.id;

    // Remove the agent instance to reset conversation
    if (agentInstances.has(userId)) {
      agentInstances.delete(userId);
    }

    res.json({
      success: true,
      message: "Conversation reset successfully"
    });
  } catch (error) {
    console.error("Error resetting conversation:", error);
    res.status(500).json({
      error: "Failed to reset conversation",
      details: error.message
    });
  }
});

/**
 * Direct tool access endpoint - Call a specific tool directly
 */
app.post('/api/host-agent/tools/:toolName', async (req, res) => {
  try {
    const { toolName } = req.params;
    const { input } = req.body;
    const userId = req.user.id;

    if (!input) {
      return res.status(400).json({ error: "Input is required" });
    }

    // Find the requested tool
    const tool = hostTools.find(t => t.name.toLowerCase() === toolName.toLowerCase());

    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    // Call the tool directly
    console.log(`User ${userId} calling tool ${toolName} directly`);
    const result = await tool.func(input);

    res.json({
      result: result,
      toolName: toolName,
      userId: userId
    });
  } catch (error) {
    console.error(`Error calling tool ${req.params.toolName}:`, error);
    res.status(500).json({
      error: `Failed to call tool ${req.params.toolName}`,
      details: error.message
    });
  }
});

/**
 * Health check endpoint
 */
app.get('/api/host-agent/health', (req, res) => {
  res.json({
    status: 'ok',
    version: '1.0.0',
    user: req.user
  });
});

// Start the server
const PORT = process.env.PORT || 3001;

export function startServer() {
  app.listen(PORT, () => {
    console.log(`Host Acquisition Agent API running on port ${PORT}`);
  });
}

export default {
  app,
  startServer
};
