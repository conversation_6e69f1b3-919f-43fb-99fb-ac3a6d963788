import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Calendar, Clock, Users, DollarSign, MapPin, ArrowLeft, CheckCircle } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import BookingPayment from '../components/booking/BookingPayment';
import { createBooking } from '../api/bookings';
// Import email service
import { sendBookingConfirmationEmail, sendHostNotificationEmail } from '../services/email';

export default function BookingReview() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [bookingData, setBookingData] = useState<any>(null);
  const [venue, setVenue] = useState<any>(null);
  const [bookingId, setBookingId] = useState<string | null>(null);
  const [step, setStep] = useState<'review' | 'payment' | 'success'>('review');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!user) {
      navigate('/login?redirect=' + encodeURIComponent(window.location.pathname));
      return;
    }

    // Get booking data from location state
    const state = location.state as any;
    if (!state || !state.bookingData || !state.venue) {
      navigate('/');
      return;
    }

    setBookingData(state.bookingData);
    setVenue(state.venue);
  }, [location, navigate, user]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Handle booking confirmation
  const handleConfirmBooking = async () => {
    if (!user || !bookingData || !venue) return;

    setLoading(true);
    setError(null);

    try {
      // Create the booking in the database
      const booking = await createBooking(bookingData, user.id, venue.id);

      // Store the booking ID for the payment step
      setBookingId(booking.id);

      // Move to payment step
      setStep('payment');
    } catch (err) {
      console.error('Booking failed:', err);
      setError('Failed to create booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle payment success
  const handlePaymentSuccess = async () => {
    setStep('success');

    try {
      // Send confirmation emails
      if (user && venue && bookingId) {
        const booking = {
          ...bookingData,
          id: bookingId,
          venue_id: venue.id,
          guest_id: user.id
        };

        // Send confirmation emails
        await sendBookingConfirmationEmail(booking, user, venue);
        await sendHostNotificationEmail(booking, venue.host || { email: '<EMAIL>' }, venue, user);

        console.log('Booking successful:', booking);
      }
    } catch (err) {
      console.error('Error processing successful booking:', err);
    }
  };

  // Handle payment cancellation
  const handlePaymentCancel = () => {
    setStep('review');
  };

  // Handle view booking details
  const handleViewBooking = () => {
    navigate(`/booking-confirmation/${bookingId}`);
  };

  if (!bookingData || !venue) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-24 pb-12 px-4 md:pt-28 md:px-6 lg:px-8">
      <div className="w-full max-w-3xl mx-auto transition-all duration-300">
        {step === 'review' && (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="bg-purple-600 text-white p-6 sticky top-0 z-10 booking-page-header">
              <h1 className="text-2xl font-bold text-center">Review Your Booking</h1>
            </div>

            <div className="p-6 md:p-8 booking-page-content booking-step-transition">
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-3 text-purple-900">{venue.title}</h2>
                <p className="text-gray-600 flex items-center text-base">
                  <MapPin className="w-5 h-5 mr-2 text-purple-500" />
                  {venue.address}
                </p>
              </div>

              <div className="bg-gray-50 rounded-xl p-6 mb-8 shadow-sm booking-section">
                <h3 className="text-lg font-semibold mb-4 text-gray-800">Booking Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-y-8 gap-x-12">
                  <div className="flex items-start">
                    <Calendar className="w-6 h-6 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">Date</p>
                      <p className="text-base font-semibold text-gray-900">{formatDate(bookingData.date)}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Clock className="w-6 h-6 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">Time</p>
                      <p className="text-base font-semibold text-gray-900">
                        {bookingData.startTime} - {bookingData.endTime}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Users className="w-6 h-6 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">Guests</p>
                      <p className="text-base font-semibold text-gray-900">{bookingData.guests} people</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <DollarSign className="w-6 h-6 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">Total Price</p>
                      <p className="text-base font-semibold text-gray-900">${bookingData.totalPrice.toFixed(2)} AUD</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-100 rounded-lg p-5 mb-8">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-blue-800 font-medium">
                      By proceeding, you agree to our <a href="#" className="underline hover:text-blue-600">Terms of Service</a> and <a href="#" className="underline hover:text-blue-600">Cancellation Policy</a>.
                    </p>
                    <p className="text-blue-700 text-sm mt-1">Your booking is secure and protected by our payment system.</p>
                  </div>
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-5 py-4 rounded-lg mb-6 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium">{error}</span>
                </div>
              )}

              <div className="mb-8">
                <div className="flex justify-center mb-6">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-8 h-8 bg-purple-600 text-white rounded-full font-bold">1</div>
                    <div className="w-16 h-1 bg-purple-600"></div>
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full font-bold">2</div>
                    <div className="w-16 h-1 bg-gray-200"></div>
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full font-bold">3</div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between gap-5">
                  <button
                    onClick={handleConfirmBooking}
                    disabled={loading}
                    className="px-6 py-3 bg-purple-600 text-white rounded-lg text-center hover:bg-purple-700 transition-colors disabled:opacity-50 font-medium text-base shadow-sm flex-1 flex items-center justify-center booking-button"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-3"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        Proceed to Payment
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </>
                    )}
                  </button>

                  <button
                    onClick={() => navigate(-1)}
                    className="px-6 py-3 border border-gray-300 rounded-lg text-center flex items-center justify-center hover:bg-gray-50 transition-colors text-gray-700 font-medium booking-button"
                  >
                    <ArrowLeft className="w-5 h-5 mr-2" />
                    Back
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'payment' && bookingId && (
          <BookingPayment
            bookingId={bookingId}
            amount={bookingData.totalPrice}
            venueName={venue.title}
            bookingDate={bookingData.date}
            onSuccess={handlePaymentSuccess}
            onCancel={handlePaymentCancel}
          />
        )}

        {step === 'success' && (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="bg-green-600 text-white p-8 flex flex-col items-center justify-center">
              <div className="bg-white rounded-full p-2 mb-4">
                <CheckCircle className="w-12 h-12 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold">Payment Successful!</h1>
              <p className="text-green-100 mt-2">Your booking has been confirmed</p>
            </div>

            <div className="p-6 md:p-8">
              <div className="mb-8">
                <div className="flex justify-center mb-6">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full font-bold">1</div>
                    <div className="w-16 h-1 bg-purple-600"></div>
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full font-bold">2</div>
                    <div className="w-16 h-1 bg-purple-600"></div>
                    <div className="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full font-bold">3</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-6 mb-8 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Booking Details</h3>
                <div className="mb-4">
                  <h2 className="text-xl font-bold mb-2 text-purple-900">{venue.title}</h2>
                  <p className="text-gray-600 flex items-center">
                    <MapPin className="w-5 h-5 mr-2 text-purple-500" />
                    {venue.address}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-12 mt-6">
                  <div className="flex items-start">
                    <Calendar className="w-6 h-6 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">Date</p>
                      <p className="text-base font-semibold text-gray-900">{formatDate(bookingData.date)}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Clock className="w-6 h-6 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-1">Time</p>
                      <p className="text-base font-semibold text-gray-900">
                        {bookingData.startTime} - {bookingData.endTime}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-100 rounded-lg p-5 mb-8">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-green-500 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-green-800 font-medium">
                      Your booking has been confirmed and paid for.
                    </p>
                    <p className="text-green-700 text-sm mt-1">We've sent a confirmation email with all the details to your registered email address.</p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-5">
                <button
                  onClick={handleViewBooking}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg text-center hover:bg-purple-700 transition-colors font-medium flex-1 flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                  View Booking Details
                </button>

                <button
                  onClick={() => navigate('/')}
                  className="px-6 py-3 border border-gray-300 rounded-lg text-center flex items-center justify-center hover:bg-gray-50 transition-colors text-gray-700 font-medium"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Back to Home
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
