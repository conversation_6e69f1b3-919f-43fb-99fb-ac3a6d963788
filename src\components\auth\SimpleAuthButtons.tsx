import React from 'react';
import { Link } from 'react-router-dom';
import { User, UserPlus, LogOut, Settings } from 'lucide-react';
import { useUser, useAuth, useClerk } from '@clerk/clerk-react';
import { useSupabase } from '../../providers/SupabaseProvider';

interface SimpleAuthButtonsProps {
  className?: string;
}

export default function SimpleAuthButtons({ className = '' }: SimpleAuthButtonsProps) {
  // Use both Clerk and Supabase auth states
  const { isSignedIn, isLoaded: isClerkLoaded } = useAuth();
  const { user } = useUser();
  const { signOut: clerkSignOut } = useClerk();
  const { isAuthenticated, userProfile, isLoading: isSupabaseLoading, signOut: supabaseSignOut } = useSupabase();

  // Determine the loading state from both providers
  const isLoading = isSupabaseLoading || !isClerkLoaded;

  // Show loading state
  if (isLoading) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="animate-pulse bg-gray-200 h-10 w-24 rounded"></div>
        <div className="animate-pulse bg-gray-200 h-10 w-24 rounded"></div>
      </div>
    );
  }

  // Show authenticated state - prioritize Clerk auth
  if (isSignedIn && user) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center mr-2">
          <div className="bg-purple-100 text-purple-800 rounded-full h-8 w-8 flex items-center justify-center font-medium">
            {user.firstName ? user.firstName.charAt(0).toUpperCase() :
             user.primaryEmailAddress ? user.primaryEmailAddress.emailAddress.charAt(0).toUpperCase() : 'U'}
          </div>
          <span className="text-sm text-gray-700 ml-2">
            {user.firstName || (user.primaryEmailAddress ? user.primaryEmailAddress.emailAddress.split('@')[0] : 'User')}
          </span>
        </div>
        <Link to="/my-account">
          <button className="btn-secondary flex items-center space-x-1 px-4 py-2 border border-purple-200">
            <Settings className="w-4 h-4" />
            <span>Account</span>
          </button>
        </Link>
        <button
          onClick={() => {
            // Sign out from both Clerk and Supabase
            clerkSignOut();
            supabaseSignOut();
          }}
          className="btn-primary flex items-center space-x-1 px-4 py-2"
        >
          <LogOut className="w-4 h-4" />
          <span>Sign Out</span>
        </button>
      </div>
    );
  }
  // Fallback to Supabase auth if Clerk auth is not available
  else if (isAuthenticated && userProfile) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center mr-2">
          <div className="bg-purple-100 text-purple-800 rounded-full h-8 w-8 flex items-center justify-center font-medium">
            {userProfile.first_name ? userProfile.first_name.charAt(0).toUpperCase() :
             userProfile.email ? userProfile.email.charAt(0).toUpperCase() : 'U'}
          </div>
          <span className="text-sm text-gray-700 ml-2">
            {userProfile.first_name || userProfile.email?.split('@')[0] || 'User'}
          </span>
        </div>
        <Link to="/my-account">
          <button className="btn-secondary flex items-center space-x-1 px-4 py-2 border border-purple-200">
            <Settings className="w-4 h-4" />
            <span>Account</span>
          </button>
        </Link>
        <button
          onClick={supabaseSignOut}
          className="btn-primary flex items-center space-x-1 px-4 py-2"
        >
          <LogOut className="w-4 h-4" />
          <span>Sign Out</span>
        </button>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <Link to="/login">
        <button className="btn-secondary flex items-center space-x-1 px-4 py-2 border border-purple-200">
          <User className="w-4 h-4" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to="/signup">
        <button className="btn-primary px-4 py-2">
          <span>Sign Up</span>
        </button>
      </Link>
    </div>
  );
}
