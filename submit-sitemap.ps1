# submit-sitemap.ps1
# PowerShell script to run the sitemap submission helper

Write-Host "Running Sitemap Submission Helper..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node -v
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Cyan
} catch {
    Write-Host "Error: Node.js is not installed or not in PATH. Please install Node.js and try again." -ForegroundColor Red
    exit 1
}

# Check if the script exists
$scriptPath = ".\scripts\submit-sitemap.js"
if (-not (Test-Path $scriptPath)) {
    Write-Host "Error: Sitemap submission script not found at $scriptPath" -ForegroundColor Red
    exit 1
}

# Run the script
Write-Host "Starting sitemap submission helper..." -ForegroundColor Yellow
node $scriptPath

Write-Host "`nSitemap submission helper completed. Follow the instructions above to submit your sitemap to Google." -ForegroundColor Green
