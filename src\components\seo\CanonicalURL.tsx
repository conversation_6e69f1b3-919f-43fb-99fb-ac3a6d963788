import React from 'react';
import { Helmet } from 'react-helmet';

interface CanonicalURLProps {
  url?: string;
  title?: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article' | 'product';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
}

export default function CanonicalURL({
  url,
  title,
  description,
  image,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section
}: CanonicalURLProps) {
  // Get current URL if not provided
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
  
  // Clean URL - remove query parameters and fragments for canonical
  const cleanUrl = currentUrl.split('?')[0].split('#')[0];
  
  // Ensure URL starts with https://housegoing.com.au
  const canonicalUrl = cleanUrl.startsWith('https://housegoing.com.au') 
    ? cleanUrl 
    : `https://housegoing.com.au${cleanUrl.replace(/^https?:\/\/[^\/]+/, '')}`;

  // Default values
  const defaultTitle = 'HouseGoing - Premium Party Venue Rentals in NSW, Australia';
  const defaultDescription = 'Discover and book exclusive party venues across NSW. From Sydney CBD to beachside locations, find the perfect space for your celebration.';
  const defaultImage = 'https://housegoing.com.au/images/housegoing-logo.svg';

  return (
    <Helmet>
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Basic Meta Tags */}
      <title>{title || defaultTitle}</title>
      <meta name="description" content={description || defaultDescription} />
      
      {/* Open Graph Tags */}
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title || defaultTitle} />
      <meta property="og:description" content={description || defaultDescription} />
      <meta property="og:image" content={image || defaultImage} />
      <meta property="og:site_name" content="HouseGoing" />
      <meta property="og:locale" content="en_AU" />
      
      {/* Article-specific tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && author && (
        <meta property="article:author" content={author} />
      )}
      {type === 'article' && section && (
        <meta property="article:section" content={section} />
      )}
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:url" content={canonicalUrl} />
      <meta name="twitter:title" content={title || defaultTitle} />
      <meta name="twitter:description" content={description || defaultDescription} />
      <meta name="twitter:image" content={image || defaultImage} />
      
      {/* Additional SEO Tags */}
      <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      
      {/* Schema.org structured data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": type === 'article' ? 'Article' : 'WebPage',
          "url": canonicalUrl,
          "name": title || defaultTitle,
          "description": description || defaultDescription,
          "image": image || defaultImage,
          "publisher": {
            "@type": "Organization",
            "name": "HouseGoing",
            "url": "https://housegoing.com.au",
            "logo": {
              "@type": "ImageObject",
              "url": "https://housegoing.com.au/images/housegoing-logo.svg"
            }
          },
          ...(type === 'article' && {
            "datePublished": publishedTime,
            "dateModified": modifiedTime || publishedTime,
            "author": {
              "@type": "Person",
              "name": author || "HouseGoing Team"
            }
          })
        })}
      </script>
    </Helmet>
  );
}
