/**
 * Supabase Client Checker
 *
 * This utility helps identify and fix direct Supabase client imports
 * that could cause the "Multiple GoTrueClient instances" warning.
 */

// Import the supabase client
import supabase from '../lib/supabase-client';

// List of known files that might create their own Supabase clients
const knownProblematicFiles = [
  'src/lib/supabase.ts',
  'src/lib/supabase.js',
  'src/integrations/supabase/client.ts',
  'src/services/api/index.ts'
];

/**
 * Check if a Supabase client is being created directly
 * This function should be called in development mode to help identify issues
 */
export function checkForDirectSupabaseImports() {
  if (process.env.NODE_ENV !== 'production') {
    console.log('Checking for direct Supabase client imports...');

    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      // Check if multiple clients have been created
      // @ts-ignore
      if (window.__SUPABASE_CLIENT_INITIALIZED && window.__SUPABASE_MULTIPLE_CLIENTS_WARNING) {
        console.warn('Multiple Supabase clients detected. This can cause the "Multiple GoTrueClient instances" warning.');
        console.warn('Please use the centralized client from src/lib/supabase-client.ts instead.');
        console.warn('Known problematic files that might create their own clients:');
        knownProblematicFiles.forEach(file => console.warn(`- ${file}`));
      } else {
        // Set a flag to detect if multiple clients are created
        // @ts-ignore
        window.__SUPABASE_CLIENT_INITIALIZED = true;
      }

      // Monkey patch createClient to detect direct usage
      try {
        // @ts-ignore
        if (window.__SUPABASE_CREATE_CLIENT_PATCHED) {
          return;
        }

        // Try to get the original createClient function
        const originalModule = require('@supabase/supabase-js');
        const originalCreateClient = originalModule.createClient;

        if (originalCreateClient && typeof originalCreateClient === 'function') {
          // Replace it with our version that logs warnings
          originalModule.createClient = function(...args: any[]) {
            // Log a warning
            console.warn('WARNING: You are creating a new Supabase client. This can cause the "Multiple GoTrueClient instances" warning. Use the centralized client from src/lib/supabase-client.ts instead.');

            // Set a flag to indicate multiple clients
            // @ts-ignore
            window.__SUPABASE_MULTIPLE_CLIENTS_WARNING = true;

            // Call the original function
            return originalCreateClient.apply(this, args);
          };

          // Mark as patched
          // @ts-ignore
          window.__SUPABASE_CREATE_CLIENT_PATCHED = true;

          console.log('Successfully patched Supabase createClient to detect direct usage');
        }
      } catch (error) {
        console.error('Error patching Supabase createClient:', error);
      }
    }
  }
}

/**
 * Initialize the checker
 * This should be called early in the application lifecycle
 */
export function initSupabaseClientChecker() {
  checkForDirectSupabaseImports();
}

export default initSupabaseClientChecker;
