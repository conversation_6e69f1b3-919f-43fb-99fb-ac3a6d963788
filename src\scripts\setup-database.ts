/**
 * Database Setup Script
 * 
 * This script sets up the Supabase database for the HouseGoing application.
 * It creates the necessary SQL functions and tables.
 * 
 * Run this script with:
 * npx ts-node src/scripts/setup-database.ts
 */

import { getSupabaseClient } from '../lib/supabase-client';
import { setupAllFunctions } from '../lib/database/setup-functions';
import { runMigrations } from '../lib/database/migrations';

async function setupDatabase() {
  console.log('=== HouseGoing Supabase Database Setup ===');
  
  try {
    // Get the Supabase client
    const supabase = getSupabaseClient();
    console.log('Supabase client initialized');
    
    // Set up SQL functions
    console.log('\nSetting up SQL functions...');
    const functionsSetup = await setupAllFunctions(supabase);
    
    if (!functionsSetup) {
      console.error('Failed to set up SQL functions');
      console.error('Please create the SQL functions manually in the Supabase dashboard SQL editor');
      console.error('See src/lib/database/setup-functions.ts for the SQL to execute');
      return;
    }
    
    console.log('SQL functions set up successfully');
    
    // Run migrations
    console.log('\nRunning migrations...');
    const migrationsResult = await runMigrations(supabase);
    
    if (!migrationsResult.success) {
      console.error('Failed to run migrations:', migrationsResult.error);
      return;
    }
    
    console.log('Migrations completed successfully');
    
    // Verify tables
    console.log('\nVerifying tables...');
    
    // Check user_profiles table
    const { data: userProfiles, error: userProfilesError } = await supabase
      .from('user_profiles')
      .select('id', { count: 'exact', head: true })
      .limit(1);
    
    if (userProfilesError) {
      console.error('Error verifying user_profiles table:', userProfilesError);
    } else {
      console.log('user_profiles table exists');
    }
    
    // Check admin_users table
    const { data: adminUsers, error: adminUsersError } = await supabase
      .from('admin_users')
      .select('id', { count: 'exact', head: true })
      .limit(1);
    
    if (adminUsersError) {
      console.error('Error verifying admin_users table:', adminUsersError);
    } else {
      console.log('admin_users table exists');
    }
    
    console.log('\n=== Database setup completed ===');
  } catch (error) {
    console.error('Error setting up database:', error);
  }
}

// Run the setup
setupDatabase().catch(console.error);
