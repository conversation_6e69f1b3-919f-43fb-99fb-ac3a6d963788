import React from 'react';
import { <PERSON> } from 'react-router-dom';
import SEO from '../../components/seo/SEO';
import { Calendar, User, ArrowRight, MapPin, Users, Music } from 'lucide-react';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  category: string;
  image: string;
  slug: string;
  readTime: string;
}

const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Ultimate Guide to Planning a Party in NSW: Noise Laws & Venue Selection',
    excerpt: 'Everything you need to know about NSW noise restrictions, council permits, and choosing the perfect venue for your celebration.',
    content: '',
    author: 'HouseGoing Team',
    date: '2024-01-15',
    category: 'Party Planning',
    image: '/images/blog/nsw-party-planning.jpg',
    slug: 'ultimate-guide-party-planning-nsw-noise-laws',
    readTime: '8 min read'
  },
  {
    id: '2',
    title: 'Top 10 Party Venues in Sydney: Hidden Gems for Your Next Event',
    excerpt: 'Discover Sydney\'s best-kept secrets for hosting unforgettable parties, from rooftop terraces to waterfront spaces.',
    content: '',
    author: '<PERSON>',
    date: '2024-01-10',
    category: 'Venue Guides',
    image: '/images/blog/sydney-venues.jpg',
    slug: 'top-10-party-venues-sydney-hidden-gems',
    readTime: '6 min read'
  },
  {
    id: '3',
    title: 'Budget-Friendly Party Ideas: How to Host Amazing Events Under $500',
    excerpt: 'Creative tips and tricks to throw memorable parties without breaking the bank, including venue alternatives and DIY decorations.',
    content: '',
    author: 'Mike Johnson',
    date: '2024-01-05',
    category: 'Budget Tips',
    image: '/images/blog/budget-party-ideas.jpg',
    slug: 'budget-friendly-party-ideas-under-500',
    readTime: '5 min read'
  },
  {
    id: '4',
    title: 'Seasonal Party Planning: Best Times to Book Venues in NSW',
    excerpt: 'Learn when to book venues for the best prices and availability throughout the year in New South Wales.',
    content: '',
    author: 'Emma Wilson',
    date: '2024-01-01',
    category: 'Seasonal Tips',
    image: '/images/blog/seasonal-planning.jpg',
    slug: 'seasonal-party-planning-best-times-book-venues-nsw',
    readTime: '7 min read'
  }
];

const categories = ['All', 'Party Planning', 'Venue Guides', 'Budget Tips', 'Seasonal Tips'];

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = React.useState('All');

  const filteredPosts = selectedCategory === 'All' 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  return (
    <>
      <SEO
        title="Party Planning Blog & Venue Guides | HouseGoing"
        description="Expert tips for party planning in NSW, venue selection guides, budget advice, and seasonal booking strategies. Your ultimate resource for event planning."
        keywords="party planning blog, NSW event tips, venue selection guide, party budget tips, seasonal party planning, event planning advice"
        url="https://housegoing.com.au/blog"
      />

      <div className="pt-32 px-4 sm:px-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Party Planning Blog
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Expert tips, venue guides, and insider secrets for planning unforgettable events in NSW
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-2 rounded-full transition-colors ${
                selectedCategory === category
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Blog Posts Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {filteredPosts.map((post) => (
            <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-video bg-gray-200 relative">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
                    {post.category}
                  </span>
                </div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <User className="h-4 w-4 mr-1" />
                  <span className="mr-4">{post.author}</span>
                  <Calendar className="h-4 w-4 mr-1" />
                  <span className="mr-4">{new Date(post.date).toLocaleDateString()}</span>
                  <span>{post.readTime}</span>
                </div>
                
                <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                  {post.title}
                </h2>
                
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                
                <Link
                  to={`/blog/${post.slug}`}
                  className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium"
                >
                  Read More
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </article>
          ))}
        </div>

        {/* Featured Content Sections */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {/* Popular Locations */}
          <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-8">
            <div className="flex items-center mb-4">
              <MapPin className="h-6 w-6 text-purple-600 mr-2" />
              <h3 className="text-2xl font-bold text-gray-900">Popular Locations</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Discover the best party venues in NSW's most sought-after locations
            </p>
            <div className="space-y-3">
              {['Sydney CBD', 'Bondi Beach', 'Manly', 'Parramatta', 'Newcastle'].map((location) => (
                <Link
                  key={location}
                  to={`/find-venues?location=${location}`}
                  className="block text-purple-600 hover:text-purple-700 font-medium"
                >
                  Party Venues in {location} →
                </Link>
              ))}
            </div>
          </div>

          {/* Party Types */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-8">
            <div className="flex items-center mb-4">
              <Music className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-2xl font-bold text-gray-900">Party Types</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Find venues perfect for your specific type of celebration
            </p>
            <div className="space-y-3">
              {['Birthday Parties', 'Corporate Events', 'Wedding Receptions', 'Graduation Parties', 'Holiday Celebrations'].map((type) => (
                <Link
                  key={type}
                  to={`/find-venues?type=${type.toLowerCase().replace(' ', '-')}`}
                  className="block text-blue-600 hover:text-blue-700 font-medium"
                >
                  {type} Venues →
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="bg-gray-900 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-white mb-4">
            Stay Updated with Party Planning Tips
          </h3>
          <p className="text-gray-300 mb-6">
            Get the latest venue guides, planning tips, and exclusive deals delivered to your inbox
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <button className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
