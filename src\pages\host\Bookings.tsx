import React, { useState } from 'react';
import {
  Calendar,
  Filter,
  Search,
  Check,
  X,
  MessageSquare,
  ChevronDown,
  ChevronUp,
  User,
  Phone,
  Mail,
  MapPin,
  Star,
  Clock,
  Send,
  Eye
} from 'lucide-react';
import { messageStore } from '../../stores/messageStore';

// Mock data for bookings
const mockBookings = [
  {
    id: 1,
    guest: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+61 412 345 678',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      location: 'Sydney, NSW',
      joinDate: '2022-03-15',
      rating: 4.8,
      totalBookings: 12,
      bio: 'Love traveling and exploring new places. Always respectful of properties.',
      previousBookings: [
        { property: 'City Apartment', date: '2023-06-10', rating: 5 },
        { property: 'Beach House', date: '2023-04-22', rating: 4 },
        { property: 'Mountain Retreat', date: '2023-02-14', rating: 5 }
      ]
    },
    property: 'Beachside Villa',
    checkIn: '2023-08-15',
    checkOut: '2023-08-18',
    guests: 2,
    status: 'confirmed',
    amount: 1050,
    createdAt: '2023-07-20T14:30:00Z',
    messages: 2,
    messageHistory: [
      { from: 'guest', message: 'Hi! Looking forward to our stay. What time is check-in?', time: '2023-08-10T10:30:00Z' },
      { from: 'host', message: 'Hello <PERSON>! Check-in is from 3 PM. Looking forward to hosting you!', time: '2023-08-10T11:15:00Z' }
    ]
  },
  {
    id: 2,
    guest: {
      name: '<PERSON> <PERSON>',
      email: '<EMAIL>',
      phone: '+61 423 456 789',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      location: 'Melbourne, VIC',
      joinDate: '2023-01-20',
      rating: 4.9,
      totalBookings: 8,
      bio: 'Family traveler who loves nature and outdoor activities. Very clean and respectful.',
      previousBookings: [
        { property: 'Lakeside Cottage', date: '2023-05-15', rating: 5 },
        { property: 'Forest Cabin', date: '2023-03-08', rating: 5 }
      ]
    },
    property: 'Mountain Cabin',
    checkIn: '2023-08-20',
    checkOut: '2023-08-25',
    guests: 4,
    status: 'pending',
    amount: 1375,
    createdAt: '2023-07-22T09:15:00Z',
    messages: 0,
    messageHistory: []
  },
  {
    id: 3,
    guest: {
      name: 'Michael Brown',
      email: '<EMAIL>',
      phone: '+61 4XX XXX XXX',
      image: 'https://randomuser.me/api/portraits/men/22.jpg'
    },
    property: 'Downtown Loft',
    checkIn: '2023-08-10',
    checkOut: '2023-08-12',
    guests: 2,
    status: 'completed',
    amount: 840,
    createdAt: '2023-07-15T11:45:00Z',
    messages: 3
  },
  {
    id: 4,
    guest: {
      name: 'Emily Wilson',
      email: '<EMAIL>',
      phone: '+61 4XX XXX XXX',
      image: 'https://randomuser.me/api/portraits/women/17.jpg'
    },
    property: 'Beachside Villa',
    checkIn: '2023-09-05',
    checkOut: '2023-09-10',
    guests: 3,
    status: 'confirmed',
    amount: 1750,
    createdAt: '2023-07-25T16:20:00Z',
    messages: 1
  },
  {
    id: 5,
    guest: {
      name: 'David Lee',
      email: '<EMAIL>',
      phone: '+61 4XX XXX XXX',
      image: 'https://randomuser.me/api/portraits/men/57.jpg'
    },
    property: 'Mountain Cabin',
    checkIn: '2023-09-15',
    checkOut: '2023-09-18',
    guests: 2,
    status: 'cancelled',
    amount: 825,
    createdAt: '2023-07-18T10:30:00Z',
    messages: 4
  }
];

export default function Bookings() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [expandedBooking, setExpandedBooking] = useState<number | null>(null);

  // Modal states
  const [showGuestProfile, setShowGuestProfile] = useState<number | null>(null);
  const [showMessaging, setShowMessaging] = useState<number | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [bookingMessages, setBookingMessages] = useState<{[key: number]: any[]}>({});

  // Date/time formatting function
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year}, ${hours}:${minutes}`;
  };

  // Initialize message history and sync with message store
  React.useEffect(() => {
    const initialMessages: {[key: number]: any[]} = {};

    mockBookings.forEach(booking => {
      const messages = booking.messageHistory || [];
      initialMessages[booking.id] = messages;

      // Sync with central message store
      if (messages.length > 0) {
        messageStore.syncBookingMessages(booking.id, messages);
      }
    });

    setBookingMessages(initialMessages);
  }, []);

  // Filter bookings based on search term and status filter
  const filteredBookings = mockBookings.filter(booking => {
    const matchesSearch =
      booking.guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.property.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Toggle booking details
  const toggleBookingDetails = (bookingId: number) => {
    if (expandedBooking === bookingId) {
      setExpandedBooking(null);
    } else {
      setExpandedBooking(bookingId);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Interactive functions
  const handleApproveBooking = (bookingId: number) => {
    alert(`Booking #${bookingId} approved! In a real app, this would update the database.`);
  };

  const handleRejectBooking = (bookingId: number) => {
    alert(`Booking #${bookingId} rejected! In a real app, this would update the database.`);
  };

  const handleCancelBooking = (bookingId: number) => {
    alert(`Booking #${bookingId} cancelled! In a real app, this would update the database.`);
  };

  const handleSendMessage = (bookingId: number) => {
    if (!newMessage.trim()) return;

    const booking = mockBookings.find(b => b.id === bookingId);
    if (!booking) return;

    const newMsg = {
      from: 'host',
      message: newMessage,
      time: new Date().toISOString()
    };

    // Update local booking messages
    setBookingMessages(prev => ({
      ...prev,
      [bookingId]: [...(prev[bookingId] || []), newMsg]
    }));

    // Add to central message store
    const conversationId = `booking-${bookingId}`;
    messageStore.addMessage({
      conversationId,
      from: 'host',
      to: 'guest',
      message: newMessage,
      timestamp: new Date().toISOString(),
      read: false,
      bookingId,
      guestName: booking.guest.name,
      hostName: 'Host User',
      propertyName: booking.property
    });

    // Update the mock booking data to reflect new message count
    booking.messages = (bookingMessages[bookingId]?.length || 0) + 1;

    setNewMessage('');

    // Show success message
    alert(`Message sent to ${booking.guest.name}! They will receive an email notification: "Your host sent you a message" and can reply through the messaging portal.`);
  };

  const openGuestProfile = (bookingId: number) => {
    setShowGuestProfile(bookingId);
  };

  const openMessaging = (bookingId: number) => {
    setShowMessaging(bookingId);
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Bookings</h1>
        <p className="mt-1 text-gray-600">Manage all your property bookings in one place</p>
      </div>

      {/* Search and filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by guest or property..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="relative sm:w-48">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter className="h-5 w-5 text-gray-400" />
          </div>
          <select
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Bookings list */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Guest
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Property
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dates
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBookings.map((booking) => (
                <React.Fragment key={booking.id}>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full cursor-pointer hover:ring-2 hover:ring-purple-500"
                            src={booking.guest.image}
                            alt={booking.guest.name}
                            onClick={() => openGuestProfile(booking.id)}
                            title="View guest profile"
                          />
                        </div>
                        <div className="ml-4">
                          <div
                            className="text-sm font-medium text-gray-900 cursor-pointer hover:text-purple-600"
                            onClick={() => openGuestProfile(booking.id)}
                          >
                            {booking.guest.name}
                          </div>
                          <div className="text-sm text-gray-500">{booking.guest.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{booking.property}</div>
                      <div className="text-sm text-gray-500">{booking.guests} guests</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(booking.checkIn)}</div>
                      <div className="text-sm text-gray-500">to {formatDate(booking.checkOut)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(booking.status)}`}>
                        {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${booking.amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2">
                        {booking.status === 'pending' && (
                          <>
                            <button
                              className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                              onClick={() => handleApproveBooking(booking.id)}
                              title="Approve booking"
                            >
                              <Check className="h-5 w-5" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                              onClick={() => handleRejectBooking(booking.id)}
                              title="Reject booking"
                            >
                              <X className="h-5 w-5" />
                            </button>
                          </>
                        )}
                        <button
                          className="text-purple-600 hover:text-purple-900 relative p-1 rounded hover:bg-purple-50"
                          onClick={() => openMessaging(booking.id)}
                          title="Message guest"
                        >
                          <MessageSquare className="h-5 w-5" />
                          {(bookingMessages[booking.id]?.length || 0) > 0 && (
                            <span className="absolute -top-1 -right-1 px-1.5 py-0.5 bg-red-500 text-white text-xs rounded-full">
                              {bookingMessages[booking.id]?.length || 0}
                            </span>
                          )}
                        </button>
                        <button
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          onClick={() => openGuestProfile(booking.id)}
                          title="View guest profile"
                        >
                          <Eye className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => toggleBookingDetails(booking.id)}
                          className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                          title="Toggle details"
                        >
                          {expandedBooking === booking.id ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* Expanded booking details */}
                  {expandedBooking === booking.id && (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Booking Details</h4>
                            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                              <dt className="text-gray-500">Booking ID</dt>
                              <dd className="text-gray-900">#{booking.id}</dd>
                              <dt className="text-gray-500">Created</dt>
                              <dd className="text-gray-900">{new Date(booking.createdAt).toLocaleString()}</dd>
                              <dt className="text-gray-500">Check-in</dt>
                              <dd className="text-gray-900">{formatDate(booking.checkIn)}</dd>
                              <dt className="text-gray-500">Check-out</dt>
                              <dd className="text-gray-900">{formatDate(booking.checkOut)}</dd>
                              <dt className="text-gray-500">Guests</dt>
                              <dd className="text-gray-900">{booking.guests}</dd>
                              <dt className="text-gray-500">Total Amount</dt>
                              <dd className="text-gray-900">${booking.amount.toFixed(2)}</dd>
                            </dl>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Guest Information</h4>
                            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                              <dt className="text-gray-500">Name</dt>
                              <dd className="text-gray-900">{booking.guest.name}</dd>
                              <dt className="text-gray-500">Email</dt>
                              <dd className="text-gray-900">{booking.guest.email}</dd>
                              <dt className="text-gray-500">Phone</dt>
                              <dd className="text-gray-900">{booking.guest.phone}</dd>
                            </dl>

                            <div className="mt-4 flex space-x-2">
                              <button
                                className="px-3 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                                onClick={() => openMessaging(booking.id)}
                              >
                                Message Guest
                              </button>
                              <button
                                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                                onClick={() => openGuestProfile(booking.id)}
                              >
                                View Profile
                              </button>
                              {booking.status === 'confirmed' && (
                                <button
                                  className="px-3 py-1 bg-red-100 text-red-600 text-sm rounded-md hover:bg-red-200"
                                  onClick={() => handleCancelBooking(booking.id)}
                                >
                                  Cancel Booking
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {filteredBookings.length === 0 && (
          <div className="px-6 py-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all'
                ? "Try adjusting your search or filters"
                : "You don't have any bookings yet"}
            </p>
          </div>
        )}
      </div>

      {/* Guest Profile Modal */}
      {showGuestProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {(() => {
              const booking = mockBookings.find(b => b.id === showGuestProfile);
              if (!booking) return null;

              return (
                <>
                  <div className="flex justify-between items-start mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">Guest Profile</h2>
                    <button
                      onClick={() => setShowGuestProfile(null)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="flex items-center mb-6">
                    <img
                      className="h-20 w-20 rounded-full"
                      src={booking.guest.image}
                      alt={booking.guest.name}
                    />
                    <div className="ml-6">
                      <h3 className="text-xl font-semibold text-gray-900">{booking.guest.name}</h3>
                      <div className="flex items-center mt-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm text-gray-600">{booking.guest.rating}/5</span>
                        <span className="ml-2 text-sm text-gray-500">({booking.guest.totalBookings} bookings)</span>
                      </div>
                      <div className="flex items-center mt-1 text-sm text-gray-500">
                        <MapPin className="h-4 w-4 mr-1" />
                        {booking.guest.location}
                      </div>
                      <div className="flex items-center mt-1 text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        Member since {new Date(booking.guest.joinDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">About</h4>
                    <p className="text-gray-600">{booking.guest.bio}</p>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Contact Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <Mail className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-gray-600">{booking.guest.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-gray-600">{booking.guest.phone}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Previous Bookings</h4>
                    <div className="space-y-3">
                      {booking.guest.previousBookings.map((prevBooking, index) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">{prevBooking.property}</div>
                            <div className="text-sm text-gray-500">{new Date(prevBooking.date).toLocaleDateString()}</div>
                          </div>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="ml-1 text-sm text-gray-600">{prevBooking.rating}/5</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                      onClick={() => {
                        setShowGuestProfile(null);
                        openMessaging(booking.id);
                      }}
                    >
                      Message Guest
                    </button>
                    <button
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                      onClick={() => setShowGuestProfile(null)}
                    >
                      Close
                    </button>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}

      {/* Messaging Modal */}
      {showMessaging && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
            {(() => {
              const booking = mockBookings.find(b => b.id === showMessaging);
              if (!booking) return null;

              return (
                <>
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Message {booking.guest.name}</h2>
                      <p className="text-gray-600">Booking #{booking.id} - {booking.property}</p>
                    </div>
                    <button
                      onClick={() => setShowMessaging(null)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="flex-1 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50 min-h-[300px]">
                    <div className="space-y-4">
                      {(bookingMessages[booking.id] || []).map((message, index) => (
                        <div key={index} className={`flex ${message.from === 'host' ? 'justify-end' : 'justify-start'}`}>
                          <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.from === 'host'
                              ? 'bg-blue-100 text-blue-900 border border-blue-200'
                              : 'bg-gray-100 border border-gray-200 text-gray-900'
                          }`}>
                            <p className="text-sm">{message.message}</p>
                            <p className={`text-xs mt-1 ${
                              message.from === 'host' ? 'text-blue-600' : 'text-gray-500'
                            }`}>
                              {formatDateTime(message.time)}
                            </p>
                          </div>
                        </div>
                      ))}
                      {(bookingMessages[booking.id] || []).length === 0 && (
                        <div className="text-center text-gray-500 py-8">
                          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>No messages yet. Start the conversation!</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(booking.id)}
                    />
                    <button
                      onClick={() => handleSendMessage(booking.id)}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
                    >
                      <Send className="h-4 w-4" />
                    </button>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}
