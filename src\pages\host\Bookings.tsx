import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';
import {
  Calendar,
  Filter,
  Search,
  Check,
  X,
  MessageSquare,
  ChevronDown,
  ChevronUp,
  User,
  Phone,
  Mail,
  MapPin,
  Star,
  Clock,
  Send,
  Eye,
  AlertCircle
} from 'lucide-react';
import { messageStore } from '../../stores/messageStore';

interface Guest {
  id: string;
  name: string;
  email: string;
  phone?: string;
  image?: string;
  location?: string;
  joinDate: string;
  rating?: number;
  totalBookings?: number;
  bio?: string;
}

interface Booking {
  id: string;
  guest: Guest;
  venue_name: string;
  booking_date: string;
  end_date: string;
  guest_count: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  total_amount: number;
  created_at: string;
  special_requests?: string;
  venue_id: string;
}

export default function Bookings() {
  const { user } = useUser();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [expandedBooking, setExpandedBooking] = useState<string | null>(null);

  // Modal states
  const [showGuestProfile, setShowGuestProfile] = useState<string | null>(null);
  const [showMessaging, setShowMessaging] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [bookingMessages, setBookingMessages] = useState<{[key: string]: any[]}>({});

  useEffect(() => {
    if (user) {
      fetchBookings();
    }
  }, [user]);

  const fetchBookings = async () => {
    try {
      setLoading(true);

      // Fetch bookings for this host's venues
      const { data: bookingsData, error } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          end_date,
          guest_count,
          total_amount,
          status,
          special_requests,
          created_at,
          venues!inner(id, name, host_id),
          user_profiles!inner(id, first_name, last_name, email, phone, created_at)
        `)
        .eq('venues.host_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform data to match our interface
      const transformedBookings: Booking[] = (bookingsData || []).map(booking => ({
        id: booking.id,
        venue_name: booking.venues?.name || 'Unknown Venue',
        booking_date: booking.booking_date,
        end_date: booking.end_date,
        guest_count: booking.guest_count,
        status: booking.status,
        total_amount: booking.total_amount,
        created_at: booking.created_at,
        special_requests: booking.special_requests,
        venue_id: booking.venues?.id || '',
        guest: {
          id: booking.user_profiles?.id || '',
          name: `${booking.user_profiles?.first_name || ''} ${booking.user_profiles?.last_name || ''}`.trim() || 'Unknown Guest',
          email: booking.user_profiles?.email || '',
          phone: booking.user_profiles?.phone || '',
          joinDate: booking.user_profiles?.created_at || '',
          location: '', // We don't have this data yet
          rating: 0, // We don't have this data yet
          totalBookings: 0, // We don't have this data yet
          bio: '' // We don't have this data yet
        }
      }));

      setBookings(transformedBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Failed to load bookings');
    } finally {
      setLoading(false);
    }
  };

  // Date/time formatting function
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year}, ${hours}:${minutes}`;
  };

  // Filter bookings based on search term and status filter
  const filteredBookings = bookings.filter(booking => {
    const matchesSearch =
      booking.guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.venue_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Toggle booking details
  const toggleBookingDetails = (bookingId: string) => {
    if (expandedBooking === bookingId) {
      setExpandedBooking(null);
    } else {
      setExpandedBooking(bookingId);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Interactive functions
  const handleApproveBooking = (bookingId: number) => {
    alert(`Booking #${bookingId} approved! In a real app, this would update the database.`);
  };

  const handleRejectBooking = (bookingId: number) => {
    alert(`Booking #${bookingId} rejected! In a real app, this would update the database.`);
  };

  const handleCancelBooking = (bookingId: number) => {
    alert(`Booking #${bookingId} cancelled! In a real app, this would update the database.`);
  };

  const handleSendMessage = (bookingId: number) => {
    if (!newMessage.trim()) return;

    const booking = mockBookings.find(b => b.id === bookingId);
    if (!booking) return;

    const newMsg = {
      from: 'host',
      message: newMessage,
      time: new Date().toISOString()
    };

    // Update local booking messages
    setBookingMessages(prev => ({
      ...prev,
      [bookingId]: [...(prev[bookingId] || []), newMsg]
    }));

    // Add to central message store
    const conversationId = `booking-${bookingId}`;
    messageStore.addMessage({
      conversationId,
      from: 'host',
      to: 'guest',
      message: newMessage,
      timestamp: new Date().toISOString(),
      read: false,
      bookingId,
      guestName: booking.guest.name,
      hostName: 'Host User',
      propertyName: booking.property
    });

    // Update the mock booking data to reflect new message count
    booking.messages = (bookingMessages[bookingId]?.length || 0) + 1;

    setNewMessage('');

    // Show success message
    alert(`Message sent to ${booking.guest.name}! They will receive an email notification: "Your host sent you a message" and can reply through the messaging portal.`);
  };

  const openGuestProfile = (bookingId: number) => {
    setShowGuestProfile(bookingId);
  };

  const openMessaging = (bookingId: number) => {
    setShowMessaging(bookingId);
  };

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Bookings</h1>
          <p className="mt-1 text-gray-600">Manage all your property bookings in one place</p>
        </div>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="bg-white rounded-lg shadow-sm p-6">
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Bookings</h1>
        <p className="mt-1 text-gray-600">Manage all your property bookings in one place</p>
      </div>

      {/* Search and filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by guest or property..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="relative sm:w-48">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter className="h-5 w-5 text-gray-400" />
          </div>
          <select
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Empty state */}
      {filteredBookings.length === 0 && !loading && (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
          <p className="text-gray-600 mb-4">
            {bookings.length === 0
              ? "You haven't received any bookings yet. Once guests book your venues, they'll appear here."
              : "No bookings match your current filters. Try adjusting your search or filter criteria."
            }
          </p>
          {bookings.length === 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Tips to get your first booking:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Make sure your venue listings are complete with photos</li>
                <li>• Set competitive pricing for your area</li>
                <li>• Respond quickly to guest inquiries</li>
                <li>• Keep your availability calendar up to date</li>
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Bookings list */}
      {filteredBookings.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Guest
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Property
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dates
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredBookings.map((booking) => (
                <React.Fragment key={booking.id}>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center cursor-pointer hover:ring-2 hover:ring-purple-500">
                            <User className="h-5 w-5 text-purple-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div
                            className="text-sm font-medium text-gray-900 cursor-pointer hover:text-purple-600"
                            onClick={() => openGuestProfile(booking.id)}
                          >
                            {booking.guest.name}
                          </div>
                          <div className="text-sm text-gray-500">{booking.guest.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{booking.venue_name}</div>
                      <div className="text-sm text-gray-500">{booking.guest_count} guests</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(booking.booking_date)}</div>
                      <div className="text-sm text-gray-500">to {formatDate(booking.end_date)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(booking.status)}`}>
                        {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${booking.total_amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2">
                        {booking.status === 'pending' && (
                          <>
                            <button
                              className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                              onClick={() => handleApproveBooking(booking.id)}
                              title="Approve booking"
                            >
                              <Check className="h-5 w-5" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                              onClick={() => handleRejectBooking(booking.id)}
                              title="Reject booking"
                            >
                              <X className="h-5 w-5" />
                            </button>
                          </>
                        )}
                        <button
                          className="text-purple-600 hover:text-purple-900 relative p-1 rounded hover:bg-purple-50"
                          onClick={() => openMessaging(booking.id)}
                          title="Message guest"
                        >
                          <MessageSquare className="h-5 w-5" />
                          {(bookingMessages[booking.id]?.length || 0) > 0 && (
                            <span className="absolute -top-1 -right-1 px-1.5 py-0.5 bg-red-500 text-white text-xs rounded-full">
                              {bookingMessages[booking.id]?.length || 0}
                            </span>
                          )}
                        </button>
                        <button
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          onClick={() => openGuestProfile(booking.id)}
                          title="View guest profile"
                        >
                          <Eye className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => toggleBookingDetails(booking.id)}
                          className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                          title="Toggle details"
                        >
                          {expandedBooking === booking.id ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* Expanded booking details */}
                  {expandedBooking === booking.id && (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Booking Details</h4>
                            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                              <dt className="text-gray-500">Booking ID</dt>
                              <dd className="text-gray-900">#{booking.id}</dd>
                              <dt className="text-gray-500">Created</dt>
                              <dd className="text-gray-900">{new Date(booking.created_at).toLocaleString()}</dd>
                              <dt className="text-gray-500">Check-in</dt>
                              <dd className="text-gray-900">{formatDate(booking.booking_date)}</dd>
                              <dt className="text-gray-500">Check-out</dt>
                              <dd className="text-gray-900">{formatDate(booking.end_date)}</dd>
                              <dt className="text-gray-500">Guests</dt>
                              <dd className="text-gray-900">{booking.guest_count}</dd>
                              <dt className="text-gray-500">Total Amount</dt>
                              <dd className="text-gray-900">${booking.total_amount.toFixed(2)}</dd>
                              {booking.special_requests && (
                                <>
                                  <dt className="text-gray-500">Special Requests</dt>
                                  <dd className="text-gray-900">{booking.special_requests}</dd>
                                </>
                              )}
                            </dl>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Guest Information</h4>
                            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                              <dt className="text-gray-500">Name</dt>
                              <dd className="text-gray-900">{booking.guest.name}</dd>
                              <dt className="text-gray-500">Email</dt>
                              <dd className="text-gray-900">{booking.guest.email}</dd>
                              <dt className="text-gray-500">Phone</dt>
                              <dd className="text-gray-900">{booking.guest.phone}</dd>
                            </dl>

                            <div className="mt-4 flex space-x-2">
                              <button
                                className="px-3 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                                onClick={() => openMessaging(booking.id)}
                              >
                                Message Guest
                              </button>
                              <button
                                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                                onClick={() => openGuestProfile(booking.id)}
                              >
                                View Profile
                              </button>
                              {booking.status === 'confirmed' && (
                                <button
                                  className="px-3 py-1 bg-red-100 text-red-600 text-sm rounded-md hover:bg-red-200"
                                  onClick={() => handleCancelBooking(booking.id)}
                                >
                                  Cancel Booking
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {filteredBookings.length === 0 && (
          <div className="px-6 py-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all'
                ? "Try adjusting your search or filters"
                : "You don't have any bookings yet"}
            </p>
          </div>
        )}
      </div>

      {/* Guest Profile Modal */}
      {showGuestProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {(() => {
              const booking = mockBookings.find(b => b.id === showGuestProfile);
              if (!booking) return null;

              return (
                <>
                  <div className="flex justify-between items-start mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">Guest Profile</h2>
                    <button
                      onClick={() => setShowGuestProfile(null)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="flex items-center mb-6">
                    <img
                      className="h-20 w-20 rounded-full"
                      src={booking.guest.image}
                      alt={booking.guest.name}
                    />
                    <div className="ml-6">
                      <h3 className="text-xl font-semibold text-gray-900">{booking.guest.name}</h3>
                      <div className="flex items-center mt-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm text-gray-600">{booking.guest.rating}/5</span>
                        <span className="ml-2 text-sm text-gray-500">({booking.guest.totalBookings} bookings)</span>
                      </div>
                      <div className="flex items-center mt-1 text-sm text-gray-500">
                        <MapPin className="h-4 w-4 mr-1" />
                        {booking.guest.location}
                      </div>
                      <div className="flex items-center mt-1 text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        Member since {new Date(booking.guest.joinDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">About</h4>
                    <p className="text-gray-600">{booking.guest.bio}</p>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Contact Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <Mail className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-gray-600">{booking.guest.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-gray-600">{booking.guest.phone}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-3">Previous Bookings</h4>
                    <div className="space-y-3">
                      {booking.guest.previousBookings.map((prevBooking, index) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">{prevBooking.property}</div>
                            <div className="text-sm text-gray-500">{new Date(prevBooking.date).toLocaleDateString()}</div>
                          </div>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="ml-1 text-sm text-gray-600">{prevBooking.rating}/5</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                      onClick={() => {
                        setShowGuestProfile(null);
                        openMessaging(booking.id);
                      }}
                    >
                      Message Guest
                    </button>
                    <button
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                      onClick={() => setShowGuestProfile(null)}
                    >
                      Close
                    </button>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}

      {/* Messaging Modal */}
      {showMessaging && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
            {(() => {
              const booking = mockBookings.find(b => b.id === showMessaging);
              if (!booking) return null;

              return (
                <>
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Message {booking.guest.name}</h2>
                      <p className="text-gray-600">Booking #{booking.id} - {booking.property}</p>
                    </div>
                    <button
                      onClick={() => setShowMessaging(null)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>

                  <div className="flex-1 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50 min-h-[300px]">
                    <div className="space-y-4">
                      {(bookingMessages[booking.id] || []).map((message, index) => (
                        <div key={index} className={`flex ${message.from === 'host' ? 'justify-end' : 'justify-start'}`}>
                          <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.from === 'host'
                              ? 'bg-blue-100 text-blue-900 border border-blue-200'
                              : 'bg-gray-100 border border-gray-200 text-gray-900'
                          }`}>
                            <p className="text-sm">{message.message}</p>
                            <p className={`text-xs mt-1 ${
                              message.from === 'host' ? 'text-blue-600' : 'text-gray-500'
                            }`}>
                              {formatDateTime(message.time)}
                            </p>
                          </div>
                        </div>
                      ))}
                      {(bookingMessages[booking.id] || []).length === 0 && (
                        <div className="text-center text-gray-500 py-8">
                          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>No messages yet. Start the conversation!</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(booking.id)}
                    />
                    <button
                      onClick={() => handleSendMessage(booking.id)}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
                    >
                      <Send className="h-4 w-4" />
                    </button>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}
