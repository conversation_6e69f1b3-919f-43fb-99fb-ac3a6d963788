import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import SupabaseAuth from '../../components/auth/SupabaseAuth';

export default function SupabaseSignUp() {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirectTo = searchParams.get('redirectTo') || '/';
  const userType = searchParams.get('userType') as 'host' | 'guest' || 'guest';
  const isHost = userType === 'host';

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link to="/">
          <img
            className="mx-auto h-12 w-auto"
            src="/images/logo.svg"
            alt="HouseGoing"
          />
        </Link>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {isHost ? 'Create your Host account' : 'Create your account'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {isHost
            ? 'Start listing your properties and earn extra income'
            : 'Join HouseGoing to find and book amazing venues'}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <SupabaseAuth 
            mode="signup" 
            redirectTo={isHost ? '/host/dashboard' : redirectTo}
            userType={userType}
          />
          
          {isHost ? (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Looking to book a venue instead?{' '}
                <Link to="/signup" className="font-medium text-purple-600 hover:text-purple-500">
                  Sign up as a guest
                </Link>
              </p>
            </div>
          ) : (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Do you have a venue to share?{' '}
                <Link to="/host/signup" className="font-medium text-purple-600 hover:text-purple-500">
                  Sign up as a host
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
