import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const apiKey = process.env.LANGSMITH_API_KEY;
const projectName = process.env.LANGSMITH_PROJECT;
const orgId = process.env.LANGSMITH_ORG_ID;

console.log('Using API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');
console.log('Project Name:', projectName);
console.log('Organization ID:', orgId);

async function testLangSmithWithOrg() {
  try {
    // Test API connection by fetching runs with organization ID
    const response = await fetch(`https://api.smith.langchain.com/runs?organization_id=${orgId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey
      }
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('\nRuns found:', data.length);
    
    if (data.length > 0) {
      console.log('\nLatest run:', data[0]);
    }
    
    // Test API connection by fetching projects with organization ID
    const projectsResponse = await fetch(`https://api.smith.langchain.com/projects?organization_id=${orgId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey
      }
    });
    
    console.log('\nProjects response status:', projectsResponse.status);
    
    if (!projectsResponse.ok) {
      const errorText = await projectsResponse.text();
      throw new Error(`API request failed with status ${projectsResponse.status}: ${errorText}`);
    }
    
    const projectsData = await projectsResponse.json();
    console.log('\nProjects found:', projectsData.length);
    
    if (projectsData.length > 0) {
      console.log('\nProjects:');
      projectsData.forEach(project => {
        console.log(`- ${project.name} (${project.id})`);
      });
      
      // Check if our project exists
      const ourProject = projectsData.find(project => project.id === projectName || project.name === projectName);
      if (ourProject) {
        console.log(`\nOur project "${projectName}" found with ID: ${ourProject.id}`);
      } else {
        console.log(`\nOur project "${projectName}" not found in the list of available projects.`);
      }
    }
    
  } catch (error) {
    console.error('Error testing LangSmith API:', error);
  }
}

testLangSmithWithOrg();
