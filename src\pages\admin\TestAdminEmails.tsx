/**
 * Test Admin Emails Page
 *
 * Test all admin email notifications
 */

import React, { useState } from 'react';
import { useAdminAuth } from '../../hooks/useAdminAuth.tsx';
import {
  sendPropertySubmissionNotification,
  sendPropertyApprovalEmail,
  sendPropertyRejectionEmail,
  sendNewHostSignupNotification,
  sendNewCustomerSignupNotification
} from '../../services/adminEmailService';
import { Mail, Send, CheckCircle, AlertCircle, User, Home, Star } from 'lucide-react';

export default function TestAdminEmails() {
  const { isAdmin, adminUser, isLoading } = useAdminAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  // Test property submission notification
  const testPropertySubmission = async () => {
    setLoading(true);
    try {
      const success = await sendPropertySubmissionNotification({
        id: 'test-property-123',
        name: 'Harbour View Rooftop',
        address: '123 Circular Quay, Sydney NSW 2000',
        type: 'Rooftop',
        description: 'Beautiful rooftop venue with harbour views, perfect for parties and events.',
        maxGuests: 50,
        price: 150,
        ownerName: '<PERSON>',
        ownerEmail: '<EMAIL>',
        created_at: new Date().toISOString()
      });

      setResults(prev => [...prev, {
        type: 'Property Submission Notification',
        success,
        message: success ? 'Admin notification sent successfully' : 'Failed to send admin notification',
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error: any) {
      setResults(prev => [...prev, {
        type: 'Property Submission Notification',
        success: false,
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Test property approval email
  const testPropertyApproval = async () => {
    setLoading(true);
    try {
      const success = await sendPropertyApprovalEmail({
        id: 'test-property-123',
        name: 'Harbour View Rooftop',
        address: '123 Circular Quay, Sydney NSW 2000',
        type: 'Rooftop',
        description: 'Beautiful rooftop venue with harbour views.',
        maxGuests: 50,
        price: 150,
        ownerName: 'Sarah Johnson',
        ownerEmail: '<EMAIL>', // Send to Tom for testing
        created_at: new Date().toISOString()
      }, 'Your property meets all our quality standards and is perfect for our platform!');

      setResults(prev => [...prev, {
        type: 'Property Approval Email',
        success,
        message: success ? 'Approval email sent successfully' : 'Failed to send approval email',
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error: any) {
      setResults(prev => [...prev, {
        type: 'Property Approval Email',
        success: false,
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Test property rejection email
  const testPropertyRejection = async () => {
    setLoading(true);
    try {
      const success = await sendPropertyRejectionEmail({
        id: 'test-property-123',
        name: 'Test Venue',
        address: '456 Test Street, Sydney NSW 2000',
        type: 'House',
        description: 'Test venue description.',
        maxGuests: 30,
        price: 100,
        ownerName: 'Test Host',
        ownerEmail: '<EMAIL>', // Send to Tom for testing
        created_at: new Date().toISOString()
      }, 'We need more photos of the venue and clearer house rules before we can approve your listing.', 'Please add at least 5 high-quality photos and detailed house rules.');

      setResults(prev => [...prev, {
        type: 'Property Rejection Email',
        success,
        message: success ? 'Rejection email sent successfully' : 'Failed to send rejection email',
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error: any) {
      setResults(prev => [...prev, {
        type: 'Property Rejection Email',
        success: false,
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Test new host signup notification
  const testHostSignup = async () => {
    setLoading(true);
    try {
      const success = await sendNewHostSignupNotification({
        name: 'John Smith',
        email: '<EMAIL>',
        signupDate: new Date().toLocaleDateString()
      });

      setResults(prev => [...prev, {
        type: 'New Host Signup Notification',
        success,
        message: success ? 'Host signup notification sent successfully' : 'Failed to send host signup notification',
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error: any) {
      setResults(prev => [...prev, {
        type: 'New Host Signup Notification',
        success: false,
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Test new customer signup notification
  const testCustomerSignup = async () => {
    setLoading(true);
    try {
      const success = await sendNewCustomerSignupNotification({
        name: 'Emma Wilson',
        email: '<EMAIL>',
        signupDate: new Date().toLocaleDateString()
      });

      setResults(prev => [...prev, {
        type: 'New Customer Signup Notification',
        success,
        message: success ? 'Customer signup notification sent successfully' : 'Failed to send customer signup notification',
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error: any) {
      setResults(prev => [...prev, {
        type: 'New Customer Signup Notification',
        success: false,
        message: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Clear results
  const clearResults = () => {
    setResults([]);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Test Admin Email Notifications</h1>
          <p className="text-gray-600">
            Test all admin email notification functions to ensure they're working correctly.
          </p>

          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800">
              ✅ Logged in as admin: <strong>{adminUser?.name}</strong> ({adminUser?.email})
            </p>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <button
            onClick={testPropertySubmission}
            disabled={loading}
            className="p-6 bg-white border border-gray-200 rounded-lg hover:border-red-300 hover:shadow-md transition-all disabled:opacity-50"
          >
            <div className="flex items-center mb-3">
              <Home className="w-6 h-6 text-red-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Property Submission</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Test admin notification when a new property is submitted.
            </p>
          </button>

          <button
            onClick={testPropertyApproval}
            disabled={loading}
            className="p-6 bg-white border border-gray-200 rounded-lg hover:border-green-300 hover:shadow-md transition-all disabled:opacity-50"
          >
            <div className="flex items-center mb-3">
              <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Property Approval</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Test approval email sent to property owners.
            </p>
          </button>

          <button
            onClick={testPropertyRejection}
            disabled={loading}
            className="p-6 bg-white border border-gray-200 rounded-lg hover:border-yellow-300 hover:shadow-md transition-all disabled:opacity-50"
          >
            <div className="flex items-center mb-3">
              <AlertCircle className="w-6 h-6 text-yellow-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Property Rejection</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Test rejection email with feedback for property owners.
            </p>
          </button>

          <button
            onClick={testHostSignup}
            disabled={loading}
            className="p-6 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all disabled:opacity-50"
          >
            <div className="flex items-center mb-3">
              <User className="w-6 h-6 text-blue-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Host Signup</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Test admin notification for new host registrations.
            </p>
          </button>

          <button
            onClick={testCustomerSignup}
            disabled={loading}
            className="p-6 bg-white border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all disabled:opacity-50"
          >
            <div className="flex items-center mb-3">
              <Star className="w-6 h-6 text-purple-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Customer Signup</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Test admin notification for new customer registrations.
            </p>
          </button>
        </div>

        {/* Results Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Test Results</h2>
            {results.length > 0 && (
              <button
                onClick={clearResults}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Results
              </button>
            )}
          </div>

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mr-3"></div>
              <span className="text-gray-600">Sending email...</span>
            </div>
          )}

          {results.length === 0 && !loading ? (
            <div className="text-center py-8 text-gray-500">
              <Mail className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p>No tests run yet. Click a test button above to start.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.success
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-start">
                    {result.success ? (
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className={`font-medium ${
                          result.success ? 'text-green-900' : 'text-red-900'
                        }`}>
                          {result.type}
                        </h4>
                        <span className="text-xs text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className={`text-sm ${
                        result.success ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {result.message}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Testing Instructions</h3>
          <div className="space-y-2 text-blue-800 text-sm">
            <p>• <strong>Property Submission:</strong> Sends admin <NAME_EMAIL></p>
            <p>• <strong>Property Approval:</strong> Sends approval <NAME_EMAIL> (test)</p>
            <p>• <strong>Property Rejection:</strong> Sends rejection <NAME_EMAIL> (test)</p>
            <p>• <strong>Host Signup:</strong> Sends admin <NAME_EMAIL></p>
            <p>• <strong>Customer Signup:</strong> Sends admin <NAME_EMAIL></p>
            <p>• Check email inboxes and spam folders for test emails</p>
          </div>
        </div>
      </div>
    </div>
  );
}
