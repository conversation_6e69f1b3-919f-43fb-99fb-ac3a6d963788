import React, { useEffect, useState } from 'react';
import { SignUp as ClerkSignUp } from '@clerk/clerk-react';
import { clerkAppearance } from '../../utils/clerk-theme';
import { Link } from 'react-router-dom';
import { CLERK_CONFIG } from '../../config/clerk';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';
import OAuthButton from '../../components/auth/OAuthButton';
import GoogleButton from '../../components/auth/GoogleButton';
import { Building } from 'lucide-react';

export default function HostSignUp() {
  const [showDirectOAuth, setShowDirectOAuth] = useState(false);

  // Set localStorage flag to indicate host registration
  useEffect(() => {
    // Set flag in localStorage
    localStorage.setItem('registering_as_host', 'true');
    localStorage.setItem('user_type', 'host');
    localStorage.setItem('auth_user_type', 'host');
    console.log('HostSignUp: Set registering_as_host flag in localStorage');

    // Log configuration for debugging
    console.log('HostSignUp: Clerk config:', {
      redirectUrl: CLERK_CONFIG.oauthCallbackURL,
      hostSignUpRedirectURL: CLERK_CONFIG.redirectUrls.hostSignUp
    });

    // Simulate a webhook event for testing
    simulateWebhookEvent('page.viewed', {
      id: 'host-signup-page',
      page: 'host-signup'
    });

    // Check if we should show the direct OAuth button
    // This is a fallback in case the regular flow isn't working
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('direct') === 'true') {
      setShowDirectOAuth(true);
    }

    return () => {
      // Don't remove the flag on unmount, as we need it for the OAuth callback
    };
  }, []);
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="text-center text-3xl font-bold text-gray-900">
          Owner Portal{' '}
          <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            HouseGoing
          </span>
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Create an account to list your venues and start earning
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-xl">
        <div className="bg-white py-10 px-6 shadow-xl sm:rounded-xl sm:px-12 border border-gray-100">
          {showDirectOAuth ? (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900">Create Owner Portal Account</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose one of the following options to create your venue owner account
                </p>
              </div>

              <div className="space-y-3">
                <OAuthButton
                  provider="google"
                  registrationType="host"
                  className="w-full"
                  label="Sign up with Google"
                />

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or continue with Clerk</span>
                  </div>
                </div>

                <button
                  onClick={() => setShowDirectOAuth(false)}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Use Standard Sign Up
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Host Portal Sign Up</h1>
                <p className="text-lg text-gray-600">
                  List your venue and start earning with HouseGoing
                </p>
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-800">
                    <strong>Host Portal</strong> - Share your space and earn money hosting events
                  </p>
                </div>
              </div>

              <div className="mb-8">
                <p className="text-center text-base text-gray-600 mb-4">Sign up with:</p>
                <GoogleButton registrationType="host" label="Continue with Google" />
              </div>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center">
                  <span className="px-4 py-1 bg-white text-gray-500 text-base">Or sign up with email</span>
                </div>
              </div>

              <ClerkSignUp
                appearance={clerkAppearance}
                routing="path"
                path="/host/signup"
                redirectUrl="/auth/callback?userType=host"
                signInUrl="/host/login"
              />

              <div className="mt-4 text-center">
                <button
                  onClick={() => setShowDirectOAuth(true)}
                  className="text-sm text-purple-600 hover:text-purple-800"
                >
                  Having trouble? Try direct sign up
                </button>
              </div>
            </>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <Link to="/host/login" className="text-purple-600 hover:text-purple-800 font-medium">
                Sign in
              </Link>
            </p>
          </div>

          {/* Customer Portal Link */}
          <div className="mt-8 text-center border-t border-gray-200 pt-6">
            <p className="text-sm text-gray-600 mb-2">
              Looking to book a venue instead?
            </p>
            <Link
              to="/signup"
              className="inline-flex items-center px-4 py-2 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors"
            >
              Join as a Customer
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
