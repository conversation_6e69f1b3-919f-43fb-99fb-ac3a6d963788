/**
 * Auth Debug Helper
 * 
 * This utility helps diagnose authentication issues in production by providing
 * detailed debugging information and storing it for later analysis.
 */

/**
 * Logs authentication flow events for debugging purposes
 */
export function logAuthEvent(event: string, data?: any): void {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    event,
    data: data || {}
  };
  
  console.log(`[Auth Debug] ${event}`, data);
  
  // Store in localStorage for later retrieval
  try {
    const authLogs = JSON.parse(localStorage.getItem('auth_debug_logs') || '[]');
    authLogs.push(logEntry);
    
    // Keep only the last 50 entries to avoid localStorage size issues
    if (authLogs.length > 50) {
      authLogs.shift();
    }
    
    localStorage.setItem('auth_debug_logs', JSON.stringify(authLogs));
  } catch (error) {
    console.error('Failed to store auth debug log:', error);
  }
}

/**
 * Captures information about the current auth state
 */
export function captureAuthState(): Record<string, any> {
  const storageKeys = [
    'clerk_user_id',
    'clerk_user_email',
    'clerk_user_name',
    'clerk_auth_time',
    'user_role',
    'registering_as_host',
    'auth_success',
    'auth_success_time',
    'auth_timestamp',
    'auth_fallback_used',
    'auth_fallback_timestamp',
    'google_oauth_flow',
    'temp_user_id',
    'temp_user_email'
  ];
  
  const state: Record<string, any> = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent
  };
  
  // Collect localStorage values
  storageKeys.forEach(key => {
    state[key] = localStorage.getItem(key);
  });
  
  return state;
}

/**
 * Gets the current authentication debug logs
 */
export function getAuthDebugLogs(): any[] {
  try {
    return JSON.parse(localStorage.getItem('auth_debug_logs') || '[]');
  } catch (error) {
    console.error('Failed to parse auth debug logs:', error);
    return [];
  }
}

/**
 * Checks for authentication-related issues based on the current state
 */
export function checkAuthIssues(): {
  hasIssues: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  // Check for common auth issues
  if (!localStorage.getItem('clerk_user_id')) {
    issues.push('No clerk_user_id in localStorage');
  }
  
  if (localStorage.getItem('auth_fallback_used') === 'true') {
    issues.push('Using authentication fallback');
  }
  
  if (!localStorage.getItem('auth_success')) {
    issues.push('No auth_success flag set');
  }
  
  const authTime = localStorage.getItem('auth_timestamp') || localStorage.getItem('auth_success_time');
  if (authTime) {
    const timeDiff = Date.now() - new Date(authTime).getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);
    
    if (hoursDiff > 24) {
      issues.push(`Auth is ${hoursDiff.toFixed(1)} hours old, might need refresh`);
    }
  } else {
    issues.push('No auth timestamp found');
  }
  
  return {
    hasIssues: issues.length > 0,
    issues
  };
}

/**
 * Attempts to fix common authentication issues
 */
export function attemptAuthFix(): {
  attempted: boolean;
  fixed: boolean;
  action: string;
} {
  // Only attempt a fix if there are actual issues
  const { hasIssues, issues } = checkAuthIssues();
  
  if (!hasIssues) {
    return {
      attempted: false,
      fixed: false,
      action: 'No issues detected, no fix attempted'
    };
  }
  
  logAuthEvent('attempting_auth_fix', { issues });
  
  // If using fallback authentication, try to improve the state
  if (localStorage.getItem('auth_fallback_used') === 'true') {
    const tempEmail = localStorage.getItem('temp_user_email');
    
    if (tempEmail && !localStorage.getItem('clerk_user_email')) {
      localStorage.setItem('clerk_user_email', tempEmail);
      return {
        attempted: true,
        fixed: true,
        action: 'Set clerk_user_email from temp_user_email'
      };
    }
  }
  
  // Add more fix attempts here as needed
  
  return {
    attempted: true,
    fixed: false,
    action: 'Attempted fixes but could not resolve issues'
  };
}

/**
 * Generates a comprehensive auth debug report
 */
export function generateAuthDebugReport(): Record<string, any> {
  const authState = captureAuthState();
  const authLogs = getAuthDebugLogs();
  const { hasIssues, issues } = checkAuthIssues();
  
  return {
    timestamp: new Date().toISOString(),
    authState,
    authLogs,
    authIssues: {
      hasIssues,
      issues
    },
    browser: {
      userAgent: navigator.userAgent,
      language: navigator.language,
      cookiesEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    },
    url: window.location.href,
    referrer: document.referrer
  };
}
