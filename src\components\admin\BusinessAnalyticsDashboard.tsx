/**
 * Business Analytics Dashboard - <PERSON> Inspired
 *
 * Key Growth Metrics Dashboard based on <PERSON>'s business principles:
 * - Customer Acquisition Cost (CAC) vs Lifetime Value (LTV)
 * - Revenue Growth and Unit Economics
 * - Customer Segments and Retention
 * - Host Performance and Supply Metrics
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Target,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import {
  getBusinessMetrics,
  getCustomerSegments,
  getTopHosts,
  BusinessMetrics,
  CustomerSegment,
  HostPerformance
} from '../../services/businessAnalytics';

export default function BusinessAnalyticsDashboard() {
  const [metrics, setMetrics] = useState<BusinessMetrics | null>(null);
  const [customerSegments, setCustomerSegments] = useState<CustomerSegment[]>([]);
  const [topHosts, setTopHosts] = useState<HostPerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'customers' | 'hosts' | 'growth'>('overview');

  useEffect(() => {
    // Immediately load mock data for development
    console.log('📊 Loading Alex Hormozi-inspired analytics dashboard...');

    // Simulate loading for 1 second then show mock data
    setTimeout(() => {
      console.log('✅ Analytics dashboard loaded with sample data');
      setMetrics(getMockMetrics());
      setCustomerSegments(getMockCustomerSegments());
      setTopHosts(getMockTopHosts());
      setLoading(false);
    }, 1000);
  }, []);

  const loadAnalytics = () => {
    setLoading(true);
    setTimeout(() => {
      setMetrics(getMockMetrics());
      setCustomerSegments(getMockCustomerSegments());
      setTopHosts(getMockTopHosts());
      setLoading(false);
    }, 500);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getHealthStatus = (ltv: number, cac: number) => {
    const ratio = ltv / cac;
    if (ratio >= 3) return { status: 'healthy', color: 'text-green-600', icon: CheckCircle };
    if (ratio >= 2) return { status: 'warning', color: 'text-yellow-600', icon: AlertTriangle };
    return { status: 'critical', color: 'text-red-600', icon: AlertTriangle };
  };

  if (loading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          <p className="ml-3 text-purple-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6">
        <p className="text-center text-gray-500">Unable to load analytics data</p>
      </div>
    );
  }

  const healthStatus = getHealthStatus(metrics.customerLifetimeValue, metrics.customerAcquisitionCost);
  const HealthIcon = healthStatus.icon;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Business Analytics</h2>
            <p className="text-gray-600">Alex Hormozi-inspired growth metrics</p>
            <div className="mt-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm inline-block">
              📊 Sample Data - Ready for Real Integration
            </div>
          </div>
          <button
            onClick={loadAnalytics}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md"
          >
            Refresh Data
          </button>
        </div>

        {/* Tabs */}
        <div className="flex mt-6 border-b">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'customers', label: 'Customers', icon: Users },
            { id: 'hosts', label: 'Hosts', icon: Target },
            { id: 'growth', label: 'Growth', icon: TrendingUp }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                className={`flex items-center px-4 py-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'text-purple-600 border-b-2 border-purple-600'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                onClick={() => setActiveTab(tab.id as any)}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Revenue */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.totalRevenue)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="mt-2 flex items-center">
                {metrics.revenueGrowthRate >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${metrics.revenueGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercentage(Math.abs(metrics.revenueGrowthRate))} vs last month
                </span>
              </div>
            </div>

            {/* LTV:CAC Ratio */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">LTV:CAC Ratio</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {metrics.customerAcquisitionCost > 0
                      ? (metrics.customerLifetimeValue / metrics.customerAcquisitionCost).toFixed(1)
                      : 'N/A'
                    }:1
                  </p>
                </div>
                <HealthIcon className={`h-8 w-8 ${healthStatus.color}`} />
              </div>
              <div className="mt-2">
                <span className={`text-sm ${healthStatus.color}`}>
                  {healthStatus.status === 'healthy' && 'Excellent unit economics'}
                  {healthStatus.status === 'warning' && 'Good, room for improvement'}
                  {healthStatus.status === 'critical' && 'Needs immediate attention'}
                </span>
              </div>
            </div>

            {/* Active Customers */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Customers</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.activeCustomers}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="mt-2">
                <span className="text-sm text-gray-600">
                  {formatPercentage(metrics.bookingConversionRate)} conversion rate
                </span>
              </div>
            </div>

            {/* Profit Margin */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Profit Margin</p>
                  <p className="text-2xl font-bold text-gray-900">{formatPercentage(metrics.profitMargin)}</p>
                </div>
                <Activity className="h-8 w-8 text-purple-600" />
              </div>
              <div className="mt-2">
                <span className="text-sm text-gray-600">
                  {formatCurrency(metrics.platformCommission)} commission
                </span>
              </div>
            </div>
          </div>

          {/* Detailed Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Metrics */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">Customer Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Customer Acquisition Cost</span>
                  <span className="font-medium">{formatCurrency(metrics.customerAcquisitionCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Customer Lifetime Value</span>
                  <span className="font-medium">{formatCurrency(metrics.customerLifetimeValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Churn Rate</span>
                  <span className="font-medium">{formatPercentage(metrics.churnRate)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Repeat Booking Rate</span>
                  <span className="font-medium">{formatPercentage(metrics.repeatBookingRate)}</span>
                </div>
              </div>
            </div>

            {/* Host Metrics */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">Host Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Hosts</span>
                  <span className="font-medium">{metrics.totalHosts}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Hosts</span>
                  <span className="font-medium">{metrics.activeHosts}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Average Host Revenue</span>
                  <span className="font-medium">{formatCurrency(metrics.averageHostRevenue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Host Retention Rate</span>
                  <span className="font-medium">{formatPercentage(metrics.hostRetentionRate)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Customer Segments Tab */}
      {activeTab === 'customers' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-6">Customer Segments</h3>
          {customerSegments.length === 0 ? (
            <p className="text-center text-gray-500 py-8">No customer segment data available</p>
          ) : (
            <div className="space-y-4">
              {customerSegments.map((segment, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-lg">{segment.segment} Customers</h4>
                    <span className="text-sm text-gray-500">{segment.count} customers</span>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Total Revenue:</span>
                      <p className="font-medium">{formatCurrency(segment.revenue)}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Average Value:</span>
                      <p className="font-medium">{formatCurrency(segment.averageValue)}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Retention Rate:</span>
                      <p className="font-medium">{formatPercentage(segment.retentionRate)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Top Hosts Tab */}
      {activeTab === 'hosts' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-6">Top Performing Hosts</h3>
          {topHosts.length === 0 ? (
            <p className="text-center text-gray-500 py-8">No host performance data available</p>
          ) : (
            <div className="space-y-4">
              {topHosts.map((host, index) => (
                <div key={host.hostId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center">
                      <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2 py-1 rounded-full mr-3">
                        #{index + 1}
                      </span>
                      <h4 className="font-medium text-lg">{host.hostName}</h4>
                    </div>
                    <span className="text-lg font-bold text-green-600">{formatCurrency(host.totalRevenue)}</span>
                  </div>
                  <div className="grid grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Bookings:</span>
                      <p className="font-medium">{host.totalBookings}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Rating:</span>
                      <p className="font-medium">{host.averageRating.toFixed(1)}/5</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Response Rate:</span>
                      <p className="font-medium">{formatPercentage(host.responseRate)}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Listings:</span>
                      <p className="font-medium">{host.listingCount}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Growth Tab */}
      {activeTab === 'growth' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-6">Growth Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium">Revenue Growth</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span>Month over Month</span>
                  <span className={`font-bold ${metrics.monthOverMonthGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(metrics.monthOverMonthGrowth)}
                  </span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span>Year over Year</span>
                  <span className={`font-bold ${metrics.yearOverYearGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(metrics.yearOverYearGrowth)}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Key Ratios</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span>LTV:CAC Ratio</span>
                  <span className="font-bold">
                    {metrics.customerAcquisitionCost > 0
                      ? (metrics.customerLifetimeValue / metrics.customerAcquisitionCost).toFixed(1)
                      : 'N/A'
                    }:1
                  </span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span>Profit Margin</span>
                  <span className="font-bold">{formatPercentage(metrics.profitMargin)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Mock data functions for development/fallback
function getMockMetrics(): BusinessMetrics {
  return {
    totalRevenue: 45000,
    monthlyRecurringRevenue: 12000,
    averageOrderValue: 350,
    revenueGrowthRate: 15.5,
    totalCustomers: 234,
    newCustomers: 28,
    activeCustomers: 156,
    customerAcquisitionCost: 85,
    customerLifetimeValue: 420,
    churnRate: 8.2,
    totalHosts: 67,
    activeHosts: 45,
    averageHostRevenue: 1200,
    hostRetentionRate: 78.5,
    totalBookings: 189,
    bookingConversionRate: 12.8,
    averageBookingValue: 350,
    repeatBookingRate: 35.6,
    platformCommission: 4500,
    operationalCosts: 13500,
    profitMargin: 22.2,
    monthOverMonthGrowth: 15.5,
    yearOverYearGrowth: 145.8,
    calculatedAt: new Date().toISOString()
  };
}

function getMockCustomerSegments(): CustomerSegment[] {
  return [
    {
      segment: 'High Value',
      count: 23,
      revenue: 18500,
      averageValue: 804,
      retentionRate: 85.2
    },
    {
      segment: 'Regular',
      count: 89,
      revenue: 22100,
      averageValue: 248,
      retentionRate: 67.4
    },
    {
      segment: 'One-time',
      count: 122,
      revenue: 4400,
      averageValue: 36,
      retentionRate: 12.3
    }
  ];
}

function getMockTopHosts(): HostPerformance[] {
  return [
    {
      hostId: 'host-1',
      hostName: 'Sarah Johnson',
      totalBookings: 45,
      totalRevenue: 8900,
      averageRating: 4.8,
      responseRate: 98,
      listingCount: 3
    },
    {
      hostId: 'host-2',
      hostName: 'Michael Chen',
      totalBookings: 38,
      totalRevenue: 7200,
      averageRating: 4.6,
      responseRate: 95,
      listingCount: 2
    },
    {
      hostId: 'host-3',
      hostName: 'Emma Wilson',
      totalBookings: 32,
      totalRevenue: 6100,
      averageRating: 4.9,
      responseRate: 97,
      listingCount: 2
    },
    {
      hostId: 'host-4',
      hostName: 'David Brown',
      totalBookings: 28,
      totalRevenue: 5400,
      averageRating: 4.5,
      responseRate: 92,
      listingCount: 1
    },
    {
      hostId: 'host-5',
      hostName: 'Lisa Taylor',
      totalBookings: 25,
      totalRevenue: 4800,
      averageRating: 4.7,
      responseRate: 94,
      listingCount: 1
    }
  ];
}
