import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth, useUser } from '@clerk/clerk-react';
import RegisterAsHost from '../../components/host/RegisterAsHost';
import {
  Building,
  DollarSign,
  Clock,
  Shield,
  Sparkles,
  Users,
  ArrowRight,
  CheckCircle,
  Warehouse,
  Home,
  Landmark,
  Music,
  Calendar
} from 'lucide-react';

export default function OwnerLanding() {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const navigate = useNavigate();

  // Enhanced check for host status - check multiple sources
  const userIsHost = user && (
    user.publicMetadata?.role === 'host' ||
    user.publicMetadata?.isHost === true ||
    user.unsafeMetadata?.role === 'host' ||
    user.unsafeMetadata?.isHost === true
  );

  // Auto-redirect authenticated hosts to dashboard
  useEffect(() => {
    if (isLoaded && isSignedIn && userIsHost) {
      console.log('OwnerLanding: Authenticated host detected, redirecting to dashboard');
      navigate('/host/dashboard');
    }
  }, [isLoaded, isSignedIn, userIsHost, navigate]);

  // Scroll to the sign-up section
  const scrollToSignUp = () => {
    const element = document.getElementById('sign-up-section');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section - Simplified with clear value proposition */}
      <section className="bg-white pt-32 pb-16 px-4 sm:px-6 border-b border-gray-200">
        <div className="max-w-5xl mx-auto">
          <div className="flex flex-col items-center text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-medium text-gray-900 mb-4 w-full">
              Risk-Free: $0 to List. We Only Succeed When You Do.
            </h1>
            <p className="text-xl text-gray-600 mb-8 w-full max-w-3xl mx-auto">
              List your space in minutes. No upfront costs, no commitments. HouseGoing only takes a service fee if you get a booking—so you never pay out of pocket.
            </p>
            <button
              onClick={scrollToSignUp}
              className="px-10 py-4 bg-purple-600 text-white rounded-md font-medium hover:bg-purple-700 transition-colors w-full sm:w-auto text-lg shadow-lg"
            >
              Start Hosting for Free
            </button>
          </div>

          {/* Key stats in a clean, accessible format */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
            <div className="bg-white p-4 rounded-md border border-gray-200">
              <div className="flex items-center mb-2">
                <DollarSign className="h-5 w-5 text-purple-600 mr-2" />
                <span className="text-lg font-medium text-gray-900">$1,200</span>
              </div>
              <p className="text-sm text-gray-600">Avg. Monthly Earnings</p>
            </div>

            <div className="bg-white p-4 rounded-md border border-gray-200">
              <div className="flex items-center mb-2">
                <Calendar className="h-5 w-5 text-purple-600 mr-2" />
                <span className="text-lg font-medium text-gray-900">8.5 hrs</span>
              </div>
              <p className="text-sm text-gray-600">Avg. Booking Duration</p>
            </div>

            <div className="bg-white p-4 rounded-md border border-gray-200">
              <div className="flex items-center mb-2">
                <Clock className="h-5 w-5 text-purple-600 mr-2" />
                <span className="text-lg font-medium text-gray-900">3-4 hrs</span>
              </div>
              <p className="text-sm text-gray-600">Monthly Time Investment</p>
            </div>

            <div className="bg-white p-4 rounded-md border border-gray-200">
              <div className="flex items-center mb-2">
                <Users className="h-5 w-5 text-purple-600 mr-2" />
                <span className="text-lg font-medium text-gray-900">98%</span>
              </div>
              <p className="text-sm text-gray-600">Guest Satisfaction</p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Benefits Section - Focused on the key benefits */}
      <section className="py-16 px-4 sm:px-6 bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-medium text-gray-900 mb-8 text-center">Why Host with HouseGoing?</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Benefit 1 */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <div className="bg-purple-50 p-2 rounded-md mr-4">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">$0 Listing Fee</h3>
              </div>
              <p className="text-gray-600">
                Always free to list your space.
              </p>
            </div>

            {/* Benefit 2 */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <div className="bg-purple-50 p-2 rounded-md mr-4">
                  <Clock className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Your Schedule</h3>
              </div>
              <p className="text-gray-600">
                Host only when you want—total flexibility.
              </p>
            </div>

            {/* Benefit 3 */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <div className="bg-purple-50 p-2 rounded-md mr-4">
                  <Shield className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">No Upfront Risk</h3>
              </div>
              <p className="text-gray-600">
                Only pay a small fee if you get booked—nothing to lose.
              </p>
            </div>

            {/* Benefit 4 */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-start mb-4">
                <div className="bg-purple-50 p-2 rounded-md mr-4">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Hosts Are In Control</h3>
              </div>
              <p className="text-gray-600">
                Set your own price, rules, and approve every booking request.
              </p>
            </div>
          </div>

          {/* FAQ Snippet */}
          <div className="mt-12 bg-purple-50 p-6 rounded-lg border border-purple-100">
            <h3 className="text-lg font-medium text-gray-900 mb-3">"What's the catch?"</h3>
            <p className="text-gray-600">
              There isn't one. HouseGoing only earns a small commission after your space gets booked. No surprises. No lock-in contracts. Zero risk.
            </p>
          </div>
        </div>
      </section>

      {/* Space Types Section - Simplified */}
      <section className="py-12 px-4 sm:px-6 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <h2 className="text-2xl font-medium text-gray-900 mb-4 text-center">Any Space Can Earn</h2>
            <p className="text-gray-600 text-center mb-8 max-w-2xl mx-auto">
              From warehouses to backyards, studios to rooftops — if you have space, we have guests looking to book it.
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-4 rounded-md text-center">
                <Warehouse className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900">Warehouses</h3>
              </div>

              <div className="bg-gray-50 p-4 rounded-md text-center">
                <Home className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900">Homes</h3>
              </div>

              <div className="bg-gray-50 p-4 rounded-md text-center">
                <Landmark className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900">Studios</h3>
              </div>

              <div className="bg-gray-50 p-4 rounded-md text-center">
                <Music className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900">Event Spaces</h3>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Sign Up Section - Clear CTA */}
      <section id="sign-up-section" className="py-16 px-4 sm:px-6 bg-white border-t border-gray-200">
        <div className="max-w-md mx-auto">
          {!isSignedIn ? (
            // Not signed in - show regular sign up
            <>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-medium text-gray-900 mb-4">Start earning extra income from your unused space, risk-free.</h2>
                <p className="text-gray-600 mb-2">
                  It's only a win if you win.
                </p>
              </div>

              <div className="space-y-4">
                <Link
                  to="/host/signup"
                  className="block w-full px-6 py-4 bg-purple-600 text-white rounded-md font-medium hover:bg-purple-700 transition-colors text-center text-lg shadow-md"
                >
                  Create Your Free Listing
                </Link>
                <Link
                  to="/host/login"
                  className="block w-full px-6 py-3 bg-white text-gray-700 border border-gray-300 rounded-md font-medium hover:bg-gray-50 transition-colors text-center"
                >
                  Sign In
                </Link>
              </div>

              <p className="text-xs text-gray-500 text-center mt-6">
                By continuing, you agree to our <Link to="/terms" className="underline">Terms of Service</Link> and <Link to="/privacy" className="underline">Privacy Policy</Link>
              </p>
            </>
          ) : userIsHost ? (
            // Signed in and already a host - show dashboard link
            <div className="text-center">
              <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-medium text-green-800 mb-2">Welcome back, {user?.firstName}!</h2>
                <p className="text-green-700">You're already set up as a host. Ready to manage your listings?</p>
              </div>

              <Link
                to="/host/dashboard"
                className="block w-full px-6 py-4 bg-purple-600 text-white rounded-md font-medium hover:bg-purple-700 transition-colors text-center text-lg shadow-md"
              >
                Go to Host Dashboard
              </Link>
            </div>
          ) : (
            // Signed in but not a host - show upgrade option
            <div className="text-center">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-medium text-blue-800 mb-2">Hi {user?.firstName}!</h2>
                <p className="text-blue-700 mb-4">
                  You're signed in! Now let's set up your host account so you can start listing venues.
                </p>
                <div className="bg-green-100 border border-green-300 rounded-md p-3">
                  <p className="text-green-800 text-sm">
                    ✅ Already signed in - just need to upgrade to host status
                  </p>
                </div>
              </div>

              <RegisterAsHost />

              <p className="text-xs text-gray-500 mt-4">
                This will upgrade your account and redirect you to the host dashboard.
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
