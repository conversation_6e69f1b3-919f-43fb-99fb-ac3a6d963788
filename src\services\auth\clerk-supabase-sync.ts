/**
 * Clerk-Supabase Synchronization Service
 *
 * This service handles the synchronization of user data between Clerk and Supabase.
 * It ensures that when a user authenticates with <PERSON>, their data is properly
 * synchronized with <PERSON>pa<PERSON> for backend operations.
 */

import { User as ClerkUser } from '@clerk/clerk-js';
// Correcting the import to use the official Clerk package
import { User as ClerkUser } from '@clerk/types';

// Correcting the import for UserRole
import { UserRole } from './roles';

// Import the Supabase client and the official integration
import { createClient, SupabaseClient } from '@supabase/supabase-js';

/**
 * Synchronize a Clerk user with Supabase
 * This should be called after authentication or when user data changes
 */
export async function syncClerkUserWithSupabase(
  clerkUser: ClerkUser,
  role?: UserRole
): Promise<any> {
  try {
    if (!clerkUser) {
      console.error('Cannot sync null user with Supabase');
      return null;
    }

    const clerkId = clerkUser.id;
    const email = clerkUser.primaryEmailAddress?.emailAddress || '';

    if (!email) {
      console.error('User has no email address, cannot sync with <PERSON><PERSON><PERSON>');
      return null;
    }

    console.log(`Syncing Clerk user ${email} (${clerkId}) with Supabase...`);

    // Check for user type in localStorage

    // Check if the user_profiles table exists
    let existingProfile = null;
    try {
      // First check if the table exists
      const { error: tableCheckError } = await clerkSupabase
        .from('user_profiles')
        .select('count(*)', { count: 'exact', head: true });

      // If table doesn't exist, log it but continue
      if (tableCheckError && tableCheckError.code === '42P01') {
        console.warn('user_profiles table does not exist, will attempt to create it during sync');
      } else {
        // Table exists, check for user profile
        const { data: profile, error: fetchError } = await clerkSupabase
          .from('user_profiles')
          .select('*')
          .eq('clerk_id', clerkId)
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error fetching user profile:', fetchError);
        } else if (profile) {
          existingProfile = profile;
        }
      }
    } catch (error) {
      console.error('Exception checking for user profile:', error);
      // Continue with sync even if profile check fails
    }

    // Determine role with priority:
    // 1. Explicitly provided role parameter
    // 2. Role from auth_user_type in localStorage
    // 3. Role based on registering_as_host in localStorage
    // 4. Existing profile role
    // 5. Role from Clerk metadata
    // 6. Default to 'guest'
    const userRole = determineUserRole(clerkUser, existingProfile, {
      registering_as_host: localStorage.getItem('registering_as_host') || undefined,
      auth_user_type: localStorage.getItem('auth_user_type') as UserRole
    });

    // Prepare user data for upsert
    const userData = {
      clerk_id: clerkId,
      email: email,
      role: userRole,
      first_name: clerkUser.firstName || '',
      last_name: clerkUser.lastName || '',
      is_host: userRole === 'host',
      updated_at: new Date().toISOString(),
      ...(existingProfile ? {} : { created_at: new Date().toISOString() })
    };

    console.log('Upserting user data in Supabase:', userData);

    // Try to upsert user in Supabase
    let data;
    try {
      const result = await clerkSupabase
        .from('user_profiles')
        .upsert(userData, {
          onConflict: 'clerk_id'
        })
        .select()
        .single();

      data = result.data;
    } catch (error) {
      console.error('Error during upsert:', error);
      data = null;
    }

    if (data) {
      // If the role in Clerk doesn't match the role in Supabase, update Clerk
      if (clerkUser.publicMetadata?.role !== userRole) {
        try {
          await clerkUser.update({
            publicMetadata: {
              ...clerkUser.publicMetadata,
              role: userRole
            }
          });
          console.log(`Updated Clerk user role to ${userRole}`);
        } catch (updateError) {
          console.error('Error updating Clerk user metadata:', updateError);
        }
      }

      // Set a flag in localStorage to indicate successful authentication
      localStorage.setItem('auth_success', 'true');
      localStorage.setItem('auth_success_time', new Date().toISOString());

      return data;
    } else {
      return {
        id: null,
        clerk_id: clerkId,
        email: email,
        role: userRole
      };
    }
  } catch (error) {
    console.error('Error in syncClerkUserWithSupabase:', error);
    return null;
  }
}

/**
 * Create a Supabase session from a Clerk user
 * This is a simplified version that doesn't try to create Supabase auth sessions
 * Instead, it just creates a user profile in the database
 */
export async function createSupabaseSessionFromClerk(clerkUser: ClerkUser): Promise<boolean> {
  try {
    if (!clerkUser) {
      console.error('Cannot create Supabase session for null user');
      return false;
    }

    const email = clerkUser.primaryEmailAddress?.emailAddress;
    if (!email) {
      console.error('Cannot create Supabase session for user without email');
      return false;
    }

    console.log('Syncing Clerk user with Supabase database:', email);

    // Just sync the user profile to the database
    const result = await syncClerkUserWithSupabase(clerkUser);

    if (result) {
      console.log('Successfully synced user profile to Supabase');

      // Set auth success flags
      localStorage.setItem('auth_success', 'true');
      localStorage.setItem('auth_success_time', new Date().toISOString());

      // Dispatch auth complete event
      window.dispatchEvent(new CustomEvent('auth_complete', {
        detail: { success: true, provider: 'clerk-supabase' }
      }));

      return true;
    } else {
      console.error('Failed to sync user profile to Supabase');
      return false;
    }
  } catch (error) {
    console.error('Error in createSupabaseSessionFromClerk:', error);
    return false;
  }
}

/**
 * Initialize the Clerk-Supabase synchronization
 * This should be called when the app starts
 */
export function initClerkSupabaseSync(): void {
  // Add event listeners for Clerk authentication events
  document.addEventListener('ClerkLoaded', () => {
    console.log('Clerk loaded, initializing Clerk-Supabase sync');
  });

  // Listen for auth_success events from localStorage
  const checkAuthSuccess = () => {
    const authSuccess = localStorage.getItem('auth_success');
    if (authSuccess === 'true') {
      console.log('Auth success flag found in localStorage');

      // Get user info from localStorage
      const email = localStorage.getItem('clerk_user_email');
      const firstName = localStorage.getItem('first_name');
      const lastName = localStorage.getItem('last_name');
      const clerkId = localStorage.getItem('clerk_user_id');
      const role = localStorage.getItem('auth_user_type') || 'guest';

      // Remove the flag to prevent repeated checks
      localStorage.removeItem('auth_success');

      // Dispatch a custom event to notify the app that authentication is complete
      window.dispatchEvent(new CustomEvent('auth_complete', {
        detail: {
          success: true,
          provider: 'clerk-supabase',
          user: {
            id: clerkId || `temp-${Date.now()}`,
            email: email || '',
            first_name: firstName || '',
            last_name: lastName || '',
            name: `${firstName || ''} ${lastName || ''}`.trim() || (email ? email.split('@')[0] : ''),
            role: role
          }
        }
      }));
    }
  };

  // Check for auth success flag every second for 15 seconds
  const authSuccessInterval = setInterval(checkAuthSuccess, 1000);
  setTimeout(() => clearInterval(authSuccessInterval), 15000);

  // Initial check
  checkAuthSuccess();
}

// Define the type for clerkSupabase
let clerkSupabase: SupabaseClient;

// Function to get a Clerk session token
async function getClerkSessionToken(): Promise<string | null> {
  try {
    if (typeof window !== 'undefined' && window.Clerk?.session) {
      console.log('Getting Clerk token for Supabase...');
      // Get standard token from Clerk session - no template needed for native integration
      const token = await window.Clerk.session.getToken();
      console.log('Clerk token retrieved successfully:', token ? 'Token available' : 'No token');
      return token;
    } else {
      console.warn('Clerk session not available');
      return null;
    }
  } catch (error) {
    console.error('Error getting Clerk session token:', error);
    return null;
  }
}

// Function to initialize Supabase client with Clerk authentication
async function initializeClerkSupabaseClient(): Promise<SupabaseClient> {
  try {
    // Create basic client
    console.log('Creating basic Supabase client...');
    const client = createClient(
      import.meta.env.VITE_SUPABASE_URL || '',
      import.meta.env.VITE_SUPABASE_ANON_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
          detectSessionInUrl: false
        }
      }
    );

    // Get token and set session if available
    const token = await getClerkSessionToken();
    if (token) {
      console.log('Setting Supabase session with Clerk token...');
      try {
        await client.auth.setSession({
          access_token: token,
          refresh_token: ''
        });
        console.log('Supabase session set successfully');
      } catch (sessionError) {
        console.error('Error setting Supabase session:', sessionError);
      }
    } else {
      console.warn('No Clerk token available - using anonymous Supabase client');
    }

    return client;
  } catch (error) {
    console.error('Error initializing Clerk-Supabase client:', error);
    throw new Error('Failed to initialize Clerk-Supabase client');
  }
}

// Improve role determination logic
function determineUserRole(
  clerkUser: ClerkUser,
  existingProfile: { role?: UserRole } | null,
  localStorageData: { registering_as_host?: string; auth_user_type?: UserRole }
): UserRole {
  const { role: roleFromMetadata } = clerkUser.publicMetadata || {};
  const { registering_as_host, auth_user_type } = localStorageData;

  if (auth_user_type) return auth_user_type;
  if (registering_as_host === 'true') return 'host';
  if (existingProfile?.role) return existingProfile.role;
  if (roleFromMetadata) return roleFromMetadata;
  return 'guest';
}

// Initialize clerkSupabase with authenticated client
let initializationAttempted = false;

// Initialize Clerk-Supabase client
async function initializeClerkSupabase() {
  if (initializationAttempted) return;
  initializationAttempted = true;

  try {
    // Check if required environment variables are set
    if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
      throw new Error('Missing Supabase environment variables');
    }

    // Initialize the client
    clerkSupabase = await initializeClerkSupabaseClient();
    console.log('Clerk-Supabase client initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Clerk-Supabase client:', error);
    throw new Error('Clerk-Supabase client initialization failed');
  }
}

// Initialize on first use
initializeClerkSupabase();
