/**
 * Admin Authentication Hook
 *
 * Manages admin user authentication and permissions
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../providers/AuthProvider';
import { isDevelopmentMode, createMockUser } from '../config/clerk';

// Admin email addresses
const ADMIN_EMAILS = [
  '<EMAIL>'
];

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'admin';
  permissions: string[];
}

export function useAdminAuth() {
  const { user, isLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [checkingAdmin, setCheckingAdmin] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      setCheckingAdmin(true);

      try {
        // In development mode, bypass auth and create a mock admin user immediately
        if (isDevelopmentMode()) {
          console.log('🔧 Development mode: Creating mock admin user');
          setIsAdmin(true);
          setAdminUser({
            id: 'dev-admin-001',
            email: '<EMAIL>',
            name: 'Development Admin',
            role: 'admin',
            permissions: [
              'view_submissions',
              'approve_properties',
              'reject_properties',
              'manage_users',
              'view_analytics',
              'send_notifications'
            ]
          });
          setCheckingAdmin(false);
          return;
        }

        // Production mode: wait for auth to load
        if (isLoading) {
          return; // Still loading, don't set checkingAdmin to false yet
        }

        if (!user || !user.email) {
          setIsAdmin(false);
          setAdminUser(null);
          setCheckingAdmin(false);
          return;
        }

        // Check if user email is in admin list
        const userIsAdmin = ADMIN_EMAILS.includes(user.email.toLowerCase());

        if (userIsAdmin) {
          setIsAdmin(true);
          setAdminUser({
            id: user.id,
            email: user.email,
            name: user.first_name && user.last_name
              ? `${user.first_name} ${user.last_name}`
              : user.email,
            role: 'admin',
            permissions: [
              'view_submissions',
              'approve_properties',
              'reject_properties',
              'manage_users',
              'view_analytics',
              'send_notifications'
            ]
          });
        } else {
          setIsAdmin(false);
          setAdminUser(null);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
        setAdminUser(null);
      } finally {
        if (!isDevelopmentMode()) {
          setCheckingAdmin(false);
        }
      }
    };

    checkAdminStatus();
  }, [user, isLoading]);

  // Check if admin has specific permission
  const hasPermission = (permission: string): boolean => {
    return adminUser?.permissions.includes(permission) || false;
  };

  // Check if user can approve properties
  const canApproveProperties = (): boolean => {
    return hasPermission('approve_properties');
  };

  // Check if user can reject properties
  const canRejectProperties = (): boolean => {
    return hasPermission('reject_properties');
  };

  // Check if user can manage users
  const canManageUsers = (): boolean => {
    return hasPermission('manage_users');
  };

  // Check if user can view analytics
  const canViewAnalytics = (): boolean => {
    return hasPermission('view_analytics');
  };

  return {
    isAdmin,
    adminUser,
    isLoading: isLoading || checkingAdmin,
    hasPermission,
    canApproveProperties,
    canRejectProperties,
    canManageUsers,
    canViewAnalytics
  };
}

// Admin route protection component
export function AdminRoute({ children }: { children: React.ReactNode }) {
  const { isAdmin, isLoading } = useAdminAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You don't have permission to access the admin dashboard.
          </p>
          <p className="text-sm text-gray-500">
            Only authorized administrators can access this area.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

export default useAdminAuth;
