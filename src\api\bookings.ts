import { getSupabaseClient, isSupabaseAvailable } from '../services/api';
import { mockBookings, mockVenues, mockUsers } from '../services/api';
import { BookingData } from '../components/booking/BookingForm';
import { checkVenueAvailability as checkAvailabilityNew } from './availability';

/**
 * Create a new booking in the database
 * This function checks availability before creating the booking to prevent double bookings
 */
export async function createBooking(bookingData: BookingData, userId: string, venueId: string) {
  try {
    // Format the date and time for database storage
    const startDate = bookingData.startDate;
    const endDate = bookingData.endDate || bookingData.startDate;
    const startTime = bookingData.startTime;
    const endTime = bookingData.endTime;

    // Format start and end datetimes
    const startDateTime = `${startDate}T${startTime}:00Z`;
    const endDateTime = `${endDate || startDate}T${endTime}:00Z`;

    // Check if the venue is available using the new availability system
    try {
      const availabilityCheck = await checkAvailabilityNew(venueId, startDateTime, endDateTime);

      if (!availabilityCheck.available) {
        const conflictMessages = availabilityCheck.conflicts.join(', ');
        throw new Error(`This venue is not available for the selected time: ${conflictMessages}`);
      }
    } catch (availabilityError) {
      console.warn('New availability check failed, falling back to old system:', availabilityError);

      // Fallback to old availability check
      const oldAvailabilityCheck = await checkVenueAvailability(
        venueId,
        startDate,
        startTime,
        endTime,
        endDate
      );

      if (!oldAvailabilityCheck.available) {
        throw new Error('This venue is already booked for the selected time. Please choose a different time.');
      }
    }

    // Try to insert the booking into the database
    try {
      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      // Use a transaction to ensure atomicity
      const { data, error } = await supabase.rpc('create_booking_with_availability_check', {
        p_venue_id: venueId,
        p_guest_id: userId,
        p_start_date: startDateTime,
        p_end_date: endDateTime,
        p_guests_count: bookingData.guests,
        p_total_price: bookingData.totalPrice,
        p_status: 'pending'
      });

      if (error) {
        // If the RPC function doesn't exist, fall back to regular insert
        if (error.message.includes('does not exist')) {
          console.warn('RPC function not available, falling back to regular insert');

          // Check availability again right before inserting
          const latestAvailabilityCheck = await checkVenueAvailability(
            venueId,
            startDate,
            startTime,
            endTime,
            endDate
          );

          if (!latestAvailabilityCheck.available) {
            throw new Error('This venue was just booked by someone else. Please choose a different time.');
          }

          // Proceed with regular insert
          const { data: insertData, error: insertError } = await supabase
            .from('bookings')
            .insert({
              venue_id: venueId,
              guest_id: userId,
              start_date: startDateTime,
              end_date: endDateTime,
              guests_count: bookingData.guests,
              total_price: bookingData.totalPrice,
              status: 'pending' // Initial status is pending until payment is processed
            })
            .select();

          if (insertError) throw insertError;
          return insertData?.[0];
        } else {
          throw error;
        }
      }

      // Return the created booking
      return data;
    } catch (supabaseError) {
      console.warn('Supabase error, using mock data instead:', supabaseError);

      // Create a mock booking with a unique ID
      const mockBooking = {
        id: `mock-${Date.now()}`,
        venue_id: venueId,
        guest_id: userId,
        start_date: startDateTime,
        end_date: endDateTime,
        guests_count: bookingData.guests,
        total_price: bookingData.totalPrice,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Add to mock bookings array
      mockBookings.push(mockBooking);

      return mockBooking;
    }
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
}

/**
 * Get a booking by ID
 */
export async function getBookingById(bookingId: string) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        venue:venue_id (
          id,
          title,
          description,
          address,
          images,
          price,
          capacity
        ),
        guest:guest_id (
          id,
          first_name,
          last_name,
          email
        )
      `)
      .eq('id', bookingId)
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error('Error fetching booking:', error);
    throw error;
  }
}

/**
 * Get all bookings for a user
 */
export async function getUserBookings(userId: string, userEmail?: string) {
  try {
    // Special <NAME_EMAIL> - return mock data
    if (userEmail === '<EMAIL>' || localStorage.getItem('clerk_user_email') === '<EMAIL>') {
      console.log('Returning mock <NAME_EMAIL>');

      // Create mock venues if they don't exist
      if (mockVenues.length === 0) {
        mockVenues.push({
          id: 'mock-venue-1',
          title: 'Luxury Beachfront Villa',
          description: 'Beautiful beachfront villa with stunning ocean views',
          address: '123 Beach Road, Bondi Beach, NSW',
          images: ['https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=1000&auto=format&fit=crop'],
          price: 150,
          capacity: 20
        });

        mockVenues.push({
          id: 'mock-venue-2',
          title: 'Modern City Loft',
          description: 'Spacious loft in the heart of Sydney CBD',
          address: '456 George Street, Sydney, NSW',
          images: ['https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1000&auto=format&fit=crop'],
          price: 120,
          capacity: 15
        });
      }

      // Create mock bookings
      const mockUserBookings = [
        {
          id: 'mock-booking-1',
          venue_id: 'mock-venue-1',
          guest_id: userId,
          start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 5 * 60 * 60 * 1000).toISOString(), // 5 hours later
          guests_count: 12,
          total_price: 750,
          status: 'confirmed',
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
          updated_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          venue: mockVenues[0]
        },
        {
          id: 'mock-booking-2',
          venue_id: 'mock-venue-2',
          guest_id: userId,
          start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
          end_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000).toISOString(), // 4 hours later
          guests_count: 8,
          total_price: 480,
          status: 'completed',
          created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days ago
          updated_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          venue: mockVenues[1]
        },
        {
          id: 'mock-booking-3',
          venue_id: 'mock-venue-1',
          guest_id: userId,
          start_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
          end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000).toISOString(), // 6 hours later
          guests_count: 15,
          total_price: 900,
          status: 'pending',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
          updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          venue: mockVenues[0]
        }
      ];

      return mockUserBookings;
    }

    // For other users, try to get data from Supabase
    try {
      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          venue:venue_id (
            id,
            title,
            description,
            address,
            images,
            price
          )
        `)
        .eq('guest_id', userId)
        .order('start_date', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (supabaseError) {
      console.error('Error fetching user bookings from Supabase:', supabaseError);

      // Return empty array instead of throwing error
      return [];
    }
  } catch (error) {
    console.error('Error fetching user bookings:', error);
    // Return empty array instead of throwing error
    return [];
  }
}

/**
 * Get all bookings for a venue
 */
export async function getVenueBookings(venueId: string) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        guest:guest_id (
          id,
          first_name,
          last_name,
          email
        )
      `)
      .eq('venue_id', venueId)
      .order('start_date', { ascending: false });

    if (error) throw error;

    return data || [];
  } catch (error) {
    console.error('Error fetching venue bookings:', error);
    throw error;
  }
}

/**
 * Update a booking's status
 */
export async function updateBookingStatus(bookingId: string, status: 'pending' | 'confirmed' | 'cancelled' | 'completed') {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('bookings')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', bookingId)
      .select();

    if (error) throw error;

    return data?.[0];
  } catch (error) {
    console.error('Error updating booking status:', error);
    throw error;
  }
}

/**
 * Check if a venue is available for a specific date and time range
 * This function checks for any overlapping bookings in the database
 * and returns whether the venue is available for the specified time range
 */
export async function checkVenueAvailability(
  venueId: string,
  startDate: string,
  startTime: string,
  endTime: string,
  endDate?: string
) {
  try {
    // Validate inputs
    if (!venueId || !startDate || !startTime || !endTime) {
      throw new Error('Missing required parameters for availability check');
    }

    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
      throw new Error('Invalid start date format. Expected YYYY-MM-DD');
    }

    if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
      throw new Error('Invalid end date format. Expected YYYY-MM-DD');
    }

    // Validate time format
    if (!/^\d{2}:\d{2}$/.test(startTime)) {
      throw new Error('Invalid start time format. Expected HH:MM');
    }

    if (!/^\d{2}:\d{2}$/.test(endTime)) {
      throw new Error('Invalid end time format. Expected HH:MM');
    }

    // Format dates for database query
    const effectiveEndDate = endDate || startDate;

    // Parse times for validation and comparison
    const startHour = parseInt(startTime.split(':')[0], 10);
    const startMinute = parseInt(startTime.split(':')[1], 10);
    const endHour = parseInt(endTime.split(':')[0], 10);
    const endMinute = parseInt(endTime.split(':')[1], 10);

    // Validate time values
    if (startHour < 0 || startHour > 23 || startMinute < 0 || startMinute > 59) {
      throw new Error('Invalid start time values');
    }

    if (endHour < 0 || endHour > 23 || endMinute < 0 || endMinute > 59) {
      throw new Error('Invalid end time values');
    }

    // Handle overnight bookings (when end time is earlier than start time)
    const isOvernightBooking = !endDate && (
      endHour < startHour ||
      (endHour === startHour && endMinute < startMinute)
    );

    // Format start and end datetimes
    let startDateTime = `${startDate}T${startTime}:00`;
    let endDateTime;

    if (isOvernightBooking) {
      // For overnight bookings, add one day to the end date
      const endDateObj = new Date(startDate);
      endDateObj.setDate(endDateObj.getDate() + 1);
      const formattedEndDate = endDateObj.toISOString().split('T')[0];
      endDateTime = `${formattedEndDate}T${endTime}:00`;
    } else {
      endDateTime = `${effectiveEndDate}T${endTime}:00`;
    }

    // Validate that start date/time is before end date/time
    const startDateTimeObj = new Date(startDateTime);
    const endDateTimeObj = new Date(endDateTime);

    if (isNaN(startDateTimeObj.getTime())) {
      throw new Error('Invalid start date/time');
    }

    if (isNaN(endDateTimeObj.getTime())) {
      throw new Error('Invalid end date/time');
    }

    if (startDateTimeObj >= endDateTimeObj) {
      throw new Error('Start date/time must be before end date/time');
    }

    console.log('Checking availability for:', {
      venueId,
      startDateTime,
      endDateTime,
      isOvernightBooking
    });

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Check for any overlapping bookings
    const { data, error } = await supabase
      .from('bookings')
      .select('id, start_date, end_date, status')
      .eq('venue_id', venueId)
      .not('status', 'in', '("cancelled", "rejected")') // Exclude cancelled and rejected bookings
      .filter('start_date', 'lt', endDateTime)
      .filter('end_date', 'gt', startDateTime);

    if (error) {
      console.error('Database error checking availability:', error);
      throw error;
    }

    // Log the results for debugging
    console.log('Availability check results:', {
      conflictsFound: data && data.length > 0,
      conflicts: data
    });

    // Format conflicts for better display
    const formattedConflicts = (data || []).map(conflict => {
      const conflictStart = new Date(conflict.start_date);
      const conflictEnd = new Date(conflict.end_date);

      return {
        id: conflict.id,
        start_date: conflict.start_date,
        end_date: conflict.end_date,
        status: conflict.status,
        formattedStart: conflictStart.toLocaleString(),
        formattedEnd: conflictEnd.toLocaleString(),
        displayRange: `${conflictStart.toLocaleString()} - ${conflictEnd.toLocaleString()}`
      };
    });

    // If no data is returned, the venue is available
    return {
      available: !data || data.length === 0,
      conflicts: formattedConflicts,
      requestDetails: {
        startDateTime,
        endDateTime,
        isOvernightBooking,
        startDateTimeObj,
        endDateTimeObj
      }
    };
  } catch (error) {
    console.error('Error checking venue availability:', error);
    throw error;
  }
}
