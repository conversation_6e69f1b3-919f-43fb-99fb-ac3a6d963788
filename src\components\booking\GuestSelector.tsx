import React, { useState, useRef } from 'react';
import { Users, ChevronDown, UserPlus, UserMinus } from 'lucide-react';
import { useClickOutside } from '../../hooks/useClickOutside';
import { useEscapeKey } from '../../hooks/useEscapeKey';

interface GuestSelectorProps {
  value: number;
  onChange: (guests: number) => void;
  maxCapacity: number;
}

export default function GuestSelector({ value, onChange, maxCapacity }: GuestSelectorProps) {
  const [showDropdown, setShowDropdown] = useState(false);

  // Ref for click outside detection
  const guestRef = useRef<HTMLDivElement>(null);
  useClickOutside(guestRef, () => setShowDropdown(false), showDropdown);
  useEscapeKey(() => setShowDropdown(false), showDropdown);

  // Define capacity ranges based on venue size
  const getCapacityRanges = () => {
    // For smaller venues
    if (maxCapacity <= 20) {
      return [
        { min: 1, max: maxCapacity, label: `Up to ${maxCapacity} guests` }
      ];
    }
    // For medium venues
    else if (maxCapacity <= 50) {
      return [
        { min: 1, max: 10, label: 'Small group (1-10)' },
        { min: 11, max: 25, label: 'Medium group (11-25)' },
        { min: 26, max: maxCapacity, label: `Large group (26-${maxCapacity})` }
      ];
    }
    // For large venues
    else {
      return [
        { min: 1, max: 20, label: 'Small group (1-20)' },
        { min: 21, max: 50, label: 'Medium group (21-50)' },
        { min: 51, max: 100, label: 'Large group (51-100)' },
        { min: 101, max: maxCapacity, label: `Very large group (101-${maxCapacity})` }
      ];
    }
  };

  const capacityRanges = getCapacityRanges();

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Expected Number of Guests
      </label>
      <div className="relative" ref={guestRef}>
        <div
          className="relative cursor-pointer bg-white rounded-lg border border-gray-300 hover:border-purple-400 transition-colors focus-within:border-purple-500 focus-within:ring-2 focus-within:ring-purple-500 focus-within:ring-opacity-50"
          onClick={() => setShowDropdown(!showDropdown)}
          tabIndex={0}
          role="button"
          aria-haspopup="listbox"
          aria-expanded={showDropdown}
          aria-labelledby="guest-selector-label"
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setShowDropdown(!showDropdown);
            }
          }}
        >
          <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-500 h-5 w-5" />
          <div className="pl-10 w-full pr-10 py-3 rounded-lg text-gray-700">
            {value} {value === 1 ? 'guest' : 'guests'}
          </div>
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-2">
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100 active:bg-gray-200 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                if (value > 1) {
                  onChange(value - 1);
                }
              }}
              disabled={value <= 1}
              aria-label="Decrease guest count"
            >
              <UserMinus className="h-4 w-4 text-gray-500" />
            </button>
            <button
              type="button"
              className="p-1 rounded-full hover:bg-gray-100 active:bg-gray-200 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                if (value < maxCapacity) {
                  onChange(value + 1);
                }
              }}
              disabled={value >= maxCapacity}
              aria-label="Increase guest count"
            >
              <UserPlus className="h-4 w-4 text-gray-500" />
            </button>
            <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ml-1 ${showDropdown ? 'rotate-180' : ''}`} />
          </div>
        </div>

        {/* Hidden input for form submission */}
        <input
          type="hidden"
          value={value}
          required
        />

        {/* Guest dropdown */}
        {showDropdown && (
          <div className="absolute z-20 mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-200 animate-fadeIn">
            {/* Capacity ranges */}
            <div className="p-4">
              <p className="text-sm text-gray-500 mb-3">This venue has a maximum capacity of {maxCapacity} guests</p>

              <div className="space-y-2">
                {capacityRanges.map((range, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => {
                      onChange(range.max);
                      setShowDropdown(false);
                    }}
                    className={`
                      w-full py-3 px-4 rounded-lg text-left flex items-center justify-between
                      transition-colors duration-200
                      ${value === range.max ? 'bg-purple-100 border border-purple-300' : 'hover:bg-gray-50 active:bg-gray-100 border border-gray-200'}
                    `}
                    role="option"
                    aria-selected={value === range.max}
                  >
                    <div>
                      <span className="font-medium text-gray-800">{range.label}</span>
                      <p className="text-xs text-gray-500 mt-1">
                        {range.min === range.max
                          ? `Exactly ${range.max} guests`
                          : `Maximum capacity: ${range.max} guests`}
                      </p>
                    </div>
                    {value === range.max && (
                      <div className="h-5 w-5 bg-purple-600 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Fixed price note */}
            <div className="px-4 py-3 bg-gray-50 text-xs text-gray-600 rounded-b-lg border-t border-gray-200">
              <p className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                The venue price is fixed regardless of your group size
              </p>
            </div>
          </div>
        )}
      </div>
      <p className="text-xs text-gray-500 mt-1">Venue price is fixed regardless of actual attendance</p>
    </div>
  );
}