/**
 * Enhanced Booking Flow Component
 *
 * This component manages the complete booking flow:
 * 1. Booking form and validation
 * 2. Payment processing
 * 3. Confirmation and email notifications
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../providers/AuthProvider';
import BookingForm from './BookingForm';
import BookingPayment from './BookingPayment';
import { bookingFlowService, BookingFlowData } from '../../services/booking-flow';
import { Venue } from '../../types/venue';
import { BookingData } from './BookingForm';
import { CheckCircle, ArrowLeft, AlertCircle, CreditCard, Calendar } from 'lucide-react';

interface EnhancedBookingFlowProps {
  venue: Venue;
  onComplete?: (bookingId: string) => void;
  onCancel?: () => void;
}

type BookingStep = 'form' | 'payment' | 'processing' | 'success' | 'error';

export default function EnhancedBookingFlow({ venue, onComplete, onCancel }: EnhancedBookingFlowProps) {
  const { user } = useAuth();
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState<BookingStep>('form');
  const [bookingData, setBookingData] = useState<BookingData | null>(null);
  const [bookingId, setBookingId] = useState<string>('');
  const [paymentId, setPaymentId] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Handle booking form submission
  const handleBookingSubmit = async (formData: BookingData) => {
    if (!user) {
      setError('You must be logged in to make a booking');
      setCurrentStep('error');
      return;
    }

    setLoading(true);
    setError('');
    setBookingData(formData);

    try {
      // Prepare booking flow data
      const flowData: BookingFlowData = {
        venue,
        bookingData: formData,
        user: {
          id: user.id,
          email: user.email || '',
          first_name: user.first_name,
          last_name: user.last_name,
        },
        priceCalculation: formData.priceCalculation!,
      };

      // Execute booking flow
      const result = await bookingFlowService.executeBookingFlow(flowData);

      if (result.success && result.bookingId) {
        setBookingId(result.bookingId);
        setCurrentStep('payment');
      } else {
        setError(result.error || 'Failed to create booking');
        setCurrentStep('error');
      }
    } catch (err: any) {
      console.error('Booking submission failed:', err);
      setError(err.message || 'Failed to create booking');
      setCurrentStep('error');
    } finally {
      setLoading(false);
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = async (paymentIntentId: string) => {
    setPaymentId(paymentIntentId);
    setCurrentStep('processing');

    try {
      // Process payment success
      const result = await bookingFlowService.handlePaymentSuccess(
        bookingId,
        paymentIntentId,
        bookingData?.totalPrice || 0
      );

      if (result.success) {
        setCurrentStep('success');

        // Call completion callback if provided
        if (onComplete) {
          onComplete(bookingId);
        }
      } else {
        setError(result.error || 'Payment processing failed');
        setCurrentStep('error');
      }
    } catch (err: any) {
      console.error('Payment processing failed:', err);
      setError(err.message || 'Payment processing failed');
      setCurrentStep('error');
    }
  };

  // Handle payment error
  const handlePaymentError = async (errorMessage: string) => {
    setError(errorMessage);

    // Update booking status to payment failed
    if (bookingId) {
      await bookingFlowService.handlePaymentFailure(bookingId, errorMessage);
    }

    setCurrentStep('error');
  };

  // Handle back navigation
  const handleBack = () => {
    switch (currentStep) {
      case 'payment':
        setCurrentStep('form');
        break;
      case 'error':
        setCurrentStep('form');
        setError('');
        break;
      default:
        if (onCancel) {
          onCancel();
        } else {
          navigate(-1);
        }
    }
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { key: 'form', label: 'Booking Details', icon: Calendar },
      { key: 'payment', label: 'Payment', icon: CreditCard },
      { key: 'success', label: 'Confirmation', icon: CheckCircle },
    ];

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => {
          const isActive = currentStep === step.key;
          const isCompleted = steps.findIndex(s => s.key === currentStep) > index;
          const Icon = step.icon;

          return (
            <React.Fragment key={step.key}>
              <div className={`flex items-center ${isActive ? 'text-purple-600' : isCompleted ? 'text-green-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  isActive ? 'border-purple-600 bg-purple-50' :
                  isCompleted ? 'border-green-600 bg-green-50' :
                  'border-gray-300 bg-gray-50'
                }`}>
                  <Icon className="w-4 h-4" />
                </div>
                <span className="ml-2 text-sm font-medium">{step.label}</span>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-300'}`} />
              )}
            </React.Fragment>
          );
        })}
      </div>
    );
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'form':
        return (
          <div className="max-w-2xl mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Book {venue.title}</h2>
              <p className="text-gray-600">Complete your booking details below</p>
            </div>
            <BookingForm venue={venue} onSubmit={handleBookingSubmit} />
          </div>
        );

      case 'payment':
        if (!bookingData) return null;

        return (
          <div className="max-w-2xl mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Complete Your Payment</h2>
              <p className="text-gray-600">Secure payment processing by Stripe</p>
            </div>

            <BookingPayment
              bookingId={bookingId}
              amount={bookingData.totalPrice}
              venueName={venue.title}
              bookingDate={bookingData.startDate}
              customerEmail={user?.email}
              priceCalculation={bookingData.priceCalculation}
              onSuccess={handlePaymentSuccess}
              onCancel={() => setCurrentStep('form')}
            />
          </div>
        );

      case 'processing':
        return (
          <div className="max-w-md mx-auto text-center py-12">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500 mx-auto mb-6"></div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Processing Your Booking</h2>
            <p className="text-gray-600">Please wait while we confirm your payment and send confirmation emails...</p>
          </div>
        );

      case 'success':
        return (
          <div className="max-w-md mx-auto text-center py-12">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Booking Confirmed!</h2>
            <p className="text-gray-600 mb-6">
              Your booking at {venue.title} has been confirmed. You'll receive a confirmation email shortly.
            </p>

            <div className="space-y-3">
              <button
                onClick={() => navigate(`/booking-confirmation/${bookingId}`)}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
              >
                View Booking Details
              </button>
              <button
                onClick={() => navigate('/my-bookings')}
                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors"
              >
                View All Bookings
              </button>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="max-w-md mx-auto text-center py-12">
            <AlertCircle className="w-16 h-16 text-red-600 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Booking Failed</h2>
            <p className="text-gray-600 mb-6">{error}</p>

            <button
              onClick={handleBack}
              className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Back button */}
        {currentStep !== 'processing' && (
          <button
            onClick={handleBack}
            className="flex items-center text-purple-600 hover:text-purple-700 mb-6"
            disabled={loading}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>
        )}

        {/* Step indicator */}
        {currentStep !== 'error' && renderStepIndicator()}

        {/* Step content */}
        {renderStepContent()}
      </div>
    </div>
  );
}
