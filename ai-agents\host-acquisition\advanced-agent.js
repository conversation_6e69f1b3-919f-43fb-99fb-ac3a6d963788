import dotenv from 'dotenv';
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { PromptTemplate } from "@langchain/core/prompts";
import { LL<PERSON>hain } from "langchain/chains";
import { <PERSON>uffer<PERSON>emory } from "langchain/memory";
import { initializeAgentExecutorWithOptions } from "langchain/agents";
import { hostTools } from './tools.js';
import partyScoreTool from './party-score-tool.js';
import earningsAnalyzerTool from './earnings-tool.js';

// Load environment variables
dotenv.config();

// Configure LangSmith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;
process.env.LANGCHAIN_ORG_ID = process.env.LANGSMITH_ORG_ID;

// Initialize the Hugging Face model
const model = new HuggingFaceInference({
  model: "mistralai/Mistral-7B-Instruct-v0.3", // Updated to the latest version
  apiKey: process.env.HUGGINGFACE_API_KEY,
  temperature: 0.7,
  maxTokens: 1024,
});

// Combine all tools for the agent
const tools = [
  // Party Score Tool
  partyScoreTool,

  // Earnings Analyzer Tool
  earningsAnalyzerTool,

  // Add all tools from the hostTools array
  ...hostTools
];

// Create a prompt template for the agent
const promptTemplate = `
You are Alex, the AI host acquisition specialist for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.

IMPORTANT: Always use Australian English spelling (e.g., 'organise' not 'organize', 'colour' not 'color', 'specialise' not 'specialize').

Your goal is to help potential hosts successfully list their venues on HouseGoing.
The user has signed up to our host enquiry list and needs guidance.

You have access to several tools:
1. PartyScoreCalculator - Calculate a Party Score (1-10) for venues based on local regulations and zoning
2. VenueAnalyzer - Analyze venue potential and estimate earnings
3. ListingEnhancer - Improve venue descriptions and suggest photo improvements
4. PricingOptimizer - Recommend optimal pricing strategies
5. CalendarManager - Help manage venue availability and booking settings
6. RulesGenerator - Create customized house rules
7. CompetitorAnalysis - Analyze similar venues in the area

When a host mentions their venue location, always use the PartyScoreCalculator to determine:
- The venue's Party Score (1-10)
- Local council noise regulations and curfew times
- Zoning restrictions that affect the venue
- Recommendations for time limits to display in the booking calendar

GUIDELINES:
- Be warm and welcoming but not overly casual
- Be enthusiastic about helping hosts find success
- Be confident and knowledgeable about venue options
- Use Australian-friendly language with occasional local phrases
- Keep initial greetings to 1-2 sentences
- Format lists with bullet points for easy scanning
- Bold important information
- Personalize responses by referencing specific venue types
- After identifying key details, share a brief benefit specific to that venue type
- Occasionally mention trending venue types or features in their area

Remember that you are only accessible to hosts, and your primary goal is to help them list their venues successfully.
`;

// Create the agent
async function createAgent() {
  const executor = await initializeAgentExecutorWithOptions(tools, model, {
    agentType: "structured-chat-zero-shot-react-description",
    verbose: true,
    prefix: promptTemplate,
    memory: new BufferMemory({
      memoryKey: "chat_history",
      returnMessages: true,
    }),
  });

  return executor;
}

// Interactive console interface
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log("HouseGoing Advanced AI Host Acquisition Agent");
console.log("============================================");
console.log("Type 'exit' to quit the conversation");
console.log("");

async function startAgent() {
  const agent = await createAgent();

  function askQuestion() {
    rl.question("You: ", async (input) => {
      if (input.toLowerCase() === 'exit') {
        console.log("Goodbye!");
        rl.close();
        return;
      }

      console.log("AI is thinking...");
      try {
        const response = await agent.call({ input });
        console.log("\nAlex: " + response.output + "\n");
      } catch (error) {
        console.error("Error:", error);
        console.log("\nAlex: I'm sorry, I encountered an error. Please try again.\n");
      }

      askQuestion();
    });
  }

  askQuestion();
}

// Start the agent
startAgent().catch(console.error);
