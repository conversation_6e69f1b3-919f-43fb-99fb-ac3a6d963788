#!/usr/bin/env node

/**
 * Comprehensive Supabase Database Setup Script for HouseGoing
 *
 * This script:
 * 1. Authenticates with <PERSON>pa<PERSON> using the service_role key
 * 2. Checks if tables already exist
 * 3. Creates the SQL execution function if needed
 * 4. Creates all necessary tables with proper error handling
 * 5. Verifies the creation was successful
 *
 * Usage: node setup-supabase-complete.js
 */

// Import required packages
import { createClient } from '@supabase/supabase-js';
import chalk from 'chalk'; // For colored console output
import ora from 'ora'; // For spinner indicators

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client with service role key for full admin access
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// SQL for creating the exec_sql function
const CREATE_EXEC_SQL_FUNCTION = `
-- Create a secure function to execute SQL
CREATE OR REPLACE FUNCTION exec_sql(query text)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE query;
  RETURN jsonb_build_object('success', true);
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object(
    'success', false,
    'error', SQLERRM
  );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_sql TO authenticated;
GRANT EXECUTE ON FUNCTION exec_sql TO anon;
GRANT EXECUTE ON FUNCTION exec_sql TO service_role;
`;

// SQL for creating all tables
const CREATE_TABLES_SQL = `
-- Create updated_at trigger function (used by multiple tables)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  clerk_id TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  role TEXT NOT NULL DEFAULT 'guest',
  full_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON user_profiles(clerk_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- Create trigger for user_profiles
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
BEFORE UPDATE ON user_profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create venues table
CREATE TABLE IF NOT EXISTS venues (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  owner_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
  title TEXT NOT NULL,
  description TEXT,
  location TEXT NOT NULL,
  address TEXT NOT NULL,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  zip_code TEXT NOT NULL,
  coordinates JSONB,
  price DECIMAL(10,2) NOT NULL,
  capacity INTEGER NOT NULL,
  amenities JSONB,
  house_rules TEXT,
  images TEXT[],
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for venues
CREATE INDEX IF NOT EXISTS idx_venues_owner_id ON venues(owner_id);
CREATE INDEX IF NOT EXISTS idx_venues_location ON venues(location);

-- Create trigger for venues
DROP TRIGGER IF EXISTS update_venues_updated_at ON venues;
CREATE TRIGGER update_venues_updated_at
BEFORE UPDATE ON venues
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  guest_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  guests_count INTEGER NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for bookings
CREATE INDEX IF NOT EXISTS idx_bookings_venue_id ON bookings(venue_id);
CREATE INDEX IF NOT EXISTS idx_bookings_guest_id ON bookings(guest_id);
CREATE INDEX IF NOT EXISTS idx_bookings_dates ON bookings(start_date, end_date);

-- Create trigger for bookings
DROP TRIGGER IF EXISTS update_bookings_updated_at ON bookings;
CREATE TRIGGER update_bookings_updated_at
BEFORE UPDATE ON bookings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create reviews table
CREATE TABLE IF NOT EXISTS reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  reviewer_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for reviews
CREATE INDEX IF NOT EXISTS idx_reviews_booking_id ON reviews(booking_id);
CREATE INDEX IF NOT EXISTS idx_reviews_venue_id ON reviews(venue_id);
CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON reviews(reviewer_id);

-- Create trigger for reviews
DROP TRIGGER IF EXISTS update_reviews_updated_at ON reviews;
CREATE TRIGGER update_reviews_updated_at
BEFORE UPDATE ON reviews
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  sender_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
  recipient_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for messages
CREATE INDEX IF NOT EXISTS idx_messages_booking_id ON messages(booking_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON messages(recipient_id);

-- Create trigger for messages
DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
CREATE TRIGGER update_messages_updated_at
BEFORE UPDATE ON messages
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE venues ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_profiles
CREATE POLICY IF NOT EXISTS read_own_profile ON user_profiles
  FOR SELECT
  USING (auth.uid()::text = clerk_id);

CREATE POLICY IF NOT EXISTS update_own_profile ON user_profiles
  FOR UPDATE
  USING (auth.uid()::text = clerk_id);

CREATE POLICY IF NOT EXISTS service_read_all_profiles ON user_profiles
  FOR SELECT
  TO service_role
  USING (true);

CREATE POLICY IF NOT EXISTS service_update_all_profiles ON user_profiles
  FOR UPDATE
  TO service_role
  USING (true);

CREATE POLICY IF NOT EXISTS service_insert_profiles ON user_profiles
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Create RLS policies for venues
CREATE POLICY IF NOT EXISTS read_venues ON venues
  FOR SELECT
  USING (true);

CREATE POLICY IF NOT EXISTS insert_own_venues ON venues
  FOR INSERT
  WITH CHECK (owner_id = auth.uid()::text);

CREATE POLICY IF NOT EXISTS update_own_venues ON venues
  FOR UPDATE
  USING (owner_id = auth.uid()::text);

CREATE POLICY IF NOT EXISTS delete_own_venues ON venues
  FOR DELETE
  USING (owner_id = auth.uid()::text);

-- Create RLS policies for bookings
CREATE POLICY IF NOT EXISTS read_own_bookings ON bookings
  FOR SELECT
  USING (guest_id = auth.uid()::text OR EXISTS (
    SELECT 1 FROM venues WHERE venues.id = bookings.venue_id AND venues.owner_id = auth.uid()::text
  ));

CREATE POLICY IF NOT EXISTS insert_bookings ON bookings
  FOR INSERT
  WITH CHECK (guest_id = auth.uid()::text);

CREATE POLICY IF NOT EXISTS update_own_bookings ON bookings
  FOR UPDATE
  USING (guest_id = auth.uid()::text OR EXISTS (
    SELECT 1 FROM venues WHERE venues.id = bookings.venue_id AND venues.owner_id = auth.uid()::text
  ));

-- Insert pre-registered hosts
INSERT INTO user_profiles (clerk_id, email, role, full_name)
VALUES
  ('pre-registered-host-1', '<EMAIL>', 'host', 'Tom C')
ON CONFLICT (email)
DO UPDATE SET role = 'host', updated_at = NOW();
`;

// List of tables to verify
const TABLES_TO_VERIFY = [
  'user_profiles',
  'venues',
  'bookings',
  'reviews',
  'messages'
];

/**
 * Check if a table exists in the database
 * @param {string} tableName - The name of the table to check
 * @returns {Promise<boolean>} - True if the table exists, false otherwise
 */
async function checkTableExists(tableName) {
  try {
    // Query the information_schema to check if the table exists
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName);

    if (error) {
      console.error(chalk.red(`Error checking if table ${tableName} exists:`), error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error(chalk.red(`Exception checking if table ${tableName} exists:`), error);
    return false;
  }
}

/**
 * Check if the exec_sql function exists
 * @returns {Promise<boolean>} - True if the function exists, false otherwise
 */
async function checkExecSqlFunctionExists() {
  try {
    // Query the information_schema to check if the function exists
    const { data, error } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .eq('routine_name', 'exec_sql');

    if (error) {
      console.error(chalk.red('Error checking if exec_sql function exists:'), error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error(chalk.red('Exception checking if exec_sql function exists:'), error);
    return false;
  }
}

/**
 * Execute SQL directly using the Supabase REST API
 * @param {string} sql - The SQL to execute
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
async function executeSql(sql) {
  try {
    // First try using the exec_sql function if it exists
    const functionExists = await checkExecSqlFunctionExists();

    if (functionExists) {
      const { data, error } = await supabase.rpc('exec_sql', { query: sql });

      if (error) {
        console.error(chalk.red('Error executing SQL using exec_sql function:'), error);
        return false;
      }

      if (data && data.success === false) {
        console.error(chalk.red('SQL execution failed:'), data.error);
        return false;
      }

      return true;
    } else {
      // If the function doesn't exist, use a direct query
      // Note: This has limitations and may not work for all SQL statements
      const { error } = await supabase.rpc('pg_query', { query: sql });

      if (error) {
        console.error(chalk.red('Error executing SQL directly:'), error);
        return false;
      }

      return true;
    }
  } catch (error) {
    console.error(chalk.red('Exception executing SQL:'), error);
    return false;
  }
}

/**
 * Create the exec_sql function
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
async function createExecSqlFunction() {
  const spinner = ora('Creating exec_sql function...').start();

  try {
    // Check if the function already exists
    const functionExists = await checkExecSqlFunctionExists();

    if (functionExists) {
      spinner.succeed(chalk.green('exec_sql function already exists'));
      return true;
    }

    // Create the function using a direct query
    const { error } = await supabase.rpc('pg_query', { query: CREATE_EXEC_SQL_FUNCTION });

    if (error) {
      spinner.fail(chalk.red('Failed to create exec_sql function'));
      console.error(chalk.red('Error:'), error);
      return false;
    }

    spinner.succeed(chalk.green('exec_sql function created successfully'));
    return true;
  } catch (error) {
    spinner.fail(chalk.red('Exception creating exec_sql function'));
    console.error(chalk.red('Error:'), error);
    return false;
  }
}

/**
 * Create all tables
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
async function createTables() {
  const spinner = ora('Creating tables...').start();

  try {
    // Execute the SQL to create all tables
    const success = await executeSql(CREATE_TABLES_SQL);

    if (!success) {
      spinner.fail(chalk.red('Failed to create tables'));
      return false;
    }

    spinner.succeed(chalk.green('Tables created successfully'));
    return true;
  } catch (error) {
    spinner.fail(chalk.red('Exception creating tables'));
    console.error(chalk.red('Error:'), error);
    return false;
  }
}

/**
 * Verify that all tables were created
 * @returns {Promise<boolean>} - True if all tables exist, false otherwise
 */
async function verifyTables() {
  const spinner = ora('Verifying tables...').start();

  try {
    // Check if each table exists
    const results = await Promise.all(
      TABLES_TO_VERIFY.map(async (tableName) => {
        const exists = await checkTableExists(tableName);
        return { tableName, exists };
      })
    );

    // Check if all tables exist
    const allTablesExist = results.every((result) => result.exists);

    if (allTablesExist) {
      spinner.succeed(chalk.green('All tables verified successfully'));

      // Log the status of each table
      results.forEach((result) => {
        console.log(chalk.green(`✓ Table ${result.tableName} exists`));
      });

      return true;
    } else {
      spinner.fail(chalk.red('Some tables are missing'));

      // Log the status of each table
      results.forEach((result) => {
        if (result.exists) {
          console.log(chalk.green(`✓ Table ${result.tableName} exists`));
        } else {
          console.log(chalk.red(`✗ Table ${result.tableName} is missing`));
        }
      });

      return false;
    }
  } catch (error) {
    spinner.fail(chalk.red('Exception verifying tables'));
    console.error(chalk.red('Error:'), error);
    return false;
  }
}

/**
 * Count rows in a table
 * @param {string} tableName - The name of the table to count rows in
 * @returns {Promise<number>} - The number of rows in the table
 */
async function countRows(tableName) {
  try {
    const { data, error, count } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error(chalk.red(`Error counting rows in ${tableName}:`), error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error(chalk.red(`Exception counting rows in ${tableName}:`), error);
    return 0;
  }
}

/**
 * Verify pre-registered host
 * @returns {Promise<boolean>} - True if the pre-registered host exists, false otherwise
 */
async function verifyPreRegisteredHost() {
  const spinner = ora('Verifying pre-registered host...').start();

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .eq('role', 'host');

    if (error) {
      spinner.fail(chalk.red('Failed to verify pre-registered host'));
      console.error(chalk.red('Error:'), error);
      return false;
    }

    if (data && data.length > 0) {
      spinner.succeed(chalk.green('Pre-registered host verified successfully'));
      console.log(chalk.green('Host details:'), data[0]);
      return true;
    } else {
      spinner.fail(chalk.red('Pre-registered host not found'));
      return false;
    }
  } catch (error) {
    spinner.fail(chalk.red('Exception verifying pre-registered host'));
    console.error(chalk.red('Error:'), error);
    return false;
  }
}

/**
 * Main function to set up the database
 */
async function setupDatabase() {
  console.log(chalk.blue('=== HouseGoing Supabase Database Setup ==='));
  console.log(chalk.blue(`URL: ${SUPABASE_URL}`));
  console.log(chalk.blue('Service Role Key: [REDACTED]'));
  console.log(chalk.blue('======================================='));

  // Step 1: Test connection
  const connectionSpinner = ora('Testing connection to Supabase...').start();
  try {
    const { data, error } = await supabase.from('pg_catalog.pg_tables').select('*').limit(1);

    if (error) {
      connectionSpinner.fail(chalk.red('Failed to connect to Supabase'));
      console.error(chalk.red('Error:'), error);
      return;
    }

    connectionSpinner.succeed(chalk.green('Connected to Supabase successfully'));
  } catch (error) {
    connectionSpinner.fail(chalk.red('Exception connecting to Supabase'));
    console.error(chalk.red('Error:'), error);
    return;
  }

  // Step 2: Create exec_sql function
  const execSqlFunctionCreated = await createExecSqlFunction();
  if (!execSqlFunctionCreated) {
    console.log(chalk.yellow('Warning: exec_sql function creation failed, but we will try to continue...'));
  }

  // Step 3: Check if tables already exist
  const tablesExist = await verifyTables();
  if (tablesExist) {
    console.log(chalk.green('All tables already exist. No need to create them.'));

    // Verify pre-registered host
    await verifyPreRegisteredHost();

    // Count rows in each table
    console.log(chalk.blue('=== Table Row Counts ==='));
    for (const tableName of TABLES_TO_VERIFY) {
      const count = await countRows(tableName);
      console.log(chalk.blue(`${tableName}: ${count} rows`));
    }

    return;
  }

  // Step 4: Create tables
  const tablesCreated = await createTables();
  if (!tablesCreated) {
    console.log(chalk.red('Failed to create tables. Aborting.'));
    return;
  }

  // Step 5: Verify tables were created
  const tablesVerified = await verifyTables();
  if (!tablesVerified) {
    console.log(chalk.red('Failed to verify tables. Something went wrong.'));
    return;
  }

  // Step 6: Verify pre-registered host
  await verifyPreRegisteredHost();

  // Step 7: Count rows in each table
  console.log(chalk.blue('=== Table Row Counts ==='));
  for (const tableName of TABLES_TO_VERIFY) {
    const count = await countRows(tableName);
    console.log(chalk.blue(`${tableName}: ${count} rows`));
  }

  console.log(chalk.green('=== Database setup completed successfully! ==='));
}

// Run the setup
setupDatabase().catch(error => {
  console.error(chalk.red('Unhandled exception:'), error);
  process.exit(1);
});
