/**
 * Setup script for error logging tables in Supabase
 */
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupErrorLogsTable() {
  try {
    console.log('Setting up error logs table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'src', 'lib', 'supabase', 'error-logs-table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      throw error;
    }
    
    console.log('Error logs table created successfully!');
    
    // Insert some sample data for testing
    await insertSampleData();
    
    console.log('Setup complete!');
  } catch (error) {
    console.error('Error setting up error logs table:', error);
  }
}

async function insertSampleData() {
  try {
    console.log('Inserting sample error data...');
    
    // Sample errors
    const sampleErrors = [
      {
        message: 'Failed to process payment',
        stack: 'Error: Failed to process payment\n    at processPayment (/src/services/payments.js:42:7)\n    at async checkout (/src/pages/checkout.js:87:5)',
        context: 'payment',
        metadata: JSON.stringify({
          userId: 'user_123',
          amount: 150,
          paymentMethod: 'credit_card'
        }),
        user_id: 'user_123',
        severity: 'error',
        url: '/checkout',
        status: 'open',
        created_at: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
      },
      {
        message: 'Authentication failed',
        stack: 'Error: Authentication failed\n    at authenticateUser (/src/services/auth.js:28:9)\n    at async login (/src/pages/login.js:65:3)',
        context: 'auth',
        metadata: JSON.stringify({
          attemptCount: 3,
          browser: 'Chrome'
        }),
        severity: 'warning',
        url: '/login',
        status: 'resolved',
        created_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        resolved_at: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
      },
      {
        message: 'Database connection timeout',
        stack: 'Error: Database connection timeout\n    at connectToDatabase (/src/lib/db.js:15:11)\n    at async getVenues (/src/services/venues.js:8:3)',
        context: 'database',
        metadata: JSON.stringify({
          query: 'SELECT * FROM venues',
          timeout: 30000
        }),
        severity: 'critical',
        url: '/venues',
        status: 'open',
        created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      }
    ];
    
    // Insert sample errors
    const { error } = await supabase
      .from('error_logs')
      .insert(sampleErrors);
    
    if (error) {
      throw error;
    }
    
    console.log('Sample error data inserted successfully!');
  } catch (error) {
    console.error('Error inserting sample data:', error);
  }
}

// Run the setup
setupErrorLogsTable();
