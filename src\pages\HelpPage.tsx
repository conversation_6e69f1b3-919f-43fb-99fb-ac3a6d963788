import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Search, Home, Shield, Scale, Phone, Mail, ExternalLink, ChevronDown, ChevronUp } from 'lucide-react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const HelpPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const faqItems: FAQItem[] = [
    {
      id: '1',
      question: 'How do I book a venue?',
      answer: 'Browse our verified venue listings, select your preferred dates, review the terms, and complete your booking. All bookings are subject to host approval and Australian Consumer Law protections.',
      category: 'Booking'
    },
    {
      id: '2',
      question: 'What are NSW noise restrictions?',
      answer: 'NSW has specific noise regulations under the Protection of the Environment Operations Act 1997. Generally, noise levels must not exceed 55dB(A) during the day and 45dB(A) at night in residential areas. Check our NSW Party Planning Guide for detailed information.',
      category: 'Legal'
    },
    {
      id: '3',
      question: 'What insurance do I need as a host?',
      answer: 'You must have public liability insurance of at least $10 million. This protects you against claims for property damage or personal injury. HouseGoing does NOT provide any insurance coverage - you need your own policy covering short-term venue hire.',
      category: 'Hosting'
    },
    {
      id: '4',
      question: 'How do refunds work?',
      answer: 'Refunds are processed according to the host\'s specific cancellation policy set for each venue. Most venues offer: Free cancellation 48+ hours before event, 50% refund for cancellations within 48 hours, and no refund for no-shows. Weather-related cancellations may qualify for full refunds with official weather warnings. Each booking clearly displays the applicable cancellation terms before payment.',
      category: 'Payments'
    },
    {
      id: '5',
      question: 'What if there\'s a dispute?',
      answer: 'Contact our support team first. If unresolved, you can escalate to NSW Fair Trading or the Australian Financial Complaints Authority (AFCA) for independent dispute resolution.',
      category: 'Legal'
    }
  ];

  const filteredFAQs = faqItems.filter(item =>
    item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <>
      <Helmet>
        <title>Help Center | HouseGoing</title>
        <meta name="description" content="Find answers to common questions about venue booking, hosting, and using the HouseGoing platform. Australian consumer law information included." />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://housegoing.com.au/help" />
        
        {/* Open Graph */}
        <meta property="og:title" content="Help Center | HouseGoing" />
        <meta property="og:description" content="Find answers to common questions about venue booking, hosting, and using the HouseGoing platform. Australian consumer law information included." />
        <meta property="og:url" content="https://housegoing.com.au/help" />
        <meta property="og:type" content="website" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Center</h1>
            <p className="text-lg text-gray-600">
              Find answers to common questions and get the support you need
            </p>
          </div>

          {/* Search Bar */}
          <div className="mb-8">
            <div className="relative max-w-xl mx-auto">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search for help..."
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Quick Help Categories */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <div className="text-3xl mb-4">🏠</div>
              <h3 className="text-xl font-semibold mb-2">Booking Venues</h3>
              <p className="text-gray-600">Find and book the perfect venue for your event</p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <div className="text-3xl mb-4">🏡</div>
              <h3 className="text-xl font-semibold mb-2">Hosting</h3>
              <p className="text-gray-600">List your property and earn income</p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <div className="text-3xl mb-4">⚖️</div>
              <h3 className="text-xl font-semibold mb-2">Legal & Safety</h3>
              <p className="text-gray-600">NSW regulations and safety requirements</p>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-6">Frequently Asked Questions</h2>
            
            <div className="space-y-4">
              {filteredFAQs.map((faq) => (
                <div key={faq.id} className="border border-gray-200 rounded-lg">
                  <button
                    className="w-full px-4 py-3 text-left flex justify-between items-center hover:bg-gray-50"
                    onClick={() => toggleFAQ(faq.id)}
                  >
                    <span className="font-medium text-gray-900">{faq.question}</span>
                    {expandedFAQ === faq.id ? (
                      <ChevronUp className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                  {expandedFAQ === faq.id && (
                    <div className="px-4 pb-3">
                      <p className="text-gray-600">{faq.answer}</p>
                      <span className="inline-block mt-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                        {faq.category}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Safety & Compliance */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Safety & Compliance</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Host Requirements</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Valid public liability insurance</li>
                  <li>Compliance with local council regulations</li>
                  <li>Working smoke alarms and safety equipment</li>
                  <li>Clear emergency exit information</li>
                  <li>Adherence to noise restrictions</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-3">Guest Responsibilities</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Respect property rules and local laws</li>
                  <li>Follow capacity and noise limits</li>
                  <li>Report safety concerns immediately</li>
                  <li>Leave property in clean condition</li>
                  <li>Obtain necessary permits if required</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Contact Support */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">Still Need Help?</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">Contact Support</h3>
                <p className="text-blue-800 mb-2">Email: <EMAIL></p>
                <p className="text-blue-800 mb-2">Phone: 1300 HOUSE GO</p>
                <p className="text-blue-800">Response time: Within 24 hours</p>
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">Emergency Contacts</h3>
                <p className="text-blue-800 mb-2">Emergency Services: 000</p>
                <p className="text-blue-800 mb-2">NSW Fair Trading: 13 32 20</p>
                <p className="text-blue-800">Poison Information: 13 11 26</p>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <a href="/safety" className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow text-center">
              <Shield className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Safety Guidelines</h3>
            </a>
            <a href="/terms" className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow text-center">
              <Scale className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Terms of Service</h3>
            </a>
            <a href="/privacy" className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow text-center">
              <Shield className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Privacy Policy</h3>
            </a>
            <a href="/contact" className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow text-center">
              <Mail className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Contact Us</h3>
            </a>
          </div>

          {/* Legal Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              <strong>Last Updated:</strong> January 6, 2025<br />
              <strong>Effective Date:</strong> January 6, 2025<br />
              <strong>Governing Law:</strong> New South Wales, Australia
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default HelpPage;
