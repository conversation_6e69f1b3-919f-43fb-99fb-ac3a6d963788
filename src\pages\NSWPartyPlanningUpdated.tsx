import React, { useState, useEffect } from 'react';
import { getCurfewInfo, formatCurfewTime } from '../lib/nsw-party-planning/curfew-api';
import { extractLGAFromAddress } from '../utils/addressUtils';
import {
  saveR<PERSON>entAddress,
  getR<PERSON>entAdd<PERSON>,
  saveR<PERSON>ult,
  getSavedR<PERSON><PERSON><PERSON>,
  SavedAddress,
  SavedResult
} from '../lib/nsw-party-planning/local-storage-api';
import { v4 as uuidv4 } from 'uuid';

// Define types for curfew info
interface CurfewRecommendation {
  time_period: string;
  recommendation: string;
  priority: number;
}

interface CurfewConfidence {
  level: string;
  score: number;
  factors?: string[];
}

interface CurfewInfo {
  property_type: string;
  zone_code: string;
  zone_name: string;
  lga_name: string;
  event_date?: string;
  is_weekend?: boolean;
  is_holiday?: boolean;
  holiday_name?: string | null;
  curfew_start: string;
  curfew_end: string;
  bass_restriction_start?: string | null;
  bass_restriction_end?: string | null;
  outdoor_cutoff?: string | null;
  special_conditions?: string;
  confidence: CurfewConfidence;
  recommendations: CurfewRecommendation[];
}

const NSWPartyPlanningUpdated: React.FC = () => {
  const [address, setAddress] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]); // Today's date in YYYY-MM-DD format
  const [curfewInfo, setCurfewInfo] = useState<CurfewInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [recentAddresses, setRecentAddresses] = useState<SavedAddress[]>([]);
  const [savedResults, setSavedResults] = useState<SavedResult[]>([]);

  // Load recent addresses and saved results from local storage
  useEffect(() => {
    setRecentAddresses(getRecentAddresses());
    setSavedResults(getSavedResults());
  }, []);

  const handleAddressChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAddress(event.target.value);
  };

  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDate(event.target.value);
  };

  const handleLookup = async () => {
    setLoading(true);
    setError(null);
    setCurfewInfo(null);
    try {
      // Determine property type based on address format
      const isApartment = /^\d+\//.test(address) ||
                          address.toLowerCase().includes('unit') ||
                          address.toLowerCase().includes('apartment');

      // Detect addresses with letter suffixes (e.g., "17a George Street")
      const hasSuffix = /^\d+[a-zA-Z]\s+/.test(address);

      // Determine property type
      let inferredPropertyType = 'House';

      if (isApartment) {
        inferredPropertyType = 'Apartment/Unit';
      } else if (hasSuffix) {
        // For addresses with letter suffixes, we'll use "Duplex/Townhouse" as the property type
        // This better reflects the nature of these properties for noise restriction purposes
        inferredPropertyType = 'Duplex/Townhouse';
      }

      // First, try to geocode the address to get coordinates
      let coordinates = null;
      let inferredLgaName = null;

      try {
        // Geocode the address using Mapbox
        const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA&country=AU&limit=1`;

        const geocodeResponse = await fetch(geocodeUrl);
        const geocodeData = await geocodeResponse.json();

        if (geocodeData.features && geocodeData.features.length > 0) {
          const [lng, lat] = geocodeData.features[0].center;
          coordinates = { lat, lng };
          console.log(`Geocoded coordinates: ${lat}, ${lng}`);

          // Use Layer 8 of NSW Spatial Services API for LGA detection
          try {
            console.log('Querying NSW Spatial Services API Layer 8 for LGA...');
            const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;

            const lgaParams = new URLSearchParams({
              geometry: `${lng},${lat}`, // longitude first, then latitude
              geometryType: 'esriGeometryPoint',
              inSR: '4326', // WGS84 coordinate system
              outFields: 'lganame', // We know the field name is 'lganame'
              f: 'json'
            });

            const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);

            if (lgaResponse.ok) {
              const lgaData = await lgaResponse.json();

              if (lgaData.features && lgaData.features.length > 0) {
                const lgaName = lgaData.features[0].attributes.lganame;
                console.log('LGA from Layer 8:', lgaName);

                // Format the LGA name to title case for consistency
                if (lgaName) {
                  // Convert to title case (e.g., "CITY OF SYDNEY" to "City of Sydney")
                  inferredLgaName = lgaName.toLowerCase().split(' ').map((word: string) =>
                    word.charAt(0).toUpperCase() + word.slice(1)
                  ).join(' ');

                  console.log('Formatted LGA name:', inferredLgaName);
                }
              }
            }
          } catch (lgaError) {
            console.error('Error fetching LGA from Layer 8:', lgaError);
          }
        }
      } catch (geocodeError) {
        console.error('Error geocoding address:', geocodeError);
      }

      // If Layer 8 failed, fall back to text extraction
      if (!inferredLgaName) {
        const extractedLGA = extractLGAFromAddress(address);
        console.log('Extracted LGA from address text:', extractedLGA);
        inferredLgaName = extractedLGA || 'City of Sydney';
      }

      // Determine zoning based on suburb patterns
      let inferredZoneCode = 'R2'; // Default to Low Density Residential

      // For addresses in Sydney CBD, use B8 zoning
      if (address.toLowerCase().includes('sydney') &&
          (address.toLowerCase().includes('cbd') ||
           address.toLowerCase().includes('pitt street') ||
           address.toLowerCase().includes('george street'))) {
        inferredZoneCode = 'B8';
      }

      // Use type assertion to include the date parameter
      const info = await getCurfewInfo({
        address,
        propertyType: inferredPropertyType,
        zoneCode: inferredZoneCode,
        lgaName: inferredLgaName,
        date: date, // Use the selected date
      } as any);

      setCurfewInfo(info);

      // Save the address to recent addresses
      const newAddress: SavedAddress = {
        address,
        coordinates: coordinates || {
          lat: -33.8688, // Default Sydney coordinates
          lng: 151.2093
        },
        council: inferredLgaName,
        zoning: inferredZoneCode,
        timestamp: Date.now()
      };

      saveRecentAddress(newAddress);
      setRecentAddresses(getRecentAddresses());

      // Save the result
      const newResult: SavedResult = {
        id: uuidv4(),
        address,
        propertyType: inferredPropertyType,
        zoneCode: inferredZoneCode,
        zoneName: info.zone_name,
        lgaName: inferredLgaName,
        date,
        curfewStart: info.curfew_start,
        curfewEnd: info.curfew_end,
        bassRestrictionStart: info.bass_restriction_start || null,
        bassRestrictionEnd: info.bass_restriction_end || null,
        outdoorCutoff: info.outdoor_cutoff || null,
        specialConditions: info.special_conditions || '',
        recommendations: info.recommendations.map(rec => ({
          timePeriod: rec.time_period,
          recommendation: rec.recommendation
        })),
        timestamp: Date.now()
      };

      saveResult(newResult);
      setSavedResults(getSavedResults());
    } catch (err) {
      setError('Error fetching curfew information.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="bg-purple-700 text-white p-6 rounded-t-lg">
        <h1 className="text-2xl font-bold mb-2">NSW Party Planning & Noise Guide</h1>
        <p className="text-purple-100">Find out noise curfew times and restrictions for any address in NSW</p>
      </div>

      <div className="bg-white p-6 rounded-b-lg shadow-md">
        <div className="mb-6 space-y-4">
          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <div className="flex">
              <input
                id="address"
                type="text"
                className="flex-grow border border-gray-300 p-2 rounded-md focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter full NSW address (e.g. 13-15 Collins Street Beaconsfield NSW 2015)"
                value={address}
                onChange={handleAddressChange}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && address) {
                    handleLookup();
                  }
                }}
              />
            </div>
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
              Event Date
            </label>
            <input
              id="date"
              type="date"
              className="w-full border border-gray-300 p-2 rounded-md focus:ring-purple-500 focus:border-purple-500"
              value={date}
              onChange={handleDateChange}
            />
          </div>

          <button
            className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
            onClick={handleLookup}
            disabled={loading || !address}
          >
            {loading ? 'Searching...' : 'Check Noise Restrictions'}
          </button>
        </div>

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-md mb-6">
            {error}
          </div>
        )}

        {curfewInfo && (
          <div className="space-y-6">
            <div className="p-4 bg-purple-50 rounded-md border border-purple-100">
              <h2 className="text-xl font-semibold text-purple-800 mb-4">Address Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-700">Address</p>
                  <p className="text-gray-900">{address}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Event Date</p>
                  <p className="text-gray-900">{new Date(date).toLocaleDateString('en-AU', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Property Type</p>
                  <p className="text-gray-900">{curfewInfo.property_type}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Council</p>
                  <p className="text-gray-900">{curfewInfo.lga_name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Zoning</p>
                  <p className="text-gray-900">{curfewInfo.zone_name} ({curfewInfo.zone_code})</p>
                </div>
                {curfewInfo.is_weekend && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Day Type</p>
                    <p className="text-gray-900">{curfewInfo.is_weekend ? 'Weekend' : 'Weekday'}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="p-4 bg-blue-50 rounded-md border border-blue-100">
              <h2 className="text-xl font-semibold text-blue-800 mb-4">Noise Restrictions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-700">Noise Curfew</p>
                  <p className="text-gray-900">{formatCurfewTime(curfewInfo.curfew_start)} to {formatCurfewTime(curfewInfo.curfew_end)}</p>
                </div>

                {curfewInfo.bass_restriction_start && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Bass Music Restriction</p>
                    <p className="text-gray-900">{formatCurfewTime(curfewInfo.bass_restriction_start)} to {formatCurfewTime(curfewInfo.bass_restriction_end || '')}</p>
                  </div>
                )}

                {curfewInfo.outdoor_cutoff && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Outdoor Music Cutoff</p>
                    <p className="text-gray-900">{formatCurfewTime(curfewInfo.outdoor_cutoff)}</p>
                  </div>
                )}
              </div>

              {curfewInfo.special_conditions && (
                <div className="mt-4 p-3 bg-blue-100 rounded-md">
                  <p className="text-sm font-medium text-blue-800">Special Conditions</p>
                  <p className="text-blue-700">{curfewInfo.special_conditions}</p>
                </div>
              )}
            </div>

            {curfewInfo.recommendations && curfewInfo.recommendations.length > 0 && (
              <div className="p-4 bg-green-50 rounded-md border border-green-100">
                <h2 className="text-xl font-semibold text-green-800 mb-4">Recommendations</h2>
                <ul className="space-y-3">
                  {curfewInfo.recommendations.map((rec: CurfewRecommendation, index: number) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-5 h-5 bg-green-500 text-white rounded-full text-center mr-2 flex-shrink-0">
                        {index + 1}
                      </span>
                      <div>
                        <p className="font-medium text-gray-700">{rec.time_period}</p>
                        <p className="text-gray-900">{rec.recommendation}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="text-sm text-gray-500 flex items-center">
              <span>
                Confidence Level: {curfewInfo.confidence.level} -
                This information is provided as a guide only. Always check with your local council for specific regulations.
              </span>
            </div>
          </div>
        )}

        {/* Recent Addresses Section */}
        {recentAddresses.length > 0 && !curfewInfo && (
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Recent Addresses</h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <ul className="space-y-2">
                {recentAddresses.slice(0, 5).map((recentAddress, index) => (
                  <li key={index} className="hover:bg-gray-100 p-2 rounded cursor-pointer" onClick={() => {
                    setAddress(recentAddress.address);
                  }}>
                    <p className="font-medium">{recentAddress.address}</p>
                    <p className="text-sm text-gray-500">{recentAddress.council} • {recentAddress.zoning}</p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Saved Results Section */}
        {savedResults.length > 0 && !curfewInfo && (
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Saved Results</h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <ul className="space-y-2">
                {savedResults.slice(0, 5).map((result, index) => (
                  <li key={index} className="hover:bg-gray-100 p-2 rounded cursor-pointer" onClick={() => {
                    setAddress(result.address);
                    setDate(result.date);
                  }}>
                    <p className="font-medium">{result.address}</p>
                    <p className="text-sm text-gray-500">
                      {result.propertyType} • {result.lgaName} • {new Date(result.date).toLocaleDateString('en-AU')}
                    </p>
                    <p className="text-xs text-gray-400">
                      Curfew: {formatCurfewTime(result.curfewStart)} - {formatCurfewTime(result.curfewEnd)}
                    </p>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NSWPartyPlanningUpdated;
