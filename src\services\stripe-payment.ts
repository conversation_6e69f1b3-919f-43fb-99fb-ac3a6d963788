/**
 * Stripe payment service for HouseGoing
 * Handles integration with Stripe API
 *
 * NOTE: This is a temporary implementation for testing purposes.
 * In a production environment, the createPaymentIntent and createCustomer methods
 * should be implemented as server-side API calls to keep the Stripe secret key secure.
 *
 * Future implementation should use the API endpoints in src/api/stripe.ts
 */
import { loadStripe } from '@stripe/stripe-js';
import axios from 'axios';
import { STRIPE_CONFIG } from '../config/stripe';

// Stripe payment service interface
export interface StripePaymentService {
  createPaymentIntent(paymentDetails: PaymentIntentDetails): Promise<PaymentIntentResponse>;
  confirmCardPayment(clientSecret: string, paymentMethod: any): Promise<PaymentConfirmationResponse>;
  createCustomer(customerDetails: CustomerDetails): Promise<CustomerResponse>;
  calculatePrice(basePrice: number, hours: number, options?: PricingOptions): PriceCalculation;
}

// Payment intent details interface
export interface PaymentIntentDetails {
  amount: number; // Amount in cents
  currency: string;
  description: string;
  customer_email?: string;
  metadata?: Record<string, string>;
  payment_method_types?: string[];
  receipt_email?: string;
}

// Payment intent response interface
export interface PaymentIntentResponse {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
}

// Payment confirmation response interface
export interface PaymentConfirmationResponse {
  id: string;
  status: string;
  amount: number;
  currency: string;
  payment_method: string;
  receipt_url?: string;
  created: number;
}

// Customer details interface
export interface CustomerDetails {
  email: string;
  name?: string;
  phone?: string;
  metadata?: Record<string, string>;
}

// Customer response interface
export interface CustomerResponse {
  id: string;
  email: string;
  name?: string;
  created: number;
}

// Venue pricing configuration interface
export interface VenuePricingConfig {
  progressivePricing?: {
    enabled: boolean;
    tiers: Array<{
      minHours: number;
      maxHours?: number;
      discountPercentage: number;
      label: string;
    }>;
  };
  surcharges?: {
    weekend?: number;
    holiday?: number;
    lateNight?: number;
    overtime?: number;
  };
}

// Pricing options interface
export interface PricingOptions {
  isWeekend?: boolean;
  isHoliday?: boolean;
  isLateNight?: boolean;
  isOvertime?: boolean;
  additionalSurcharge?: number;
  discountPercentage?: number;
  venuePricingConfig?: VenuePricingConfig;
}

// Price calculation interface
export interface PriceCalculation {
  basePrice: number;
  hours: number;
  hourlyRate: number;
  subtotal: number;
  platformFee: number;
  processingFee: number;
  surcharges: {
    weekend?: number;
    holiday?: number;
    lateNight?: number;
    additional?: number;
  };
  discount: number;
  total: number;
  breakdown: {
    [key: string]: number;
  };
}

/**
 * Stripe payment service implementation
 */
class StripePaymentServiceImpl implements StripePaymentService {
  private publishableKey: string;
  private secretKey: string;
  private apiVersion: string;
  private stripePromise: Promise<any>;

  constructor() {
    this.publishableKey = STRIPE_CONFIG.publishableKey;
    this.secretKey = STRIPE_CONFIG.secretKey;
    this.apiVersion = STRIPE_CONFIG.apiVersion;
    this.stripePromise = loadStripe(this.publishableKey);
  }

  /**
   * Create a payment intent
   * @param paymentDetails Payment intent details
   * @returns Payment intent response
   */
  async createPaymentIntent(paymentDetails: PaymentIntentDetails): Promise<PaymentIntentResponse> {
    try {
      // Validate input
      if (!paymentDetails.amount || paymentDetails.amount <= 0) {
        throw new Error('Invalid payment amount');
      }

      // Log the request for debugging
      console.log('Creating payment intent with details:', {
        amount: paymentDetails.amount,
        currency: paymentDetails.currency || STRIPE_CONFIG.currency,
        description: paymentDetails.description,
      });

      // Check if we're in development mode and functions aren't available
      const isDevelopment = import.meta.env.DEV;

      if (isDevelopment) {
        try {
          // Try the Netlify function first
          const response = await axios.post('/.netlify/functions/create-payment-intent', paymentDetails, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 3000, // Shorter timeout for dev
          });
          return response.data;
        } catch (error) {
          // If function not available, create a mock payment intent for development
          console.warn('Netlify functions not available in development, using mock payment intent');
          return this.createMockPaymentIntent(paymentDetails);
        }
      }

      // Production: Use the Netlify function endpoint (secure approach)
      const response = await axios.post('/.netlify/functions/create-payment-intent', paymentDetails, {
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout to prevent hanging requests
        timeout: 10000,
      });

      // Validate the response
      if (!response.data || !response.data.clientSecret) {
        throw new Error('Invalid payment intent response from server');
      }

      console.log('Payment intent created via server endpoint:', response.data);
      return response.data;
    } catch (error: any) {
      // Enhanced error handling
      console.error('Error creating payment intent:', error);

      // Format error message for better user experience
      const errorMessage = error.response?.data?.error?.message ||
                          error.message ||
                          'Failed to create payment intent';

      // Create a standardized error object
      const formattedError = new Error(errorMessage);

      // Add additional context to the error
      (formattedError as any).code = error.response?.data?.error?.code || 'unknown';
      (formattedError as any).type = error.response?.data?.error?.type || 'api_error';
      (formattedError as any).isServerError = !!error.response;

      this.handleError(formattedError);
      throw formattedError;
    }
  }

  /**
   * Confirm a card payment
   * @param clientSecret Client secret from payment intent
   * @param paymentMethod Payment method details
   * @returns Payment confirmation response
   */
  async confirmCardPayment(clientSecret: string, paymentMethod: any): Promise<PaymentConfirmationResponse> {
    try {
      const stripe = await this.stripePromise;
      const result = await stripe.confirmCardPayment(clientSecret, {
        payment_method: paymentMethod,
      });

      if (result.error) {
        throw result.error;
      }

      return result.paymentIntent;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Create a customer
   * @param customerDetails Customer details
   * @returns Customer response
   */
  async createCustomer(customerDetails: CustomerDetails): Promise<CustomerResponse> {
    try {
      // Validate input
      if (!customerDetails.email) {
        throw new Error('Customer email is required');
      }

      // Check if we're in development mode and functions aren't available
      const isDevelopment = import.meta.env.DEV;

      if (isDevelopment) {
        try {
          // Try the Netlify function first
          const response = await axios.post('/.netlify/functions/create-customer', customerDetails, {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 3000, // Shorter timeout for dev
          });
          return response.data;
        } catch (error) {
          // If function not available, create a mock customer for development
          console.warn('Netlify functions not available in development, using mock customer');
          return this.createMockCustomer(customerDetails);
        }
      }

      // Production: Use the Netlify function endpoint (secure approach)
      const response = await axios.post('/.netlify/functions/create-customer', customerDetails, {
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout to prevent hanging requests
        timeout: 10000,
      });

      // Validate the response
      if (!response.data || !response.data.id) {
        throw new Error('Invalid customer response from server');
      }

      console.log('Customer created via server endpoint:', response.data);
      return response.data;
    } catch (error: any) {
      // Enhanced error handling
      console.error('Error creating customer:', error);

      // Format error message for better user experience
      const errorMessage = error.response?.data?.error?.message ||
                          error.message ||
                          'Failed to create customer';

      // Create a standardized error object
      const formattedError = new Error(errorMessage);

      this.handleError(formattedError);
      throw formattedError;
    }
  }

  /**
   * Calculate price based on hourly rate and number of hours with venue-specific pricing
   * @param basePrice Base price per hour
   * @param hours Number of hours
   * @param options Pricing options including venue-specific configuration
   * @returns Price calculation
   */
  calculatePrice(basePrice: number, hours: number, options: PricingOptions = {}): PriceCalculation {
    const { venuePricingConfig } = options;

    // Get progressive pricing multiplier - use venue-specific if available
    let progressiveMultiplier = 1.0;
    let progressiveDiscount = 0;
    let progressiveLabel = '';

    if (venuePricingConfig?.progressivePricing?.enabled && venuePricingConfig.progressivePricing.tiers.length > 0) {
      // Use venue-specific progressive pricing
      const applicableTier = venuePricingConfig.progressivePricing.tiers
        .filter(tier => hours >= tier.minHours && (!tier.maxHours || hours <= tier.maxHours))
        .sort((a, b) => b.discountPercentage - a.discountPercentage)[0]; // Get highest discount applicable

      if (applicableTier) {
        progressiveDiscount = applicableTier.discountPercentage;
        progressiveMultiplier = 1 - (progressiveDiscount / 100);
        progressiveLabel = applicableTier.label;
      }
    } else {
      // Fallback to default progressive pricing
      progressiveMultiplier = this.getProgressiveMultiplier(hours);
      progressiveDiscount = (1 - progressiveMultiplier) * 100;
    }

    // Calculate hourly rate with progressive pricing
    const hourlyRate = basePrice * progressiveMultiplier;

    // Calculate subtotal (hourly rate * hours)
    const subtotal = hourlyRate * hours;

    // Calculate surcharges using venue-specific rates if available
    const surcharges = {
      weekend: options.isWeekend ?
        subtotal * ((venuePricingConfig?.surcharges?.weekend ?? 20) / 100) : 0,
      holiday: options.isHoliday ?
        subtotal * ((venuePricingConfig?.surcharges?.holiday ?? 30) / 100) : 0,
      lateNight: options.isLateNight ?
        subtotal * ((venuePricingConfig?.surcharges?.lateNight ?? 15) / 100) : 0,
      overtime: options.isOvertime && venuePricingConfig?.surcharges?.overtime ?
        subtotal * (venuePricingConfig.surcharges.overtime / 100) : 0,
      additional: options.additionalSurcharge || 0,
    };

    // Calculate total surcharges
    const totalSurcharges = Object.values(surcharges).reduce((sum, value) => sum + value, 0);

    // Calculate discount
    const discount = options.discountPercentage ? (subtotal * options.discountPercentage / 100) : 0;

    // Calculate fees
    const platformFee = subtotal * STRIPE_CONFIG.fees.platformFee;
    const subtotalWithSurcharges = subtotal + totalSurcharges - discount;
    const processingFee = (subtotalWithSurcharges * STRIPE_CONFIG.fees.paymentProcessingFee) + STRIPE_CONFIG.fees.fixedFee;

    // Calculate total
    const total = subtotalWithSurcharges + platformFee + processingFee;

    // Create breakdown
    const breakdown: { [key: string]: number } = {
      'Base Rate': basePrice,
      'Hours': hours,
      'Subtotal': basePrice * hours,
    };

    // Add progressive pricing discount if applicable
    if (progressiveDiscount > 0) {
      const progressiveSavings = (basePrice * hours) - subtotal;
      breakdown[progressiveLabel || `Progressive Discount (${progressiveDiscount}%)`] = -progressiveSavings;
    }

    // Add surcharges to breakdown
    if (surcharges.weekend > 0) {
      const weekendRate = venuePricingConfig?.surcharges?.weekend ?? 20;
      breakdown[`Weekend Surcharge (${weekendRate}%)`] = surcharges.weekend;
    }
    if (surcharges.holiday > 0) {
      const holidayRate = venuePricingConfig?.surcharges?.holiday ?? 30;
      breakdown[`Holiday Surcharge (${holidayRate}%)`] = surcharges.holiday;
    }
    if (surcharges.lateNight > 0) {
      const lateNightRate = venuePricingConfig?.surcharges?.lateNight ?? 15;
      breakdown[`Late Night Surcharge (${lateNightRate}%)`] = surcharges.lateNight;
    }
    if (surcharges.overtime > 0) {
      const overtimeRate = venuePricingConfig?.surcharges?.overtime ?? 0;
      breakdown[`Overtime Surcharge (${overtimeRate}%)`] = surcharges.overtime;
    }
    if (surcharges.additional > 0) breakdown['Additional Surcharge'] = surcharges.additional;
    if (discount > 0) breakdown['Additional Discount'] = -discount;

    breakdown['Platform Fee (10%)'] = platformFee;
    breakdown['Processing Fee'] = processingFee;

    return {
      basePrice,
      hours,
      hourlyRate,
      subtotal: subtotalWithSurcharges,
      platformFee,
      processingFee,
      surcharges,
      discount,
      total,
      breakdown,
    };
  }

  /**
   * Get progressive pricing multiplier based on hours
   * @param hours Number of hours
   * @returns Multiplier
   */
  private getProgressiveMultiplier(hours: number): number {
    const { progressivePricing } = STRIPE_CONFIG;

    if (hours <= progressivePricing.tier1.hours) {
      return progressivePricing.tier1.multiplier;
    } else if (hours <= progressivePricing.tier2.hours) {
      return progressivePricing.tier2.multiplier;
    } else if (hours <= progressivePricing.tier3.hours) {
      return progressivePricing.tier3.multiplier;
    } else {
      return progressivePricing.tier4.multiplier;
    }
  }

  /**
   * Create a mock payment intent for development
   * @param paymentDetails Payment details
   * @returns Mock payment intent response
   */
  private createMockPaymentIntent(paymentDetails: PaymentIntentRequest): PaymentIntentResponse {
    const mockId = `pi_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const mockClientSecret = `${mockId}_secret_${Math.random().toString(36).substr(2, 16)}`;

    console.log('🧪 Creating mock payment intent for development:', {
      id: mockId,
      amount: paymentDetails.amount,
      currency: paymentDetails.currency || 'aud',
      description: paymentDetails.description,
    });

    return {
      id: mockId,
      clientSecret: mockClientSecret,
      amount: Math.round(paymentDetails.amount * 100), // Convert to cents
      currency: paymentDetails.currency || 'aud',
      status: 'requires_payment_method',
      created: Math.floor(Date.now() / 1000),
    };
  }

  /**
   * Create a mock customer for development
   * @param customerDetails Customer details
   * @returns Mock customer response
   */
  private createMockCustomer(customerDetails: CustomerDetails): CustomerResponse {
    const mockId = `cus_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log('🧪 Creating mock customer for development:', {
      id: mockId,
      email: customerDetails.email,
      name: customerDetails.name,
    });

    return {
      id: mockId,
      email: customerDetails.email,
      name: customerDetails.name || 'Test Customer',
      created: Math.floor(Date.now() / 1000),
    };
  }

  /**
   * Handle API errors
   * @param error Error object
   */
  private handleError(error: any): void {
    if (error.response) {
      console.error('Stripe API error:', error.response.data);
    } else if (error.request) {
      console.error('Stripe API request error:', error.request);
    } else {
      console.error('Stripe error:', error.message);
    }
  }
}

// Export test card numbers for development
export const STRIPE_TEST_CARDS = {
  VISA_SUCCESS: '4242 4242 4242 4242', // Always succeeds
  VISA_AUTHENTICATION_REQUIRED: '4000 0025 0000 3155', // Requires authentication
  VISA_DECLINED: '4000 0000 0000 0002', // Always declined
  VISA_INSUFFICIENT_FUNDS: '4000 0000 0000 9995', // Declined for insufficient funds
  VISA_EXPIRED_CARD: '4000 0000 0000 0069', // Declined for expired card
  VISA_INCORRECT_CVC: '4000 0000 0000 0127', // Declined for incorrect CVC
  VISA_PROCESSING_ERROR: '4000 0000 0000 0119', // Declined for processing error
  MASTERCARD_SUCCESS: '5555 5555 5555 4444', // Always succeeds
  AMEX_SUCCESS: '3782 822463 10005', // Always succeeds
};

// WARNING: This implementation is for testing purposes only!
// In a production environment, the secret key should never be exposed in client-side code.
// A proper implementation would use a server-side API endpoint to create payment intents.
console.warn('SECURITY WARNING: Using client-side Stripe implementation for testing purposes only!');

// Export a singleton instance of the Stripe payment service
export const stripePaymentService = new StripePaymentServiceImpl();

export default stripePaymentService;
