/**
 * Simplified Clerk-Supabase Integration
 * 
 * This provides a basic Supabase client that works with Clerk authentication
 * without complex token handling that can cause loading issues.
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL!;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

/**
 * Create a basic Supabase client
 * This works for most operations and doesn't require complex Clerk token handling
 */
export function createSimpleSupabaseClient() {
  console.log('Creating simple Supabase client...');
  
  return createClient<Database>(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false
    }
  });
}

/**
 * Get a Supabase client instance
 */
export const supabase = createSimpleSupabaseClient();

export default supabase;
