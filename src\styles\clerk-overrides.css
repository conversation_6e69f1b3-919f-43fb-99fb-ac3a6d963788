/* Custom styles to override Clerk's default styling */

/* Hide the <PERSON> branding and development mode text */
.cl-footer,
.cl-footer-item,
.cl-footer-text,
.cl-footer-link,
.cl-development-mode-badge,
.cl-developmentModeText,
.cl-developmentModeBox,
.cl-internal-b3fm6y {
  display: none !important;
}

/* Improve the card styling */
.cl-card {
  border-radius: 1rem !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: none !important;
  overflow: hidden !important;
}

/* Style the form inputs */
.cl-form-control {
  margin-bottom: 1.25rem !important;
}

.cl-input {
  border-radius: 0.5rem !important;
  border: 1px solid #E5E7EB !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.2s ease-in-out !important;
}

.cl-input:focus {
  border-color: #7C3AED !important;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1) !important;
}

/* Style the buttons */
.cl-button-primary {
  background: linear-gradient(to right, #7C3AED, #EC4899) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease-in-out !important;
  border: none !important;
}

.cl-button-primary:hover {
  opacity: 0.9 !important;
  transform: translateY(-1px) !important;
}

/* Hide all social buttons */
.cl-socialButtonsBlockButton,
.cl-socialButtonsIconButton,
.cl-socialButtons,
.cl-socialButtonsBlockButtonText,
.cl-socialButtonsBlockButtonArrow,
.cl-socialButtonsProviderIcon {
  display: none !important;
}

/* Hide specific provider buttons */
.cl-socialButtonsIconButton__google,
.cl-socialButtonsIconButton__facebook,
.cl-socialButtonsIconButton__tiktok,
.cl-socialButtonsProviderIcon__google,
.cl-socialButtonsProviderIcon__facebook,
.cl-socialButtonsProviderIcon__tiktok {
  display: none !important;
}

/* Style the divider */
.cl-divider {
  margin: 1.5rem 0 !important;
}

.cl-dividerText {
  display: none !important;
}

.cl-dividerLine {
  background-color: #E5E7EB !important;
  height: 1px !important;
}

/* Style the form labels */
.cl-formFieldLabel {
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
}

/* Style the links */
.cl-footerActionLink {
  color: #7C3AED !important;
  font-weight: 500 !important;
  text-decoration: none !important;
}

.cl-footerActionLink:hover {
  text-decoration: underline !important;
}

/* Add a subtle animation to the card */
.cl-card {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Make the form more compact and elegant */
.cl-form {
  padding: 0.5rem !important;
}

/* Style the header */
.cl-headerTitle {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #1F2937 !important;
  margin-bottom: 0.5rem !important;
}

.cl-headerSubtitle {
  color: #6B7280 !important;
  font-size: 0.875rem !important;
}

/* Add a subtle hover effect to inputs */
.cl-input:hover {
  border-color: #D1D5DB !important;
}

/* Style the error messages */
.cl-formFieldErrorText {
  color: #EF4444 !important;
  font-size: 0.75rem !important;
  margin-top: 0.25rem !important;
}

/* Style the password visibility toggle */
.cl-passwordShowButton {
  color: #6B7280 !important;
}

.cl-passwordShowButton:hover {
  color: #374151 !important;
}

/* Remove any unwanted borders */
.cl-formButtonPrimary,
.cl-formButtonReset {
  border: none !important;
}

/* Style the sign-in/sign-up toggle link */
.cl-footerActionText {
  color: #6B7280 !important;
}

/* Add a subtle background pattern */
.cl-auth-form-wrapper {
  position: relative;
}

.cl-auth-form-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3Cpath d='M36 34c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2m-12 0c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2' fill='%239B7CFF' fill-opacity='.05'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: -1;
}

/* Add a subtle gradient background to the card */
.cl-card {
  background: linear-gradient(to bottom right, #FFFFFF, #FAFAFA) !important;
}
