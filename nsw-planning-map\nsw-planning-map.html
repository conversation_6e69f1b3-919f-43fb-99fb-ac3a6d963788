<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NSW Planning Map Lookup Tool</title>
  
  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  
  <!-- Custom CSS -->
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    
    .container {
      display: flex;
      height: 100vh;
    }
    
    .sidebar {
      width: 350px;
      padding: 20px;
      background-color: #f8f9fa;
      overflow-y: auto;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      z-index: 1000;
    }
    
    .map-container {
      flex: 1;
      position: relative;
    }
    
    #map {
      height: 100%;
      width: 100%;
    }
    
    .header {
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0 0 10px 0;
      font-size: 1.5rem;
      color: #333;
    }
    
    .header p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
    
    .search-container {
      margin-bottom: 20px;
    }
    
    .search-input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 1rem;
    }
    
    .search-button {
      width: 100%;
      padding: 10px;
      margin-top: 10px;
      background-color: #4a6ee0;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s;
    }
    
    .search-button:hover {
      background-color: #3a5bc0;
    }
    
    .search-button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    .results-container {
      margin-top: 20px;
      padding: 15px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .results-container h2 {
      margin-top: 0;
      font-size: 1.2rem;
      color: #333;
    }
    
    .results-container p {
      margin: 5px 0;
      font-size: 0.9rem;
    }
    
    .results-container .label {
      font-weight: bold;
      color: #555;
    }
    
    .results-container .value {
      color: #333;
    }
    
    .layer-control {
      margin-top: 20px;
    }
    
    .layer-control h2 {
      font-size: 1.2rem;
      margin-bottom: 10px;
      color: #333;
    }
    
    .layer-group {
      margin-bottom: 15px;
    }
    
    .layer-group h3 {
      font-size: 1rem;
      margin: 0 0 10px 0;
      color: #444;
    }
    
    .layer-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .layer-item input {
      margin-right: 8px;
    }
    
    .layer-item label {
      font-size: 0.9rem;
      color: #555;
      cursor: pointer;
    }
    
    .color-box {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 8px;
      border: 1px solid #ddd;
      vertical-align: middle;
    }
    
    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(255, 255, 255, 0.8);
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      display: none;
    }
    
    .loading-indicator.active {
      display: block;
    }
    
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 2s linear infinite;
      margin: 0 auto 10px auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-message {
      color: #d9534f;
      background-color: #f9f2f2;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
      font-size: 0.9rem;
      display: none;
    }
    
    .error-message.active {
      display: block;
    }
    
    .legend {
      padding: 10px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
      max-height: 300px;
      overflow-y: auto;
    }
    
    .legend h4 {
      margin: 0 0 10px 0;
      font-size: 1rem;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      font-size: 0.85rem;
    }
    
    .attribution {
      font-size: 0.8rem;
      color: #777;
      margin-top: 20px;
      padding-top: 10px;
      border-top: 1px solid #eee;
    }
    
    .popup-content {
      font-size: 0.9rem;
    }
    
    .popup-content h3 {
      margin: 0 0 5px 0;
      font-size: 1rem;
    }
    
    .popup-content p {
      margin: 3px 0;
    }
    
    .popup-content .label {
      font-weight: bold;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
      .container {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        max-height: 40vh;
        padding: 10px;
      }
      
      .map-container {
        height: 60vh;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="header">
        <h1>NSW Planning Map Lookup Tool</h1>
        <p>Search for zoning and council information across New South Wales</p>
      </div>
      
      <div class="search-container">
        <input type="text" id="address-input" class="search-input" placeholder="Enter an address in NSW...">
        <button id="search-button" class="search-button">Search</button>
        <div id="error-message" class="error-message"></div>
      </div>
      
      <div id="results-container" class="results-container" style="display: none;">
        <h2>Location Information</h2>
        <p><span class="label">Address:</span> <span id="result-address" class="value">-</span></p>
        <p><span class="label">Coordinates:</span> <span id="result-coordinates" class="value">-</span></p>
        <p><span class="label">Zone:</span> <span id="result-zone" class="value">-</span></p>
        <p><span class="label">Zone Name:</span> <span id="result-zone-name" class="value">-</span></p>
        <p><span class="label">Council:</span> <span id="result-council" class="value">-</span></p>
      </div>
      
      <div class="layer-control">
        <h2>Map Layers</h2>
        
        <div class="layer-group">
          <h3>Base Maps</h3>
          <div class="layer-item">
            <input type="radio" id="basemap-streets" name="basemap" value="streets" checked>
            <label for="basemap-streets">Streets</label>
          </div>
          <div class="layer-item">
            <input type="radio" id="basemap-satellite" name="basemap" value="satellite">
            <label for="basemap-satellite">Satellite</label>
          </div>
        </div>
        
        <div class="layer-group">
          <h3>Zoning Layers</h3>
          <div id="zoning-layers">
            <!-- Zoning layers will be added here dynamically -->
            <div class="layer-item">
              <input type="checkbox" id="zoning-layer-4" checked>
              <label for="zoning-layer-4">
                <span class="color-box" style="background-color: rgba(255, 200, 200, 0.7);"></span>
                Land Zoning
              </label>
            </div>
          </div>
        </div>
        
        <div class="layer-group">
          <h3>Administrative Boundaries</h3>
          <div id="admin-layers">
            <!-- Admin layers will be added here dynamically -->
            <div class="layer-item">
              <input type="checkbox" id="admin-layer-9">
              <label for="admin-layer-9">
                <span class="color-box" style="background-color: rgba(200, 200, 255, 0.7);"></span>
                Local Government Areas
              </label>
            </div>
          </div>
        </div>
      </div>
      
      <div class="attribution">
        <p>Data sources: NSW Department of Planning and Environment, NSW Spatial Services</p>
        <p>Basemap: © Mapbox © OpenStreetMap contributors</p>
      </div>
    </div>
    
    <div class="map-container">
      <div id="map"></div>
      <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Loading data...</p>
      </div>
    </div>
  </div>
  
  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  
  <!-- Custom JS -->
  <script src="nsw-planning-map.js"></script>
</body>
</html>
