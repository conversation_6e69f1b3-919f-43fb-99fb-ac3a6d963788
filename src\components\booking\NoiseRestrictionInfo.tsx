import React from 'react';
import { Volume2, Clock, MapPin, AlertTriangle, Info, Home, Users, Music } from 'lucide-react';
import { Venue } from '../../types/venue';

interface NoiseRestrictionInfoProps {
  venue: Venue;
}

// Helper function to get party score color
const getPartyScoreColor = (score: number) => {
  if (score >= 8) return 'bg-green-500';
  if (score >= 5) return 'bg-yellow-500';
  return 'bg-red-500';
};

// Helper function to get party score label
const getPartyScoreLabel = (score: number) => {
  if (score >= 8) return 'Party-friendly';
  if (score >= 5) return 'Moderate restrictions';
  return 'Strict restrictions';
};

export default function NoiseRestrictionInfo({ venue }: NoiseRestrictionInfoProps) {
  const { noiseRestrictions, partyScore } = venue;

  // If no noise restrictions are defined, show default message
  if (!noiseRestrictions) {
    return (
      <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-100">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-yellow-800">Noise Information</h4>
            <p className="text-sm text-yellow-700 mt-1">
              Please check with the host about any noise restrictions that may apply to this venue.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const {
    curfewTime,
    councilArea,
    allowsOvernight,
    notes,
    windowsClosedAfter,
    zoning,
    residentialProximity,
    soundproofing,
    outdoorMusic
  } = noiseRestrictions;

  return (
    <div className="bg-yellow-50 rounded-lg p-5 border border-yellow-100">
      {/* Party Score Section */}
      {partyScore && (
        <div className="mb-4 pb-4 border-b border-yellow-200">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-yellow-800 flex items-center">
              <Music className="h-5 w-5 mr-2" />
              Party Score
            </h4>
            <div className="flex items-center">
              <div className={`w-10 h-10 rounded-full ${getPartyScoreColor(partyScore.score)} text-white flex items-center justify-center font-bold text-lg`}>
                {partyScore.score}
              </div>
              <span className="ml-2 text-sm font-medium text-gray-700">{getPartyScoreLabel(partyScore.score)}</span>
            </div>
          </div>

          {partyScore.factors.length > 0 && (
            <div className="mt-3 bg-white bg-opacity-50 rounded-md p-3">
              <p className="text-xs text-gray-600 mb-2">Based on:</p>
              <ul className="text-sm text-gray-700 space-y-1 pl-5 list-disc">
                {partyScore.factors.map((factor, index) => (
                  <li key={index}>{factor}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Noise Restrictions Section */}
      <div className="flex items-start">
        <Volume2 className="h-5 w-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
        <div className="flex-1">
          <h4 className="font-medium text-yellow-800">Noise Restrictions</h4>

          <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
            {curfewTime && (
              <div className="flex items-start">
                <Clock className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Noise curfew</p>
                  <p className="text-sm text-yellow-700">
                    Loud music must be turned down after {curfewTime}
                  </p>
                </div>
              </div>
            )}

            {windowsClosedAfter && (
              <div className="flex items-start">
                <Home className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Windows & Doors</p>
                  <p className="text-sm text-yellow-700">
                    Must be closed after {windowsClosedAfter}
                  </p>
                </div>
              </div>
            )}

            {outdoorMusic && (
              <div className="flex items-start">
                <Music className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Outdoor Music</p>
                  <p className="text-sm text-yellow-700">
                    {outdoorMusic.allowed
                      ? `Allowed until ${outdoorMusic.until || curfewTime || '10:00 PM'}`
                      : 'Not allowed outdoors'}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-start">
              <Users className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-yellow-800">Overnight Stays</p>
                <p className="text-sm text-yellow-700">
                  {allowsOvernight ? 'Guests may stay overnight' : 'All guests must leave by the end time'}
                </p>
              </div>
            </div>

            {councilArea && (
              <div className="flex items-start">
                <MapPin className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Council Area</p>
                  <p className="text-sm text-yellow-700">{councilArea}</p>
                </div>
              </div>
            )}

            {zoning && (
              <div className="flex items-start">
                <Home className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Zoning</p>
                  <p className="text-sm text-yellow-700">
                    {zoning.charAt(0).toUpperCase() + zoning.slice(1)} area
                  </p>
                </div>
              </div>
            )}

            {residentialProximity && (
              <div className="flex items-start">
                <Home className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Residential Proximity</p>
                  <p className="text-sm text-yellow-700">
                    {residentialProximity === 'adjacent' ? 'Residential properties adjacent' :
                     residentialProximity === 'nearby' ? 'Residential properties nearby' :
                     'Residential properties distant'}
                  </p>
                </div>
              </div>
            )}

            {soundproofing !== undefined && (
              <div className="flex items-start">
                <Volume2 className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Soundproofing</p>
                  <p className="text-sm text-yellow-700">
                    {soundproofing ? 'Venue has soundproofing' : 'No soundproofing'}
                  </p>
                </div>
              </div>
            )}
          </div>

          {notes && (
            <div className="mt-4 pt-3 border-t border-yellow-200">
              <div className="flex items-start">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Additional Information</p>
                  <p className="text-sm text-yellow-700 mt-1">{notes}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
