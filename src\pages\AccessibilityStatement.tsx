import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ointer, Keyboard, Mail, Phone } from 'lucide-react';

export default function AccessibilityStatement() {
  return (
    <div className="pt-32 px-4 max-w-4xl mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex items-center mb-6">
          <Eye className="h-8 w-8 text-purple-600 mr-3" />
          <h1 className="text-3xl font-bold">Accessibility Statement</h1>
        </div>
        
        <div className="prose max-w-none">
          <p className="mb-6 text-lg">
            HouseGoing is committed to ensuring digital accessibility for people with disabilities. 
            We are continually improving the user experience for everyone and applying the relevant 
            accessibility standards.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">Our Commitment</h2>
          <p className="mb-4">
            We strive to conform to the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA standards. 
            These guidelines explain how to make web content more accessible for people with disabilities 
            and user-friendly for everyone.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">Accessibility Features</h2>
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Eye className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="font-semibold">Visual Accessibility</h4>
              </div>
              <ul className="text-sm list-disc pl-4">
                <li>High contrast color schemes</li>
                <li>Scalable text and images</li>
                <li>Alternative text for images</li>
                <li>Clear visual hierarchy</li>
              </ul>
            </div>
            
            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Keyboard className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="font-semibold">Keyboard Navigation</h4>
              </div>
              <ul className="text-sm list-disc pl-4">
                <li>Full keyboard accessibility</li>
                <li>Logical tab order</li>
                <li>Visible focus indicators</li>
                <li>Skip navigation links</li>
              </ul>
            </div>
            
            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Ear className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="font-semibold">Audio & Video</h4>
              </div>
              <ul className="text-sm list-disc pl-4">
                <li>Captions for video content</li>
                <li>Audio descriptions where needed</li>
                <li>No auto-playing audio</li>
                <li>Volume controls available</li>
              </ul>
            </div>
            
            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-2">
                <MousePointer className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="font-semibold">Motor Accessibility</h4>
              </div>
              <ul className="text-sm list-disc pl-4">
                <li>Large clickable areas</li>
                <li>No time-sensitive actions</li>
                <li>Drag and drop alternatives</li>
                <li>Gesture alternatives</li>
              </ul>
            </div>
          </div>

          <h2 className="text-xl font-semibold mt-6 mb-3">Assistive Technology Support</h2>
          <p className="mb-4">
            Our website is designed to work with assistive technologies including:
          </p>
          <ul className="list-disc pl-6 mb-6">
            <li>Screen readers (JAWS, NVDA, VoiceOver)</li>
            <li>Voice recognition software</li>
            <li>Keyboard-only navigation</li>
            <li>Screen magnification software</li>
            <li>Switch navigation devices</li>
          </ul>

          <h2 className="text-xl font-semibold mt-6 mb-3">Known Limitations</h2>
          <p className="mb-4">
            Despite our efforts, some limitations may exist. We are actively working to address:
          </p>
          <ul className="list-disc pl-6 mb-6">
            <li>Third-party content that may not meet accessibility standards</li>
            <li>Complex interactive maps (alternative text descriptions provided)</li>
            <li>PDF documents (we're working to make these more accessible)</li>
            <li>Some legacy content that is being updated</li>
          </ul>

          <h2 className="text-xl font-semibold mt-6 mb-3">Feedback & Support</h2>
          <p className="mb-4">
            We welcome your feedback on the accessibility of HouseGoing. If you encounter 
            accessibility barriers or have suggestions for improvement, please contact us:
          </p>
          
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-purple-900 mb-3">Accessibility Support</h4>
            <div className="space-y-2">
              <div className="flex items-center text-purple-800">
                <Mail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-purple-800">
                <Phone className="h-4 w-4 mr-2" />
                <span>1300 HOUSE GO (1300 468 734)</span>
              </div>
            </div>
            <p className="text-purple-700 text-sm mt-3">
              We aim to respond to accessibility feedback within 2 business days.
            </p>
          </div>

          <h2 className="text-xl font-semibold mt-6 mb-3">Ongoing Improvements</h2>
          <p className="mb-4">
            We regularly review and test our website for accessibility compliance. Our ongoing efforts include:
          </p>
          <ul className="list-disc pl-6 mb-6">
            <li>Regular accessibility audits by third-party experts</li>
            <li>User testing with people with disabilities</li>
            <li>Staff training on accessibility best practices</li>
            <li>Continuous monitoring and improvement of our platform</li>
          </ul>

          <h2 className="text-xl font-semibold mt-6 mb-3">Legal Framework</h2>
          <p className="mb-4">
            This accessibility statement is aligned with:
          </p>
          <ul className="list-disc pl-6 mb-6">
            <li>Disability Discrimination Act 1992 (Cth)</li>
            <li>Web Content Accessibility Guidelines (WCAG) 2.1 Level AA</li>
            <li>Australian Government Digital Service Standard</li>
            <li>EN 301 549 European accessibility standard</li>
          </ul>

          <div className="bg-gray-50 border rounded-lg p-4 mt-6">
            <h4 className="font-semibold mb-2">Alternative Formats</h4>
            <p className="text-sm">
              If you need information from our website in an alternative format, 
              please contact us and we'll provide it in a format that works for you, 
              such as large print, audio, or accessible electronic format.
            </p>
          </div>

          <p className="mt-8 text-sm text-gray-500">
            This statement was last updated: January 2025<br />
            Next review scheduled: July 2025
          </p>
        </div>
      </div>
    </div>
  );
}
