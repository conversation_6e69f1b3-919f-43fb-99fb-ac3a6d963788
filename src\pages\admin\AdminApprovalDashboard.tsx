/**
 * Admin Approval Dashboard
 *
 * Main dashboard for reviewing and approving venue submissions
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../hooks/useAdminAuth.tsx';
import { sendPropertyApprovalEmail, sendPropertyRejectionEmail } from '../../services/adminEmailService';
import {
  getAllPropertySubmissions,
  getPropertySubmissionStats,
  approvePropertyAndCreateVenue,
  rejectPropertySubmission
} from '../../services/propertyDataService';
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Mail,
  MapPin,
  Users,
  DollarSign,
  Calendar,
  AlertCircle,
  Filter,
  Search
} from 'lucide-react';

interface PropertySubmission {
  id: string;
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  ownerId: string;
  ownerEmail?: string;
  ownerName?: string;
  images?: string[];
  amenities?: string[];
  rejection_reason?: string;
  admin_notes?: string;
  approved_by?: string;
  approved_at?: string;
}

export default function AdminApprovalDashboard() {
  const { isAdmin, adminUser, isLoading } = useAdminAuth();
  const navigate = useNavigate();
  const [submissions, setSubmissions] = useState<PropertySubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  });

  // Load submissions
  useEffect(() => {
    if (!isAdmin) return;
    loadSubmissions();
  }, [isAdmin]);

  const loadSubmissions = async () => {
    try {
      setLoading(true);

      // Try to load real data first, fall back to mock data if needed
      console.log('Loading property submissions...');

      const realSubmissions = await getAllPropertySubmissions();
      const realStats = await getPropertySubmissionStats();

      if (realSubmissions.length > 0) {
        console.log('✅ Loaded real submissions:', realSubmissions.length);
        setSubmissions(realSubmissions);
        setStats(realStats);
        return;
      }

      // Fallback to mock data for testing
      console.log('📝 Using mock data for testing...');
      const mockSubmissions = [
        {
          id: 'mock-1',
          name: 'Harbour View Rooftop',
          address: '123 Circular Quay, Sydney NSW 2000',
          type: 'Rooftop',
          description: 'Beautiful rooftop venue with stunning harbour views, perfect for parties and events. Features modern amenities and professional lighting.',
          maxGuests: 50,
          price: 150,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          ownerId: 'dev_host_001',
          ownerEmail: '<EMAIL>',
          ownerName: 'Tom Chen',
          images: [
            'https://images.unsplash.com/photo-1566737236500-c8ac43014a67',
            'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8'
          ],
          amenities: ['Sound System', 'Lighting', 'Bar Area', 'Harbour Views', 'Parking']
        },
        {
          id: 'mock-2',
          name: 'Industrial Warehouse Space',
          address: '456 Industrial Lane, Marrickville NSW 2204',
          type: 'Warehouse',
          description: 'Spacious industrial warehouse with exposed brick walls and high ceilings. Perfect for large events and creative parties.',
          maxGuests: 200,
          price: 200,
          status: 'pending',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          ownerId: 'dev_host_001',
          ownerEmail: '<EMAIL>',
          ownerName: 'Tom Chen',
          images: [
            'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
            'https://images.unsplash.com/photo-1571902943202-507ec2618e8f'
          ],
          amenities: ['Sound System', 'High Ceilings', 'Loading Dock', 'Parking', 'Kitchen']
        },
        {
          id: 'mock-3',
          name: 'Beachside Garden Villa',
          address: '789 Beach Road, Bondi NSW 2026',
          type: 'Villa',
          description: 'Elegant beachside villa with beautiful gardens and ocean views. Ideal for intimate gatherings and celebrations.',
          maxGuests: 30,
          price: 120,
          status: 'approved',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          ownerId: 'dev_customer_001',
          ownerEmail: '<EMAIL>',
          ownerName: 'Sarah Johnson',
          images: [
            'https://images.unsplash.com/photo-1564013799919-ab600027ffc6',
            'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9'
          ],
          amenities: ['Garden', 'Ocean Views', 'Pool', 'BBQ Area', 'Parking'],
          approved_by: '<EMAIL>',
          approved_at: new Date(Date.now() - 86400000).toISOString(),
          admin_notes: 'Excellent property with great amenities'
        },
        {
          id: 'mock-4',
          name: 'Urban Loft Space',
          address: '321 King Street, Newtown NSW 2042',
          type: 'Loft',
          description: 'Modern urban loft with contemporary design and city views. Perfect for stylish parties and corporate events.',
          maxGuests: 80,
          price: 180,
          status: 'rejected',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          updated_at: new Date(Date.now() - 172800000).toISOString(),
          ownerId: 'dev_host_001',
          ownerEmail: '<EMAIL>',
          ownerName: 'Tom Chen',
          images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7',
            'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2'
          ],
          amenities: ['City Views', 'Modern Kitchen', 'Sound System', 'Elevator', 'Parking'],
          approved_by: '<EMAIL>',
          approved_at: new Date(Date.now() - 172800000).toISOString(),
          rejection_reason: 'Property needs better photos and more detailed description',
          admin_notes: 'Good potential but requires improvements'
        },
        {
          id: 'mock-5',
          name: 'Backyard Pool Paradise',
          address: '654 Suburban Street, Manly NSW 2095',
          type: 'Backyard',
          description: 'Beautiful backyard with swimming pool and entertainment area. Great for pool parties and summer celebrations.',
          maxGuests: 40,
          price: 100,
          status: 'pending',
          created_at: new Date(Date.now() - 345600000).toISOString(),
          updated_at: new Date(Date.now() - 345600000).toISOString(),
          ownerId: 'dev_customer_001',
          ownerEmail: '<EMAIL>',
          ownerName: 'Sarah Johnson',
          images: [
            'https://images.unsplash.com/photo-1544551763-46a013bb70d5',
            'https://images.unsplash.com/photo-1571896349842-33c89424de2d'
          ],
          amenities: ['Swimming Pool', 'BBQ Area', 'Outdoor Seating', 'Garden', 'Parking']
        }
      ];

      setSubmissions(mockSubmissions);

      // Calculate stats from mock data
      const stats = {
        total: mockSubmissions.length,
        pending: mockSubmissions.filter(s => s.status === 'pending').length,
        approved: mockSubmissions.filter(s => s.status === 'approved').length,
        rejected: mockSubmissions.filter(s => s.status === 'rejected').length
      };
      setStats(stats);

      console.log('✅ Mock data loaded successfully:', stats);

    } catch (error) {
      console.error('Error loading submissions:', error);
      setSubmissions([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter submissions
  const filteredSubmissions = submissions.filter(submission => {
    const matchesFilter = filter === 'all' || submission.status === filter;
    const matchesSearch = searchTerm === '' ||
      submission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (submission.ownerName && submission.ownerName.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesFilter && matchesSearch;
  });

  // All properties require detailed review through the detail page

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Approval Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {adminUser?.name}! Review and approve venue submissions.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Calendar className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Submissions</option>
                <option value="pending">Pending ({stats.pending})</option>
                <option value="approved">Approved ({stats.approved})</option>
                <option value="rejected">Rejected ({stats.rejected})</option>
              </select>
            </div>

            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by venue name, address, or owner..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Submissions List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading submissions...</p>
            </div>
          ) : filteredSubmissions.length === 0 ? (
            <div className="p-8 text-center">
              <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No submissions found</h3>
              <p className="text-gray-600">
                {filter === 'all'
                  ? 'No venue submissions yet.'
                  : `No ${filter} submissions found.`}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredSubmissions.map((submission) => (
                <SubmissionCard
                  key={submission.id}
                  submission={submission}
                  onViewDetails={() => navigate(`/admin/submissions/${submission.id}`)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Submission Card Component
function SubmissionCard({
  submission,
  onViewDetails
}: {
  submission: PropertySubmission;
  onViewDetails: () => void;
}) {

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Pending</span>;
      case 'approved':
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Approved</span>;
      case 'rejected':
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Rejected</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">{status}</span>;
    }
  };

  return (
    <div className="p-6 hover:bg-gray-50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{submission.name}</h3>
            {getStatusBadge(submission.status)}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="w-4 h-4 mr-2" />
                {submission.address}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Users className="w-4 h-4 mr-2" />
                Up to {submission.maxGuests} guests
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <DollarSign className="w-4 h-4 mr-2" />
                ${submission.price}/hour
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <Mail className="w-4 h-4 mr-2" />
                {submission.ownerName} ({submission.ownerEmail})
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="w-4 h-4 mr-2" />
                Submitted {new Date(submission.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>

          {submission.description && (
            <p className="text-sm text-gray-600 mb-4 line-clamp-2">
              {submission.description}
            </p>
          )}
        </div>

        <div className="ml-6 flex flex-col space-y-2">
          <button
            onClick={onViewDetails}
            className="flex items-center px-3 py-2 text-sm font-medium text-purple-600 hover:text-purple-700 border border-purple-200 hover:border-purple-300 rounded-lg transition-colors"
          >
            <Eye className="w-4 h-4 mr-1" />
            View Details
          </button>

          {/* All properties require detailed review - no quick actions */}
        </div>
      </div>
    </div>
  );
}
