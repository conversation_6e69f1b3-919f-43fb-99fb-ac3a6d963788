import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

export default function ListSpace() {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      navigate('/host/onboarding');
    } else {
      navigate('/host/login');
    }
  };

  return (
    <div className="pt-32 px-4 sm:px-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-6">List Your Space</h1>
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Turn your property into the next popular party venue
              </h2>
              <p className="text-lg text-gray-600">
                Join House<PERSON>oing and start earning by sharing your space with those looking for the perfect venue.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="p-6 bg-purple-50 rounded-xl">
                <div className="text-2xl font-bold text-purple-600 mb-2">$2,500+</div>
                <div className="text-gray-700">Average monthly earnings for hosts in NSW</div>
              </div>
              <div className="p-6 bg-purple-50 rounded-xl">
                <div className="text-2xl font-bold text-purple-600 mb-2">1M+</div>
                <div className="text-gray-700">Active users searching for venues monthly</div>
              </div>
              <div className="p-6 bg-purple-50 rounded-xl">
                <div className="text-2xl font-bold text-purple-600 mb-2">24/7</div>
                <div className="text-gray-700">Support team to help you succeed</div>
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-800">Why host with HouseGoing?</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Verified Guests</h4>
                    <p className="text-gray-600">All guests are verified through our thorough screening process</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Secure Payments</h4>
                    <p className="text-gray-600">Get paid securely and on time, every time</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Insurance Coverage</h4>
                    <p className="text-gray-600">$1M liability insurance for every booking</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Flexible Control</h4>
                    <p className="text-gray-600">Set your own pricing and availability</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center space-y-4">
              <button
                onClick={handleGetStarted}
                className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-full hover:opacity-90 transition-opacity"
              >
                Get Started Now
              </button>
              <p className="text-sm text-gray-600">
                No obligations, you can stop hosting anytime
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}