# NSW Planning Map Lookup Tool

A comprehensive tool for looking up zoning and council information across New South Wales using official government data services.

## Features

- Interactive map with Mapbox basemap tiles
- Address search using Maps.co Geocoding API
- Display of zoning and administrative boundaries from NSW Planning Portal
- Spatial queries for zoning and council information
- Layer controls for toggling different map layers
- Responsive design for desktop and mobile devices

## Data Sources

This tool uses the following official NSW government data services:

### Zoning Data

- **WMS**: https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer
- **WFS**: https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer
- **Capabilities**: https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer?service=WMS&request=GetCapabilities

### Administrative Boundaries

- **WMS**: https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer
- **WFS**: https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer
- **Capabilities**: https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer?service=WMS&request=GetCapabilities

## Setup

### Prerequisites

- Node.js (v12 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd nsw-planning-map
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the proxy server:
   ```
   npm start
   ```

4. Open the application in your browser:
   ```
   http://localhost:3000/nsw-planning-map.html
   ```

## Usage

1. **Search for an address**: Enter an address in NSW in the search box and click "Search" or press Enter.
2. **Click on the map**: Click anywhere on the map to get zoning and council information for that location.
3. **Toggle map layers**: Use the layer controls in the sidebar to toggle different zoning and administrative boundary layers.
4. **Switch basemaps**: Choose between "Streets" and "Satellite" basemaps.

## Technical Details

### Libraries and APIs

- **Leaflet.js**: For the interactive map
- **Mapbox**: For the basemap tiles
- **Maps.co**: For geocoding addresses
- **NSW Planning Portal**: For zoning and administrative boundary data

### Architecture

The application is built with vanilla JavaScript and follows an object-oriented approach:

- **NSWPlanningMap**: Main application class that handles map initialization, event listeners, and data fetching
- **proxy-server.js**: Simple Express server to handle CORS issues with WFS requests

### Spatial Queries

The application uses multiple approaches to fetch spatial data:

1. **WFS with CQL_FILTER**: Primary method for spatial queries
2. **WFS with XML Filter**: Alternative method if CQL_FILTER fails
3. **WMS GetFeatureInfo**: Fallback method if both WFS approaches fail

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- NSW Department of Planning and Environment for providing the zoning data
- NSW Spatial Services for providing the administrative boundary data
- OpenStreetMap contributors for the basemap data
