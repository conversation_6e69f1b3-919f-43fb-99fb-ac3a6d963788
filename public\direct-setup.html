<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct Supabase Setup</title>
  <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #6366f1;
    }
    button {
      background-color: #6366f1;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
    }
    button:hover {
      background-color: #4f46e5;
    }
    button:disabled {
      background-color: #a5a6f6;
      cursor: not-allowed;
    }
    .log {
      margin-top: 20px;
      border: 1px solid #e2e8f0;
      padding: 15px;
      border-radius: 4px;
      max-height: 400px;
      overflow-y: auto;
      background-color: #f8fafc;
      font-family: monospace;
    }
    .success {
      color: #10b981;
    }
    .error {
      color: #ef4444;
    }
    .warning {
      color: #f59e0b;
    }
    .info {
      color: #3b82f6;
    }
  </style>
</head>
<body>
  <h1>Direct Supabase Setup</h1>
  <p>This tool will directly set up your Supabase database for the HouseGoing application.</p>
  
  <div>
    <button id="setupButton">Set Up Database</button>
    <button id="verifyButton">Verify Setup</button>
  </div>
  
  <h2>Log</h2>
  <div id="log" class="log"></div>
  
  <script>
    // Supabase configuration
    const SUPABASE_URL = window.SUPABASE_URL || '';
    const SUPABASE_SERVICE_KEY = window.SUPABASE_SERVICE_KEY || '';
    
    // Create Supabase client
    const supabase = supabase.createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    
    // DOM elements
    const setupButton = document.getElementById('setupButton');
    const verifyButton = document.getElementById('verifyButton');
    const logElement = document.getElementById('log');
    
    // Log function
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logEntry.classList.add(type);
      logElement.appendChild(logEntry);
      logElement.scrollTop = logElement.scrollHeight;
    }
    
    // SQL statements
    const SQL = {
      createUserProfilesTable: `
        CREATE TABLE IF NOT EXISTS user_profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          clerk_id TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          role TEXT NOT NULL DEFAULT 'guest',
          full_name TEXT,
          avatar_url TEXT,
          bio TEXT,
          phone TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
      
      createVenuesTable: `
        CREATE TABLE IF NOT EXISTS venues (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          owner_id TEXT NOT NULL,
          title TEXT NOT NULL,
          description TEXT,
          location TEXT NOT NULL,
          address TEXT NOT NULL,
          city TEXT NOT NULL,
          state TEXT NOT NULL,
          zip_code TEXT NOT NULL,
          coordinates JSONB,
          price DECIMAL(10,2) NOT NULL,
          capacity INTEGER NOT NULL,
          amenities JSONB,
          house_rules TEXT,
          images TEXT[],
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
      
      createBookingsTable: `
        CREATE TABLE IF NOT EXISTS bookings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          venue_id UUID NOT NULL,
          guest_id TEXT NOT NULL,
          start_date TIMESTAMP WITH TIME ZONE NOT NULL,
          end_date TIMESTAMP WITH TIME ZONE NOT NULL,
          guests_count INTEGER NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
      
      createReviewsTable: `
        CREATE TABLE IF NOT EXISTS reviews (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          booking_id UUID NOT NULL,
          venue_id UUID NOT NULL,
          reviewer_id TEXT NOT NULL,
          rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
          comment TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
      
      createMessagesTable: `
        CREATE TABLE IF NOT EXISTS messages (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          booking_id UUID,
          sender_id TEXT NOT NULL,
          recipient_id TEXT NOT NULL,
          content TEXT NOT NULL,
          is_read BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    };
    
    // Execute SQL directly
    async function executeSql(sql) {
      try {
        // Use direct SQL execution
        const { data, error } = await supabase.rpc('pg_query', { query: sql });
        
        if (error) {
          log(`Error executing SQL: ${error.message}`, 'error');
          return false;
        }
        
        return true;
      } catch (error) {
        log(`Exception executing SQL: ${error.message}`, 'error');
        
        // Try alternative method
        try {
          log('Trying alternative method...', 'warning');
          
          // Use direct fetch for SQL execution
          const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/pg_query`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
              'apikey': SUPABASE_SERVICE_KEY
            },
            body: JSON.stringify({ query: sql })
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            log(`Alternative method failed: ${errorText}`, 'error');
            return false;
          }
          
          log('Alternative method succeeded', 'success');
          return true;
        } catch (altError) {
          log(`Alternative method exception: ${altError.message}`, 'error');
          return false;
        }
      }
    }
    
    // Create a table
    async function createTable(tableName, sql) {
      log(`Creating ${tableName} table...`);
      
      const success = await executeSql(sql);
      
      if (success) {
        log(`${tableName} table created successfully`, 'success');
      } else {
        log(`Failed to create ${tableName} table`, 'error');
      }
      
      return success;
    }
    
    // Insert pre-registered host
    async function insertPreregisteredHost() {
      log('Inserting pre-registered host...');
      
      try {
        const { data, error } = await supabase
          .from('user_profiles')
          .upsert([
            {
              clerk_id: 'pre-registered-host-1',
              email: '<EMAIL>',
              role: 'host',
              full_name: 'Tom C'
            }
          ]);
        
        if (error) {
          log(`Error inserting pre-registered host: ${error.message}`, 'error');
          return false;
        }
        
        log('Pre-registered host inserted successfully', 'success');
        return true;
      } catch (error) {
        log(`Exception inserting pre-registered host: ${error.message}`, 'error');
        return false;
      }
    }
    
    // Check if a table exists
    async function checkTableExists(tableName) {
      try {
        // Try to select from the table
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error && error.code === '42P01') {
          // Table doesn't exist
          return false;
        }
        
        return true;
      } catch (error) {
        log(`Exception checking if table ${tableName} exists: ${error.message}`, 'error');
        return false;
      }
    }
    
    // Verify setup
    async function verifySetup() {
      log('Verifying setup...');
      
      const tables = [
        'user_profiles',
        'venues',
        'bookings',
        'reviews',
        'messages'
      ];
      
      let allTablesExist = true;
      
      for (const tableName of tables) {
        const exists = await checkTableExists(tableName);
        
        if (exists) {
          log(`Table ${tableName} exists`, 'success');
          
          // Count rows
          try {
            const { count, error } = await supabase
              .from(tableName)
              .select('*', { count: 'exact', head: true });
            
            if (!error) {
              log(`Table ${tableName} has ${count} rows`, 'info');
            }
          } catch (error) {
            log(`Error counting rows in ${tableName}: ${error.message}`, 'error');
          }
        } else {
          log(`Table ${tableName} does not exist`, 'error');
          allTablesExist = false;
        }
      }
      
      if (allTablesExist) {
        log('All tables exist. Setup is complete!', 'success');
      } else {
        log('Some tables are missing. Setup is incomplete.', 'error');
      }
      
      return allTablesExist;
    }
    
    // Set up database
    async function setupDatabase() {
      log('Starting database setup...');
      
      // Disable buttons
      setupButton.disabled = true;
      verifyButton.disabled = true;
      
      try {
        // Create tables
        await createTable('user_profiles', SQL.createUserProfilesTable);
        await createTable('venues', SQL.createVenuesTable);
        await createTable('bookings', SQL.createBookingsTable);
        await createTable('reviews', SQL.createReviewsTable);
        await createTable('messages', SQL.createMessagesTable);
        
        // Insert pre-registered host
        await insertPreregisteredHost();
        
        // Verify setup
        await verifySetup();
        
        log('Database setup completed!', 'success');
      } catch (error) {
        log(`Error setting up database: ${error.message}`, 'error');
      } finally {
        // Re-enable buttons
        setupButton.disabled = false;
        verifyButton.disabled = false;
      }
    }
    
    // Add event listeners
    setupButton.addEventListener('click', setupDatabase);
    verifyButton.addEventListener('click', verifySetup);
    
    // Log initial message
    log('Ready to set up database. Click "Set Up Database" to begin.', 'info');
  </script>
</body>
</html>
