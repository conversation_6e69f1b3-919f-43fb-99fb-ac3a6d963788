import React, { Suspense } from 'react';
import { Route, Routes } from 'react-router-dom';

// Loading fallback
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
);

// Lazy-loaded components
const HomePage = React.lazy(() => import('../pages/HomePage'));
const FindVenues = React.lazy(() => import('../pages/FindVenues'));
const VenueDetail = React.lazy(() => import('../pages/VenueDetail'));
const ListSpace = React.lazy(() => import('../pages/ListSpace'));
const HowItWorks = React.lazy(() => import('../pages/HowItWorks'));
const MyAccount = React.lazy(() => import('../pages/MyAccount'));
const UserProfile = React.lazy(() => import('../pages/UserProfile'));
const UserSettings = React.lazy(() => import('../pages/UserSettings'));
const MessagesPage = React.lazy(() => import('../pages/Messages'));
const SalesAssistant = React.lazy(() => import('../pages/SalesAssistant'));
const VenueAssistant = React.lazy(() => import('../pages/VenueAssistant'));
const BecomeHost = React.lazy(() => import('../pages/BecomeHost'));
const Terms = React.lazy(() => import('../pages/Terms'));
const Privacy = React.lazy(() => import('../pages/Privacy'));
const NotFound = React.lazy(() => import('../pages/NotFound'));
const NSWPartyPlanningUpdated = React.lazy(() => import('../pages/NSWPartyPlanningUpdated'));

// Host pages
const HostDashboard = React.lazy(() => import('../pages/host/Dashboard'));
const OwnerPortal = React.lazy(() => import('../pages/host/OwnerPortal'));
const Properties = React.lazy(() => import('../pages/host/Properties'));
const PropertyForm = React.lazy(() => import('../pages/host/PropertyForm'));
const Bookings = React.lazy(() => import('../pages/host/Bookings'));
const Earnings = React.lazy(() => import('../pages/host/Earnings'));
const HostMessages = React.lazy(() => import('../pages/host/MessagesSimple'));
const HostSettings = React.lazy(() => import('../pages/host/Settings'));
const HostHelp = React.lazy(() => import('../pages/host/Help'));

// Admin pages
const AdminDashboard = React.lazy(() => import('../pages/admin/Dashboard'));
const AIAnalytics = React.lazy(() => import('../pages/admin/AIAnalytics'));

// Auth components
const Login = React.lazy(() => import('../pages/auth/SignIn'));
const Signup = React.lazy(() => import('../pages/auth/SignUp'));

// Route guards
const ProtectedRoute = React.lazy(() => import('./ProtectedRoute'));
const HostProtectedRoute = React.lazy(() => import('./HostProtectedRoute'));
const AdminProtectedRoute = React.lazy(() => import('./AdminProtectedRoute'));

export const LazyRoutes = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<HomePage />} />
        <Route path="/find-venues" element={<FindVenues />} />
        <Route path="/venue/:id" element={<VenueDetail />} />
        <Route path="/list-space" element={<ListSpace />} />
        <Route path="/how-it-works" element={<HowItWorks />} />
        <Route path="/become-host" element={<BecomeHost />} />
        <Route path="/terms" element={<Terms />} />
        <Route path="/privacy" element={<Privacy />} />
        <Route path="/ai-assistant" element={<SalesAssistant />} />
        <Route path="/venue-assistant" element={<VenueAssistant />} />
        <Route path="/nsw-party-planning-updated" element={<NSWPartyPlanningUpdated />} />

        {/* Auth routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<Signup />} />

        {/* Protected user routes */}
        <Route path="/account" element={<ProtectedRoute><MyAccount /></ProtectedRoute>} />
        <Route path="/profile" element={<ProtectedRoute><UserProfile /></ProtectedRoute>} />
        <Route path="/settings" element={<ProtectedRoute><UserSettings /></ProtectedRoute>} />
        <Route path="/messages" element={<ProtectedRoute><MessagesPage /></ProtectedRoute>} />

        {/* Host routes */}
        <Route path="/host" element={<HostProtectedRoute><OwnerPortal /></HostProtectedRoute>} />
        <Route path="/host/dashboard" element={<HostProtectedRoute><HostDashboard /></HostProtectedRoute>} />
        <Route path="/host/properties" element={<HostProtectedRoute><Properties /></HostProtectedRoute>} />
        <Route path="/host/properties/new" element={<HostProtectedRoute><PropertyForm /></HostProtectedRoute>} />
        <Route path="/host/properties/:id" element={<HostProtectedRoute><PropertyForm /></HostProtectedRoute>} />
        <Route path="/host/bookings" element={<HostProtectedRoute><Bookings /></HostProtectedRoute>} />
        <Route path="/host/earnings" element={<HostProtectedRoute><Earnings /></HostProtectedRoute>} />
        <Route path="/host/messages" element={<HostProtectedRoute><HostMessages /></HostProtectedRoute>} />
        <Route path="/host/settings" element={<HostProtectedRoute><HostSettings /></HostProtectedRoute>} />
        <Route path="/host/help" element={<HostProtectedRoute><HostHelp /></HostProtectedRoute>} />

        {/* Admin routes */}
        <Route path="/admin" element={<AdminProtectedRoute><AdminDashboard /></AdminProtectedRoute>} />
        <Route path="/admin/ai-analytics" element={<AdminProtectedRoute><AIAnalytics /></AdminProtectedRoute>} />

        {/* 404 route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
};
