import { supabase } from '../../supabase';

// Migration interface
export interface Migration {
  id: number;
  name: string;
  up: () => Promise<void>;
  down: () => Promise<void>;
}

// Migration history table
const MIGRATION_TABLE = 'migrations';

/**
 * Initialize the migrations system
 */
export async function initMigrations(): Promise<void> {
  // Check if migrations table exists
  const { error } = await supabase.from(MIGRATION_TABLE).select('id').limit(1);
  
  if (error && error.code === '42P01') { // Table doesn't exist
    // Create migrations table
    await supabase.rpc('create_migrations_table', {
      table_name: MIGRATION_TABLE
    });
  }
}

/**
 * Get all applied migrations
 */
export async function getAppliedMigrations(): Promise<number[]> {
  const { data, error } = await supabase
    .from(MIGRATION_TABLE)
    .select('id')
    .order('id', { ascending: true });
  
  if (error) {
    console.error('Error fetching applied migrations:', error);
    return [];
  }
  
  return data.map(m => m.id);
}

/**
 * Apply pending migrations
 * @param migrations Array of migrations to apply
 */
export async function applyMigrations(migrations: Migration[]): Promise<void> {
  // Initialize migrations system
  await initMigrations();
  
  // Get applied migrations
  const appliedMigrations = await getAppliedMigrations();
  
  // Sort migrations by ID
  const sortedMigrations = [...migrations].sort((a, b) => a.id - b.id);
  
  // Apply pending migrations
  for (const migration of sortedMigrations) {
    if (!appliedMigrations.includes(migration.id)) {
      console.log(`Applying migration ${migration.id}: ${migration.name}`);
      
      try {
        // Start transaction
        await supabase.rpc('begin_transaction');
        
        // Apply migration
        await migration.up();
        
        // Record migration
        await supabase.from(MIGRATION_TABLE).insert({
          id: migration.id,
          name: migration.name,
          applied_at: new Date().toISOString()
        });
        
        // Commit transaction
        await supabase.rpc('commit_transaction');
        
        console.log(`Migration ${migration.id} applied successfully`);
      } catch (error) {
        // Rollback transaction
        await supabase.rpc('rollback_transaction');
        
        console.error(`Error applying migration ${migration.id}:`, error);
        throw error;
      }
    }
  }
}

/**
 * Rollback the last applied migration
 * @param migrations Array of all migrations
 */
export async function rollbackLastMigration(migrations: Migration[]): Promise<void> {
  // Get applied migrations
  const { data, error } = await supabase
    .from(MIGRATION_TABLE)
    .select('id, name')
    .order('id', { ascending: false })
    .limit(1);
  
  if (error || !data.length) {
    console.error('Error fetching last migration:', error);
    return;
  }
  
  const lastMigration = data[0];
  const migrationToRollback = migrations.find(m => m.id === lastMigration.id);
  
  if (!migrationToRollback) {
    console.error(`Migration ${lastMigration.id} not found in migrations array`);
    return;
  }
  
  console.log(`Rolling back migration ${lastMigration.id}: ${lastMigration.name}`);
  
  try {
    // Start transaction
    await supabase.rpc('begin_transaction');
    
    // Rollback migration
    await migrationToRollback.down();
    
    // Remove migration record
    await supabase
      .from(MIGRATION_TABLE)
      .delete()
      .eq('id', lastMigration.id);
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    console.log(`Migration ${lastMigration.id} rolled back successfully`);
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    
    console.error(`Error rolling back migration ${lastMigration.id}:`, error);
    throw error;
  }
}
