import { handleApiRequest } from './api/routes';

// Main server handler
export async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  
  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    return await handleApiRequest(request);
  }
  
  // For all other requests, let the frontend handle it
  return new Response('Not found', { status: 404 });
}
