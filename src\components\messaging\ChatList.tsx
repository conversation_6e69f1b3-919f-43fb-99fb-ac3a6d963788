import React from 'react';
import { useChats } from '../../hooks/useChats';
import { formatDate } from '../../utils/dates';

export default function ChatList() {
  const { chats, isLoading } = useChats();

  if (isLoading) {
    return <div>Loading conversations...</div>;
  }

  return (
    <div className="space-y-2">
      {chats?.map((chat) => (
        <div 
          key={chat.id} 
          className="flex items-center space-x-4 p-4 bg-white rounded-lg hover:bg-gray-50 cursor-pointer"
        >
          <img
            src={chat.otherUser.image}
            alt={chat.otherUser.name}
            className="w-12 h-12 rounded-full"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold truncate">{chat.otherUser.name}</h3>
              <span className="text-sm text-gray-500">
                {formatDate(chat.lastMessage.timestamp)}
              </span>
            </div>
            <p className="text-sm text-gray-600 truncate">
              {chat.lastMessage.content}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}