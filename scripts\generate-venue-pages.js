#!/usr/bin/env node

/**
 * Venue Static Page Generator for SEO
 * 
 * This script generates static HTML files for venue pages to ensure
 * Google can crawl and index them properly.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const publicDir = path.resolve(__dirname, '../public');

// Base URL for the website
const baseUrl = 'https://housegoing.com.au';

// Mock venue data for static generation
const mockVenues = [
  {
    id: 'venue-001',
    title: 'Luxury Rooftop Terrace - Sydney CBD',
    description: 'Stunning rooftop venue in the heart of Sydney CBD with panoramic city views. Perfect for corporate events, parties, and celebrations.',
    location: 'Sydney CBD, NSW',
    capacity: 150,
    price: 250,
    rating: 4.8,
    amenities: ['City Views', 'Sound System', 'Bar Area', 'Catering Kitchen', 'Parking']
  },
  {
    id: 'venue-002',
    title: 'Beachside Villa - Bondi Beach',
    description: 'Beautiful beachside villa just steps from Bondi Beach. Ideal for beach parties, weddings, and intimate gatherings.',
    location: 'Bondi Beach, NSW',
    capacity: 80,
    price: 180,
    rating: 4.9,
    amenities: ['Beach Access', 'Pool', 'BBQ Area', 'Sound System', 'Garden']
  },
  {
    id: 'venue-003',
    title: 'Industrial Warehouse - Surry Hills',
    description: 'Converted warehouse space in trendy Surry Hills. Perfect for creative events, product launches, and unique celebrations.',
    location: 'Surry Hills, NSW',
    capacity: 200,
    price: 300,
    rating: 4.7,
    amenities: ['High Ceilings', 'Loading Dock', 'Sound System', 'Lighting Rig', 'Kitchen']
  },
  {
    id: 'venue-004',
    title: 'Garden Pavilion - Parramatta',
    description: 'Elegant garden pavilion surrounded by lush greenery. Ideal for weddings, garden parties, and outdoor events.',
    location: 'Parramatta, NSW',
    capacity: 120,
    price: 200,
    rating: 4.6,
    amenities: ['Garden Setting', 'Pavilion', 'Catering Kitchen', 'Parking', 'Bridal Suite']
  },
  {
    id: 'venue-005',
    title: 'Harbourside Function Centre - Manly',
    description: 'Premium harbourside venue with stunning water views. Perfect for corporate events and special celebrations.',
    location: 'Manly, NSW',
    capacity: 180,
    price: 280,
    rating: 4.8,
    amenities: ['Harbour Views', 'Dance Floor', 'Bar', 'Catering Kitchen', 'Valet Parking']
  }
];

// Generate venue HTML template
function generateVenueHTML(venue) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${venue.title} | HouseGoing</title>
    <meta name="description" content="${venue.description} Book this ${venue.location} venue for up to ${venue.capacity} guests. Starting from $${venue.price}/hour.">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="${baseUrl}/venue/${venue.id}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="${venue.title} | HouseGoing">
    <meta property="og:description" content="${venue.description}">
    <meta property="og:url" content="${baseUrl}/venue/${venue.id}">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="HouseGoing">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="${venue.title}">
    <meta name="twitter:description" content="${venue.description}">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Place",
        "name": "${venue.title}",
        "description": "${venue.description}",
        "url": "${baseUrl}/venue/${venue.id}",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "${venue.location}",
            "addressRegion": "NSW",
            "addressCountry": "AU"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "${venue.rating}",
            "bestRating": "5",
            "worstRating": "1"
        },
        "offers": {
            "@type": "Offer",
            "price": "${venue.price}",
            "priceCurrency": "AUD",
            "availability": "https://schema.org/InStock"
        }
    }
    </script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Redirect to SPA for booking functionality -->
    <script>
        if (!navigator.userAgent.includes('bot') && !navigator.userAgent.includes('crawler')) {
            setTimeout(() => {
                window.location.href = '/venue/${venue.id}';
            }, 3000);
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-purple-600">HouseGoing</a>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="/find-venues" class="text-gray-700 hover:text-purple-600">Find Venues</a>
                    <a href="/list-space" class="text-gray-700 hover:text-purple-600">List Space</a>
                    <a href="/how-it-works" class="text-gray-700 hover:text-purple-600">How It Works</a>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="pt-20 px-4 sm:px-6 max-w-6xl mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li><a href="/" class="hover:text-purple-600">Home</a></li>
                <li>/</li>
                <li><a href="/find-venues" class="hover:text-purple-600">Find Venues</a></li>
                <li>/</li>
                <li class="text-gray-900">${venue.title}</li>
            </ol>
        </nav>
        
        <!-- Venue Header -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div class="h-64 bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                <div class="text-center text-white">
                    <h1 class="text-4xl font-bold mb-2">${venue.title}</h1>
                    <p class="text-xl opacity-90">${venue.location}</p>
                </div>
            </div>
            
            <div class="p-6">
                <div class="grid md:grid-cols-3 gap-6">
                    <!-- Venue Details -->
                    <div class="md:col-span-2">
                        <h2 class="text-2xl font-semibold mb-4">About This Venue</h2>
                        <p class="text-gray-600 mb-6">${venue.description}</p>
                        
                        <h3 class="text-xl font-semibold mb-3">Amenities</h3>
                        <div class="grid grid-cols-2 gap-2 mb-6">
                            ${venue.amenities.map(amenity => `
                                <div class="flex items-center">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                    <span class="text-gray-700">${amenity}</span>
                                </div>
                            `).join('')}
                        </div>
                        
                        <h3 class="text-xl font-semibold mb-3">Location</h3>
                        <p class="text-gray-600">${venue.location}</p>
                    </div>
                    
                    <!-- Booking Info -->
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="text-center mb-4">
                            <div class="text-3xl font-bold text-purple-600">$${venue.price}</div>
                            <div class="text-gray-500">per hour</div>
                        </div>
                        
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Capacity:</span>
                                <span class="font-semibold">${venue.capacity} guests</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Rating:</span>
                                <span class="font-semibold">${venue.rating}/5 ⭐</span>
                            </div>
                        </div>
                        
                        <a href="/venue/${venue.id}" class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors text-center block">
                            Book This Venue
                        </a>
                        
                        <p class="text-xs text-gray-500 mt-3 text-center">
                            Click to access full booking system
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Venues -->
        <section class="mb-12">
            <h2 class="text-2xl font-semibold mb-6">More Venues in ${venue.location.split(',')[0]}</h2>
            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h3 class="font-semibold mb-2">Similar Venues</h3>
                    <p class="text-gray-600 text-sm">Discover more amazing venues in your area</p>
                    <a href="/find-venues" class="text-purple-600 hover:text-purple-700 text-sm">Browse All →</a>
                </div>
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h3 class="font-semibold mb-2">Party Planning</h3>
                    <p class="text-gray-600 text-sm">Get help planning your perfect event</p>
                    <a href="/nsw-party-planning" class="text-purple-600 hover:text-purple-700 text-sm">Learn More →</a>
                </div>
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h3 class="font-semibold mb-2">Need Help?</h3>
                    <p class="text-gray-600 text-sm">Contact our team for assistance</p>
                    <a href="/contact" class="text-purple-600 hover:text-purple-700 text-sm">Get Support →</a>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center">
                <h3 class="text-lg font-semibold mb-4">HouseGoing</h3>
                <p class="text-gray-400 mb-4">Find your perfect party venue in NSW, Australia.</p>
                <div class="flex justify-center space-x-6">
                    <a href="/find-venues" class="text-gray-400 hover:text-white">Find Venues</a>
                    <a href="/list-space" class="text-gray-400 hover:text-white">List Space</a>
                    <a href="/contact" class="text-gray-400 hover:text-white">Contact</a>
                </div>
                <div class="border-t border-gray-800 mt-8 pt-8">
                    <p class="text-gray-400">&copy; 2025 HouseGoing Pty Ltd. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>`;
}

// Generate static venue pages
function generateVenuePages() {
    console.log('Generating static venue pages for SEO...');
    
    // Create venue directory
    const venueDir = path.join(publicDir, 'venue');
    if (!fs.existsSync(venueDir)) {
        fs.mkdirSync(venueDir, { recursive: true });
    }
    
    let generatedCount = 0;
    
    for (const venue of mockVenues) {
        try {
            const html = generateVenueHTML(venue);
            const filePath = path.join(venueDir, `${venue.id}.html`);
            
            fs.writeFileSync(filePath, html, 'utf8');
            console.log(`✓ Generated: venue/${venue.id}.html`);
            generatedCount++;
        } catch (error) {
            console.error(`✗ Failed to generate venue ${venue.id}:`, error.message);
        }
    }
    
    console.log(`\n🎉 Successfully generated ${generatedCount} venue pages!`);
    console.log('\nThese venue pages are now crawlable and should fix venue-related Soft 404 issues.');
}

// Execute the function
generateVenuePages();
