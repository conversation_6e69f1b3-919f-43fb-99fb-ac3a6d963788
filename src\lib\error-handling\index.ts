import { logger, LogLevel } from '../logging';

// Error types
export enum ErrorType {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  CONFLICT = 'conflict',
  EXTERNAL_SERVICE = 'external_service',
  DATABASE = 'database',
  NETWORK = 'network',
  UNKNOWN = 'unknown'
}

// Application error class
export class AppError extends Error {
  type: ErrorType;
  statusCode: number;
  context?: Record<string, any>;
  
  constructor(message: string, type: ErrorType = ErrorType.UNKNOWN, statusCode = 500, context?: Record<string, any>) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.context = context;
  }
}

// Error handler for API routes
export function errorHandler(error: any, req: any, res: any, next: any) {
  // Default error
  let appError: AppError;
  
  // Convert to AppError if needed
  if (error instanceof AppError) {
    appError = error;
  } else {
    appError = new AppError(
      error.message || 'An unexpected error occurred',
      ErrorType.UNKNOWN,
      500,
      { originalError: error }
    );
  }
  
  // Log error
  logger.error(`API Error: ${appError.message}`, {
    type: appError.type,
    statusCode: appError.statusCode,
    stack: appError.stack,
    context: appError.context,
    url: req.url,
    method: req.method,
    params: req.params,
    query: req.query,
    body: req.body
  });
  
  // Send response
  res.status(appError.statusCode).json({
    error: {
      message: appError.message,
      type: appError.type,
      ...(process.env.NODE_ENV === 'development' ? { stack: appError.stack } : {})
    }
  });
}

// Global error handler for client-side errors
export function setupGlobalErrorHandling() {
  if (typeof window !== 'undefined') {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      logger.error('Unhandled Promise Rejection', {
        reason: event.reason,
        stack: event.reason?.stack
      });
    });
    
    // Handle uncaught exceptions
    window.addEventListener('error', (event) => {
      logger.error('Uncaught Exception', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });
  }
}

// Create specific error types
export function createValidationError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.VALIDATION, 400, context);
}

export function createAuthenticationError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.AUTHENTICATION, 401, context);
}

export function createAuthorizationError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.AUTHORIZATION, 403, context);
}

export function createNotFoundError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.NOT_FOUND, 404, context);
}

export function createConflictError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.CONFLICT, 409, context);
}

export function createExternalServiceError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.EXTERNAL_SERVICE, 502, context);
}

export function createDatabaseError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.DATABASE, 500, context);
}

export function createNetworkError(message: string, context?: Record<string, any>): AppError {
  return new AppError(message, ErrorType.NETWORK, 500, context);
}
