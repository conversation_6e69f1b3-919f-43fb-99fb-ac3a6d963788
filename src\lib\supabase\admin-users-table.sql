-- Admin users table for HouseGoing

-- Create admin_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  role VARCHAR(50) NOT NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);

-- Insert default admin users
INSERT INTO admin_users (email, role, name)
VALUES 
  ('<EMAIL>', 'super_admin', 'Tom'),
  ('<EMAIL>', 'admin', 'Admin User'),
  ('<EMAIL>', 'support', 'Support User')
ON CONFLICT (email) DO NOTHING;

-- <PERSON><PERSON> function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_admin_last_active()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_active = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update last_active
DROP TRIGGER IF EXISTS update_admin_last_active_trigger ON admin_users;
CREATE TRIGGER update_admin_last_active_trigger
BEFORE UPDATE ON admin_users
FOR EACH ROW
EXECUTE FUNCTION update_admin_last_active();
