import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth, useUser, useClerk } from '@clerk/clerk-react';

export default function ClerkCallback() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isLoaded, userId, getToken } = useAuth();
  const { user } = useUser();
  const clerk = useClerk();
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(true);
  const [redirectTimer, setRedirectTimer] = useState(3);

  // Immediately set auth flags and redirect to home page
  useEffect(() => {
    // Log Clerk state
    console.log("Clerk state in ClerkCallback:", {
      isLoaded,
      userId,
      user: user ? {
        id: user.id,
        email: user.primaryEmailAddress?.emailAddress,
        firstName: user.firstName,
        lastName: user.lastName
      } : null,
      clerk: clerk ? {
        loaded: clerk.loaded,
        user: clerk.user ? clerk.user.id : null,
        session: clerk.session ? "available" : "not available"
      } : null
    });

    // Try to get token directly
    const getClerkToken = async () => {
      try {
        if (clerk.session) {
          const token = await clerk.session.getToken({ template: 'supabase' });
          console.log("Direct Clerk token in callback:", token ? token.substring(0, 20) + '...' : 'null');
        } else {
          console.log("No Clerk session available in callback");
        }
      } catch (error) {
        console.error("Error getting Clerk token in callback:", error);
      }
    };

    if (clerk.loaded) {
      getClerkToken();
    }

    // Parse query parameters
    const searchParams = new URLSearchParams(location.search);
    const destination = searchParams.get('destination');
    const redirectTo = localStorage.getItem('auth_redirect_to') || '/';
    const userType = localStorage.getItem('auth_user_type') || 'guest';
    const isHost = userType === 'host' || destination === 'host-portal';

    // Check if this is an auto-create account flow
    const isAutoCreate = localStorage.getItem('auth_auto_create') === 'true';
    console.log('Auto-create account flow:', isAutoCreate);

    // Set auth flags
    localStorage.setItem('auth_success', 'true');
    localStorage.setItem('auth_success_time', new Date().toISOString());
    localStorage.setItem('auth_user_type', isHost ? 'host' : 'guest');
    localStorage.setItem('auth_processing', 'true'); // Indicate that we're in the authentication process

    // Set additional flags for header display
    if (user) {
      localStorage.setItem('user_authenticated', 'true');
      localStorage.setItem('user_display_name', user.firstName || user.username || 'User');
    }

    // Clear the auto-create flag if it exists
    if (isAutoCreate) {
      localStorage.removeItem('auth_auto_create');
      console.log('Auto-create account flow completed');
    }

    // Store user info if available
    if (user) {
      localStorage.setItem('clerk_user_id', userId);
      localStorage.setItem('clerk_user_email', user.primaryEmailAddress?.emailAddress || '');
      localStorage.setItem('first_name', user.firstName || '');
      localStorage.setItem('last_name', user.lastName || '');
      localStorage.setItem('user_role', isHost ? 'host' : 'guest');

      // Store full name for display
      const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      localStorage.setItem('user_full_name', fullName || user.username || 'User');

      // Store profile image if available
      if (user.imageUrl) {
        localStorage.setItem('user_profile_image', user.imageUrl);
      }
    }

    // Determine where to redirect
    let finalRedirectUrl = redirectTo;
    if (isHost) {
      finalRedirectUrl = '/host/dashboard';
    } else if (destination === 'checkout') {
      finalRedirectUrl = '/checkout';
    } else if (redirectTo && redirectTo !== '/') {
      finalRedirectUrl = redirectTo;
    } else {
      finalRedirectUrl = '/';
    }

    // Create a user profile in Supabase
    const createUserProfile = async () => {
      try {
        // Get the Clerk token for Supabase
        const token = await getToken({ template: 'supabase' });
        console.log("Got Clerk token for Supabase:", token ? "token received" : "no token");

        if (token) {
          // Import the supabase client
          const { supabase } = await import('../../lib/supabase-client');

          // Set the token on the Supabase client
          if (typeof supabase.auth.setAuth === 'function') {
            (supabase.auth as any).setAuth(token);
          } else if (typeof supabase.auth.setSession === 'function') {
            await supabase.auth.setSession({
              access_token: token,
              refresh_token: ''
            });
          }

          // Create a user profile
          const email = user?.primaryEmailAddress?.emailAddress || '';
          const firstName = user?.firstName || '';
          const lastName = user?.lastName || '';

          // Check if a profile already exists
          const { data: existingProfile, error: checkError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('clerk_id', userId)
            .maybeSingle();

          console.log("Checked for existing profile:", existingProfile ? "found" : "not found", checkError ? `error: ${checkError.message}` : "no error");

          if (!existingProfile) {
            // Create a new profile
            const newProfile = {
              clerk_id: userId,
              email: email,
              role: isHost ? 'host' : 'guest',
              first_name: firstName,
              last_name: lastName,
              is_host: isHost,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            const { data, error } = await supabase
              .from('user_profiles')
              .insert(newProfile)
              .select();

            console.log("Created user profile:", data ? "success" : "failed", error ? `error: ${error.message}` : "no error");
          } else {
            console.log("User profile already exists, no need to create");
          }
        }
      } catch (error) {
        console.error("Error creating user profile:", error);
      }
    };

    // Call the async function if user is available
    if (user && userId) {
      createUserProfile();
    }

    // Dispatch auth_complete event
    const authCompleteEvent = new CustomEvent('auth_complete', {
      detail: {
        success: true,
        provider: 'clerk',
        user: {
          id: userId || `temp-${Date.now()}`,
          clerk_id: userId,
          email: user?.primaryEmailAddress?.emailAddress || '',
          name: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
          role: isHost ? 'host' : 'guest',
          first_name: user?.firstName || '',
          last_name: user?.lastName || ''
        }
      }
    });
    window.dispatchEvent(authCompleteEvent);

    // Set up countdown timer
    const timer = setInterval(() => {
      setRedirectTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Clear the auth_processing flag
          localStorage.removeItem('auth_processing');

          // Clear the auto-create flag if it exists
          localStorage.removeItem('auth_auto_create');

          // Redirect to home page
          navigate(finalRedirectUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Clean up timer
    return () => clearInterval(timer);
  }, [navigate, location.search, userId, user, getToken, clerk]);

  // Handle any errors
  useEffect(() => {
    const handleError = () => {
      if (!isLoaded && redirectTimer === 0) {
        setError('Authentication service is not responding. Please try again later.');
      }
    };

    handleError();
  }, [isLoaded, redirectTimer]);

  return (
    <div className="min-h-screen flex flex-col justify-center items-center p-4 bg-gray-50">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <img
            src="/images/housegoing-logo.svg"
            alt="HouseGoing"
            className="h-12 mx-auto mb-6"
          />

          {error ? (
            <>
              <h2 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <button
                onClick={() => navigate('/login')}
                className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
              >
                Return to Login
              </button>
            </>
          ) : (
            <>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Authentication Complete!
              </h2>
              <p className="text-gray-600 mb-6">
                You have been successfully authenticated. Redirecting you in {redirectTimer} seconds...
              </p>

              <div className="flex flex-col items-center space-y-4">
                <div className="flex justify-center">
                  <svg className="animate-spin h-8 w-8 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>

                <div className="mt-4">
                  <button
                    onClick={() => {
                      // Clear the auth_processing flag
                      localStorage.removeItem('auth_processing');

                      // Clear the auto-create flag if it exists
                      localStorage.removeItem('auth_auto_create');
                      navigate('/');
                    }}
                    className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                  >
                    Go to Home Page Now
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
