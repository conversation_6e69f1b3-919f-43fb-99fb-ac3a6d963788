import React, { useEffect, useState } from 'react';
import { AIAssistantButton } from '../../components/host/AIAssistantButton.jsx';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../providers/AuthProvider';
import { isPreregisteredHost } from '../../data/preregisteredHosts';
import {
  Plus,
  TrendingUp,
  Users,
  Star,
  Calendar,
  MessageSquare,
  ArrowRight
} from 'lucide-react';

// Mock data for the dashboard
const mockStats = {
  totalEarnings: '$4,250',
  bookingsThisMonth: 8,
  averageRating: 4.8,
  responseRate: '95%',
  pendingRequests: 3,
  upcomingBookings: 5,
  totalProperties: 2
};

// Mock data for recent bookings
const recentBookings = [
  { id: 1, guest: '<PERSON>', property: 'Beachside Villa', date: '2023-08-15', status: 'confirmed', amount: '$350' },
  { id: 2, guest: '<PERSON>', property: 'Mountain Cabin', date: '2023-08-18', status: 'pending', amount: '$275' },
  { id: 3, guest: '<PERSON>', property: 'Downtown Loft', date: '2023-08-20', status: 'confirmed', amount: '$420' },
];

// Mock data for recent messages
const recentMessages = [
  { id: 1, from: 'John Smith', preview: 'Hi, I have a question about...', time: '2 hours ago', unread: true },
  { id: 2, from: 'Sarah <PERSON>', preview: 'Thanks for accepting my booking!', time: '1 day ago', unread: false },
];

export default function HostDashboard() {
  const location = useLocation();
  const navigate = useNavigate();
  const [showRegistrationSuccess, setShowRegistrationSuccess] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState('');
  const [isPreregistered, setIsPreregistered] = useState(false);
  const [showMockData, setShowMockData] = useState(false);

  // Check if we're in development mode
  const isDevelopmentMode = typeof window !== 'undefined' &&
                           (window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('local'));

  // Development mode: use mock data, Production mode: use real auth
  let user;

  if (isDevelopmentMode) {
    // Mock host user data for development
    user = {
      id: 'dev-host-123',
      firstName: 'John',
      lastName: 'Host',
      fullName: 'John Host',
      primaryEmailAddress: { emailAddress: '<EMAIL>' },
      imageUrl: 'https://via.placeholder.com/150',
      createdAt: new Date().toISOString(),
      publicMetadata: {
        role: 'host'
      }
    };
  } else {
    // Production mode: use real auth
    const authData = useAuth();
    user = authData.user;
  }

  // Check if user was redirected from direct email registration
  useEffect(() => {
    if (isDevelopmentMode) {
      // In development mode, always show as registered host with mock data
      console.log('Dashboard: Development mode - showing mock host data');
      setShowMockData(true);
      setIsPreregistered(true);
      setRegisteredEmail('<EMAIL>');
      setShowRegistrationSuccess(true);
      return;
    }

    // Production mode logic
    const params = new URLSearchParams(location.search);
    const registered = params.get('registered');
    const registration = params.get('registration');
    const email = params.get('email');

    // Handle registration success message
    if (registered === 'true' || registration === 'success') {
      console.log('Dashboard: Registration success detected');
      setShowRegistrationSuccess(true);

      // If email is provided in URL, use it
      if (email) {
        setRegisteredEmail(email);
      }
      // Otherwise use the current user's email
      else if (user?.primaryEmailAddress?.emailAddress) {
        setRegisteredEmail(user.primaryEmailAddress.emailAddress);
      }

      // Remove query parameters from URL
      navigate('/host/dashboard', { replace: true });

      // Clear the registering_as_host flag from localStorage
      try {
        localStorage.removeItem('registering_as_host');
      } catch (err) {
        console.error('Failed to clear localStorage flag:', err);
      }
    }

    // Check if current user is a pre-registered host
    const userEmail = user?.primaryEmailAddress?.emailAddress || '';
    if (user) {
      // Check if user is a host from multiple sources
      const isHostFromLocalStorage = localStorage.getItem('registering_as_host') === 'true';
      const isHostFromRegisteredHosts = isPreregisteredHost(userEmail);

      if (isHostFromRegisteredHosts || isHostFromLocalStorage) {
        console.log('Dashboard: Host detected:', userEmail);
        console.log('- From preregisteredHosts:', isHostFromRegisteredHosts);
        console.log('- From localStorage:', isHostFromLocalStorage);

        setIsPreregistered(true);
        setRegisteredEmail(userEmail);

        // Only show mock <NAME_EMAIL>
        if (userEmail.toLowerCase() === '<EMAIL>') {
          setShowMockData(true);
        }
      }
    }
  }, [location, navigate, user, isDevelopmentMode]);

  const firstName = user?.firstName || 'Property Owner';

  return (
    <>
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        {(showRegistrationSuccess || isPreregistered) && (
          <div className="mb-4 p-4 bg-green-50 text-green-700 rounded-md">
            <p className="font-medium">Success! {registeredEmail} has been registered as a host.</p>
            <p className="mt-1 text-sm">You can now start listing your properties and managing bookings.</p>
            {isPreregistered && registeredEmail === '<EMAIL>' && (
              <p className="mt-1 font-medium">Welcome back, Tom! Your pre-registered host account is active.</p>
            )}
          </div>
        )}
        <h1 className="text-2xl font-bold text-gray-900">Welcome back, {firstName}!</h1>
        <p className="mt-1 text-gray-600">Here's what's happening with your properties today.</p>
      </div>

      {/* Quick actions */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-4">
          <Link
            to="/host/properties/new"
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            <Plus className="mr-2 h-5 w-5" />
            Add New Property
          </Link>
          <Link
            to="/host/bookings"
            className="inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            <Calendar className="mr-2 h-5 w-5 text-gray-500" />
            Manage Bookings
          </Link>
          <Link
            to="/host/messages"
            className="inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="mr-2 h-5 w-5 text-gray-500" />
            View Messages
            {mockStats.pendingRequests > 0 && (
              <span className="ml-2 px-2 py-0.5 bg-red-100 text-red-600 rounded-full text-xs font-medium">
                {mockStats.pendingRequests}
              </span>
            )}
          </Link>
        </div>
      </div>

      {/* Stats overview */}
      {showMockData ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <TrendingUp className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <h3 className="text-xl font-bold text-gray-900">{mockStats.totalEarnings}</h3>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <Calendar className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Bookings This Month</p>
              <h3 className="text-xl font-bold text-gray-900">{mockStats.bookingsThisMonth}</h3>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <Star className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Rating</p>
              <h3 className="text-xl font-bold text-gray-900">{mockStats.averageRating}/5</h3>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <Users className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Response Rate</p>
              <h3 className="text-xl font-bold text-gray-900">{mockStats.responseRate}</h3>
            </div>
          </div>
        </div>
      </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <TrendingUp className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Earnings</p>
                <h3 className="text-xl font-bold text-gray-900">$0</h3>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Calendar className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Bookings This Month</p>
                <h3 className="text-xl font-bold text-gray-900">0</h3>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <Star className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Rating</p>
                <h3 className="text-xl font-bold text-gray-900">-</h3>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <Users className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Response Rate</p>
                <h3 className="text-xl font-bold text-gray-900">-</h3>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent bookings */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">Recent Bookings</h2>
              <Link to="/host/bookings" className="text-sm text-purple-600 hover:text-purple-800 flex items-center">
                View all
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {showMockData ? (
              recentBookings.map((booking) => (
                <div key={booking.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{booking.guest}</p>
                      <p className="text-sm text-gray-500">{booking.property}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{booking.amount}</p>
                      <p className="text-sm">
                        <span
                          className={`inline-flex px-2 py-0.5 rounded-full text-xs font-medium ${
                            booking.status === 'confirmed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {booking.status}
                        </span>
                      </p>
                    </div>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">Check-in: {new Date(booking.date).toLocaleDateString()}</p>
                </div>
              ))
            ) : (
              <div className="px-6 py-8 text-center">
                <p className="text-gray-500">No bookings yet</p>
                <p className="text-sm text-gray-400 mt-1">Your bookings will appear here once you list a property and receive reservations</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent messages */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">Recent Messages</h2>
              <Link to="/host/messages" className="text-sm text-purple-600 hover:text-purple-800 flex items-center">
                View all
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {showMockData ? (
              recentMessages.map((message) => (
                <div key={message.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 flex items-center">
                        {message.from}
                        {message.unread && (
                          <span className="ml-2 w-2 h-2 bg-purple-600 rounded-full"></span>
                        )}
                      </p>
                      <p className="text-sm text-gray-500 truncate">{message.preview}</p>
                    </div>
                    <p className="text-xs text-gray-500">{message.time}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-8 text-center">
                <p className="text-gray-500">No messages yet</p>
                <p className="text-sm text-gray-400 mt-1">Messages from guests will appear here</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
      <AIAssistantButton context="dashboard" position="bottom-right" />
    </>
  );
}