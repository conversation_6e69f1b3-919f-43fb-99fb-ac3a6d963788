import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const apiKey = process.env.LANGSMITH_API_KEY;

console.log('Using API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');

async function testLangSmithAuth() {
  try {
    // Test API connection by fetching user info
    const response = await fetch('https://api.smith.langchain.com/api/me', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('\nUser info:', data);
    
  } catch (error) {
    console.error('Error testing LangSmith API:', error);
  }
}

testLangSmithAuth();
