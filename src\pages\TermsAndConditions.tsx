import React, { useState } from 'react';
import SEO from '../components/seo/SEO';
import { ChevronDown, ChevronUp, Info, Users, Home, Shield, CreditCard, AlertTriangle, FileText, Scale } from 'lucide-react';

interface TermsSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  summary: string;
  content: string;
  examples?: string[];
}

// Utility function to convert **text** to <strong>text</strong>
const formatBoldText = (text: string): string => {
  return text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
};

const termsData: TermsSection[] = [
  {
    id: 'introduction',
    title: '1. Introduction',
    icon: <Info className="h-5 w-5" />,
    summary: 'HouseGoing connects party hosts with venue owners across NSW, Australia.',
    content: `HouseGoing Pty Ltd (ABN 49 ***********) operates an online platform that connects property owners ("Hosts") with individuals seeking venues for private gatherings ("Guests"). These Terms of Service govern your access to and use of the HouseGoing website, mobile applications, and services.

These Terms constitute a legally binding agreement between you and HouseGoing. By accessing or using the Platform, you acknowledge that you have read, understood, and agree to be bound by these Terms.

These Terms have been prepared in accordance with Australian law, specifically the laws of New South Wales (NSW) and comply with the Australian Consumer Law (ACL) contained in Schedule 2 of the Competition and Consumer Act 2010 (Cth).`,
    examples: [
      'Creating an account automatically means you accept these terms',
      'ABN 49 *********** - Registered Australian business',
      'Complies with NSW law and Australian Consumer Law'
    ]
  },
  {
    id: 'guest-eligibility',
    title: '2. Guest Eligibility and Account',
    icon: <Users className="h-5 w-5" />,
    summary: 'Requirements for booking venues and account responsibilities.',
    content: `**Eligibility Requirements:**
To book a venue as a Guest, you must:
• Be at least 18 years of age
• Possess legal authority to enter binding agreements
• Provide accurate, current, and complete information
• Maintain the accuracy of such information
• Complete identity verification when required

**Identity Verification:**
Verification may include government-issued photo ID, proof of address, and facial recognition technology. This is conducted in compliance with the Privacy Act 1988 (Cth) and Identity-matching Services Bill 2019 (Cth).

**Account Security:**
You are responsible for maintaining the confidentiality of your account credentials and must immediately notify HouseGoing of any unauthorized use.`,
    examples: [
      'Must be 18+ to create an account',
      'Provide real name and contact information',
      'Upload government ID for verification'
    ]
  },
  {
    id: 'booking-venues',
    title: '3. Booking and Using Venues',
    icon: <Home className="h-5 w-5" />,
    summary: 'Rules for private gatherings, booking process, and guest conduct.',
    content: `**Private Gatherings Only:**
Venues may only be used for private gatherings with:
• Pre-planned events with defined guest lists
• All attendees personally known to the organizer
• No public advertising or ticket sales
• Compliance with venue occupancy limits and NSW regulations

**Booking Process:**
Bookings require Host acceptance, payment of all fees, and HouseGoing confirmation. All requests must include date/time, attendee numbers, event nature, and special requirements.

**Guest Conduct Requirements:**
• Respect maximum occupancy limits
• Adhere to all House Rules set by the Host
• Comply with NSW EPA Noise Control Regulation 2017 (quiet hours 10 PM-8 AM)
• Leave venue in same condition as arrival
• Report any incidents immediately

**Prohibited Activities:**
• Exceeding maximum occupancy
• Public events or ticket sales
• Illegal activities or safety risks
• Excessive noise during quiet hours (10 PM-8 AM)
• Allowing minors to consume alcohol`,
    examples: [
      'Birthday parties, weddings, family gatherings are allowed',
      'No public events or ticket sales permitted',
      'Quiet hours: 10 PM to 8 AM on weekdays'
    ]
  },
  {
    id: 'fees-payments',
    title: '4. Fees and Payments',
    icon: <CreditCard className="h-5 w-5" />,
    summary: 'Booking fees, payment processing, and refund policies.',
    content: `**Fees Include:**
• Venue fee (set by Host)
• Service fee (set by HouseGoing)
• GST and applicable taxes
• Security deposit (where applicable)

**$1 Party Booking Offer (Loyalty Unlocked: Real Rewards, Real Savings):**
• First booking: $1 booking fee (normally 5% of venue cost)
• Every 5th completed booking: $1 booking fee (normally 5% of venue cost)
• Venue cost always paid in full by customer
• System automatically tracks booking count and eligibility
• Cancelled bookings do not count toward discount progression
• Offer applies to booking fee only, not venue cost or other charges
• Abuse-proof system prevents manipulation of booking counts

**Payment Processing:**
All payments processed through secure payment system. Guest payments held in designated trust account compliant with Payment Systems (Regulation) Act 1998 (Cth) until 24 hours post-event.

**Security Deposit:**
• 20% of booking fee held as pre-authorization
• Released within 48 hours post-event unless valid claims submitted
• May be applied toward damage, excessive cleaning, unauthorized extended stay, or rule violations
• Guest remains liable for full cost of damages even if exceeding deposit amount

**Cancellations and Refunds:**
• 48-hour cooling-off period under Australian Consumer Law
• Free cancellation up to 48 hours before event (except bookings within 7 days)
• Refunds processed according to Host's cancellation policy
• HouseGoing may cancel bookings that violate Terms`,
    examples: [
      'Book $200 venue: Pay venue fee + service fee + GST',
      'Cancel 3 days early: Full refund available',
      '$40 security deposit held for $200 booking (not damage cap)'
    ]
  },
  {
    id: 'guest-insurance',
    title: '5. Guest Liability and Insurance',
    icon: <Shield className="h-5 w-5" />,
    summary: 'Guest responsibilities and liability for damages (no platform coverage).',
    content: `**No Platform Insurance Coverage:**
HouseGoing does NOT provide any insurance coverage or damage protection. We are a booking platform only that facilitates connections between hosts and guests.

**Guest Liability:**
You are fully responsible for:
• Your safety and the safety of your attendees
• Security of personal belongings
• ANY and ALL damage caused to venue or contents by you or attendees
• Compliance with all applicable laws and regulations
• Full cost of repairs/replacement (security deposit is not a liability cap)
• Obtaining your own event insurance if desired

**Direct Host-Guest Relationship:**
Any disputes, damages, or issues are between you and the host directly. HouseGoing will assist with communication but is not liable for resolution.

**Indemnification:**
You agree to indemnify and hold harmless HouseGoing from ALL claims arising from your use of the Platform, violation of Terms, or conduct in connection with venues.`,
    examples: [
      'NO damage coverage provided by platform',
      'You are fully liable for ALL damages',
      'Get your own insurance if you want coverage',
      'Disputes are between you and host directly'
    ]
  },
  {
    id: 'host-eligibility',
    title: '6. Host Eligibility and Requirements',
    icon: <Home className="h-5 w-5" />,
    summary: 'Requirements for venue owners to list properties on HouseGoing.',
    content: `**Eligibility Requirements:**
To list a venue as a Host, you must:
• Be at least 18 years of age
• Have legal right to list and offer the venue for use
• Provide accurate, current, and complete information
• Complete identity and property verification

**Venue Listing Requirements:**
All venue listings must:
• Provide accurate and complete venue information
• Specify maximum occupancy per Building Code of Australia
• Include comprehensive House Rules
• Display accurate and current photographs
• Set clear cancellation policies

**Safety Requirements:**
Your venue must comply with:
• Fire Safety Regulations (Environmental Planning and Assessment Regulation 2000 NSW)
• Building Code of Australia requirements
• Electrical safety standards
• Work Health and Safety Act 2011 (NSW)

**Required Safety Equipment:**
• Functional smoke detectors on each level
• Fire extinguishers (1 per 200m²) regularly serviced
• Clearly marked emergency exits
• First aid kit (WorkCover NSW compliant)
• Emergency evacuation plan`,
    examples: [
      'Must own property or have written authorization',
      'Smoke detectors required on every level',
      'Maximum 50 people without special permits'
    ]
  },
  {
    id: 'host-insurance',
    title: '7. Host Insurance and Compliance',
    icon: <Shield className="h-5 w-5" />,
    summary: 'Insurance requirements and compliance documentation for hosts.',
    content: `**Insurance Requirements:**
You must maintain:
• Public liability insurance (tiered requirements):
  - Basic venues: Minimum $5 million (small private parties)
  - Standard venues: $10 million recommended (most bookings)
  - Premium venues: $20 million required (large events, food service)
• Insurance from APRA-regulated insurer
• Policy covering short-term venue hire for events
• Liquor liability coverage (if offering alcohol service)

**Compliance Documentation:**
You must upload and maintain:
• Public liability insurance certificate
• Fire Safety Certificate (Annual Fire Safety Statement)
• Council approval for intended use (DA or CDC)
• Strata approval (if applicable)
• Any licenses or permits for specialized activities

**Service Fees:**
• Host Service Fee: 10% (deducted from your payout)
• Guest Booking Fee: 5% (charged to guests)
• Payment processed within 24 hours after event conclusion
• You are responsible for reporting and remitting all applicable taxes

**Host Conduct Requirements:**
• Maintain venue in clean, safe, and habitable condition
• Ensure all amenities are functional as described
• Respond promptly to Guest inquiries
• Respect Guests' privacy during venue use
• No discrimination based on protected characteristics`,
    examples: [
      'Tiered insurance: $5M basic, $10M standard, $20M premium venues',
      'Annual fire safety certificate must be current',
      '10% Host Service Fee + 5% Guest Booking Fee'
    ]
  },
  {
    id: 'platform-rules',
    title: '8. Platform Rules and Prohibited Uses',
    icon: <AlertTriangle className="h-5 w-5" />,
    summary: 'What you can and cannot do on HouseGoing platform.',
    content: `**Prohibited Activities for All Users:**
• Using venues for illegal activities
• Discriminating against users based on protected characteristics
• Posting false, misleading, or fraudulent content
• Circumventing payment system
• Harassing, threatening, or abusing other users
• Violating local, state, or federal laws

**Additional Guest Prohibitions:**
• Exceeding maximum occupancy limits
• Hosting public events or selling tickets
• Publicly promoting event or venue location
• Removing or tampering with safety equipment
• Bringing illegal drugs or prohibited substances
• Allowing minors to consume alcohol

**Additional Host Prohibitions:**
• Misrepresenting venue or authority to list
• Listing non-compliant venues
• Requesting payment outside Platform
• Entering venue during booking without permission
• Monitoring Guests without proper disclosure

**Account Suspension:**
We reserve the right to suspend or terminate accounts that violate these terms, engage in fraudulent activity, or pose risk to our community.`,
    examples: [
      'Cannot list venues for underage drinking parties',
      'Cannot refuse bookings based on race, religion, etc.',
      'Cannot ask guests to pay outside the platform'
    ]
  },
  {
    id: 'dispute-resolution',
    title: '9. Dispute Resolution and Governing Law',
    icon: <Scale className="h-5 w-5" />,
    summary: 'How disputes are resolved and which laws apply.',
    content: `**Informal Resolution Process:**
Before formal dispute resolution:
• Contact other party directly to discuss issue
• Use Platform's messaging system to document communications
• Allow reasonable time (at least 7 days) for resolution
• HouseGoing may mediate disputes between Hosts and Guests

**Formal Dispute Resolution:**
If informal resolution fails:
1. Written Notice of Dispute detailing nature and relief sought
2. 30-day negotiation period in good faith
3. Mediation through NSW Civil and Administrative Tribunal (NCAT)
4. Legal proceedings if mediation fails

**Governing Law and Jurisdiction:**
• Terms governed by laws of New South Wales, Australia
• Legal proceedings must be instituted in NSW courts
• Each party submits to jurisdiction of NSW courts

**Limitation Period:**
Any claim must be filed within one (1) year after the claim arose, or be forever barred.

**Australian Consumer Law:**
Nothing in these Terms excludes consumer guarantees under Australian Consumer Law, including guarantees of acceptable quality and fitness for purpose.`,
    examples: [
      'Try to resolve disputes directly first',
      'NSW Civil and Administrative Tribunal for mediation',
      'Must file legal claims within 1 year'
    ]
  },
  {
    id: 'liability-limitations',
    title: '10. Limitation of Liability',
    icon: <FileText className="h-5 w-5" />,
    summary: 'Platform liability limitations and user acknowledgments.',
    content: `**Disclaimer of Warranties:**
To the maximum extent permitted by law, the Platform is provided "as is" and "as available" without warranties. HouseGoing does not warrant that the Platform will be uninterrupted, error-free, or secure.

**Limitation of Liability:**
HouseGoing's total liability for all claims shall not exceed the greater of:
• Amount paid by you to HouseGoing in the 12 months preceding the liability event
• AUD $1,000

**Excluded Liability:**
These limitations do not apply to liability that cannot be excluded under applicable law, including:
• Death or personal injury caused by negligence
• Liability arising under Australian Consumer Law
• Fraudulent misrepresentation

**Platform Role Acknowledgment:**
You acknowledge that:
• HouseGoing is a platform facilitating connections only
• We do not own, control, manage, or operate any venues
• Venue use involves inherent risks
• We do not guarantee conduct of Hosts or Guests

**Force Majeure:**
HouseGoing not liable for failure to perform due to causes beyond reasonable control, including natural disasters, pandemics, terrorism, or governmental actions.`,
    examples: [
      'Platform provided "as is" without guarantees',
      'Maximum liability: $1,000 or amount you paid',
      'Cannot exclude liability for death/injury from negligence'
    ]
  }
];

export default function TermsAndConditions() {
  const [activeTab, setActiveTab] = useState<'summary' | 'full'>('summary');
  const [openSections, setOpenSections] = useState<Set<string>>(new Set());

  const toggleSection = (sectionId: string) => {
    const newOpenSections = new Set(openSections);
    if (newOpenSections.has(sectionId)) {
      newOpenSections.delete(sectionId);
    } else {
      newOpenSections.add(sectionId);
    }
    setOpenSections(newOpenSections);
  };

  return (
    <>
      <SEO
        title="Terms and Conditions | HouseGoing"
        description="Terms and conditions for using HouseGoing's party venue rental platform in NSW, Australia. Clear, easy-to-understand legal terms."
        keywords="terms and conditions, legal terms, venue rental terms, party booking terms, NSW regulations"
        url="https://housegoing.com.au/terms"
      />

      <div className="pt-32 px-4 sm:px-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Terms and Conditions</h1>
          <p className="text-gray-600">Last Updated: February 26, 2025</p>
        </div>

        {/* Notification Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-1">We've simplified our terms</h3>
              <p className="text-blue-800 text-sm">
                We've updated our terms to be more user-friendly and transparent. Each section includes a simple summary and practical examples.
              </p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 mb-8">
          <button
            onClick={() => setActiveTab('summary')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'summary'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Quick Summary
          </button>
          <button
            onClick={() => setActiveTab('full')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'full'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Full Terms
          </button>
        </div>

        {/* Quick Summary Tab */}
        {activeTab === 'summary' && (
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Guest Responsibilities */}
              <div className="bg-gradient-to-br from-blue-50 to-sky-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center mb-4">
                  <Users className="h-6 w-6 text-blue-600 mr-3" />
                  <h3 className="text-lg font-semibold text-blue-900">Guest Responsibilities</h3>
                </div>
                <p className="text-blue-800 text-sm mb-4">
                  Party organizers must respect venues, follow house rules, and comply with NSW noise restrictions.
                </p>
                <ul className="text-blue-700 text-sm space-y-1">
                  <li>• Must be 18+ with valid ID</li>
                  <li>• Private gatherings only</li>
                  <li>• Respect noise limits (10 PM-8 AM)</li>
                  <li>• Pay for any damages</li>
                </ul>
              </div>

              {/* Host Requirements */}
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                <div className="flex items-center mb-4">
                  <Home className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-lg font-semibold text-green-900">Host Requirements</h3>
                </div>
                <p className="text-green-800 text-sm mb-4">
                  Venue owners must maintain safety standards, proper insurance, and comply with NSW regulations.
                </p>
                <ul className="text-green-700 text-sm space-y-1">
                  <li>• $20M public liability insurance</li>
                  <li>• Safety compliance (fire, electrical)</li>
                  <li>• Accurate venue descriptions</li>
                  <li>• Council permits and approvals</li>
                </ul>
              </div>

              {/* Payment & Security */}
              <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg p-6 border border-purple-200">
                <div className="flex items-center mb-4">
                  <CreditCard className="h-6 w-6 text-purple-600 mr-3" />
                  <h3 className="text-lg font-semibold text-purple-900">Payment & Security</h3>
                </div>
                <p className="text-purple-800 text-sm mb-4">
                  Secure payments with 48-hour cooling-off period. Security deposits held toward potential damage claims (not liability cap).
                </p>
                <ul className="text-purple-700 text-sm space-y-1">
                  <li>• 48-hour cancellation policy</li>
                  <li>• 20% security deposit held (not damage cap)</li>
                  <li>• NO platform damage coverage</li>
                  <li>• Guest liable for full damage costs</li>
                </ul>
              </div>

              {/* Legal Compliance */}
              <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg p-6 border border-orange-200">
                <div className="flex items-center mb-4">
                  <Scale className="h-6 w-6 text-orange-600 mr-3" />
                  <h3 className="text-lg font-semibold text-orange-900">Legal Compliance</h3>
                </div>
                <p className="text-orange-800 text-sm mb-4">
                  Full compliance with NSW and Australian law, including Consumer Law protection.
                </p>
                <ul className="text-orange-700 text-sm space-y-1">
                  <li>• Australian Consumer Law compliant</li>
                  <li>• NSW noise regulations</li>
                  <li>• Privacy Act 1988 compliance</li>
                  <li>• Dispute resolution process</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Full Terms Tab */}
        {activeTab === 'full' && (
          <div className="space-y-4">
            {termsData.map((section) => (
              <div key={section.id} className="bg-white border border-gray-200 rounded-lg shadow-sm">
                <button
                  onClick={() => toggleSection(section.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <div className="text-purple-600 mr-3">{section.icon}</div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{section.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{section.summary}</p>
                    </div>
                  </div>
                  {openSections.has(section.id) ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                
                {openSections.has(section.id) && (
                  <div className="px-6 pb-6">
                    <div className="prose prose-sm max-w-none">
                      <div
                        className="whitespace-pre-line text-gray-700 leading-relaxed mb-4"
                        dangerouslySetInnerHTML={{ __html: formatBoldText(section.content) }}
                      />
                      
                      {section.examples && (
                        <div className="bg-gray-50 rounded-lg p-4 mt-4">
                          <h4 className="font-medium text-gray-900 mb-2">Examples:</h4>
                          <ul className="space-y-1">
                            {section.examples.map((example, index) => (
                              <li key={index} className="text-sm text-gray-600 flex items-start">
                                <span className="text-purple-600 mr-2">•</span>
                                {example}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Contact Section */}
        <div className="mt-12 bg-gray-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Questions about our Terms?
          </h2>
          <p className="text-gray-600 mb-6">
            Our team is here to help clarify any questions about our terms and conditions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Contact Legal Team
            </a>
            <a
              href="/faq"
              className="inline-flex items-center px-6 py-3 bg-white text-purple-600 border border-purple-600 rounded-lg hover:bg-purple-50 transition-colors"
            >
              View FAQ
            </a>
          </div>
        </div>
      </div>
    </>
  );
}
