# HouseGoing SEO & Brand Search Optimization

This guide provides steps to solve the Google indexing issues and improve brand visibility in search results for "housegoing" without "au".

## Quick Start

Run these commands to fix the key issues:

```powershell
# Install required packages for the scripts
npm install --save-dev cheerio glob toml axios

# Fix canonical tag issues
node scripts/canonical-fix.js

# Identify soft 404 pages
node scripts/soft-404-fixer.js

# Analyze redirect chains
node scripts/redirect-chain-analyzer.js

# Build your site with the improved SEO
npm run build:prod

# Deploy to production
# (Use your normal deployment process)
```

> **Note**: If you encounter "require is not defined" errors, the scripts have been updated to use ES modules. Make sure you've installed the dependencies:
>
> ```powershell
> cd scripts
> npm install cheerio glob toml axios
> cd ..
> ```

## Key Issues Being Addressed

From your Google Search Console report:

1. **Alternative page with canonical tag issues (47 pages)** - Fixed by `canonical-fix.js`
2. **Soft 404 errors (30 pages)** - Identified by `soft-404-fixer.js`
3. **Page redirects (11 pages)** - Analyzed by `redirect-chain-analyzer.js`
4. **"housegoing" search term visibility** - Improved meta tags & structured data

## What Changed

1. **Updated Meta Tags**
   - Title now emphasizes "HouseGoing" as standalone brand
   - Description highlights HouseGoing as Australia's premier venue platform
   - Keywords include "HouseGoing" as a specific term

2. **Added Schema.org Structured Data**
   - Organization markup for brand identity
   - Website markup with search action
   - Improves Google's understanding of your brand

3. **SEO Analysis Scripts**
   - Script to fix canonical tag issues
   - Tool to identify thin content pages (soft 404s)
   - Redirect chain analyzer

## Submit to Google

After implementing fixes:

1. Go to [Google Search Console](https://search.google.com/search-console)
2. Request indexing for your main URLs:
   - https://housegoing.com.au/
   - https://housegoing.com.au/find-venues
   - https://housegoing.com.au/host/list-your-space
   - Any other important landing pages

## Monitoring

Monitor your Google Search Console over the next 2-4 weeks. You should see:
- Decreasing number of pages with errors
- Increasing number of indexed pages
- Better ranking for "housegoing" without "au"

For detailed recommendations, see the [Brand Search Optimization](./docs/brand-search-optimization.md) document.
