/**
 * LangChain model configurations for HouseGoing
 */
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { RunnableSequence } from "@langchain/core/runnables";
import { LangChainTracer } from "langchain/callbacks";

// Default model to use
const DEFAULT_MODEL = "mistralai/Mistral-7B-Instruct-v0.3";

/**
 * Create a HuggingFace model instance with the specified configuration
 * @param {Object} options - Configuration options
 * @returns {HuggingFaceInference} - Configured model instance
 */
export function createHuggingFaceModel(options = {}) {
  const {
    model = DEFAULT_MODEL,
    temperature = 0.7,
    maxTokens = 1024,
    apiKey = process.env.HUGGINGFACE_API_KEY || process.env.NEXT_PUBLIC_HUGGINGFACE_API_KEY
  } = options;

  return new HuggingFaceInference({
    model,
    apiKey,
    temperature,
    maxTokens,
  });
}

/**
 * Create a LangChain tracer for monitoring and debugging
 * @returns {LangChainTracer} - Configured tracer
 */
export function createTracer() {
  return new LangChainTracer({
    projectName: process.env.LANGSMITH_PROJECT,
  });
}

/**
 * Create a runnable chain with the specified prompt template and model
 * @param {ChatPromptTemplate} promptTemplate - The prompt template to use
 * @param {HuggingFaceInference} model - The model to use
 * @returns {RunnableSequence} - The configured chain
 */
export function createChain(promptTemplate, model) {
  return RunnableSequence.from([
    promptTemplate,
    model,
    new StringOutputParser(),
  ]);
}
