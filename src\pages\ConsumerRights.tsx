import React from 'react';
import { Shield, AlertCircle, Phone, Mail, ExternalLink } from 'lucide-react';

export default function ConsumerRights() {
  return (
    <div className="pt-32 px-4 max-w-4xl mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex items-center mb-6">
          <Shield className="h-8 w-8 text-purple-600 mr-3" />
          <h1 className="text-3xl font-bold">Consumer Rights & Guarantees</h1>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">Your Rights Under Australian Consumer Law</h3>
              <p className="text-blue-800 text-sm">
                When you book through HouseGoing, you're protected by Australian Consumer Law. 
                These rights cannot be excluded or limited by our terms and conditions.
              </p>
            </div>
          </div>
        </div>

        <div className="prose max-w-none">
          <h2 className="text-xl font-semibold mt-6 mb-3">Consumer Guarantees</h2>
          <p className="mb-4">
            Under Australian Consumer Law, you automatically receive these guarantees when booking venues:
          </p>
          <ul className="list-disc pl-6 mb-6">
            <li><strong>Services will be provided with due care and skill</strong> - Venues must be properly maintained and safe</li>
            <li><strong>Services will be fit for purpose</strong> - Venues must be suitable for the intended event type</li>
            <li><strong>Services will be provided within reasonable time</strong> - Bookings must be honored as agreed</li>
            <li><strong>Goods will be of acceptable quality</strong> - Any provided equipment or amenities must work properly</li>
          </ul>

          <h2 className="text-xl font-semibold mt-6 mb-3">Refunds & Remedies</h2>
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <h4 className="font-semibold mb-2">Major Problems</h4>
            <p className="text-sm mb-2">You can choose a refund or replacement if:</p>
            <ul className="text-sm list-disc pl-4">
              <li>The venue has a safety issue that makes it unsuitable for use</li>
              <li>The venue doesn't match the description in a significant way</li>
              <li>The host fails to provide the service after being given reasonable opportunity</li>
            </ul>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-semibold mb-2">Minor Problems</h4>
            <p className="text-sm mb-2">The host must fix the problem within reasonable time, or you can:</p>
            <ul className="text-sm list-disc pl-4">
              <li>Cancel the booking and get a refund</li>
              <li>Keep the booking and seek compensation for the difference in value</li>
            </ul>
          </div>

          <h2 className="text-xl font-semibold mt-6 mb-3">Dispute Resolution</h2>
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold mb-2">Step 1: Contact the Host</h4>
              <p className="text-sm">Try to resolve the issue directly with your host through HouseGoing's messaging system.</p>
            </div>
            
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold mb-2">Step 2: HouseGoing Resolution</h4>
              <p className="text-sm">If you can't resolve it directly, contact our support team for assistance.</p>
            </div>
            
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold mb-2">Step 3: External Resolution</h4>
              <p className="text-sm">For unresolved disputes, you can contact:</p>
              <ul className="text-sm list-disc pl-4 mt-2">
                <li>NSW Fair Trading: 13 32 20</li>
                <li>NSW Civil and Administrative Tribunal (NCAT)</li>
                <li>Australian Competition and Consumer Commission (ACCC)</li>
              </ul>
            </div>
          </div>

          <h2 className="text-xl font-semibold mt-6 mb-3">Contact Information</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold mb-2">HouseGoing Support</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  <span>1300 HOUSE GO (1300 468 734)</span>
                </div>
              </div>
            </div>
            
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold mb-2">External Agencies</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  <a href="https://www.fairtrading.nsw.gov.au" className="text-purple-600 hover:underline">
                    NSW Fair Trading
                  </a>
                </div>
                <div className="flex items-center">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  <a href="https://www.accc.gov.au" className="text-purple-600 hover:underline">
                    ACCC Consumer Rights
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
            <h4 className="font-semibold text-yellow-900 mb-2">Important Note</h4>
            <p className="text-yellow-800 text-sm">
              This information is general in nature. For specific legal advice about your situation, 
              consult with a qualified legal professional or contact the relevant consumer protection agency.
            </p>
          </div>

          <p className="mt-8 text-sm text-gray-500">
            Last updated: January 2025
          </p>
        </div>
      </div>
    </div>
  );
}
