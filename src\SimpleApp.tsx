import React, { useState, useEffect, useRef } from 'react';
import { formatMessageText } from './utils/formatMessage.js';
import { saveMessagesToSession, loadMessagesFromSession, saveContextToSession, loadContextFromSession, clearChatSession } from './utils/sessionStorage.js';
import VenuePreview from './components/VenuePreview';
import './simple-app.css';

// Mock data for venues
const mockVenues = [
  {
    id: 1,
    title: 'Luxury Waterfront Villa',
    description: 'Perfect for parties and events with stunning water views.',
    price: 250,
    location: 'Sydney Harbour',
    capacity: 50,
    images: ['https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-1.2.1&auto=format&fit=crop&w=1567&q=80'],
    rating: 4.8,
    reviews: 24
  },
  {
    id: 2,
    title: 'Modern Urban Loft',
    description: 'Spacious industrial-style loft in the heart of the city.',
    price: 180,
    location: 'Melbourne CBD',
    capacity: 30,
    images: ['https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-1.2.1&auto=format&fit=crop&w=1500&q=80'],
    rating: 4.6,
    reviews: 18
  },
  {
    id: 3,
    title: 'Beachfront Paradise',
    description: 'Direct beach access with large outdoor entertainment area.',
    price: 300,
    location: 'Gold Coast',
    capacity: 40,
    images: ['https://images.unsplash.com/photo-1499793983690-e29da59ef1c2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1500&q=80'],
    rating: 4.9,
    reviews: 32
  }
];

// Quick reply options for AI assistant
const quickReplyOptions = [
  { id: 'birthday', text: 'Birthday Party' },
  { id: 'wedding', text: 'Wedding' },
  { id: 'corporate', text: 'Corporate Event' },
  { id: 'social', text: 'Social Gathering' },
  { id: 'other', text: 'Other Event Type' }
];

/**
 * A simplified version of the HouseGoing app that works without external dependencies
 */
export default function SimpleApp() {
  const [searchLocation, setSearchLocation] = useState('');
  const [searchDate, setSearchDate] = useState('');
  const [searchGuests, setSearchGuests] = useState('');
  const [venues, setVenues] = useState(mockVenues);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [activeTab, setActiveTab] = useState('venues');

  // AI Assistant state
  const [messages, setMessages] = useState(() => {
    const savedMessages = loadMessagesFromSession();
    return savedMessages || [
      { id: 1, type: 'assistant', text: 'Hi there! I\'m Alex from HouseGoing. How can I help you find the perfect venue for your celebration today?', timestamp: new Date() }
    ];
  });
  const [userInput, setUserInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [conversationContext, setConversationContext] = useState(() => {
    const savedContext = loadContextFromSession();
    return savedContext || {
      eventType: null,
      guestCount: null,
      location: null,
      budget: null,
      date: null,
      specialRequirements: null,
      stage: 'greeting' // greeting, event_type, guest_count, location, budget, date, special_req, recommendation
    };
  });
  const [selectedVenue, setSelectedVenue] = useState(null);
  const messagesEndRef = useRef(null);

  // Scroll to bottom of messages when new ones are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }

    // Save messages to session storage when they change
    saveMessagesToSession(messages);
  }, [messages]);

  // Save conversation context to session storage when it changes
  useEffect(() => {
    saveContextToSession(conversationContext);
  }, [conversationContext]);

  // Simulate loading data
  useEffect(() => {
    console.log('SimpleApp loaded successfully');
  }, []);

  // Function to reset the conversation
  const resetConversation = () => {
    clearChatSession();
    setMessages([
      { id: 1, type: 'assistant', text: 'Hi there! I\'m Homie from HouseGoing. How can I help you find the perfect venue for your celebration today?', timestamp: new Date() }
    ]);
    setConversationContext({
      eventType: null,
      guestCount: null,
      location: null,
      budget: null,
      date: null,
      specialRequirements: null,
      stage: 'greeting'
    });
    setSelectedVenue(null);
  };

  // Simple search function
  const handleSearch = (e) => {
    e.preventDefault();
    console.log('Searching for:', { searchLocation, searchDate, searchGuests });
    // In a real app, this would filter venues based on search criteria
  };

  // Mock login function
  const handleLogin = (e) => {
    e.preventDefault();
    setIsLoggedIn(true);
    setShowLoginModal(false);
  };

  // AI Assistant message handling
  const handleSendMessage = () => {
    if (!userInput.trim()) return;

    // Add user message
    const newUserMessage = {
      id: messages.length + 1,
      type: 'user',
      text: userInput,
      timestamp: new Date()
    };

    // Save the input before clearing it
    const inputText = userInput;

    // Update UI immediately
    setMessages(prev => [...prev, newUserMessage]);
    setUserInput('');
    setIsTyping(true);

    // Calculate response time based on message complexity
    // Shorter response time for simple messages, longer for complex ones
    const messageLength = inputText.length;
    const baseThinkingTime = 250; // minimum thinking time in ms
    const additionalTimePerChar = 1.5; // additional ms per character
    const maxAdditionalTime = 350; // cap the additional time

    const additionalTime = Math.min(messageLength * additionalTimePerChar, maxAdditionalTime);
    const thinkingTime = baseThinkingTime + additionalTime;

    setTimeout(() => {
      const botResponse = generateBotResponse(inputText);
      setMessages(prev => [...prev, {
        id: prev.length + 1,
        type: 'assistant',
        text: botResponse.text,
        quickReplies: botResponse.quickReplies,
        venueData: botResponse.venueData,
        timestamp: new Date()
      }]);
      setIsTyping(false);
    }, thinkingTime);
  };

  // Handle quick reply selection
  const handleQuickReply = (replyText) => {
    // Check if this is a venue selection
    const isVenueSelection = mockVenues.some(venue => venue.title === replyText);

    if (isVenueSelection) {
      // Find the selected venue
      const venue = mockVenues.find(v => v.title === replyText);
      setSelectedVenue(venue);

      // Add user message indicating venue selection
      const newUserMessage = {
        id: messages.length + 1,
        type: 'user',
        text: `I'd like to know more about ${replyText}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, newUserMessage]);
      setIsTyping(true);

      // Quick response for venue selection
      setTimeout(() => {
        const venueResponse = {
          id: messages.length + 2,
          type: 'assistant',
          text: `Great choice! **${venue.title}** is one of our most popular venues. Located in ${venue.location}, it can accommodate up to ${venue.capacity} guests and costs $${venue.price} per hour.\n\nWould you like to check availability or see more venues?`,
          quickReplies: [
            { id: 'check-availability', text: 'Check Availability' },
            { id: 'more-venues', text: 'See More Venues' },
            { id: 'contact-host', text: 'Contact Host' }
          ],
          timestamp: new Date()
        };

        setMessages(prev => [...prev, venueResponse]);
        setIsTyping(false);
      }, 300);

      return;
    }

    // Regular quick reply handling
    const newUserMessage = {
      id: messages.length + 1,
      type: 'user',
      text: replyText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newUserMessage]);
    setIsTyping(true);

    // Quick replies get faster responses (200-400ms)
    // This creates the impression that the AI already knows how to respond to these options
    const thinkingTime = Math.floor(Math.random() * 200) + 200;

    setTimeout(() => {
      const botResponse = generateBotResponse(replyText);
      setMessages(prev => [...prev, {
        id: prev.length + 1,
        type: 'assistant',
        text: botResponse.text,
        quickReplies: botResponse.quickReplies,
        venueData: botResponse.venueData,
        timestamp: new Date()
      }]);
      setIsTyping(false);
    }, thinkingTime);
  };

  // Handle venue preview click
  const handleVenueClick = (venue) => {
    setSelectedVenue(venue);

    // Add user message indicating venue selection
    const newUserMessage = {
      id: messages.length + 1,
      type: 'user',
      text: `Tell me more about ${venue.title}`,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newUserMessage]);
    setIsTyping(true);

    // Quick response for venue selection
    setTimeout(() => {
      const venueResponse = {
        id: messages.length + 2,
        type: 'assistant',
        text: `**${venue.title}** is a fantastic choice! Located in ${venue.location}, it features:\n\n- Capacity for up to ${venue.capacity} guests\n- $${venue.price} per hour\n- ${venue.rating} star rating from ${venue.reviews} reviews\n\nThe venue is particularly popular for ${conversationContext.eventType || 'events'} and offers excellent amenities. Would you like to check availability for your date?`,
        quickReplies: [
          { id: 'check-availability', text: 'Check Availability' },
          { id: 'more-venues', text: 'See More Venues' },
          { id: 'contact-host', text: 'Contact Host' }
        ],
        timestamp: new Date()
      };

      setMessages(prev => [...prev, venueResponse]);
      setIsTyping(false);
    }, 300);
  };

  // Generate bot response based on user input and conversation context
  const generateBotResponse = (input) => {
    const lowerInput = input.toLowerCase();
    let newContext = {...conversationContext};

    // Update conversation context based on input
    if (newContext.stage === 'greeting') {
      // Check for booking intent or event type in initial message
      if (lowerInput.includes('book') || lowerInput.includes('reserve') || lowerInput.includes('venue') ||
          lowerInput.includes('looking for') || lowerInput.includes('need a')) {
        newContext.stage = 'event_type';
      } else if (lowerInput.includes('birthday') || lowerInput.includes('party')) {
        newContext.eventType = 'birthday';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('wedding')) {
        newContext.eventType = 'wedding';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('corporate') || lowerInput.includes('business')) {
        newContext.eventType = 'corporate';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('engagement') || lowerInput.includes('proposal')) {
        newContext.eventType = 'engagement';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('reunion') || lowerInput.includes('family')) {
        newContext.eventType = 'reunion';
        newContext.stage = 'guest_count';
      } else {
        newContext.stage = 'event_type';
      }
    } else if (newContext.stage === 'event_type') {
      if (lowerInput.includes('birthday') || lowerInput.includes('party')) {
        newContext.eventType = 'birthday';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('wedding')) {
        newContext.eventType = 'wedding';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('corporate') || lowerInput.includes('business')) {
        newContext.eventType = 'corporate';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('engagement') || lowerInput.includes('proposal')) {
        newContext.eventType = 'engagement';
        newContext.stage = 'guest_count';
      } else if (lowerInput.includes('reunion') || lowerInput.includes('family')) {
        newContext.eventType = 'reunion';
        newContext.stage = 'guest_count';
      }
    } else if (newContext.stage === 'guest_count') {
      if (lowerInput.includes('under 30') || lowerInput.includes('small') || lowerInput.includes('20') ||
          lowerInput.includes('25') || lowerInput.includes('15') || lowerInput.includes('10')) {
        newContext.guestCount = 'small';
        newContext.stage = 'location';
      } else if (lowerInput.includes('30-50') || lowerInput.includes('medium') || lowerInput.includes('40') ||
                lowerInput.includes('35') || lowerInput.includes('45')) {
        newContext.guestCount = 'medium';
        newContext.stage = 'location';
      } else if (lowerInput.includes('over 50') || lowerInput.includes('large') || lowerInput.includes('60') ||
                lowerInput.includes('70') || lowerInput.includes('100')) {
        newContext.guestCount = 'large';
        newContext.stage = 'location';
      }
    } else if (newContext.stage === 'location') {
      if (lowerInput.includes('sydney') || lowerInput.includes('harbour')) {
        newContext.location = 'Sydney Harbour';
        newContext.stage = 'budget';
      } else if (lowerInput.includes('melbourne') || lowerInput.includes('cbd')) {
        newContext.location = 'Melbourne CBD';
        newContext.stage = 'budget';
      } else if (lowerInput.includes('gold coast') || lowerInput.includes('beach')) {
        newContext.location = 'Gold Coast';
        newContext.stage = 'budget';
      } else if (lowerInput.includes('city') || lowerInput.includes('urban')) {
        newContext.location = 'City Center';
        newContext.stage = 'budget';
      } else if (lowerInput.includes('waterfront') || lowerInput.includes('water')) {
        newContext.location = 'Waterfront';
        newContext.stage = 'budget';
      }
    } else if (newContext.stage === 'budget') {
      if (lowerInput.includes('$') || lowerInput.includes('budget') || lowerInput.includes('cost') ||
          lowerInput.includes('price') || lowerInput.includes('expensive') || lowerInput.includes('affordable')) {
        // Extract budget information if possible
        if (lowerInput.includes('affordable') || lowerInput.includes('cheap') || lowerInput.includes('budget')) {
          newContext.budget = 'affordable';
        } else if (lowerInput.includes('premium') || lowerInput.includes('luxury') || lowerInput.includes('high-end')) {
          newContext.budget = 'premium';
        } else if (lowerInput.includes('mid') || lowerInput.includes('average')) {
          newContext.budget = 'mid-range';
        }
        newContext.stage = 'date';
      }
    } else if (newContext.stage === 'date') {
      if (lowerInput.includes('weekend') || lowerInput.includes('saturday') || lowerInput.includes('sunday')) {
        newContext.date = 'weekend';
        newContext.stage = 'special_req';
      } else if (lowerInput.includes('weekday') || lowerInput.includes('monday') || lowerInput.includes('tuesday') ||
                lowerInput.includes('wednesday') || lowerInput.includes('thursday') || lowerInput.includes('friday')) {
        newContext.date = 'weekday';
        newContext.stage = 'special_req';
      } else if (lowerInput.includes('month') || lowerInput.includes('january') || lowerInput.includes('february') ||
                lowerInput.includes('march') || lowerInput.includes('april') || lowerInput.includes('may') ||
                lowerInput.includes('june') || lowerInput.includes('july') || lowerInput.includes('august') ||
                lowerInput.includes('september') || lowerInput.includes('october') || lowerInput.includes('november') ||
                lowerInput.includes('december')) {
        // Extract month information if possible
        newContext.date = 'specific';
        newContext.stage = 'special_req';
      }
    } else if (newContext.stage === 'special_req') {
      newContext.specialRequirements = input;
      newContext.stage = 'recommendation';
    }

    // Save updated context
    setConversationContext(newContext);

    // Generate response based on updated context
    if (newContext.stage === 'event_type') {
      return {
        text: "Great! What type of event are you planning? This helps me find the perfect venue that matches your celebration needs.",
        quickReplies: [
          { id: 'birthday', text: 'Birthday Party' },
          { id: 'wedding', text: 'Wedding' },
          { id: 'corporate', text: 'Corporate Event' },
          { id: 'engagement', text: 'Engagement Party' },
          { id: 'reunion', text: 'Family Reunion' }
        ]
      };
    } else if (newContext.stage === 'guest_count') {
      let responseText = '';
      let quickReplies = [
        { id: 'guests-small', text: 'Under 30 guests' },
        { id: 'guests-medium', text: '30-50 guests' },
        { id: 'guests-large', text: 'Over 50 guests' }
      ];

      switch(newContext.eventType) {
        case 'birthday':
          responseText = "Birthday parties are our specialty! 🎉 We've hosted some amazing celebrations recently. How many guests are you planning to invite?";
          break;
        case 'wedding':
          responseText = "Congratulations on your upcoming wedding! 💍 Sydney offers some stunning backdrops for your special day. Approximately how many guests will be attending?";
          break;
        case 'corporate':
          responseText = "Corporate events need the right atmosphere to make an impression. Our venues offer professional setups with all the tech you need. What's your expected attendance?";
          break;
        case 'engagement':
          responseText = "How exciting! Engagement parties are trending at our waterfront venues this season. How many friends and family will be celebrating with you?";
          break;
        case 'reunion':
          responseText = "Family reunions are all about creating new memories in a special space. Our venues offer plenty of room for the whole family. How many relatives are you expecting?";
          break;
        default:
          responseText = "Fantastic! To help find the perfect venue, could you let me know approximately how many guests you're expecting?";
      }

      return { text: responseText, quickReplies };
    } else if (newContext.stage === 'location') {
      let responseText = '';
      let quickReplies = [
        { id: 'location-sydney', text: 'Sydney Harbour' },
        { id: 'location-melbourne', text: 'Melbourne CBD' },
        { id: 'location-goldcoast', text: 'Gold Coast' }
      ];

      // Personalize based on event type and guest count
      if (newContext.eventType === 'wedding' && newContext.guestCount === 'large') {
        responseText = "For a larger wedding, our beachfront and harbour venues offer stunning views and plenty of space. Do you have a preferred location in mind?";
        quickReplies = [
          { id: 'location-sydney', text: 'Sydney Harbour' },
          { id: 'location-goldcoast', text: 'Gold Coast' },
          { id: 'location-other', text: 'Other Areas' }
        ];
      } else if (newContext.eventType === 'corporate' && newContext.guestCount === 'medium') {
        responseText = "For mid-sized corporate events, our CBD venues offer convenience and professional atmospheres. Which location works best for your team?";
        quickReplies = [
          { id: 'location-sydney', text: 'Sydney CBD' },
          { id: 'location-melbourne', text: 'Melbourne CBD' },
          { id: 'location-other', text: 'Other Areas' }
        ];
      } else if (newContext.eventType === 'birthday' && newContext.guestCount === 'small') {
        responseText = "Intimate birthday celebrations work beautifully in our boutique venues. We have some gems in popular areas. Where would you prefer?";
      } else {
        responseText = "Thanks! Location is key to a successful event. Where are you looking to host your celebration?";
      }

      return { text: responseText, quickReplies };
    } else if (newContext.stage === 'budget') {
      return {
        text: "Great choice! Now, could you give me an idea of your budget range? This helps me suggest venues that offer the best value for your celebration.",
        quickReplies: [
          { id: 'budget-affordable', text: 'Budget-Friendly' },
          { id: 'budget-midrange', text: 'Mid-Range' },
          { id: 'budget-premium', text: 'Premium' }
        ]
      };
    } else if (newContext.stage === 'date') {
      const currentMonth = new Date().toLocaleString('default', { month: 'long' });
      const nextMonth = new Date(new Date().setMonth(new Date().getMonth() + 1)).toLocaleString('default', { month: 'long' });

      return {
        text: `When are you planning to host your event? ${currentMonth} and ${nextMonth} are booking quickly for ${newContext.eventType} events, especially at our ${newContext.location} venues.`,
        quickReplies: [
          { id: 'date-weekend', text: 'Weekend' },
          { id: 'date-weekday', text: 'Weekday' },
          { id: 'date-specific', text: `${currentMonth}` },
          { id: 'date-specific', text: `${nextMonth}` }
        ]
      };
    } else if (newContext.stage === 'special_req') {
      return {
        text: "We're getting closer to finding your perfect venue! Any special requirements or must-have features for your event? (e.g., outdoor space, catering options, AV equipment)",
        quickReplies: [
          { id: 'feature-outdoor', text: 'Outdoor Space' },
          { id: 'feature-catering', text: 'In-house Catering' },
          { id: 'feature-av', text: 'AV Equipment' },
          { id: 'feature-parking', text: 'Parking' },
          { id: 'feature-none', text: 'No Special Requirements' }
        ]
      };
    } else if (newContext.stage === 'recommendation') {
      // Generate personalized recommendations based on collected information
      let recommendationText = '';
      let venueOptions = [];
      let venueData = [];

      // Match venues based on collected preferences
      if (newContext.location === 'Sydney Harbour' || newContext.location === 'Waterfront') {
        venueOptions.push('Luxury Waterfront Villa');
        venueData.push(mockVenues.find(v => v.title === 'Luxury Waterfront Villa'));
      }
      if (newContext.location === 'Melbourne CBD' || newContext.location === 'City Center') {
        venueOptions.push('Modern Urban Loft');
        venueData.push(mockVenues.find(v => v.title === 'Modern Urban Loft'));
      }
      if (newContext.location === 'Gold Coast' || newContext.guestCount === 'large') {
        venueOptions.push('Beachfront Paradise');
        venueData.push(mockVenues.find(v => v.title === 'Beachfront Paradise'));
      }

      // If no specific matches, include all options
      if (venueOptions.length === 0) {
        venueOptions = ['Luxury Waterfront Villa', 'Modern Urban Loft', 'Beachfront Paradise'];
        venueData = mockVenues;
      }

      // Create personalized recommendation
      recommendationText = `Based on your ${newContext.eventType} plans, I've found some perfect venues for you! 🎉\n\n`;

      if (venueOptions.includes('Luxury Waterfront Villa')) {
        recommendationText += `**Luxury Waterfront Villa** - Perfect for ${newContext.eventType === 'wedding' ? 'intimate wedding ceremonies' : 'upscale celebrations'} with stunning water views. Accommodates up to 50 guests with premium amenities.\n\n`;
      }

      if (venueOptions.includes('Modern Urban Loft')) {
        recommendationText += `**Modern Urban Loft** - A stylish ${newContext.eventType === 'corporate' ? 'corporate-friendly space' : 'contemporary venue'} in the heart of the city. Ideal for ${newContext.guestCount === 'small' ? 'intimate gatherings' : 'mid-sized events'} with modern aesthetics.\n\n`;
      }

      if (venueOptions.includes('Beachfront Paradise')) {
        recommendationText += `**Beachfront Paradise** - Our most popular venue for ${newContext.eventType === 'wedding' ? 'beach weddings' : 'larger celebrations'}. Offers both indoor and outdoor spaces with breathtaking ocean views.\n\n`;
      }

      recommendationText += "Would you like to see more details about any of these venues? Or would you prefer to explore more options?";

      return {
        text: recommendationText,
        quickReplies: venueOptions.map(venue => ({ id: `venue-${venue.toLowerCase().replace(/\s+/g, '-')}`, text: venue })),
        venueData: venueData // Include the venue data for previews
      };
    } else {
      // Default response for initial greeting or fallback
      return {
        text: "I'd be happy to help you find the perfect venue for your celebration! What type of event are you planning?",
        quickReplies: [
          { id: 'birthday', text: 'Birthday Party' },
          { id: 'wedding', text: 'Wedding' },
          { id: 'corporate', text: 'Corporate Event' },
          { id: 'engagement', text: 'Engagement Party' },
          { id: 'reunion', text: 'Family Reunion' }
        ]
      };
    }
  };

  // Render venue card
  const renderVenueCard = (venue) => (
    <div key={venue.id} className="venue-card">
      <div className="venue-image" style={{ backgroundImage: `url(${venue.images[0]})` }}></div>
      <div className="venue-details">
        <h3>{venue.title}</h3>
        <p className="venue-location">{venue.location}</p>
        <p className="venue-description">{venue.description}</p>
        <div className="venue-meta">
          <span className="venue-capacity">Up to {venue.capacity} guests</span>
          <span className="venue-price">${venue.price}/hour</span>
        </div>
        <div className="venue-rating">
          <span className="stars">{'★'.repeat(Math.floor(venue.rating))}{'☆'.repeat(5 - Math.floor(venue.rating))}</span>
          <span className="reviews">({venue.reviews} reviews)</span>
        </div>
        <button className="view-button">View Details</button>
      </div>
    </div>
  );

  // Login modal
  const renderLoginModal = () => (
    <div className={`login-modal ${showLoginModal ? 'show' : ''}`}>
      <div className="login-content">
        <h2>Log In</h2>
        <form onSubmit={handleLogin}>
          <div className="form-group">
            <label>Email</label>
            <input type="email" placeholder="Your email" required />
          </div>
          <div className="form-group">
            <label>Password</label>
            <input type="password" placeholder="Your password" required />
          </div>
          <button type="submit" className="login-button">Log In</button>
        </form>
        <button className="close-button" onClick={() => setShowLoginModal(false)}>×</button>
      </div>
    </div>
  );

  // Host dashboard (simplified)
  const renderHostDashboard = () => (
    <div className="host-dashboard">
      <h2>Host Dashboard</h2>
      <div className="dashboard-stats">
        <div className="stat-card">
          <h3>Properties</h3>
          <p className="stat-value">3</p>
        </div>
        <div className="stat-card">
          <h3>Bookings</h3>
          <p className="stat-value">12</p>
        </div>
        <div className="stat-card">
          <h3>Revenue</h3>
          <p className="stat-value">$2,450</p>
        </div>
      </div>
      <div className="dashboard-actions">
        <button className="action-button">Add New Property</button>
        <button className="action-button">View Bookings</button>
        <button className="action-button">Messages</button>
      </div>
    </div>
  );

  // Render AI Assistant chat interface
  const renderAIAssistant = () => (
    <div className="ai-assistant-section">
      <div className="ai-assistant-header">
        <h2>Chat with Homie, your HouseGoing Assistant</h2>
        <button className="reset-chat-button" onClick={resetConversation}>New Conversation</button>
      </div>
      <div className="chat-container">
        <div className="chat-messages">
          {messages.map(message => (
            <div key={message.id} className={`message ${message.type}`}>
              {message.type === 'assistant' && <div className="assistant-avatar">AI</div>}
              <div className="message-content">
                <p dangerouslySetInnerHTML={{ __html: formatMessageText(message.text) }}></p>

                {/* Venue previews */}
                {message.venueData && message.venueData.length > 0 && (
                  <div className="venue-previews">
                    {message.venueData.map(venue => (
                      <VenuePreview
                        key={venue.id}
                        venue={venue}
                        onClick={() => handleVenueClick(venue)}
                      />
                    ))}
                  </div>
                )}

                {/* Quick replies */}
                {message.quickReplies && (
                  <div className="quick-replies">
                    {message.quickReplies.map(reply => (
                      <button
                        key={reply.id}
                        className="quick-reply-button"
                        onClick={() => handleQuickReply(reply.text)}
                      >
                        {reply.text}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {message.type === 'user' && <div className="user-avatar">You</div>}
            </div>
          ))}
          {isTyping && (
            <div className="message assistant typing">
              <div className="assistant-avatar">AI</div>
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="chat-input">
          <input
            type="text"
            placeholder="Type your message here..."
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          />
          <button onClick={handleSendMessage}>Send</button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="app-container">
      <header className="app-header">
        <div className="logo">HouseGoing</div>
        <nav className="main-nav">
          <ul>
            <li className={activeTab === 'venues' ? 'active' : ''}>
              <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('venues'); }}>Venues</a>
            </li>
            <li className={activeTab === 'how-it-works' ? 'active' : ''}>
              <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('how-it-works'); }}>How It Works</a>
            </li>
            <li className={activeTab === 'ai-assistant' ? 'active' : ''}>
              <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('ai-assistant'); }}>AI Assistant</a>
            </li>
          </ul>
        </nav>
        <div className="user-actions">
          {isLoggedIn ? (
            <div className="user-menu">
              <span className="user-name">Demo User</span>
              <button className="host-button" onClick={() => setActiveTab('host')}>Host Portal</button>
              <button className="logout-button" onClick={() => setIsLoggedIn(false)}>Log Out</button>
            </div>
          ) : (
            <>
              <button className="login-button" onClick={() => setShowLoginModal(true)}>Log In</button>
              <button className="signup-button">Sign Up</button>
            </>
          )}
        </div>
      </header>

      <main className="app-main">
        {activeTab === 'venues' && (
          <>
            <section className="hero-section">
              <h1>Where Great Parties <span className="highlight">Begin</span></h1>
              <p className="subtitle">Discover and book fully verified venues across NSW, where every event can shine!</p>

              <form className="search-form" onSubmit={handleSearch}>
                <div className="search-inputs">
                  <div className="search-group">
                    <label>Location</label>
                    <input
                      type="text"
                      placeholder="Where to?"
                      value={searchLocation}
                      onChange={(e) => setSearchLocation(e.target.value)}
                    />
                  </div>
                  <div className="search-group">
                    <label>Date</label>
                    <input
                      type="date"
                      value={searchDate}
                      onChange={(e) => setSearchDate(e.target.value)}
                    />
                  </div>
                  <div className="search-group">
                    <label>Guests</label>
                    <input
                      type="number"
                      placeholder="Number of guests"
                      value={searchGuests}
                      onChange={(e) => setSearchGuests(e.target.value)}
                    />
                  </div>
                  <button type="submit" className="search-button">Search</button>
                </div>
              </form>
            </section>

            <section className="venues-section">
              <h2>Featured Venues</h2>
              <div className="venues-grid">
                {venues.map(renderVenueCard)}
              </div>
            </section>

            <section className="features-section">
              <h2>Why Choose HouseGoing</h2>
              <div className="features-grid">
                <div className="feature-card">
                  <h3>Verified Properties</h3>
                  <p>Every venue is thoroughly verified for quality and safety.</p>
                </div>
                <div className="feature-card">
                  <h3>Trusted Reviews</h3>
                  <p>Real reviews from verified guests.</p>
                </div>
                <div className="feature-card">
                  <h3>Quick Response Time</h3>
                  <p>Most hosts respond within 2 hours.</p>
                </div>
              </div>
            </section>
          </>
        )}

        {activeTab === 'how-it-works' && (
          <section className="how-it-works-section">
            <h2>How HouseGoing Works</h2>
            <div className="steps-container">
              <div className="step">
                <div className="step-number">1</div>
                <h3>Find Your Perfect Venue</h3>
                <p>Search through our curated list of party-friendly venues with detailed descriptions and verified photos.</p>
              </div>
              <div className="step">
                <div className="step-number">2</div>
                <h3>Book With Confidence</h3>
                <p>Secure your booking with our easy payment system. All venues have clear pricing and no hidden fees.</p>
              </div>
              <div className="step">
                <div className="step-number">3</div>
                <h3>Enjoy Your Event</h3>
                <p>Arrive at your venue and enjoy your event with peace of mind, knowing everything has been arranged.</p>
              </div>
            </div>
          </section>
        )}

        {activeTab === 'ai-assistant' && renderAIAssistant()}

        {activeTab === 'host' && renderHostDashboard()}
      </main>

      <footer className="app-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h4>HouseGoing</h4>
            <ul>
              <li><a href="#">About Us</a></li>
              <li><a href="#">Careers</a></li>
              <li><a href="#">Press</a></li>
              <li><a href="#">Policies</a></li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Support</h4>
            <ul>
              <li><a href="#">Help Center</a></li>
              <li><a href="#">Safety Information</a></li>
              <li><a href="#">Cancellation Options</a></li>
              <li><a href="#">Contact Us</a></li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Hosting</h4>
            <ul>
              <li><a href="#">Try Hosting</a></li>
              <li><a href="#">Host Resources</a></li>
              <li><a href="#">Community Forum</a></li>
              <li><a href="#">Responsible Hosting</a></li>
            </ul>
          </div>
        </div>
        <div className="copyright">
          &copy; 2024 HouseGoing. All rights reserved.
        </div>
      </footer>

      {renderLoginModal()}
    </div>
  );
}
