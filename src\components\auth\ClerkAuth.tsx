import React from 'react';
import { SignIn, SignUp, useUser, useAuth } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase-client';

interface ClerkAuthProps {
  mode: 'signIn' | 'signUp';
  redirectUrl?: string;
  userType?: 'guest' | 'host';
}

export default function ClerkAuth({ mode, redirectUrl = '/dashboard', userType = 'guest' }: ClerkAuthProps) {
  const navigate = useNavigate();
  const { isSignedIn, user } = useUser();
  const { getToken } = useAuth();

  // If user is already signed in, redirect to the specified URL
  React.useEffect(() => {
    if (isSignedIn && user) {
      // Create user profile in Supabase
      const createUserProfile = async () => {
        try {
          // Get token from Clerk
          const token = await getToken({ template: 'supabase' });
          
          if (!token) {
            console.error('No Supabase token available from <PERSON>');
            return;
          }
          
          // Create a Supabase client with the token
          const supabaseWithAuth = supabase;
          
          // Set the auth token
          supabaseWithAuth.auth.setAuth(token);
          
          // Check if user profile exists
          const { data: existingProfile, error: checkError } = await supabaseWithAuth
            .from('user_profiles')
            .select('*')
            .eq('clerk_id', user.id)
            .maybeSingle();
            
          if (checkError) {
            console.error('Error checking for existing profile:', checkError);
          }
          
          if (!existingProfile) {
            // Create user profile
            const { error: createError } = await supabaseWithAuth
              .from('user_profiles')
              .insert({
                clerk_id: user.id,
                email: user.primaryEmailAddress?.emailAddress || '',
                first_name: user.firstName || '',
                last_name: user.lastName || '',
                role: userType,
                is_host: userType === 'host',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
              
            if (createError) {
              console.error('Error creating user profile in Supabase:', createError);
            } else {
              console.log('User profile created successfully in Supabase');
            }
          } else {
            console.log('User profile already exists in Supabase');
          }
          
          // Store user type in localStorage
          localStorage.setItem('auth_user_type', userType);
          localStorage.setItem('auth_success', 'true');
          localStorage.setItem('auth_success_time', new Date().toISOString());
          
          // Dispatch auth_complete event
          const authCompleteEvent = new CustomEvent('auth_complete', {
            detail: {
              user: {
                id: user.id,
                email: user.primaryEmailAddress?.emailAddress,
                first_name: user.firstName,
                last_name: user.lastName,
                role: userType
              }
            }
          });
          window.dispatchEvent(authCompleteEvent);
          
          // Redirect to the specified URL
          navigate(redirectUrl);
        } catch (error) {
          console.error('Error in createUserProfile:', error);
        }
      };
      
      createUserProfile();
    }
  }, [isSignedIn, user, navigate, redirectUrl, userType, getToken]);

  // Appearance customization for Clerk components
  const appearance = {
    elements: {
      rootBox: "mx-auto w-full max-w-md",
      card: "shadow-lg rounded-lg border border-gray-200 p-6",
      headerTitle: "text-2xl font-bold text-center text-gray-900",
      headerSubtitle: "text-center text-gray-600",
      formButtonPrimary: "w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium py-3 px-4 rounded-md",
      formFieldLabel: "block text-sm font-medium text-gray-700",
      formFieldInput: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",
      footerActionLink: "text-purple-600 hover:text-purple-800",
      identityPreviewEditButton: "text-purple-600 hover:text-purple-800",
      formFieldAction: "text-purple-600 hover:text-purple-800",
      alert: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",
      alertText: "text-sm",
      socialButtonsBlockButton: "border border-gray-300 hover:border-gray-400 bg-white text-gray-700 hover:bg-gray-50",
      socialButtonsBlockButtonText: "font-medium",
      socialButtonsBlockButtonArrow: "text-gray-500",
    },
  };

  // Render the appropriate Clerk component based on the mode
  return (
    <div className="container mx-auto py-8">
      {mode === 'signIn' ? (
        <SignIn 
          routing="path"
          path="/sign-in"
          signUpUrl="/sign-up"
          redirectUrl={redirectUrl}
          appearance={appearance}
        />
      ) : (
        <SignUp 
          routing="path"
          path="/sign-up"
          signInUrl="/sign-in"
          redirectUrl={redirectUrl}
          appearance={appearance}
        />
      )}
    </div>
  );
}
