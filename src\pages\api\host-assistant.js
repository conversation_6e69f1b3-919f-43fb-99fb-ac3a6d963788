/**
 * API route for the Host Assistant
 */

// This is a mock implementation since we don't have the actual AI model running in the browser
// In production, this would connect to a backend service

// Store sessions in memory (in production, use a database)
const sessions = new Map();

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, context, sessionId } = req.body;

    // Validate message
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get or create session
    let session;
    if (sessionId && sessions.has(sessionId)) {
      session = sessions.get(sessionId);
    } else {
      // Create a new session
      session = {
        id: Math.random().toString(36).substring(2, 15),
        history: []
      };
      sessions.set(session.id, session);
    }

    // Add message to history
    session.history.push({ role: 'user', content: message });

    // Generate a mock response based on the message and context
    let response = '';

    if (message.toLowerCase().includes('party score')) {
      response = "The Party Score is our proprietary rating system that evaluates how suitable a venue is for hosting parties based on local regulations, noise restrictions, and venue features. It ranges from 1-10, with 10 being the most party-friendly. Would you like me to calculate a Party Score for your venue?";
    } else if (message.toLowerCase().includes('pricing') || message.toLowerCase().includes('price')) {
      response = "For pricing your venue, I recommend considering factors like location, amenities, capacity, and competition. Most party venues on HouseGoing charge between $100-$300 per hour. Premium venues with pools or special features can charge more. Would you like me to analyze optimal pricing for your specific venue?";
    } else if (message.toLowerCase().includes('listing') || message.toLowerCase().includes('description')) {
      response = "Great listings include high-quality photos, detailed descriptions highlighting unique features, clear house rules, and accurate amenity information. Professional photos can increase bookings by up to 40%. Would you like specific tips for improving your venue listing?";
    } else if (message.toLowerCase().includes('rules') || message.toLowerCase().includes('policy')) {
      response = "Clear house rules are essential for successful hosting. I recommend covering noise restrictions, guest limits, parking information, alcohol policies, and cleanup expectations. Would you like me to help generate customized rules for your venue?";
    } else {
      response = "G'day! I'm your HouseGoing Host Assistant. I can help with venue optimization, Party Score calculations, pricing strategies, and more. How can I help you make the most of your venue today?";
    }

    // Add context-specific information if provided
    if (context) {
      response += ` Based on your ${context} context, I'd be happy to provide more specific guidance.`;
    }

    // Add response to history
    session.history.push({ role: 'assistant', content: response });

    // Return response
    return res.status(200).json({
      response,
      sessionId: session.id
    });
  } catch (error) {
    console.error('Error processing host assistant message:', error);
    return res.status(500).json({
      error: 'An error occurred while processing your message',
      details: error.message
    });
  }
}
