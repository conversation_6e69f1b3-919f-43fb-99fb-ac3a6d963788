# 📧 Signup Notifications Setup Guide

## 🎯 **What This Does**

When someone signs up on HouseGoing:
- **Customer signup** → <PERSON>ail sent to `<EMAIL>`
- **Host signup** → Email sent to `<EMAIL>`

## 📁 **Files Added**

### 1. **Email Templates** (`src/services/cleanEmailTemplates.ts`)
- `CUSTOMER_SIGNUP_ADMIN` - Green themed customer notification
- `HOST_SIGNUP_ADMIN` - Purple themed host notification with "Action Required" alert

### 2. **Signup Hook** (`src/hooks/useSignupNotifications.ts`)
- `useSignupNotifications()` - React hook for components
- `notifyAdminOfSignup()` - Utility function for anywhere

## 🔧 **How to Integrate**

### **Option 1: In Your Signup Components**

```typescript
import { useSignupNotifications } from '../hooks/useSignupNotifications';

function SignupForm() {
  const { sendSignupNotification } = useSignupNotifications();

  const handleSignup = async (formData) => {
    // Your existing signup logic...
    
    // Send admin notification
    await sendSignupNotification({
      email: formData.email,
      firstName: formData.firstName,
      lastName: formData.lastName,
      userType: 'customer' // or 'host'
    });
  };

  return (
    // Your signup form...
  );
}
```

### **Option 2: In Clerk Webhooks/Events**

```typescript
import { notifyAdminOfSignup } from '../hooks/useSignupNotifications';

// When Clerk user is created
const handleUserCreated = async (user) => {
  // Determine user type based on your logic
  const userType = user.publicMetadata?.userType || 'customer';
  
  await notifyAdminOfSignup(
    user.primaryEmailAddress.emailAddress,
    `${user.firstName} ${user.lastName}`,
    userType
  );
};
```

### **Option 3: Direct Function Call**

```typescript
import { sendCustomerSignupNotification, sendHostSignupNotification } from '../services/cleanEmailTemplates';

// For customer signup
await sendCustomerSignupNotification('<EMAIL>', 'John Doe');

// For host signup  
await sendHostSignupNotification('<EMAIL>', 'Jane Smith');
```

## 📧 **Email Routing**

### **Customer Signups** → `<EMAIL>`
- **Subject**: "New customer signup: [Name] joined HouseGoing"
- **Color**: Green theme
- **Content**: Customer details, growth message

### **Host Signups** → `<EMAIL>`
- **Subject**: "New host signup: [Name] wants to list venues on HouseGoing"
- **Color**: Purple theme
- **Content**: Host details, "Action Required" alert, onboarding reminder

## 🎨 **Email Design Features**

### **Clean & Professional**
- No emojis (fixes the ?? issue)
- Good contrast for readability
- Mobile responsive
- Admin-focused layout

### **Dynamic Content**
- Shows name if provided, "Not provided" if missing
- Email always included
- User type clearly labeled
- Timestamp shows "Just now"

## 🚀 **Testing**

```typescript
// Test customer notification
import { generateSampleEmail } from '../services/cleanEmailTemplates';

const customerEmail = generateSampleEmail('CUSTOMER_SIGNUP_ADMIN', {
  recipientName: 'Test Customer',
  recipientEmail: '<EMAIL>'
});

// Test host notification
const hostEmail = generateSampleEmail('HOST_SIGNUP_ADMIN', {
  recipientName: 'Test Host',
  recipientEmail: '<EMAIL>'
});
```

## 🔍 **Where to Add the Integration**

### **Recommended Locations:**

1. **Signup Form Components**
   - `src/components/auth/SignupForm.tsx`
   - `src/components/auth/CustomerSignup.tsx`
   - `src/components/auth/HostSignup.tsx`

2. **Authentication Handlers**
   - `src/services/auth.ts`
   - `src/hooks/useAuth.ts`

3. **Clerk Integration**
   - `src/components/ClerkProvider.tsx`
   - Clerk webhook handlers

### **Best Practice:**
Add the notification call **after** successful user creation but **before** redirecting the user.

## 📊 **Benefits**

### **For You:**
- **Real-time alerts** when new users join
- **User type identification** (customer vs host)
- **Growth tracking** - see signup patterns
- **Onboarding opportunities** - reach out to new hosts

### **For Business:**
- **Customer insights** - track user acquisition
- **Host support** - help new hosts get started
- **Quality control** - monitor who's joining
- **Engagement** - follow up with new users

## 🛠 **Next Steps**

1. **Choose integration method** (Option 1, 2, or 3 above)
2. **Add to your signup flow**
3. **Test with real signups**
4. **Monitor email delivery**
5. **Consider adding user analytics dashboard**

The notifications are ready to go - just add the function calls to your existing signup process! 🎉
