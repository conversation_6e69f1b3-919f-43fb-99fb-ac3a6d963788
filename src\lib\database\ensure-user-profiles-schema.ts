/**
 * Ensure User Profiles Table Schema
 * 
 * This function ensures the user_profiles table has the correct schema
 * with first_name and last_name columns
 */

import { getSupabaseClient } from '../supabase-client';

export async function ensureUserProfilesSchema(): Promise<boolean> {
  console.log('Ensuring user_profiles table has correct schema...');
  
  try {
    const supabase = getSupabaseClient();
    
    // First, check if the table exists and what columns it has
    const { data: tableInfo, error: tableError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);
    
    if (tableError && tableError.message.includes('relation "user_profiles" does not exist')) {
      console.log('user_profiles table does not exist, creating it...');
      
      // Create the table with the correct schema
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS user_profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          clerk_id TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          role TEXT NOT NULL DEFAULT 'guest',
          first_name TEXT,
          last_name TEXT,
          full_name TEXT,
          avatar_url TEXT,
          bio TEXT,
          phone TEXT,
          is_host BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON user_profiles(clerk_id);
        CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
        
        -- Create trigger for updated_at
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        CREATE TRIGGER update_user_profiles_updated_at
        BEFORE UPDATE ON user_profiles
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `;
      
      // Try to execute the SQL
      try {
        const { error: execError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
        if (execError) {
          console.warn('Could not create table using exec_sql:', execError);
          return false;
        }
        console.log('user_profiles table created successfully');
      } catch (error) {
        console.warn('exec_sql function not available, table creation skipped');
        return false;
      }
    } else {
      console.log('user_profiles table exists, checking schema...');
      
      // Check if we have the required columns by trying to select them
      try {
        const { error: schemaError } = await supabase
          .from('user_profiles')
          .select('first_name, last_name, clerk_id, email, role, bio, phone, is_host')
          .limit(1);
        
        if (schemaError) {
          console.log('Schema check error:', schemaError);
          
          // If columns are missing, try to add them
          const alterTableSQL = `
            -- Add missing columns if they don't exist
            DO $$
            BEGIN
              -- Add first_name column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='first_name') THEN
                ALTER TABLE user_profiles ADD COLUMN first_name TEXT;
              END IF;
              
              -- Add last_name column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='last_name') THEN
                ALTER TABLE user_profiles ADD COLUMN last_name TEXT;
              END IF;
              
              -- Add clerk_id column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='clerk_id') THEN
                ALTER TABLE user_profiles ADD COLUMN clerk_id TEXT UNIQUE;
              END IF;
              
              -- Add role column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='role') THEN
                ALTER TABLE user_profiles ADD COLUMN role TEXT DEFAULT 'guest';
              END IF;
              
              -- Add bio column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='bio') THEN
                ALTER TABLE user_profiles ADD COLUMN bio TEXT;
              END IF;
              
              -- Add phone column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='phone') THEN
                ALTER TABLE user_profiles ADD COLUMN phone TEXT;
              END IF;
              
              -- Add is_host column if it doesn't exist
              IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                           WHERE table_name='user_profiles' AND column_name='is_host') THEN
                ALTER TABLE user_profiles ADD COLUMN is_host BOOLEAN DEFAULT false;
              END IF;
            END $$;
          `;
          
          try {
            const { error: alterError } = await supabase.rpc('exec_sql', { sql: alterTableSQL });
            if (alterError) {
              console.warn('Could not alter table schema:', alterError);
              return false;
            }
            console.log('user_profiles table schema updated successfully');
          } catch (error) {
            console.warn('exec_sql function not available, schema update skipped');
            return false;
          }
        } else {
          console.log('user_profiles table schema is correct');
        }
      } catch (error) {
        console.error('Error checking table schema:', error);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error ensuring user_profiles schema:', error);
    return false;
  }
}

/**
 * Test the user_profiles table functionality
 */
export async function testUserProfilesTable(): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    
    // Try to select from the table to test if it works
    const { data, error } = await supabase
      .from('user_profiles')
      .select('id, clerk_id, email, first_name, last_name, role, bio, phone, is_host')
      .limit(1);
    
    if (error) {
      console.error('Error testing user_profiles table:', error);
      return false;
    }
    
    console.log('user_profiles table test successful');
    return true;
  } catch (error) {
    console.error('Error testing user_profiles table:', error);
    return false;
  }
}
