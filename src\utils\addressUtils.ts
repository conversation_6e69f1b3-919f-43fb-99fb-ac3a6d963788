/**
 * Parses and normalizes NSW addresses for better geocoding results
 */
export async function parseNSWAddress(address: string): Promise<string> {
  // Remove extra whitespace
  let normalized = address.trim().replace(/\s+/g, ' ');

  // Handle unit numbers (e.g. "701/97-99 Bathurst St" -> "Unit 701, 97-99 Bathurst St")
  const unitMatch = normalized.match(/^(\d+)\/(\d+.*)/);
  if (unitMatch) {
    normalized = `Unit ${unitMatch[1]}, ${unitMatch[2]}`;
  }

  // Handle addresses with letter suffixes (e.g. "17a George Street")
  // This could be a duplex, townhouse, semi-detached house, or separate house on the same plot
  const letterSuffixMatch = normalized.match(/^(\d+)([a-zA-Z])\s+(.+)/);
  if (letterSuffixMatch) {
    // Don't modify the address format, but flag it for property type detection
    console.log('Detected address with letter suffix - could be duplex, townhouse, or separate dwelling on shared land');
  }

  // Fetch address suggestions from the NSW Planning API
  const response = await axios.get('https://nsw-planning-api.com/suggestions', { params: { q: address } });
  const suggestions = response.data;

  // Add NSW suffix if not present
  if (!normalized.toLowerCase().includes('nsw') &&
      !normalized.toLowerCase().includes('new south wales')) {
    normalized += ', NSW';
  }

  // Add Australia suffix if not present
  if (!normalized.toLowerCase().includes('australia')) {
    normalized += ', Australia';
  }

  return normalized;
}

// Hook for address lookup
export const useAddressLookup = () => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSuggestions = async (address) => {
      try {
        setLoading(true);
        const response = await axios.get('https://nsw-planning-api.com/suggestions', { params: { q: address } });
        setSuggestions(response.data);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchSuggestions(address);
  }, [address]);

  return { suggestions, loading, error };
};


/**
 * Extracts the LGA (Local Government Area) from an address string
 */
export function extractLGAFromAddress(address: string): string | null {
  // Common LGA patterns in addresses
  const lgaPatterns = [
    /City of ([A-Za-z\s]+)(?:,|$)/i,       // City of Sydney
    /([A-Za-z\s]+) Council(?:,|$)/i,       // Randwick Council
    /([A-Za-z\s]+) Shire(?:,|$)/i,         // Sutherland Shire
    /([A-Za-z\s]+) Municipal(?:,|$)/i      // Woollahra Municipal
  ];

  // Check for specific suburbs and their corresponding LGAs
  const suburbToLGA: Record<string, string> = {
    // Northern Sydney
    'epping': 'City of Ryde',
    'eastwood': 'City of Ryde',
    'ryde': 'City of Ryde',
    'hornsby': 'Hornsby Shire',
    'gordon': 'Ku-ring-gai Council',
    'pymble': 'Ku-ring-gai Council',
    'chatswood': 'City of Willoughby',
    'north sydney': 'North Sydney Council',

    // Inner Sydney
    'sydney': 'City of Sydney',
    'surry hills': 'City of Sydney',
    'beaconsfield': 'City of Sydney',
    'alexandria': 'City of Sydney',
    'redfern': 'City of Sydney',
    'waterloo': 'City of Sydney',
    'zetland': 'City of Sydney',
    'erskineville': 'City of Sydney',
    'newtown': 'Inner West Council',
    'marrickville': 'Inner West Council',
    'leichhardt': 'Inner West Council',

    // Eastern Suburbs
    'bondi': 'Waverley Council',
    'randwick': 'Randwick City Council',
    'maroubra': 'Randwick City Council',
    'coogee': 'Randwick City Council',

    // Western Sydney
    'parramatta': 'City of Parramatta',
    'bankstown': 'City of Canterbury-Bankstown',
    'blacktown': 'City of Blacktown',
    'glendenning': 'City of Blacktown',
    'wetherill park': 'City of Fairfield',
    'smithfield': 'City of Fairfield',
    'liverpool': 'City of Liverpool',
    'penrith': 'City of Penrith',

    // Industrial Areas
    'eastern creek': 'City of Blacktown',
    'erskine park': 'City of Penrith',
    'huntingwood': 'City of Blacktown',
    'minto': 'City of Campbelltown',
    'moorebank': 'City of Liverpool',
    'smeaton grange': 'Camden Council',
    'prestons': 'City of Liverpool'
  };

  // First try to extract LGA directly from the address
  for (const pattern of lgaPatterns) {
    const match = address.match(pattern);
    if (match && match[1]) {
      if (pattern.toString().includes('City of')) {
        return `City of ${match[1].trim()}`;
      } else if (pattern.toString().includes('Council')) {
        return `${match[1].trim()} Council`;
      } else if (pattern.toString().includes('Shire')) {
        return `${match[1].trim()} Shire`;
      } else if (pattern.toString().includes('Municipal')) {
        return `${match[1].trim()} Municipal Council`;
      }
    }
  }

  // If no direct LGA found, try to match by suburb
  const lowerAddress = address.toLowerCase();
  for (const [suburb, lga] of Object.entries(suburbToLGA)) {
    if (lowerAddress.includes(suburb)) {
      return lga;
    }
  }

  return null;
}

/**
 * Extracts the base address (without unit number) for geocoding fallback
 */
export function getBaseAddress(address: string) {
  // Remove unit number if present
  return address.replace(/^\d+\//, '');
}
