import React from 'react';
import { Link } from 'react-router-dom';
import SEO from '../components/seo/SEO';

export default function SitemapPage() {
  const mainPages = [
    { url: '/', title: 'Home' },
    { url: '/find-venues', title: 'Find Venues' },
    { url: '/list-space', title: 'List Your Space' },
    { url: '/how-it-works', title: 'How It Works' },
    { url: '/venue-guide', title: 'Venue Guide' },
    { url: '/party-planning-guide', title: 'Party Planning Guide' },
    { url: '/nsw-party-planning', title: 'NSW Party Planning' },
    { url: '/sales-assistant', title: 'Sales Assistant' },
    { url: '/venue-assistant', title: 'Venue Assistant' },

    { url: '/nsw-noise-guide', title: 'NSW Noise Guide' },
    { url: '/blog', title: 'Blog' },
    { url: '/faq', title: 'FAQ' },
    { url: '/contact', title: 'Contact' },
    { url: '/safety', title: 'Safety' },
    { url: '/help', title: 'Help' },
    { url: '/terms', title: 'Terms & Conditions' },
    { url: '/privacy', title: 'Privacy Policy' },
    { url: '/cookies', title: 'Cookies Policy' },
  ];

  const locationPages = [
    { url: '/locations/sydney-cbd', title: 'Sydney CBD Venues' },
    { url: '/locations/bondi-beach', title: 'Bondi Beach Venues' },
    { url: '/locations/parramatta', title: 'Parramatta Venues' },
    { url: '/locations/manly', title: 'Manly Venues' },
    { url: '/locations/newcastle', title: 'Newcastle Venues' },
    { url: '/locations/newtown', title: 'Newtown Venues' },
    { url: '/locations/surry-hills', title: 'Surry Hills Venues' },
    { url: '/locations/chatswood', title: 'Chatswood Venues' },
    { url: '/locations/cronulla', title: 'Cronulla Venues' },
    { url: '/locations/penrith', title: 'Penrith Venues' },
  ];

  const venuePages = [
    { url: '/venue/venue-001', title: 'Harbour View Rooftop Terrace' },
    { url: '/venue/venue-002', title: 'Bondi Beach House' },
    { url: '/venue/venue-003', title: 'Industrial Warehouse Loft' },
    { url: '/venue/venue-004', title: 'Manly Pavilion' },
    { url: '/venue/venue-005', title: 'Boutique Gallery Space' },
    { url: '/venue/venue-006', title: 'Riverside Function Centre' },
    { url: '/venue/venue-006b', title: 'Parramatta Park Pavilion' },
    { url: '/venue/venue-007', title: 'Garden Terrace Restaurant' },
    { url: '/venue/venue-008', title: 'Beachside Event Space' },
    { url: '/venue/venue-009b', title: 'Westmead Sports Club' },
    { url: '/venue/venue-010', title: 'Mountain View Function Room' },
    { url: '/venue/venue-011', title: 'Newtown Social Club' },
    { url: '/venue/venue-012', title: 'Rooftop Garden Bar' },
  ];

  return (
    <div className="pt-32 px-4 sm:px-6">
      <SEO
        title="Sitemap - All Pages | HouseGoing"
        description="Complete sitemap of all pages on HouseGoing - find venues, locations, guides, and more."
        url="https://housegoing.com.au/sitemap"
      />
      
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Sitemap</h1>
        <p className="text-gray-600 mb-8">
          Find all pages and content available on HouseGoing.
        </p>

        {/* Main Pages */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Main Pages</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mainPages.map((page) => (
              <Link
                key={page.url}
                to={page.url}
                className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all"
              >
                <span className="text-purple-600 hover:text-purple-700 font-medium">
                  {page.title}
                </span>
              </Link>
            ))}
          </div>
        </section>

        {/* Location Pages */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Venue Locations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {locationPages.map((page) => (
              <Link
                key={page.url}
                to={page.url}
                className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all"
              >
                <span className="text-purple-600 hover:text-purple-700 font-medium">
                  {page.title}
                </span>
              </Link>
            ))}
          </div>
        </section>

        {/* Venue Pages */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Featured Venues</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {venuePages.map((page) => (
              <Link
                key={page.url}
                to={page.url}
                className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all"
              >
                <span className="text-purple-600 hover:text-purple-700 font-medium">
                  {page.title}
                </span>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
