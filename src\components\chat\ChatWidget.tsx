import React, { useState, useRef, useEffect } from 'react';
import { Send, X, MessageSquare, Loader, RefreshCw, AlertCircle, Maximize2, Minimize2 } from 'lucide-react';
import { useGitHubAgent } from '../../hooks/useGitHubAgent';
import { useUser } from '@clerk/clerk-react';
import { isAdmin, isMockAdmin } from '../../lib/admin/auth';
import { createChatSession, saveMessage, closeChatSession, requestAdminIntervention, mockChatStorage } from '../../lib/admin/chat-storage';

interface ChatWidgetProps {
  apiKey?: string;
}

export default function ChatWidget({ apiKey }: ChatWidgetProps) {
  const { user } = useUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [message, setMessage] = useState('');
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [needsHelp, setNeedsHelp] = useState(false);
  const [helpReason, setHelpReason] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    messages,
    isLoading,
    error,
    sendMessage,
    resetConversation,
    currentStage
  } = useGitHubAgent(apiKey);

  // Check if user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) return;

      // For development, use the mock admin check
      // In production, use the real admin check
      const userEmail = user.primaryEmailAddress?.emailAddress || '';
      const userId = user.id || '';

      const adminStatus = process.env.NODE_ENV === 'development'
        ? isMockAdmin(userEmail)
        : await isAdmin(userId);

      setIsAdmin(adminStatus);
    };

    checkAdminStatus();
  }, [user]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }

    // Create a new chat session when opened
    if (isOpen && !sessionId) {
      const createSession = async () => {
        try {
          // In production, use the real session creation
          // For development, use the mock storage
          if (process.env.NODE_ENV === 'development') {
            const newSessionId = mockChatStorage.createSession();
            setSessionId(newSessionId);
          } else {
            const userEmail = user?.primaryEmailAddress?.emailAddress || null;
            const userId = user?.id || null;
            const metadata = {
              userAgent: navigator.userAgent,
              referrer: document.referrer,
              url: window.location.href
            };

            const newSessionId = await createChatSession(userId, userEmail, metadata);
            if (newSessionId) {
              setSessionId(newSessionId);
            }
          }
        } catch (error) {
          console.error('Error creating chat session:', error);
        }
      };

      createSession();
    }

    // Close the chat session when closed
    if (!isOpen && sessionId) {
      const closeSession = async () => {
        try {
          // In production, use the real session closing
          // For development, use the mock storage
          if (process.env.NODE_ENV === 'development') {
            mockChatStorage.closeSession(sessionId);
          } else {
            await closeChatSession(sessionId);
          }

          setSessionId(null);
        } catch (error) {
          console.error('Error closing chat session:', error);
        }
      };

      closeSession();
    }
  }, [isOpen, sessionId, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    // Send the message through the AI agent
    await sendMessage(message);

    // Save the user message to the database
    if (sessionId) {
      const userMessage = {
        id: Date.now().toString(),
        role: 'human',
        content: message,
        timestamp: new Date()
      };

      // In production, use the real message saving
      // For development, use the mock storage
      if (process.env.NODE_ENV === 'development') {
        mockChatStorage.saveMessage(sessionId, userMessage);
      } else {
        await saveMessage(sessionId, userMessage);
      }

      // Save the AI response to the database
      // We get the last message from the messages array, which should be the AI response
      const aiMessages = messages.filter(msg => msg.role === 'ai');
      if (aiMessages.length > 0) {
        const lastAiMessage = aiMessages[aiMessages.length - 1];

        if (process.env.NODE_ENV === 'development') {
          mockChatStorage.saveMessage(sessionId, lastAiMessage);
        } else {
          await saveMessage(sessionId, lastAiMessage);
        }
      }
    }

    setMessage('');
  };

  const toggleChat = () => {
    setIsOpen(prev => !prev);
  };

  const toggleMaximize = () => {
    setIsMaximized(prev => !prev);
  };

  const handleReset = () => {
    resetConversation();
    setNeedsHelp(false);
    setHelpReason('');

    // Create a new session when resetting
    if (sessionId) {
      const closeAndCreateSession = async () => {
        try {
          // Close the current session
          if (process.env.NODE_ENV === 'development') {
            mockChatStorage.closeSession(sessionId);
          } else {
            await closeChatSession(sessionId);
          }

          // Create a new session
          if (process.env.NODE_ENV === 'development') {
            const newSessionId = mockChatStorage.createSession();
            setSessionId(newSessionId);
          } else {
            const userEmail = user?.primaryEmailAddress?.emailAddress || null;
            const userId = user?.id || null;
            const metadata = {
              userAgent: navigator.userAgent,
              referrer: document.referrer,
              url: window.location.href
            };

            const newSessionId = await createChatSession(userId, userEmail, metadata);
            if (newSessionId) {
              setSessionId(newSessionId);
            }
          }
        } catch (error) {
          console.error('Error resetting chat session:', error);
        }
      };

      closeAndCreateSession();
    }
  };

  // Handle requesting admin help
  const handleRequestHelp = async () => {
    if (!sessionId || !helpReason.trim()) return;

    try {
      // In production, use the real admin intervention request
      // For development, use the mock storage
      if (process.env.NODE_ENV === 'development') {
        // In development, we just log the request (helpReason)
      } else {
        await requestAdminIntervention(sessionId, helpReason);
      }

      // Add a system message to the chat
      await sendMessage(`[SYSTEM: Admin assistance has been requested. An admin will review this conversation and may join to help.]`);

      setNeedsHelp(false);
      setHelpReason('');
    } catch (error) {
      console.error('Error requesting admin help:', error);
    }
  };

  return (
    <div className={`fixed z-50 ${isMaximized ? 'inset-0' : 'bottom-4 right-4'}`}>
      {/* Chat button - hidden when maximized */}
      {(!isOpen || !isMaximized) && (
        <button
          onClick={toggleChat}
          className="bg-purple-600 text-white p-4 rounded-full shadow-lg hover:bg-purple-700 transition-colors"
          aria-label={isOpen ? "Close chat" : "Open chat"}
        >
          {isOpen ? <X size={24} /> : <MessageSquare size={24} />}
        </button>
      )}

      {/* Chat window */}
      {isOpen && (
        <div className={`fixed z-50 bg-white rounded-lg shadow-xl flex flex-col overflow-hidden border border-gray-200 transition-all duration-300 ${isMaximized
          ? 'inset-4 md:inset-10'
          : 'bottom-16 right-0 w-80 sm:w-96 h-[32rem]'}`}>
          {/* Header */}
          <div className="bg-purple-600 text-white p-4 flex justify-between items-center">
            <div>
              <h3 className="font-medium">HouseGoing Assistant</h3>
              {currentStage && (
                <p className="text-xs text-purple-100">{currentStage.name}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {!isAdmin && (
                <button
                  onClick={() => setNeedsHelp(true)}
                  className="text-white hover:text-purple-200 p-1 rounded"
                  title="Request admin help"
                >
                  <AlertCircle size={16} />
                </button>
              )}
              <button
                onClick={handleReset}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Reset conversation"
              >
                <RefreshCw size={16} />
              </button>
              <button
                onClick={toggleMaximize}
                className="text-white hover:text-purple-200 p-1 rounded"
                title={isMaximized ? "Minimize" : "Maximize"}
                aria-label={isMaximized ? "Minimize chat window" : "Maximize chat window"}
                aria-expanded={isMaximized}
              >
                {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
              </button>
              <button
                onClick={toggleChat}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Close chat"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
            <div className="space-y-4">
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${msg.role === 'human' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] px-4 py-2 rounded-lg ${
                      msg.role === 'human'
                        ? 'bg-purple-200 text-purple-900'
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    <p className="text-sm whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: msg.content }}></p>
                    <p className={`text-xs mt-1 ${msg.role === 'human' ? 'text-purple-700' : 'text-gray-500'}`}>
                      {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-white text-gray-800 border border-gray-200 px-4 py-2 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Loader size={16} className="animate-spin text-purple-600" />
                      <p className="text-sm">Typing...</p>
                    </div>
                  </div>
                </div>
              )}
              {error && (
                <div className="flex justify-center">
                  <div className="bg-red-100 text-red-800 px-4 py-2 rounded-lg text-sm flex items-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <span>{error}</span>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Help Request Form */}
          {needsHelp ? (
            <div className="p-4 border-t border-gray-200 bg-white">
              <h4 className="font-medium text-gray-900 mb-2">Request Admin Assistance</h4>
              <p className="text-sm text-gray-600 mb-3">
                Please describe what you need help with and an admin will review your conversation.
              </p>
              <textarea
                value={helpReason}
                onChange={(e) => setHelpReason(e.target.value)}
                placeholder="Describe your issue..."
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded mb-3"
                rows={3}
              />
              <div className="flex space-x-2">
                <button
                  onClick={() => setNeedsHelp(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRequestHelp}
                  disabled={!helpReason.trim()}
                  className="flex-1 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:bg-purple-400"
                >
                  Submit
                </button>
              </div>
            </div>
          ) : (
            /* Input */
            <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200 bg-white">
              <div className="flex space-x-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 text-white rounded-r-lg hover:bg-purple-700 transition-colors disabled:bg-purple-400"
                  disabled={isLoading || !message.trim()}
                >
                  <Send size={18} />
                </button>
              </div>
            </form>
          )}
        </div>
      )}
    </div>
  );
}
