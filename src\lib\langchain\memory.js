/**
 * <PERSON><PERSON>hain memory management for HouseGoing
 */
import { BufferMemory } from "langchain/memory";
import { ChatMessageHistory } from "langchain/stores/message/in_memory";
import { HumanMessage, AIMessage } from "@langchain/core/messages";

/**
 * Create a buffer memory instance for conversation history
 * @param {Object} options - Configuration options
 * @returns {BufferMemory} - Configured memory instance
 */
export function createBufferMemory(options = {}) {
  const {
    returnMessages = true,
    memoryKey = "chat_history",
    inputKey = "input",
    outputKey = "output",
  } = options;

  return new BufferMemory({
    returnMessages,
    memoryKey,
    inputKey,
    outputKey,
  });
}

/**
 * Create a chat message history from an array of messages
 * @param {Array} messages - Array of message objects with role and content
 * @returns {ChatMessageHistory} - Configured message history
 */
export function createChatHistory(messages = []) {
  const history = new ChatMessageHistory();
  
  // Add each message to the history
  for (const message of messages) {
    if (message.role === 'user' || message.role === 'human') {
      history.addMessage(new HumanMessage(message.content));
    } else if (message.role === 'assistant' || message.role === 'ai') {
      history.addMessage(new AIMessage(message.content));
    }
  }
  
  return history;
}

/**
 * Create a buffer memory with existing chat history
 * @param {Array} messages - Array of message objects with role and content
 * @param {Object} options - Additional memory configuration options
 * @returns {BufferMemory} - Configured memory instance with history
 */
export function createMemoryWithHistory(messages = [], options = {}) {
  const history = createChatHistory(messages);
  
  return new BufferMemory({
    chatHistory: history,
    returnMessages: options.returnMessages || true,
    memoryKey: options.memoryKey || "chat_history",
    inputKey: options.inputKey || "input",
    outputKey: options.outputKey || "output",
  });
}
