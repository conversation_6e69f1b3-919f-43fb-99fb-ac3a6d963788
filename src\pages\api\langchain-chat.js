/**
 * API route for LangChain-powered AI chat assistants
 */

import { generateHostAssistantResponse, generateSalesAssistantResponse, generateVenueAssistantResponse } from '../../lib/langchain';
import { extractVenueSearchCriteria, getFilteredVenues } from '../api/ai-chat';

// Default fallback responses for each agent type
const FALLBACK_RESPONSES = {
  sales: {
    default: "Thanks for your message! I'm <PERSON><PERSON>, your HouseGoing assistant. I can help you find the perfect venue for your event, answer questions about pricing, booking, or venue features. What kind of event are you planning?"
  },
  host: {
    default: "G'day! I'm <PERSON><PERSON>, your HouseGoing Host Assistant. I can help with venue optimization, Party Score calculations, pricing strategies, and more. How can I help you make the most of your venue today?"
  },
  booking: {
    default: "Hello! I'm <PERSON><PERSON>, your HouseGoing Booking Assistant. I can help with checking availability, processing payments, and managing your bookings. How can I assist you today?"
  },
  support: {
    default: "Welcome to HouseGoing Support. I'm <PERSON><PERSON>, and I'm here to help with any issues or questions you have about our platform. How can I assist you today?"
  }
};

// In-memory storage for chat sessions
const chatSessions = {};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, sessionId, agentType = 'sales', context = 'general' } = req.body;

    // Validate required fields
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Generate a session ID if not provided
    const currentSessionId = sessionId || `session_${Date.now()}`;

    // Get or create session
    if (!chatSessions[currentSessionId]) {
      chatSessions[currentSessionId] = {
        history: [],
        agentType,
        context
      };
    }

    const session = chatSessions[currentSessionId];

    // Add user message to history
    session.history.push({ role: 'user', content: message });

    // Generate response based on agent type
    let response;

    try {
      if (agentType === 'host') {
        response = await generateHostAssistantResponse(message, session.history, context);
      } else if (agentType === 'sales') {
        // Extract venue search criteria from message
        const criteria = extractVenueSearchCriteria(message, session.history);
        
        // Get filtered venues based on criteria
        const filteredVenues = getFilteredVenues(criteria);
        
        // Generate response with venue recommendations
        response = await generateSalesAssistantResponse(message, session.history, filteredVenues);
      } else if (agentType === 'venue') {
        // Extract venue search criteria from message
        const criteria = extractVenueSearchCriteria(message, session.history);
        
        // Get filtered venues based on criteria
        const filteredVenues = getFilteredVenues(criteria);
        
        // Generate response with venue recommendations
        response = await generateVenueAssistantResponse(message, session.history, filteredVenues);
      } else {
        // Use fallback response for unsupported agent types
        response = FALLBACK_RESPONSES[agentType]?.default || "I'm sorry, I don't understand that request.";
      }
    } catch (error) {
      console.error(`Error generating ${agentType} response:`, error);
      response = FALLBACK_RESPONSES[agentType]?.default || "I'm sorry, I encountered an error. Please try again.";
    }

    // Add assistant response to history
    session.history.push({ role: 'assistant', content: response });

    // Limit history length to prevent token limits
    if (session.history.length > 20) {
      session.history = session.history.slice(-20);
    }

    return res.status(200).json({
      response,
      sessionId: currentSessionId
    });
  } catch (error) {
    console.error('Error in AI chat API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
