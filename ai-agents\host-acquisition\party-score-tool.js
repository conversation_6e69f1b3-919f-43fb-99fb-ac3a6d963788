/**
 * Party Score Tool for the AI Host Acquisition Agent
 * 
 * This tool allows the agent to calculate a Party Score for venues
 * based on local regulations, zoning, and noise restrictions.
 */

import partyScore from './party-score.js';

/**
 * Party Score Tool
 * Calculates a Party Score for a venue based on its address and features
 */
export const partyScoreTool = {
  name: "PartyScoreCalculator",
  description: "Calculates a Party Score (1-10) for a venue based on address, local regulations, zoning, and noise restrictions. Provides information about curfew times and booking limits.",
  func: async (input) => {
    console.log("Calculating Party Score for:", input);
    
    // Parse input to extract venue details
    const address = extractAddress(input);
    const venueType = extractVenueType(input);
    const isIndoor = input.toLowerCase().includes("indoor");
    const hasNeighbors = extractNeighborInfo(input);
    const soundproofing = extractSoundproofing(input);
    
    // Calculate the Party Score
    const result = await partyScore.calculatePartyScore({
      address,
      venueType,
      isIndoor,
      hasNeighbors,
      soundproofing
    });
    
    // Format the response in a user-friendly way
    return formatPartyScoreResult(result);
  }
};

/**
 * Extract address from input text
 * @param {string} input - User input
 * @returns {string} Extracted address
 */
function extractAddress(input) {
  // In a real implementation, this would use more sophisticated NLP
  // For now, we'll use a simple approach
  
  // Look for address: or location: in the input
  const addressMatch = input.match(/(?:address|location):\s*([^,\.]+(?:,\s*[^,\.]+)*)/i);
  if (addressMatch && addressMatch[1]) {
    return addressMatch[1].trim();
  }
  
  // Look for "in [location]" patterns
  const inLocationMatch = input.match(/\sin\s+([^,\.]+(?:,\s*[^,\.]+)*)/i);
  if (inLocationMatch && inLocationMatch[1]) {
    return inLocationMatch[1].trim();
  }
  
  // Look for common Australian city names
  const cities = ["Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide", 
                 "Gold Coast", "Newcastle", "Canberra", "Wollongong", "Hobart"];
  
  for (const city of cities) {
    if (input.includes(city)) {
      // Extract a phrase around the city name
      const cityMatch = input.match(new RegExp(`([^,\\.]+${city}[^,\\.]+)`, 'i'));
      if (cityMatch && cityMatch[1]) {
        return cityMatch[1].trim();
      }
      return city; // Fall back to just the city name
    }
  }
  
  // If no address found, return a placeholder
  return "Unknown location";
}

/**
 * Extract venue type from input text
 * @param {string} input - User input
 * @returns {string} Venue type
 */
function extractVenueType(input) {
  const input_lower = input.toLowerCase();
  
  if (input_lower.includes("backyard") || input_lower.includes("garden")) {
    return "backyard";
  } else if (input_lower.includes("house") || input_lower.includes("home")) {
    return "house";
  } else if (input_lower.includes("apartment") || input_lower.includes("flat") || input_lower.includes("unit")) {
    return "apartment";
  } else if (input_lower.includes("warehouse") || input_lower.includes("industrial")) {
    return "warehouse";
  } else if (input_lower.includes("rooftop") || input_lower.includes("roof top")) {
    return "rooftop";
  } else if (input_lower.includes("outdoor") || input_lower.includes("outside")) {
    return "outdoor";
  } else {
    return "venue";
  }
}

/**
 * Extract neighbor information from input text
 * @param {string} input - User input
 * @returns {string} Neighbor proximity (close, distant, none)
 */
function extractNeighborInfo(input) {
  const input_lower = input.toLowerCase();
  
  if (input_lower.includes("no neighbors") || 
      input_lower.includes("no neighbours") || 
      input_lower.includes("isolated") || 
      input_lower.includes("secluded")) {
    return "none";
  } else if (input_lower.includes("close neighbors") || 
             input_lower.includes("close neighbours") || 
             input_lower.includes("apartment") || 
             input_lower.includes("dense") || 
             input_lower.includes("suburban")) {
    return "close";
  } else {
    return "distant"; // Default assumption
  }
}

/**
 * Extract soundproofing information from input text
 * @param {string} input - User input
 * @returns {string} Soundproofing quality (excellent, good, none)
 */
function extractSoundproofing(input) {
  const input_lower = input.toLowerCase();
  
  if (input_lower.includes("soundproof") || 
      input_lower.includes("sound proof") || 
      input_lower.includes("excellent insulation") || 
      input_lower.includes("well insulated")) {
    return "excellent";
  } else if (input_lower.includes("good insulation") || 
             input_lower.includes("thick walls") || 
             input_lower.includes("double glazed")) {
    return "good";
  } else {
    return "none"; // Default assumption
  }
}

/**
 * Format the Party Score result in a user-friendly way
 * @param {Object} result - Party Score calculation result
 * @returns {string} Formatted result
 */
function formatPartyScoreResult(result) {
  return `
# 🎉 Party Score Analysis: ${result.partyScore}/10

## Venue Regulations Summary
- **Council Area**: ${result.council}
- **Zoning Type**: ${result.zoning}
- **Noise Restriction Level**: ${result.noiseRestrictions.level}

## Time Limits
- **Weekday Events**: Must end by ${result.timeLimits.weekday}
- **Weekend Events**: Must end by ${result.timeLimits.weekend}
- **Special Events**: ${result.timeLimits.specialEvents}

## Parking Information
${result.parkingInfo}

## Recommendations
${result.recommendations.map(rec => `- ${rec}`).join('\n')}

## Display Implementation Suggestions
1. **Calendar Integration**: Show these time limits directly in your booking calendar
2. **Transparent Disclosure**: Display the Party Score and curfew times prominently on your listing
3. **Guest Education**: Include a brief explanation of local noise regulations in your house rules
4. **Visual Indicators**: Add a "Party-Friendly Score" badge to quickly communicate venue suitability

This analysis is based on local council regulations, zoning laws, and venue characteristics. Actual restrictions may vary, and it's always recommended to check with your local council for specific requirements.
`;
}

export default partyScoreTool;
