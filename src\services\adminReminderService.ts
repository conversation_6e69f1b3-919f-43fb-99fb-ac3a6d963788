/**
 * Admin Reminder Service
 *
 * Handles automated daily reminders for pending property approvals
 */

import { getSupabaseClient } from '../lib/supabase-client';

interface PendingSubmission {
  id: string;
  name: string;
  address: string;
  type: string;
  ownerName: string;
  ownerEmail: string;
  created_at: string;
  daysPending: number;
}

interface ReminderEmailData {
  totalPending: number;
  urgentCount: number; // Pending > 3 days
  submissions: PendingSubmission[];
  adminEmails: string[];
}

// Internal admin emails for reminders
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
];

// Email service configuration
const EMAIL_SERVICE_URL = 'https://housegoing-email-service.onrender.com';

/**
 * Get all pending property submissions
 */
export async function getPendingSubmissions(): Promise<PendingSubmission[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select(`
        id,
        name,
        address,
        type,
        ownerName,
        ownerEmail,
        created_at
      `)
      .eq('status', 'pending')
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching pending submissions:', error);
      return [];
    }

    // Calculate days pending for each submission
    const now = new Date();
    const submissions = data?.map(submission => {
      const createdDate = new Date(submission.created_at);
      const daysPending = Math.floor((now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

      return {
        ...submission,
        daysPending
      };
    }) || [];

    return submissions;
  } catch (error) {
    console.error('Exception getting pending submissions:', error);
    return [];
  }
}

/**
 * Send daily reminder email to admin team
 */
export async function sendDailyReminderEmail(submissions: PendingSubmission[]): Promise<boolean> {
  try {
    if (submissions.length === 0) {
      console.log('No pending submissions - skipping reminder email');
      return true;
    }

    const urgentCount = submissions.filter(s => s.daysPending > 3).length;

    const emailData: ReminderEmailData = {
      totalPending: submissions.length,
      urgentCount,
      submissions,
      adminEmails: ADMIN_EMAILS
    };

    // Send email to each admin
    for (const adminEmail of ADMIN_EMAILS) {
      const response = await fetch(`${EMAIL_SERVICE_URL}/send-admin-reminder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: adminEmail,
          data: emailData
        }),
      });

      if (!response.ok) {
        console.error(`Failed to send reminder email to ${adminEmail}:`, response.statusText);
        continue;
      }

      console.log(`✅ Daily reminder email sent to ${adminEmail}`);
    }

    return true;
  } catch (error) {
    console.error('Error sending daily reminder email:', error);
    return false;
  }
}

/**
 * Check if reminder should be sent today
 * (Only send on weekdays, skip weekends)
 */
export function shouldSendReminderToday(): boolean {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = Sunday, 6 = Saturday

  // Only send reminders Monday (1) through Friday (5)
  return dayOfWeek >= 1 && dayOfWeek <= 5;
}

/**
 * Get reminder email template
 */
export function generateReminderEmailTemplate(data: ReminderEmailData): string {
  const { totalPending, urgentCount, submissions } = data;

  const urgentSubmissions = submissions.filter(s => s.daysPending > 3);
  const recentSubmissions = submissions.filter(s => s.daysPending <= 3);

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>HouseGoing - Daily Approval Reminder</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { flex: 1; background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #667eea; }
        .urgent { border-left-color: #ef4444; }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .urgent .stat-number { color: #ef4444; }
        .stat-label { color: #64748b; font-size: 0.9em; }
        .submission-list { margin: 20px 0; }
        .submission-item { background: #f8fafc; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #667eea; }
        .urgent-item { border-left-color: #ef4444; background: #fef2f2; }
        .submission-name { font-weight: bold; color: #1e293b; }
        .submission-details { color: #64748b; font-size: 0.9em; margin-top: 5px; }
        .days-pending { float: right; background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; }
        .urgent-days { background: #ef4444; }
        .cta-button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { background: #f8fafc; padding: 20px; text-align: center; color: #64748b; font-size: 0.9em; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏠 HouseGoing Admin Reminder</h1>
          <p>Daily Property Approval Summary</p>
        </div>

        <div class="content">
          <h2>Pending Approvals Summary</h2>

          <div class="stats">
            <div class="stat-card">
              <div class="stat-number">${totalPending}</div>
              <div class="stat-label">Total Pending</div>
            </div>
            <div class="stat-card urgent">
              <div class="stat-number">${urgentCount}</div>
              <div class="stat-label">Urgent (>3 days)</div>
            </div>
          </div>

          ${urgentCount > 0 ? `
          <h3 style="color: #ef4444;">🚨 Urgent Submissions (>3 days pending)</h3>
          <div class="submission-list">
            ${urgentSubmissions.map(submission => `
              <div class="submission-item urgent-item">
                <div class="submission-name">${submission.name}</div>
                <div class="submission-details">
                  📍 ${submission.address}<br>
                  🏷️ ${submission.type} | 👤 ${submission.ownerName} (${submission.ownerEmail})<br>
                  📅 Submitted: ${new Date(submission.created_at).toLocaleDateString()}
                </div>
                <span class="days-pending urgent-days">${submission.daysPending} days</span>
              </div>
            `).join('')}
          </div>
          ` : ''}

          ${recentSubmissions.length > 0 ? `
          <h3>📋 Recent Submissions (≤3 days)</h3>
          <div class="submission-list">
            ${recentSubmissions.map(submission => `
              <div class="submission-item">
                <div class="submission-name">${submission.name}</div>
                <div class="submission-details">
                  📍 ${submission.address}<br>
                  🏷️ ${submission.type} | 👤 ${submission.ownerName} (${submission.ownerEmail})<br>
                  📅 Submitted: ${new Date(submission.created_at).toLocaleDateString()}
                </div>
                <span class="days-pending">${submission.daysPending} days</span>
              </div>
            `).join('')}
          </div>
          ` : ''}

          <div style="text-align: center; margin: 30px 0;">
            <a href="https://housegoing.com.au/admin/approval" class="cta-button">
              Review Pending Approvals
            </a>
          </div>

          <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
            <h4 style="margin: 0 0 10px 0; color: #0c4a6e;">💡 Quick Actions</h4>
            <ul style="margin: 0; padding-left: 20px; color: #0c4a6e;">
              <li>Review property details and documentation</li>
              <li>Check identity verification documents</li>
              <li>Approve compliant properties quickly</li>
              <li>Provide detailed feedback for rejections</li>
            </ul>
          </div>
        </div>

        <div class="footer">
          <p>This is an automated daily reminder from HouseGoing Admin System</p>
          <p>📧 Sent to: ${ADMIN_EMAILS.join(', ')}</p>
          <p>🕒 ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Main function to run daily reminder check
 */
export async function runDailyReminderCheck(): Promise<void> {
  try {
    console.log('🔄 Running daily reminder check...');

    // Check if we should send reminder today
    if (!shouldSendReminderToday()) {
      console.log('⏭️ Skipping reminder - weekend or holiday');
      return;
    }

    // Get pending submissions
    const pendingSubmissions = await getPendingSubmissions();

    if (pendingSubmissions.length === 0) {
      console.log('✅ No pending submissions - no reminder needed');
      return;
    }

    // Send reminder email
    const success = await sendDailyReminderEmail(pendingSubmissions);

    if (success) {
      console.log(`✅ Daily reminder sent successfully for ${pendingSubmissions.length} pending submissions`);
    } else {
      console.error('❌ Failed to send daily reminder');
    }

  } catch (error) {
    console.error('Error in daily reminder check:', error);
  }
}

/**
 * Schedule daily reminders (for use in production with cron jobs)
 */
export function scheduleDailyReminders(): void {
  // Run immediately for testing
  runDailyReminderCheck();

  // Schedule to run daily at 9 AM
  const scheduleTime = 9; // 9 AM
  const now = new Date();
  const scheduledTime = new Date();
  scheduledTime.setHours(scheduleTime, 0, 0, 0);

  // If it's already past 9 AM today, schedule for tomorrow
  if (now > scheduledTime) {
    scheduledTime.setDate(scheduledTime.getDate() + 1);
  }

  const timeUntilNext = scheduledTime.getTime() - now.getTime();

  setTimeout(() => {
    runDailyReminderCheck();

    // Then run every 24 hours
    setInterval(runDailyReminderCheck, 24 * 60 * 60 * 1000);
  }, timeUntilNext);

  console.log(`📅 Daily reminders scheduled for ${scheduledTime.toLocaleString()}`);
}
