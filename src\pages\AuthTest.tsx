import React from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';

export default function AuthTest() {
  const { isLoaded, isSignedIn, userId } = useAuth();
  const { user } = useUser();

  return (
    <div className="min-h-screen bg-gray-50 pt-32">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold mb-8">Authentication Test</h1>
          
          <div className="space-y-4">
            <div className="p-4 bg-gray-100 rounded">
              <h2 className="font-semibold mb-2">Clerk Status</h2>
              <div className="space-y-2 text-sm">
                <div>isLoaded: <span className={isLoaded ? 'text-green-600' : 'text-red-600'}>{isLoaded ? 'Yes' : 'No'}</span></div>
                <div>isSignedIn: <span className={isSignedIn ? 'text-green-600' : 'text-red-600'}>{isSignedIn ? 'Yes' : 'No'}</span></div>
                <div>userId: <span className="font-mono">{userId || 'null'}</span></div>
              </div>
            </div>

            {user && (
              <div className="p-4 bg-blue-50 rounded">
                <h2 className="font-semibold mb-2">User Information</h2>
                <div className="space-y-2 text-sm">
                  <div>Email: {user.primaryEmailAddress?.emailAddress}</div>
                  <div>First Name: {user.firstName}</div>
                  <div>Last Name: {user.lastName}</div>
                  <div>Created: {user.createdAt ? new Date(user.createdAt).toLocaleString() : 'N/A'}</div>
                </div>
              </div>
            )}

            <div className="p-4 bg-yellow-50 rounded">
              <h2 className="font-semibold mb-2">Environment</h2>
              <div className="space-y-2 text-sm">
                <div>Mode: {import.meta.env.DEV ? 'Development' : 'Production'}</div>
                <div>URL: {window.location.href}</div>
                <div>Timestamp: {new Date().toLocaleString()}</div>
              </div>
            </div>

            <div className="flex gap-4">
              {!isSignedIn ? (
                <a 
                  href="/login" 
                  className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md"
                >
                  Sign In
                </a>
              ) : (
                <a 
                  href="/my-account" 
                  className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-md"
                >
                  Go to My Account
                </a>
              )}
              <a 
                href="/" 
                className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-md"
              >
                Home
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
