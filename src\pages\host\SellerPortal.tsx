import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, Camera, Building2, Wallet2, FileCheck2, HelpCircle } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../providers/AuthProvider';

type Step = 'photos' | 'personal' | 'documents' | 'payment' | 'review';

interface HostProfile {
  nswId: string;
  address: string;
  bankDetails: {
    accountName: string;
    bsb: string;
    accountNumber: string;
  };
  documents: {
    idProof: File | null;
    addressProof: File | null;
    ownershipProof: File | null;
  };
  photos: File[];
}

export default function SellerPortal() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<Step>('photos');
  const [profile, setProfile] = useState<HostProfile>({
    nswId: '',
    address: '',
    bankDetails: {
      accountName: '',
      bsb: '',
      accountNumber: '',
    },
    documents: {
      idProof: null,
      addressProof: null,
      ownershipProof: null,
    },
    photos: [],
  });

  const steps = [
    { id: 'photos', label: 'Property Photos', icon: Camera },
    { id: 'personal', label: 'Personal Info', icon: Building2 },
    { id: 'documents', label: 'Documents', icon: FileCheck2 },
    { id: 'payment', label: 'Payment Details', icon: Wallet2 },
    { id: 'review', label: 'Review', icon: HelpCircle },
  ];

  const handlePhotoUpload = async (files: FileList) => {
    const newPhotos = Array.from(files);
    setProfile(prev => ({
      ...prev,
      photos: [...prev.photos, ...newPhotos],
    }));
  };

  const handleDocumentUpload = async (type: keyof HostProfile['documents'], file: File) => {
    setProfile(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [type]: file,
      },
    }));
  };

  const handleSubmit = async () => {
    try {
      // Upload photos to storage
      const photoUrls = await Promise.all(
        profile.photos.map(async (photo) => {
          const fileName = `${user?.id}/${Date.now()}-${photo.name}`;
          const { data, error } = await supabase.storage
            .from('venue-photos')
            .upload(fileName, photo);

          if (error) throw error;
          return data.path;
        })
      );

      // Upload documents
      const documentUrls: Record<string, string> = {};
      for (const [key, file] of Object.entries(profile.documents)) {
        if (file) {
          const fileName = `${user?.id}/${Date.now()}-${file.name}`;
          const { data, error } = await supabase.storage
            .from('host-documents')
            .upload(fileName, file);

          if (error) throw error;
          documentUrls[key] = data.path;
        }
      }

      // Create host profile
      const { error } = await supabase
        .from('profiles')
        .update({
          is_host: true,
          nsw_id: profile.nswId,
          address: profile.address,
          bank_details: profile.bankDetails,
          document_urls: documentUrls,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user?.id);

      if (error) throw error;

      navigate('/host/dashboard');
    } catch (error) {
      console.error('Error submitting host profile:', error);
    }
  };

  return (
    <div className="pt-32 px-4 sm:px-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Become a Host</h1>
          <p className="mt-2 text-gray-600">
            Complete your profile to start hosting on HouseGoing
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = steps.findIndex(s => s.id === currentStep) > index;

              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div
                    className={`
                      w-12 h-12 rounded-full flex items-center justify-center
                      ${isActive ? 'bg-purple-600 text-white' :
                        isCompleted ? 'bg-green-500 text-white' :
                        'bg-gray-100 text-gray-500'}
                    `}
                  >
                    <Icon className="h-6 w-6" />
                  </div>
                  <span className="mt-2 text-sm text-gray-600">{step.label}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-xl shadow-md p-6">
          {currentStep === 'photos' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-4">Upload Property Photos</h2>
                <p className="text-gray-600 mb-4">
                  High-quality photos help attract more bookings. Show your space at its best!
                </p>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium mb-2">Must-have shots:</h3>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Exterior & entrance</li>
                      <li>• Main living areas</li>
                      <li>• All bedrooms</li>
                      <li>• Bathrooms</li>
                      <li>• Special features</li>
                    </ul>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium mb-2">Photo tips:</h3>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Use natural light</li>
                      <li>• Keep spaces tidy</li>
                      <li>• Show multiple angles</li>
                      <li>• Highlight unique features</li>
                      <li>• Minimum 1920x1080px</li>
                    </ul>
                  </div>
                </div>

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => e.target.files && handlePhotoUpload(e.target.files)}
                    id="photo-upload"
                  />
                  <label
                    htmlFor="photo-upload"
                    className="cursor-pointer flex flex-col items-center"
                  >
                    <Upload className="h-12 w-12 text-gray-400 mb-4" />
                    <span className="text-gray-600">
                      Drag and drop photos here, or click to browse
                    </span>
                    <span className="text-sm text-gray-500 mt-2">
                      Supported formats: JPG, PNG (max 10MB each)
                    </span>
                  </label>
                </div>

                {profile.photos.length > 0 && (
                  <div className="mt-6">
                    <h3 className="font-medium mb-4">Uploaded Photos</h3>
                    <div className="grid grid-cols-4 gap-4">
                      {profile.photos.map((photo, index) => (
                        <div key={index} className="relative aspect-square rounded-lg overflow-hidden">
                          <img
                            src={URL.createObjectURL(photo)}
                            alt={`Property photo ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 'personal' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold mb-4">Personal Information</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NSW ID Number
                  </label>
                  <input
                    type="text"
                    value={profile.nswId}
                    onChange={(e) => setProfile(prev => ({ ...prev, nswId: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter your NSW ID number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Address
                  </label>
                  <textarea
                    value={profile.address}
                    onChange={(e) => setProfile(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={3}
                    placeholder="Enter your full residential address"
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 'documents' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold mb-4">Required Documents</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="font-medium mb-2">ID Proof</h3>
                  <input
                    type="file"
                    accept=".pdf,image/*"
                    onChange={(e) => e.target.files?.[0] && handleDocumentUpload('idProof', e.target.files[0])}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-full file:border-0
                      file:text-sm file:font-semibold
                      file:bg-purple-50 file:text-purple-700
                      hover:file:bg-purple-100"
                  />
                </div>
                <div>
                  <h3 className="font-medium mb-2">Proof of Address</h3>
                  <input
                    type="file"
                    accept=".pdf,image/*"
                    onChange={(e) => e.target.files?.[0] && handleDocumentUpload('addressProof', e.target.files[0])}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-full file:border-0
                      file:text-sm file:font-semibold
                      file:bg-purple-50 file:text-purple-700
                      hover:file:bg-purple-100"
                  />
                </div>
                <div>
                  <h3 className="font-medium mb-2">Proof of Ownership</h3>
                  <input
                    type="file"
                    accept=".pdf,image/*"
                    onChange={(e) => e.target.files?.[0] && handleDocumentUpload('ownershipProof', e.target.files[0])}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-full file:border-0
                      file:text-sm file:font-semibold
                      file:bg-purple-50 file:text-purple-700
                      hover:file:bg-purple-100"
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 'payment' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold mb-4">Payment Information</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Account Name
                  </label>
                  <input
                    type="text"
                    value={profile.bankDetails.accountName}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      bankDetails: { ...prev.bankDetails, accountName: e.target.value }
                    }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter account holder name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    BSB
                  </label>
                  <input
                    type="text"
                    value={profile.bankDetails.bsb}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      bankDetails: { ...prev.bankDetails, bsb: e.target.value }
                    }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter BSB number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Account Number
                  </label>
                  <input
                    type="text"
                    value={profile.bankDetails.accountNumber}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      bankDetails: { ...prev.bankDetails, accountNumber: e.target.value }
                    }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter account number"
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 'review' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold mb-4">Review Your Information</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="font-medium mb-2">Property Photos</h3>
                  <div className="grid grid-cols-6 gap-2">
                    {profile.photos.map((photo, index) => (
                      <div key={index} className="aspect-square rounded-lg overflow-hidden">
                        <img
                          src={URL.createObjectURL(photo)}
                          alt={`Property photo ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Personal Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p><strong>NSW ID:</strong> {profile.nswId}</p>
                    <p><strong>Address:</strong> {profile.address}</p>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Documents</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p>
                      <strong>ID Proof:</strong>{' '}
                      {profile.documents.idProof?.name || 'Not uploaded'}
                    </p>
                    <p>
                      <strong>Address Proof:</strong>{' '}
                      {profile.documents.addressProof?.name || 'Not uploaded'}
                    </p>
                    <p>
                      <strong>Ownership Proof:</strong>{' '}
                      {profile.documents.ownershipProof?.name || 'Not uploaded'}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Payment Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p>
                      <strong>Account Name:</strong> {profile.bankDetails.accountName}
                    </p>
                    <p>
                      <strong>BSB:</strong> {profile.bankDetails.bsb}
                    </p>
                    <p>
                      <strong>Account Number:</strong>{' '}
                      {profile.bankDetails.accountNumber.replace(/\d(?=\d{4})/g, '*')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="mt-8 flex justify-between">
            <button
              onClick={() => {
                const currentIndex = steps.findIndex(step => step.id === currentStep);
                if (currentIndex > 0) {
                  setCurrentStep(steps[currentIndex - 1].id as Step);
                }
              }}
              className="px-6 py-2 rounded-full border border-gray-300 hover:border-purple-600 transition-colors"
              disabled={currentStep === steps[0].id}
            >
              Previous
            </button>

            {currentStep === steps[steps.length - 1].id ? (
              <button
                onClick={handleSubmit}
                className="px-6 py-2 rounded-full bg-purple-600 text-white hover:bg-purple-700 transition-colors"
              >
                Submit
              </button>
            ) : (
              <button
                onClick={() => {
                  const currentIndex = steps.findIndex(step => step.id === currentStep);
                  if (currentIndex < steps.length - 1) {
                    setCurrentStep(steps[currentIndex + 1].id as Step);
                  }
                }}
                className="px-6 py-2 rounded-full bg-purple-600 text-white hover:bg-purple-700 transition-colors"
              >
                Next
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}