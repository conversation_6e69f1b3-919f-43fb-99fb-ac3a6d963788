[build]
  command = "npm run build:prod"
  publish = "dist"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "20.12.2"
  SECRETS_SCAN_OMIT_PATHS = "docs/,*.md,.env.example,scripts/,public/,PRODUCTION_*.md,clerk-supabase-setup.md,supabase/README.md,.env,.env.*"
  SECRETS_SCAN_OMIT_KEYS = "VITE_SUPABASE_URL,VITE_SUPABASE_ANON_KEY,NEXT_PUBLIC_SUPABASE_URL,NEXT_PUBLIC_SUPABASE_ANON_KEY,VITE_STRIPE_PUBLISHABLE_KEY,VITE_CLERK_PUBLISHABLE_KEY,CLERK_SECRET_KEY"

[functions]
  node_bundler = "esbuild"

# Handle specific static pages first
[[redirects]]
  from = "/how-it-works"
  to = "/how-it-works.html"
  status = 200

[[redirects]]
  from = "/find-venues"
  to = "/find-venues.html"
  status = 200

[[redirects]]
  from = "/list-space"
  to = "/list-space.html"
  status = 200

[[redirects]]
  from = "/sign-up"
  to = "/sign-up.html"
  status = 200

[[redirects]]
  from = "/sign-in"
  to = "/sign-in.html"
  status = 200

[[redirects]]
  from = "/housegoing-brand"
  to = "/housegoing-brand.html"
  status = 200

[[redirects]]
  from = "/housegoing-faq"
  to = "/housegoing-faq.html"
  status = 200

[[redirects]]
  from = "/safety"
  to = "/safety-guidelines.md"
  status = 200

# CRITICAL FIX: Don't redirect /about-housegoing to index.html
# This was preventing proper indexing of the about page
# Instead, let the SPA router handle it

# Handle client-side routing for React app without spa=true
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# NSW Party Planning Tool redirects
[[redirects]]
  from = "/nsw-curfew-zoning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning-updated"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-address-v2"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-address"
  to = "/index.html"
  status = 200
  force = true

# API redirects for Stripe functions
[[redirects]]
  from = "/api/stripe/payment-intent"
  to = "/.netlify/functions/create-payment-intent"
  status = 200

[[redirects]]
  from = "/api/stripe/create-customer"
  to = "/.netlify/functions/create-customer"
  status = 200

[[redirects]]
  from = "/api/stripe/webhook"
  to = "/.netlify/functions/stripe-webhook"
  status = 200

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Headers for API endpoints
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type, Authorization, Stripe-Signature"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
