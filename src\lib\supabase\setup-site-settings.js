/**
 * Setup script for site settings table in Supabase
 */
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupSiteSettingsTable() {
  try {
    console.log('Setting up site settings table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'src', 'lib', 'supabase', 'site-settings-table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      throw error;
    }
    
    console.log('Site settings table created successfully!');
    
    // Verify the settings were created
    const { data, error: fetchError } = await supabase
      .from('site_settings')
      .select('*')
      .single();
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log('Site settings initialized:', data);
    console.log('Setup complete!');
  } catch (error) {
    console.error('Error setting up site settings table:', error);
  }
}

// Run the setup
setupSiteSettingsTable();
