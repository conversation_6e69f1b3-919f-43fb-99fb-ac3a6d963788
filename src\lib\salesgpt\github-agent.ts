import OpenAI from "openai";
import {
  SALES_AGENT_SYSTEM_PROMPT,
  SALES_AGENT_TOOLS_PROMPT,
  SALES_CONVERSATION_STAGES,
  SALES_AGENT_INCEPTION_PROMPT,
  MOCK_PROPERTIES
} from "./config";
import { HOUSEGOING_KNOWLEDGE_BASE } from "./knowledge-base";

// Define the conversation stage type
export type ConversationStage = {
  name: string;
  description: string;
};

// Define the message type
export type Message = {
  id: string;
  role: "human" | "ai";
  content: string;
  timestamp: Date;
};

// Define the GitHub Marketplace agent class
export class GitHubMarketplaceAgent {
  private client: OpenAI;
  private conversationHistory: Array<{ role: string; content: string }> = [];
  private currentStage: ConversationStage;
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;

    // Initialize the OpenAI client with GitHub Marketplace settings
    this.client = new OpenAI({
      baseURL: "https://models.inference.ai.azure.com",
      apiKey: apiKey,
      dangerouslyAllowBrowser: true // Allow usage in browser environment
    });

    // Initialize with system prompts and knowledge base
    const knowledgeBasePrompt = `
\n\nHOUSEGOING KNOWLEDGE BASE:\n\n
${JSON.stringify(HOUSEGOING_KNOWLEDGE_BASE, null, 2)}
`;

    this.conversationHistory.push({
      role: "system",
      content: SALES_AGENT_SYSTEM_PROMPT + "\n\n" + SALES_AGENT_TOOLS_PROMPT + knowledgeBasePrompt
    });

    // Set initial conversation stage
    this.currentStage = SALES_CONVERSATION_STAGES[0];

    // Add stage information to the conversation
    const stagePrompt = `Current conversation stage: ${this.currentStage.name} - ${this.currentStage.description}`;
    this.conversationHistory.push({ role: "system", content: stagePrompt });

    // Add inception prompt as the first AI message
    this.conversationHistory.push({ role: "assistant", content: SALES_AGENT_INCEPTION_PROMPT });
  }

  // Method to get the current conversation stage
  public getCurrentStage(): ConversationStage {
    return this.currentStage;
  }

  // Method to update the conversation stage
  private async updateStage(stageName: string): Promise<void> {
    const newStage = SALES_CONVERSATION_STAGES.find(stage => stage.name === stageName);
    if (newStage) {
      this.currentStage = newStage;
      const stagePrompt = `Current conversation stage: ${this.currentStage.name} - ${this.currentStage.description}`;
      this.conversationHistory.push({ role: "system", content: stagePrompt });
    }
  }

  // Method to determine the next conversation stage based on the conversation history
  private async determineNextStage(): Promise<string> {
    const stageAnalysisPrompt = `
    Based on the conversation so far, what should be the next conversation stage?
    Current stage: ${this.currentStage.name}

    Available stages:
    ${SALES_CONVERSATION_STAGES.map(stage => `- ${stage.name}: ${stage.description}`).join('\n')}

    Respond with just the name of the next appropriate stage.
    `;

    try {
      const stageAnalysisMessages = [
        ...this.conversationHistory,
        { role: "user", content: stageAnalysisPrompt }
      ];

      const response = await this.client.chat.completions.create({
        messages: stageAnalysisMessages,
        model: "gpt-4o",
        temperature: 0.1,  // Lower temperature for more deterministic responses
        max_tokens: 20,    // Reduced max tokens for faster responses
        top_p: 1,
        response_format: { type: "text" }  // Optimize for text responses
      });

      return response.choices[0].message.content?.trim() || this.currentStage.name;
    } catch (error) {
      console.error("Error determining next stage:", error);
      return this.currentStage.name;
    }
  }

  // Method to process user input and generate a response
  public async processMessage(userMessage: string): Promise<string> {
    try {
      // Add user message to conversation history
      this.conversationHistory.push({ role: "user", content: userMessage });

      // Generate AI response with optimized settings for faster responses
      const response = await this.client.chat.completions.create({
        messages: this.conversationHistory,
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 800,  // Reduced max tokens for faster responses
        top_p: 1,
        presence_penalty: 0.1,  // Slight penalty to avoid repetition
        frequency_penalty: 0.1,  // Slight penalty to encourage diversity
        response_format: { type: "text" }  // Optimize for text responses
      });

      let aiResponse = response.choices[0].message.content || "I'm sorry, I couldn't generate a response.";

      // Format the response to use proper markdown
      // Replace **text** with <strong>text</strong> for proper bold formatting
      aiResponse = aiResponse.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

      // Replace ### headings with proper formatting
      aiResponse = aiResponse.replace(/###\s+(.*?)\n/g, '<h3>$1</h3>\n');

      // Replace ## headings with proper formatting
      aiResponse = aiResponse.replace(/##\s+(.*?)\n/g, '<h2>$1</h2>\n');

      // Add AI response to conversation history
      this.conversationHistory.push({ role: "assistant", content: aiResponse });

      // Determine and update the next conversation stage
      const nextStage = await this.determineNextStage();
      if (nextStage !== this.currentStage.name) {
        await this.updateStage(nextStage);
      }

      return aiResponse;
    } catch (error) {
      console.error("Error processing message:", error);
      return "I'm sorry, I encountered an error processing your message. Please try again.";
    }
  }

  // Method to search for properties based on criteria
  public async searchProperties(criteria: {
    location?: string;
    minCapacity?: number;
    maxPrice?: number;
    amenities?: string[];
    eventType?: string;
  }): Promise<any[]> {
    // In a real implementation, this would query a database
    // For now, we'll filter the mock properties

    let results = [...MOCK_PROPERTIES];

    if (criteria.location) {
      results = results.filter(property =>
        property.location.toLowerCase().includes(criteria.location!.toLowerCase())
      );
    }

    if (criteria.minCapacity) {
      results = results.filter(property => property.capacity >= criteria.minCapacity!);
    }

    if (criteria.maxPrice) {
      results = results.filter(property => property.price <= criteria.maxPrice!);
    }

    if (criteria.amenities && criteria.amenities.length > 0) {
      results = results.filter(property =>
        criteria.amenities!.some(amenity =>
          property.amenities.some(a => a.toLowerCase().includes(amenity.toLowerCase()))
        )
      );
    }

    if (criteria.eventType) {
      results = results.filter(property =>
        property.eventTypes.some(type =>
          type.toLowerCase().includes(criteria.eventType!.toLowerCase())
        )
      );
    }

    return results;
  }

  // Method to get property details
  public async getPropertyDetails(propertyId: string): Promise<any> {
    // In a real implementation, this would query a database
    const property = MOCK_PROPERTIES.find(p => p.id === propertyId);
    return property || null;
  }

  // Method to check property availability
  public async checkAvailability(params: {
    propertyId: string;
    date: string;
    startTime: string;
    endTime: string;
  }): Promise<{ available: boolean; conflicts?: string[] }> {
    // In a real implementation, this would check a booking database
    // For now, we'll return mock data
    const property = MOCK_PROPERTIES.find(p => p.id === params.propertyId);

    if (!property) {
      return { available: false, conflicts: ["Property not found"] };
    }

    // Mock availability check - 80% chance of being available
    const isAvailable = Math.random() > 0.2;

    return {
      available: isAvailable,
      conflicts: isAvailable ? [] : ["Time slot already booked"]
    };
  }

  // Method to schedule a viewing
  public async scheduleViewing(params: {
    propertyId: string;
    date: string;
    time: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
  }): Promise<{ success: boolean; bookingId?: string; message: string }> {
    // In a real implementation, this would create a booking in the database
    // For now, we'll return mock data
    const property = MOCK_PROPERTIES.find(p => p.id === params.propertyId);

    if (!property) {
      return {
        success: false,
        message: "Property not found"
      };
    }

    // Generate a mock booking ID
    const bookingId = `VIEW-${Date.now().toString(36).toUpperCase()}`;

    return {
      success: true,
      bookingId,
      message: `Viewing scheduled for ${property.title} on ${params.date} at ${params.time}`
    };
  }

  // Method to create a lead
  public async createLead(params: {
    name: string;
    email: string;
    phone?: string;
    interests?: string[];
    notes?: string;
  }): Promise<{ success: boolean; leadId?: string; message: string }> {
    // In a real implementation, this would create a lead in the CRM
    // For now, we'll return mock data

    // Generate a mock lead ID
    const leadId = `LEAD-${Date.now().toString(36).toUpperCase()}`;

    return {
      success: true,
      leadId,
      message: `Lead created for ${params.name} (${params.email})`
    };
  }

  // Method to reset the conversation
  public resetConversation(): void {
    this.conversationHistory = [];

    // Initialize with system prompts and knowledge base
    const knowledgeBasePrompt = `
\n\nHOUSEGOING KNOWLEDGE BASE:\n\n
${JSON.stringify(HOUSEGOING_KNOWLEDGE_BASE, null, 2)}
`;

    this.conversationHistory.push({
      role: "system",
      content: SALES_AGENT_SYSTEM_PROMPT + "\n\n" + SALES_AGENT_TOOLS_PROMPT + knowledgeBasePrompt
    });
    this.currentStage = SALES_CONVERSATION_STAGES[0];
    const stagePrompt = `Current conversation stage: ${this.currentStage.name} - ${this.currentStage.description}`;
    this.conversationHistory.push({ role: "system", content: stagePrompt });
    this.conversationHistory.push({ role: "assistant", content: SALES_AGENT_INCEPTION_PROMPT });
  }
}
