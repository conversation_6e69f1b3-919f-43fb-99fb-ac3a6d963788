/**
 * Authentication middleware for the Host Acquisition Agent
 * Ensures that only authenticated hosts can access the agent
 */

// Mock user database for demonstration purposes
// In a real implementation, this would connect to your actual user database
const mockUserDatabase = {
  "host123": {
    id: "host123",
    email: "<EMAIL>",
    name: "John Host",
    role: "host",
    venueId: "venue456"
  },
  "user456": {
    id: "user456",
    email: "<EMAIL>",
    name: "<PERSON> User",
    role: "guest"
  },
  "admin789": {
    id: "admin789",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin"
  }
};

/**
 * Verifies if a user is authenticated and has host privileges
 * @param {string} userId - The ID of the user to authenticate
 * @param {string} apiKey - API key for authentication
 * @returns {Object} Authentication result with user data if successful
 */
export async function authenticateHost(userId, apiKey) {
  // In a real implementation, this would verify the API key and user ID
  // against your authentication system
  
  // For demonstration, we'll use the mock database
  const user = mockUserDatabase[userId];
  
  if (!user) {
    return {
      authenticated: false,
      error: "User not found"
    };
  }
  
  // Check if user is a host or admin
  if (user.role !== "host" && user.role !== "admin") {
    return {
      authenticated: false,
      error: "User does not have host privileges"
    };
  }
  
  return {
    authenticated: true,
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
    }
  };
}

/**
 * Express middleware for host authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export function hostAuthMiddleware(req, res, next) {
  const userId = req.headers['x-user-id'];
  const apiKey = req.headers['x-api-key'];
  
  if (!userId || !apiKey) {
    return res.status(401).json({
      error: "Authentication required"
    });
  }
  
  authenticateHost(userId, apiKey)
    .then(result => {
      if (!result.authenticated) {
        return res.status(403).json({
          error: result.error || "Access denied"
        });
      }
      
      // Attach user to request object
      req.user = result.user;
      next();
    })
    .catch(error => {
      console.error("Authentication error:", error);
      res.status(500).json({
        error: "Authentication failed"
      });
    });
}

export default {
  authenticateHost,
  hostAuthMiddleware
};
