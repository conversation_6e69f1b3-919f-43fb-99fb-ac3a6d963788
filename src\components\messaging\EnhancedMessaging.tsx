/**
 * Enhanced Messaging Component
 *
 * Provides a complete two-way messaging interface with:
 * - Real-time messaging
 * - Booking-specific conversations
 * - Email notifications
 * - Message status tracking
 * - Mobile-responsive design
 */

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../providers/AuthProvider';
import {
  getConversationMessages,
  sendMessage,
  markConversationAsRead,
  getConversations
} from '../../api/userAccount';
import { getSupabaseClient } from '../../services/api';
import {
  Send,
  ArrowLeft,
  Search,
  MessageCircle,
  Clock,
  CheckCircle2,
  User,
  Calendar,
  MapPin
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface Message {
  id: string;
  sender_id: string;
  receiver_id: string;
  booking_id?: string;
  content: string;
  created_at: string;
  read: boolean;
  sender_name: string;
  receiver_name: string;
}

interface Conversation {
  otherUser: {
    id: string;
    name: string;
    email: string;
  };
  booking?: {
    id: string;
    venue_id: string;
    start_date: string;
    end_date: string;
    status: string;
  };
  messages: Message[];
  lastMessage: Message;
  unreadCount: number;
}

interface EnhancedMessagingProps {
  bookingId?: string;
  otherUserId?: string;
  venueName?: string;
  className?: string;
}

export default function EnhancedMessaging({
  bookingId,
  otherUserId,
  venueName,
  className = ''
}: EnhancedMessagingProps) {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showConversationList, setShowConversationList] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load conversations
  useEffect(() => {
    if (!user) return;

    const loadConversations = async () => {
      try {
        setLoading(true);
        const conversationData = await getConversations(user.id);
        setConversations(conversationData);

        // Auto-select conversation if bookingId or otherUserId provided
        if (bookingId || otherUserId) {
          const targetConversation = conversationData.find(conv =>
            (bookingId && conv.booking?.id === bookingId) ||
            (otherUserId && conv.otherUser.id === otherUserId)
          );
          if (targetConversation) {
            setSelectedConversation(targetConversation);
            setShowConversationList(false);
          }
        }
      } catch (error) {
        console.error('Error loading conversations:', error);
      } finally {
        setLoading(false);
      }
    };

    loadConversations();
  }, [user, bookingId, otherUserId]);

  // Load messages for selected conversation
  useEffect(() => {
    if (!selectedConversation || !user) return;

    const loadMessages = async () => {
      try {
        const messageData = await getConversationMessages(
          user.id,
          selectedConversation.otherUser.id,
          selectedConversation.booking?.id
        );
        setMessages(messageData);

        // Mark conversation as read
        await markConversationAsRead(
          user.id,
          selectedConversation.otherUser.id,
          selectedConversation.booking?.id
        );
      } catch (error) {
        console.error('Error loading messages:', error);
      }
    };

    loadMessages();
  }, [selectedConversation, user]);

  // Real-time message subscription
  useEffect(() => {
    if (!user) return;

    const supabase = getSupabaseClient();
    const subscription = supabase
      .channel('messages-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `or(sender_id.eq.${user.id},receiver_id.eq.${user.id})`
        },
        (payload) => {
          const newMessage = payload.new as any;

          // Add to messages if it's for the current conversation
          if (selectedConversation &&
              ((newMessage.sender_id === selectedConversation.otherUser.id && newMessage.receiver_id === user.id) ||
               (newMessage.sender_id === user.id && newMessage.receiver_id === selectedConversation.otherUser.id))) {
            setMessages(prev => [...prev, newMessage]);
          }

          // Update conversations list
          setConversations(prev => {
            // Update existing conversation or add new one
            const updated = [...prev];
            const existingIndex = updated.findIndex(conv =>
              conv.otherUser.id === (newMessage.sender_id === user.id ? newMessage.receiver_id : newMessage.sender_id)
            );

            if (existingIndex >= 0) {
              updated[existingIndex].lastMessage = newMessage;
              if (newMessage.receiver_id === user.id) {
                updated[existingIndex].unreadCount++;
              }
            }

            return updated.sort((a, b) =>
              new Date(b.lastMessage.created_at).getTime() - new Date(a.lastMessage.created_at).getTime()
            );
          });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user, selectedConversation]);

  // Send message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !user || sending) return;

    try {
      setSending(true);

      const messageData = await sendMessage(
        user.id,
        selectedConversation.otherUser.id,
        newMessage.trim(),
        selectedConversation.booking?.id,
        `${user.first_name || ''} ${user.last_name || ''}`.trim(),
        venueName
      );

      setMessages(prev => [...prev, messageData]);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  // Filter conversations based on search
  const filteredConversations = conversations.filter(conv =>
    conv.otherUser.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.lastMessage.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`}>
      <div className="flex h-[600px]">
        {/* Conversations List */}
        {showConversationList && (
          <div className="w-full md:w-1/3 border-r border-gray-200 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Messages</h3>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search conversations..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>
            </div>

            {/* Conversations */}
            <div className="flex-1 overflow-y-auto">
              {filteredConversations.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p>No conversations yet</p>
                </div>
              ) : (
                filteredConversations.map((conversation, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSelectedConversation(conversation);
                      setShowConversationList(false);
                    }}
                    className={`w-full p-4 text-left border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                      selectedConversation === conversation ? 'bg-purple-50 border-l-4 border-l-purple-600' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="w-5 h-5 text-purple-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {conversation.otherUser.name}
                          </p>
                          <div className="flex items-center space-x-2">
                            {conversation.unreadCount > 0 && (
                              <span className="bg-purple-600 text-white text-xs rounded-full px-2 py-1">
                                {conversation.unreadCount}
                              </span>
                            )}
                            <p className="text-xs text-gray-500">
                              {formatDistanceToNow(new Date(conversation.lastMessage.created_at), { addSuffix: true })}
                            </p>
                          </div>
                        </div>

                        {conversation.booking && (
                          <div className="flex items-center text-xs text-gray-500 mb-1">
                            <Calendar className="w-3 h-3 mr-1" />
                            Booking #{conversation.booking.id.slice(0, 8)}
                          </div>
                        )}

                        <p className="text-sm text-gray-600 truncate">
                          {conversation.lastMessage.sender_id === user?.id ? 'You: ' : ''}
                          {conversation.lastMessage.content}
                        </p>
                      </div>
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>
        )}

        {/* Message View */}
        {selectedConversation ? (
          <div className="flex-1 flex flex-col">
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setShowConversationList(true)}
                    className="md:hidden text-gray-600 hover:text-gray-800"
                  >
                    <ArrowLeft className="w-5 h-5" />
                  </button>
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{selectedConversation.otherUser.name}</h4>
                    {selectedConversation.booking && venueName && (
                      <p className="text-sm text-gray-600 flex items-center">
                        <MapPin className="w-3 h-3 mr-1" />
                        {venueName}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => {
                const isCurrentUser = message.sender_id === user?.id;
                return (
                  <div
                    key={message.id}
                    className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        isCurrentUser
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="whitespace-pre-wrap break-words">{message.content}</p>
                      <div className={`flex items-center justify-end mt-2 text-xs ${
                        isCurrentUser ? 'text-purple-200' : 'text-gray-500'
                      }`}>
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                        {isCurrentUser && message.read && (
                          <CheckCircle2 className="w-3 h-3 ml-1" />
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Type your message..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  disabled={sending}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim() || sending}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
              <p className="text-gray-600">Choose a conversation from the list to start messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
