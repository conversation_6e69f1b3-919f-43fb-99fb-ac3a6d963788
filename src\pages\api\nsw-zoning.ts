// API handler for NSW zoning information
// This is a simple API endpoint that returns zoning and curfew information for a given address

// @ts-nocheck
// Disable TypeScript checking for this file to avoid issues with API types
import { getCurfewInfo } from '../../lib/nsw-party-planning/curfew-api'
import { parseNSWAddress } from '../../utils/addressUtils'
import { loadGeoJSON, findZoneForPoint, findLGAForPoint } from '../../utils/spatialUtils'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { address, lat, lng } = req.body

    const [zoningData, lgaData] = await Promise.all([
      loadGeoJSON('/data/nswZoningData.json'),
      loadGeoJSON('/data/lga.geojson')
    ])

    const point = { type: 'Point' as const, coordinates: [parseFloat(lng), parseFloat(lat)] }
    const zoneCode = findZoneForPoint(point, zoningData) || 'R2'
    const lgaName = findLGAForPoint(point, lgaData) || 'Unknown'
    const isApartment = address && /^\d+\//.test(address)

    const curfewInfo = await getCurfewInfo({
      address: parseNSWAddress(address),
      propertyType: isApartment ? 'Apartment/Unit' : null,
      zoneCode,
      lgaName
    })

    res.json({
      address,
      coordinates: { lat, lng },
      zoning: zoneCode,
      lga: lgaName,
      curfew: {
        start: curfewInfo.curfew_start,
        end: curfewInfo.curfew_end,
        formatted: `${formatTime(curfewInfo.curfew_start)} to ${formatTime(curfewInfo.curfew_end)}`
      },
      rules: curfewInfo.rules
    })
  } catch (error) {
    console.error('NSW Zoning API error:', error)
    res.status(500).json({ error: 'Server error' })
  }
}

function formatTime(timeStr: string): string {
  const [hours, minutes] = timeStr.split(':')
  const hourNum = parseInt(hours, 10)
  const period = hourNum >= 12 ? 'PM' : 'AM'
  const displayHour = hourNum % 12 || 12
  return `${displayHour}:${minutes} ${period}`
}
