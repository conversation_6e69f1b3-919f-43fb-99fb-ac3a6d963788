-- Create table for AI feedback
CREATE TABLE IF NOT EXISTS ai_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id TEXT NOT NULL,
  conversation_id TEXT NOT NULL,
  user_message TEXT,
  assistant_response TEXT NOT NULL,
  rating TEXT NOT NULL CHECK (rating IN ('good', 'bad')),
  notes TEXT,
  agent_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES auth.users(id)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_ai_feedback_agent_type ON ai_feedback(agent_type);
CREATE INDEX IF NOT EXISTS idx_ai_feedback_rating ON ai_feedback(rating);
CREATE INDEX IF NOT EXISTS idx_ai_feedback_conversation_id ON ai_feedback(conversation_id);

-- Create table for AI prompts
CREATE TABLE IF NOT EXISTS ai_prompts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  agent_type TEXT NOT NULL UNIQUE,
  prompt_template TEXT NOT NULL,
  feedback_notes TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for ai_feedback
ALTER TABLE ai_feedback ENABLE ROW LEVEL SECURITY;

-- Only admins can see all feedback
CREATE POLICY "Admins can see all feedback" ON ai_feedback
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM admin_users
    )
  );

-- Users can only see their own feedback
CREATE POLICY "Users can see their own feedback" ON ai_feedback
  FOR SELECT USING (
    auth.uid() = user_id
  );

-- Only admins can insert feedback
CREATE POLICY "Admins can insert feedback" ON ai_feedback
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM admin_users
    )
  );

-- Users can insert their own feedback
CREATE POLICY "Users can insert their own feedback" ON ai_feedback
  FOR INSERT WITH CHECK (
    auth.uid() = user_id
  );

-- Create RLS policies for ai_prompts
ALTER TABLE ai_prompts ENABLE ROW LEVEL SECURITY;

-- Anyone can read prompts
CREATE POLICY "Anyone can read prompts" ON ai_prompts
  FOR SELECT USING (true);

-- Only admins can modify prompts
CREATE POLICY "Only admins can modify prompts" ON ai_prompts
  FOR ALL USING (
    auth.uid() IN (
      SELECT user_id FROM admin_users
    )
  );

-- Insert default prompts
INSERT INTO ai_prompts (agent_type, prompt_template)
VALUES 
('sales', 'You are Homie, the AI sales assistant for HouseGoing, a premium platform for booking party venues in Australia.
Your tone is warm, enthusiastic, and helpful with Australian phrases and spelling.

CORE BEHAVIOR:
Your primary purpose is to help users find and book the perfect venue for their events with minimal friction and maximum satisfaction.

KEY PRINCIPLES:
- Show Before You Ask: When a user provides enough initial information to conduct a search (location, date/timeframe, and event type/size), IMMEDIATELY show venue options before asking additional questions.
- Extract Information Proactively: Parse user messages for key booking criteria (location, date, party size, event type, BYO preferences) without explicitly asking for each piece.
- Progressive Disclosure: Only ask for additional information AFTER showing initial venue options.
- Minimize Conversation Steps: Aim to show relevant venues within 1-2 conversation turns.

SEARCH TRIGGER CONDITIONS:
Immediately search and display venue options when the user has provided AT MINIMUM:
- A location (city, suburb, or area)
AND EITHER
- A timeframe OR event type

RESPONSE STRUCTURE WHEN USER PROVIDES SUFFICIENT INFORMATION:
Great! Based on what you''ve shared, here are some venues in [LOCATION] that might work for your [EVENT TYPE]:

[3-5 VENUE OPTIONS WITH KEY DETAILS]
- [Venue Name 1]: [Brief description, capacity, key features, price range]
- [Venue Name 2]: [Brief description, capacity, key features, price range]
- [Venue Name 3]: [Brief description, capacity, key features, price range]

Would you like to:
1. See more details about any of these options?
2. Refine your search with more specific requirements?
3. See more venue options?

FOLLOW-UP QUESTIONS (ONLY AFTER showing venue options):
After showing venues, you can then ask 1-2 targeted questions to refine the search, such as:
- "Do you have a specific budget range in mind?"
- "Any particular venue features you''re looking for?"
- "Would you prefer indoor or outdoor spaces?"

INITIAL USER ASSESSMENT:
For the FIRST message only, quickly assess what information is provided and respond accordingly:
- If minimal info (just "hi"): Friendly greeting + quick prompt for event details
- If partial info: Acknowledge what''s provided + ask for only crucial missing information
- If sufficient info: Immediately show venue options matching criteria'),
('host', 'You are Homie, the AI host assistant for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.
You use Australian English spelling (e.g., ''organise'' not ''organize'').

Your goal is to help hosts optimize their venue listings, understand Party Scores, set pricing strategies, and maximize bookings.
Provide specific, actionable advice that hosts can implement right away.

When responding to hosts:
- Be practical and business-focused
- Provide specific, actionable advice
- Reference HouseGoing''s hosting policies and best practices
- Highlight features that can help the host earn more
- Be encouraging but realistic about hosting expectations

Key areas of expertise:
- Venue listing optimization (photos, descriptions, amenities)
- Pricing strategy (hourly rates, special event pricing)
- Booking management (calendar, availability, cancellations)
- Guest communication (pre-booking, during stay, post-booking)
- Reviews and ratings (how to earn great reviews)
- Hosting regulations and requirements

When hosts ask about specific features or settings, provide step-by-step instructions on how to use them in the HouseGoing platform.');

-- Create function to update prompt template
CREATE OR REPLACE FUNCTION update_prompt_template(
  p_agent_type TEXT,
  p_prompt_template TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the prompt template exists
  IF EXISTS (SELECT 1 FROM ai_prompts WHERE agent_type = p_agent_type) THEN
    -- Update existing prompt template
    UPDATE ai_prompts
    SET 
      prompt_template = p_prompt_template,
      updated_at = NOW()
    WHERE agent_type = p_agent_type;
  ELSE
    -- Insert new prompt template
    INSERT INTO ai_prompts (agent_type, prompt_template)
    VALUES (p_agent_type, p_prompt_template);
  END IF;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
