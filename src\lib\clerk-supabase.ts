/**
 * Clerk-Supabase Integration
 *
 * This file provides utilities for integrating Clerk authentication with Supabase.
 * It creates a Supabase client that automatically includes the Clerk JWT token
 * in requests to Supabase, allowing for proper authentication and authorization.
 */

import { supabase } from './supabase-client';

// Add TypeScript declaration for the global Clerk object
declare global {
  interface Window {
    Clerk?: {
      loaded: boolean;
      user: any;
      session?: {
        getToken: (options: { template: string }) => Promise<string | null>;
        id?: string; // Add id field
        status?: string; // Add status field
        lastActiveAt?: Date; // Add lastActiveAt field
        expireAt?: Date; // Add expireAt field
        reload?: () => Promise<void>; // Add reload method
      };
    };
  }
}

// Keep track of the token to avoid unnecessary requests
let cachedToken: string | null = null;
let tokenExpiryTime: number = 0;

/**
 * Get a Clerk JWT token for Supabase authentication
 * This function uses the window.Clerk global object that's available
 * when the Clerk React provider is initialized
 */
export async function getClerkToken(): Promise<string | null> {
  try {
    // Check if we have a cached token that's still valid
    const now = Date.now();
    if (cachedToken && now < tokenExpiryTime) {
      return cachedToken;
    }

    // Check if Clerk is available globally
    if (typeof window !== 'undefined' && window.Clerk) {
      // Wait for Clerk to be ready
      if (!window.Clerk.loaded) {
        await new Promise<void>((resolve) => {
          const checkLoaded = () => {
            if (window.Clerk?.loaded) {
              resolve();
            } else {
              setTimeout(checkLoaded, 100);
            }
          };
          checkLoaded();
        });
      }

      // Check if the user is signed in
      if (!window.Clerk.user) {
        console.warn('No authenticated user found in Clerk');
        return null;
      }

      // Check if session exists - if not, try to force a session reload
      if (!window.Clerk.session) {
        console.warn('No Clerk session available, attempting to reload');
        try {
          // Use client-side fetch to get the current session
          const response = await fetch('/api/auth/session', {
            method: 'GET',
            credentials: 'same-origin'
          });
          
          if (!response.ok) {
            console.error('Failed to refresh session data');
          }
        } catch (error) {
          console.error('Error refreshing session:', error);
        }
      }

      // Get the token with the Supabase template
      let token = null;
      try {
        token = await window.Clerk.session?.getToken({ template: 'supabase' });
      } catch (e) {
        console.error('Error getting token from Clerk session:', e);
      }

      // Cache the token
      if (token) {
        cachedToken = token;
        tokenExpiryTime = now + 5 * 60 * 1000; // 5 minutes from now
      } else {
        console.warn('Failed to get Supabase token from Clerk');
      }

      return token || null;
    } else {
      console.warn('Clerk is not available globally');
      return null;
    }
  } catch (error) {
    console.error('Error getting Clerk token:', error);
    return null;
  }
}

/**
 * Sets the Clerk JWT token on the existing Supabase client
 * Fixed version that properly handles Supabase v2 authentication
 *
 * @returns The centralized Supabase client with Clerk authentication
 */
export async function getAuthenticatedSupabaseClient() {
  try {
    // Get a fresh token
    const token = await getClerkToken();

    if (token) {
      try {
        // For Supabase v2, we need to set the session properly
        await supabase.auth.setSession({
          access_token: token,
          refresh_token: '' // Clerk handles refresh, so we don't need this
        });

        console.log('Successfully set Clerk token on Supabase client');

        // Cache the token
        cachedToken = token;
        tokenExpiryTime = Date.now() + 5 * 60 * 1000; // 5 minutes from now

      } catch (error) {
        console.error('Error setting auth token:', error);

        // Fallback: manually set headers for API requests
        (supabase as any).supabaseKey = token;
        (supabase as any).headers = {
          ...(supabase as any).headers,
          'Authorization': `Bearer ${token}`,
          'apikey': token
        };
      }
    } else {
      console.warn('No Clerk token available for Supabase authentication');
    }

    return supabase;
  } catch (error) {
    console.error('Error in getAuthenticatedSupabaseClient:', error);
    return supabase;
  }
}

/**
 * DEPRECATED: Use getAuthenticatedSupabaseClient() instead
 * This function is kept for backward compatibility
 */
export function createClerkSupabaseClient() {
  // Warn about using deprecated function
  console.warn('DEPRECATED: Use getAuthenticatedSupabaseClient() instead of createClerkSupabaseClient()');
  console.warn('Multiple Supabase clients detected - use centralized client to avoid conflicts');

  // Return the centralized client
  return supabase;
}

/**
 * DEPRECATED: Use getAuthenticatedSupabaseClient() instead
 * This is kept for backward compatibility
 */
export const clerkSupabase = supabase;

/**
 * Function to manually set a Clerk token for Supabase
 * This is useful for testing or when you already have a token
 *
 * @param token The Clerk JWT token
 */
export function setClerkToken(token: string) {
  cachedToken = token;
  tokenExpiryTime = Date.now() + 5 * 60 * 1000; // 5 minutes from now
}

/**
 * Function to clear the cached Clerk token
 * This should be called when the user logs out
 */
export function clearClerkToken() {
  cachedToken = null;
  tokenExpiryTime = 0;
}
