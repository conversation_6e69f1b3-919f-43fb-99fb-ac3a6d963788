/**
 * Supabase client configuration
 *
 * This file imports the centralized Supabase client from src/lib/supabase-client.ts
 * to prevent multiple GoTrueClient instances.
 */

import { supabase as centralizedClient } from './supabase-client';

// Log that we're using the centralized client
console.log('Supabase imported successfully for curfew API');

// Export the centralized client
export const supabase = centralizedClient;

// Direct API functions for NSW Party Planning Tool
export async function getNSWZoningInfo(lat, lng) {
  try {
    const { data, error } = await supabase.rpc('get_nsw_zoning_info', {
      latitude: lat,
      longitude: lng
    });

    if (error) {
      console.error('Error getting NSW zoning info:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception getting NSW zoning info:', error);
    return null;
  }
}

export async function getNSWLGAInfo(lat, lng) {
  try {
    const { data, error } = await supabase.rpc('get_nsw_lga_info', {
      latitude: lat,
      longitude: lng
    });

    if (error) {
      console.error('Error getting NSW LGA info:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception getting NSW LGA info:', error);
    return null;
  }
}

export async function getSuburbToLGAMapping(suburb) {
  try {
    const { data, error } = await supabase
      .from('nsw_suburbs')
      .select('suburb, lga_name')
      .ilike('suburb', `%${suburb}%`)
      .limit(1);

    if (error) {
      console.error('Error getting suburb to LGA mapping:', error);
      return null;
    }

    return data.length > 0 ? data[0].lga_name : null;
  } catch (error) {
    console.error('Exception getting suburb to LGA mapping:', error);
    return null;
  }
}

/**
 * Check if user is a host
 */
export async function isUserHost(clerkId) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('clerk_id', clerkId)
      .single();

    if (error) {
      console.error('Error checking if user is host:', error);
      return false;
    }

    return data?.role === 'host';
  } catch (error) {
    console.error('Exception checking if user is host:', error);
    return false;
  }
}

/**
 * Get user profile from Supabase
 */
export async function getUserProfile(clerkId) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('clerk_id', clerkId)
      .single();

    if (error) {
      console.error('Error getting user profile:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception getting user profile:', error);
    return null;
  }
}

/**
 * Set user role in Supabase
 */
export async function setUserRole(clerkId, email, role) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        clerk_id: clerkId,
        email: email,
        role: role,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'clerk_id'
      });

    if (error) {
      console.error('Error setting user role:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception setting user role:', error);
    return false;
  }
}

/**
 * Sync Clerk user with Supabase
 */
export async function syncClerkUser(clerkId, email) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        clerk_id: clerkId,
        email: email,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'clerk_id'
      });

    if (error) {
      console.error('Error syncing Clerk user:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception syncing Clerk user:', error);
    return null;
  }
}

export default {
  supabase,
  getNSWZoningInfo,
  getNSWLGAInfo,
  getSuburbToLGAMapping,
  isUserHost,
  getUserProfile,
  setUserRole,
  syncClerkUser
};
