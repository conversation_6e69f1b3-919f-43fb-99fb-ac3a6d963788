/**
 * Image Optimization Service
 * 
 * This service provides utilities for optimizing and loading images efficiently.
 * It works with both static assets and Cloudinary-based dynamic images.
 */

interface ImageDimensions {
  width: number;
  height?: number;
}

interface ImageOptions {
  quality?: number;
  format?: 'webp' | 'avif' | 'jpg' | 'png' | 'auto';
  blur?: number;
}

/**
 * Generate an optimized image URL for Cloudinary
 */
export function getOptimizedCloudinaryUrl(
  imageUrl: string,
  dimensions: ImageDimensions,
  options: ImageOptions = {}
): string {
  if (!imageUrl || !imageUrl.includes('cloudinary.com')) {
    return imageUrl; // Return original if not a Cloudinary URL
  }

  const { width, height } = dimensions;
  const { quality = 80, format = 'auto', blur = 0 } = options;

  // Find the upload part of the URL to insert transformations
  const uploadIndex = imageUrl.indexOf('/upload/');
  if (uploadIndex === -1) return imageUrl;

  // Build the transformation string
  const transformations = [
    'c_fill', // Crop mode
    \w_\\,
    height ? \h_\\ : '',
    \q_\\,
    format !== 'auto' ? \_\\ : 'f_auto',
    blur > 0 ? \e_blur:\\ : '',
  ].filter(Boolean).join(',');

  // Insert transformations
  const optimizedUrl = \\\/\\;
  
  return optimizedUrl;
}

/**
 * Generate srcSet for responsive images
 */
export function generateSrcSet(
  imageUrl: string,
  options: ImageOptions = {}
): string {
  if (!imageUrl) return '';

  const widths = [320, 640, 960, 1280, 1920];
  
  return widths
    .map(width => {
      const optimizedUrl = getOptimizedCloudinaryUrl(
        imageUrl,
        { width },
        options
      );
      return \\ \w\;
    })
    .join(', ');
}

/**
 * Get correct image dimensions based on aspect ratio
 */
export function calculateAspectRatioDimensions(
  width: number,
  aspectRatio: number
): ImageDimensions {
  return {
    width,
    height: Math.round(width / aspectRatio)
  };
}

/**
 * Generate a low-quality image placeholder
 */
export function getLowQualityPlaceholder(imageUrl: string): string {
  return getOptimizedCloudinaryUrl(
    imageUrl,
    { width: 20 },
    { quality: 20, blur: 10 }
  );
}
