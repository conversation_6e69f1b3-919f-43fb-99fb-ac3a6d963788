import React, { useState, useEffect } from 'react';
import Button from '../ui/Button';
import DateRangeSelector from './DateRangeSelector';
import TimeSelector from './TimeSelector';
import GuestSelector from './GuestSelector';
import PriceSummary from './PriceSummary';
import NoiseRestrictionInfo from './NoiseRestrictionInfo';
import { calculateHours, getAvailableStartHours, getAvailableEndHours, isWeekend, isHoliday, isLateNight } from '../../utils/booking';
import { Venue } from '../../types/venue';
import { stripePaymentService, PriceCalculation } from '../../services/stripe-payment';
import { checkVenueAvailability } from '../../api/bookings';


interface BookingFormProps {
  venue: Venue;
  onSubmit: (bookingData: BookingData) => Promise<void>;
}

export interface BookingData {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  guests: number;
  totalPrice: number;
  isMultiDay: boolean;
  priceCalculation?: PriceCalculation;
}

export default function BookingForm({ venue, onSubmit }: BookingFormProps) {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [guests, setGuests] = useState(1);
  const [loading, setLoading] = useState(false);
  const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);

  // Get current date in YYYY-MM-DD format using Sydney timezone
  const now = new Date();
  const sydneyOffset = 10 * 60; // Sydney is UTC+10 (in minutes)
  const utcDate = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
  const sydneyDate = new Date(utcDate.getTime() + (sydneyOffset * 60000));
  const today = sydneyDate.toISOString().split('T')[0];

  // Format date as DD/MM/YYYY for display
  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return '';

    try {
      // Validate date format (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        console.error('Invalid date format:', dateString);
        return 'Invalid Date';
      }

      const [year, month, day] = dateString.split('-').map(part => parseInt(part, 10));

      // Validate date parts
      if (isNaN(year) || isNaN(month) || isNaN(day)) {
        console.error('Invalid date parts:', { year, month, day });
        return 'Invalid Date';
      }

      // Create date object (use noon to avoid timezone issues)
      const date = new Date(Date.UTC(year, month - 1, day, 12, 0, 0));

      // Validate date object
      if (isNaN(date.getTime())) {
        console.error('Invalid date object:', date);
        return 'Invalid Date';
      }

      // Format date
      return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Calculate total hours for the booking
  const hours = calculateHours(startTime, endTime);

  // Check if booking spans multiple days
  const isMultiDay = startDate && endDate && startDate !== endDate;

  // Check if booking is overnight (same day but end time is earlier than start time)
  const isOvernightBooking = !isMultiDay && startTime && endTime ?
    parseInt(endTime.split(':')[0]) < parseInt(startTime.split(':')[0]) : false;

  // Check if venue allows overnight bookings
  const allowsOvernight = venue.noiseRestrictions?.allowsOvernight ?? false;

  // Calculate total days for multi-day bookings
  const calculateTotalDays = () => {
    if (!startDate || !endDate) return 1;

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays || 1; // Ensure at least 1 day
  };

  const totalDays = calculateTotalDays();

  // Check if booking is on a weekend
  const bookingIsWeekend = startDate ? isWeekend(startDate) : false;

  // Check if booking is on a holiday
  const bookingIsHoliday = startDate ? isHoliday(startDate) : false;

  // Check if booking is late night
  const bookingIsLateNight = startTime ? isLateNight(startTime) : false;

  // Calculate price using Stripe payment service with venue-specific pricing
  useEffect(() => {
    if (hours > 0 && venue.price > 0) {
      // For multi-day bookings, calculate based on days
      const effectiveHours = isMultiDay ? totalDays * 24 : hours;

      // Extract venue pricing configuration from venue data
      const venuePricingConfig = (venue as any).pricing ? {
        progressivePricing: (venue as any).pricing.progressivePricing,
        surcharges: (venue as any).pricing.surcharges
      } : undefined;

      const calculation = stripePaymentService.calculatePrice(
        venue.price,
        effectiveHours,
        {
          isWeekend: bookingIsWeekend,
          isHoliday: bookingIsHoliday,
          isLateNight: bookingIsLateNight,
          isOvertime: effectiveHours > 12, // Consider 12+ hours as overtime
          venuePricingConfig
        }
      );

      setPriceCalculation(calculation);
    }
  }, [venue.price, hours, isMultiDay, totalDays, bookingIsWeekend, bookingIsHoliday, bookingIsLateNight]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate dates and times
      if (!startDate) {
        throw new Error('Please select a start date');
      }

      if (!startTime) {
        throw new Error('Please select a start time');
      }

      if (!endTime) {
        throw new Error('Please select an end time');
      }

      // Validate date format
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
        throw new Error('Invalid start date format');
      }

      if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        throw new Error('Invalid end date format');
      }

      // Check if venue is available
      try {
        console.log('Checking venue availability...');

        const availabilityCheck = await checkVenueAvailability(
          venue.id,
          startDate,
          startTime,
          endTime,
          endDate
        );

        console.log('Availability check result:', availabilityCheck);

        if (!availabilityCheck.available) {
          // Format conflicting bookings for display
          const conflictTimes = availabilityCheck.conflicts.map((conflict: any) => {
            const conflictStart = new Date(conflict.start_date);
            const conflictEnd = new Date(conflict.end_date);

            return `${conflictStart.toLocaleString()} - ${conflictEnd.toLocaleString()}`;
          }).join(', ');

          throw new Error(`This venue is already booked for the selected time. Conflicting bookings: ${conflictTimes}`);
        }
      } catch (availabilityError: any) {
        console.error('Availability check failed:', availabilityError);
        throw new Error(availabilityError.message || 'Failed to check venue availability. Please try again.');
      }

      // Make sure we have a price calculation
      if (!priceCalculation) {
        // Calculate price if not already calculated
        const effectiveHours = isMultiDay ? totalDays * 24 : hours;
        const calculation = stripePaymentService.calculatePrice(
          venue.price,
          effectiveHours,
          {
            isWeekend: bookingIsWeekend,
            isHoliday: bookingIsHoliday,
            isLateNight: bookingIsLateNight
          }
        );
        setPriceCalculation(calculation);

        // Create the booking data
        const bookingData = {
          startDate,
          endDate: endDate || startDate, // If no end date, use start date
          startTime,
          endTime,
          guests,
          totalPrice: calculation.total,
          isMultiDay: Boolean(isMultiDay),
          priceCalculation: calculation
        };

        // Log booking data for debugging
        console.log('Submitting booking:', bookingData);

        await onSubmit(bookingData);
      } else {
        // Create the booking data
        const bookingData = {
          startDate,
          endDate: endDate || startDate, // If no end date, use start date
          startTime,
          endTime,
          guests,
          totalPrice: priceCalculation.total,
          isMultiDay: Boolean(isMultiDay),
          priceCalculation
        };

        // Log booking data for debugging
        console.log('Submitting booking:', bookingData);

        await onSubmit(bookingData);
      }
    } catch (error: any) {
      console.error('Booking failed:', error);
      alert(error.message || 'Booking failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-2">
        <h3 className="text-lg font-semibold text-purple-800 mb-1">When would you like to book?</h3>
          <p className="text-sm text-gray-600 mb-4">Select your preferred date and time</p>

        <div className="flex flex-col space-y-2 mb-4">
          <label className="text-sm font-medium text-gray-700">Start Date</label>
          <DateRangeSelector
            startDate={startDate}
            endDate={endDate}
            onChangeStartDate={setStartDate}
            onChangeEndDate={setEndDate}
            minDate={today}
            allowSameDay={true}
          />
          {startDate && (
            <p className="text-xs text-gray-500">
              Selected: {formatDisplayDate(startDate)}
            </p>
          )}
        </div>

        {endDate && (
          <div className="flex flex-col space-y-2 mb-4">
            <label className="text-sm font-medium text-gray-700">End Date</label>
            <p className="text-xs text-gray-500">
              Selected: {formatDisplayDate(endDate)}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <TimeSelector
            label="Start Time"
            value={startTime}
            onChange={setStartTime}
            availableHours={getAvailableStartHours()}
          />

          <TimeSelector
            label="End Time"
            value={endTime}
            onChange={setEndTime}
            availableHours={getAvailableEndHours(startTime)}
            disabled={!startTime}
          />
        </div>

        {/* Warning for overnight bookings if not allowed */}
        {isOvernightBooking && !allowsOvernight && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-3 flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-red-800">This venue does not allow overnight bookings</p>
              <p className="text-xs text-red-700 mt-1">Please select an end time on the same day as your start time.</p>
            </div>
          </div>
        )}

        {/* Info for overnight bookings if allowed */}
        {isOvernightBooking && allowsOvernight && (
          <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3 flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-blue-800">You've selected an overnight booking</p>
              <p className="text-xs text-blue-700 mt-1">Your booking will continue into the next day. The venue allows overnight stays.</p>
            </div>
          </div>
        )}
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-2">
        <h3 className="text-lg font-semibold text-purple-800 mb-1">How many guests?</h3>
        <p className="text-sm text-gray-600 mb-4">This venue can accommodate up to {venue.capacity} guests</p>

        <GuestSelector
          value={guests}
          onChange={setGuests}
          maxCapacity={venue.capacity}
        />
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm mb-4">
        <PriceSummary
          pricePerHour={venue.price}
          hours={hours}
          isMultiDay={Boolean(isMultiDay)}
          totalDays={totalDays}
          isWeekend={bookingIsWeekend}
          isHoliday={bookingIsHoliday}
          isLateNight={bookingIsLateNight}
          venue={venue}
        />
      </div>

      <NoiseRestrictionInfo venue={venue} />

      <div className="pt-4">
        <Button
          type="submit"
          fullWidth
          size="lg"
          isLoading={loading}
          loadingText="Processing..."
          disabled={!startDate || !endDate || !startTime || !endTime || (isOvernightBooking && !allowsOvernight)}
          className="py-4 text-base font-medium"
        >
          Request to Book
        </Button>

        <p className="text-sm text-gray-500 text-center mt-3 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          You won't be charged yet
        </p>
      </div>
    </form>
  );
}
