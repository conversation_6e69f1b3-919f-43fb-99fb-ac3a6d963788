import { supabase } from '../supabase';

// Log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// Log entry interface
export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  timestamp: string;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
}

// Logger configuration
interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableDatabase: boolean;
  batchSize: number;
  flushInterval: number;
}

// Default configuration
const defaultConfig: LoggerConfig = {
  minLevel: LogLevel.INFO,
  enableConsole: true,
  enableDatabase: true,
  batchSize: 10,
  flushInterval: 10000 // 10 seconds
};

// Logger class
export class Logger {
  private config: LoggerConfig;
  private logQueue: LogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  
  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.startFlushTimer();
  }
  
  /**
   * Log a debug message
   */
  debug(message: string, context?: Record<string, any>, userId?: string, sessionId?: string): void {
    this.log(LogLevel.DEBUG, message, context, userId, sessionId);
  }
  
  /**
   * Log an info message
   */
  info(message: string, context?: Record<string, any>, userId?: string, sessionId?: string): void {
    this.log(LogLevel.INFO, message, context, userId, sessionId);
  }
  
  /**
   * Log a warning message
   */
  warn(message: string, context?: Record<string, any>, userId?: string, sessionId?: string): void {
    this.log(LogLevel.WARN, message, context, userId, sessionId);
  }
  
  /**
   * Log an error message
   */
  error(message: string, context?: Record<string, any>, userId?: string, sessionId?: string): void {
    this.log(LogLevel.ERROR, message, context, userId, sessionId);
  }
  
  /**
   * Log a message with the specified level
   */
  private log(level: LogLevel, message: string, context?: Record<string, any>, userId?: string, sessionId?: string): void {
    // Check if level is enabled
    if (!this.isLevelEnabled(level)) {
      return;
    }
    
    // Create log entry
    const entry: LogEntry = {
      level,
      message,
      context,
      timestamp: new Date().toISOString(),
      userId,
      sessionId,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined
    };
    
    // Log to console
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }
    
    // Add to queue for database logging
    if (this.config.enableDatabase) {
      this.logQueue.push(entry);
      
      // Flush if queue is full
      if (this.logQueue.length >= this.config.batchSize) {
        this.flush();
      }
    }
  }
  
  /**
   * Check if a log level is enabled
   */
  private isLevelEnabled(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR];
    const minLevelIndex = levels.indexOf(this.config.minLevel);
    const levelIndex = levels.indexOf(level);
    
    return levelIndex >= minLevelIndex;
  }
  
  /**
   * Log to console
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}]`;
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.context || '');
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.context || '');
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.context || '');
        break;
      case LogLevel.ERROR:
        console.error(prefix, entry.message, entry.context || '');
        break;
    }
  }
  
  /**
   * Start the flush timer
   */
  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }
  
  /**
   * Flush the log queue to the database
   */
  async flush(): Promise<void> {
    if (this.logQueue.length === 0) {
      return;
    }
    
    const entries = [...this.logQueue];
    this.logQueue = [];
    
    try {
      const { error } = await supabase
        .from('logs')
        .insert(entries.map(entry => ({
          level: entry.level,
          message: entry.message,
          context: entry.context,
          timestamp: entry.timestamp,
          user_id: entry.userId,
          session_id: entry.sessionId,
          url: entry.url,
          user_agent: entry.userAgent
        })));
      
      if (error) {
        console.error('Error writing logs to database:', error);
        
        // Put entries back in queue
        this.logQueue = [...entries, ...this.logQueue];
      }
    } catch (error) {
      console.error('Error flushing logs:', error);
      
      // Put entries back in queue
      this.logQueue = [...entries, ...this.logQueue];
    }
  }
  
  /**
   * Update logger configuration
   */
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Restart flush timer if interval changed
    if (config.flushInterval !== undefined) {
      this.startFlushTimer();
    }
  }
}

// Create default logger instance
export const logger = new Logger();
