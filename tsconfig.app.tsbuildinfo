{"root": ["./src/app.tsx", "./src/simpleapp.tsx", "./src/i18n.d.ts", "./src/main-with-clerk.tsx", "./src/main.tsx", "./src/server.ts", "./src/vite-env.d.ts", "./src/api/analytics.ts", "./src/api/availability.ts", "./src/api/bookings.ts", "./src/api/clerk-webhook-handler.ts", "./src/api/clerk-webhook-route.ts", "./src/api/clerk.ts", "./src/api/notifications.ts", "./src/api/proxy.ts", "./src/api/routes.ts", "./src/api/stripe.ts", "./src/api/useraccount.ts", "./src/api/venues.ts", "./src/api/webhooks/clerk.ts", "./src/api/webhooks/index.ts", "./src/components/authform.tsx", "./src/components/clerkheader.tsx", "./src/components/errorboundary.tsx", "./src/components/featuredvenues.tsx", "./src/components/header.tsx", "./src/components/howitworks.tsx", "./src/components/leafletmap.tsx", "./src/components/map.tsx", "./src/components/searchbar.tsx", "./src/components/venuecard.tsx", "./src/components/venuepreview.tsx", "./src/components/account/accountpagefix.tsx", "./src/components/account/messagessection.tsx", "./src/components/account/paymentmethodssection.tsx", "./src/components/account/reviewssection.tsx", "./src/components/account/savedvenuessection.tsx", "./src/components/admin/adminaianalytics.tsx", "./src/components/admin/adminchatinterface.tsx", "./src/components/admin/adminlayout.tsx", "./src/components/admin/businessanalyticsdashboard.tsx", "./src/components/admin/realtimeanalyticsdashboard.tsx", "./src/components/admin/suburbanalytics.tsx", "./src/components/analytics/googleanalytics.tsx", "./src/components/auth/authstatus.tsx", "./src/components/auth/authtoggle.tsx", "./src/components/auth/basicauthbuttons.tsx", "./src/components/auth/basicmobileauthstatus.tsx", "./src/components/auth/clerkauth.tsx", "./src/components/auth/clerkauthbuttons.tsx", "./src/components/auth/clerkmobileauthstatus.tsx", "./src/components/auth/clerksupabaseinitializer.tsx", "./src/components/auth/emailpasswordsignin.tsx", "./src/components/auth/emailpasswordsignup.tsx", "./src/components/auth/fallbackloginform.tsx", "./src/components/auth/googlebutton.tsx", "./src/components/auth/headerauthbuttons.tsx", "./src/components/auth/headermobileauthstatus.tsx", "./src/components/auth/hostportalauth.tsx", "./src/components/auth/manualverification.tsx", "./src/components/auth/mobileauthstatus.tsx", "./src/components/auth/oauthbutton.tsx", "./src/components/auth/simpleauthbuttons.tsx", "./src/components/auth/supabaseauth.tsx", "./src/components/auth/supabaseauthbutton.tsx", "./src/components/auth/supabaseauthoverlay.tsx", "./src/components/auth/userbutton.tsx", "./src/components/booking/bookingform.tsx", "./src/components/booking/bookingpayment.tsx", "./src/components/booking/daterangeselector.tsx", "./src/components/booking/dateselector.tsx", "./src/components/booking/enhancedbookingflow.tsx", "./src/components/booking/guestselector.tsx", "./src/components/booking/noiserestrictioninfo.tsx", "./src/components/booking/pricesummary.tsx", "./src/components/booking/timeselector.tsx", "./src/components/bookings/calendarintegration.tsx", "./src/components/chat/chatwidget.tsx", "./src/components/customer/homieassistant.tsx", "./src/components/debug/clerkauthdebug.tsx", "./src/components/debug/clerkdebug.tsx", "./src/components/debug/clerksupabasetest.tsx", "./src/components/diagnostics/diagnosticsbutton.tsx", "./src/components/diagnostics/googleoauthdiagnosticsbutton.tsx", "./src/components/favorites/favoritebutton.tsx", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/host/addresslookup.tsx", "./src/components/host/bookingslist.tsx", "./src/components/host/earningschart.tsx", "./src/components/host/listingstats.tsx", "./src/components/host/noiserestrictionsuggestions.tsx", "./src/components/host/photoupload.tsx", "./src/components/host/pricingconfiguration.tsx", "./src/components/host/registerashost.tsx", "./src/components/layout/footer.tsx", "./src/components/layout/header.tsx", "./src/components/map/venuemap.tsx", "./src/components/messaging/bookingmessageform.tsx", "./src/components/messaging/bookingmessages.tsx", "./src/components/messaging/bookingmessagingintegration.tsx", "./src/components/messaging/chatlist.tsx", "./src/components/messaging/chatwindow.tsx", "./src/components/messaging/enhancedmessaging.tsx", "./src/components/messaging/messagenotifications.tsx", "./src/components/navigation/logo.tsx", "./src/components/navigation/mainnav.tsx", "./src/components/navigation/mobilemenu.tsx", "./src/components/navigation/usermenu.tsx", "./src/components/nsw-curfew/addresssuggestionservice.ts", "./src/components/nsw-curfew/nswcurfewzoningtool.tsx", "./src/components/nsw-curfew/partyscorecard.tsx", "./src/components/nsw-curfew/precisenswaddresslookup.test.ts", "./src/components/nsw-curfew/precisenswaddresslookup.tsx", "./src/components/owner/availabilitymanager.tsx", "./src/components/owner/ownerpropertylist.tsx", "./src/components/payment/paymentform.tsx", "./src/components/payment/stripepaymentform.tsx", "./src/components/reviews/reviewform.tsx", "./src/components/reviews/reviewlist.tsx", "./src/components/search/budgetsearch.tsx", "./src/components/search/datepicker.tsx", "./src/components/search/daterangepicker.tsx", "./src/components/search/guestcounter.tsx", "./src/components/search/guestpicker.tsx", "./src/components/search/locationradiusfilter.tsx", "./src/components/search/locationsearch.tsx", "./src/components/search/mobilesearchdropdown.tsx", "./src/components/search/nswsuburbssearchbar.tsx", "./src/components/search/searchdropdown.tsx", "./src/components/search/smartvenuesearch.tsx", "./src/components/search/venuelocationsearch.tsx", "./src/components/seo/jsonld.tsx", "./src/components/seo/seo.tsx", "./src/components/social/sharebuttons.tsx", "./src/components/ui/accessiblemodal.tsx", "./src/components/ui/button.test.tsx", "./src/components/ui/button.tsx", "./src/components/ui/formerror.tsx", "./src/components/ui/formfield.tsx", "./src/components/ui/input.tsx", "./src/components/ui/loadingspinner.tsx", "./src/components/ui/optimizedimage.tsx", "./src/components/ui/responsiveimage.tsx", "./src/components/ui/simplecheckbox.tsx", "./src/components/ui/simpleform.tsx", "./src/components/ui/simpleselect.tsx", "./src/components/ui/simpletextarea.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.ts", "./src/components/venues/venuecard.tsx", "./src/components/venues/venuefilters.tsx", "./src/components/venues/venuegrid.tsx", "./src/components/venues/venuesort.tsx", "./src/components/verification/customerverification.tsx", "./src/config/appconfig.ts", "./src/config/clerk.ts", "./src/config/security.ts", "./src/config/stripe.ts", "./src/config/supabaseconfig.ts", "./src/constants/theme.ts", "./src/context/authcontext.tsx", "./src/context/themecontext.tsx", "./src/data/mockvenues.ts", "./src/data/preregisteredhosts.ts", "./src/diagnostics/clerksupabasedebug.ts", "./src/diagnostics/clerksupabasedebugbutton.tsx", "./src/diagnostics/clerksupabasedebugger.tsx", "./src/hooks/use-mobile.tsx", "./src/hooks/use-toast.ts", "./src/hooks/useadminauth.tsx", "./src/hooks/useanalytics.ts", "./src/hooks/usebookings.ts", "./src/hooks/usechats.ts", "./src/hooks/useclerksafe.ts", "./src/hooks/useclerksupabase.ts", "./src/hooks/useclickoutside.ts", "./src/hooks/useconversations.ts", "./src/hooks/useescapekey.ts", "./src/hooks/usefavorites.ts", "./src/hooks/usefeatureflag.ts", "./src/hooks/useform.ts", "./src/hooks/useformsubmit.ts", "./src/hooks/useformvalidation.ts", "./src/hooks/usegeminiagent.ts", "./src/hooks/usegithubagent.ts", "./src/hooks/usemessages.ts", "./src/hooks/usenswsuburbs.ts", "./src/hooks/userecentlyviewed.ts", "./src/hooks/usesalesgpt.ts", "./src/hooks/usesignupnotifications.ts", "./src/hooks/usevenuecreation.ts", "./src/integrations/supabase/client.ts", "./src/integrations/supabase/types.ts", "./src/layouts/ownerportallayout.tsx", "./src/lib/auth-manager.ts", "./src/lib/clerk-supabase-client.ts", "./src/lib/clerk-supabase-official.ts", "./src/lib/clerk-supabase.ts", "./src/lib/cloudinary.ts", "./src/lib/cloudinaryuploadservice.ts", "./src/lib/mock-storage.ts", "./src/lib/supabase-client.ts", "./src/lib/supabase-server.ts", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/lib/admin/auth.ts", "./src/lib/admin/chat-storage.ts", "./src/lib/api/adminapi.ts", "./src/lib/api/propertyapi.ts", "./src/lib/database/ensure-user-profiles-schema.ts", "./src/lib/database/migrations.ts", "./src/lib/database/setup-functions.ts", "./src/lib/db/migrations/001_initial_schema.ts", "./src/lib/db/migrations/002_add_ai_analytics.ts", "./src/lib/db/migrations/index.ts", "./src/lib/db/migrations/run-migrations.ts", "./src/lib/error-handling/index.ts", "./src/lib/feature-flags/index.ts", "./src/lib/logging/index.ts", "./src/lib/nsw-party-planning/curfew-api.ts", "./src/lib/nsw-party-planning/local-storage-api.ts", "./src/lib/nsw-party-planning/party-score.ts", "./src/lib/salesgpt/agent.ts", "./src/lib/salesgpt/config.ts", "./src/lib/salesgpt/gemini-agent.ts", "./src/lib/salesgpt/github-agent.ts", "./src/lib/salesgpt/knowledge-base.ts", "./src/lib/validation/schemas.ts", "./src/pages/abouthousegoing.tsx", "./src/pages/accessibilitystatement.tsx", "./src/pages/becomehost.tsx", "./src/pages/bookingconfirmation.tsx", "./src/pages/bookingreview.tsx", "./src/pages/consumerrights.tsx", "./src/pages/contactpage.tsx", "./src/pages/enhancedbooking.tsx", "./src/pages/faqpage.tsx", "./src/pages/findvenues.tsx", "./src/pages/helppage.tsx", "./src/pages/homepage.tsx", "./src/pages/howitworks.tsx", "./src/pages/index.tsx", "./src/pages/listspace.tsx", "./src/pages/listvenue.tsx", "./src/pages/listvenueauth.tsx", "./src/pages/listvenuelocation.tsx", "./src/pages/listvenuephotos.tsx", "./src/pages/login.tsx", "./src/pages/messages.tsx", "./src/pages/myaccount.tsx", "./src/pages/mybookings.tsx", "./src/pages/nswcurfewzoningpage.tsx", "./src/pages/nswnoiseguide.tsx", "./src/pages/nswpartyplanningtoolpage.tsx", "./src/pages/nswpartyplanningupdated.tsx", "./src/pages/notfound.tsx", "./src/pages/partyplanningguide.tsx", "./src/pages/partyvenueguide.tsx", "./src/pages/privacy.tsx", "./src/pages/profilepage.tsx", "./src/pages/safetypage.tsx", "./src/pages/salesassistant.tsx", "./src/pages/searchresults.tsx", "./src/pages/signup.tsx", "./src/pages/sitemappage.tsx", "./src/pages/terms.tsx", "./src/pages/termsandconditions.tsx", "./src/pages/termsofservice.tsx", "./src/pages/unauthorized.tsx", "./src/pages/userprofile.tsx", "./src/pages/userprofiletemp.tsx", "./src/pages/usersettings.tsx", "./src/pages/venueassistant.tsx", "./src/pages/venuedetail.tsx", "./src/pages/venueguidehub.tsx", "./src/pages/venuesearchresults.tsx", "./src/pages/supabase-test.tsx", "./src/pages/test-auth.tsx", "./src/pages/admin/aianalytics.tsx", "./src/pages/admin/aitraining.tsx", "./src/pages/admin/adminapprovaldashboard.tsx", "./src/pages/admin/admindashboard.tsx", "./src/pages/admin/adminproperties.tsx", "./src/pages/admin/adminpropertydetail.tsx", "./src/pages/admin/adminremindertest.tsx", "./src/pages/admin/adminsubmissiondetail.tsx", "./src/pages/admin/adminsubmissiondetailsections.tsx", "./src/pages/admin/analytics.tsx", "./src/pages/admin/dashboard.tsx", "./src/pages/admin/emailmanager.tsx", "./src/pages/admin/emailtest.tsx", "./src/pages/admin/errorlogs.tsx", "./src/pages/admin/sitesettings.tsx", "./src/pages/admin/testadminemails.tsx", "./src/pages/admin/testdatamanager.tsx", "./src/pages/admin/usermanagement.tsx", "./src/pages/api/nsw-zoning.ts", "./src/pages/auth/clerkcallback.tsx", "./src/pages/auth/clerkoauthcallback.tsx", "./src/pages/auth/clerksignin.tsx", "./src/pages/auth/clerksignup.tsx", "./src/pages/auth/hostsignin.tsx", "./src/pages/auth/hostsignup.tsx", "./src/pages/auth/oauthcallback.tsx", "./src/pages/auth/signin.tsx", "./src/pages/auth/signup.tsx", "./src/pages/auth/supabaseauthcallback.tsx", "./src/pages/auth/supabaseoauthcallback.tsx", "./src/pages/auth/supabasesignin.tsx", "./src/pages/auth/supabasesignup.tsx", "./src/pages/auth/verifyemail.tsx", "./src/pages/blog/blogpage.tsx", "./src/pages/categories/categorypage.tsx", "./src/pages/event-types/eventtypepage.tsx", "./src/pages/gallery/gallerypage.tsx", "./src/pages/host/australianpropertyform.tsx", "./src/pages/host/availabilitymanagement.tsx", "./src/pages/host/bookings.tsx", "./src/pages/host/dashboard.tsx", "./src/pages/host/earnings.tsx", "./src/pages/host/help.tsx", "./src/pages/host/hostlogin.tsx", "./src/pages/host/hostsignup.tsx", "./src/pages/host/messages.tsx", "./src/pages/host/messagessimple.tsx", "./src/pages/host/ownerlanding.tsx", "./src/pages/host/ownerportal.tsx", "./src/pages/host/properties.tsx", "./src/pages/host/propertyform.tsx", "./src/pages/host/sellerportal.tsx", "./src/pages/host/unauthorized.tsx", "./src/pages/locations/locationpage.tsx", "./src/pages/owner/editproperty.tsx", "./src/pages/owner/manageproperty.tsx", "./src/pages/owner/ownerdashboard.tsx", "./src/pages/venue-types/venuetypepage.tsx", "./src/providers/authprovider.tsx", "./src/providers/clerkprovider.tsx", "./src/providers/clerksupabaseprovider.tsx", "./src/providers/supabaseprovider.tsx", "./src/routes/adminprotectedroute.tsx", "./src/routes/hostprotectedroute.tsx", "./src/routes/lazyroutes.tsx", "./src/routes/protectedroute.tsx", "./src/scripts/fix-venue-coordinates.ts", "./src/scripts/setup-database.ts", "./src/server/index.ts", "./src/services/admin-auth.ts", "./src/services/adminemailservice.ts", "./src/services/adminreminderservice.ts", "./src/services/aiservice.ts", "./src/services/analytics.ts", "./src/services/api.ts", "./src/services/booking-flow.ts", "./src/services/businessanalytics.ts", "./src/services/cleanemailtemplates.ts", "./src/services/datatracking.ts", "./src/services/email.ts", "./src/services/emailnotifications.ts", "./src/services/geocoding.ts", "./src/services/imageoptimizationservice.ts", "./src/services/init.ts", "./src/services/initializeanalytics.ts", "./src/services/notificationservice.ts", "./src/services/nsw-suburbs-search.ts", "./src/services/nsw-suburbs.ts", "./src/services/payment.ts", "./src/services/propertydataservice.ts", "./src/services/propertylifecycleservice.ts", "./src/services/propertysubmissionservice.ts", "./src/services/stripe-payment.ts", "./src/services/venue-coordinates.ts", "./src/services/venue-location-search.ts", "./src/services/ai/agent-factory.ts", "./src/services/ai/testing/agent-tester.ts", "./src/services/api/index.ts", "./src/services/auth/clerk-supabase-sync.ts", "./src/services/auth/index.ts", "./src/services/auth/roles.ts", "./src/services/recommendations/venue-recommender.ts", "./src/stores/messagestore.ts", "./src/types/auth.ts", "./src/types/leaflet-control-geocoder.d.ts", "./src/types/review.ts", "./src/types/supabase.ts", "./src/types/venue.ts", "./src/utils/addresstest.ts", "./src/utils/addressutils.ts", "./src/utils/auth-debug.ts", "./src/utils/booking.ts", "./src/utils/clerk-oauth-helper.ts", "./src/utils/clerk-oauth.ts", "./src/utils/clerk-supabase-diagnostic.ts", "./src/utils/clerk-supabase-template.ts", "./src/utils/clerk-theme.ts", "./src/utils/create-nsw-tables.ts", "./src/utils/createtestsubmissions.ts", "./src/utils/database-initializer.ts", "./src/utils/dates.ts", "./src/utils/enhancedaddressutils.ts", "./src/utils/formatmessage.ts", "./src/utils/google-oauth-debug.ts", "./src/utils/google-oauth-fix.ts", "./src/utils/integration-diagnostics.ts", "./src/utils/localstorageutils.ts", "./src/utils/populate-supabase-venues.ts", "./src/utils/run-migration.ts", "./src/utils/safe-auth.ts", "./src/utils/sessionstorage.ts", "./src/utils/site-url.ts", "./src/utils/social-auth.ts", "./src/utils/spatialutils.test.ts", "./src/utils/spatialutils.ts", "./src/utils/supabase-client-checker.ts", "./src/utils/supabase-health-check.ts", "./src/utils/supabase-migrations.ts", "./src/utils/test-supabase.ts", "./src/utils/testglendenning.ts", "./src/utils/testzoning.ts", "./src/utils/user-roles.ts", "./src/utils/validation.ts"], "errors": true, "version": "5.8.3"}