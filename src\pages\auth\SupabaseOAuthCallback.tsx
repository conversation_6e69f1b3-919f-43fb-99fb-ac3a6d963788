import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useClerk, useUser } from '@clerk/clerk-react';
import { supabase } from '../../lib/supabase-client';

// Helper function to validate redirect paths
const isValidPath = (path: string): boolean => {
  try {
    const url = new URL(path, window.location.origin);
    return url.pathname === path; // Ensure it's a path, not full URL
  } catch {
    return false;
  }
};

interface SupabaseOAuthCallbackProps {
  // Add any props if needed
}

const SupabaseOAuthCallback: React.FC<SupabaseOAuthCallbackProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const clerk = useClerk();
  const { user: clerkUser, isLoaded: isUserLoaded } = useUser();
  const [status, setStatus] = useState('Processing your sign-in...');
  const [error, setError] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 10; // Increased max retries for better reliability

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('OAuth callback initiated');
        console.log('Current URL:', window.location.href);
        console.log('Search params:', location.search);

        // Get user type and redirect path from URL or localStorage
        const params = new URLSearchParams(location.search);
        const destination = params.get('destination');
        const userType = localStorage.getItem('auth_user_type') as 'host' | 'guest' || 'guest';
        const registrationTypeFromStorage = localStorage.getItem('registering_as_host');
        const effectiveUserType = userType || (registrationTypeFromStorage === 'true' ? 'host' : 'guest');

        let redirectTo = localStorage.getItem('auth_redirect_to') || '/';

        if (destination === 'host-portal') {
          redirectTo = '/host/dashboard';
        }

        // Validate redirect path
        if (!isValidPath(redirectTo)) {
          console.warn('Invalid redirect path, defaulting to /');
          redirectTo = '/';
        }

        console.log('User type:', effectiveUserType);
        console.log('Redirect path:', redirectTo);

        // Check if Clerk is loaded
        if (!clerk.loaded) {
          console.log('Clerk not loaded yet, waiting...');
          if (retryCount < maxRetries) {
            setRetryCount(prev => prev + 1);
            setTimeout(() => {
              handleCallback();
            }, 1000);
            return;
          } else {
            // If Clerk fails to load after max retries, try direct navigation
            console.warn('Clerk failed to load after max retries, attempting direct navigation');
            setStatus('Finalizing authentication...');

            // Set success flag
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());

            // Create a temporary ID if none exists
            const tempId = `temp-${Date.now()}`;
            localStorage.setItem('clerk_user_id', tempId);
            localStorage.setItem('user_role', effectiveUserType);

            // Dispatch auth complete event
            window.dispatchEvent(new CustomEvent('auth_complete', {
              detail: {
                success: true,
                provider: 'clerk-fallback-load'
              }
            }));

            // Redirect to appropriate page
            const finalRedirect = effectiveUserType === 'host' ? '/host/dashboard' : redirectTo;
            window.location.href = finalRedirect;
            return;
          }
        }

        // Check for active session
        if (!clerk.session) {
          console.log('No active Clerk session, checking if we need to wait...');

          // If user is loaded but no session, log the situation
          if (isUserLoaded && clerkUser) {
            console.log('User is loaded but no session, will retry...');
            console.log('User email:', clerkUser.primaryEmailAddress?.emailAddress);
            console.log('User ID:', clerkUser.id);
          }

          // If still no session but we haven't reached max retries, try again
          if (retryCount < maxRetries) {
            console.log(`Retry ${retryCount + 1}/${maxRetries} for session...`);
            setRetryCount(prev => prev + 1);
            setTimeout(() => {
              handleCallback();
            }, 1000);
            return;
          }

          // If we've reached max retries and still no session, try direct navigation
          if (retryCount >= maxRetries) {
            console.warn('Max retries reached, attempting direct navigation');
            setStatus('Finalizing authentication...');

            // Set success flag
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());

            // Create a temporary ID if none exists
            const tempId = `temp-${Date.now()}`;
            localStorage.setItem('clerk_user_id', tempId);
            localStorage.setItem('user_role', effectiveUserType);

            // Dispatch auth complete event
            window.dispatchEvent(new CustomEvent('auth_complete', {
              detail: {
                success: true,
                provider: 'clerk-fallback-timeout'
              }
            }));

            // Redirect to appropriate page
            const finalRedirect = effectiveUserType === 'host' ? '/host/dashboard' : redirectTo;
            window.location.href = finalRedirect;
            return;
          }

          // If all else fails, show error
          console.error('No active Clerk session after retries');
          throw new Error('Authentication session not found. Please try signing in again.');
        }

        // If we have a session, proceed with saving user data and redirect
        console.log('Clerk session found, processing authentication...');
        setStatus('Authentication successful! Processing your account...');

        // Set localStorage flags
        if (effectiveUserType === 'host') {
          localStorage.setItem('registering_as_host', 'true');
          localStorage.setItem('user_role', 'host');
        } else {
          localStorage.removeItem('registering_as_host');
          localStorage.setItem('user_role', 'guest');
        }

        // Clean up localStorage items
        localStorage.removeItem('auth_user_type');
        localStorage.removeItem('auth_redirect_to');

        // Save user data if available
        if (clerkUser) {
          try {
            // Store user info in localStorage
            localStorage.setItem('clerk_user_email', clerkUser.primaryEmailAddress?.emailAddress || '');
            localStorage.setItem('clerk_user_id', clerkUser.id);
            localStorage.setItem('clerk_user_name', `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim());
            localStorage.setItem('clerk_auth_time', new Date().toISOString());

            // Set success flag early to ensure it's set even if database save fails
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());

            // Save user profile to database
            const saveResult = await saveUserProfileToDatabase(clerkUser, effectiveUserType);
            console.log('Database save result:', saveResult);

            // If database save failed, we've already set localStorage and dispatched event in saveUserProfileToDatabase
            if (saveResult) {
              // Dispatch auth complete event
              window.dispatchEvent(new CustomEvent('auth_complete', {
                detail: {
                  success: true,
                  provider: 'clerk',
                  user: clerkUser
                }
              }));
            }
          } catch (error) {
            console.error('Error saving user data:', error);

            // Even if there's an error, ensure we set the success flag and dispatch the event
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());

            window.dispatchEvent(new CustomEvent('auth_complete', {
              detail: {
                success: true,
                provider: 'clerk-error-fallback',
                user: clerkUser
              }
            }));
          }
        } else {
          // No clerk user, but we still want to set success flag and dispatch event
          console.log('No Clerk user available, using fallback authentication');
          localStorage.setItem('auth_success', 'true');
          localStorage.setItem('auth_success_time', new Date().toISOString());

          // Create a temporary ID if none exists
          const tempId = `temp-${Date.now()}`;
          localStorage.setItem('clerk_user_id', tempId);

          // Dispatch auth complete event
          window.dispatchEvent(new CustomEvent('auth_complete', {
            detail: {
              success: true,
              provider: 'clerk-fallback'
            }
          }));
        }

        // Update status
        setStatus('Authentication successful! Redirecting...');

        // Redirect to appropriate page
        const finalRedirect = effectiveUserType === 'host' ? '/host/dashboard' : redirectTo;
        console.log('Redirecting to:', finalRedirect);

        // Force a hard page reload to ensure the auth state is properly updated
        setTimeout(() => {
          // Use replace instead of href to avoid adding to browser history
          window.location.replace(finalRedirect);
        }, 1500);
      } catch (err) {
        console.error('Error in OAuth callback:', err);
        setError(err instanceof Error ? err.message : 'An error occurred during authentication');
      }
    };

    handleCallback();
  }, [navigate, location, clerk.loaded, clerk.session, isUserLoaded, clerkUser, retryCount]);

  // Function to save user profile to database
  const saveUserProfileToDatabase = async (clerkUser: any, userType: string): Promise<boolean> => {
    try {
      if (!clerkUser || !clerkUser.primaryEmailAddress) {
        console.warn('No valid Clerk user or email address provided');
        return false;
      }

      const email = clerkUser.primaryEmailAddress.emailAddress;
      const role = userType === 'host' ? 'host' : 'guest';

      // Create user profile object
      const userProfile = {
        clerk_id: clerkUser.id,
        email: email,
        role: role,
        first_name: clerkUser.firstName || '',
        last_name: clerkUser.lastName || '',
        is_host: role === 'host',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('Attempting to save user profile to database:', userProfile);

      try {
        // Use the Supabase client (for database operations only)
        const { error } = await supabase
          .from('user_profiles')
          .upsert(userProfile, { onConflict: 'clerk_id' });

        if (error) {
          console.error('Error saving user profile to database:', error);

          // Even if there's an error, store the user info in localStorage
          localStorage.setItem('clerk_user_email', email);
          localStorage.setItem('clerk_user_id', clerkUser.id);
          localStorage.setItem('clerk_user_name', `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim());
          localStorage.setItem('user_role', role);

          // Dispatch an event to notify the app that authentication is complete
          window.dispatchEvent(new CustomEvent('auth_complete', {
            detail: {
              success: true,
              provider: 'clerk-supabase',
              user: clerkUser
            }
          }));

          return true; // Return true even if database save fails
        }

        console.log('User profile saved to database successfully');
        return true;
      } catch (dbError) {
        console.error('Exception saving user profile to database:', dbError);

        // Even if there's an exception, store the user info in localStorage
        localStorage.setItem('clerk_user_email', email);
        localStorage.setItem('clerk_user_id', clerkUser.id);
        localStorage.setItem('clerk_user_name', `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim());
        localStorage.setItem('user_role', role);

        // Dispatch an event to notify the app that authentication is complete
        window.dispatchEvent(new CustomEvent('auth_complete', {
          detail: {
            success: true,
            provider: 'clerk-supabase',
            user: clerkUser
          }
        }));

        return true; // Return true even if database save fails
      }
    } catch (error) {
      console.error('Error in saveUserProfileToDatabase:', error);
      return false;
    }
  };

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
        {error ? (
          <>
            <div className="text-red-600 text-xl font-semibold mb-4">Authentication Error</div>
            <p className="text-gray-700 mb-6">{error}</p>
            <button
              onClick={() => navigate('/login')}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              Return to Login
            </button>
          </>
        ) : (
          <>
            <div className="mb-6">
              <img
                src="/images/housegoing-logo.svg"
                alt="HouseGoing"
                className="h-16 w-16 mx-auto"
              />
            </div>
            <h2 className="text-2xl font-semibold text-gray-800 mb-2">Authentication in Progress</h2>
            <p className="text-gray-600">{status}</p>
            <p className="text-gray-400 text-sm mt-4">Retry count: {retryCount}/{maxRetries}</p>
          </>
        )}
      </div>
    </div>
  );
}

export default SupabaseOAuthCallback;
