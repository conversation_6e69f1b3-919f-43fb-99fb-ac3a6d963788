import { useState, useCallback } from 'react';

type ValidationRule<T> = {
  validate: (value: T) => boolean;
  message: string;
};

type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T[K]>[];
};

export function useFormValidation<T extends Record<string, any>>(initialValues: T, validationRules: ValidationRules<T>) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});

  // Handle input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setValues(prev => ({
      ...prev,
      [name]: type === 'number' ? (value === '' ? '' : Number(value)) : value
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof T]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  }, [errors]);

  // Handle input blur
  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name } = e.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
    
    validateField(name as keyof T);
  }, [values]);

  // Set a specific field value programmatically
  const setFieldValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when value is set programmatically
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  }, [errors]);

  // Set a specific field error programmatically
  const setFieldError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, []);

  // Validate a specific field
  const validateField = useCallback((name: keyof T) => {
    const fieldRules = validationRules[name];
    if (!fieldRules) return true;
    
    const value = values[name];
    
    for (const rule of fieldRules) {
      if (!rule.validate(value)) {
        setErrors(prev => ({
          ...prev,
          [name]: rule.message
        }));
        return false;
      }
    }
    
    // Clear error if validation passes
    setErrors(prev => ({
      ...prev,
      [name]: undefined
    }));
    
    return true;
  }, [values, validationRules]);

  // Validate all fields
  const validateForm = useCallback(() => {
    let isValid = true;
    
    // Mark all fields as touched
    const newTouched: Partial<Record<keyof T, boolean>> = {};
    Object.keys(values).forEach(key => {
      newTouched[key as keyof T] = true;
    });
    setTouched(newTouched);
    
    // Validate each field
    Object.keys(validationRules).forEach(key => {
      const fieldKey = key as keyof T;
      const fieldIsValid = validateField(fieldKey);
      if (!fieldIsValid) {
        isValid = false;
      }
    });
    
    return isValid;
  }, [values, validationRules, validateField]);

  // Reset form to initial values
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    setFieldError,
    validateField,
    validateForm,
    resetForm
  };
}
