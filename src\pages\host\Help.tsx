import React, { useState } from 'react';
import { 
  Search, 
  ChevronDown, 
  ChevronUp, 
  MessageSquare, 
  Mail, 
  Phone, 
  FileText 
} from 'lucide-react';

// FAQ data
const faqs = [
  {
    id: 1,
    question: 'How do I list a new property?',
    answer: 'To list a new property, go to the Properties page in your Owner Portal and click on the "Add New Property" button. Fill out the required information about your property, upload photos, set your pricing, and submit the form. Our team will review your listing and approve it within 24-48 hours.'
  },
  {
    id: 2,
    question: 'How do payouts work?',
    answer: 'Payouts are processed automatically after a guest checks out. The funds will be transferred to your designated payment method (bank account or PayPal) within 3-5 business days. You can view your payout history and status in the Earnings section of your Owner Portal.'
  },
  {
    id: 3,
    question: 'What happens if a guest cancels their booking?',
    answer: 'If a guest cancels their booking, the refund amount will depend on your cancellation policy. We offer three cancellation policies: Flexible, Moderate, and Strict. You can set your preferred cancellation policy for each property in the property settings.'
  },
  {
    id: 4,
    question: 'How do I communicate with guests?',
    answer: 'You can communicate with guests through the messaging system in your Owner Portal. Go to the Messages section to view and respond to guest inquiries. We recommend responding to messages within 2 hours to maintain a good response rate.'
  },
  {
    id: 5,
    question: 'What fees does HouseGoing charge?',
    answer: 'HouseGoing charges a 10% Host Service Fee (deducted from your payout) and a 5% Guest Booking Fee (charged to guests). There are no monthly subscription fees or listing fees.'
  },
  {
    id: 6,
    question: 'How do I get verified as a host?',
    answer: 'To get verified as a host, you need to complete your profile information, verify your identity, and list at least one property. Our team will review your information and grant you verified status, which helps build trust with potential guests.'
  },
  {
    id: 7,
    question: 'Can I block certain dates on my calendar?',
    answer: 'Yes, you can block dates on your calendar to prevent bookings during those times. Go to the Properties section, select the property, and use the calendar tool to block specific dates or date ranges.'
  },
  {
    id: 8,
    question: 'How do reviews work?',
    answer: 'After a guest completes their stay, both you and the guest have 14 days to leave a review. Reviews are published once both parties have submitted their reviews, or after the 14-day period ends. You can view and respond to reviews in your Owner Portal.'
  }
];

export default function Help() {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);
  
  // Filter FAQs based on search term
  const filteredFaqs = faqs.filter(faq => {
    return faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
           faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
  });
  
  // Toggle FAQ expansion
  const toggleFaq = (faqId: number) => {
    if (expandedFaq === faqId) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(faqId);
    }
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Help Center</h1>
        <p className="mt-1 text-gray-600">Find answers to common questions and get support</p>
      </div>
      
      {/* Search */}
      <div className="mb-8">
        <div className="relative max-w-xl mx-auto">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search for help..."
            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      {/* Quick links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <div className="mx-auto w-12 h-12 flex items-center justify-center bg-purple-100 rounded-full mb-4">
            <MessageSquare className="h-6 w-6 text-purple-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Live Chat</h3>
          <p className="text-sm text-gray-600 mb-4">
            Chat with our support team for immediate assistance
          </p>
          <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 w-full">
            Start Chat
          </button>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <div className="mx-auto w-12 h-12 flex items-center justify-center bg-blue-100 rounded-full mb-4">
            <Mail className="h-6 w-6 text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Email Support</h3>
          <p className="text-sm text-gray-600 mb-4">
            Send us an email and we'll respond within 24 hours
          </p>
          <a 
            href="mailto:<EMAIL>" 
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 block"
          >
            Email Us
          </a>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <div className="mx-auto w-12 h-12 flex items-center justify-center bg-green-100 rounded-full mb-4">
            <Phone className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Phone Support</h3>
          <p className="text-sm text-gray-600 mb-4">
            Call us for urgent issues or complex questions
          </p>
          <a 
            href="tel:+61400000000" 
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 block"
          >
            Call Now
          </a>
        </div>
      </div>
      
      {/* FAQs */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Frequently Asked Questions</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredFaqs.map((faq) => (
            <div key={faq.id} className="px-6 py-4">
              <button
                className="flex w-full justify-between items-center text-left"
                onClick={() => toggleFaq(faq.id)}
              >
                <h3 className="text-base font-medium text-gray-900">{faq.question}</h3>
                {expandedFaq === faq.id ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </button>
              
              {expandedFaq === faq.id && (
                <div className="mt-2 text-sm text-gray-600">
                  <p>{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
          
          {filteredFaqs.length === 0 && (
            <div className="px-6 py-8 text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-600">
                Try adjusting your search or browse the help categories below
              </p>
            </div>
          )}
        </div>
      </div>
      
      {/* Help categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <a href="#" className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-purple-100 rounded-md">
              <FileText className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="ml-3 text-base font-medium text-gray-900">Getting Started</h3>
          </div>
          <p className="text-sm text-gray-600">
            Learn the basics of listing your property and managing bookings
          </p>
        </a>
        
        <a href="#" className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-md">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="ml-3 text-base font-medium text-gray-900">Account & Settings</h3>
          </div>
          <p className="text-sm text-gray-600">
            Manage your profile, payment methods, and notification preferences
          </p>
        </a>
        
        <a href="#" className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-green-100 rounded-md">
              <FileText className="h-5 w-5 text-green-600" />
            </div>
            <h3 className="ml-3 text-base font-medium text-gray-900">Bookings & Calendar</h3>
          </div>
          <p className="text-sm text-gray-600">
            Learn how to manage bookings, set availability, and handle cancellations
          </p>
        </a>
        
        <a href="#" className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-yellow-100 rounded-md">
              <FileText className="h-5 w-5 text-yellow-600" />
            </div>
            <h3 className="ml-3 text-base font-medium text-gray-900">Payments & Taxes</h3>
          </div>
          <p className="text-sm text-gray-600">
            Understand how payments work, payout schedules, and tax information
          </p>
        </a>
      </div>
    </div>
  );
}
