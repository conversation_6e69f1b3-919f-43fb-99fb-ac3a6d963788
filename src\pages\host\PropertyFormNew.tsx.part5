  // Render Insurance & Compliance section
  const renderInsuranceCompliance = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Insurance & Compliance</h2>
      
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-blue-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Important Information</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Public liability insurance is strongly recommended for all venues in Australia. 
                Many councils and local authorities require it for venues hosting events.
                The recommended minimum coverage is $10-20 million.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <input
            id="has-insurance"
            type="checkbox"
            checked={formData.hasInsurance}
            onChange={(e) => setFormData({...formData, hasInsurance: e.target.checked})}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="has-insurance" className="ml-2 block text-sm font-medium text-gray-700">
            I have public liability insurance for this venue
          </label>
        </div>
      </div>
      
      {formData.hasInsurance && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block mb-2 font-medium">Insurance Provider</label>
              <input
                type="text"
                value={formData.insuranceProvider}
                onChange={(e) => setFormData({...formData, insuranceProvider: e.target.value})}
                className="w-full p-2 border rounded"
                placeholder="e.g. AAMI, QBE, Allianz"
              />
            </div>
            
            <div>
              <label className="block mb-2 font-medium">Policy Number</label>
              <input
                type="text"
                value={formData.policyNumber}
                onChange={(e) => setFormData({...formData, policyNumber: e.target.value})}
                className="w-full p-2 border rounded"
                placeholder="e.g. PLI-123456789"
              />
            </div>
            
            <div>
              <label className="block mb-2 font-medium">Coverage Amount</label>
              <input
                type="text"
                value={formData.coverageAmount}
                onChange={(e) => setFormData({...formData, coverageAmount: e.target.value})}
                className="w-full p-2 border rounded"
                placeholder="e.g. $10 million"
              />
            </div>
            
            <div>
              <label className="block mb-2 font-medium">Expiry Date</label>
              <input
                type="date"
                value={formData.expiryDate}
                onChange={(e) => setFormData({...formData, expiryDate: e.target.value})}
                className="w-full p-2 border rounded"
              />
            </div>
          </div>
          
          <div className="mt-6">
            <label className="block mb-2 font-medium">Upload Insurance Certificate (optional)</label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div className="space-y-1 text-center">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                  aria-hidden="true"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <div className="flex text-sm text-gray-600">
                  <label
                    htmlFor="insurance-certificate"
                    className="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-purple-500"
                  >
                    <span>Upload a file</span>
                    <input id="insurance-certificate" name="insurance-certificate" type="file" className="sr-only" />
                  </label>
                  <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-500">PDF, PNG, JPG up to 10MB</p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
  
  // Render Licenses & Permits section
  const renderLicensesPermits = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Licenses & Permits</h2>
      
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-blue-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Important Information</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Depending on your venue type and the events you host, you may need specific licenses and permits.
                This information helps guests understand what activities are permitted at your venue.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <input
            id="has-liquor-license"
            type="checkbox"
            checked={formData.hasLiquorLicense}
            onChange={(e) => setFormData({...formData, hasLiquorLicense: e.target.checked})}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="has-liquor-license" className="ml-2 block text-sm font-medium text-gray-700">
            This venue has a liquor license
          </label>
        </div>
        
        {formData.hasLiquorLicense && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ml-6 mt-4">
            <div>
              <label className="block mb-2 font-medium">License Number</label>
              <input
                type="text"
                value={formData.liquorLicenseNumber}
                onChange={(e) => setFormData({...formData, liquorLicenseNumber: e.target.value})}
                className="w-full p-2 border rounded"
                placeholder="e.g. LIQH123456789"
              />
            </div>
            
            <div>
              <label className="block mb-2 font-medium">Expiry Date</label>
              <input
                type="date"
                value={formData.liquorLicenseExpiry}
                onChange={(e) => setFormData({...formData, liquorLicenseExpiry: e.target.value})}
                className="w-full p-2 border rounded"
              />
            </div>
          </div>
        )}
      </div>
      
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <input
            id="has-food-permit"
            type="checkbox"
            checked={formData.hasFoodPermit}
            onChange={(e) => setFormData({...formData, hasFoodPermit: e.target.checked})}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="has-food-permit" className="ml-2 block text-sm font-medium text-gray-700">
            This venue has food handling permits
          </label>
        </div>
        
        {formData.hasFoodPermit && (
          <div className="ml-6 mt-4">
            <label className="block mb-2 font-medium">Permit Number</label>
            <input
              type="text"
              value={formData.foodPermitNumber}
              onChange={(e) => setFormData({...formData, foodPermitNumber: e.target.value})}
              className="w-full p-2 border rounded"
              placeholder="e.g. FP-123456789"
            />
          </div>
        )}
      </div>
      
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <input
            id="has-entertainment-license"
            type="checkbox"
            checked={formData.hasEntertainmentLicense}
            onChange={(e) => setFormData({...formData, hasEntertainmentLicense: e.target.checked})}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="has-entertainment-license" className="ml-2 block text-sm font-medium text-gray-700">
            This venue has an entertainment license
          </label>
        </div>
        
        {formData.hasEntertainmentLicense && (
          <div className="ml-6 mt-4">
            <label className="block mb-2 font-medium">License Number</label>
            <input
              type="text"
              value={formData.entertainmentLicenseNumber}
              onChange={(e) => setFormData({...formData, entertainmentLicenseNumber: e.target.value})}
              className="w-full p-2 border rounded"
              placeholder="e.g. ENT-123456789"
            />
          </div>
        )}
      </div>
      
      <div className="mb-6">
        <label className="block mb-2 font-medium">Venue Capacity Certificate</label>
        <input
          type="text"
          value={formData.capacityCertificate}
          onChange={(e) => setFormData({...formData, capacityCertificate: e.target.value})}
          className="w-full p-2 border rounded"
          placeholder="e.g. Maximum 100 people"
        />
        <p className="text-xs text-gray-500 mt-1">Enter the maximum capacity as per your certificate</p>
      </div>
      
      <div className="mb-6">
        <div className="flex items-center">
          <input
            id="fire-safety"
            type="checkbox"
            checked={formData.fireSafetyCompliance}
            onChange={(e) => setFormData({...formData, fireSafetyCompliance: e.target.checked})}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="fire-safety" className="ml-2 block text-sm font-medium text-gray-700">
            This venue complies with fire safety regulations
          </label>
        </div>
      </div>
    </div>
  );
