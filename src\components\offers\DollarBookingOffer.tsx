/**
 * $1 Party Booking Offer Component
 * Displays the special offer and tracks user progress
 */

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { DollarSign, Star, Gift, TrendingUp, CheckCircle, Clock } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface BookingStatus {
  totalBookings: number;
  completedBookings: number;
  nextDiscountAt: number;
  discountBookingsUsed: number;
  isEligibleForDiscount: boolean;
  offerType: string | null;
  bookingsUntilNextDiscount: number;
}

interface DollarBookingOfferProps {
  venuePrice?: number;
  showProgress?: boolean;
  compact?: boolean;
  className?: string;
}

export default function DollarBookingOffer({ 
  venuePrice = 0, 
  showProgress = true, 
  compact = false,
  className = ""
}: DollarBookingOfferProps) {
  const { user } = useUser();
  const [bookingStatus, setBookingStatus] = useState<BookingStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchBookingStatus();
    }
  }, [user?.id]);

  const fetchBookingStatus = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .rpc('get_user_booking_status', { p_user_id: user.id });

      if (error) {
        console.error('Error fetching booking status:', error);
        setError('Unable to load offer status');
        return;
      }

      if (data && data.length > 0) {
        const status = data[0];
        setBookingStatus({
          totalBookings: status.total_bookings,
          completedBookings: status.completed_bookings,
          nextDiscountAt: status.next_discount_at,
          discountBookingsUsed: status.discount_bookings_used,
          isEligibleForDiscount: status.is_eligible_for_discount,
          offerType: status.offer_type,
          bookingsUntilNextDiscount: status.bookings_until_next_discount
        });
      }
    } catch (error) {
      console.error('Error fetching booking status:', error);
      setError('Unable to load offer status');
    } finally {
      setLoading(false);
    }
  };

  const calculateSavings = (venuePrice: number): number => {
    const normalFee = venuePrice * 0.05; // 5% booking fee
    return Math.max(0, normalFee - 1); // Savings = normal fee - $1
  };

  const getProgressPercentage = (): number => {
    if (!bookingStatus) return 0;
    
    if (bookingStatus.isEligibleForDiscount) return 100;
    
    const progress = bookingStatus.completedBookings % 5;
    return (progress / 5) * 100;
  };

  const getOfferTitle = (): string => {
    if (!bookingStatus) return "💰 THE $1 PARTY BOOKING OFFER";
    
    if (bookingStatus.isEligibleForDiscount) {
      return bookingStatus.offerType === 'first_booking' 
        ? "🎉 YOUR FIRST BOOKING - ONLY $1!"
        : "🎊 YOUR 5TH BOOKING - ONLY $1!";
    }
    
    return "💰 THE $1 PARTY BOOKING OFFER";
  };

  const getOfferDescription = (): string => {
    if (!bookingStatus) return "First booking: $1 booking fee (normally 5% of venue cost)";
    
    if (bookingStatus.isEligibleForDiscount) {
      return bookingStatus.offerType === 'first_booking'
        ? "Welcome! Your first booking fee is just $1 instead of the normal 5%"
        : "Congratulations! Your 5th booking fee is just $1 instead of the normal 5%";
    }
    
    return "First booking: $1 booking fee • Every 5th booking: $1 booking fee";
  };

  if (loading) {
    return (
      <div className={`bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-lg p-4 ${className}`}>
        <div className="animate-pulse flex items-center">
          <div className="h-6 w-6 bg-yellow-300 rounded mr-3"></div>
          <div className="h-4 bg-yellow-300 rounded w-48"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <p className="text-gray-600 text-sm">{error}</p>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <DollarSign className="h-5 w-5 text-yellow-600 mr-2" />
            <div>
              <p className="font-bold text-yellow-800 text-sm">$1 Booking Fee</p>
              {bookingStatus?.isEligibleForDiscount ? (
                <p className="text-yellow-700 text-xs">Available now!</p>
              ) : (
                <p className="text-yellow-700 text-xs">
                  {bookingStatus?.bookingsUntilNextDiscount || 0} more until next discount
                </p>
              )}
            </div>
          </div>
          {venuePrice > 0 && (
            <div className="text-right">
              <p className="text-yellow-800 font-bold text-sm">
                Save ${calculateSavings(venuePrice).toFixed(2)}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-400 to-orange-400 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-white rounded-full p-2 mr-3">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <h3 className="font-bold text-white text-lg">{getOfferTitle()}</h3>
              <p className="text-yellow-100 text-sm">Sustainable Version - Abuse-Proof System</p>
            </div>
          </div>
          {bookingStatus?.isEligibleForDiscount && (
            <div className="bg-white rounded-full px-3 py-1">
              <span className="text-yellow-600 font-bold text-sm">ELIGIBLE NOW!</span>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <p className="text-gray-700 mb-4">{getOfferDescription()}</p>

        {/* Offer Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4 border border-yellow-200">
            <div className="flex items-center mb-2">
              <Star className="h-5 w-5 text-yellow-500 mr-2" />
              <h4 className="font-semibold text-gray-900">First Booking</h4>
            </div>
            <p className="text-gray-600 text-sm mb-2">$1 booking fee (normally 5% of venue cost)</p>
            {venuePrice > 0 && (
              <p className="text-green-600 font-medium text-sm">
                Save ${calculateSavings(venuePrice).toFixed(2)} on this booking!
              </p>
            )}
          </div>

          <div className="bg-white rounded-lg p-4 border border-yellow-200">
            <div className="flex items-center mb-2">
              <Gift className="h-5 w-5 text-orange-500 mr-2" />
              <h4 className="font-semibold text-gray-900">Every 5th Booking</h4>
            </div>
            <p className="text-gray-600 text-sm mb-2">$1 booking fee (normally 5% of venue cost)</p>
            <p className="text-blue-600 font-medium text-sm">Ongoing rewards for loyal customers</p>
          </div>
        </div>

        {/* Progress Tracking */}
        {showProgress && bookingStatus && (
          <div className="bg-white rounded-lg p-4 border border-yellow-200 mb-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-gray-900 flex items-center">
                <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
                Your Progress
              </h4>
              <span className="text-sm text-gray-600">
                {bookingStatus.completedBookings} completed bookings
              </span>
            </div>

            {bookingStatus.isEligibleForDiscount ? (
              <div className="flex items-center text-green-600">
                <CheckCircle className="h-5 w-5 mr-2" />
                <span className="font-medium">You're eligible for the $1 booking fee!</span>
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Progress to next $1 booking</span>
                  <span className="text-sm font-medium text-gray-900">
                    {5 - (bookingStatus.completedBookings % 5)}/5 bookings remaining
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-orange-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Important Notes */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <Clock className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Important Details:</h4>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Venue cost is always paid in full by customer (no refunds)</li>
                <li>• Offer applies to booking fee only (normally 5% of venue cost)</li>
                <li>• System automatically tracks your booking count</li>
                <li>• Completed bookings count toward your next discount</li>
                <li>• Cancelled bookings don't count toward progress</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Statistics */}
        {bookingStatus && (
          <div className="mt-4 grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-gray-900">{bookingStatus.totalBookings}</p>
              <p className="text-gray-600 text-sm">Total Bookings</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">{bookingStatus.completedBookings}</p>
              <p className="text-gray-600 text-sm">Completed</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-yellow-600">{bookingStatus.discountBookingsUsed}</p>
              <p className="text-gray-600 text-sm">$1 Bookings Used</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
