import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

export default function MainNav() {
  useEffect(() => {
    console.log('MainNav component rendered - NSW Party Planning link should be visible');
  }, []);

  return (
    <nav className="hidden md:flex items-center justify-center space-x-6">
      <Link
        to="/find-venues"
        className="text-gray-700 hover:text-purple-600 transition-colors"
      >
        Find Venues
      </Link>
      <Link
        to="/venue-guide"
        className="text-gray-700 hover:text-purple-600 transition-colors"
      >
        Venue Guide
      </Link>
      <Link
        to="/dollar-booking-offer"
        className="text-yellow-600 hover:text-yellow-700 transition-colors font-semibold"
      >
        💰 $1 Booking
      </Link>
      <Link
        to="/nsw-party-planning"
        className="text-gray-700 hover:text-purple-600 transition-colors"
      >
        NSW Planning
      </Link>

      {/* Quick Links - Moved from main nav */}
      <div className="flex items-center space-x-4 ml-4 pl-4 border-l border-gray-200">
        <Link
          to="/how-it-works"
          className="text-sm text-gray-600 hover:text-purple-600 transition-colors"
        >
          How It Works
        </Link>
        <Link
          to="/venue-assistant"
          className="text-sm text-gray-600 hover:text-purple-600 transition-colors"
        >
          Venue Assistant
        </Link>
        <Link
          to="/host/portal"
          className="text-sm text-gray-600 hover:text-purple-600 transition-colors"
        >
          Owner Portal
        </Link>
      </div>
    </nav>
  );
}