import React, { useState, useEffect } from 'react';
import { Clock, Calendar, Home, MapPin, Music, Volume2, AlertTriangle, CheckCircle, Info, Search } from 'lucide-react';

// Import our enhanced address utilities
import {
  extractAddressComponents,
  extractLGAFromAddress,
  detectZoning,
  determinePropertyType,
  analyzeNSWAddress
} from '../utils/enhancedAddressUtils';

const NSWNoiseGuide: React.FC = () => {
  const [address, setAddress] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const [testResults, setTestResults] = useState<any[]>([]);

  // Run tests on component mount
  useEffect(() => {
    // Test addresses
    const testAddresses = [
      {
        address: '15-19 Robert Street, Holroyd, NSW 2142',
        lat: -33.8302,
        lng: 150.9893
      },
      {
        address: '5 Tiptrees Ave, Carlingford, NSW 2118',
        lat: -33.7828,
        lng: 151.0463
      },
      {
        address: '24 BERRY STREET, Clyde, NSW 2142',
        lat: -33.8336,
        lng: 151.0249
      }
    ];

    try {
      // Run tests
      const results = testAddresses.map(test => {
        const analysis = analyzeNSWAddress(test.address, test.lat, test.lng);
        return {
          address: test.address,
          analysis
        };
      });

      setTestResults(results);

      // Signal that the component has loaded successfully
      document.dispatchEvent(new CustomEvent('component-loaded', {
        detail: { component: 'NSWNoiseGuide', success: true }
      }));
    } catch (error) {
      console.error('Error running address tests:', error);
      // Signal that the component has loaded with errors
      document.dispatchEvent(new CustomEvent('component-loaded', {
        detail: { component: 'NSWNoiseGuide', success: false, error }
      }));
    }
  }, []);

  // Enhanced search function using our address utilities
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (!address) {
      setError('Please enter an address');
      return;
    }

    setLoading(true);
    setError('');

    // Simulate geocoding with timeout
    setTimeout(() => {
      try {
        // Mock geocoding result - in a real implementation, we would use a geocoding API
        let lat = -33.8688;
        let lng = 151.2093;

        // Adjust coordinates based on address for demo purposes
        if (address.toLowerCase().includes('holroyd')) {
          lat = -33.8302;
          lng = 150.9893;
        } else if (address.toLowerCase().includes('carlingford')) {
          lat = -33.7828;
          lng = 151.0463;
        } else if (address.toLowerCase().includes('clyde')) {
          lat = -33.8336;
          lng = 151.0249;
        } else if (address.toLowerCase().includes('parramatta')) {
          lat = -33.8150;
          lng = 151.0011;
        } else if (address.toLowerCase().includes('ryde')) {
          lat = -33.8182;
          lng = 151.1030;
        } else if (address.toLowerCase().includes('hornsby')) {
          lat = -33.7035;
          lng = 151.0982;
        }

        // Use our enhanced address utilities
        const analysis = analyzeNSWAddress(address, lat, lng);

        // Extract values from analysis
        const zoneCode = analysis.zoneCode;
        const zoneName = analysis.zoneName;
        const propertyType = analysis.propertyType;
        const lgaName = analysis.lgaName;

        // Determine if it's an apartment for curfew times
        const isApartment = propertyType === 'Apartment/Unit';
        const isCommercial = zoneCode.startsWith('B');

        // Set result using our analysis
        setResult({
          address: analysis.fullAddress,
          propertyType: propertyType,
          zoneCode: zoneCode,
          zoneName: zoneName,
          lgaName: lgaName,
          curfewStart: isCommercial ? '00:00:00' : isApartment ? '21:00:00' : '22:00:00',
          curfewEnd: '07:00:00',
          bassRestrictionStart: isApartment ? '21:00:00' : null,
          bassRestrictionEnd: isApartment ? '08:00:00' : null,
          outdoorCutoff: isApartment ? '21:00:00' : '22:00:00',
          specialConditions: isApartment
            ? 'Strata bylaws may impose stricter rules than council regulations'
            : 'Standard residential noise restrictions apply',
          recommendations: [
            {
              time_period: "Before 8:00 PM",
              recommendation: "Regular conversation and moderate music acceptable"
            },
            {
              time_period: "8:00 PM - 10:00 PM",
              recommendation: "Reduce volume, keep windows closed"
            },
            {
              time_period: "After 10:00 PM",
              recommendation: "Low background music only, focus on conversation"
            }
          ]
        });

        setLoading(false);
      } catch (error) {
        console.error('Error analyzing address:', error);
        setError('Failed to analyze address. Please try a different address format.');
        setLoading(false);
      }
    }, 1000);
  };

  // Format time string (e.g., "22:00:00" to "10:00 PM")
  const formatTime = (timeStr: string): string => {
    const [hours, minutes] = timeStr.split(':');
    const hourNum = parseInt(hours, 10);
    const period = hourNum >= 12 ? 'PM' : 'AM';
    const displayHour = hourNum % 12 || 12;
    return `${displayHour}:${minutes} ${period}`;
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6 text-center md:text-left">NSW Party Planning & Noise Guide</h1>

      <div className="mb-6">
        <p className="text-gray-700 mb-4 text-sm md:text-base">
          Find out noise curfew times, restrictions, and recommendations for any address in NSW to help plan your event and stay
          compliant with local regulations.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <form onSubmit={handleSearch} className="flex flex-col gap-4">
              <div className="w-full">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Enter NSW address"
                    className="pl-10 w-full p-2 border border-gray-300 rounded-md md:rounded-l-md md:rounded-r-none focus:ring-purple-500 focus:border-purple-500"
                  />
                  <button
                    type="submit"
                    className={`mt-2 md:mt-0 md:absolute md:right-0 md:top-0 w-full md:w-auto md:h-full bg-purple-600 text-white px-4 py-2 rounded-md md:rounded-l-none md:rounded-r-md hover:bg-purple-700 transition-colors ${loading ? 'opacity-75 cursor-not-allowed' : ''}`}
                    disabled={loading}
                  >
                    {loading ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Searching...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </span>
                    )}
                  </button>
                </div>
              </div>

              <div className="w-full">
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                  Event Date
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="date"
                    id="date"
                    value={date}
                    onChange={(e) => setDate(e.target.value)}
                    className="pl-10 w-full p-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>
            </form>
          </div>

          {error && (
            <div className="p-3 text-red-600 bg-red-50 rounded-md flex items-start mb-6">
              <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          {result && (
            <div className="space-y-6">
              <div className="p-3 sm:p-4 bg-purple-50 rounded-md border border-purple-100">
                <h3 className="font-semibold text-base sm:text-lg text-purple-800 mb-2">Address Information</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="flex items-start">
                    <MapPin className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Address</p>
                      <p className="text-xs sm:text-sm text-gray-900 break-words">{result.address}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Home className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Property Type</p>
                      <p className="text-xs sm:text-sm text-gray-900">{result.propertyType}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <MapPin className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Council</p>
                      <p className="text-xs sm:text-sm text-gray-900">{result.lgaName}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <MapPin className="h-4 sm:h-5 w-4 sm:w-5 text-purple-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Zoning</p>
                      <p className="text-xs sm:text-sm text-gray-900">{result.zoneCode} - {result.zoneName}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-3 sm:p-4 bg-blue-50 rounded-md border border-blue-100">
                <h3 className="font-semibold text-base sm:text-lg text-blue-800 mb-2">Noise Restrictions</h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="flex items-start">
                    <Clock className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-xs sm:text-sm font-medium text-gray-700">Noise Curfew</p>
                      <p className="text-xs sm:text-sm text-gray-900">
                        {formatTime(result.curfewStart)} to {formatTime(result.curfewEnd)}
                      </p>
                    </div>
                  </div>

                  {result.bassRestrictionStart && (
                    <div className="flex items-start">
                      <Volume2 className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-gray-700">Bass Music Restriction</p>
                        <p className="text-xs sm:text-sm text-gray-900">
                          {formatTime(result.bassRestrictionStart)} to {formatTime(result.bassRestrictionEnd || '07:00:00')}
                        </p>
                      </div>
                    </div>
                  )}

                  {result.outdoorCutoff && (
                    <div className="flex items-start">
                      <Music className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-gray-700">Outdoor Music Cutoff</p>
                        <p className="text-xs sm:text-sm text-gray-900">
                          {formatTime(result.outdoorCutoff)}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {result.specialConditions && (
                  <div className="mt-3 sm:mt-4 p-2 sm:p-3 bg-blue-100 rounded-md">
                    <p className="text-xs sm:text-sm font-medium text-blue-800">Special Conditions</p>
                    <p className="text-xs sm:text-sm text-blue-700">{result.specialConditions}</p>
                  </div>
                )}
              </div>

              {result.recommendations && result.recommendations.length > 0 && (
                <div className="p-3 sm:p-4 bg-green-50 rounded-md border border-green-100">
                  <h3 className="font-semibold text-base sm:text-lg text-green-800 mb-2">Recommendations</h3>
                  <ul className="space-y-2">
                    {result.recommendations.slice(0, 5).map((rec: any, index: number) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="h-4 sm:h-5 w-4 sm:w-5 text-green-600 mt-0.5 mr-1 sm:mr-2 flex-shrink-0" />
                        <div>
                          <p className="text-xs sm:text-sm font-medium text-gray-700">{rec.time_period}</p>
                          <p className="text-xs sm:text-sm text-gray-900">{rec.recommendation}</p>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="text-xs sm:text-sm text-gray-500 flex items-center">
                <Info className="h-3 sm:h-4 w-3 sm:w-4 mr-1" />
                <span>
                  This information is provided as a guide only. Always check with your local council for specific regulations.
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Test Results Section */}
      <div className="mt-6 p-3 sm:p-4 bg-yellow-50 rounded-md border border-yellow-200">
        <h3 className="text-base sm:text-lg font-medium text-yellow-800 mb-2">Address Analysis Test Results</h3>
        <p className="mb-4 text-xs sm:text-sm">Verification of our enhanced address utilities with specific test cases</p>

        {testResults.map((test, index) => (
          <div key={index} className="mb-4 sm:mb-6 p-3 sm:p-4 bg-white rounded-md shadow-sm">
            <h4 className="font-medium text-gray-800 mb-2 text-sm sm:text-base break-words">{test.address}</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-700">LGA (Council)</p>
                <p className="text-xs sm:text-sm text-gray-900">{test.analysis.lgaName}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-700">Zoning</p>
                <p className="text-xs sm:text-sm text-gray-900">{test.analysis.zoneCode} - {test.analysis.zoneName}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-700">Property Type</p>
                <p className="text-xs sm:text-sm text-gray-900">{test.analysis.propertyType}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-700">Full Address</p>
                <p className="text-xs sm:text-sm text-gray-900 break-words">{test.analysis.fullAddress}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-3 sm:p-4 bg-purple-50 rounded-md border border-purple-200">
        <h3 className="text-base sm:text-lg font-medium text-purple-800 mb-2">About NSW Noise Regulations</h3>
        <p className="mb-3 sm:mb-4 text-xs sm:text-sm">Understanding the rules helps you plan better events</p>

        <h4 className="font-medium text-purple-700 mb-1 sm:mb-2 text-sm sm:text-base">How Noise Regulations Work</h4>
        <p className="mb-1 sm:mb-2 text-xs sm:text-sm">Noise regulations in NSW are determined by several factors:</p>
        <ul className="list-disc list-inside text-gray-700 space-y-0.5 sm:space-y-1 mb-3 sm:mb-4 text-xs sm:text-sm">
          <li>Property type (apartment, house, townhouse, commercial)</li>
          <li>Zoning (residential, commercial, industrial, mixed-use)</li>
          <li>Local Government Area (LGA) specific rules</li>
          <li>Day of the week (weekday vs weekend)</li>
          <li>Special events and holidays</li>
        </ul>

        <h4 className="font-medium text-purple-700 mb-1 sm:mb-2 text-sm sm:text-base">Enforcement & Penalties</h4>
        <p className="mb-1 sm:mb-2 text-xs sm:text-sm">Noise restrictions are enforced by:</p>
        <ul className="list-disc list-inside text-gray-700 space-y-0.5 sm:space-y-1 mb-3 sm:mb-4 text-xs sm:text-sm">
          <li>NSW Police (can issue noise abatement directions)</li>
          <li>Local Council Rangers (can issue warning notices and fines)</li>
          <li>Liquor & Gaming NSW (for licensed venues)</li>
          <li>NSW Environment Protection Authority (for complex cases)</li>
        </ul>
        <p className="text-xs text-gray-600 mb-3 sm:mb-4">Penalties for non-compliance can range from $200-$400 on-the-spot fines to up to $3,000 for repeat offenses.</p>

        <h4 className="font-medium text-purple-700 mb-1 sm:mb-2 text-sm sm:text-base">Tips for Being a Good Neighbor</h4>
        <ul className="list-disc list-inside text-gray-700 space-y-0.5 sm:space-y-1 text-xs sm:text-sm">
          <li>Inform your neighbors in advance about your event</li>
          <li>Provide them with your contact details</li>
          <li>Keep music at a reasonable volume, especially after curfew times</li>
          <li>Move activities indoors after outdoor cutoff times</li>
          <li>Monitor noise levels throughout your event</li>
          <li>Respond promptly to any noise complaints</li>
        </ul>
      </div>
    </div>
  );
};

export default NSWNoiseGuide;
