import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Calendar, 
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';

// Mock data for AI analytics
const mockAgentData = {
  totalInteractions: 1245,
  averageRating: 4.7,
  completionRate: 0.89,
  averageResponseTime: 1.2, // seconds
  topIntents: [
    { intent: 'venue_search', count: 423 },
    { intent: 'pricing_inquiry', count: 312 },
    { intent: 'booking_help', count: 287 },
    { intent: 'availability_check', count: 156 },
    { intent: 'cancellation_policy', count: 67 }
  ],
  dailyInteractions: [
    { date: '2023-08-01', count: 42 },
    { date: '2023-08-02', count: 38 },
    { date: '2023-08-03', count: 45 },
    { date: '2023-08-04', count: 51 },
    { date: '2023-08-05', count: 63 },
    { date: '2023-08-06', count: 58 },
    { date: '2023-08-07', count: 47 }
  ],
  agentTypes: [
    { type: 'sales', interactions: 645, avgRating: 4.8 },
    { type: 'host', interactions: 320, avgRating: 4.6 },
    { type: 'booking', interactions: 180, avgRating: 4.5 },
    { type: 'support', interactions: 100, avgRating: 4.7 }
  ],
  commonIssues: [
    { issue: 'Unclear venue availability', count: 87 },
    { issue: 'Pricing confusion', count: 65 },
    { issue: 'Booking process questions', count: 58 },
    { issue: 'Payment issues', count: 42 },
    { issue: 'Cancellation policy', count: 38 }
  ]
};

export default function AIAnalytics() {
  const [timeframe, setTimeframe] = useState('week');
  const [agentType, setAgentType] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState(mockAgentData);

  // Simulate loading data
  const loadData = () => {
    setIsLoading(true);
    setTimeout(() => {
      setAnalyticsData(mockAgentData);
      setIsLoading(false);
    }, 800);
  };

  useEffect(() => {
    loadData();
  }, [timeframe, agentType]);

  return (
    <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">AI Assistant Analytics</h1>
        <p className="mt-1 text-gray-600">Monitor and optimize your AI assistants' performance</p>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap gap-4">
        <div className="flex items-center">
          <span className="mr-2 text-sm font-medium text-gray-700">Timeframe:</span>
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
        </div>
        <div className="flex items-center">
          <span className="mr-2 text-sm font-medium text-gray-700">Agent Type:</span>
          <select
            value={agentType}
            onChange={(e) => setAgentType(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="all">All Agents</option>
            <option value="sales">Sales Assistant</option>
            <option value="host">Host Assistant</option>
            <option value="booking">Booking Assistant</option>
            <option value="support">Support Assistant</option>
          </select>
        </div>
        <button
          onClick={loadData}
          className="ml-auto flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </button>
        <button className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
          <Download className="mr-2 h-4 w-4" />
          Export
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : (
        <>
          {/* Summary cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-sm font-medium text-gray-500">Total Interactions</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">{analyticsData.totalInteractions}</p>
              <div className="mt-2 text-sm text-green-600">+12% from last period</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-sm font-medium text-gray-500">Average Rating</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">{analyticsData.averageRating}/5.0</p>
              <div className="mt-2 text-sm text-green-600">+0.2 from last period</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-sm font-medium text-gray-500">Completion Rate</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">{(analyticsData.completionRate * 100).toFixed(1)}%</p>
              <div className="mt-2 text-sm text-green-600">+3.5% from last period</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-sm font-medium text-gray-500">Avg. Response Time</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">{analyticsData.averageResponseTime}s</p>
              <div className="mt-2 text-sm text-green-600">-0.3s from last period</div>
            </div>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Daily Interactions</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-md">
                <p className="text-gray-500">Line chart visualization would go here</p>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Top Intents</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-md">
                <p className="text-gray-500">Bar chart visualization would go here</p>
              </div>
            </div>
          </div>

          {/* Agent performance comparison */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Agent Performance Comparison</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Agent Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Interactions
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg. Rating
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completion Rate
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg. Response Time
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {analyticsData.agentTypes.map((agent) => (
                    <tr key={agent.type}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 capitalize">
                        {agent.type} Assistant
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {agent.interactions}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {agent.avgRating}/5.0
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {Math.round(85 + Math.random() * 10)}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {(1 + Math.random()).toFixed(1)}s
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Common issues */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Common Issues</h3>
            <ul className="space-y-4">
              {analyticsData.commonIssues.map((issue, index) => (
                <li key={index} className="flex items-center justify-between">
                  <span className="text-gray-700">{issue.issue}</span>
                  <div className="flex items-center">
                    <div className="w-48 bg-gray-200 rounded-full h-2.5 mr-4">
                      <div 
                        className="bg-purple-600 h-2.5 rounded-full" 
                        style={{ width: `${(issue.count / analyticsData.commonIssues[0].count) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-gray-500 w-12 text-right">{issue.count}</span>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Improvement suggestions */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">AI Improvement Suggestions</h3>
            <div className="space-y-4">
              <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-md">
                <h4 className="font-medium text-yellow-800">Pricing Confusion</h4>
                <p className="mt-1 text-yellow-700">Users are frequently asking for clarification on pricing. Consider updating the pricing display on venue listings to make hourly rates more prominent.</p>
              </div>
              <div className="p-4 border border-blue-200 bg-blue-50 rounded-md">
                <h4 className="font-medium text-blue-800">Availability Questions</h4>
                <p className="mt-1 text-blue-700">Many users are asking about venue availability. Enhancing the calendar interface and making it more prominent could reduce these queries.</p>
              </div>
              <div className="p-4 border border-green-200 bg-green-50 rounded-md">
                <h4 className="font-medium text-green-800">Booking Process</h4>
                <p className="mt-1 text-green-700">Users are asking multiple questions during the booking process. Consider adding more inline help and tooltips to guide users through each step.</p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
