import { z } from 'zod';

// User profile schema
export const userProfileSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(3).max(50).optional(),
  full_name: z.string().min(2).max(100).optional(),
  avatar_url: z.string().url().optional().nullable(),
  bio: z.string().max(500).optional().nullable(),
  is_host: z.boolean().default(false),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

export type UserProfile = z.infer<typeof userProfileSchema>;

// Venue schema
export const venueSchema = z.object({
  id: z.string().uuid().optional(),
  title: z.string().min(5).max(100),
  description: z.string().min(20).max(2000),
  location: z.string().min(5).max(200),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }).optional(),
  price: z.number().positive(),
  capacity: z.number().int().positive(),
  amenities: z.array(z.string()).optional(),
  images: z.array(z.string().url()).min(1, "At least one image is required"),
  host_id: z.string().uuid(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

export type Venue = z.infer<typeof venueSchema>;

// Booking schema
export const bookingSchema = z.object({
  id: z.string().uuid().optional(),
  venue_id: z.string().uuid(),
  guest_id: z.string().uuid(),
  start_date: z.string().datetime(),
  end_date: z.string().datetime(),
  guests_count: z.number().int().positive(),
  total_price: z.number().positive(),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed']).default('pending'),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
}).refine(data => new Date(data.end_date) >= new Date(data.start_date), {
  message: "End date must be after start date",
  path: ["end_date"],
});

export type Booking = z.infer<typeof bookingSchema>;

// Review schema
export const reviewSchema = z.object({
  id: z.string().uuid().optional(),
  venue_id: z.string().uuid(),
  user_id: z.string().uuid(),
  rating: z.number().int().min(1).max(5),
  comment: z.string().max(1000).optional(),
  created_at: z.string().datetime().optional(),
});

export type Review = z.infer<typeof reviewSchema>;

// Message schema
export const messageSchema = z.object({
  id: z.string().uuid().optional(),
  booking_id: z.string().uuid().optional(),
  sender_id: z.string().uuid(),
  receiver_id: z.string().uuid(),
  content: z.string().min(1).max(2000),
  read: z.boolean().default(false),
  created_at: z.string().datetime().optional(),
});

export type Message = z.infer<typeof messageSchema>;

// Validation helper function
export function validateData<T>(schema: z.ZodType<T>, data: unknown): { success: true; data: T } | { success: false; errors: z.ZodError } {
  try {
    const validData = schema.parse(data);
    return { success: true, data: validData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
}
