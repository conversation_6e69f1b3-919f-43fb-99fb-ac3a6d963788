import { UserButton as ClerkU<PERSON><PERSON><PERSON><PERSON> } from '@clerk/clerk-react';
import { Link } from 'react-router-dom';
import { useAuth } from '@clerk/clerk-react';
import { clerkAppearance } from '../../utils/clerk-theme';
import { User, Building } from 'lucide-react';

export default function UserButton() {
  const { isSignedIn, isLoaded } = useAuth();

  if (!isLoaded) {
    return <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse"></div>;
  }

  if (isSignedIn) {
    return (
      <div className="flex items-center space-x-4">
        <Link to="/my-account">
          <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm flex items-center">
            <User className="w-4 h-4 mr-1.5 text-purple-600" />
            <span>My Account</span>
          </button>
        </Link>
        <ClerkUserButton
          appearance={clerkAppearance}
          afterSignOutUrl="/"
        />
      </div>
    );
  }

  // Not signed in - show sign in and sign up buttons
  console.log("UserButton: Not signed in, showing auth buttons");

  return (
    <div className="flex items-center space-x-3">
      <Link to="/login">
        <button className="btn-secondary flex items-center space-x-1 px-4 py-2 border border-purple-200">
          <User className="w-4 h-4" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to="/signup">
        <button className="btn-primary px-4 py-2">
          Sign Up
        </button>
      </Link>
    </div>
  );
}
