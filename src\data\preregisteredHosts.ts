// This file contains a list of pre-registered host emails
// These users will automatically be granted host privileges when they sign in

export const preregisteredHosts = [
  '<EMAIL>',
  // Add any other emails that should be pre-registered as hosts
];

/**
 * Gets all registered host emails, including those from localStorage
 */
export const getAllRegisteredHosts = (): string[] => {
  try {
    // Get any hosts registered through the UI
    const localStorageHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
    // Combine with pre-registered hosts
    return [...preregisteredHosts, ...localStorageHosts];
  } catch (err) {
    console.error('Error getting registered hosts from localStorage:', err);
    return preregisteredHosts;
  }
};

/**
 * Checks if an email is in the pre-registered hosts list or localStorage
 */
export const isPreregisteredHost = (email: string): boolean => {
  if (!email) return false;
  const lowerEmail = email.toLowerCase();

  // Check hardcoded list first
  if (preregisteredHosts.includes(lowerEmail)) {
    return true;
  }

  // Then check localStorage
  try {
    const localStorageHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
    return localStorageHosts.includes(lowerEmail);
  } catch (err) {
    console.error('Error checking localStorage for registered hosts:', err);
    return false;
  }
};
