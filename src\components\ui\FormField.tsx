import React from 'react';
import Input from './Input';

interface FormFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  name: string;
  error?: string;
  touched?: boolean;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  containerClassName?: string;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function FormField({
  label,
  name,
  error,
  touched,
  helperText,
  leftIcon,
  rightIcon,
  isLoading,
  containerClassName,
  onBlur,
  onChange,
  ...props
}: FormFieldProps) {
  // Only show error if the field has been touched
  const showError = touched && error ? error : undefined;
  
  return (
    <Input
      label={label}
      name={name}
      id={name}
      error={showError}
      helperText={helperText}
      leftIcon={leftIcon}
      rightIcon={rightIcon}
      isLoading={isLoading}
      containerClassName={containerClassName}
      onBlur={onBlur}
      onChange={onChange}
      {...props}
    />
  );
}
