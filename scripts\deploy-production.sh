#!/bin/bash

# HouseGoing Production Deployment Script
# This script securely builds and deploys the application to production

set -e  # Exit on any error

echo "🚀 Starting HouseGoing Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if .env.server exists
if [ ! -f ".env.server" ]; then
    print_error ".env.server file not found. Please create it with your production secrets."
    exit 1
fi

print_status "Validating environment..."

# Check for required environment variables
required_vars=("VITE_SUPABASE_URL" "VITE_SUPABASE_ANON_KEY" "VITE_CLERK_PUBLISHABLE_KEY" "VITE_STRIPE_PUBLISHABLE_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    printf '%s\n' "${missing_vars[@]}"
    exit 1
fi

print_success "Environment validation passed"

# Security checks
print_status "Running security checks..."

# Check for development artifacts
if grep -r "console.log" src/ --exclude-dir=node_modules --include="*.ts" --include="*.tsx" | grep -v "// Production:" | head -5; then
    print_warning "Found console.log statements in source code. Consider removing them for production."
fi

# Check for TODO/FIXME comments
if grep -r "TODO\|FIXME" src/ --exclude-dir=node_modules --include="*.ts" --include="*.tsx" | head -5; then
    print_warning "Found TODO/FIXME comments in source code."
fi

# Check for hardcoded secrets
if grep -r "sk_live\|sk_test" src/ --exclude-dir=node_modules --include="*.ts" --include="*.tsx"; then
    print_error "Found potential hardcoded secrets in source code!"
    exit 1
fi

print_success "Security checks passed"

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
rm -rf node_modules/.vite/

# Install dependencies
print_status "Installing dependencies..."
npm ci --production=false

# Run tests (if they exist)
if [ -f "package.json" ] && npm run | grep -q "test"; then
    print_status "Running tests..."
    npm run test
    print_success "Tests passed"
fi

# Build for production
print_status "Building for production..."
export NODE_ENV=production
npm run build

if [ ! -d "dist" ]; then
    print_error "Build failed - dist directory not created"
    exit 1
fi

print_success "Production build completed"

# Validate build output
print_status "Validating build output..."

# Check if critical files exist
critical_files=("dist/index.html" "dist/assets")
for file in "${critical_files[@]}"; do
    if [ ! -e "$file" ]; then
        print_error "Critical file missing: $file"
        exit 1
    fi
done

# Check build size
build_size=$(du -sh dist/ | cut -f1)
print_status "Build size: $build_size"

# Check for source maps (should not exist in production)
if find dist/ -name "*.map" | grep -q .; then
    print_warning "Source maps found in production build. Consider removing them for security."
fi

print_success "Build validation passed"

# Security hardening
print_status "Applying security hardening..."

# Remove any .env files from dist (just in case)
find dist/ -name ".env*" -delete 2>/dev/null || true

# Create security headers file for Netlify/Vercel
cat > dist/_headers << EOF
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(self)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://js.stripe.com https://clerk.housegoing.com.au; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https://*.supabase.co https://res.cloudinary.com; connect-src 'self' https://*.supabase.co https://api.stripe.com https://clerk.housegoing.com.au; font-src 'self' https://fonts.gstatic.com; frame-src https://js.stripe.com;

/api/*
  X-Robots-Tag: noindex
EOF

# Create robots.txt
cat > dist/robots.txt << EOF
User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /assets/

Sitemap: https://housegoing.com.au/sitemap.xml
EOF

print_success "Security hardening applied"

# Generate deployment summary
print_status "Generating deployment summary..."

cat > deployment-summary.txt << EOF
HouseGoing Production Deployment Summary
========================================
Date: $(date)
Build Size: $build_size
Node Environment: $NODE_ENV
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Not a git repository")

Security Measures Applied:
- Source maps disabled
- Development artifacts removed
- Security headers configured
- Environment variables validated
- Console logs checked
- Secret keys secured

Files Generated:
- dist/ (production build)
- dist/_headers (security headers)
- dist/robots.txt (SEO configuration)

Next Steps:
1. Upload dist/ folder to your hosting provider
2. Configure environment variables on the server
3. Set up SSL certificate
4. Configure domain DNS
5. Test the production deployment

Important Notes:
- Secret keys are NOT included in the build
- Configure server-side environment variables separately
- Ensure HTTPS is enabled for all production traffic
- Monitor application logs for any issues
EOF

print_success "Deployment summary generated"

# Final checks
print_status "Running final checks..."

# Check if index.html contains the app
if ! grep -q "root" dist/index.html; then
    print_error "index.html appears to be malformed"
    exit 1
fi

# Check if assets are properly generated
if [ ! -d "dist/assets" ] || [ -z "$(ls -A dist/assets)" ]; then
    print_error "Assets directory is empty or missing"
    exit 1
fi

print_success "Final checks passed"

echo ""
echo "🎉 Production deployment preparation completed successfully!"
echo ""
print_status "Deployment Summary:"
echo "  📁 Build output: ./dist/"
echo "  📊 Build size: $build_size"
echo "  🔒 Security: Hardened"
echo "  📋 Summary: ./deployment-summary.txt"
echo ""
print_status "Next steps:"
echo "  1. Upload the 'dist' folder to your hosting provider"
echo "  2. Configure server-side environment variables"
echo "  3. Set up SSL certificate and domain"
echo "  4. Test the production deployment"
echo ""
print_warning "Remember: Secret keys are NOT included in this build."
print_warning "Configure them separately on your hosting provider."
echo ""
print_success "Ready for production deployment! 🚀"
