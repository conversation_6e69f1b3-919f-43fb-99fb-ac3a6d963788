// Import mock data and types instead of actual Supabase client
// This is a temporary fix to avoid 500 errors
import { Message } from '../salesgpt/github-agent';

// Mock supabase client
const supabase = {
  from: () => ({
    insert: () => Promise.resolve({ data: { id: 'mock-id' }, error: null }),
    update: () => Promise.resolve({ error: null }),
    select: () => ({
      eq: () => Promise.resolve({ data: null, error: null }),
      single: () => Promise.resolve({ data: null, error: null }),
      order: () => ({
        range: () => Promise.resolve({ data: [], error: null, count: 0 })
      })
    }),
    rpc: () => ({})
  })
};

// Chat session interface
export interface ChatSession {
  id: string;
  userId: string | null;
  userEmail: string | null;
  startTime: Date;
  endTime: Date | null;
  status: 'active' | 'closed' | 'admin_intervention';
  adminId: string | null;
  feedback: ChatFeedback | null;
  messages: Message[];
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    referrer?: string;
    location?: string;
    [key: string]: any;
  };
}

// Chat feedback interface
export interface ChatFeedback {
  id: string;
  chatSessionId: string;
  adminId: string;
  rating: 1 | 2 | 3 | 4 | 5;
  feedbackText: string;
  categories: string[];
  createdAt: Date;
  improvements: {
    tone?: string;
    length?: string;
    accuracy?: string;
    helpfulness?: string;
    [key: string]: string | undefined;
  };
}

// Create a new chat session
export async function createChatSession(userId: string | null, userEmail: string | null, metadata: any = {}): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .insert({
        user_id: userId,
        user_email: userEmail,
        start_time: new Date().toISOString(),
        status: 'active',
        metadata
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating chat session:', error);
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Error creating chat session:', error);
    return null;
  }
}

// Save a message to a chat session
export async function saveMessage(sessionId: string, message: Message): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('chat_messages')
      .insert({
        session_id: sessionId,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp.toISOString()
      });

    if (error) {
      console.error('Error saving message:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error saving message:', error);
    return false;
  }
}

// Close a chat session
export async function closeChatSession(sessionId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('chat_sessions')
      .update({
        end_time: new Date().toISOString(),
        status: 'closed'
      })
      .eq('id', sessionId);

    if (error) {
      console.error('Error closing chat session:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error closing chat session:', error);
    return false;
  }
}

// Request admin intervention for a chat session
export async function requestAdminIntervention(sessionId: string, reason: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('chat_sessions')
      .update({
        status: 'admin_intervention',
        metadata: supabase.rpc('jsonb_set', {
          target: 'metadata',
          path: '{intervention_reason}',
          value: reason
        })
      })
      .eq('id', sessionId);

    if (error) {
      console.error('Error requesting admin intervention:', error);
      return false;
    }

    // Also create a notification for admins
    const { error: notifError } = await supabase
      .from('admin_notifications')
      .insert({
        type: 'chat_intervention',
        content: `Chat session ${sessionId} requires intervention: ${reason}`,
        is_read: false
      });

    if (notifError) {
      console.error('Error creating admin notification:', notifError);
    }

    return true;
  } catch (error) {
    console.error('Error requesting admin intervention:', error);
    return false;
  }
}

// Admin takes over a chat session
export async function adminTakeoverChat(sessionId: string, adminId: string): Promise<boolean> {
  try {
    console.log(`Mock admin takeover: Admin ${adminId} taking over session ${sessionId}`);
    return true;
  } catch (error) {
    console.error('Error with admin takeover:', error);
    return false;
  }
}

// Submit feedback for a chat session
export async function submitChatFeedback(
  chatSessionId: string,
  adminId: string,
  rating: 1 | 2 | 3 | 4 | 5,
  feedbackText: string,
  categories: string[] = [],
  improvements: Record<string, string> = {}
): Promise<boolean> {
  try {
    console.log('Mock feedback submission:', {
      chatSessionId,
      adminId,
      rating,
      feedbackText,
      categories,
      improvements
    });
    return true;
  } catch (error) {
    console.error('Error submitting chat feedback:', error);
    return false;
  }
}

// Get chat sessions (with pagination)
export async function getChatSessions(
  status?: 'active' | 'closed' | 'admin_intervention',
  page: number = 1,
  limit: number = 20
): Promise<{ sessions: ChatSession[], total: number }> {
  try {
    console.log('Using mock chat sessions for development');

    // Create mock sessions based on the requested status
    const mockSessions: ChatSession[] = [];

    // Add a mock active session
    if (!status || status === 'active') {
      mockSessions.push({
        id: 'mock-active-1',
        userId: 'user-1',
        userEmail: '<EMAIL>',
        startTime: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        endTime: null,
        status: 'active',
        adminId: null,
        feedback: null,
        messages: [
          {
            id: 'msg-1',
            role: 'human',
            content: 'Hello, I\'m looking for a venue for a birthday party',
            timestamp: new Date(Date.now() - 25 * 60 * 1000)
          },
          {
            id: 'msg-2',
            role: 'ai',
            content: 'Hi there! I\'d be happy to help you find the perfect venue for a birthday party. Could you tell me how many guests you\'re expecting and what area you\'re looking in?',
            timestamp: new Date(Date.now() - 24 * 60 * 1000)
          },
          {
            id: 'msg-3',
            role: 'human',
            content: 'About 30 people, looking in Sydney',
            timestamp: new Date(Date.now() - 20 * 60 * 1000)
          }
        ],
        metadata: {
          userAgent: 'Mozilla/5.0',
          referrer: 'https://housegoing.com.au'
        }
      });
    }

    // Add a mock admin intervention session
    if (!status || status === 'admin_intervention') {
      mockSessions.push({
        id: 'mock-intervention-1',
        userId: 'user-2',
        userEmail: '<EMAIL>',
        startTime: new Date(Date.now() - 120 * 60 * 1000), // 2 hours ago
        endTime: null,
        status: 'admin_intervention',
        adminId: null,
        feedback: null,
        messages: [
          {
            id: 'msg-4',
            role: 'human',
            content: 'I need help with a booking I made',
            timestamp: new Date(Date.now() - 115 * 60 * 1000)
          },
          {
            id: 'msg-5',
            role: 'ai',
            content: 'I\'d be happy to help with your booking. Could you provide your booking reference number?',
            timestamp: new Date(Date.now() - 114 * 60 * 1000)
          },
          {
            id: 'msg-6',
            role: 'human',
            content: 'I need to speak with a human, this is urgent',
            timestamp: new Date(Date.now() - 110 * 60 * 1000)
          }
        ],
        metadata: {
          userAgent: 'Mozilla/5.0',
          referrer: 'https://housegoing.com.au/bookings'
        }
      });
    }

    // Add a mock closed session
    if (!status || status === 'closed') {
      mockSessions.push({
        id: 'mock-closed-1',
        userId: 'user-3',
        userEmail: '<EMAIL>',
        startTime: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        endTime: new Date(Date.now() - 23 * 60 * 60 * 1000),
        status: 'closed',
        adminId: 'admin-1',
        feedback: {
          id: 'feedback-1',
          chatSessionId: 'mock-closed-1',
          adminId: 'admin-1',
          rating: 4,
          feedbackText: 'Good conversation but could have been more proactive',
          categories: ['Helpful', 'Too Long'],
          createdAt: new Date(Date.now() - 22 * 60 * 60 * 1000),
          improvements: {
            tone: 'More enthusiastic',
            length: 'More concise responses'
          }
        },
        messages: [
          {
            id: 'msg-7',
            role: 'human',
            content: 'Do you have any venues in Bondi?',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 5 * 60 * 1000)
          },
          {
            id: 'msg-8',
            role: 'ai',
            content: 'Yes, we have several venues in Bondi! Are you looking for something beachfront or more inland?',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 6 * 60 * 1000)
          },
          {
            id: 'msg-9',
            role: 'human',
            content: 'Beachfront would be nice',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 10 * 60 * 1000)
          },
          {
            id: 'msg-10',
            role: 'ai',
            content: 'Great choice! We have 3 beachfront properties in Bondi. The first is a modern apartment with panoramic views...',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 11 * 60 * 1000)
          }
        ],
        metadata: {
          userAgent: 'Mozilla/5.0',
          referrer: 'https://housegoing.com.au/venues'
        }
      });
    }

    return {
      sessions: mockSessions,
      total: mockSessions.length
    };
  } catch (error) {
    console.error('Error fetching chat sessions:', error);
    return { sessions: [], total: 0 };
  }
}

// Get a specific chat session by ID
export async function getChatSession(sessionId: string): Promise<ChatSession | null> {
  try {
    console.log('Using mock chat session for development');

    // Get all mock sessions
    const { sessions } = await getChatSessions();

    // Find the session with the matching ID
    const session = sessions.find(s => s.id === sessionId);

    if (!session) {
      console.log(`No mock session found with ID ${sessionId}`);
      return null;
    }

    return session;
  } catch (error) {
    console.error('Error fetching chat session:', error);
    return null;
  }
}

// For development/testing - mock storage functions
export const mockChatStorage = {
  sessions: new Map<string, ChatSession>(),

  createSession(): string {
    const sessionId = `session-${Date.now()}`;
    this.sessions.set(sessionId, {
      id: sessionId,
      userId: null,
      userEmail: null,
      startTime: new Date(),
      endTime: null,
      status: 'active',
      adminId: null,
      feedback: null,
      messages: [],
      metadata: {}
    });
    return sessionId;
  },

  saveMessage(sessionId: string, message: Message): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    session.messages.push(message);
    return true;
  },

  getSession(sessionId: string): ChatSession | null {
    return this.sessions.get(sessionId) || null;
  },

  closeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    session.endTime = new Date();
    session.status = 'closed';
    return true;
  },

  getAllSessions(): ChatSession[] {
    return Array.from(this.sessions.values());
  }
};
