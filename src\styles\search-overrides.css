/* Custom styles for search components */

/* Ensure dropdowns appear above the search bar */
.search-dropdown-container {
  position: relative;
  overflow: visible;
}

/* Style for the dropdown containers */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 12px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Ensure the search bar has proper positioning */
.search-bar-container {
  position: relative;
  overflow: visible;
}

/* Add a subtle animation to the dropdowns */
.search-dropdown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure the dropdowns are visible on mobile */
@media (max-width: 768px) {
  .search-dropdown {
    position: fixed !important;
    width: calc(100vw - 16px) !important;
    left: 8px !important;
    right: 8px !important;
    max-height: 60vh;
    overflow-y: auto;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .search-dropdown-container {
    overflow: visible !important;
  }

  .search-dropdown button,
  .search-dropdown a {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 16px;
  }
}

/* Ensure parent containers don't clip dropdowns */
.search-bar-container,
.search-dropdown-container,
.search-dropdown-container > * {
  overflow: visible !important;
}

/* Ensure dropdowns appear above all other content */
.search-dropdown,
.search-dropdown * {
  z-index: 10000 !important;
}

/* Fix any potential clipping issues */
body {
  overflow-x: hidden;
  overflow-y: auto;
}

/* Ensure the main content area doesn't clip dropdowns */
main,
section,
.hero-section {
  overflow: visible !important;
}
