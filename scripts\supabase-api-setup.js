#!/usr/bin/env node

/**
 * Direct Supabase API Integration Script for HouseGoing
 *
 * This script directly interacts with the Supabase API to create tables
 * without requiring manual SQL execution in the Supabase dashboard.
 *
 * It uses the service_role key to authenticate and execute SQL commands
 * via the Supabase REST API.
 */

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Supabase configuration - use environment variables
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://fxqoowlruissctsgbljk.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || (() => {
  console.error('SUPABASE_SERVICE_KEY environment variable is required');
  process.exit(1);
})();

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// SQL statements for database setup
const SQL_STATEMENTS = {
  // Create the updated_at trigger function
  createTriggerFunction: `
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    R<PERSON>URNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // Create user_profiles table
  createUserProfilesTable: `
    CREATE TABLE IF NOT EXISTS user_profiles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      clerk_id TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT NOT NULL DEFAULT 'guest',
      full_name TEXT,
      avatar_url TEXT,
      bio TEXT,
      phone TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON user_profiles(clerk_id);
    CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

    DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
    CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `,

  // Create venues table
  createVenuesTable: `
    CREATE TABLE IF NOT EXISTS venues (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      owner_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
      title TEXT NOT NULL,
      description TEXT,
      location TEXT NOT NULL,
      address TEXT NOT NULL,
      city TEXT NOT NULL,
      state TEXT NOT NULL,
      zip_code TEXT NOT NULL,
      coordinates JSONB,
      price DECIMAL(10,2) NOT NULL,
      capacity INTEGER NOT NULL,
      amenities JSONB,
      house_rules TEXT,
      images TEXT[],
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_venues_owner_id ON venues(owner_id);
    CREATE INDEX IF NOT EXISTS idx_venues_location ON venues(location);

    DROP TRIGGER IF EXISTS update_venues_updated_at ON venues;
    CREATE TRIGGER update_venues_updated_at
    BEFORE UPDATE ON venues
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `,

  // Create bookings table
  createBookingsTable: `
    CREATE TABLE IF NOT EXISTS bookings (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
      guest_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
      start_date TIMESTAMP WITH TIME ZONE NOT NULL,
      end_date TIMESTAMP WITH TIME ZONE NOT NULL,
      guests_count INTEGER NOT NULL,
      total_price DECIMAL(10,2) NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_bookings_venue_id ON bookings(venue_id);
    CREATE INDEX IF NOT EXISTS idx_bookings_guest_id ON bookings(guest_id);
    CREATE INDEX IF NOT EXISTS idx_bookings_dates ON bookings(start_date, end_date);

    DROP TRIGGER IF EXISTS update_bookings_updated_at ON bookings;
    CREATE TRIGGER update_bookings_updated_at
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `,

  // Create reviews table
  createReviewsTable: `
    CREATE TABLE IF NOT EXISTS reviews (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
      venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
      reviewer_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
      rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
      comment TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_reviews_booking_id ON reviews(booking_id);
    CREATE INDEX IF NOT EXISTS idx_reviews_venue_id ON reviews(venue_id);
    CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON reviews(reviewer_id);

    DROP TRIGGER IF EXISTS update_reviews_updated_at ON reviews;
    CREATE TRIGGER update_reviews_updated_at
    BEFORE UPDATE ON reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `,

  // Create messages table
  createMessagesTable: `
    CREATE TABLE IF NOT EXISTS messages (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
      sender_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
      recipient_id TEXT NOT NULL REFERENCES user_profiles(clerk_id),
      content TEXT NOT NULL,
      is_read BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_messages_booking_id ON messages(booking_id);
    CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
    CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON messages(recipient_id);

    DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
    CREATE TRIGGER update_messages_updated_at
    BEFORE UPDATE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `,

  // Enable RLS and create policies
  setupRLS: `
    -- Enable RLS on all tables
    ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE venues ENABLE ROW LEVEL SECURITY;
    ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
    ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
    ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

    -- Create RLS policies for user_profiles
    CREATE POLICY IF NOT EXISTS read_own_profile ON user_profiles
      FOR SELECT
      USING (auth.uid()::text = clerk_id);

    CREATE POLICY IF NOT EXISTS update_own_profile ON user_profiles
      FOR UPDATE
      USING (auth.uid()::text = clerk_id);

    CREATE POLICY IF NOT EXISTS service_read_all_profiles ON user_profiles
      FOR SELECT
      TO service_role
      USING (true);

    CREATE POLICY IF NOT EXISTS service_update_all_profiles ON user_profiles
      FOR UPDATE
      TO service_role
      USING (true);

    CREATE POLICY IF NOT EXISTS service_insert_profiles ON user_profiles
      FOR INSERT
      TO service_role
      WITH CHECK (true);

    -- Create RLS policies for venues
    CREATE POLICY IF NOT EXISTS read_venues ON venues
      FOR SELECT
      USING (true);

    CREATE POLICY IF NOT EXISTS insert_own_venues ON venues
      FOR INSERT
      WITH CHECK (owner_id = auth.uid()::text);

    CREATE POLICY IF NOT EXISTS update_own_venues ON venues
      FOR UPDATE
      USING (owner_id = auth.uid()::text);

    CREATE POLICY IF NOT EXISTS delete_own_venues ON venues
      FOR DELETE
      USING (owner_id = auth.uid()::text);

    -- Create RLS policies for bookings
    CREATE POLICY IF NOT EXISTS read_own_bookings ON bookings
      FOR SELECT
      USING (guest_id = auth.uid()::text OR EXISTS (
        SELECT 1 FROM venues WHERE venues.id = bookings.venue_id AND venues.owner_id = auth.uid()::text
      ));

    CREATE POLICY IF NOT EXISTS insert_bookings ON bookings
      FOR INSERT
      WITH CHECK (guest_id = auth.uid()::text);

    CREATE POLICY IF NOT EXISTS update_own_bookings ON bookings
      FOR UPDATE
      USING (guest_id = auth.uid()::text OR EXISTS (
        SELECT 1 FROM venues WHERE venues.id = bookings.venue_id AND venues.owner_id = auth.uid()::text
      ));
  `,

  // Insert pre-registered host
  insertPreregisteredHost: `
    INSERT INTO user_profiles (clerk_id, email, role, full_name)
    VALUES
      ('pre-registered-host-1', '<EMAIL>', 'host', 'Tom C')
    ON CONFLICT (email)
    DO UPDATE SET role = 'host', updated_at = NOW();
  `
};

/**
 * Execute SQL directly using the Supabase REST API
 * @param {string} sql - The SQL to execute
 * @param {string} description - Description of the operation for logging
 * @returns {Promise<boolean>} - True if successful, false otherwise
 */
async function executeSQL(sql, description) {
  console.log(`\n🔄 ${description}...`);

  try {
    // First try using the REST API directly
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/pg_query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify({ query: sql })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Error executing SQL: ${errorText}`);

      // Try alternative method using supabase-js client
      console.log('🔄 Trying alternative method...');

      const { error } = await supabase.rpc('pg_query', { query: sql });

      if (error) {
        console.error(`❌ Alternative method failed: ${error.message}`);
        return false;
      }
    }

    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Exception executing SQL: ${error.message}`);
    return false;
  }
}

/**
 * Check if a table exists in the database
 * @param {string} tableName - The name of the table to check
 * @returns {Promise<boolean>} - True if the table exists, false otherwise
 */
async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName);

    if (error) {
      console.error(`❌ Error checking if table ${tableName} exists: ${error.message}`);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error(`❌ Exception checking if table ${tableName} exists: ${error.message}`);
    return false;
  }
}

/**
 * Create the pg_query function if it doesn't exist
 * This function allows executing arbitrary SQL from the REST API
 */
async function createPgQueryFunction() {
  console.log('\n🔄 Creating pg_query function...');

  try {
    // Check if the function already exists
    const { data, error } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .eq('routine_name', 'pg_query');

    if (error) {
      console.error(`❌ Error checking if pg_query function exists: ${error.message}`);
    } else if (data && data.length > 0) {
      console.log('✅ pg_query function already exists');
      return true;
    }

    // Create the function using a direct query
    const sql = `
      CREATE OR REPLACE FUNCTION pg_query(query text)
      RETURNS JSONB
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE query;
        RETURN jsonb_build_object('success', true);
      EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object(
          'success', false,
          'error', SQLERRM
        );
      END;
      $$;

      -- Grant execute permission to authenticated users
      GRANT EXECUTE ON FUNCTION pg_query TO authenticated;
      GRANT EXECUTE ON FUNCTION pg_query TO anon;
      GRANT EXECUTE ON FUNCTION pg_query TO service_role;
    `;

    // Use direct fetch for this initial function creation
    const response = await fetch(`${SUPABASE_URL}/rest/v1/sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY,
        'Prefer': 'return=minimal'
      },
      body: sql
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Failed to create pg_query function: ${errorText}`);

      // Try an alternative approach using the REST API
      console.log('🔄 Trying alternative approach...');

      const altResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
          'apikey': SUPABASE_SERVICE_KEY
        },
        body: JSON.stringify({
          type: 'sql',
          query: sql
        })
      });

      if (!altResponse.ok) {
        const altErrorText = await altResponse.text();
        console.error(`❌ Alternative approach failed: ${altErrorText}`);
        return false;
      }
    }

    console.log('✅ pg_query function created successfully');
    return true;
  } catch (error) {
    console.error(`❌ Exception creating pg_query function: ${error.message}`);
    return false;
  }
}

/**
 * Verify that all tables were created
 * @returns {Promise<boolean>} - True if all tables exist, false otherwise
 */
async function verifyTables() {
  console.log('\n🔍 Verifying tables...');

  const tablesToVerify = [
    'user_profiles',
    'venues',
    'bookings',
    'reviews',
    'messages'
  ];

  try {
    // Check if each table exists
    const results = await Promise.all(
      tablesToVerify.map(async (tableName) => {
        const exists = await checkTableExists(tableName);
        return { tableName, exists };
      })
    );

    // Check if all tables exist
    const allTablesExist = results.every((result) => result.exists);

    if (allTablesExist) {
      console.log('✅ All tables verified successfully');

      // Log the status of each table
      results.forEach((result) => {
        console.log(`  ✓ Table ${result.tableName} exists`);
      });

      return true;
    } else {
      console.log('❌ Some tables are missing');

      // Log the status of each table
      results.forEach((result) => {
        if (result.exists) {
          console.log(`  ✓ Table ${result.tableName} exists`);
        } else {
          console.log(`  ✗ Table ${result.tableName} is missing`);
        }
      });

      return false;
    }
  } catch (error) {
    console.error(`❌ Exception verifying tables: ${error.message}`);
    return false;
  }
}

/**
 * Count rows in a table
 * @param {string} tableName - The name of the table to count rows in
 * @returns {Promise<number>} - The number of rows in the table
 */
async function countRows(tableName) {
  try {
    const { data, error, count } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error(`❌ Error counting rows in ${tableName}: ${error.message}`);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error(`❌ Exception counting rows in ${tableName}: ${error.message}`);
    return 0;
  }
}

/**
 * Verify pre-registered host
 * @returns {Promise<boolean>} - True if the pre-registered host exists, false otherwise
 */
async function verifyPreRegisteredHost() {
  console.log('\n🔍 Verifying pre-registered host...');

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .eq('role', 'host');

    if (error) {
      console.error(`❌ Failed to verify pre-registered host: ${error.message}`);
      return false;
    }

    if (data && data.length > 0) {
      console.log('✅ Pre-registered host verified successfully');
      console.log(`  Host details: ${JSON.stringify(data[0], null, 2)}`);
      return true;
    } else {
      console.log('❌ Pre-registered host not found');
      return false;
    }
  } catch (error) {
    console.error(`❌ Exception verifying pre-registered host: ${error.message}`);
    return false;
  }
}

/**
 * Main function to set up the database
 */
async function setupDatabase() {
  console.log('=== HouseGoing Supabase Database Setup ===');
  console.log(`URL: ${SUPABASE_URL}`);
  console.log('Service Role Key: [REDACTED]');
  console.log('=======================================');

  // Step 1: Test connection
  console.log('\n🔄 Testing connection to Supabase...');
  try {
    const { data, error } = await supabase.from('pg_catalog.pg_tables').select('*').limit(1);

    if (error) {
      console.error(`❌ Failed to connect to Supabase: ${error.message}`);
      return;
    }

    console.log('✅ Connected to Supabase successfully');
  } catch (error) {
    console.error(`❌ Exception connecting to Supabase: ${error.message}`);
    return;
  }

  // Step 2: Check if tables already exist
  const tablesExist = await verifyTables();
  if (tablesExist) {
    console.log('\n✅ All tables already exist. No need to create them.');

    // Verify pre-registered host
    await verifyPreRegisteredHost();

    // Count rows in each table
    console.log('\n📊 Table Row Counts:');
    for (const tableName of ['user_profiles', 'venues', 'bookings', 'reviews', 'messages']) {
      const count = await countRows(tableName);
      console.log(`  ${tableName}: ${count} rows`);
    }

    return;
  }

  // Step 3: Create pg_query function
  await createPgQueryFunction();

  // Step 4: Create tables and set up database
  const steps = [
    { sql: SQL_STATEMENTS.createTriggerFunction, description: 'Creating trigger function' },
    { sql: SQL_STATEMENTS.createUserProfilesTable, description: 'Creating user_profiles table' },
    { sql: SQL_STATEMENTS.createVenuesTable, description: 'Creating venues table' },
    { sql: SQL_STATEMENTS.createBookingsTable, description: 'Creating bookings table' },
    { sql: SQL_STATEMENTS.createReviewsTable, description: 'Creating reviews table' },
    { sql: SQL_STATEMENTS.createMessagesTable, description: 'Creating messages table' },
    { sql: SQL_STATEMENTS.setupRLS, description: 'Setting up Row Level Security' },
    { sql: SQL_STATEMENTS.insertPreregisteredHost, description: 'Inserting pre-registered host' }
  ];

  for (const step of steps) {
    const success = await executeSQL(step.sql, step.description);
    if (!success) {
      console.log(`⚠️ Warning: ${step.description} may have failed, but continuing with next steps...`);
    }
  }

  // Step 5: Verify tables were created
  const tablesVerified = await verifyTables();
  if (!tablesVerified) {
    console.log('❌ Failed to verify all tables. Something went wrong.');
  }

  // Step 6: Verify pre-registered host
  await verifyPreRegisteredHost();

  // Step 7: Count rows in each table
  console.log('\n📊 Table Row Counts:');
  for (const tableName of ['user_profiles', 'venues', 'bookings', 'reviews', 'messages']) {
    const count = await countRows(tableName);
    console.log(`  ${tableName}: ${count} rows`);
  }

  console.log('\n✅ Database setup completed!');
}

// Run the setup
setupDatabase().catch(error => {
  console.error(`❌ Unhandled exception: ${error.message}`);
  process.exit(1);
});
