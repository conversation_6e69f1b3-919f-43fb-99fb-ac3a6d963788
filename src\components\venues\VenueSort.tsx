import React from 'react';
import { ArrowUpDown } from 'lucide-react';

interface SortProps {
  onSortChange: (sortBy: string) => void;
}

export default function VenueSort({ onSortChange }: SortProps) {
  return (
    <div className="flex items-center gap-3">
      <ArrowUpDown className="h-5 w-5 text-gray-500" />
      <select
        onChange={(e) => onSortChange(e.target.value)}
        className="border-0 bg-transparent text-gray-600 focus:ring-0 cursor-pointer"
      >
        <option value="recommended">Recommended</option>
        <option value="price_low">Price: Low to High</option>
        <option value="price_high">Price: High to Low</option>
        <option value="rating">Highest Rated</option>
        <option value="capacity">Capacity</option>
      </select>
    </div>
  );
}