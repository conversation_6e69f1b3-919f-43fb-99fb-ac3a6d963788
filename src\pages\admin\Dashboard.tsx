import React, { useState, useEffect } from 'react';
import { useUserSafe } from '../../hooks/useClerkSafe';
import { Link } from 'react-router-dom';
import { MessageSquare, Bot, BarChart2, CheckCircle, TestTube, Building, Eye, Edit, MapPin, Users, DollarSign, Calendar } from 'lucide-react';
import AdminApprovalDashboard from './AdminApprovalDashboard';
import RealTimeAnalyticsDashboard from '../../components/admin/RealTimeAnalyticsDashboard';
import SuburbAnalytics from '../../components/admin/SuburbAnalytics';
import { getAllLiveVenues, getVenueManagementStats, LiveVenue } from '../../services/propertyDataService';
import { initializeAnalyticsSystem, getAnalyticsSystemStatus } from '../../services/initializeAnalytics';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';

interface AdminStats {
  totalUsers: number;
  totalHosts: number;
  totalVenues: number;
  totalBookings: number;
  totalRevenue: number;
  pendingApprovals: number;
  activeBookings: number;
  monthlyGrowth: number;
}

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  created_at: string;
  role?: string;
}

export default function AdminDashboard() {
  const { user, isLoaded } = useUserSafe();
  const [activeTab, setActiveTab] = useState<'approvals' | 'users' | 'venues' | 'analytics' | 'suburbs' | 'testing'>('approvals');
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [venues, setVenues] = useState<LiveVenue[]>([]);
  const [venueStats, setVenueStats] = useState({ total: 0, published: 0, unpublished: 0, active: 0 });
  const [loadingVenues, setLoadingVenues] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [adminStats, setAdminStats] = useState<AdminStats>({
    totalUsers: 0,
    totalHosts: 0,
    totalVenues: 0,
    totalBookings: 0,
    totalRevenue: 0,
    pendingApprovals: 0,
    activeBookings: 0,
    monthlyGrowth: 0
  });
  const [loadingStats, setLoadingStats] = useState(true);

  // Production authentication check
  if (!isLoaded) {
    return <div className="pt-32 flex justify-center">Loading...</div>;
  }

  if (!user) {
    return <div className="pt-32 flex justify-center">Please sign in to access the admin dashboard</div>;
  }

  // Admin email verification
  const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
  const userEmail = user.primaryEmailAddress?.emailAddress || '';
  const isAdmin = ADMIN_EMAILS.includes(userEmail.toLowerCase());

  if (!isAdmin) {
    return (
      <div className="pt-32 px-4 flex flex-col items-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-8">
          You don't have permission to access the admin dashboard.
        </p>
        <a href="/" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
          Return to Home
        </a>
      </div>
    );
  }

  // Load initial data
  useEffect(() => {
    loadAdminStats();
    loadUsers();
  }, []);

  // Load venues when Venues tab is selected
  useEffect(() => {
    if (activeTab === 'venues') {
      loadVenues();
    }
  }, [activeTab]);

  const loadAdminStats = async () => {
    try {
      setLoadingStats(true);

      // Fetch all statistics in parallel
      const [
        { data: usersData },
        { data: hostsData },
        { data: venuesData },
        { data: bookingsData },
        { data: pendingVenuesData }
      ] = await Promise.all([
        supabase.from('user_profiles').select('id').eq('role', 'customer'),
        supabase.from('host_profiles').select('id'),
        supabase.from('venues').select('id').eq('status', 'approved'),
        supabase.from('bookings').select('id, total_amount, status'),
        supabase.from('venues').select('id').eq('status', 'pending')
      ]);

      // Calculate revenue from completed bookings
      const completedBookings = bookingsData?.filter(b => b.status === 'completed') || [];
      const totalRevenue = completedBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);

      // Calculate active bookings (confirmed or in progress)
      const activeBookings = bookingsData?.filter(b => ['confirmed', 'in_progress'].includes(b.status))?.length || 0;

      setAdminStats({
        totalUsers: usersData?.length || 0,
        totalHosts: hostsData?.length || 0,
        totalVenues: venuesData?.length || 0,
        totalBookings: bookingsData?.length || 0,
        totalRevenue,
        pendingApprovals: pendingVenuesData?.length || 0,
        activeBookings,
        monthlyGrowth: 0 // TODO: Calculate monthly growth
      });

    } catch (error) {
      console.error('Error loading admin stats:', error);
      toast.error('Failed to load admin statistics');
    } finally {
      setLoadingStats(false);
    }
  };

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('id, email, first_name, last_name, created_at')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      setUsers(data || []);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    }
  };

  const loadVenues = async () => {
    try {
      setLoadingVenues(true);
      const venuesData = await getAllLiveVenues();
      const statsData = await getVenueManagementStats();

      setVenues(venuesData);
      setVenueStats(statsData);
      console.log('✅ Loaded venues:', venuesData.length);
    } catch (error) {
      console.error('Error loading venues:', error);
    } finally {
      setLoadingVenues(false);
    }
  };

  // Filter users based on search term and filters
  const filteredUsers = users.filter((user) => {
    const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim();
    const matchesSearch =
      fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  return (
    <div className="pt-32 px-4 max-w-7xl mx-auto">
      {/* Production Admin Dashboard */}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <div className="mt-4 md:mt-0">
          <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
            Admin
          </span>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b mb-6 overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium whitespace-nowrap flex items-center ${
            activeTab === 'approvals'
              ? 'text-purple-600 border-b-2 border-purple-600'
              : 'text-gray-600 hover:text-gray-800'
          }`}
          onClick={() => setActiveTab('approvals')}
        >
          <CheckCircle className="h-4 w-4 mr-1" />
          Approvals
        </button>
        <button
          className={`px-4 py-2 font-medium whitespace-nowrap ${
            activeTab === 'users'
              ? 'text-purple-600 border-b-2 border-purple-600'
              : 'text-gray-600 hover:text-gray-800'
          }`}
          onClick={() => setActiveTab('users')}
        >
          Users
        </button>
        <button
          className={`px-4 py-2 font-medium whitespace-nowrap ${
            activeTab === 'venues'
              ? 'text-purple-600 border-b-2 border-purple-600'
              : 'text-gray-600 hover:text-gray-800'
          }`}
          onClick={() => setActiveTab('venues')}
        >
          Venues
        </button>
        <button
          className={`px-4 py-2 font-medium whitespace-nowrap flex items-center ${
            activeTab === 'analytics'
              ? 'text-purple-600 border-b-2 border-purple-600'
              : 'text-gray-600 hover:text-gray-800'
          }`}
          onClick={() => {
            console.log('🔄 Analytics button clicked!');
            setActiveTab('analytics');
          }}
        >
          <BarChart2 className="h-4 w-4 mr-1" />
          Analytics
        </button>
        <button
          className={`px-4 py-2 font-medium whitespace-nowrap flex items-center ${
            activeTab === 'suburbs'
              ? 'text-purple-600 border-b-2 border-purple-600'
              : 'text-gray-600 hover:text-gray-800'
          }`}
          onClick={() => setActiveTab('suburbs')}
        >
          <MapPin className="h-4 w-4 mr-1" />
          Suburb Analytics
        </button>
        <button
          className={`px-4 py-2 font-medium whitespace-nowrap flex items-center ${
            activeTab === 'testing'
              ? 'text-purple-600 border-b-2 border-purple-600'
              : 'text-gray-600 hover:text-gray-800'
          }`}
          onClick={() => setActiveTab('testing')}
        >
          <TestTube className="h-4 w-4 mr-1" />
          Testing
        </button>
      </div>

      {/* Admin Statistics Overview */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Platform Overview</h2>
        {loadingStats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-white rounded-lg p-6 shadow-sm animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg p-6 shadow-sm border-l-4 border-blue-500">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{adminStats.totalUsers.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border-l-4 border-green-500">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-green-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Venues</p>
                  <p className="text-2xl font-bold text-gray-900">{adminStats.totalVenues.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border-l-4 border-purple-500">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-purple-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                  <p className="text-2xl font-bold text-gray-900">{adminStats.totalBookings.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border-l-4 border-yellow-500">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-yellow-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">${adminStats.totalRevenue.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Secondary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <p className="text-sm font-medium text-gray-600">Hosts</p>
            <p className="text-xl font-bold text-gray-900">{adminStats.totalHosts.toLocaleString()}</p>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
            <p className="text-xl font-bold text-orange-600">{adminStats.pendingApprovals.toLocaleString()}</p>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <p className="text-sm font-medium text-gray-600">Active Bookings</p>
            <p className="text-xl font-bold text-green-600">{adminStats.activeBookings.toLocaleString()}</p>
          </div>
        </div>
      </div>

      {/* Quick Access Notice */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-blue-800 font-semibold mb-2">🎯 Admin Portal - All Features Available via Tabs</h3>
        <p className="text-blue-700 text-sm">Use the tabs above to access: Property Approvals, User Management, Venue Management, Business Analytics, and Development Tools.</p>
      </div>

      {/* Approvals Tab */}
      {activeTab === 'approvals' && (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <AdminApprovalDashboard />
        </div>
      )}

      {/* Users Tab */}
      {activeTab === 'users' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex flex-col md:flex-row justify-between mb-6">
            <h2 className="text-xl font-semibold mb-4 md:mb-0">User Management</h2>
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
              Add New User
            </button>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Roles</option>
                <option value="user">User</option>
                <option value="host">Host</option>
                <option value="admin">Admin</option>
              </select>
            </div>
            <div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Users Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Name
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Email
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Role
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Created
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{user.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-gray-500">{user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          user.role === 'admin'
                            ? 'bg-purple-100 text-purple-800'
                            : user.role === 'host'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          user.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-500">
                      {user.createdAt}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button className="text-purple-600 hover:text-purple-900 mr-3">
                        Edit
                      </button>
                      <button className="text-red-600 hover:text-red-900">
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No users found matching your filters.
            </div>
          )}

          {/* Pagination */}
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-gray-500">
              Showing {filteredUsers.length} of {mockUsers.length} users
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-50">
                Previous
              </button>
              <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-50">
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Venues Tab */}
      {activeTab === 'venues' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Live Venue Management</h2>
            <button
              onClick={loadVenues}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm"
            >
              Refresh
            </button>
          </div>

          {/* Venue Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-600 mb-1">Total Venues</h3>
              <p className="text-2xl font-bold text-blue-900">{venueStats.total}</p>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-green-600 mb-1">Published</h3>
              <p className="text-2xl font-bold text-green-900">{venueStats.published}</p>
            </div>
            <div className="bg-yellow-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-yellow-600 mb-1">Unpublished</h3>
              <p className="text-2xl font-bold text-yellow-900">{venueStats.unpublished}</p>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-purple-600 mb-1">Active</h3>
              <p className="text-2xl font-bold text-purple-900">{venueStats.active}</p>
            </div>
          </div>

          {/* Venues List */}
          {loadingVenues ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
              <p className="ml-3 text-purple-600">Loading venues...</p>
            </div>
          ) : venues.length === 0 ? (
            <div className="text-center py-8">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No live venues found</p>
              <p className="text-sm text-gray-400 mt-2">
                Venues will appear here after properties are approved
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {venues.map((venue) => (
                <div key={venue.id} className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{venue.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          venue.is_published
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {venue.is_published ? 'Published' : 'Unpublished'}
                        </span>
                      </div>
                      <p className="text-gray-600 mb-2">{venue.address}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>Capacity: {venue.capacity} guests</span>
                        <span>Price: ${venue.price_per_hour}/hour</span>
                        <span>Host ID: {venue.host_id}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-md">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-md">
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <RealTimeAnalyticsDashboard />
      )}

      {/* Suburb Analytics Tab */}
      {activeTab === 'suburbs' && (
        <SuburbAnalytics />
      )}

      {/* Testing Tab */}
      {activeTab === 'testing' && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-6">Testing & Development Tools</h2>
          <div className="mb-4 px-3 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm">
            🔧 Development Mode - Testing tools for localhost development
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Email Testing */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center mb-2">
                <MessageSquare className="h-5 w-5 text-amber-600 mr-2" />
                <h4 className="font-semibold text-gray-900">Email Testing</h4>
              </div>
              <p className="text-gray-600 text-sm mb-3">Test email notifications and templates</p>
              <div className="space-y-2">
                <button className="w-full px-3 py-2 bg-amber-100 text-amber-800 rounded text-sm hover:bg-amber-200">
                  Test Approval Email
                </button>
                <button className="w-full px-3 py-2 bg-amber-100 text-amber-800 rounded text-sm hover:bg-amber-200">
                  Test Rejection Email
                </button>
                <button className="w-full px-3 py-2 bg-amber-100 text-amber-800 rounded text-sm hover:bg-amber-200">
                  Test Signup Email
                </button>
              </div>
            </div>

            {/* Database Testing */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center mb-2">
                <Bot className="h-5 w-5 text-green-600 mr-2" />
                <h4 className="font-semibold text-gray-900">Database Testing</h4>
              </div>
              <p className="text-gray-600 text-sm mb-3">Test database connections and queries</p>
              <div className="space-y-2">
                <button className="w-full px-3 py-2 bg-green-100 text-green-800 rounded text-sm hover:bg-green-200">
                  Test Supabase Connection
                </button>
                <button className="w-full px-3 py-2 bg-green-100 text-green-800 rounded text-sm hover:bg-green-200">
                  Test User Queries
                </button>
                <button className="w-full px-3 py-2 bg-green-100 text-green-800 rounded text-sm hover:bg-green-200">
                  Test Venue Queries
                </button>
              </div>
            </div>

            {/* Analytics Testing */}
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center mb-2">
                <TestTube className="h-5 w-5 text-blue-600 mr-2" />
                <h4 className="font-semibold text-gray-900">Analytics System</h4>
              </div>
              <p className="text-gray-600 text-sm mb-3">Initialize and test Alex Hormozi analytics</p>
              <div className="space-y-2">
                <button
                  onClick={async () => {
                    console.log('🚀 Initializing analytics system...');
                    const result = await initializeAnalyticsSystem();
                    console.log('Analytics initialization result:', result);
                    alert(result.success ? 'Analytics system initialized!' : 'Failed to initialize analytics');
                  }}
                  className="w-full px-3 py-2 bg-blue-100 text-blue-800 rounded text-sm hover:bg-blue-200"
                >
                  Initialize Analytics System
                </button>
                <button
                  onClick={async () => {
                    console.log('📊 Checking analytics status...');
                    const status = await getAnalyticsSystemStatus();
                    console.log('Analytics status:', status);
                    alert(`Events: ${status.eventCount}, Tables: ${status.tablesExist ? 'Yes' : 'No'}`);
                  }}
                  className="w-full px-3 py-2 bg-blue-100 text-blue-800 rounded text-sm hover:bg-blue-200"
                >
                  Check System Status
                </button>
                <button className="w-full px-3 py-2 bg-blue-100 text-blue-800 rounded text-sm hover:bg-blue-200">
                  View Sample Events
                </button>
              </div>
            </div>
          </div>

          {/* Development Status */}
          <div className="mt-6 bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Development Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-green-600">✅ Admin Portal:</span> Working
              </div>
              <div>
                <span className="text-green-600">✅ Property Approvals:</span> Working
              </div>
              <div>
                <span className="text-green-600">✅ Analytics Dashboard:</span> Working
              </div>
              <div>
                <span className="text-green-600">✅ Venue Management:</span> Working
              </div>
              <div>
                <span className="text-yellow-600">⚠️ Email System:</span> Localhost mode
              </div>
              <div>
                <span className="text-yellow-600">⚠️ Authentication:</span> Development bypass
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
