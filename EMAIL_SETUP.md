# Email Notifications Setup Guide

This guide will help you set up email notifications for messages and reviews using Brevo (formerly Sendinblue).

## 🔧 **Environment Variables Setup**

Add these variables to your `.env` file:

```env
# Brevo Email Service
REACT_APP_BREVO_API_KEY=your_brevo_api_key_here

# Email Configuration
REACT_APP_FROM_EMAIL=<EMAIL>
REACT_APP_FROM_NAME=HouseGoing
```

## 📧 **Brevo API Key Setup**

### Step 1: Get Your Brevo API Key

1. **Go to Brevo Dashboard**: https://app.brevo.com/
2. **Login** with your account (<EMAIL>)
3. **Go to API Keys**: Settings → API Keys
4. **Create New API Key**:
   - Name: "HouseGoing Notifications"
   - Permissions: "Send emails"
5. **Copy the API key** and add it to your `.env` file

### Step 2: Verify Domain (if not done already)

1. **Go to Senders & IP**: Settings → Senders & IP
2. **Add Domain**: housegoing.com.au
3. **Follow DNS verification steps**
4. **Verify the domain**

## 🚀 **How Email Notifications Work**

### **Message Notifications**

When a user sends a message:

```javascript
// In MessagesSection.tsx
await sendMessage(
  userId,           // Sender ID
  receiverId,       // Receiver ID  
  messageContent,   // Message text
  bookingId,        // Optional booking ID
  senderName,       // Sender's name for email
  venueName         // Venue name if booking-related
);
```

This automatically:
1. ✅ Stores message in database
2. ✅ Checks if receiver wants email notifications
3. ✅ Sends beautiful HTML email via Brevo
4. ✅ Creates in-app notification
5. ✅ Handles errors gracefully

### **Review Notifications**

When a user submits a review:

```javascript
// In review submission
await submitReview(
  bookingId,        // Booking ID
  reviewerId,       // Reviewer ID
  revieweeId,       // Person being reviewed
  rating,           // 1-5 stars
  comment,          // Review text
  type,             // 'guest_to_host' or 'host_to_guest'
  reviewerName,     // Reviewer's name for email
  venueName         // Venue name
);
```

This automatically:
1. ✅ Stores review in database
2. ✅ Checks if reviewee wants email notifications
3. ✅ Sends beautiful HTML email with star rating
4. ✅ Creates in-app notification
5. ✅ Handles errors gracefully

## 📱 **Email Templates**

### **Message Email Template**
- **Subject**: "New message from [Sender Name] - HouseGoing"
- **Content**: Professional HTML email with message preview
- **CTA**: "View Message" button linking to My Account
- **Branding**: HouseGoing colors and logo

### **Review Email Template**
- **Subject**: "New [X]-star review received - HouseGoing"
- **Content**: HTML email with star rating display
- **CTA**: "View Review" button linking to My Account
- **Branding**: Consistent with HouseGoing design

### **Booking Message Template**
- **Subject**: "Message about your booking - HouseGoing"
- **Content**: Special template for booking-related messages
- **Info**: Shows venue name and booking details
- **CTA**: "Reply Now" for urgent booking matters

## ⚙️ **User Preferences**

Users can control email notifications in **My Account → Preferences**:

```sql
-- User preferences table
user_preferences (
  user_id TEXT,
  email_notifications BOOLEAN DEFAULT TRUE,  -- Controls all emails
  marketing_emails BOOLEAN DEFAULT FALSE,    -- Marketing emails
  sms_notifications BOOLEAN DEFAULT FALSE    -- Future SMS feature
)
```

### **Preference Controls**
- ✅ **Email Notifications**: On/Off for all notifications
- ✅ **Marketing Emails**: Separate control for promotional emails
- ✅ **Unsubscribe Links**: In every email footer
- ✅ **Preference Management**: Direct link in emails

## 🔒 **Security & Privacy**

### **Data Protection**
- ✅ **No sensitive data** in emails (no passwords, payment info)
- ✅ **Secure API calls** to Brevo with API key
- ✅ **User consent** required for email notifications
- ✅ **Easy unsubscribe** process

### **Error Handling**
- ✅ **Graceful failures**: If email fails, core function still works
- ✅ **Logging**: All email attempts logged for debugging
- ✅ **Fallback**: In-app notifications always work
- ✅ **Rate limiting**: Prevents spam

## 📊 **Testing Email Notifications**

### **Test Message Notification**
1. **Login** to your account
2. **Go to My Account → Messages**
3. **Send a test message** to another user
4. **Check email** for notification

### **Test Review Notification**
1. **Complete a booking**
2. **Submit a review**
3. **Check host's email** for notification

### **Test Email Templates**
```javascript
// Test in browser console
await sendMessageNotification(
  'sender_id',
  'receiver_id', 
  'Test message content',
  'Test Sender',
  null,
  'Test Venue'
);
```

## 🎨 **Email Design Features**

### **Professional Design**
- ✅ **Responsive HTML** templates
- ✅ **HouseGoing branding** and colors
- ✅ **Mobile-friendly** design
- ✅ **Clear call-to-action** buttons

### **Content Features**
- ✅ **Message preview** (first 100 characters)
- ✅ **Star rating display** for reviews
- ✅ **Venue information** when relevant
- ✅ **Booking context** for booking-related messages

### **Footer Information**
- ✅ **Company branding**
- ✅ **Unsubscribe link**
- ✅ **Preference management**
- ✅ **Legal compliance**

## 🚨 **Troubleshooting**

### **Emails Not Sending**
1. **Check API key** in environment variables
2. **Verify Brevo account** is active
3. **Check domain verification** in Brevo
4. **Review console logs** for errors

### **Users Not Receiving Emails**
1. **Check spam folder**
2. **Verify email preferences** in user account
3. **Check email address** is correct
4. **Test with different email provider**

### **Email Template Issues**
1. **Check HTML rendering** in email clients
2. **Test responsive design** on mobile
3. **Verify links** are working correctly
4. **Check image loading**

## 📈 **Analytics & Monitoring**

### **Email Metrics** (Available in Brevo)
- ✅ **Delivery rates**
- ✅ **Open rates**
- ✅ **Click-through rates**
- ✅ **Bounce rates**

### **Custom Tracking**
- ✅ **Notification success/failure** logging
- ✅ **User preference** analytics
- ✅ **Email engagement** tracking
- ✅ **Performance monitoring**

## 🎯 **Next Steps**

1. **Add Brevo API key** to environment variables
2. **Test email notifications** with real accounts
3. **Customize email templates** if needed
4. **Monitor email delivery** and engagement
5. **Set up SMS notifications** (future enhancement)

The email notification system is now **fully integrated** and ready for production use! 🎉
