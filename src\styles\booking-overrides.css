/* Booking page specific styles */

/* Fix header overlap issues */
@media (max-width: 768px) {
  .booking-page-header {
    position: relative;
    top: 0;
    z-index: 20;
  }
  
  .booking-page-content {
    padding-top: 0;
  }
}

/* Improve touch targets on mobile */
@media (max-width: 640px) {
  .booking-button {
    min-height: 48px;
    font-size: 16px;
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.15s ease;
  }

  .booking-button:active {
    transform: scale(0.98);
    background-color: rgba(139, 92, 246, 0.9);
  }

  .booking-input,
  .booking-select {
    min-height: 48px;
    font-size: 16px;
    padding: 12px 16px;
    border-radius: 8px;
  }

  .booking-input:focus,
  .booking-select:focus {
    border-color: #8B5CF6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    outline: none;
  }

  .booking-label {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }
}

/* Enhance visual hierarchy */
.booking-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4B0082;
}

.booking-subtitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.booking-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f9fafb;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.booking-info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.booking-info-icon {
  flex-shrink: 0;
  margin-right: 0.75rem;
  color: #7e22ce;
}

.booking-info-content {
  flex: 1;
}

.booking-info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25rem;
}

.booking-info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

/* Accessibility improvements */
.booking-button:focus,
.booking-input:focus,
.booking-select:focus {
  outline: 2px solid #7e22ce;
  outline-offset: 2px;
}

/* Animation for step transitions */
.booking-step-transition {
  transition: all 0.3s ease-in-out;
}

.booking-step-enter {
  opacity: 0;
  transform: translateY(10px);
}

.booking-step-enter-active {
  opacity: 1;
  transform: translateY(0);
}

.booking-step-exit {
  opacity: 1;
  transform: translateY(0);
}

.booking-step-exit-active {
  opacity: 0;
  transform: translateY(-10px);
}
