import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Settings, X, Plus, Save, AlertCircle } from 'lucide-react';
import {
  getVenueAvailabilitySettings,
  updateVenueAvailabilitySettings,
  getVenueOperatingHours,
  updateVenueOperatingHours,
  getVenueDayAvailability,
  updateVenueDayAvailability,
  batchUpdateDayAvailability,
  getVenueBlockedSlots,
  addVenueBlockedSlot,
  removeVenueBlockedSlot,
  VenueAvailabilitySettings,
  VenueOperatingHours,
  VenueDayAvailability,
  VenueBlockedSlot
} from '../../api/availability';

interface AvailabilityManagerProps {
  venueId: string;
  venueName: string;
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday', short: 'Sun' },
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' }
];

export default function AvailabilityManager({ venueId, venueName }: AvailabilityManagerProps) {
  const [activeTab, setActiveTab] = useState<'general' | 'hours' | 'calendar' | 'blocked'>('general');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // State for different availability settings
  const [settings, setSettings] = useState<VenueAvailabilitySettings | null>(null);
  const [operatingHours, setOperatingHours] = useState<VenueOperatingHours[]>([]);
  const [dayAvailability, setDayAvailability] = useState<VenueDayAvailability[]>([]);
  const [blockedSlots, setBlockedSlots] = useState<VenueBlockedSlot[]>([]);

  // Calendar state
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [calendarMonth, setCalendarMonth] = useState(new Date());

  useEffect(() => {
    loadAvailabilityData();
  }, [venueId]);

  const loadAvailabilityData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all availability data
      const [settingsData, hoursData, dayData, blockedData] = await Promise.all([
        getVenueAvailabilitySettings(venueId),
        getVenueOperatingHours(venueId),
        getVenueDayAvailability(venueId,
          new Date().toISOString().split('T')[0],
          new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        ),
        getVenueBlockedSlots(venueId,
          new Date().toISOString(),
          new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString()
        )
      ]);

      setSettings(settingsData || {
        venue_id: venueId,
        available_days: [1, 2, 3, 4, 5, 6, 0], // All days
        default_start_time: '09:00:00',
        default_end_time: '23:00:00',
        min_booking_hours: 4,
        max_booking_hours: 12,
        lead_time_hours: 24,
        max_advance_days: 365,
        instant_booking_enabled: false
      });

      setOperatingHours(hoursData);
      setDayAvailability(dayData);
      setBlockedSlots(blockedData);
    } catch (err) {
      console.error('Error loading availability data:', err);
      setError('Failed to load availability data');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      setError(null);

      await updateVenueAvailabilitySettings(settings);
      setSuccess('Settings saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const saveOperatingHours = async () => {
    try {
      setSaving(true);
      setError(null);

      await updateVenueOperatingHours(venueId, operatingHours);
      setSuccess('Operating hours saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error saving operating hours:', err);
      setError('Failed to save operating hours');
    } finally {
      setSaving(false);
    }
  };

  const toggleDayAvailability = (day: number) => {
    if (!settings) return;

    const newAvailableDays = settings.available_days.includes(day)
      ? settings.available_days.filter(d => d !== day)
      : [...settings.available_days, day];

    setSettings({
      ...settings,
      available_days: newAvailableDays
    });
  };

  const updateOperatingHour = (dayOfWeek: number, field: keyof VenueOperatingHours, value: any) => {
    const existingHour = operatingHours.find(h => h.day_of_week === dayOfWeek);

    if (existingHour) {
      setOperatingHours(operatingHours.map(h =>
        h.day_of_week === dayOfWeek ? { ...h, [field]: value } : h
      ));
    } else {
      setOperatingHours([...operatingHours, {
        venue_id: venueId,
        day_of_week: dayOfWeek,
        start_time: '09:00:00',
        end_time: '23:00:00',
        is_available: true,
        [field]: value
      }]);
    }
  };

  const blockDate = async (date: string, reason: string = 'Owner blocked') => {
    try {
      const startDatetime = `${date}T00:00:00Z`;
      const endDatetime = `${date}T23:59:59Z`;

      await addVenueBlockedSlot({
        venue_id: venueId,
        start_datetime: startDatetime,
        end_datetime: endDatetime,
        reason,
        block_type: 'manual',
        is_recurring: false
      });

      await loadAvailabilityData(); // Reload data
      setSuccess('Date blocked successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error blocking date:', err);
      setError('Failed to block date');
    }
  };

  const unblockSlot = async (slotId: string) => {
    try {
      await removeVenueBlockedSlot(slotId);
      await loadAvailabilityData(); // Reload data
      setSuccess('Time slot unblocked successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error unblocking slot:', err);
      setError('Failed to unblock time slot');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2">Loading availability settings...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          <Calendar className="h-6 w-6 mr-2 text-purple-600" />
          Availability Management
        </h2>
        <p className="text-gray-600 mt-1">Manage when your venue "{venueName}" is available for bookings</p>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {success && (
        <div className="mx-6 mt-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center">
          <div className="h-5 w-5 bg-green-500 rounded-full mr-2 flex items-center justify-center">
            <div className="h-2 w-2 bg-white rounded-full"></div>
          </div>
          <span className="text-green-700">{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'general', label: 'General Settings', icon: Settings },
            { id: 'hours', label: 'Operating Hours', icon: Clock },
            { id: 'calendar', label: 'Calendar View', icon: Calendar },
            { id: 'blocked', label: 'Blocked Times', icon: X }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'general' && settings && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">General Availability Settings</h3>

            {/* Available Days */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Available Days</label>
              <div className="grid grid-cols-7 gap-2">
                {DAYS_OF_WEEK.map(day => (
                  <button
                    key={day.value}
                    onClick={() => toggleDayAvailability(day.value)}
                    className={`p-3 text-center rounded-lg border-2 transition-colors ${
                      settings.available_days.includes(day.value)
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 bg-gray-50 text-gray-500'
                    }`}
                  >
                    <div className="font-medium">{day.short}</div>
                    <div className="text-xs">{day.label}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Default Hours */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Default Start Time</label>
                <input
                  type="time"
                  value={settings.default_start_time}
                  onChange={(e) => setSettings({...settings, default_start_time: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Default End Time</label>
                <input
                  type="time"
                  value={settings.default_end_time}
                  onChange={(e) => setSettings({...settings, default_end_time: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>

            {/* Booking Duration */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Minimum Booking (hours)</label>
                <input
                  type="number"
                  min="1"
                  max="24"
                  value={settings.min_booking_hours}
                  onChange={(e) => setSettings({...settings, min_booking_hours: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Maximum Booking (hours)</label>
                <input
                  type="number"
                  min="1"
                  max="48"
                  value={settings.max_booking_hours}
                  onChange={(e) => setSettings({...settings, max_booking_hours: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>

            {/* Lead Time and Advance Booking */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Lead Time Required (hours)</label>
                <input
                  type="number"
                  min="0"
                  max="168"
                  value={settings.lead_time_hours}
                  onChange={(e) => setSettings({...settings, lead_time_hours: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Advance Booking (days)</label>
                <input
                  type="number"
                  min="1"
                  max="730"
                  value={settings.max_advance_days}
                  onChange={(e) => setSettings({...settings, max_advance_days: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>

            {/* Instant Booking */}
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.instant_booking_enabled}
                  onChange={(e) => setSettings({...settings, instant_booking_enabled: e.target.checked})}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Enable instant booking (guests can book without approval)</span>
              </label>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={saveSettings}
                disabled={saving}
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Settings
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'hours' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Operating Hours</h3>
            <p className="text-gray-600">Set specific operating hours for each day of the week. Leave blank to use default hours.</p>

            <div className="space-y-4">
              {DAYS_OF_WEEK.map(day => {
                const hours = operatingHours.find(h => h.day_of_week === day.value);
                return (
                  <div key={day.value} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                    <div className="w-24">
                      <span className="font-medium text-gray-900">{day.label}</span>
                    </div>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={hours?.is_available !== false}
                        onChange={(e) => updateOperatingHour(day.value, 'is_available', e.target.checked)}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Available</span>
                    </label>

                    {hours?.is_available !== false && (
                      <>
                        <div>
                          <input
                            type="time"
                            value={hours?.start_time || '09:00:00'}
                            onChange={(e) => updateOperatingHour(day.value, 'start_time', e.target.value)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                          />
                        </div>
                        <span className="text-gray-500">to</span>
                        <div>
                          <input
                            type="time"
                            value={hours?.end_time || '23:00:00'}
                            onChange={(e) => updateOperatingHour(day.value, 'end_time', e.target.value)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                          />
                        </div>
                      </>
                    )}
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end">
              <button
                onClick={saveOperatingHours}
                disabled={saving}
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Operating Hours
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'calendar' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Calendar View</h3>
            <p className="text-gray-600">Click on dates to block or unblock them. Green = available, Red = blocked, Gray = unavailable.</p>

            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">Calendar functionality will be implemented here with date picker and visual availability display.</p>
              <p className="text-sm text-gray-600 mt-2">This will show a monthly calendar where owners can click dates to block/unblock them.</p>
            </div>
          </div>
        )}

        {activeTab === 'blocked' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Blocked Time Slots</h3>

            {blockedSlots.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <X className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No blocked time slots</p>
              </div>
            ) : (
              <div className="space-y-3">
                {blockedSlots.map(slot => (
                  <div key={slot.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">
                        {new Date(slot.start_datetime).toLocaleDateString()} - {new Date(slot.end_datetime).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-600">
                        {new Date(slot.start_datetime).toLocaleTimeString()} - {new Date(slot.end_datetime).toLocaleTimeString()}
                      </div>
                      {slot.reason && (
                        <div className="text-sm text-gray-500 mt-1">{slot.reason}</div>
                      )}
                    </div>
                    <button
                      onClick={() => slot.id && unblockSlot(slot.id)}
                      className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200"
                    >
                      Unblock
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
