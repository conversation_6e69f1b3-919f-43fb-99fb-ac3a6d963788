#!/usr/bin/env node

/**
 * SEO Monitoring and Improvement Script for HouseGoing
 * 
 * This script helps monitor and improve SEO indexing by:
 * 1. Checking sitemap accessibility
 * 2. Validating all URLs in sitemap
 * 3. Generating SEO reports
 * 4. Suggesting improvements
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const publicDir = path.resolve(__dirname, '../public');

// Base URL for the website
const baseUrl = 'https://housegoing.com.au';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, 'bold');
  console.log('='.repeat(60));
}

// Check if sitemap files exist and are accessible
function checkSitemapFiles() {
  logHeader('CHECKING SITEMAP FILES');
  
  const sitemapFiles = [
    'sitemap.xml',
    'sitemap_index.xml',
    'sitemap_main.xml',
    'sitemap_blog.xml',
    'sitemap_venues.xml'
  ];

  let allFilesExist = true;

  for (const file of sitemapFiles) {
    const filePath = path.join(publicDir, file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      log(`✓ ${file} exists (${sizeKB} KB)`, 'green');
    } else {
      log(`✗ ${file} missing`, 'red');
      allFilesExist = false;
    }
  }

  return allFilesExist;
}

// Count URLs in sitemap files
function countSitemapUrls() {
  logHeader('SITEMAP URL ANALYSIS');
  
  const sitemapFiles = [
    { file: 'sitemap_main.xml', description: 'Static pages' },
    { file: 'sitemap_blog.xml', description: 'Blog posts' },
    { file: 'sitemap_venues.xml', description: 'Venue pages' }
  ];

  let totalUrls = 0;

  for (const { file, description } of sitemapFiles) {
    const filePath = path.join(publicDir, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const urlCount = (content.match(/<loc>/g) || []).length;
      totalUrls += urlCount;
      log(`${description}: ${urlCount} URLs`, 'blue');
    }
  }

  log(`\nTotal URLs in all sitemaps: ${totalUrls}`, 'bold');
  return totalUrls;
}

// Generate SEO recommendations
function generateSeoRecommendations() {
  logHeader('SEO RECOMMENDATIONS');
  
  const recommendations = [
    {
      priority: 'HIGH',
      action: 'Submit updated sitemap to Google Search Console',
      reason: 'Ensure Google knows about all your pages'
    },
    {
      priority: 'HIGH',
      action: 'Add structured data (JSON-LD) to venue pages',
      reason: 'Improve rich snippets in search results'
    },
    {
      priority: 'MEDIUM',
      action: 'Create individual blog post pages with unique URLs',
      reason: 'Currently blog posts may not be individually indexable'
    },
    {
      priority: 'MEDIUM',
      action: 'Add canonical URLs to all pages',
      reason: 'Prevent duplicate content issues'
    },
    {
      priority: 'MEDIUM',
      action: 'Optimize meta descriptions for all pages',
      reason: 'Improve click-through rates from search results'
    },
    {
      priority: 'LOW',
      action: 'Add hreflang tags if targeting multiple regions',
      reason: 'Help Google understand geographic targeting'
    }
  ];

  recommendations.forEach((rec, index) => {
    const priorityColor = rec.priority === 'HIGH' ? 'red' : 
                         rec.priority === 'MEDIUM' ? 'yellow' : 'blue';
    
    log(`\n${index + 1}. [${rec.priority}] ${rec.action}`, priorityColor);
    log(`   Reason: ${rec.reason}`, 'reset');
  });
}

// Check robots.txt
function checkRobotsTxt() {
  logHeader('ROBOTS.TXT ANALYSIS');
  
  const robotsPath = path.join(publicDir, 'robots.txt');
  if (fs.existsSync(robotsPath)) {
    const content = fs.readFileSync(robotsPath, 'utf8');
    const sitemapCount = (content.match(/Sitemap:/g) || []).length;
    
    log(`✓ robots.txt exists`, 'green');
    log(`✓ Contains ${sitemapCount} sitemap references`, 'green');
    
    // Check for important directives
    if (content.includes('Allow: /')) {
      log(`✓ Allows crawling of main content`, 'green');
    }
    
    if (content.includes('Disallow: /admin/')) {
      log(`✓ Blocks admin areas`, 'green');
    }
    
    if (content.includes('Disallow: /api/')) {
      log(`✓ Blocks API endpoints`, 'green');
    }
    
  } else {
    log(`✗ robots.txt missing`, 'red');
  }
}

// Generate indexing improvement suggestions
function generateIndexingTips() {
  logHeader('INDEXING IMPROVEMENT TIPS');
  
  const tips = [
    'Submit sitemap to Google Search Console and Bing Webmaster Tools',
    'Use Google Search Console to request indexing of important pages',
    'Ensure all pages load quickly (under 3 seconds)',
    'Add internal links between related pages',
    'Create high-quality, unique content for each page',
    'Use descriptive, keyword-rich URLs',
    'Optimize images with alt text and proper file names',
    'Ensure mobile-friendly design',
    'Add social media sharing buttons to increase engagement',
    'Monitor Core Web Vitals and fix any issues'
  ];

  tips.forEach((tip, index) => {
    log(`${index + 1}. ${tip}`, 'blue');
  });
}

// Main execution
async function main() {
  log('HouseGoing SEO Monitor & Improvement Tool', 'bold');
  log('==========================================\n', 'bold');
  
  // Run all checks
  const sitemapsExist = checkSitemapFiles();
  const totalUrls = countSitemapUrls();
  checkRobotsTxt();
  generateSeoRecommendations();
  generateIndexingTips();
  
  // Summary
  logHeader('SUMMARY');
  
  if (sitemapsExist) {
    log(`✓ All sitemap files are present`, 'green');
  } else {
    log(`✗ Some sitemap files are missing`, 'red');
  }
  
  log(`📊 Total URLs in sitemap: ${totalUrls}`, 'blue');
  
  if (totalUrls > 40) {
    log(`✓ Good URL coverage for indexing`, 'green');
  } else {
    log(`⚠ Consider adding more content pages`, 'yellow');
  }
  
  log('\n🚀 Next steps:', 'bold');
  log('1. Submit updated sitemap to Google Search Console', 'blue');
  log('2. Monitor indexing status in Search Console', 'blue');
  log('3. Run this script weekly to track improvements', 'blue');
  
  console.log('\n');
}

// Run the script
main().catch(error => {
  console.error('Error running SEO monitor:', error);
  process.exit(1);
});
