/**
 * Error logging system for HouseGoing
 * Captures and stores errors for monitoring and debugging
 */
import { getSupabaseClient } from '../supabase-client';

/**
 * Log an error to the database
 * @param {Error} error - The error object
 * @param {string} context - Where the error occurred (e.g., 'api', 'auth', 'payment')
 * @param {Object} metadata - Additional information about the error
 * @returns {Promise<Object>} - The logged error
 */
export async function logError(error, context = 'general', metadata = {}) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Extract error details
    const errorData = {
      message: error.message || 'Unknown error',
      stack: error.stack || '',
      context,
      metadata: JSON.stringify(metadata),
      user_id: metadata.userId || null,
      severity: metadata.severity || 'error',
      url: metadata.url || '',
      created_at: new Date().toISOString()
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${context}] ${error.message}`, {
        stack: error.stack,
        metadata
      });
    }

    // Store in database
    const { data, error: dbError } = await supabase
      .from('error_logs')
      .insert(errorData)
      .select()
      .single();

    if (dbError) {
      console.error('Failed to log error to database:', dbError);
    }

    return data;
  } catch (loggingError) {
    // Fallback if logging itself fails
    console.error('Error in error logging system:', loggingError);
    console.error('Original error:', error);
    return null;
  }
}

/**
 * Get recent errors from the database
 * @param {number} limit - Maximum number of errors to retrieve
 * @param {string} context - Filter by context
 * @returns {Promise<Array>} - The errors
 */
export async function getRecentErrors(limit = 50, context = null) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    let query = supabase
      .from('error_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (context) {
      query = query.eq('context', context);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Failed to get recent errors:', error);
      return [];
    }

    return data;
  } catch (error) {
    console.error('Error getting recent errors:', error);
    return [];
  }
}

/**
 * Mark an error as resolved
 * @param {string} errorId - The ID of the error to mark as resolved
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function markErrorResolved(errorId) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { error } = await supabase
      .from('error_logs')
      .update({ status: 'resolved', resolved_at: new Date().toISOString() })
      .eq('id', errorId);

    if (error) {
      console.error('Failed to mark error as resolved:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error marking error as resolved:', error);
    return false;
  }
}

/**
 * Global error handler for React components
 * @param {Error} error - The error that was caught
 * @param {Object} info - Information about the error
 * @returns {void}
 */
export function globalErrorHandler(error, info) {
  logError(error, 'react', {
    componentStack: info?.componentStack,
    url: window.location.href,
    userAgent: navigator.userAgent
  });
}

/**
 * Initialize global error listeners
 * This sets up window.onerror and unhandledrejection listeners
 */
export function initializeErrorLogging() {
  // Handle uncaught errors
  window.onerror = function(message, source, lineno, colno, error) {
    logError(error || new Error(message), 'window', {
      source,
      lineno,
      colno,
      url: window.location.href
    });
    return false; // Let the default handler run
  };

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    logError(event.reason, 'promise', {
      url: window.location.href
    });
  });

  console.log('Error logging system initialized');
}
