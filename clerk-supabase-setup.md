# Clerk + Supabase Integration Guide

This guide explains how to set up and use <PERSON> for authentication with <PERSON><PERSON><PERSON> as the database backend.

## 1. Set Up Clerk in Your React Application

1. Install Clerk packages:

```bash
npm install @clerk/clerk-react
```

2. Add Clerk environment variables to your `.env` file:

```
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key_here
```

3. Wrap your application with the ClerkProvider in `main.tsx`:

```tsx
import { Clerk<PERSON>rovider } from '@clerk/clerk-react';

const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

// ...

root.render(
  <StrictMode>
    <ErrorBoundary>
      <ClerkProvider publishableKey={clerkPubKey}>
        <I18nextProvider i18n={i18n}>
          {useSimpleApp ? <SimpleApp /> : <App />}
        </I18nextProvider>
      </ClerkProvider>
    </ErrorBoundary>
  </StrictMode>
);
```

## 2. Create a Clerk-Supabase Client

The `clerk-supabase.ts` file provides utilities for using Supabase with Clerk authentication:

```typescript
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';

// Supabase URL and anon key
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Create a Supabase client that uses Clerk authentication
export function createClerkSupabaseClient() {
  return createClient<Database>(supabaseUrl, supabaseKey, {
    global: {
      fetch: async (url, options = {}) => {
        try {
          // Get token from Clerk
          const token = await window.Clerk?.session?.getToken({ template: 'supabase' });
          
          // Create headers with the token
          const headers = new Headers(options?.headers);
          if (token) {
            headers.set('Authorization', `Bearer ${token}`);
          }
          
          // Make the request with the token
          return fetch(url, {
            ...options,
            headers,
          });
        } catch (error) {
          console.error('Error getting Clerk token for Supabase:', error);
          return fetch(url, options);
        }
      },
    },
  });
}

// Singleton instance
export const clerkSupabase = createClerkSupabaseClient();
```

## 3. Set Up Supabase Row Level Security (RLS) Policies

Create RLS policies in Supabase that use the JWT claims from Clerk:

```sql
-- Allow users to select their own data
CREATE POLICY "Users can view their own data"
ON public.user_profiles
FOR SELECT USING (
  auth.jwt() ->> 'sub' = id
);

-- Allow users to update their own data
CREATE POLICY "Users can update their own data"
ON public.user_profiles
FOR UPDATE USING (
  auth.jwt() ->> 'sub' = id
);

-- Allow users to insert their own data
CREATE POLICY "Users can insert their own data"
ON public.user_profiles
FOR INSERT WITH CHECK (
  auth.jwt() ->> 'sub' = id
);
```

## 4. Using Clerk with Supabase in Components

```tsx
import { useAuth, useUser } from '@clerk/clerk-react';
import { clerkSupabase } from '../lib/clerk-supabase';

function MyComponent() {
  const { isSignedIn } = useUser();
  const { getToken } = useAuth();
  const [data, setData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!isSignedIn) return;

      // Get data from Supabase
      const { data, error } = await clerkSupabase
        .from('your_table')
        .select('*');

      if (error) {
        console.error('Supabase error:', error);
      } else {
        setData(data);
      }
    };

    fetchData();
  }, [isSignedIn]);

  return (
    <div>
      {data ? <pre>{JSON.stringify(data, null, 2)}</pre> : 'Loading...'}
    </div>
  );
}
```

## 5. Syncing Clerk User Data with Supabase

Create a function to sync user data from Clerk to Supabase:

```typescript
async function syncUserToSupabase(user) {
  const token = await getToken({ template: 'supabase' });
  
  // Create a Supabase client with the token
  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  });
  
  // Upsert user data to Supabase
  const { error } = await supabase
    .from('user_profiles')
    .upsert({
      id: user.id,
      email: user.primaryEmailAddress?.emailAddress,
      first_name: user.firstName,
      last_name: user.lastName,
      avatar_url: user.imageUrl,
      updated_at: new Date().toISOString(),
    }, { onConflict: 'id' });
    
  if (error) {
    console.error('Error syncing user to Supabase:', error);
  }
}
```

## 6. Handling Sign-In and Sign-Up

Use Clerk's components for sign-in and sign-up:

```tsx
import { SignIn, SignUp } from '@clerk/clerk-react';

// Sign-in page
function SignInPage() {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <SignIn 
        path="/sign-in"
        routing="path"
        signUpUrl="/sign-up"
        redirectUrl="/dashboard"
        appearance={{
          elements: {
            rootBox: "mx-auto",
            card: "shadow-lg rounded-lg border border-gray-200",
            headerTitle: "text-2xl font-bold text-center text-gray-900",
            headerSubtitle: "text-center text-gray-600",
            formButtonPrimary: "bg-purple-600 hover:bg-purple-700",
          }
        }}
      />
    </div>
  );
}

// Sign-up page
function SignUpPage() {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <SignUp 
        path="/sign-up"
        routing="path"
        signInUrl="/sign-in"
        redirectUrl="/dashboard"
        appearance={{
          elements: {
            rootBox: "mx-auto",
            card: "shadow-lg rounded-lg border border-gray-200",
            headerTitle: "text-2xl font-bold text-center text-gray-900",
            headerSubtitle: "text-center text-gray-600",
            formButtonPrimary: "bg-purple-600 hover:bg-purple-700",
          }
        }}
      />
    </div>
  );
}
```

## 7. Protecting Routes

Use Clerk's `<SignedIn>` and `<SignedOut>` components to protect routes:

```tsx
import { SignedIn, SignedOut, RedirectToSignIn } from '@clerk/clerk-react';

function ProtectedPage() {
  return (
    <>
      <SignedIn>
        <div>This content is only visible to signed in users</div>
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </>
  );
}
```

## 8. Customizing Email Templates

You can customize email templates in the Clerk Dashboard:

1. Go to **Email & SMS**
2. Select the template you want to customize (e.g., "Verification Email")
3. Customize the subject, content, and sender name
4. Save your changes

This will ensure that emails come from "HouseGoing Team" instead of "Clerk".
