import { <PERSON>LER<PERSON>_CONFIG } from '../config/clerk';
import { processClerkWebhook } from './clerk-webhook-handler';

/**
 * Handle Clerk webhook requests
 * This would normally be a server-side function, but we're implementing a client-side version
 * for demonstration purposes
 */
export async function handleClerkWebhook(request: Request): Promise<Response> {
  // Only allow POST requests
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // Get the request body
    const payload = await request.text();
    const event = JSON.parse(payload);
    
    // Verify the webhook signature (simplified for client-side)
    const svix_id = request.headers.get('svix-id');
    const svix_timestamp = request.headers.get('svix-timestamp');
    const svix_signature = request.headers.get('svix-signature');
    
    // Log webhook details for debugging
    console.log('Webhook received:', {
      id: svix_id,
      timestamp: svix_timestamp,
      event_type: event.type
    });
    
    // Process the webhook event
    await processClerkWebhook(event);
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error handling webhook:', error);
    return new Response(JSON.stringify({ success: false, error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * Register the webhook endpoint with Clerk
 * This would normally be done through the Clerk Dashboard, but we're providing instructions here
 */
export function getWebhookInstructions(): string {
  return `
To set up Clerk webhooks:

1. Go to the Clerk Dashboard: https://dashboard.clerk.dev/
2. Navigate to "Webhooks" in the left sidebar
3. Click "Add Endpoint"
4. Enter the following URL: ${window.location.origin}/api/webhooks/clerk
5. Select the following events:
   - user.created
   - user.updated
   - session.created
6. Set the signing secret to a secure value
7. Click "Create"

For local development, you can use a service like ngrok to expose your local server.
  `;
}
