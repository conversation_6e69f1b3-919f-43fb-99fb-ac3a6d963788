import React from 'react';
import { Helmet } from 'react-helmet-async';

interface JsonLdProps {
  type: 'Organization' | 'WebSite' | 'LocalBusiness' | 'Product' | 'Event' | 'BreadcrumbList';
  data: Record<string, any>;
}

/**
 * JsonLd component for adding structured data to pages
 * 
 * @param {string} type - The schema.org type
 * @param {object} data - The structured data object
 */
export default function JsonLd({ type, data }: JsonLdProps) {
  // Base schema with @context
  const schema = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data,
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schema)}
      </script>
    </Helmet>
  );
}

/**
 * Organization schema for HouseGoing
 */
export function OrganizationSchema() {
  const data = {
    name: 'HouseGoing',
    url: 'https://housegoing.com.au',
    logo: 'https://housegoing.com.au/images/housegoing-logo.svg',
    sameAs: [
      'https://www.instagram.com/housegoing.com.au/',
      'https://www.tiktok.com/@housegoing_au?lang=en-GB'
    ],
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Sydney',
      addressRegion: 'NSW',
      postalCode: '2000',
      addressCountry: 'AU'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+61-000-000-000',
      contactType: 'customer service',
      email: '<EMAIL>'
    }
  };

  return <JsonLd type="Organization" data={data} />;
}

/**
 * Website schema for HouseGoing
 */
export function WebsiteSchema() {
  const data = {
    name: 'HouseGoing',
    url: 'https://housegoing.com.au',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://housegoing.com.au/find-venues?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  };

  return <JsonLd type="WebSite" data={data} />;
}

/**
 * Venue schema for venue listings
 */
export function VenueSchema({
  name,
  description,
  image,
  pricePerHour,
  address,
  maxGuests,
  amenities,
  url,
  rating = 4.5,
  reviewCount = 0
}: {
  name: string;
  description: string;
  image: string;
  pricePerHour: number;
  address: string;
  maxGuests: number;
  amenities: string[];
  url: string;
  rating?: number;
  reviewCount?: number;
}) {
  const data = {
    '@type': 'EventVenue',
    name,
    description,
    image,
    priceRange: `$${pricePerHour} per hour`,
    address: {
      '@type': 'PostalAddress',
      streetAddress: address,
      addressLocality: 'Sydney',
      addressRegion: 'NSW',
      addressCountry: 'AU'
    },
    maximumAttendeeCapacity: maxGuests,
    amenityFeature: amenities.map(amenity => ({
      '@type': 'LocationFeatureSpecification',
      name: amenity
    })),
    url,
    aggregateRating: reviewCount > 0 ? {
      '@type': 'AggregateRating',
      ratingValue: rating,
      reviewCount: reviewCount,
      bestRating: 5,
      worstRating: 1
    } : undefined,
    offers: {
      '@type': 'Offer',
      price: pricePerHour,
      priceCurrency: 'AUD',
      availability: 'https://schema.org/InStock',
      validFrom: new Date().toISOString(),
      priceSpecification: {
        '@type': 'UnitPriceSpecification',
        price: pricePerHour,
        priceCurrency: 'AUD',
        unitText: 'per hour'
      }
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: -33.8688,
      longitude: 151.2093
    }
  };

  return <JsonLd type="LocalBusiness" data={data} />;
}

/**
 * FAQ Schema for pages with frequently asked questions
 */
export function FAQSchema({ faqs }: { faqs: Array<{ question: string; answer: string }> }) {
  const data = {
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return <JsonLd type="FAQPage" data={data} />;
}

/**
 * Breadcrumb Schema for navigation
 */
export function BreadcrumbSchema({ items }: {
  items: Array<{ name: string; url: string }>
}) {
  const data = {
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };

  return <JsonLd type="BreadcrumbList" data={data} />;
}

/**
 * Article Schema for blog posts
 */
export function ArticleSchema({
  headline,
  description,
  image,
  author,
  datePublished,
  dateModified,
  url
}: {
  headline: string;
  description: string;
  image: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  url: string;
}) {
  const data = {
    headline,
    description,
    image,
    author: {
      '@type': 'Person',
      name: author
    },
    publisher: {
      '@type': 'Organization',
      name: 'HouseGoing',
      logo: {
        '@type': 'ImageObject',
        url: 'https://housegoing.com.au/images/housegoing-logo.svg'
      }
    },
    datePublished,
    dateModified: dateModified || datePublished,
    url,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url
    }
  };

  return <JsonLd type="Article" data={data} />;
}
