# HouseGoing Documentation

Welcome to the HouseGoing documentation. This documentation provides detailed information about the HouseGoing platform, including its architecture, components, and APIs.

## Getting Started

### Installation

```bash
# Clone the repository
git clone https://github.com/housegoingmate/housegoing-.git

# Install dependencies
npm install

# Start the development server
npm run dev
```

### Project Structure

The HouseGoing project follows a modular structure:

- `src/components`: Reusable UI components
- `src/pages`: Page components
- `src/context`: React context providers
- `src/hooks`: Custom React hooks
- `src/lib`: Utility functions and libraries
- `src/services`: Service modules for API interactions
- `src/styles`: Global styles and theme configuration
- `src/types`: TypeScript type definitions
- `src/utils`: Helper functions

## Architecture

HouseGoing is built with a modern React architecture:

- **Frontend**: React with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Context API
- **Routing**: React Router
- **Authentication**: Clerk and Supabase Auth
- **Database**: Supabase (PostgreSQL)
- **API**: RESTful API with Express
- **AI Integration**: Lang<PERSON>hain and Hugging Face

## Key Features

- **Venue Booking**: Search, browse, and book party venues
- **Host Portal**: Manage venues, bookings, and earnings
- **AI Assistants**: Sales and host assistants powered by AI
- **Messaging**: Communication between hosts and guests
- **Reviews**: Venue rating and review system
- **Analytics**: Performance metrics for hosts and admins

## API Reference

The HouseGoing API provides endpoints for interacting with the platform:

- `/api/venues`: Venue management
- `/api/bookings`: Booking management
- `/api/messages`: Messaging system
- `/api/reviews`: Review system
- `/api/users`: User management
- `/api/sales-assistant`: Sales AI assistant
- `/api/host-assistant`: Host AI assistant

## Component Library

HouseGoing includes a comprehensive component library:

- **UI Components**: Buttons, inputs, modals, etc.
- **Layout Components**: Containers, grids, etc.
- **Form Components**: Form fields, validation, etc.
- **Data Display Components**: Tables, cards, etc.
- **Feedback Components**: Alerts, toasts, etc.
- **Navigation Components**: Menus, tabs, etc.

## Contributing

We welcome contributions to the HouseGoing project. Please follow these guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests for your changes
5. Submit a pull request

## License

HouseGoing is proprietary software. All rights reserved.
