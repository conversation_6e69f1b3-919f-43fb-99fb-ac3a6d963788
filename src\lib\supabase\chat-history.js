/**
 * Chat history service for HouseGoing
 * Handles storing and retrieving chat conversations and messages
 */
import { getSupabaseClient } from '../supabase-client';

/**
 * Create a new conversation
 * @param {string} userId - The user ID
 * @param {string} agentType - The agent type (sales, host)
 * @param {string} title - The conversation title
 * @returns {Promise<Object>} - The created conversation
 */
export async function createConversation(userId, agentType, title = 'New Conversation') {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('chat_conversations')
      .insert({
        user_id: userId,
        agent_type: agentType,
        title
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
}

/**
 * Get a conversation by ID
 * @param {string} conversationId - The conversation ID
 * @returns {Promise<Object>} - The conversation
 */
export async function getConversation(conversationId) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('chat_conversations')
      .select('*')
      .eq('id', conversationId)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error getting conversation:', error);
    throw error;
  }
}

/**
 * Get all conversations for a user
 * @param {string} userId - The user ID
 * @param {string} agentType - Optional agent type filter
 * @returns {Promise<Array>} - The conversations
 */
export async function getUserConversations(userId, agentType = null) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    let query = supabase
      .from('chat_conversations')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (agentType) {
      query = query.eq('agent_type', agentType);
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error getting user conversations:', error);
    throw error;
  }
}

/**
 * Add a message to a conversation
 * @param {string} conversationId - The conversation ID
 * @param {string} role - The message role (user, assistant, system)
 * @param {string} content - The message content
 * @returns {Promise<Object>} - The created message
 */
export async function addMessage(conversationId, role, content) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Get the current highest message index
    const { data: messages, error: messagesError } = await supabase
      .from('chat_messages')
      .select('message_index')
      .eq('conversation_id', conversationId)
      .order('message_index', { ascending: false })
      .limit(1);

    if (messagesError) {
      throw messagesError;
    }

    const messageIndex = messages.length > 0 ? messages[0].message_index + 1 : 0;

    // Add the new message
    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id: conversationId,
        role,
        content,
        message_index: messageIndex
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error adding message:', error);
    throw error;
  }
}

/**
 * Get all messages for a conversation
 * @param {string} conversationId - The conversation ID
 * @returns {Promise<Array>} - The messages
 */
export async function getConversationMessages(conversationId) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('message_index', { ascending: true });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    throw error;
  }
}

/**
 * Update a conversation title
 * @param {string} conversationId - The conversation ID
 * @param {string} title - The new title
 * @returns {Promise<Object>} - The updated conversation
 */
export async function updateConversationTitle(conversationId, title) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('chat_conversations')
      .update({ title })
      .eq('id', conversationId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error updating conversation title:', error);
    throw error;
  }
}

/**
 * Delete a conversation and all its messages
 * @param {string} conversationId - The conversation ID
 * @returns {Promise<void>}
 */
export async function deleteConversation(conversationId) {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { error } = await supabase
      .from('chat_conversations')
      .delete()
      .eq('id', conversationId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error deleting conversation:', error);
    throw error;
  }
}

/**
 * Get the full conversation history with messages
 * @param {string} conversationId - The conversation ID
 * @returns {Promise<Object>} - The conversation with messages
 */
export async function getFullConversation(conversationId) {
  try {
    // Get the conversation
    const conversation = await getConversation(conversationId);

    // Get the messages
    const messages = await getConversationMessages(conversationId);

    return {
      ...conversation,
      messages
    };
  } catch (error) {
    console.error('Error getting full conversation:', error);
    throw error;
  }
}
