/**
 * Netlify Function: Create Stripe Customer
 * 
 * This function creates a Stripe customer for storing payment methods
 * and managing recurring payments.
 */

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    // Parse the request body
    const { email, name, phone, metadata } = JSON.parse(event.body);

    // Validate required fields
    if (!email) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Email is required' }),
      };
    }

    // Check if customer already exists
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    let customer;
    if (existingCustomers.data.length > 0) {
      // Customer exists, return existing customer
      customer = existingCustomers.data[0];
    } else {
      // Create new customer
      customer = await stripe.customers.create({
        email,
        name,
        phone,
        metadata: {
          platform: 'HouseGoing',
          ...metadata,
        },
      });
    }

    // Return the customer
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        id: customer.id,
        email: customer.email,
        name: customer.name,
        created: customer.created,
      }),
    };
  } catch (error) {
    console.error('Error creating customer:', error);

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: error.message || 'Failed to create customer',
      }),
    };
  }
};
