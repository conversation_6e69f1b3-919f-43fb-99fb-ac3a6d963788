

import { Helmet } from 'react-helmet-async';

export default function AboutHouseGoing() {
  return (
    <>
      <Helmet>
        <title>About HouseGoing - Australia's Premier Venue Booking Platform</title>
        <meta name="description" content="Learn about HouseGoing, Australia's premier venue booking platform for party and event spaces. Our story, mission, and what makes us different." />
        <meta name="keywords" content="HouseGoing, about HouseGoing, venue booking platform, Australia venues, party venue booking" />
        <link rel="canonical" href="https://housegoing.com.au/about-housegoing" />
        
        {/* OpenGraph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://housegoing.com.au/about-housegoing" />
        <meta property="og:title" content="About HouseGoing - Australia's Premier Venue Booking Platform" />
        <meta property="og:description" content="Learn about HouseGoing, Australia's premier venue booking platform for party and event spaces." />
        <meta property="og:image" content="https://housegoing.com.au/og-image.svg" />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:url" content="https://housegoing.com.au/about-housegoing" />
        <meta name="twitter:title" content="About HouseGoing - Australia's Premier Venue Booking Platform" />
        <meta name="twitter:description" content="Learn about HouseGoing, Australia's premier venue booking platform for party and event spaces." />
        <meta name="twitter:image" content="https://housegoing.com.au/og-image.svg" />
      </Helmet>

      {/* Hero Section */}
      <div className="bg-white pt-24 pb-12 border-b">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-3xl font-medium mb-4">About HouseGoing</h1>
          <p className="text-gray-600">Australia's Premier Venue Booking Platform</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto py-12 px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Left Content */}
          <div>
            <section className="mb-12">
              <h2 className="text-3xl font-bold mb-6 text-purple-700">Our Story</h2>
              <p className="text-lg mb-4">
                Founded in 2024, HouseGoing was created to solve a common problem - finding private venues that allow partying, casual drinking and hanging out, which can be difficult to find.
              </p>
              <p className="text-lg">
                What began as a simple tool to help people find party-friendly venues has grown into a comprehensive platform, though currently we only serve NSW residents.
              </p>
            </section>
            
            <section className="mb-12">
              <h2 className="text-3xl font-bold mb-6 text-purple-700">What Makes HouseGoing Different</h2>
              <p className="text-lg mb-6">
                HouseGoing isn't just another booking platform. We provide specialised features designed specifically for party and event planning:
              </p>
              <div className="space-y-4">
                <div className="pb-4 border-b">
                  <h3 className="text-lg font-medium">Noise Restriction Checking</h3>
                  <p className="text-gray-600">Automatically check local council noise regulations</p>
                </div>
                <div className="pb-4 border-b">
                  <h3 className="text-lg font-medium">Venue Verification</h3>
                  <p className="text-gray-600">All venues are personally verified by our team</p>
                </div>
                <div className="pb-4 border-b">
                  <h3 className="text-lg font-medium">Party Planning Tools</h3>
                  <p className="text-gray-600">Built-in tools to help plan the perfect event</p>
                </div>
                <div className="pb-4">
                  <h3 className="text-lg font-medium">Local Knowledge</h3>
                  <p className="text-gray-600">Deep integration with local council zoning and regulations</p>
                </div>
              </div>
            </section>
            
            <section className="mb-12">
              <h2 className="text-3xl font-bold mb-6 text-purple-700">Our Mission</h2>
              <p className="text-lg mb-6">
                At HouseGoing, we believe everyone deserves access to great spaces for memorable events. We're committed to:
              </p>
              <div className="space-y-4 pl-2">
                <div className="flex items-start">
                  <span className="text-gray-500 mr-3 mt-1">1.</span>
                  <p>Making venue discovery simple and stress-free</p>
                </div>
                <div className="flex items-start">
                  <span className="text-gray-500 mr-3 mt-1">2.</span>
                  <p>Helping hosts understand and comply with local regulations</p>
                </div>
                <div className="flex items-start">
                  <span className="text-gray-500 mr-3 mt-1">3.</span>
                  <p>Supporting local venue owners in maximising their space usage</p>
                </div>
                <div className="flex items-start">
                  <span className="text-gray-500 mr-3 mt-1">4.</span>
                  <p>Creating a trusted community of hosts and guests</p>
                </div>
              </div>
            </section>
          </div>
          
          {/* Right Content */}
          <div className="space-y-10">
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-2xl font-bold mb-6 text-purple-700">The HouseGoing Team</h2>
              <p className="text-lg mb-4">
                Our diverse team brings together expertise in property management, event planning, technology, and customer service.
              </p>
              <p className="text-lg">
                We're headquartered in Sydney with team members across Australia.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-lg border">
              <h2 className="text-2xl font-bold mb-6 text-purple-700">Contact HouseGoing</h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="bg-purple-100 p-3 rounded-full mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <a href="mailto:<EMAIL>" className="text-lg hover:text-purple-700 transition-colors"><EMAIL></a>
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <h2 className="text-2xl font-bold mb-6 text-purple-700">Follow HouseGoing</h2>
              <div className="flex justify-around">
                <a href="https://facebook.com/housegoinghq" target="_blank" rel="noopener noreferrer" className="flex flex-col items-center group">
                  <div className="bg-blue-100 p-4 rounded-full mb-2 group-hover:bg-blue-200 transition-colors">
                    <svg className="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                    </svg>
                  </div>
                  <span className="group-hover:text-purple-700 transition-colors">Facebook</span>
                </a>
                <a href="https://instagram.com/housegoinghq" target="_blank" rel="noopener noreferrer" className="flex flex-col items-center group">
                  <div className="bg-pink-100 p-4 rounded-full mb-2 group-hover:bg-pink-200 transition-colors">
                    <svg className="h-6 w-6 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                    </svg>
                  </div>
                  <span className="group-hover:text-purple-700 transition-colors">Instagram</span>
                </a>
                <a href="https://linkedin.com/company/housegoing" target="_blank" rel="noopener noreferrer" className="flex flex-col items-center group">
                  <div className="bg-blue-100 p-4 rounded-full mb-2 group-hover:bg-blue-200 transition-colors">
                    <svg className="h-6 w-6 text-blue-700" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/>
                    </svg>
                  </div>
                  <span className="group-hover:text-purple-700 transition-colors">LinkedIn</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* CTA Section */}
      <div className="bg-gray-50 py-12 border-t">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-xl font-medium mb-4">Ready to find your perfect venue?</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Whether you're planning a birthday celebration, corporate event, or casual get-together, HouseGoing helps you find spaces that match your needs.
          </p>
          <a 
            href="/find-venues" 
            className="bg-black hover:bg-gray-800 text-white font-medium py-2 px-6 rounded inline-block transition-colors"
          >
            Find Venues Now
          </a>
        </div>
      </div>
    </>
  );
}
