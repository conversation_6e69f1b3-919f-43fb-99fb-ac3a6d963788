/**
 * This is a fixed version of UserProfile.tsx to resolve the Netlify build error
 * The error was in line 108: /opt/build/repo/src/pages/UserProfile.tsx:108:3: ERROR: Unexpected ","
 * 
 * Instructions for fixing:
 * 1. Copy this entire file
 * 2. Paste it over the existing UserProfile.tsx
 * 3. Commit the changes
 * 4. Deploy to Netlify
 * 
 * The main fix is removing the trailing comment on the useEffect dependency array.
 */

import React, { useState, useEffect } from 'react';
import { useUser, useClerk, useSession } from '@clerk/clerk-react';
import { isHost } from '../utils/user-roles';
import { getUserBookings } from '../api/bookings';
import { Link, useNavigate } from 'react-router-dom';
import { Calendar, Clock, Users, DollarSign, ChevronRight, AlertCircle, LogOut, MessageCircle, Star, Heart, CreditCard, Settings, User, FileText, Menu, X, Shield } from 'lucide-react';
// Import supabase dependencies
import { createClient } from '@supabase/supabase-js';
import { createClerkSupabaseClient } from '../lib/clerk-supabase-official';
import MessagesSection from '../components/account/MessagesSection';
import ReviewsSection from '../components/account/ReviewsSection';
import SavedVenuesSection from '../components/account/SavedVenuesSection';
import PaymentMethodsSection from '../components/account/PaymentMethodsSection';
import CustomerVerification from '../components/verification/CustomerVerification';
import { safeExec, getAuthDebugInfo } from '../utils/safe-auth';
import { logDiagnostics } from '../utils/integration-diagnostics';

export default function UserProfile() {
  const { user, isLoaded, isSignedIn } = useUser();
  const clerk = useClerk();
  const { session } = useSession();  // Add explicit session from useSession
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'profile' | 'bookings' | 'messages' | 'reviews' | 'saved' | 'payments' | 'security' | 'verification' | 'preferences'>('profile');
  const [sidebarOpen, setSidebarOpen] = useState(false);  // Check if we're in development mode
  const isDevelopmentMode = typeof window !== 'undefined' &&
                           (window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('local'));

  // Development mode: use mock data
  // Use the user directly (mock in development, real in production)
  let effectiveUser = user;
  let isLoading = !isLoaded;

  if (isDevelopmentMode) {
    // Mock user data for development (load from localStorage if available)
    const firstName = localStorage.getItem('dev_user_firstName') || 'John';
    const lastName = localStorage.getItem('dev_user_lastName') || 'Developer';
    const bio = localStorage.getItem('dev_user_bio') || 'Development user for testing';
    const location = localStorage.getItem('dev_user_location') || 'Sydney, NSW';

    effectiveUser = {
      id: 'dev-user-123',
      firstName,
      lastName,
      fullName: `${firstName} ${lastName}`,
      primaryEmailAddress: { emailAddress: '<EMAIL>' } as any,
      createdAt: new Date(),
      publicMetadata: {
        bio,
        location
      }
    } as any; // Use type assertion to avoid TypeScript errors with mock user
    
    isLoading = false;
  }
  const handleSignOut = async () => {
    console.log('Sign out initiated');
    try {
      // Log debug info
      console.log('Auth debug info before sign out:', getAuthDebugInfo());
      
      // Safely call signOut using our utility
      if (clerk) {
        await safeExec(async () => {
          if (typeof clerk.signOut === 'function') {
            await clerk.signOut();
          } else {
            console.warn('clerk.signOut is not a function');
          }
        }, undefined);
      } else {
        console.warn('clerk is undefined, navigating directly');
      }
      
      // Always navigate home, even if sign out fails
      navigate('/');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Force navigation even if sign out fails
      navigate('/');
    }
  };
  
  const [isEditing, setIsEditing] = useState(false);
  const [bookings, setBookings] = useState<any[]>([]);
  const [loadingBookings, setLoadingBookings] = useState(false);
  const [bookingsError, setBookingsError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    bio: '',
    location: '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Initialize form data when user data is loaded
  useEffect(() => {
    if (effectiveUser) {
      // Check if this is a Google OAuth user
      const isGoogleOAuth = 
        localStorage.getItem('google_oauth_flow') === 'true' || 
        (effectiveUser?.primaryEmailAddress?.emailAddress || '').endsWith('@gmail.com');
      
      if (isGoogleOAuth) {
        console.log('Google OAuth user detected in UserProfile');
        // Store the information for diagnostics
        localStorage.setItem('google_user_profile_loaded', 'true');
        localStorage.setItem('google_user_profile_time', new Date().toISOString());
      }
      
      // Load user profile from Supabase first, then fallback to Clerk/localStorage
      loadUserProfile();

      // Fetch user bookings if on bookings tab
      if (activeTab === 'bookings') {
        fetchUserBookings();
      }
    }
  }, [effectiveUser, activeTab]);

  // Run diagnostics if requested
  const runIntegrationDiagnostics = async () => {
    console.log('Running Clerk-Supabase integration diagnostics...');
    try {
      await logDiagnostics(session);
      setMessage({ type: 'success', text: 'Diagnostics complete. Check browser console (F12) for results.' });
    } catch (err) {
      console.error('Error running diagnostics:', err);
      setMessage({ type: 'error', text: 'Failed to run diagnostics. See console for details.' });
    }
  };

  // Load user profile from Supabase using clerk_id
  const loadUserProfile = async () => {
    if (!effectiveUser) return;
    try {
      console.log('Loading user profile from Supabase for:', effectiveUser.id);
      
      // Ensure we use the authenticated client
      const supabaseClient = await getAuthenticatedSupabaseClient();
      if (!supabaseClient) {
        console.error('Failed to get authenticated Supabase client');
        setFormData({
          firstName: effectiveUser.firstName || '',
          lastName: effectiveUser.lastName || '',
          bio: (effectiveUser.publicMetadata?.bio as string) || '',
          location: (effectiveUser.publicMetadata?.location as string) || '',
        });
        return;
      }
      
      const clerkId = effectiveUser.id || '';
      if (!clerkId) {
        console.warn('No clerk_id available, using user data directly');
        setFormData({
          firstName: effectiveUser.firstName || '',
          lastName: effectiveUser.lastName || '',
          bio: (effectiveUser.publicMetadata?.bio as string) || '',
          location: (effectiveUser.publicMetadata?.location as string) || '',
        });
        return;
      }
      
      // Try to load from Supabase using clerk_id
      const { data: profile, error } = await supabaseClient
        .from('user_profiles')
        .select('first_name, last_name, bio, phone')
        .eq('clerk_id', clerkId)
        .maybeSingle();
      
      if (error) {
        console.error('Error loading user profile from Supabase:', error);
        // Fall back to Clerk user data
        setFormData({
          firstName: effectiveUser.firstName || '',
          lastName: effectiveUser.lastName || '',
          bio: (effectiveUser.publicMetadata?.bio as string) || '',
          location: (effectiveUser.publicMetadata?.location as string) || '',
        });
        return;
      }
      
      if (profile) {
        console.log('User profile loaded from Supabase successfully');
        setFormData({
          firstName: profile.first_name || effectiveUser.firstName || '',
          lastName: profile.last_name || effectiveUser.lastName || '',
          bio: profile.bio || (effectiveUser.publicMetadata?.bio as string) || '',
          location: profile.phone || (effectiveUser.publicMetadata?.location as string) || '',
        });
      } else {
        console.warn('No user profile found in Supabase, using Clerk data');
        setFormData({
          firstName: effectiveUser.firstName || '',
          lastName: effectiveUser.lastName || '',
          bio: (effectiveUser.publicMetadata?.bio as string) || '',
          location: (effectiveUser.publicMetadata?.location as string) || '',
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setFormData({
        firstName: effectiveUser?.firstName || '',
        lastName: effectiveUser?.lastName || '',
        bio: (effectiveUser?.publicMetadata?.bio as string) || '',
        location: (effectiveUser?.publicMetadata?.location as string) || '',
      });
    }
  };
  
  // Fetch user bookings
  const fetchUserBookings = async () => {
    if (!effectiveUser) return;
    try {
      setLoadingBookings(true);
      setBookingsError(null);
      const userId = effectiveUser.id || '';
      const userEmail = effectiveUser.primaryEmailAddress?.emailAddress || '';
      if (!userId) throw new Error('User ID not found');
      const data = await getUserBookings(userId, userEmail);
      setBookings(data);
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setBookingsError('Failed to load your bookings');
    } finally {
      setLoadingBookings(false);
    }
  };

  // Profile update logic using clerk_id
  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage(null);
    try {
      if (!effectiveUser) throw new Error('No user found');
      const clerkId = effectiveUser.id || '';
      if (!clerkId) throw new Error('User ID not found');
      
      // Get authenticated Supabase client using native integration
      const supabaseClient = await getAuthenticatedSupabaseClient();
      if (!supabaseClient) {
        throw new Error('Failed to get authenticated Supabase client');
      }
      
      // Update or insert in Supabase using clerk_id
      const { data: existingProfile } = await supabaseClient
        .from('user_profiles')
        .select('clerk_id')
        .eq('clerk_id', clerkId)
        .maybeSingle();
        
      if (existingProfile) {
        await supabaseClient
          .from('user_profiles')
          .update({
            first_name: formData.firstName,
            last_name: formData.lastName,
            bio: formData.bio,
            phone: formData.location,
            updated_at: new Date().toISOString(),
          })
          .eq('clerk_id', clerkId);
      } else {
        await supabaseClient
          .from('user_profiles')
          .insert({
            clerk_id: clerkId,
            email: effectiveUser.primaryEmailAddress?.emailAddress || '',
            first_name: formData.firstName,
            last_name: formData.lastName,
            bio: formData.bio,
            phone: formData.location,
            is_host: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            role: 'guest',
          });
      }
      setMessage({ type: 'success', text: 'Profile updated successfully!' });
      setIsEditing(false);
    } catch (error) {
      console.error('Profile update error:', error);
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  // - Install from: https://marketplace.visualstudio.com/items?itemName=Supabase.vscode-supabase-extension
  // - Use @supabase in GitHub Copilot chat to get database context-aware assistance  // - Generate migrations with @supabase /migration <description>
  const getAuthenticatedSupabaseClient = async () => {
    try {
      // Check if this is a Google OAuth user
      const isGoogleOAuth = 
        localStorage.getItem('google_oauth_flow') === 'true' || 
        (effectiveUser?.primaryEmailAddress?.emailAddress || '').endsWith('@gmail.com');
      
      if (!session) {
        console.error('No Clerk session available for Supabase authentication');
        
        if (isGoogleOAuth) {
          console.log('Google OAuth user with no session, using anon client with fallback');
          localStorage.setItem('google_no_session_fallback', 'true');
        }
        
        // Return a non-authenticated client as fallback
        return createClient(
          import.meta.env.VITE_SUPABASE_URL || '',
          import.meta.env.VITE_SUPABASE_ANON_KEY || ''
        );
      }
      
      // Make sure the getToken function exists
      if (typeof session.getToken !== 'function') {
        console.error('Session getToken is not a function, using anon client');
        
        if (isGoogleOAuth) {
          console.log('Google OAuth user with no getToken, using anon client with fallback');
          localStorage.setItem('google_no_gettoken_fallback', 'true');
        }
        
        return createClient(
          import.meta.env.VITE_SUPABASE_URL || '',
          import.meta.env.VITE_SUPABASE_ANON_KEY || ''
        );
      }
      
      // Use the native integration with createClerkSupabaseClient
      console.log('Creating Supabase client with native integration for', session.id);
      const supabaseClient = createClerkSupabaseClient(session);
      
      // For Google OAuth users, skip the test query to avoid delays
      if (isGoogleOAuth) {
        console.log('Google OAuth user, skipping test query');
        localStorage.setItem('google_skipped_test_query', 'true');
        return supabaseClient;
      }
      
      // Test the client with a simple query to ensure it's working
      try {
        const { error: testError } = await supabaseClient
          .from('user_profiles')
          .select('count')
          .limit(1);
          
        if (testError) {
          console.warn('Warning: Test query failed on Supabase client:', testError);
        } else {
          console.log('Test query succeeded on Supabase client');
        }
      } catch (testErr) {
        console.warn('Warning: Error testing Supabase client:', testErr);
      }
      
      console.log('Successfully created authenticated Supabase client with native integration');
      return supabaseClient;
    } catch (error) {
      console.error('Error creating authenticated Supabase client:', error);
      // Return a non-authenticated client as fallback
      return createClient(
        import.meta.env.VITE_SUPABASE_URL || '',
        import.meta.env.VITE_SUPABASE_ANON_KEY || ''
      );
    }
  };

  // Show loading spinner while Clerk is loading (with timeout)
  if (!isLoaded) {
    return (
      <div className="pt-32 flex justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
          <p className="text-gray-600">Loading your profile...</p>
          <p className="text-gray-400 mt-2 text-sm">If this takes too long, please refresh the page</p>
        </div>
      </div>
    );
  }

  // If not signed in, show sign in button
  if (!isSignedIn) {
    return (
      <div className="pt-32 flex justify-center">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-6">You need to be signed in to view your profile.</p>
          <a href="/login" className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="pt-32 pb-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row">
            {/* Mobile menu toggle */}
            <div className="md:hidden bg-white p-4 rounded-lg shadow mb-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex justify-between items-center w-full"
              >
                <span className="font-semibold">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}</span>
                {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
              </button>
            </div>

            {/* Sidebar navigation */}
            <div className={`md:w-64 mb-6 md:mb-0 md:mr-8 ${sidebarOpen ? 'block' : 'hidden md:block'}`}>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex flex-col items-center mb-6">
                  <div className="w-20 h-20 rounded-full bg-purple-100 flex items-center justify-center mb-3">
                    <User size={40} className="text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-lg text-center">{effectiveUser?.fullName || `${effectiveUser?.firstName || ''} ${effectiveUser?.lastName || ''}`}</h3>
                  <p className="text-gray-500 text-sm">{effectiveUser?.primaryEmailAddress?.emailAddress}</p>
                </div>

                <div className="space-y-1">
                  <button
                    onClick={() => setActiveTab('profile')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'profile' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <User size={18} className="mr-3" />
                    <span>Profile</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('bookings')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'bookings' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <Calendar size={18} className="mr-3" />
                    <span>Bookings</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('messages')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'messages' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <MessageCircle size={18} className="mr-3" />
                    <span>Messages</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('reviews')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'reviews' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <Star size={18} className="mr-3" />
                    <span>Reviews</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('saved')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'saved' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <Heart size={18} className="mr-3" />
                    <span>Saved</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('payments')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'payments' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <CreditCard size={18} className="mr-3" />
                    <span>Payments</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('verification')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'verification' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <Shield size={18} className="mr-3" />
                    <span>Verification</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('security')}
                    className={`flex items-center w-full p-3 rounded-lg ${
                      activeTab === 'security' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'
                    }`}
                  >
                    <Settings size={18} className="mr-3" />
                    <span>Security</span>
                  </button>
                  
                  <button
                    onClick={handleSignOut}
                    className="flex items-center w-full p-3 rounded-lg text-red-600 hover:bg-red-50"
                  >
                    <LogOut size={18} className="mr-3" />
                    <span>Sign Out</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Main content area */}
            <div className="flex-1">
              <div className="bg-white rounded-lg shadow p-6">
                {message && (
                  <div className={`mb-6 p-4 rounded-lg ${message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    <div className="flex">
                      {message.type === 'success' ? (
                        <svg className="h-5 w-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <AlertCircle className="h-5 w-5 mr-3" />
                      )}
                      <p>{message.text}</p>
                    </div>
                  </div>
                )}

                {/* Profile Information */}
                {activeTab === 'profile' && (
                  <div>
                    <div className="flex justify-between items-start mb-8">
                      <div>
                        <h2 className="text-2xl font-semibold text-gray-900 font-inter">Profile Information</h2>
                        <p className="text-gray-600 mt-2 font-inter">Manage your personal information</p>
                      </div>
                      <div className="flex space-x-2">
                        {!isEditing ? (
                          <>
                            <button
                              onClick={() => setIsEditing(true)}
                              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors font-inter"
                            >
                              Edit Profile
                            </button>

                          </>
                        ) : (
                          <button
                            onClick={() => setIsEditing(false)}
                            className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors font-inter"
                          >
                            Cancel
                          </button>
                        )}
                      </div>
                    </div>

                    {isEditing ? (
                      <form onSubmit={handleSaveProfile} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                              First Name
                            </label>
                            <input
                              type="text"
                              value={formData.firstName}
                              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 font-inter"
                              placeholder="Enter your first name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                              Last Name
                            </label>
                            <input
                              type="text"
                              value={formData.lastName}
                              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 font-inter"
                              placeholder="Enter your last name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                              Email
                            </label>
                            <input
                              type="email"
                              value={effectiveUser?.primaryEmailAddress?.emailAddress || ''}
                              disabled
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed font-inter"
                              placeholder="Your email"
                            />
                            <p className="text-xs text-gray-500 mt-1 font-inter">
                              Email cannot be changed. Contact support if you need to update your email.
                            </p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                              Phone Number
                            </label>
                            <input
                              type="text"
                              value={formData.location}
                              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 font-inter"
                              placeholder="Enter your phone number"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2 font-inter">
                            Bio
                          </label>
                          <textarea
                            value={formData.bio}
                            onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                            rows={4}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 font-inter"
                            placeholder="Tell others about yourself..."
                          />
                        </div>
                        <div className="flex justify-end">
                          <button
                            type="submit"
                            disabled={isSaving}
                            className={`px-6 py-3 ${isSaving ? 'bg-gray-400' : 'bg-purple-600 hover:bg-purple-700'} text-white rounded-lg font-medium transition-colors font-inter`}
                          >
                            {isSaving ? 'Saving...' : 'Save Changes'}
                          </button>
                        </div>
                      </form>
                    ) : (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">First Name</h3>
                            <p className="text-gray-900 font-medium font-inter">{formData.firstName || effectiveUser?.firstName || 'Not provided'}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Last Name</h3>
                            <p className="text-gray-900 font-medium font-inter">{formData.lastName || effectiveUser?.lastName || 'Not provided'}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Email</h3>
                            <p className="text-gray-900 font-medium font-inter">{effectiveUser?.primaryEmailAddress?.emailAddress || 'No email provided'}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Phone Number</h3>
                            <p className="text-gray-900 font-medium font-inter">
                              {String(effectiveUser?.publicMetadata?.location || 'Not provided')}
                            </p>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-2 font-inter">Bio</h3>
                          <p className="text-gray-900 font-inter">
                            {String(effectiveUser?.publicMetadata?.bio || 'No bio provided')}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Bookings Tab */}
                {activeTab === 'bookings' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Your Bookings</h2>
                    {/* Bookings content here */}
                  </div>
                )}

                {/* Messages Tab */}
                {activeTab === 'messages' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Messages</h2>
                    <MessagesSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
                  </div>
                )}

                {/* Reviews Tab */}
                {activeTab === 'reviews' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Reviews</h2>
                    <ReviewsSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
                  </div>
                )}

                {/* Saved Tab */}
                {activeTab === 'saved' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Saved Venues</h2>
                    <SavedVenuesSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
                  </div>
                )}

                {/* Payments Tab */}
                {activeTab === 'payments' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Payment Methods</h2>
                    <PaymentMethodsSection userId={effectiveUser?.id || localStorage.getItem('clerk_user_id') || ''} />
                  </div>
                )}

                {/* Verification Tab */}
                {activeTab === 'verification' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Verification</h2>
                    <CustomerVerification userId={effectiveUser?.id || ''} />
                  </div>
                )}

                {/* Security Tab */}
                {activeTab === 'security' && (
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6 font-inter">Security Settings</h2>
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4 font-inter">Login Methods</h3>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium font-inter">Email and Password</p>
                              <p className="text-gray-500 text-sm font-inter">{effectiveUser?.primaryEmailAddress?.emailAddress}</p>
                            </div>
                            <div>
                              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4 font-inter">Password</h3>
                        <button
                          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-inter"
                        >
                          Change Password
                        </button>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4 font-inter">Two-Factor Authentication</h3>
                        <p className="text-gray-600 mb-2 font-inter">Add an extra layer of security to your account</p>
                        <button
                          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-inter"
                        >
                          Enable 2FA
                        </button>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4 font-inter">Sessions</h3>
                        <p className="text-gray-600 mb-2 font-inter">Manage your active sessions</p>
                        <button
                          className="px-4 py-2 border border-purple-500 text-purple-700 rounded-lg hover:bg-purple-50 font-inter"
                        >
                          Sign Out All Devices
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
