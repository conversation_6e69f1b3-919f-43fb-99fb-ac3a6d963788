import React from 'react';

interface VenuePreviewProps {
  venue: {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    capacity: number;
    images: string[];
    rating: number;
    reviews: number;
  };
  onClick: () => void;
}

/**
 * A compact venue preview component for the chat interface
 */
const VenuePreview: React.FC<VenuePreviewProps> = ({ venue, onClick }) => {
  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };

  return (
    <div className="venue-preview" onClick={onClick}>
      <div
        className="venue-preview-image"
        style={{ backgroundImage: `url(${venue.images[0]})` }}
      />
      <div className="venue-preview-details">
        <h4>{venue.title}</h4>
        <div className="venue-preview-meta">
          <span className="venue-preview-location">{venue.location}</span>
          <span className="venue-preview-price">${venue.price}/hour</span>
        </div>
        <div className="venue-preview-rating">
          <span className="stars">{'★'.repeat(Math.floor(venue.rating))}{'☆'.repeat(5 - Math.floor(venue.rating))}</span>
          <span className="rating-text">{formatRating(venue.rating)}</span>
          <span className="reviews">({venue.reviews})</span>
        </div>
      </div>
    </div>
  );
};

export default VenuePreview;
