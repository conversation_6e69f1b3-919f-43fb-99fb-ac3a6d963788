import React, { useState } from 'react';
import SEO from '../components/seo/SEO';
import { ChevronDown, ChevronUp, Info, Users, Home, Shield, CreditCard, AlertTriangle } from 'lucide-react';

interface TermsSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  summary: string;
  content: string;
  examples?: string[];
}

const termsData: TermsSection[] = [
  {
    id: 'introduction',
    title: '1. Introduction',
    icon: <Info className="h-5 w-5" />,
    summary: 'HouseGoing connects party hosts with venue owners across NSW, Australia.',
    content: `Welcome to HouseGoing, a platform connecting party hosts with venue owners across New South Wales, Australia. These Terms of Service ("Terms") govern your access to and use of the HouseGoing platform, including our website, mobile applications, and related services (collectively, the "Platform").

By accessing or using our Platform, you agree to be bound by these Terms. If you do not agree to these Terms, you may not access or use our Platform.

HouseGoing operates as a marketplace facilitating connections between venue owners ("Hosts") and party organizers ("Guests"). We do not own, operate, or control any venues listed on our Platform.`,
    examples: [
      'Creating an account automatically means you accept these terms',
      'Hosts list their venues for parties and events',
      'Guests search and book venues for their celebrations'
    ]
  },
  {
    id: 'definitions',
    title: '2. Definitions',
    icon: <Users className="h-5 w-5" />,
    summary: 'Key terms used throughout this agreement.',
    content: `For the purposes of these Terms:

"Platform" means the HouseGoing website, mobile applications, and related services.
"Host" means a venue owner who lists their property on the Platform.
"Guest" means a user who books or inquires about venues on the Platform.
"Venue" means any property, space, or facility listed on the Platform.
"Booking" means a confirmed reservation of a venue through the Platform.
"Content" means any information, text, graphics, photos, or other materials uploaded to the Platform.`,
    examples: [
      'Host: Someone who owns a backyard, hall, or event space',
      'Guest: Someone planning a birthday party, wedding, or corporate event',
      'Booking: A confirmed reservation with payment'
    ]
  },
  {
    id: 'eligibility',
    title: '3. User Eligibility and Account Terms',
    icon: <Shield className="h-5 w-5" />,
    summary: 'Requirements for using HouseGoing and account responsibilities.',
    content: `To use HouseGoing, you must:
- Be at least 18 years old
- Provide accurate and complete information
- Maintain the security of your account
- Comply with all applicable laws and regulations
- Not use the Platform for illegal activities

You are responsible for all activities that occur under your account. You must notify us immediately of any unauthorized use of your account.`,
    examples: [
      'Must be 18+ to create an account',
      'Provide real name and contact information',
      'Keep your password secure and confidential'
    ]
  },
  {
    id: 'host-responsibilities',
    title: '4. Host Responsibilities',
    icon: <Home className="h-5 w-5" />,
    summary: 'Obligations for venue owners listing on HouseGoing.',
    content: `As a Host, you agree to:

**Venue Listings:**
- Provide accurate descriptions and photos of your venue
- Disclose all relevant information about the property
- Maintain your venue in the condition described
- Comply with all local council regulations and permits

**Availability and Pricing:**
- Keep your calendar updated with accurate availability
- Honor confirmed bookings at the agreed price
- Provide clear pricing including any additional fees

**Safety and Legal Compliance:**
- Ensure your venue meets all safety requirements
- Maintain appropriate insurance coverage
- Comply with NSW noise regulations and council bylaws
- Provide necessary permits and licenses`,
    examples: [
      'Update calendar when dates become unavailable',
      'Ensure venue has working facilities as described',
      'Comply with local noise restrictions (usually 10 PM weekdays, midnight weekends)'
    ]
  },
  {
    id: 'guest-responsibilities',
    title: '5. Guest Responsibilities',
    icon: <Users className="h-5 w-5" />,
    summary: 'Obligations for party organizers booking venues.',
    content: `As a Guest, you agree to:

**Booking and Payment:**
- Provide accurate information about your event
- Pay all fees and charges as agreed
- Comply with the venue's house rules and policies
- Respect the agreed party size and duration

**During Your Event:**
- Treat the venue and surrounding area with respect
- Supervise your guests and ensure they follow venue rules
- Comply with noise restrictions and local council regulations
- End your event at the agreed time

**Damages and Liability:**
- You are responsible for any damage caused during your event
- Report any issues or damages immediately to the Host
- Pay for cleaning fees if the venue is left in poor condition
- Ensure your guests behave appropriately and legally`,
    examples: [
      'Don\'t exceed the agreed number of guests',
      'Clean up after your party as agreed',
      'Respect neighbors and noise limits',
      'Report any accidents or damages immediately'
    ]
  },
  {
    id: 'payment-terms',
    title: '6. Payment Terms',
    icon: <CreditCard className="h-5 w-5" />,
    summary: 'Booking payments, fees, and refund policies.',
    content: `**Payment Processing:**
All payments are processed securely through Stripe. HouseGoing does not store your payment information.

**Booking Fees:**
- Venue rental fee (paid to Host)
- Host Service Fee: 10% (deducted from Host payout)
- Guest Booking Fee: 5% (added to Guest total)
- Payment processing fee (as charged by Stripe)
- Optional insurance fee

**Payment Schedule:**
- 50% deposit required to confirm booking
- Remaining balance due 48 hours before event
- Security deposit held until 48 hours after event

**Cancellation and Refunds:**
- Free cancellation up to 48 hours before event
- Cancellations within 48 hours: 50% refund
- No-shows: No refund
- Host cancellations: Full refund plus compensation

**Disputes:**
Payment disputes will be handled through our resolution process in accordance with Australian Consumer Law.`,
    examples: [
      'Book a $200 venue: Pay $100 deposit, $100 before event',
      'Cancel 3 days early: Full refund minus processing fees',
      'Host cancels last minute: Full refund + $50 compensation'
    ]
  },
  {
    id: 'platform-rules',
    title: '7. Platform Rules and Prohibited Uses',
    icon: <AlertTriangle className="h-5 w-5" />,
    summary: 'What you can and cannot do on HouseGoing.',
    content: `**Prohibited Activities:**
You may not use HouseGoing to:
- List or book venues for illegal activities
- Discriminate against users based on protected characteristics
- Post false, misleading, or fraudulent content
- Circumvent our payment system
- Harass, threaten, or abuse other users
- Violate any local, state, or federal laws

**Content Guidelines:**
- All photos must accurately represent the venue
- Descriptions must be truthful and complete
- No offensive, discriminatory, or inappropriate content
- Respect intellectual property rights

**Account Suspension:**
We reserve the right to suspend or terminate accounts that violate these terms, engage in fraudulent activity, or pose a risk to our community.

**Reporting Violations:**
Users can report violations through our platform. We investigate all reports and take appropriate action.`,
    examples: [
      'Cannot list venues for underage drinking parties',
      'Cannot refuse bookings based on race, religion, etc.',
      'Cannot ask guests to pay outside the platform'
    ]
  },
  {
    id: 'liability-insurance',
    title: '8. Liability and Insurance',
    icon: <Shield className="h-5 w-5" />,
    summary: 'Insurance requirements and liability limitations.',
    content: `**Host Insurance Requirements:**
Hosts must maintain appropriate insurance coverage including:
- Public liability insurance (tiered requirements):
  • Basic venues: Minimum $5 million (small private parties)
  • Standard venues: $10 million recommended (most bookings)
  • Premium venues: $20 million required (large events, food service)
- Property insurance covering the venue
- Compliance with any strata or building insurance requirements

**Guest Insurance:**
We strongly recommend Guests obtain event insurance covering:
- Public liability for the event
- Damage to the venue or surrounding property
- Cancellation coverage

**Platform Role - Booking Facilitator Only:**
HouseGoing acts as a booking platform only. We do not:
- Own, operate, or control any venues
- Guarantee the condition or safety of venues
- Assume ANY liability for events or activities at venues
- Provide insurance coverage or damage protection for bookings
- Mediate disputes beyond facilitating communication

**Direct Host-Guest Relationship:**
All bookings create a direct contractual relationship between host and guest. HouseGoing is not a party to this relationship and bears no responsibility for performance, damages, or disputes.

**Limitation of Liability:**
To the maximum extent permitted by Australian law, HouseGoing's total liability is limited to the lesser of:
- Amount of platform fees paid for the specific booking
- AUD $100

**Indemnification:**
Users agree to indemnify and hold harmless HouseGoing against ALL claims arising from their use of the platform, violation of these terms, or any activities at venues.`,
    examples: [
      'Basic venues: $5M minimum, Standard: $10M, Premium: $20M insurance',
      'Guest should get event insurance for large parties',
      'HouseGoing not liable for venue condition issues'
    ]
  }
];

export default function TermsOfService() {
  const [activeTab, setActiveTab] = useState<'summary' | 'full'>('summary');
  const [openSections, setOpenSections] = useState<Set<string>>(new Set());

  const toggleSection = (sectionId: string) => {
    const newOpenSections = new Set(openSections);
    if (newOpenSections.has(sectionId)) {
      newOpenSections.delete(sectionId);
    } else {
      newOpenSections.add(sectionId);
    }
    setOpenSections(newOpenSections);
  };

  return (
    <>
      <SEO
        title="Terms of Service | HouseGoing"
        description="Terms and conditions for using HouseGoing's party venue rental platform in NSW, Australia. Clear, easy-to-understand legal terms."
        keywords="terms of service, legal terms, venue rental terms, party booking terms, NSW regulations"
        url="https://housegoing.com.au/terms"
      />

      <div className="pt-32 px-4 sm:px-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Terms and Conditions</h1>
          <p className="text-gray-600">Last Updated: January 15, 2025</p>
        </div>

        {/* Notification Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-1">We've simplified our terms</h3>
              <p className="text-blue-800 text-sm">
                We've updated our terms to be more user-friendly and transparent. Each section includes a simple summary and practical examples.
              </p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 mb-8">
          <button
            onClick={() => setActiveTab('summary')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'summary'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Quick Summary
          </button>
          <button
            onClick={() => setActiveTab('full')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'full'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Full Terms
          </button>
        </div>

        {/* Quick Summary Tab */}
        {activeTab === 'summary' && (
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Host Responsibilities */}
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                <div className="flex items-center mb-4">
                  <Home className="h-6 w-6 text-green-600 mr-3" />
                  <h3 className="text-lg font-semibold text-green-900">Host Responsibilities</h3>
                </div>
                <p className="text-green-800 text-sm mb-4">
                  Venue owners must provide accurate listings, maintain their properties, and comply with NSW regulations.
                </p>
                <ul className="text-green-700 text-sm space-y-1">
                  <li>• Keep venue descriptions accurate</li>
                  <li>• Maintain property condition</li>
                  <li>• Follow noise regulations</li>
                  <li>• Honor confirmed bookings</li>
                </ul>
              </div>

              {/* Guest Responsibilities */}
              <div className="bg-gradient-to-br from-blue-50 to-sky-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center mb-4">
                  <Users className="h-6 w-6 text-blue-600 mr-3" />
                  <h3 className="text-lg font-semibold text-blue-900">Guest Responsibilities</h3>
                </div>
                <p className="text-blue-800 text-sm mb-4">
                  Party organizers must respect venues, follow house rules, and comply with local noise restrictions.
                </p>
                <ul className="text-blue-700 text-sm space-y-1">
                  <li>• Respect venue and neighbors</li>
                  <li>• Follow agreed party rules</li>
                  <li>• Pay for any damages</li>
                  <li>• End parties on time</li>
                </ul>
              </div>

              {/* Payment Terms */}
              <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg p-6 border border-purple-200">
                <div className="flex items-center mb-4">
                  <CreditCard className="h-6 w-6 text-purple-600 mr-3" />
                  <h3 className="text-lg font-semibold text-purple-900">Payment & Cancellation</h3>
                </div>
                <p className="text-purple-800 text-sm mb-4">
                  Secure payments through Stripe with clear cancellation policies and refund terms.
                </p>
                <ul className="text-purple-700 text-sm space-y-1">
                  <li>• Secure payment processing</li>
                  <li>• 48-hour cancellation policy</li>
                  <li>• Partial refunds available</li>
                  <li>• Security deposit held (not insurance)</li>
                </ul>
              </div>

              {/* Platform Protection */}
              <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg p-6 border border-orange-200">
                <div className="flex items-center mb-4">
                  <Shield className="h-6 w-6 text-orange-600 mr-3" />
                  <h3 className="text-lg font-semibold text-orange-900">Platform Protection</h3>
                </div>
                <p className="text-orange-800 text-sm mb-4">
                  HouseGoing provides dispute resolution, insurance guidance, and 24/7 support for all users.
                </p>
                <ul className="text-orange-700 text-sm space-y-1">
                  <li>• Dispute resolution service</li>
                  <li>• Insurance requirements</li>
                  <li>• 24/7 customer support</li>
                  <li>• User verification system</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Full Terms Tab */}
        {activeTab === 'full' && (
          <div className="space-y-4">
            {termsData.map((section) => (
              <div key={section.id} className="bg-white border border-gray-200 rounded-lg shadow-sm">
                <button
                  onClick={() => toggleSection(section.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <div className="text-purple-600 mr-3">{section.icon}</div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{section.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{section.summary}</p>
                    </div>
                  </div>
                  {openSections.has(section.id) ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                
                {openSections.has(section.id) && (
                  <div className="px-6 pb-6">
                    <div className="prose prose-sm max-w-none">
                      <div className="whitespace-pre-line text-gray-700 leading-relaxed mb-4">
                        {section.content}
                      </div>
                      
                      {section.examples && (
                        <div className="bg-gray-50 rounded-lg p-4 mt-4">
                          <h4 className="font-medium text-gray-900 mb-2">Examples:</h4>
                          <ul className="space-y-1">
                            {section.examples.map((example, index) => (
                              <li key={index} className="text-sm text-gray-600 flex items-start">
                                <span className="text-purple-600 mr-2">•</span>
                                {example}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Contact Section */}
        <div className="mt-12 bg-gray-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Questions about our Terms?
          </h2>
          <p className="text-gray-600 mb-6">
            Our team is here to help clarify any questions about our terms and conditions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Contact Legal Team
            </a>
            <a
              href="/faq"
              className="inline-flex items-center px-6 py-3 bg-white text-purple-600 border border-purple-600 rounded-lg hover:bg-purple-50 transition-colors"
            >
              View FAQ
            </a>
          </div>
        </div>
      </div>
    </>
  );
}
