/**
 * Setup script for chat history tables in Supabase
 */
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupChatHistoryTables() {
  try {
    console.log('Setting up chat history tables...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'src', 'lib', 'supabase', 'chat-history-tables.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      throw error;
    }
    
    console.log('Chat history tables created successfully!');
    
    // Insert some sample data for testing
    await insertSampleData();
    
    console.log('Setup complete!');
  } catch (error) {
    console.error('Error setting up chat history tables:', error);
  }
}

async function insertSampleData() {
  try {
    console.log('Inserting sample data...');
    
    // Create a sample conversation
    const { data: conversation, error: conversationError } = await supabase
      .from('chat_conversations')
      .insert({
        user_id: 'sample-user',
        agent_type: 'sales',
        title: 'Sample Conversation'
      })
      .select()
      .single();
    
    if (conversationError) {
      throw conversationError;
    }
    
    // Add sample messages
    const messages = [
      {
        conversation_id: conversation.id,
        role: 'system',
        content: 'You are a helpful assistant for HouseGoing, a platform for booking party venues.',
        message_index: 0
      },
      {
        conversation_id: conversation.id,
        role: 'user',
        content: 'Hi, I\'m looking for a venue for a birthday party.',
        message_index: 1
      },
      {
        conversation_id: conversation.id,
        role: 'assistant',
        content: 'Hello! I\'d be happy to help you find a venue for a birthday party. Could you tell me how many guests you\'re expecting and where you\'d like the venue to be located?',
        message_index: 2
      },
      {
        conversation_id: conversation.id,
        role: 'user',
        content: 'About 30 people in Sydney.',
        message_index: 3
      },
      {
        conversation_id: conversation.id,
        role: 'assistant',
        content: 'Great! Here are some venue options in Sydney that would be perfect for a 30-person birthday party:\n\n1. Harbour View Terrace - Beautiful waterfront venue with stunning views\n2. Urban Party Loft - Modern space in the heart of the city\n3. Garden Pavilion - Outdoor venue with covered areas\n\nDo any of these interest you?',
        message_index: 4
      }
    ];
    
    const { error: messagesError } = await supabase
      .from('chat_messages')
      .insert(messages);
    
    if (messagesError) {
      throw messagesError;
    }
    
    console.log('Sample data inserted successfully!');
  } catch (error) {
    console.error('Error inserting sample data:', error);
  }
}

// Run the setup
setupChatHistoryTables();
