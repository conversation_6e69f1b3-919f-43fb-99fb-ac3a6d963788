<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing - JavaScript Test</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 2rem;
      background-color: #f9fafb;
      color: #111827;
      line-height: 1.5;
    }
    
    h1 {
      color: #7c3aed;
    }
    
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      padding: 2rem;
      margin-bottom: 2rem;
      max-width: 800px;
    }
    
    .button {
      display: inline-block;
      background-color: #7c3aed;
      color: white;
      font-weight: 500;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      text-decoration: none;
      transition: background-color 0.2s;
      border: none;
      cursor: pointer;
      margin-top: 1rem;
    }
    
    .button:hover {
      background-color: #6d28d9;
    }
  </style>
</head>
<body>
  <h1>HouseGoing JavaScript Test</h1>
  
  <div class="card">
    <h2>Testing JavaScript Functionality</h2>
    <p>This page tests if JavaScript is working correctly on your browser.</p>
    <p>If you see a green box in the top-right corner saying "JavaScript is working!", then JavaScript is loading correctly.</p>
    <p>You should also be able to click the button in that green box to trigger an alert.</p>
    
    <h3>Manual Test</h3>
    <p>Click the button below to manually test JavaScript:</p>
    <button class="button" onclick="alert('Manual test successful!')">Test JavaScript</button>
    
    <h3>Browser Information</h3>
    <div id="browser-info">Loading browser information...</div>
  </div>
  
  <script>
    // Display browser information
    document.getElementById('browser-info').innerHTML = `
      <ul>
        <li><strong>User Agent:</strong> ${navigator.userAgent}</li>
        <li><strong>Browser:</strong> ${navigator.appName}</li>
        <li><strong>Platform:</strong> ${navigator.platform}</li>
        <li><strong>Cookies Enabled:</strong> ${navigator.cookieEnabled}</li>
        <li><strong>Language:</strong> ${navigator.language}</li>
      </ul>
    `;
  </script>
  
  <!-- Load the test script -->
  <script src="/test.js"></script>
</body>
</html>
