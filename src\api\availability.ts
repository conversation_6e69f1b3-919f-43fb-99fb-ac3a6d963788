import { getSupabaseClient } from '../lib/supabase-client';

// Types for availability management
export interface VenueAvailabilitySettings {
  id?: string;
  venue_id: string;
  available_days: number[]; // 0 = Sunday, 1 = Monday, etc.
  default_start_time: string; // HH:MM:SS format
  default_end_time: string;
  min_booking_hours: number;
  max_booking_hours: number;
  lead_time_hours: number;
  max_advance_days: number;
  instant_booking_enabled: boolean;
}

export interface VenueDayAvailability {
  id?: string;
  venue_id: string;
  date: string; // YYYY-MM-DD format
  is_available: boolean;
  start_time?: string; // HH:MM:SS format
  end_time?: string;
  special_price?: number;
  notes?: string;
}

export interface VenueBlockedSlot {
  id?: string;
  venue_id: string;
  start_datetime: string; // ISO datetime
  end_datetime: string;
  reason?: string;
  block_type: 'manual' | 'maintenance' | 'personal' | 'holiday';
  is_recurring: boolean;
  recurrence_pattern?: 'weekly' | 'monthly' | 'yearly';
  recurrence_end_date?: string;
}

export interface VenueOperatingHours {
  id?: string;
  venue_id: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, etc.
  start_time: string;
  end_time: string;
  is_available: boolean;
}

export interface AvailabilityCheckResult {
  available: boolean;
  conflicts: string[];
  venue_id: string;
  requested_start: string;
  requested_end: string;
  settings: {
    min_booking_hours: number;
    max_booking_hours: number;
    lead_time_hours: number;
    instant_booking_enabled: boolean;
  };
}

/**
 * Get venue availability settings
 */
export async function getVenueAvailabilitySettings(venueId: string): Promise<VenueAvailabilitySettings | null> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_availability_settings')
      .select('*')
      .eq('venue_id', venueId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching availability settings:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getVenueAvailabilitySettings:', error);
    throw error;
  }
}

/**
 * Update venue availability settings
 */
export async function updateVenueAvailabilitySettings(settings: VenueAvailabilitySettings): Promise<VenueAvailabilitySettings> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_availability_settings')
      .upsert({
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating availability settings:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateVenueAvailabilitySettings:', error);
    throw error;
  }
}

/**
 * Get venue operating hours
 */
export async function getVenueOperatingHours(venueId: string): Promise<VenueOperatingHours[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_operating_hours')
      .select('*')
      .eq('venue_id', venueId)
      .order('day_of_week');

    if (error) {
      console.error('Error fetching operating hours:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getVenueOperatingHours:', error);
    throw error;
  }
}

/**
 * Update venue operating hours
 */
export async function updateVenueOperatingHours(venueId: string, operatingHours: Omit<VenueOperatingHours, 'id' | 'venue_id'>[]): Promise<VenueOperatingHours[]> {
  try {
    const supabase = getSupabaseClient();

    // Delete existing operating hours
    await supabase
      .from('venue_operating_hours')
      .delete()
      .eq('venue_id', venueId);

    // Insert new operating hours
    const hoursWithVenueId = operatingHours.map(hours => ({
      ...hours,
      venue_id: venueId,
      updated_at: new Date().toISOString()
    }));

    const { data, error } = await supabase
      .from('venue_operating_hours')
      .insert(hoursWithVenueId)
      .select();

    if (error) {
      console.error('Error updating operating hours:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in updateVenueOperatingHours:', error);
    throw error;
  }
}

/**
 * Get venue day availability overrides
 */
export async function getVenueDayAvailability(venueId: string, startDate: string, endDate: string): Promise<VenueDayAvailability[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_day_availability')
      .select('*')
      .eq('venue_id', venueId)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date');

    if (error) {
      console.error('Error fetching day availability:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getVenueDayAvailability:', error);
    throw error;
  }
}

/**
 * Update venue day availability
 */
export async function updateVenueDayAvailability(dayAvailability: VenueDayAvailability): Promise<VenueDayAvailability> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_day_availability')
      .upsert({
        ...dayAvailability,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating day availability:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateVenueDayAvailability:', error);
    throw error;
  }
}

/**
 * Get venue blocked slots
 */
export async function getVenueBlockedSlots(venueId: string, startDate: string, endDate: string): Promise<VenueBlockedSlot[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_blocked_slots')
      .select('*')
      .eq('venue_id', venueId)
      .gte('start_datetime', startDate)
      .lte('end_datetime', endDate)
      .order('start_datetime');

    if (error) {
      console.error('Error fetching blocked slots:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getVenueBlockedSlots:', error);
    throw error;
  }
}

/**
 * Add venue blocked slot
 */
export async function addVenueBlockedSlot(blockedSlot: Omit<VenueBlockedSlot, 'id'>): Promise<VenueBlockedSlot> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venue_blocked_slots')
      .insert({
        ...blockedSlot,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding blocked slot:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in addVenueBlockedSlot:', error);
    throw error;
  }
}

/**
 * Remove venue blocked slot
 */
export async function removeVenueBlockedSlot(blockedSlotId: string): Promise<void> {
  try {
    const supabase = getSupabaseClient();

    const { error } = await supabase
      .from('venue_blocked_slots')
      .delete()
      .eq('id', blockedSlotId);

    if (error) {
      console.error('Error removing blocked slot:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in removeVenueBlockedSlot:', error);
    throw error;
  }
}

/**
 * Check venue availability for specific datetime range
 */
export async function checkVenueAvailability(
  venueId: string,
  startDatetime: string,
  endDatetime: string
): Promise<AvailabilityCheckResult> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .rpc('check_venue_availability', {
        p_venue_id: venueId,
        p_start_datetime: startDatetime,
        p_end_datetime: endDatetime
      });

    if (error) {
      console.error('Error checking availability:', error);
      throw error;
    }

    // Return a properly formatted result
    return {
      available: data || false,
      conflicts: [],
      venue_id: venueId,
      requested_start: startDatetime,
      requested_end: endDatetime,
      settings: {
        min_booking_hours: 4,
        max_booking_hours: 12,
        lead_time_hours: 24,
        instant_booking_enabled: false
      }
    };
  } catch (error) {
    console.error('Error in checkVenueAvailability:', error);
    throw error;
  }
}

/**
 * Get venue availability calendar for date range
 */
export async function getVenueAvailabilityCalendar(
  venueId: string,
  startDate: string,
  endDate: string
): Promise<any> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .rpc('get_venue_availability_range', {
        p_venue_id: venueId,
        p_start_date: startDate,
        p_end_date: endDate
      });

    if (error) {
      console.error('Error getting availability calendar:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getVenueAvailabilityCalendar:', error);
    throw error;
  }
}

/**
 * Search for available venues
 */
export async function searchAvailableVenues(
  startDatetime: string,
  endDatetime: string,
  filters?: {
    suburb?: string;
    minCapacity?: number;
    maxPrice?: number;
  }
): Promise<any[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .rpc('get_available_venues', {
        p_start_datetime: startDatetime,
        p_end_datetime: endDatetime,
        p_suburb: filters?.suburb || null,
        p_min_capacity: filters?.minCapacity || null,
        p_max_price: filters?.maxPrice || null
      });

    if (error) {
      console.error('Error searching available venues:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchAvailableVenues:', error);
    throw error;
  }
}

/**
 * Batch update multiple day availabilities
 */
export async function batchUpdateDayAvailability(
  venueId: string,
  updates: Omit<VenueDayAvailability, 'venue_id'>[]
): Promise<VenueDayAvailability[]> {
  try {
    const supabase = getSupabaseClient();

    const updatesWithVenueId = updates.map(update => ({
      ...update,
      venue_id: venueId,
      updated_at: new Date().toISOString()
    }));

    const { data, error } = await supabase
      .from('venue_day_availability')
      .upsert(updatesWithVenueId)
      .select();

    if (error) {
      console.error('Error batch updating day availability:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in batchUpdateDayAvailability:', error);
    throw error;
  }
}
