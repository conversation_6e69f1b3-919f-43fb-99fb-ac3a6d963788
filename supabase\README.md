# Supabase Integration for HouseGoing

This directory contains the Supabase database schema and migration scripts for the HouseGoing platform.

## Database Schema

The HouseGoing application uses the following tables:

1. **user_profiles**: Stores user information and roles
2. **venues**: Stores venue/property information
3. **bookings**: Stores booking information
4. **reviews**: Stores reviews for venues
5. **messages**: Stores messages between users

## Setup Instructions

### Option 1: Using the SQL Editor (Recommended)

1. Go to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project: `fxqoowlruissctsgbljk`
3. Go to the SQL Editor
4. First, run the `create_execute_sql_function.sql` script:
   - Open `supabase/sql/create_execute_sql_function.sql`
   - Copy the contents
   - Paste into the SQL Editor
   - Click "Run"
5. Then, run the `create_tables.sql` script:
   - Open `supabase/sql/create_tables.sql`
   - Copy the contents
   - Paste into the SQL Editor
   - Click "Run"

### Option 2: Using the Web Interface

1. Start the application: `npm run dev`
2. Navigate to: `http://localhost:5173/setup-database.html`
3. Click the "Set Up Database" button
4. Check the logs on the page for success or error messages

### Option 3: Using the Node.js Script

1. Run: `npm run setup-db` or `npm run setup-db-direct`
2. Check the console for success or error messages

### Option 4: Set Environment Variables

Add the following environment variables to your `.env` file:

```
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 4. Set Up Clerk Integration

1. In your Clerk dashboard, go to JWT Templates
2. Create a new template for Supabase with the following claims:

```json
{
  "sub": "{{user.id}}",
  "email": "{{user.primary_email_address}}",
  "role": "{{user.public_metadata.role}}"
}
```

3. Set the template as active

## Verifying the Setup

After setting up the database, you can verify that the tables were created:

1. Go to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project: `fxqoowlruissctsgbljk`
3. Go to the Table Editor
4. You should see the following tables:
   - user_profiles
   - venues
   - bookings
   - reviews
   - messages

## Troubleshooting

If you encounter any issues:

1. Check the Supabase logs in the Dashboard
2. Make sure you have the correct permissions
3. Try running the SQL scripts one by one
4. Check for any error messages in the console

## Database URL and Keys

- **URL**: `https://fxqoowlruissctsgbljk.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5NDMyNTQsImV4cCI6MjA1OTUxOTI1NH0.ZE3ZyMccEZAmr3VsHceWzyg3o3a2Xu0QaijdYuDYTqk`
- **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4cW9vd2xydWlzc2N0c2dibGprIiwicm9zZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk0MzI1NCwiZXhwIjoyMDU5NTE5MjU0fQ.9qjOAPoSojpa-91l9jlAI79Rkw3Och4gCjuTWM6KBLI`

## Row Level Security

The following RLS policies are applied:

- Users can read and update their own profiles
- Service role can read, update, and insert all profiles

## Pre-registered Hosts

The migration script includes pre-registered hosts:

- Email: <EMAIL>
- Role: host
