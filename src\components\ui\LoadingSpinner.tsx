import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'light' | 'dark' | 'primary';
  className?: string;
}

export default function LoadingSpinner({ 
  size = 'md', 
  color = 'primary',
  className = '' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-6 w-6 border-2',
    lg: 'h-8 w-8 border-3'
  };
  
  const colorClasses = {
    light: 'border-white',
    dark: 'border-gray-600',
    primary: 'border-purple-600'
  };
  
  return (
    <div className={`inline-block ${className}`}>
      <div 
        className={`
          animate-spin rounded-full 
          ${sizeClasses[size]} 
          border-t-transparent 
          ${colorClasses[color]}
        `}
      />
    </div>
  );
}
