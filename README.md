# housegoing

## Overview

housegoing is a web application for [briefly describe the application's purpose, e.g., finding and booking venues]. It utilizes modern web technologies to provide a seamless user experience.

## Technologies Used

*   React: JavaScript library for building user interfaces.
*   TypeScript: Superset of JavaScript that adds static typing.
*   Vite: Fast build tool for modern web development.
*   Tailwind CSS: Utility-first CSS framework for styling.
*   Supabase: Open-source Firebase alternative for backend services (authentication, database, etc.).
*   Clerk: Authentication and user management service.
*   React Router DOM: For handling routing and navigation.
*   Lucide React: For icons.
*   ... (Add other technologies and libraries used)

## Installation

1.  **Clone the repository:**

    ```bash
    git clone [repository URL]
    cd housegoing
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Set up environment variables:**

    *   Create a `.env.local` file in the root directory.
    *   Add the necessary environment variables.  Refer to the `.env.example` file for guidance.  Key variables include:
        *   `VITE_SUPABASE_URL`: Your Supabase project URL.
        *   `VITE_SUPABASE_ANON_KEY`: Your Supabase anon key.
        *   `VITE_CLERK_PUBLISHABLE_KEY`: Your Clerk publishable key.
        *   `VITE_CLERK_SIGN_IN_URL`: Your Clerk sign-in URL.
        *   `VITE_CLERK_SIGN_UP_URL`: Your Clerk sign-up URL.
        *   `VITE_CLERK_AFTER_SIGN_IN_URL`: Your Clerk after sign-in URL.
        *   `VITE_CLERK_AFTER_SIGN_UP_URL`: Your Clerk after sign-up URL.
        *   `VITE_STRIPE_PUBLISHABLE_KEY`: Your Stripe publishable key.
        *   `STRIPE_SECRET_KEY`: Your Stripe secret key (for Netlify functions).

4.  **Set up Supabase:**

    *   Follow the instructions in the `AI_SETUP.md` file. This involves creating a Supabase project and setting up the necessary database schema.

5.  **Test Stripe Integration:**

    *   Visit `http://localhost:5175/test-stripe-payment` to test payment functionality.
    *   Use test card number: `************** 4242` with any future expiry date.

## Running the Application

```bash
npm run dev
```

This will start the development server. Open [http://localhost:5173](http://localhost:5173) in your browser to view the application.

## Building the Application

```bash
npm run build
```

This will build the production-ready application.

## Testing

(Add testing instructions here, once tests are implemented.  Consider using Cypress for end-to-end testing.)

## Contributing

Contributions are welcome! Please follow these guidelines:

1.  **Fork** the repository.
2.  **Create a new branch** for your feature or bug fix.  Name your branch descriptively (e.g., `feature/add-venue-search`, `fix/login-issue`).
3.  **Make your changes** and commit them with clear and concise commit messages.
4.  **Submit a pull request** to the `main` branch.  Include a description of your changes and any relevant context.

## License

MIT License

Copyright (c) [year] [Your Name]

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

## Contact

*   [Your Name]
*   [Your Email]
*   [Your Website/Portfolio (Optional)]
*   [GitHub Repository URL]

## Screenshots/Videos

(Add screenshots or links to videos showcasing the application)
