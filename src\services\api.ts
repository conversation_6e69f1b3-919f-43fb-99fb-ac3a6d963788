import { getSupabaseClient } from '../lib/supabase-client';

// Flag to track if Supabase is available
let supabaseAvailable = true;

// Test the connection
try {
  const supabase = getSupabaseClient();
  supabase.from('admin_users').select('id', { count: 'exact', head: true })
    .then(({ error }) => {
      if (error) {
        console.warn('Supabase connection test failed:', error.message);
        supabaseAvailable = false;
      } else {
        console.log('Supabase connection test successful');
        supabaseAvailable = true;
      }
    })
    .catch(error => {
      console.error('Supabase connection error:', error);
      supabaseAvailable = false;
    });
} catch (error) {
  console.error('Failed to initialize Supabase client:', error);
  supabaseAvailable = false;
}

// Function to check if Supabase is available
export function isSupabaseAvailable(): boolean {
  return supabaseAvailable;
}

// Export the function to get a Supabase client
export { getSupabaseClient };

// Mock data for development
export const mockBookings = [
  {
    id: '1',
    venue_id: '1',
    guest_id: 'user123',
    start_date: '2025-05-15T14:00:00',
    end_date: '2025-05-15T18:00:00',
    guests_count: 20,
    total_price: 1200,
    status: 'confirmed',
    created_at: '2025-05-01T10:00:00',
    updated_at: '2025-05-01T10:00:00'
  }
];

// Mock data for venues
export const mockVenues = [
  {
    id: '1',
    title: 'Beachfront Party Paradise',
    description: 'A beautiful beachfront venue perfect for parties and events.',
    address: '123 Beach Road, Sydney',
    images: ['https://images.unsplash.com/photo-1533929736458-ca588d08c8be'],
    price: 300,
    capacity: 50,
    host_id: 'host123',
    created_at: '2025-01-01T00:00:00',
    updated_at: '2025-01-01T00:00:00'
  }
];

// Mock data for users
export const mockUsers = [
  {
    id: 'user123',
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>'
  },
  {
    id: 'host123',
    first_name: 'Jane',
    last_name: 'Smith',
    email: '<EMAIL>'
  }
];
