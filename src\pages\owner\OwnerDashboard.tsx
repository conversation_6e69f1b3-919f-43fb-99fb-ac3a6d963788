/**
 * Owner Dashboard
 * 
 * Shows owner's property submissions with different statuses and actions
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { 
  Plus, 
  Edit, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Home,
  DollarSign,
  Users,
  Calendar
} from 'lucide-react';

interface PropertySubmission {
  id: string;
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  rejection_reason?: string;
  admin_notes?: string;
  images?: string[];
}

export default function OwnerDashboard() {
  const { user } = useUser();
  const navigate = useNavigate();
  const [properties, setProperties] = useState<PropertySubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');

  // Mock data for testing - in production this would come from database
  useEffect(() => {
    const loadOwnerProperties = async () => {
      setLoading(true);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data for current user's properties
      const mockProperties: PropertySubmission[] = [
        {
          id: 'mock-1',
          name: 'Harbour View Rooftop',
          address: '123 Circular Quay, Sydney NSW 2000',
          type: 'Rooftop',
          description: 'Beautiful rooftop venue with stunning harbour views, perfect for parties and events.',
          maxGuests: 50,
          price: 150,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          images: ['https://images.unsplash.com/photo-1566737236500-c8ac43014a67?w=400']
        },
        {
          id: 'mock-2',
          name: 'Industrial Warehouse Space',
          address: '456 Industrial Lane, Marrickville NSW 2204',
          type: 'Warehouse',
          description: 'Spacious industrial warehouse with exposed brick walls and high ceilings.',
          maxGuests: 200,
          price: 200,
          status: 'pending',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          images: ['https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400']
        },
        {
          id: 'mock-3',
          name: 'Beachside Garden Villa',
          address: '789 Beach Road, Bondi NSW 2026',
          type: 'Villa',
          description: 'Elegant beachside villa with beautiful gardens and ocean views.',
          maxGuests: 30,
          price: 120,
          status: 'approved',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          images: ['https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400']
        },
        {
          id: 'mock-4',
          name: 'Urban Loft Space',
          address: '321 King Street, Newtown NSW 2042',
          type: 'Loft',
          description: 'Modern urban loft with contemporary design and city views.',
          maxGuests: 80,
          price: 180,
          status: 'rejected',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          updated_at: new Date(Date.now() - 172800000).toISOString(),
          rejection_reason: 'Property photos are unclear and missing required documents',
          admin_notes: 'Please provide clearer photos of all rooms and upload your insurance certificate. The property has great potential!',
          images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400']
        },
        {
          id: 'mock-5',
          name: 'Backyard Pool Paradise',
          address: '654 Suburban Street, Manly NSW 2095',
          type: 'Backyard',
          description: 'Beautiful backyard with swimming pool and entertainment area.',
          maxGuests: 40,
          price: 100,
          status: 'rejected',
          created_at: new Date(Date.now() - 345600000).toISOString(),
          updated_at: new Date(Date.now() - 345600000).toISOString(),
          rejection_reason: 'Missing insurance documentation and pool safety compliance',
          admin_notes: 'Please provide pool safety certificate and public liability insurance before resubmitting.',
          images: ['https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400']
        }
      ];
      
      setProperties(mockProperties);
      setLoading(false);
    };

    loadOwnerProperties();
  }, []);

  const filteredProperties = properties.filter(property => {
    if (activeTab === 'all') return true;
    return property.status === activeTab;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'approved': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'rejected': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getActionButton = (property: PropertySubmission) => {
    switch (property.status) {
      case 'pending':
        return (
          <button
            onClick={() => navigate(`/owner/property/${property.id}`)}
            className="inline-flex items-center gap-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
          >
            <Eye className="w-4 h-4" />
            View Details
          </button>
        );
      case 'approved':
        return (
          <button
            onClick={() => navigate(`/owner/property/${property.id}/manage`)}
            className="inline-flex items-center gap-2 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors"
          >
            <Home className="w-4 h-4" />
            Manage Property
          </button>
        );
      case 'rejected':
        return (
          <button
            onClick={() => navigate(`/owner/property/${property.id}/edit`)}
            className="inline-flex items-center gap-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors"
          >
            <Edit className="w-4 h-4" />
            Edit & Resubmit
          </button>
        );
      default:
        return null;
    }
  };

  const stats = {
    total: properties.length,
    pending: properties.filter(p => p.status === 'pending').length,
    approved: properties.filter(p => p.status === 'approved').length,
    rejected: properties.filter(p => p.status === 'rejected').length
  };

  if (loading) {
    return (
      <div className="pt-32 px-4 sm:px-6 pb-16">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            <p className="ml-3 text-gray-600">Loading your properties...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Properties</h1>
            <p className="text-gray-600 mt-1">Manage your property submissions and listings</p>
          </div>
          <button
            onClick={() => navigate('/add-listing')}
            className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add New Property
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Home className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Properties</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Approved</p>
                <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Need Attention</p>
                <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { key: 'all', label: 'All Properties', count: stats.total },
                { key: 'pending', label: 'Pending Review', count: stats.pending },
                { key: 'approved', label: 'Approved', count: stats.approved },
                { key: 'rejected', label: 'Need Attention', count: stats.rejected }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </nav>
          </div>

          {/* Properties List */}
          <div className="p-6">
            {filteredProperties.length === 0 ? (
              <div className="text-center py-12">
                <Home className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {activeTab === 'all' ? 'No properties yet' : `No ${activeTab} properties`}
                </h3>
                <p className="text-gray-500 mb-6">
                  {activeTab === 'all' 
                    ? 'Get started by adding your first property listing.'
                    : `You don't have any ${activeTab} properties at the moment.`
                  }
                </p>
                {activeTab === 'all' && (
                  <button
                    onClick={() => navigate('/add-listing')}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    Add Your First Property
                  </button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredProperties.map((property) => (
                  <div key={property.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                    {/* Property Image */}
                    {property.images && property.images[0] && (
                      <div className="aspect-video">
                        <img
                          src={property.images[0]}
                          alt={property.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    
                    {/* Property Details */}
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">{property.name}</h3>
                          <p className="text-sm text-gray-600">{property.address}</p>
                        </div>
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(property.status)}`}>
                          {getStatusIcon(property.status)}
                          {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
                        </span>
                      </div>

                      <p className="text-sm text-gray-700 mb-4 line-clamp-2">{property.description}</p>

                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          {property.maxGuests} guests
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          ${property.price}/hr
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {new Date(property.created_at).toLocaleDateString()}
                        </div>
                      </div>

                      {/* Rejection Feedback */}
                      {property.status === 'rejected' && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                          <h4 className="text-sm font-medium text-red-800 mb-2">Admin Feedback</h4>
                          <p className="text-sm text-red-700 mb-2">{property.rejection_reason}</p>
                          {property.admin_notes && (
                            <p className="text-sm text-red-600">{property.admin_notes}</p>
                          )}
                        </div>
                      )}

                      {/* Action Button */}
                      <div className="flex justify-end">
                        {getActionButton(property)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
