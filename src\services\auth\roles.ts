import { getSupabaseClient } from '../api';

export type UserRole = 'guest' | 'host' | 'admin';

export interface UserProfile {
  id: string;
  email: string;
  role: UserRole;
  clerk_id?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get the current user's role from Supabase
 */
export async function getUserRole(): Promise<UserRole | null> {
  try {
    const supabase = getSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return null;

    // Get user profile from user_profiles table
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('clerk_id', user.id)
      .single();

    if (error || !profile) {
      console.error('Error fetching user role:', error);
      return null;
    }

    return profile.role as UserRole;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
}

/**
 * Check if the current user has a specific role
 */
export async function hasRole(role: UserRole | UserRole[]): Promise<boolean> {
  const userRole = await getUserRole();

  if (!userRole) return false;

  if (Array.isArray(role)) {
    return role.includes(userRole);
  }

  return userRole === role;
}

/**
 * Update a user's role
 */
export async function updateUserRole(userId: string, role: UserRole): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    const { error } = await supabase
      .from('user_profiles')
      .update({ role, updated_at: new Date().toISOString() })
      .eq('clerk_id', userId);

    if (error) {
      console.error('Error updating user role:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating user role:', error);
    return false;
  }
}
