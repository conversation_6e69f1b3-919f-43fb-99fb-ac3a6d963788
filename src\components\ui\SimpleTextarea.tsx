import React, { forwardRef } from 'react';

interface SimpleTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  helperText?: string;
  isLoading?: boolean;
  containerClassName?: string;
  characterCount?: boolean;
}

const SimpleTextarea = forwardRef<HTMLTextAreaElement, SimpleTextareaProps>(({
  label,
  error,
  helperText,
  isLoading = false,
  className = '',
  containerClassName = '',
  disabled,
  required,
  id,
  maxLength,
  value = '',
  characterCount = false,
  ...props
}, ref) => {
  // Generate a unique ID if none is provided
  const textareaId = id || `textarea-${label.toLowerCase().replace(/\s+/g, '-')}`;
  
  // Determine if the textarea is disabled
  const isDisabled = disabled || isLoading;
  
  // Calculate character count
  const currentLength = typeof value === 'string' ? value.length : 0;
  const showCharacterCount = characterCount && maxLength;
  
  return (
    <div className={`mb-4 ${containerClassName}`}>
      <div className="flex justify-between items-center mb-2">
        <label 
          htmlFor={textareaId}
          className="block text-gray-700 text-sm font-medium flex items-center"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        
        {showCharacterCount && (
          <span className={`text-xs ${currentLength > (maxLength as number) ? 'text-red-500' : 'text-gray-500'}`}>
            {currentLength}/{maxLength}
          </span>
        )}
      </div>
      
      <div className="relative">
        <textarea
          id={textareaId}
          ref={ref}
          disabled={isDisabled}
          maxLength={maxLength}
          value={value}
          className={`
            w-full rounded-lg border transition-all duration-200
            px-4 py-2 min-h-[100px]
            ${error 
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300 focus:ring-purple-500 focus:border-purple-500'}
            focus:outline-none focus:ring-2 focus:ring-opacity-50
            disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed
            resize-y
            ${className}
          `}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${textareaId}-error` : helperText ? `${textareaId}-helper` : undefined}
          {...props}
        />
        
        {isLoading && (
          <div className="absolute top-2 right-2">
            <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        )}
      </div>
      
      {error && (
        <p 
          id={`${textareaId}-error`} 
          className="mt-1 text-sm text-red-600 flex items-center"
          role="alert"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-4 w-4 mr-1" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
              clipRule="evenodd" 
            />
          </svg>
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p id={`${textareaId}-helper`} className="mt-1 text-sm text-gray-500">
          {helperText}
        </p>
      )}
    </div>
  );
});

SimpleTextarea.displayName = 'SimpleTextarea';

export default SimpleTextarea;
