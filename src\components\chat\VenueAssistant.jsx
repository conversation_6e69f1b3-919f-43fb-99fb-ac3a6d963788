import React, { useState, useRef, useEffect } from 'react';
import { Send, X, MessageSquare, RefreshCw, Maximize2, Minimize2 } from 'lucide-react';
import { aiService } from '../../services/aiService';

// Mock venue data
const mockVenues = [
  {
    id: 'v1',
    name: 'Harbour View Terrace',
    description: 'Stunning waterfront venue with panoramic harbour views and modern amenities.',
    capacity: 80,
    hours: '10am-11pm',
    price: 250,
    features: ['Waterfront location', 'Outdoor deck', 'Sound system', 'Bar area'],
    location: 'Sydney Harbour',
    images: ['/venues/harbour-view.jpg']
  },
  {
    id: 'v2',
    name: 'The Garden Pavilion',
    description: 'Elegant garden venue surrounded by lush greenery and flowering plants.',
    capacity: 120,
    hours: '9am-12am',
    price: 320,
    features: ['Garden setting', 'Marquee option', 'Catering kitchen', 'Parking'],
    location: 'Eastern Suburbs',
    images: ['/venues/garden-pavilion.jpg']
  },
  {
    id: 'v3',
    name: 'Urban Loft Space',
    description: 'Industrial chic warehouse conversion with exposed brick and high ceilings.',
    capacity: 150,
    hours: '2pm-2am',
    price: 380,
    features: ['Industrial design', 'DJ booth', 'Lighting rig', 'Rooftop access'],
    location: 'Inner West',
    images: ['/venues/urban-loft.jpg']
  },
  {
    id: 'v4',
    name: 'Beachside Cabana',
    description: 'Relaxed beach venue with direct sand access and coastal vibes.',
    capacity: 60,
    hours: '8am-10pm',
    price: 200,
    features: ['Beach access', 'BBQ facilities', 'Outdoor shower', 'Covered area'],
    location: 'Northern Beaches',
    images: ['/venues/beachside-cabana.jpg']
  },
  {
    id: 'v5',
    name: 'Heritage Hall',
    description: 'Stunning restored heritage building with classic architecture and modern facilities.',
    capacity: 200,
    hours: '10am-11pm',
    price: 450,
    features: ['Historic building', 'Grand staircase', 'Chandeliers', 'Dance floor'],
    location: 'CBD',
    images: ['/venues/heritage-hall.jpg']
  },
  {
    id: 'v6',
    name: 'Poolside Paradise',
    description: 'Modern venue with a large swimming pool and entertainment deck.',
    capacity: 80,
    hours: '11am-10pm',
    price: 300,
    features: ['Swimming pool', 'Cabanas', 'Outdoor speakers', 'BBQ area'],
    location: 'Eastern Suburbs',
    images: ['/venues/poolside-paradise.jpg']
  },
  {
    id: 'v7',
    name: 'Skyline Penthouse',
    description: 'Luxurious penthouse venue with 360-degree city views and upscale amenities.',
    capacity: 50,
    hours: '4pm-1am',
    price: 500,
    features: ['City views', 'Private elevator', 'Premium sound system', 'Full bar'],
    location: 'CBD',
    images: ['/venues/skyline-penthouse.jpg']
  },
  {
    id: 'v8',
    name: 'Rustic Barn',
    description: 'Charming countryside barn with rustic features and open space.',
    capacity: 100,
    hours: '10am-11pm',
    price: 280,
    features: ['Rustic setting', 'Fire pit', 'String lights', 'Country views'],
    location: 'Western Sydney',
    images: ['/venues/rustic-barn.jpg']
  }
];

// Loading messages to show while waiting for a response
const loadingMessages = [
  "🔍 Finding perfect venues for you...",
  "📅 Checking availability...",
  "🎉 Searching for amazing party spots...",
  "✨ Looking for the best match...",
  "🍾 Checking BYO options...",
  "📍 Finding venues in your area...",
  "💎 Hunting for hidden gems...",
  "🏠 Almost there, finding something special..."
];

export default function VenueAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [messages, setMessages] = useState([
    { role: 'assistant', content: "Hi there! 🏠✨ I'm Homie, your friendly venue finder! I'm super excited to help you discover the perfect party spot for your special event. What kind of celebration are you planning? 🎉" }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [sessionId, setSessionId] = useState(null);
  const [isFirstMessage, setIsFirstMessage] = useState(true);

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Listen for custom events to open chat
  useEffect(() => {
    const handleOpenChat = () => {
      setIsOpen(true);
    };

    window.addEventListener('openChat', handleOpenChat);
    return () => window.removeEventListener('openChat', handleOpenChat);
  }, []);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Get session ID from localStorage or create a new one
  useEffect(() => {
    const storedSessionId = localStorage.getItem('venueAssistantSessionId');
    if (storedSessionId) {
      setSessionId(storedSessionId);
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const toggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  // Filter venues based on user criteria
  const filterVenues = (message) => {
    const lowerMessage = message.toLowerCase();
    let filteredVenues = [...mockVenues];

    // Filter by capacity/guests
    const guestMatch = lowerMessage.match(/(\d+)\s*(people|guests|persons|capacity)/);
    if (guestMatch) {
      const guestCount = parseInt(guestMatch[1]);
      filteredVenues = filteredVenues.filter(venue => venue.capacity >= guestCount);
    }

    // Filter by location
    const locations = ['sydney', 'harbour', 'beach', 'northern beaches', 'eastern suburbs', 'inner west', 'western sydney', 'cbd'];
    for (const location of locations) {
      if (lowerMessage.includes(location)) {
        filteredVenues = filteredVenues.filter(venue =>
          venue.location.toLowerCase().includes(location) ||
          venue.description.toLowerCase().includes(location)
        );
      }
    }

    // Filter by features
    const features = ['pool', 'garden', 'outdoor', 'waterfront', 'beach', 'view', 'historic', 'heritage', 'modern', 'rustic'];
    for (const feature of features) {
      if (lowerMessage.includes(feature)) {
        filteredVenues = filteredVenues.filter(venue =>
          venue.features.some(f => f.toLowerCase().includes(feature)) ||
          venue.description.toLowerCase().includes(feature)
        );
      }
    }

    // Filter by event type
    if (lowerMessage.includes('wedding')) {
      filteredVenues = filteredVenues.filter(venue => venue.capacity >= 50);
    } else if (lowerMessage.includes('birthday') || lowerMessage.includes('party')) {
      // Birthday parties can be any venue
    } else if (lowerMessage.includes('corporate') || lowerMessage.includes('business')) {
      filteredVenues = filteredVenues.filter(venue =>
        !venue.name.toLowerCase().includes('beach') &&
        !venue.name.toLowerCase().includes('pool')
      );
    }

    // Filter by price
    const priceMatch = lowerMessage.match(/(\d+)\s*(dollars|aud|\$)/);
    if (priceMatch) {
      const priceLimit = parseInt(priceMatch[1]);
      filteredVenues = filteredVenues.filter(venue => venue.price <= priceLimit);
    }

    // If no venues match or no criteria provided, return all venues
    return filteredVenues.length > 0 ? filteredVenues : mockVenues;
  };

  // Generate follow-up questions based on user input
  const generateFollowUp = (message, venues) => {
    const lowerMessage = message.toLowerCase();
    const questions = [];

    // Check for missing criteria
    if (!lowerMessage.match(/(\d+)\s*(people|guests|persons|capacity)/)) {
      questions.push("How many guests are you planning to host?");
    }

    if (!lowerMessage.includes('sydney') &&
        !lowerMessage.includes('harbour') &&
        !lowerMessage.includes('beach') &&
        !lowerMessage.includes('eastern') &&
        !lowerMessage.includes('western') &&
        !lowerMessage.includes('northern') &&
        !lowerMessage.includes('cbd')) {
      questions.push("Do you have a preferred location in Sydney?");
    }

    if (!lowerMessage.includes('wedding') &&
        !lowerMessage.includes('birthday') &&
        !lowerMessage.includes('party') &&
        !lowerMessage.includes('corporate') &&
        !lowerMessage.includes('business')) {
      questions.push("What type of event are you planning?");
    }

    // Limit to 1-2 questions
    return questions.slice(0, 2);
  };

  // Format venue suggestions
  const formatVenueSuggestions = (venues, limit = 3) => {
    const selectedVenues = venues.slice(0, limit);

    return `Here are some venues that match your needs:\n\n${
      selectedVenues.map(venue =>
        `**${venue.name}** - ${venue.description}\n` +
        `• Capacity: ${venue.capacity} guests\n` +
        `• Hours: ${venue.hours}\n` +
        `• Starting at $${venue.price}/hour\n` +
        `• Features: ${venue.features.slice(0, 2).join(', ')}`
      ).join('\n\n')
    }`;
  };

  const sendMessage = async () => {
    if (!input.trim()) return;

    const userMessage = input;

    // Add user message
    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setIsLoading(true);
    setInput('');

    // Show random loading message
    const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
    setLoadingMessage(randomMessage);

    try {
      // Get current page context
      const context = {
        currentPage: 'venue-search',
        searchQuery: userMessage,
        userLocation: 'Sydney, NSW' // Could be dynamic based on user location
      };

      // Call AI service for customer assistance
      const response = await aiService.getCustomerAssistance(userMessage, context);

      let assistantResponse = '';

      if (response.success && response.message) {
        assistantResponse = response.message;

        // If this is the first message or user is asking for venues, add venue suggestions
        if (isFirstMessage || userMessage.toLowerCase().includes('venue') || userMessage.toLowerCase().includes('find') || userMessage.toLowerCase().includes('looking')) {
          const filteredVenues = filterVenues(userMessage);
          const venueSuggestions = formatVenueSuggestions(filteredVenues, 3);
          assistantResponse += '\n\n' + venueSuggestions;
          setIsFirstMessage(false);
        }
      } else {
        // Fallback to local venue filtering if AI fails
        const filteredVenues = filterVenues(userMessage);
        const followUpQuestions = generateFollowUp(userMessage, filteredVenues);

        assistantResponse = "Hey there! 🎉 I'm so excited to help you find some amazing venues! " + formatVenueSuggestions(filteredVenues);

        if (followUpQuestions.length > 0) {
          assistantResponse += '\n\n' + followUpQuestions.join(' ');
        }
      }

      // Add assistant response
      setMessages(prev => [...prev, { role: 'assistant', content: assistantResponse }]);

      // Generate a session ID if one doesn't exist
      if (!sessionId) {
        const newSessionId = Date.now().toString();
        localStorage.setItem('venueAssistantSessionId', newSessionId);
        setSessionId(newSessionId);
      }
    } catch (error) {
      console.error('Error generating response:', error);

      // Fallback to local venue filtering
      const filteredVenues = filterVenues(userMessage);
      const fallbackResponse = "I'm having a bit of trouble with my connection, but I can still help! " + formatVenueSuggestions(filteredVenues);

      setMessages(prev => [...prev, { role: 'assistant', content: fallbackResponse }]);
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const resetConversation = () => {
    setMessages([
      { role: 'assistant', content: "Hi there! 🏠✨ I'm Homie, your friendly venue finder! I'm super excited to help you discover the perfect party spot for your special event. What kind of celebration are you planning? 🎉" }
    ]);
    localStorage.removeItem('venueAssistantSessionId');
    setSessionId(null);
    setIsFirstMessage(true);
  };

  return (
    <>
      {/* Chat button */}
      <button
        onClick={toggleChat}
        data-chat-widget
        className="fixed bottom-4 right-4 p-4 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-colors z-50"
        aria-label="Chat with venue assistant"
      >
        {isOpen ? <X size={24} /> : <MessageSquare size={24} />}
      </button>

      {/* Chat window */}
      {isOpen && (
        <div className={`fixed z-50 bg-white rounded-lg shadow-xl flex flex-col overflow-hidden border border-gray-200 transition-all duration-300 ${isMaximized
          ? 'inset-4 md:inset-10'
          : 'bottom-20 right-4 w-80 sm:w-96 h-[32rem]'}`}>
          {/* Header */}
          <div className="bg-purple-600 text-white p-4 flex justify-between items-center">
            <div>
              <h3 className="font-medium">Chat with Homie 🏠✨</h3>
              <p className="text-xs text-purple-100">Your Friendly Venue Assistant</p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={resetConversation}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Reset conversation"
              >
                <RefreshCw size={16} />
              </button>
              <button
                onClick={toggleMaximize}
                className="text-white hover:text-purple-200 p-1 rounded"
                title={isMaximized ? "Minimize" : "Maximize"}
              >
                {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
              </button>
              <button
                onClick={toggleChat}
                className="text-white hover:text-purple-200 p-1 rounded"
                title="Close"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`mb-4 ${message.role === 'user' ? 'text-right' : ''}`}
              >
                <div
                  className={`inline-block max-w-[85%] p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-purple-600 text-white rounded-tr-none'
                      : 'bg-white border border-gray-200 rounded-tl-none'
                  }`}
                >
                  {message.content.split('\n').map((line, i) => (
                    <p key={i} className={line.startsWith('**') ? 'font-bold' : ''}>
                      {line.replace(/\*\*/g, '')}
                    </p>
                  ))}
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="mb-4">
                <div className="inline-block max-w-[85%] p-3 rounded-lg bg-white border border-gray-200 rounded-tl-none">
                  <p className="text-gray-500 mb-1">{loadingMessage}</p>
                  <div className="flex space-x-2">
                    <div className="w-2 h-2 bg-purple-300 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce delay-100"></div>
                    <div className="w-2 h-2 bg-purple-700 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t p-4 bg-white">
            <div className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Tell me about your dream party venue! 🎉"
                className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                disabled={isLoading}
              />
              <button
                onClick={sendMessage}
                disabled={isLoading || !input.trim()}
                className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
