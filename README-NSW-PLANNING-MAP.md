# NSW Planning Map Integration

This project integrates address lookup, mapping, and spatial data layers (zoning/LGA) using Leaflet.js, Mapbox tiles, and the Maps.co Geocoding API.

## Overview

The NSW Planning Map Integration provides a comprehensive solution for:

1. **Address Search & Geocoding**: Users can enter an address and get precise coordinates using the Maps.co API.
2. **Interactive Mapping**: A Leaflet.js map displays the location with Mapbox basemap tiles.
3. **Spatial Data Retrieval**: The system fetches zoning and LGA (Local Government Area) data from NSW Planning Portal WFS endpoints.
4. **Data Visualization**: Zoning and LGA boundaries are displayed as overlays on the map.
5. **Party Planning Information**: Based on zoning data, the system provides noise curfew times and party suitability ratings.

## Architecture

The integration consists of the following components:

### Frontend Components

- **NSWPlanningMap**: React component that handles map initialization, geocoding, and spatial data display.
- **NSWPlanningMapPage**: Page component that uses the map component and displays planning information.

### Backend Services

- **WFS Proxy Server**: Express server that proxies requests to NSW Planning Portal WFS services to avoid CORS issues.
- **NSW Planning Server**: Express server that provides endpoints for zoning and council data lookup.

### Utility Services

- **Geocoding Service**: TypeScript module that handles address geocoding using the Maps.co API.
- **WFS Service**: JavaScript module with utility functions for working with WFS endpoints.

## Setup Instructions

### Prerequisites

- Node.js 14+ and npm
- Mapbox access token
- Maps.co API key

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Install dependencies:
   ```
   npm install
   cd server && npm install
   cd ../mcp-servers/nsw-planning && npm install
   cd ../..
   ```

3. Configure environment variables:
   - Create a `.env` file in the root directory with the following variables:
     ```
     MAPBOX_TOKEN=your_mapbox_token
     MAPS_CO_API_KEY=your_maps_co_api_key
     WFS_PROXY_PORT=3001
     NSW_PLANNING_PORT=3002
     ```

### Starting the Servers

Use the provided script to start both the WFS proxy server and the NSW Planning server:

```
node start-servers.js
```

Or start them individually:

```
cd server && npm start
cd ../mcp-servers/nsw-planning && npm start
```

## Usage

### Basic Component Usage

```jsx
import NSWPlanningMap from '../components/map/NSWPlanningMap';

const MyComponent = () => {
  const handleDataLoaded = (data) => {
    console.log('Spatial data loaded:', data);
    // Process the data as needed
  };

  return (
    <div>
      <h1>NSW Planning Map</h1>
      <NSWPlanningMap onDataLoaded={handleDataLoaded} />
    </div>
  );
};
```

### Testing

1. Run the standalone test HTML file:
   ```
   open test-map-integration.html
   ```

2. Test the WFS integration:
   ```
   cd mcp-servers/nsw-planning && npm test
   ```

## API Endpoints

### WFS Proxy Server

- **POST /api/wfs**: Proxies requests to NSW Planning Portal WFS services.
- **GET /api/wfs/health**: Health check endpoint.

### NSW Planning Server

- **POST /api/nsw-planning/lookup**: Gets zoning and council information for a location.

## Data Sources

### NSW Planning Portal WFS Endpoints

- **Zoning**: `https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer`
- **LGA**: `https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer`

### NSW Planning Portal WMS Endpoints

- **Zoning**: `https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer`
- **LGA**: `https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer`

### Maps.co Geocoding API

- **Forward Geocoding**: `https://geocode.maps.co/search`
- **Reverse Geocoding**: `https://geocode.maps.co/reverse`

## Performance Optimizations

- **Caching**: Both geocoding and WFS requests are cached to reduce API calls.
- **WMS Layers**: Used for visual overlays to reduce data transfer.
- **GeoJSON Filtering**: Only features at the selected location are fetched.

## Error Handling

The integration includes comprehensive error handling for:
- Failed geocoding requests
- Failed WFS requests
- Empty or invalid responses

## Browser Compatibility

The integration is compatible with all modern browsers that support:
- ES6+ JavaScript
- Fetch API
- Leaflet.js

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Leaflet.js](https://leafletjs.com/) for the mapping library
- [Mapbox](https://www.mapbox.com/) for the basemap tiles
- [Maps.co](https://maps.co/) for the geocoding API
- [NSW Planning Portal](https://www.planningportal.nsw.gov.au/) for the spatial data
