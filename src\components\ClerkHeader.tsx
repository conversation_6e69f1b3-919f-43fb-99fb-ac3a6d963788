import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Menu } from 'lucide-react';
import Logo from './navigation/Logo';
import MainNav from './navigation/MainNav';
import MobileMenu from './navigation/MobileMenu';
import ClerkAuthButtons from './auth/ClerkAuthButtons';
import ClerkMobileAuthStatus from './auth/ClerkMobileAuthStatus';

export default function ClerkHeader() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  return (
    <header className="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md z-50 border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <div className="flex justify-between items-center py-4">
          <Logo />
          <MainNav />
          <div className="hidden md:flex items-center space-x-4">
            <ClerkAuthButtons />
          </div>
          {/* Mobile auth status indicator */}
          <ClerkMobileAuthStatus />
          <button
            className="md:hidden"
            aria-label="Menu"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Menu className="h-6 w-6 text-gray-700" />
          </button>
        </div>
      </div>
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        currentPath={location.pathname}
      />
    </header>
  );
}
