import React, { useState, useEffect, useRef } from 'react';
import { BsRobot } from 'react-icons/bs';
import { X, Maximize2, Minimize2 } from 'lucide-react';
import { HostAIAssistant } from './HostAIAssistant.jsx';

/**
 * A floating AI Assistant button that can be added to any page in the host portal
 * @param {Object} props
 * @param {string} props.context - The context of the current page (e.g., 'dashboard', 'properties', 'earnings')
 * @param {string} props.position - The position of the button ('bottom-right', 'bottom-left', 'top-right', 'top-left')
 */
export const AIAssistantButton = ({ context = 'general', position = 'bottom-right' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [suggestion, setSuggestion] = useState('');
  const assistantRef = useRef(null);

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };

  // Context-specific suggestions
  const contextSuggestions = {
    dashboard: [
      'How can I improve my dashboard metrics?',
      'What do these statistics mean?',
      'How can I get more bookings?'
    ],
    properties: [
      'How can I optimize my property listings?',
      'What makes a good property photo?',
      'How should I price my venue?'
    ],
    bookings: [
      'How can I manage multiple bookings?',
      'What\'s the best way to handle cancellations?',
      'How can I increase my booking rate?'
    ],
    earnings: [
      'How can I increase my revenue?',
      'What\'s the average earnings for similar venues?',
      'How should I handle taxes for my venue income?'
    ],
    general: [
      'How can I optimize my venue listings?',
      'Tell me about Party Score calculations',
      'What pricing strategies work best?'
    ]
  };

  // Listen for suggestion clicks from context buttons
  useEffect(() => {
    const handleSuggestionClick = (event) => {
      const { suggestion } = event.detail;
      setSuggestion(suggestion);
      setIsOpen(true);
    };

    document.addEventListener('ai-suggestion-click', handleSuggestionClick);
    return () => {
      document.removeEventListener('ai-suggestion-click', handleSuggestionClick);
    };
  }, []);

  // Pass suggestion to child component when it changes
  useEffect(() => {
    if (suggestion && assistantRef.current) {
      assistantRef.current.handleSuggestion(suggestion);
      setSuggestion('');
    }
  }, [suggestion]);

  const toggleAssistant = () => {
    setIsOpen(!isOpen);
  };

  const toggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  return (
    <>
      {/* Floating button */}
      <button
        onClick={toggleAssistant}
        className={`fixed ${positionClasses[position]} z-50 p-3 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-all duration-300 flex items-center justify-center`}
        aria-label="AI Assistant"
      >
        {isOpen ? <X size={24} /> : <BsRobot size={24} />}
      </button>

      {/* AI Assistant panel */}
      {isOpen && (
        <div className={`fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden transition-all duration-300 ${isMaximized
          ? 'inset-4 md:inset-10'
          : 'bottom-20 right-4 w-80 md:w-96'}`}>
          <div className="p-4 bg-purple-600 text-white flex justify-between items-center">
            <div className="flex items-center">
              <BsRobot className="text-xl mr-2" />
              <div>
                <h3 className="font-bold">AI Host Assistant</h3>
                <p className="text-xs opacity-80">Your personal venue optimization guide</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleMaximize}
                className="text-white hover:text-purple-200"
                title={isMaximized ? "Minimize" : "Maximize"}
              >
                {isMaximized ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
              </button>
              <button
                onClick={toggleAssistant}
                className="text-white hover:text-purple-200"
                title="Close"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Suggestions based on context */}
          <div className="p-4 bg-purple-50 border-b border-purple-100">
            <p className="text-sm text-purple-800 font-medium mb-2">Quick questions for this page:</p>
            <div className="flex flex-wrap gap-2">
              {contextSuggestions[context]?.map((suggestion, index) => (
                <button
                  key={index}
                  className="text-xs bg-white text-purple-700 px-3 py-1 rounded-full border border-purple-200 hover:bg-purple-100 transition-colors"
                  onClick={() => {
                    // This will be handled by the parent component
                    const event = new CustomEvent('ai-suggestion-click', {
                      detail: { suggestion }
                    });
                    document.dispatchEvent(event);
                  }}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>

          <div className={`overflow-hidden ${isMaximized ? 'h-[calc(100%-120px)]' : 'max-h-[500px]'}`}>
            <HostAIAssistant
              ref={assistantRef}
              context={context}
              isMaximized={isMaximized}
            />
          </div>
        </div>
      )}
    </>
  );
};
