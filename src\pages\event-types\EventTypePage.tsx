import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Calendar, Users, Clock, Star, ArrowRight, PartyPopper } from 'lucide-react';

// Event type data
const eventTypeData: Record<string, {
  name: string;
  description: string;
  longDescription: string;
  features: string[];
  averagePrice: string;
  averageGuests: string;
  duration: string;
  popularVenues: string[];
  seoTitle: string;
  seoDescription: string;
}> = {
  birthday: {
    name: 'Birthday Parties',
    description: 'Celebrate special birthdays with memorable venue experiences',
    longDescription: 'Birthday parties deserve special venues that create lasting memories. Whether it\'s a milestone birthday, kids party, or intimate celebration, find the perfect space with the right atmosphere, capacity, and amenities for your birthday celebration.',
    features: ['Flexible decorations', 'Kitchen access', 'Sound system', 'Party lighting', 'Photo opportunities'],
    averagePrice: '$200-500/hour',
    averageGuests: '20-50 people',
    duration: '4-6 hours',
    popularVenues: ['Party houses', 'Function rooms', 'Garden venues', 'Rooftop spaces'],
    seoTitle: 'Birthday Party Venue Hire Sydney NSW | Birthday Party Spaces',
    seoDescription: 'Book the perfect birthday party venue in Sydney. Find party spaces for milestone birthdays, kids parties & celebrations with great amenities.'
  },
  corporate: {
    name: 'Corporate Events',
    description: 'Professional venues for business meetings, conferences, and corporate functions',
    longDescription: 'Corporate events require professional, well-equipped venues that reflect your business standards. From board meetings to large conferences, team building events to product launches, find venues with the right technology, catering options, and professional atmosphere.',
    features: ['AV equipment', 'WiFi', 'Catering facilities', 'Professional atmosphere', 'Parking'],
    averagePrice: '$300-800/hour',
    averageGuests: '10-200 people',
    duration: '2-8 hours',
    popularVenues: ['Conference centers', 'Hotel function rooms', 'Business centers', 'Modern venues'],
    seoTitle: 'Corporate Event Venue Hire Sydney NSW | Business Function Spaces',
    seoDescription: 'Professional corporate event venues in Sydney. Book conference rooms, meeting spaces & function venues for business events with full AV facilities.'
  },
  wedding: {
    name: 'Wedding Celebrations',
    description: 'Beautiful venues for wedding receptions and celebrations',
    longDescription: 'Wedding celebrations deserve extraordinary venues that match the significance of your special day. Find reception venues, ceremony spaces, and celebration halls that provide the perfect backdrop for your wedding with elegant settings, catering facilities, and romantic ambiance.',
    features: ['Elegant settings', 'Catering kitchens', 'Bridal suites', 'Dance floors', 'Romantic lighting'],
    averagePrice: '$500-1500/hour',
    averageGuests: '50-150 people',
    duration: '6-12 hours',
    popularVenues: ['Reception halls', 'Garden venues', 'Waterfront venues', 'Historic venues'],
    seoTitle: 'Wedding Venue Hire Sydney NSW | Wedding Reception Venues',
    seoDescription: 'Beautiful wedding venues in Sydney for your special day. Book elegant reception venues, ceremony spaces & celebration halls with catering facilities.'
  },
  graduation: {
    name: 'Graduation Parties',
    description: 'Celebrate academic achievements with memorable graduation venues',
    longDescription: 'Graduation parties mark important milestones and deserve special celebration venues. Whether it\'s high school graduation, university completion, or professional qualifications, find venues that can accommodate family gatherings and provide the right atmosphere for this achievement celebration.',
    features: ['Family-friendly', 'Photo areas', 'Flexible seating', 'Catering options', 'Celebration atmosphere'],
    averagePrice: '$250-600/hour',
    averageGuests: '30-80 people',
    duration: '3-6 hours',
    popularVenues: ['Function rooms', 'Garden venues', 'Community halls', 'Restaurant venues'],
    seoTitle: 'Graduation Party Venue Hire Sydney NSW | Graduation Celebration Spaces',
    seoDescription: 'Book graduation party venues in Sydney to celebrate academic achievements. Family-friendly spaces perfect for graduation celebrations & milestone events.'
  },
  engagement: {
    name: 'Engagement Parties',
    description: 'Romantic venues perfect for celebrating your engagement',
    longDescription: 'Engagement parties are the perfect way to celebrate your upcoming marriage with family and friends. Find romantic, intimate venues that provide the ideal setting for this special announcement with beautiful ambiance, catering facilities, and memorable atmospheres.',
    features: ['Romantic ambiance', 'Intimate settings', 'Photo opportunities', 'Catering facilities', 'Elegant decor'],
    averagePrice: '$300-700/hour',
    averageGuests: '25-75 people',
    duration: '4-6 hours',
    popularVenues: ['Romantic restaurants', 'Garden venues', 'Waterfront spaces', 'Boutique venues'],
    seoTitle: 'Engagement Party Venue Hire Sydney NSW | Romantic Celebration Spaces',
    seoDescription: 'Romantic engagement party venues in Sydney. Book intimate spaces perfect for celebrating your engagement with beautiful ambiance & catering.'
  },
  reunion: {
    name: 'Reunion Events',
    description: 'Venues for family reunions, school reunions, and group gatherings',
    longDescription: 'Reunion events bring people together after time apart, requiring venues that can accommodate various group sizes and provide comfortable, welcoming atmospheres. Perfect for school reunions, family gatherings, military reunions, and other group celebrations.',
    features: ['Large capacity', 'Flexible layouts', 'Audio systems', 'Parking', 'Accessible facilities'],
    averagePrice: '$200-500/hour',
    averageGuests: '40-150 people',
    duration: '4-8 hours',
    popularVenues: ['Community halls', 'Function centers', 'School halls', 'Club venues'],
    seoTitle: 'Reunion Venue Hire Sydney NSW | School & Family Reunion Spaces',
    seoDescription: 'Book reunion venues in Sydney for school reunions, family gatherings & group events. Large capacity venues with flexible layouts & facilities.'
  }
};

export default function EventTypePage() {
  const { eventType } = useParams<{ eventType: string }>();
  const eventInfo = eventType ? eventTypeData[eventType] : null;

  if (!eventInfo) {
    return <Navigate to="/find-venues" replace />;
  }

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: eventInfo.name,
    description: eventInfo.description,
    provider: {
      '@type': 'Organization',
      name: 'HouseGoing',
      url: 'https://housegoing.com.au'
    },
    areaServed: {
      '@type': 'State',
      name: 'New South Wales',
      addressCountry: 'AU'
    },
    priceRange: eventInfo.averagePrice,
    serviceType: 'Event Venue Rental'
  };

  return (
    <>
      <Helmet>
        <title>{eventInfo.seoTitle}</title>
        <meta name="description" content={eventInfo.seoDescription} />
        <meta property="og:title" content={eventInfo.seoTitle} />
        <meta property="og:description" content={eventInfo.seoDescription} />
        <meta property="og:url" content={`https://housegoing.com.au/event-types/${eventType}`} />
        <link rel="canonical" href={`https://housegoing.com.au/event-types/${eventType}`} />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      <div className="pt-20 pb-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <PartyPopper className="h-16 w-16 mx-auto mb-4" />
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {eventInfo.name} Venues in NSW
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {eventInfo.description}
              </p>
              <Link
                to={`/find-venues?event=${eventType}`}
                className="inline-flex items-center px-8 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
              >
                Find Venues
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="prose prose-lg max-w-none">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Planning {eventInfo.name}
                </h2>
                <p className="text-gray-600 mb-8">
                  {eventInfo.longDescription}
                </p>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  What to Look For
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  {eventInfo.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Star className="h-5 w-5 text-purple-600 mr-3" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Popular Venue Types
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  {eventInfo.popularVenues.map((venue, index) => (
                    <Link
                      key={index}
                      to={`/find-venues?type=${encodeURIComponent(venue)}`}
                      className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                    >
                      <PartyPopper className="h-5 w-5 text-purple-600 mr-3" />
                      <span className="text-gray-700 hover:text-purple-600">
                        {venue}
                      </span>
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Event Details
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Average Price</div>
                      <div className="text-gray-600">{eventInfo.averagePrice}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Typical Guests</div>
                      <div className="text-gray-600">{eventInfo.averageGuests}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Duration</div>
                      <div className="text-gray-600">{eventInfo.duration}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Need Event Planning Help?
                </h3>
                <p className="text-gray-600 mb-4">
                  Our event planning experts can help you organize the perfect {eventInfo.name.toLowerCase()}.
                </p>
                <Link
                  to="/contact"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Get Planning Help
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
