
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImagePlus, Upload } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

const photoCategories = [
  { id: 'front', label: 'Front View', required: true },
  { id: 'interior', label: 'Interior Photos', required: true },
  { id: 'exterior', label: 'Exterior Photos', required: true },
  { id: 'amenities', label: 'Amenities', required: true },
  { id: 'additional', label: 'Additional Photos', required: false },
];

const ListVenuePhotos = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  const [photos, setPhotos] = useState<Record<string, File[]>>({});

  const handleFileSelect = async (category: string, files: FileList | null) => {
    if (!files) return;
    
    const newFiles = Array.from(files);
    setPhotos(prev => ({
      ...prev,
      [category]: [...(prev[category] || []), ...newFiles]
    }));
  };

  const handleSubmit = async () => {
    setUploading(true);
    try {
      // Create a storage bucket if it doesn't exist
      const { data: bucketData, error: bucketError } = await supabase
        .storage
        .createBucket('venue-images', {
          public: true,
          fileSizeLimit: 5242880, // 5MB
          allowedMimeTypes: ['image/jpeg', 'image/png']
        });

      if (bucketError && !bucketError.message.includes('already exists')) {
        throw bucketError;
      }

      const uploadedUrls: Record<string, string[]> = {};
      
      for (const [category, files] of Object.entries(photos)) {
        const urls = [];
        for (const file of files) {
          const fileExt = file.name.split('.').pop();
          const fileName = `${crypto.randomUUID()}.${fileExt}`;
          const { error: uploadError, data } = await supabase.storage
            .from('venue-images')
            .upload(fileName, file);

          if (uploadError) throw uploadError;
          if (data) urls.push(data.path);
        }
        uploadedUrls[category] = urls;
      }

      // Get the venue data from session storage
      const venueType = sessionStorage.getItem('venueType');
      const allowsParty = sessionStorage.getItem('allowsParty') === 'true';
      const locationData = JSON.parse(sessionStorage.getItem('venueLocation') || '{}');

      // Get authenticated user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error: venueError } = await supabase
        .from('venues')
        .insert({
          owner_id: user.id,
          title: `${locationData.city} ${venueType}`,
          description: `A beautiful ${venueType} in ${locationData.city}`,
          location: `${locationData.city}, ${locationData.state}`,
          address: `${locationData.address}, ${locationData.city}, ${locationData.state} ${locationData.zipCode}`,
          images: Object.values(uploadedUrls).flat(),
          amenities: allowsParty ? ['Party Friendly'] : [],
          capacity: 0,
          price_per_hour: 0
        });

      if (venueError) throw venueError;

      toast({
        title: "Success!",
        description: "Your venue has been listed successfully.",
      });
      
      // Clear session storage
      sessionStorage.removeItem('venueType');
      sessionStorage.removeItem('allowsParty');
      sessionStorage.removeItem('venueLocation');
      
      navigate('/');
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "Failed to upload photos. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-4">Add photos of your venue</h1>
          <p className="text-gray-600">
            Photos help guests imagine staying at your place. You can start with a few and add more after you publish.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {photoCategories.map((category) => (
            <Card key={category.id} className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">{category.label}</h3>
                  {category.required && (
                    <span className="text-sm text-red-500">Required</span>
                  )}
                </div>

                <div className="relative border-2 border-dashed rounded-lg p-4 hover:border-primary transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onChange={(e) => handleFileSelect(category.id, e.target.files)}
                  />
                  <div className="text-center">
                    <ImagePlus className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        Drop your images here or click to upload
                      </p>
                    </div>
                  </div>
                </div>

                {photos[category.id]?.length > 0 && (
                  <div className="grid grid-cols-2 gap-2">
                    {photos[category.id].map((file, index) => (
                      <div key={index} className="relative aspect-video bg-gray-100 rounded">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => navigate('/list-venue/location')}
          >
            Back
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={uploading}
          >
            {uploading ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              'Publish Listing'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ListVenuePhotos;
