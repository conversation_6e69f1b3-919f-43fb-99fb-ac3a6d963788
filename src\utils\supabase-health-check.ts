/**
 * Supabase Health Check Utility
 *
 * This utility provides functions to check the health of the Supabase connection
 * and fix common issues.
 */

import { supabase } from '../lib/supabase-client';

/**
 * Check if the Supabase connection is healthy
 * @returns A promise that resolves to a health check result
 */
export async function checkSupabaseHealth(): Promise<{
  isHealthy: boolean;
  connectionOk: boolean;
  authOk: boolean;
  tablesOk: boolean;
  errors: string[];
}> {
  const result = {
    isHealthy: false,
    connectionOk: false,
    authOk: false,
    tablesOk: false,
    errors: [] as string[]
  };

  try {
    // Check basic connection
    const { data: connectionData, error: connectionError } = await supabase
      .from('user_profiles')
      .select('id', { count: 'exact', head: true })
      .limit(1);

    if (connectionError) {
      result.errors.push(`Connection error: ${connectionError.message}`);
    } else {
      result.connectionOk = true;
    }

    // Check auth
    try {
      const { data: authData, error: authError } = await supabase.auth.getSession();

      if (authError) {
        result.errors.push(`Auth error: ${authError.message}`);
      } else {
        result.authOk = true;
      }
    } catch (authException) {
      result.errors.push(`Auth exception: ${String(authException)}`);
    }

    // Check tables
    const tables = ['user_profiles', 'admin_users'];
    let tablesWithErrors = 0;

    for (const table of tables) {
      try {
        const { error: tableError } = await supabase
          .from(table)
          .select('id', { count: 'exact', head: true })
          .limit(1);

        if (tableError) {
          result.errors.push(`Table '${table}' error: ${tableError.message}`);
          tablesWithErrors++;
        }
      } catch (tableException) {
        result.errors.push(`Table '${table}' exception: ${String(tableException)}`);
        tablesWithErrors++;
      }
    }

    result.tablesOk = tablesWithErrors === 0;
    result.isHealthy = result.connectionOk && result.authOk && result.tablesOk;

    return result;
  } catch (error) {
    result.errors.push(`Health check exception: ${String(error)}`);
    return result;
  }
}

/**
 * Fix common Supabase issues
 * @returns A promise that resolves to a result object
 */
export async function fixSupabaseIssues(): Promise<{
  success: boolean;
  actionsPerformed: string[];
  errors: string[];
}> {
  const result = {
    success: false,
    actionsPerformed: [] as string[],
    errors: [] as string[]
  };

  try {
    // Clear any existing Supabase sessions to prevent conflicts
    try {
      await supabase.auth.signOut();
      result.actionsPerformed.push('Cleared existing Supabase auth session');
    } catch (signOutError) {
      result.errors.push(`Error clearing auth session: ${String(signOutError)}`);
    }

    // Clear localStorage items related to Supabase auth
    try {
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.includes('supabase') ||
          key.includes('sb-') ||
          key.includes('gotrue') ||
          key.includes('auth')
        )) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => localStorage.removeItem(key));
      result.actionsPerformed.push(`Cleared ${keysToRemove.length} Supabase-related items from localStorage`);
    } catch (localStorageError) {
      result.errors.push(`Error clearing localStorage: ${String(localStorageError)}`);
    }

    // Run a test query to ensure connection is working
    try {
      const { error: testError } = await supabase
        .from('user_profiles')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (testError) {
        result.errors.push(`Test query error after fixes: ${testError.message}`);
      } else {
        result.actionsPerformed.push('Test query successful after fixes');
      }
    } catch (testException) {
      result.errors.push(`Test query exception after fixes: ${String(testException)}`);
    }

    result.success = result.errors.length === 0;
    return result;
  } catch (error) {
    result.errors.push(`Fix issues exception: ${String(error)}`);
    return result;
  }
}
