import { Appearance } from '@clerk/clerk-react';

export const clerkAppearance: Appearance = {
  layout: {
    socialButtonsVariant: 'iconButton',
    socialButtonsPlacement: 'top',
    termsPageUrl: '/terms',
    privacyPageUrl: '/privacy',
    helpPageUrl: '/help',
    showOptionalFields: false,
    hideBranding: true,
    logoPlacement: 'inside',
    logoImageUrl: '/images/housegoing-logo.svg',
  },
  variables: {
    colorPrimary: '#7C3AED', // Purple-600
    colorText: '#1F2937', // Gray-800
    colorTextSecondary: '#4B5563', // Gray-600
    colorBackground: '#FFFFFF',
    colorDanger: '#EF4444', // Red-500
    colorSuccess: '#10B981', // Green-500
    fontFamily: 'Inter, system-ui, sans-serif',
    borderRadius: '0.375rem',
  },
  elements: {
    // Form elements
    formButtonPrimary: 'bg-gradient-to-r from-purple-600 to-pink-600 hover:opacity-90 text-sm normal-case font-semibold py-3 rounded-lg shadow-sm transition-all duration-200 transform hover:-translate-y-0.5',
    formFieldInput: 'border-gray-300 focus:ring-purple-500 focus:border-purple-500 rounded-lg py-3 px-4 transition-all duration-200',
    formFieldLabel: 'text-gray-700 text-sm font-medium mb-1',
    formFieldHintText: 'text-gray-500 text-xs mt-1',
    formFieldErrorText: 'text-red-500 text-xs mt-1',

    // Root elements
    rootBox: 'rounded-xl overflow-hidden',
    card: 'shadow-xl rounded-xl border-0 overflow-hidden bg-gradient-to-br from-white to-gray-50',

    // Header
    headerTitle: 'text-xl font-bold text-gray-900',
    headerSubtitle: 'text-gray-600',

    // Footer
    footer: 'hidden',
    footerAction: 'hidden',
    footerActionText: 'hidden',

    // Development mode badge
    developmentModeBox: 'hidden',
    developmentModeText: 'hidden',

    // Social buttons - hide them completely
    socialButtonsBlockButton: 'hidden',
    socialButtonsBlockButtonText: 'hidden',
    socialButtonsBlockButtonArrow: 'hidden',
    socialButtonsIconButton: 'hidden',
    socialButtonsProviderIcon: 'hidden',
    socialButtonsIconButton__google: 'hidden',
    socialButtonsIconButton__facebook: 'hidden',
    socialButtonsIconButton__tiktok: 'hidden',
    socialButtonsProviderIcon__google: 'hidden',
    socialButtonsProviderIcon__facebook: 'hidden',
    socialButtonsProviderIcon__tiktok: 'hidden',

    // Divider
    dividerLine: 'bg-gray-200 h-px',
    dividerText: 'hidden', // Hide the "or continue with" text

    // Links
    footerActionLink: 'text-purple-600 hover:text-purple-800 font-medium',
    identityPreviewEditButton: 'text-purple-600 hover:text-purple-800',
    formResendCodeLink: 'text-purple-600 hover:text-purple-800',
    formButtonReset: 'text-purple-600 hover:text-purple-800 font-medium',

    // Alert
    alert: 'rounded-lg border-0 p-4',
    alertText: 'text-sm',
    alertSuccess: 'bg-green-50 text-green-800',
    alertError: 'bg-red-50 text-red-800',

    // User button
    userButtonAvatarBox: 'w-8 h-8',
    userButtonPopoverCard: 'shadow-lg rounded-lg border border-gray-200',
    userButtonPopoverActionButton: 'hover:bg-gray-100 text-gray-700',
    userButtonPopoverActionButtonText: 'text-sm font-medium',
    userButtonPopoverActionButtonIcon: 'text-gray-500',
    userButtonPopoverFooter: 'border-t border-gray-200',
  },
};
