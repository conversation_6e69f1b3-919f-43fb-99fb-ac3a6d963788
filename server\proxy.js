import express from 'express';
import axios from 'axios';
import cors from 'cors';

const app = express();
app.use(cors());

app.get('/proxy/wfs', async (req, res) => {
  try {
    const { serviceUrl, ...params } = req.query;
    const response = await axios.get(serviceUrl, { params });
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

const PORT = process.env.PROXY_PORT || 3005;
app.listen(PORT, () => {
  console.log(`CORS proxy running on port ${PORT}`);
});
