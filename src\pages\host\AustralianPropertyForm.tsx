import React, { useState } from 'react';
import { Ruler, Building, SquareIcon, MapPin } from 'lucide-react';

// Simple form that doesn't rely on Supabase
const AustralianPropertyForm: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    description: '',
    type: 'house',
    maxGuests: 1,
    price: 0,
    size: 0, // Size in square metres
    functionRooms: 0, // Number of function rooms (optional)
    eventSpaces: 0, // Number of event spaces
    bankDetails: {
      accountName: '',
      bsb: '',
      accountNumber: '',
      bankName: ''
    }
  });

  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  // Validate BSB (6 digits)
  const validateBSB = (bsb: string) => {
    return /^\d{6}$/.test(bsb);
  };

  // Validate account number (6-10 digits)
  const validateAccountNumber = (accountNumber: string) => {
    return /^\d{6,10}$/.test(accountNumber);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name || !formData.address || !formData.price) {
      setError('Please fill all required fields');
      return;
    }

    // Validate venue details
    if (formData.size <= 0) {
      setError('Please enter a valid venue size in square metres');
      return;
    }

    if (formData.eventSpaces <= 0) {
      setError('Please enter at least one event space');
      return;
    }

    // Validate bank details
    if (!validateBSB(formData.bankDetails.bsb)) {
      setError('Please enter a valid 6-digit BSB number');
      return;
    }
    
    if (!validateAccountNumber(formData.bankDetails.accountNumber)) {
      setError('Please enter a valid account number (6-10 digits)');
      return;
    }

    if (!formData.bankDetails.accountName || !formData.bankDetails.bankName) {
      setError('Please complete all bank account details');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess(true);
      
      // Show success message
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setTimeout(() => {
        setSuccess(false);
        setFormData({
          name: '',
          address: '',
          description: '',
          type: 'house',
          maxGuests: 1,
          price: 0,
          size: 0,
          functionRooms: 0,
          eventSpaces: 0,
          bankDetails: {
            accountName: '',
            bsb: '',
            accountNumber: '',
            bankName: ''
          }
        });
      }, 3000);
    } catch (err) {
      setError('Failed to submit venue. Please try again.');
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">
        Add New Venue
      </h1>

      {error && (
        <div className="bg-red-100 text-red-700 p-4 mb-4 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 text-green-700 p-4 mb-4 rounded flex flex-col gap-2">
          <p className="font-semibold">Venue submitted successfully!</p>
          <p>Thank you for listing your venue with HouseGoing. Our team will review your submission and get back to you shortly.</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Property Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block mb-2 font-medium">Venue Name*</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <div>
            <label className="block mb-2 font-medium">Address*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <MapPin className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData({...formData, address: e.target.value})}
                className="pl-10 w-full p-2 border rounded"
                placeholder="Enter your venue address"
                required
              />
            </div>
          </div>
        </div>
        
        {/* Venue Details */}
        <div className="border-t pt-6">
          <h2 className="text-xl font-bold mb-4">Venue Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block mb-2 font-medium">Venue Size (m²)*</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <SquareIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  min="0"
                  value={formData.size || ''}
                  onChange={(e) => setFormData({...formData, size: parseInt(e.target.value) || 0})}
                  className="pl-10 w-full p-2 border rounded"
                  placeholder="e.g. 200 m²"
                  required
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">Enter the total area in square metres</p>
            </div>
            
            <div>
              <label className="block mb-2 font-medium">Function Rooms</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Building className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  min="0"
                  value={formData.functionRooms || ''}
                  onChange={(e) => setFormData({...formData, functionRooms: parseInt(e.target.value) || 0})}
                  className="pl-10 w-full p-2 border rounded"
                  placeholder="e.g. 2"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">Optional - number of private function rooms</p>
            </div>
            
            <div>
              <label className="block mb-2 font-medium">Event Spaces*</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Ruler className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  min="1"
                  value={formData.eventSpaces || ''}
                  onChange={(e) => setFormData({...formData, eventSpaces: parseInt(e.target.value) || 0})}
                  className="pl-10 w-full p-2 border rounded"
                  placeholder="e.g. 1"
                  required
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">Number of separate event areas</p>
            </div>
          </div>
        </div>

        {/* Bank Details Section */}
        <div className="border-t pt-6">
          <h2 className="text-xl font-bold mb-4">Australian Bank Account Details for Payouts*</h2>
          <p className="text-sm text-gray-600 mb-4">
            This information is required for processing your venue booking payments.
            Your details are securely stored and handled according to Australian privacy laws.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block mb-2 font-medium">Account Holder Name*</label>
              <input
                type="text"
                value={formData.bankDetails.accountName}
                onChange={(e) => setFormData({
                  ...formData,
                  bankDetails: {
                    ...formData.bankDetails,
                    accountName: e.target.value
                  }
                })}
                className="w-full p-2 border rounded"
                placeholder="e.g. John Smith"
                required
              />
              <p className="text-xs text-gray-500 mt-1">Enter the name exactly as it appears on your bank account</p>
            </div>

            <div>
              <label className="block mb-2 font-medium">BSB* (6 digits)</label>
              <input
                type="text"
                value={formData.bankDetails.bsb}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '');
                  // Limit to 6 digits
                  const bsb = value.slice(0, 6);
                  setFormData({
                    ...formData,
                    bankDetails: {
                      ...formData.bankDetails,
                      bsb
                    }
                  });
                }}
                pattern="\d{6}"
                maxLength={6}
                className="w-full p-2 border rounded"
                placeholder="e.g. 062-000"
                required
              />
              <p className="text-xs text-gray-500 mt-1">Australian BSB number (6 digits)</p>
            </div>

            <div>
              <label className="block mb-2 font-medium">Account Number* (6-10 digits)</label>
              <input
                type="text"
                value={formData.bankDetails.accountNumber}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '');
                  // Limit to 10 digits
                  const accountNumber = value.slice(0, 10);
                  setFormData({
                    ...formData,
                    bankDetails: {
                      ...formData.bankDetails,
                      accountNumber
                    }
                  });
                }}
                maxLength={10}
                className="w-full p-2 border rounded"
                placeholder="e.g. ********"
                required
              />
              <p className="text-xs text-gray-500 mt-1">Your bank account number (6-10 digits)</p>
            </div>

            <div>
              <label className="block mb-2 font-medium">Bank Name*</label>
              <input
                type="text"
                value={formData.bankDetails.bankName}
                onChange={(e) => setFormData({
                  ...formData,
                  bankDetails: {
                    ...formData.bankDetails,
                    bankName: e.target.value
                  }
                })}
                className="w-full p-2 border rounded"
                placeholder="e.g. Commonwealth Bank"
                required
              />
              <p className="text-xs text-gray-500 mt-1">The name of your Australian bank</p>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={submitting}
            className="bg-purple-600 text-white px-6 py-2 rounded hover:bg-purple-700 disabled:bg-purple-300"
          >
            {submitting ? 'Submitting...' : 'Submit Venue for Approval'}
          </button>
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          <p>
            By submitting this form, your venue will be reviewed by our team before being listed on HouseGoing.
            You'll receive a notification once your venue has been approved.
          </p>
        </div>
      </form>
    </div>
  );
};

export default AustralianPropertyForm;
