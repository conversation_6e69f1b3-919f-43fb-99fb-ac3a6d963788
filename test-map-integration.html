<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NSW Planning Map Integration Test</title>

  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    h1 {
      color: #333;
      margin-bottom: 20px;
    }

    .map-container {
      height: 500px;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 20px;
      border: 1px solid #ccc;
    }

    .search-container {
      margin-bottom: 20px;
    }

    .search-form {
      display: flex;
      gap: 10px;
    }

    .search-input {
      flex-grow: 1;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 16px;
    }

    .search-button {
      padding: 10px 20px;
      background-color: #6b46c1;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }

    .search-button:hover {
      background-color: #553c9a;
    }

    .search-button:disabled {
      background-color: #a0aec0;
      cursor: not-allowed;
    }

    .error-message {
      color: #e53e3e;
      margin-top: 10px;
    }

    .results-container {
      background-color: #f7fafc;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }

    .results-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 15px;
    }

    .results-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .results-section {
      background-color: #fff;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e2e8f0;
    }

    .results-section-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .data-row {
      margin-bottom: 8px;
    }

    .data-label {
      font-weight: 500;
    }

    .loading-indicator {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(107, 70, 193, 0.3);
      border-radius: 50%;
      border-top-color: #6b46c1;
      animation: spin 1s ease-in-out infinite;
      margin-left: 10px;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .layer-control {
      margin-top: 10px;
      display: flex;
      gap: 15px;
    }

    .layer-checkbox {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>NSW Planning Map Integration Test</h1>

    <div class="search-container">
      <form id="searchForm" class="search-form">
        <input
          type="text"
          id="addressInput"
          class="search-input"
          placeholder="Enter NSW address..."
        >
        <button
          type="submit"
          id="searchButton"
          class="search-button"
        >
          Search
        </button>
      </form>
      <div id="errorMessage" class="error-message"></div>
    </div>

    <div id="mapContainer" class="map-container"></div>

    <div class="layer-control">
      <div class="layer-checkbox">
        <input type="checkbox" id="zoningLayer" checked>
        <label for="zoningLayer">Zoning Layer</label>
      </div>
      <div class="layer-checkbox">
        <input type="checkbox" id="lgaLayer">
        <label for="lgaLayer">LGA Boundaries</label>
      </div>
    </div>

    <div id="resultsContainer" style="display: none;" class="results-container">
      <h2 class="results-title">Location Information</h2>

      <div class="results-grid">
        <div class="results-section">
          <h3 class="results-section-title">Location Details</h3>
          <div class="data-row">
            <span class="data-label">Address:</span>
            <span id="addressResult"></span>
          </div>
          <div class="data-row">
            <span class="data-label">Coordinates:</span>
            <span id="coordinatesResult"></span>
          </div>
          <div class="data-row">
            <span class="data-label">Council:</span>
            <span id="councilResult"></span>
          </div>
          <div class="data-row">
            <span class="data-label">Zoning:</span>
            <span id="zoningResult"></span>
          </div>
        </div>

        <div class="results-section">
          <h3 class="results-section-title">Noise Restrictions</h3>
          <div class="data-row">
            <span class="data-label">Noise Curfew:</span>
            <span id="curfewResult"></span>
          </div>
          <div class="data-row">
            <span class="data-label">Party Suitability:</span>
            <span id="suitabilityResult"></span>
          </div>
          <div style="margin-top: 15px; font-size: 14px; color: #718096;">
            <p>Note: Always check with the local council for specific noise regulations and event permits that may be required.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

  <script>
    // Mapbox access token
    const MAPBOX_TOKEN = 'pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA';

    // Default center coordinates (Sydney)
    const DEFAULT_CENTER = [-33.8688, 151.2093];
    const DEFAULT_ZOOM = 12;

    // NSW Planning Portal WFS/WMS endpoints
    const ZONING_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer';
    const LGA_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer';
    const ZONING_WMS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer';
    const LGA_WMS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer';

    // Layer names/IDs - these might need to be adjusted based on the actual service
    const ZONING_LAYER = 'EPI_Primary_Planning_Layers:Layer_2'; // Try different variations
    const LGA_LAYER = 'EDP_Administrative_Boundaries:Layer_1';

    // Maps.co API
    const MAPS_CO_API_KEY = '67ea5a7e84615836239135wpc5a6d73';
    const MAPS_CO_BASE_URL = 'https://geocode.maps.co';

    // DOM elements
    const mapContainer = document.getElementById('mapContainer');
    const searchForm = document.getElementById('searchForm');
    const addressInput = document.getElementById('addressInput');
    const searchButton = document.getElementById('searchButton');
    const errorMessage = document.getElementById('errorMessage');
    const resultsContainer = document.getElementById('resultsContainer');
    const addressResult = document.getElementById('addressResult');
    const coordinatesResult = document.getElementById('coordinatesResult');
    const councilResult = document.getElementById('councilResult');
    const zoningResult = document.getElementById('zoningResult');
    const curfewResult = document.getElementById('curfewResult');
    const suitabilityResult = document.getElementById('suitabilityResult');
    const zoningLayerCheckbox = document.getElementById('zoningLayer');
    const lgaLayerCheckbox = document.getElementById('lgaLayer');

    // Map and layer references
    let map;
    let marker;
    let zoningWMS;
    let lgaWMS;
    let zoningGeoJSON;
    let lgaGeoJSON;

    // Initialize map
    function initMap() {
      // Create map instance
      map = L.map(mapContainer).setView(DEFAULT_CENTER, DEFAULT_ZOOM);

      // Add Mapbox tile layer
      L.tileLayer(`https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=${MAPBOX_TOKEN}`, {
        attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
        maxZoom: 19
      }).addTo(map);

      // Add WMS layers (initially not visible)
      zoningWMS = L.tileLayer.wms(ZONING_WMS_URL, {
        layers: '2',
        transparent: true,
        format: 'image/png',
        opacity: 0.7
      });

      lgaWMS = L.tileLayer.wms(LGA_WMS_URL, {
        layers: '1',
        transparent: true,
        format: 'image/png',
        opacity: 0.7
      });

      // Add scale control
      L.control.scale().addTo(map);

      // Handle map click
      map.on('click', handleMapClick);

      // Initialize layer checkboxes
      zoningLayerCheckbox.addEventListener('change', toggleZoningLayer);
      lgaLayerCheckbox.addEventListener('change', toggleLGALayer);
    }

    // Toggle zoning layer
    function toggleZoningLayer() {
      if (zoningLayerCheckbox.checked) {
        zoningWMS.addTo(map);
      } else {
        map.removeLayer(zoningWMS);
      }
    }

    // Toggle LGA layer
    function toggleLGALayer() {
      if (lgaLayerCheckbox.checked) {
        lgaWMS.addTo(map);
      } else {
        map.removeLayer(lgaWMS);
      }
    }

    // Handle map click
    function handleMapClick(e) {
      const { lat, lng } = e.latlng;

      // Add or update marker
      if (marker) {
        marker.setLatLng([lat, lng]);
      } else {
        marker = L.marker([lat, lng])
          .addTo(map)
          .bindPopup(`Selected location: ${lat.toFixed(5)}, ${lng.toFixed(5)}`)
          .openPopup();
      }

      // Load zoning and LGA data
      loadSpatialData(lat, lng);

      // Update coordinates display
      coordinatesResult.textContent = `${lat.toFixed(5)}, ${lng.toFixed(5)}`;

      // Perform reverse geocoding
      reverseGeocode(lat, lng);
    }

    // Geocode address
    async function geocodeAddress(address) {
      if (!address.trim()) return null;

      try {
        // Add NSW, Australia to the search query if not already specified
        let searchQuery = address;
        if (!searchQuery.toLowerCase().includes('nsw') && !searchQuery.toLowerCase().includes('new south wales')) {
          searchQuery += ', NSW, Australia';
        } else if (!searchQuery.toLowerCase().includes('australia')) {
          searchQuery += ', Australia';
        }

        const response = await fetch(
          `${MAPS_CO_BASE_URL}/search?q=${encodeURIComponent(searchQuery)}&api_key=${MAPS_CO_API_KEY}`
        );

        if (!response.ok) {
          throw new Error(`Geocoding API error: ${response.status}`);
        }

        const data = await response.json();

        if (!data || data.length === 0) {
          throw new Error('No results found for this address');
        }

        // Filter results to prioritize NSW locations
        const nswResults = data.filter(item =>
          item.display_name.toLowerCase().includes('nsw') ||
          item.display_name.toLowerCase().includes('new south wales')
        );

        // Use NSW result if available, otherwise use the first result
        const bestResult = nswResults.length > 0 ? nswResults[0] : data[0];

        return {
          lat: parseFloat(bestResult.lat),
          lng: parseFloat(bestResult.lon),
          displayName: bestResult.display_name
        };
      } catch (error) {
        console.error('Geocoding error:', error);
        throw new Error('Failed to geocode address. Please try again.');
      }
    }

    // Reverse geocode coordinates
    async function reverseGeocode(lat, lng) {
      try {
        const response = await fetch(
          `${MAPS_CO_BASE_URL}/reverse?lat=${lat}&lon=${lng}&api_key=${MAPS_CO_API_KEY}`
        );

        if (!response.ok) {
          throw new Error(`Reverse geocoding API error: ${response.status}`);
        }

        const data = await response.json();

        if (!data || !data.display_name) {
          throw new Error('No results found for these coordinates');
        }

        // Update address display
        addressResult.textContent = data.display_name;

        return data.display_name;
      } catch (error) {
        console.error('Reverse geocoding error:', error);
        // Use a fallback
        addressResult.textContent = `Location at ${lat.toFixed(5)}, ${lng.toFixed(5)}`;
        return null;
      }
    }

    // Load zoning and LGA data
    async function loadSpatialData(lat, lng) {
      setLoading(true);
      clearError();

      try {
        // Clear previous GeoJSON layers
        if (zoningGeoJSON) {
          map.removeLayer(zoningGeoJSON);
          zoningGeoJSON = null;
        }

        if (lgaGeoJSON) {
          map.removeLayer(lgaGeoJSON);
          lgaGeoJSON = null;
        }

        // Fetch zoning data
        const zoningData = await fetchWFSData(
          ZONING_WFS_URL,
          'EPI_Primary_Planning_Layers:2',
          lat,
          lng
        );

        // Fetch LGA data
        const lgaData = await fetchWFSData(
          LGA_WFS_URL,
          'EDP_Administrative_Boundaries:1',
          lat,
          lng
        );

        // Process and display zoning data
        if (zoningData && zoningData.features && zoningData.features.length > 0) {
          zoningGeoJSON = L.geoJSON(zoningData, {
            style: {
              color: '#ff7800',
              weight: 2,
              opacity: 0.65,
              fillOpacity: 0.2
            },
            onEachFeature: (feature, layer) => {
              const zoneCode = feature.properties.ZONE_CODE || 'Unknown';
              const zoneName = feature.properties.ZONE_NAME || 'Unknown Zone';
              layer.bindPopup(`<strong>Zone:</strong> ${zoneCode}<br><strong>Description:</strong> ${zoneName}`);
            }
          }).addTo(map);

          // Update zoning display
          const zoneCode = zoningData.features[0].properties.ZONE_CODE;
          const zoneName = zoningData.features[0].properties.ZONE_NAME;
          zoningResult.textContent = `${zoneCode} - ${zoneName}`;

          // Update curfew and suitability
          curfewResult.textContent = getCurfewByZoning(zoneCode);
          suitabilityResult.textContent = getPartySuitability(zoneCode);
        } else {
          zoningResult.textContent = 'No zoning data found';
          curfewResult.textContent = 'Unknown';
          suitabilityResult.textContent = 'Unknown';
        }

        // Process and display LGA data
        if (lgaData && lgaData.features && lgaData.features.length > 0) {
          lgaGeoJSON = L.geoJSON(lgaData, {
            style: {
              color: '#0078ff',
              weight: 2,
              opacity: 0.65,
              fillOpacity: 0.1
            },
            onEachFeature: (feature, layer) => {
              const lgaName = feature.properties.LGA_NAME || 'Unknown Council';
              layer.bindPopup(`<strong>Council:</strong> ${lgaName}`);
            }
          }).addTo(map);

          // Update council display
          councilResult.textContent = lgaData.features[0].properties.LGA_NAME;
        } else {
          councilResult.textContent = 'No council data found';
        }

        // Show results container
        resultsContainer.style.display = 'block';
      } catch (error) {
        console.error('Error loading spatial data:', error);
        showError('Failed to load zoning or council data.');
      } finally {
        setLoading(false);
      }
    }

    // Fetch WFS data
    async function fetchWFSData(serviceUrl, typeName, lat, lng) {
      try {
        // Use our proxy server to avoid CORS issues
        console.log(`Fetching WFS data for ${typeName} at coordinates: ${lng}, ${lat}`);

        const requestBody = {
          serviceUrl,
          params: {
            service: 'WFS',
            version: '1.1.0',
            request: 'GetFeature',
            typeNames: typeName === 'EPI_Primary_Planning_Layers:2' ? ZONING_LAYER : LGA_LAYER,
            outputFormat: 'application/json',
            // Try different filter approaches
            filter: `
              <Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml">
                <Intersects>
                  <PropertyName>Shape</PropertyName>
                  <gml:Point srsName="EPSG:4326">
                    <gml:coordinates>${lng},${lat}</gml:coordinates>
                  </gml:Point>
                </Intersects>
              </Filter>
            `
          }
        };

        console.log('WFS request:', requestBody);

        const response = await fetch('http://localhost:3004/api/wfs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('WFS proxy error:', errorText);
          throw new Error(`WFS request failed: ${response.status}`);
        }

        const data = await response.json();
        console.log(`WFS response for ${typeName}:`, data);

        // Check if we got features
        if (!data.features || data.features.length === 0) {
          console.warn(`No features found for ${typeName} at coordinates: ${lng}, ${lat}`);
        } else {
          console.log(`Found ${data.features.length} features for ${typeName}`);
          console.log('First feature properties:', data.features[0].properties);
        }

        return data;

        /* Fallback to mock data if the proxy server is not available
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // For Sydney CBD area
        if (lat > -33.9 && lat < -33.85 && lng > 151.19 && lng < 151.22) {
          if (typeName === 'EPI_Primary_Planning_Layers:2') {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  ZONE_CODE: 'B3',
                  ZONE_NAME: 'Commercial Core'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.01, lat-0.01], [lng+0.01, lat-0.01], [lng+0.01, lat+0.01], [lng-0.01, lat+0.01], [lng-0.01, lat-0.01]]]
                }
              }]
            };
          } else {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  LGA_NAME: 'Sydney'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.02, lat-0.02], [lng+0.02, lat-0.02], [lng+0.02, lat+0.02], [lng-0.02, lat+0.02], [lng-0.02, lat-0.02]]]
                }
              }]
            };
          }
        }
        // For Parramatta area
        else if (lat > -33.82 && lat < -33.8 && lng > 151 && lng < 151.02) {
          if (typeName === 'EPI_Primary_Planning_Layers:2') {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  ZONE_CODE: 'B4',
                  ZONE_NAME: 'Mixed Use'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.01, lat-0.01], [lng+0.01, lat-0.01], [lng+0.01, lat+0.01], [lng-0.01, lat+0.01], [lng-0.01, lat-0.01]]]
                }
              }]
            };
          } else {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  LGA_NAME: 'Parramatta'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.02, lat-0.02], [lng+0.02, lat-0.02], [lng+0.02, lat+0.02], [lng-0.02, lat+0.02], [lng-0.02, lat-0.02]]]
                }
              }]
            };
          }
        }
        // For Bondi area
        else if (lat > -33.9 && lat < -33.88 && lng > 151.27 && lng < 151.29) {
          if (typeName === 'EPI_Primary_Planning_Layers:2') {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  ZONE_CODE: 'R3',
                  ZONE_NAME: 'Medium Density Residential'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.01, lat-0.01], [lng+0.01, lat-0.01], [lng+0.01, lat+0.01], [lng-0.01, lat+0.01], [lng-0.01, lat-0.01]]]
                }
              }]
            };
          } else {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  LGA_NAME: 'Waverley'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.02, lat-0.02], [lng+0.02, lat-0.02], [lng+0.02, lat+0.02], [lng-0.02, lat+0.02], [lng-0.02, lat-0.02]]]
                }
              }]
            };
          }
        }
        // Default mock data for other areas
        else {
          if (typeName === 'EPI_Primary_Planning_Layers:2') {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  ZONE_CODE: 'R2',
                  ZONE_NAME: 'Low Density Residential'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.01, lat-0.01], [lng+0.01, lat-0.01], [lng+0.01, lat+0.01], [lng-0.01, lat+0.01], [lng-0.01, lat-0.01]]]
                }
              }]
            };
          } else {
            return {
              type: 'FeatureCollection',
              features: [{
                type: 'Feature',
                properties: {
                  LGA_NAME: 'Unknown Council'
                },
                geometry: {
                  type: 'Polygon',
                  coordinates: [[[lng-0.02, lat-0.02], [lng+0.02, lat-0.02], [lng+0.02, lat+0.02], [lng-0.02, lat+0.02], [lng-0.02, lat-0.02]]]
                }
              }]
            };
          }
        }
        */
      } catch (error) {
        console.error(`Error fetching WFS data from ${serviceUrl}:`, error);
        throw error;
      }
    }

    // Get curfew time based on zoning code
    function getCurfewByZoning(zoneCode) {
      if (!zoneCode) return '10:00 PM'; // Default

      const code = zoneCode.toUpperCase();

      // Business zones
      if (code.startsWith('B3') || code.startsWith('B4')) {
        return '12:00 AM (Midnight)';
      } else if (code.startsWith('B')) {
        return '11:00 PM';
      }
      // Residential zones
      else if (code.startsWith('R1') || code.startsWith('R2')) {
        return '10:00 PM';
      } else if (code.startsWith('R')) {
        return '10:30 PM';
      }
      // Industrial zones
      else if (code.startsWith('IN')) {
        return '11:00 PM';
      }
      // Default
      else {
        return '10:00 PM';
      }
    }

    // Get party suitability rating based on zoning code
    function getPartySuitability(zoneCode) {
      if (!zoneCode) return 'Unknown';

      const code = zoneCode.toUpperCase();

      // Business zones - generally good for parties
      if (code.startsWith('B3') || code.startsWith('B4')) {
        return 'Excellent - Commercial area with higher noise tolerance';
      } else if (code.startsWith('B')) {
        return 'Good - Business area with moderate noise tolerance';
      }
      // Residential zones - more restrictions
      else if (code.startsWith('R1') || code.startsWith('R2')) {
        return 'Limited - Residential area with stricter noise restrictions';
      } else if (code.startsWith('R')) {
        return 'Moderate - Medium density area with some noise restrictions';
      }
      // Industrial zones - can be good for parties
      else if (code.startsWith('IN')) {
        return 'Good - Industrial area with higher noise tolerance';
      }
      // Default
      else {
        return 'Moderate - Check local council regulations';
      }
    }

    // Handle form submission
    async function handleFormSubmit(e) {
      e.preventDefault();

      const address = addressInput.value.trim();
      if (!address) return;

      setLoading(true);
      clearError();

      try {
        const result = await geocodeAddress(address);

        if (!result) {
          showError('No results found for this address');
          return;
        }

        // Update map view
        map.setView([result.lat, result.lng], 15);

        // Add or update marker
        if (marker) {
          marker.setLatLng([result.lat, result.lng]);
        } else {
          marker = L.marker([result.lat, result.lng])
            .addTo(map)
            .bindPopup(result.displayName)
            .openPopup();
        }

        // Update address display
        addressResult.textContent = result.displayName;

        // Update coordinates display
        coordinatesResult.textContent = `${result.lat.toFixed(5)}, ${result.lng.toFixed(5)}`;

        // Load zoning and LGA data
        loadSpatialData(result.lat, result.lng);
      } catch (error) {
        console.error('Search error:', error);
        showError(error.message || 'Failed to search for address');
      } finally {
        setLoading(false);
      }
    }

    // Show error message
    function showError(message) {
      errorMessage.textContent = message;
      errorMessage.style.display = 'block';
    }

    // Clear error message
    function clearError() {
      errorMessage.textContent = '';
      errorMessage.style.display = 'none';
    }

    // Set loading state
    function setLoading(isLoading) {
      if (isLoading) {
        searchButton.disabled = true;
        searchButton.innerHTML = 'Searching... <span class="loading-indicator"></span>';
      } else {
        searchButton.disabled = false;
        searchButton.textContent = 'Search';
      }
    }

    // Initialize the application
    function init() {
      initMap();
      searchForm.addEventListener('submit', handleFormSubmit);
    }

    // Start the application when the page loads
    window.addEventListener('load', init);
  </script>
</body>
</html>
