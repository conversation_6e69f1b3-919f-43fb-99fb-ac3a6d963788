-- This SQL script sets up the necessary functions and tables for HouseGoing
-- Run this in the Supabase SQL Editor

-- Create exec_sql function to execute dynamic SQL
CREATE OR REPLACE FUNCTION public.exec_sql(sql_query text)
RETURNS void AS $$
BEGIN
  EXECUTE sql_query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create exec_sql_exists function to check if a table exists
CREATE OR REPLACE FUNCTION public.exec_sql_exists(table_name text)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = table_name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create user_profiles table
CREATE OR REPLACE FUNCTION public.create_user_profiles_table()
RETURNS void AS $$
BEGIN
  -- Check if table already exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_profiles'
  ) THEN
    -- Create the table
    CREATE TABLE public.user_profiles (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      email TEXT,
      full_name TEXT,
      avatar_url TEXT,
      user_type TEXT DEFAULT 'customer',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
    );

    -- Set up RLS (Row Level Security)
    ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Users can view their own profile"
      ON public.user_profiles
      FOR SELECT
      USING (auth.uid() = id);
      
    CREATE POLICY "Users can update their own profile"
      ON public.user_profiles
      FOR UPDATE
      USING (auth.uid() = id);
      
    -- Allow public read access for basic user info
    CREATE POLICY "Public read access for basic user info"
      ON public.user_profiles
      FOR SELECT
      USING (true);
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create admin_users table
CREATE OR REPLACE FUNCTION public.create_admin_users_table()
RETURNS void AS $$
BEGIN
  -- Check if table already exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'admin_users'
  ) THEN
    -- Create the table
    CREATE TABLE public.admin_users (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      email TEXT UNIQUE NOT NULL,
      role TEXT DEFAULT 'admin',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
    );

    -- Set up RLS (Row Level Security)
    ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Admins can view all admin users"
      ON public.admin_users
      FOR SELECT
      USING (
        auth.uid() IN (SELECT id FROM public.admin_users)
      );
      
    CREATE POLICY "Only super admins can insert/update admin users"
      ON public.admin_users
      FOR ALL
      USING (
        auth.uid() IN (SELECT id FROM public.admin_users WHERE role = 'super_admin')
      );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the tables directly
DO $$
BEGIN
  -- Create user_profiles table if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_profiles'
  ) THEN
    CREATE TABLE public.user_profiles (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      email TEXT,
      full_name TEXT,
      avatar_url TEXT,
      user_type TEXT DEFAULT 'customer',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
    );

    -- Set up RLS (Row Level Security)
    ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Users can view their own profile"
      ON public.user_profiles
      FOR SELECT
      USING (auth.uid() = id);
      
    CREATE POLICY "Users can update their own profile"
      ON public.user_profiles
      FOR UPDATE
      USING (auth.uid() = id);
      
    -- Allow public read access for basic user info
    CREATE POLICY "Public read access for basic user info"
      ON public.user_profiles
      FOR SELECT
      USING (true);
  END IF;

  -- Create admin_users table if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'admin_users'
  ) THEN
    CREATE TABLE public.admin_users (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      email TEXT UNIQUE NOT NULL,
      role TEXT DEFAULT 'admin',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
    );

    -- Set up RLS (Row Level Security)
    ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Admins can view all admin users"
      ON public.admin_users
      FOR SELECT
      USING (
        auth.uid() IN (SELECT id FROM public.admin_users)
      );
      
    CREATE POLICY "Only super admins can insert/update admin users"
      ON public.admin_users
      FOR ALL
      USING (
        auth.uid() IN (SELECT id FROM public.admin_users WHERE role = 'super_admin')
      );
  END IF;
END $$;
