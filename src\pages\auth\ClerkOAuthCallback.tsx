import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { processOAuthSuccess, handleOAuthCallback } from '../../utils/clerk-oauth-helper';
import { syncClerkUserWithSupabase } from '../../services/auth/clerk-supabase-sync';

/**
 * Clerk OAuth Callback Handler
 * 
 * This component handles the OAuth callback from Clerk.
 * It processes the authentication result and redirects the user.
 */
const ClerkOAuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const { user, isLoaded, isSignedIn } = useUser();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(5);

  // Process the OAuth callback
  useEffect(() => {
    const processCallback = async () => {
      try {
        console.log('Processing OAuth callback...');
        
        // Handle the OAuth callback
        await handleOAuthCallback();
        
        // If the user is loaded and signed in, process the success
        if (isLoaded) {
          if (isSignedIn && user) {
            console.log('User is signed in:', user.id);
            
            // Get the OAuth provider from URL or localStorage
            const params = new URLSearchParams(window.location.search);
            const provider = params.get('provider') || 
                            localStorage.getItem('clerk_auth_provider') || 
                            'google';
            
            // Process the OAuth success
            const result = await processOAuthSuccess(user, provider);
            
            if (result) {
              console.log('OAuth success processed successfully');
              setStatus('success');
              
              // Sync user with Supabase
              try {
                await syncClerkUserWithSupabase(user);
              } catch (syncError) {
                console.error('Error syncing user with Supabase:', syncError);
              }
              
              // Start countdown for redirect
              startCountdown();
            } else {
              console.error('Failed to process OAuth success');
              setStatus('error');
              setError('Failed to process authentication. Please try again.');
            }
          } else {
            console.log('User is not signed in');
            setStatus('error');
            setError('Authentication failed. Please try again.');
          }
        }
      } catch (error) {
        console.error('Error processing OAuth callback:', error);
        setStatus('error');
        setError(`Authentication error: ${error instanceof Error ? error.message : String(error)}`);
      }
    };
    
    processCallback();
  }, [isLoaded, isSignedIn, user]);
  
  // Start countdown for redirect
  const startCountdown = () => {
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          // Redirect to home page
          navigate('/');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  
  // Render loading state
  if (status === 'processing') {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
        <h1 className="text-2xl font-bold mb-2">Processing Authentication</h1>
        <p className="text-gray-600">Please wait while we complete your sign-in...</p>
      </div>
    );
  }
  
  // Render success state
  if (status === 'success') {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="bg-green-100 text-green-800 p-4 rounded-lg mb-4">
          <svg className="w-6 h-6 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span className="font-bold">Authentication Successful!</span>
        </div>
        <h1 className="text-2xl font-bold mb-2">Welcome{user?.firstName ? `, ${user.firstName}` : ''}!</h1>
        <p className="text-gray-600 mb-4">You have successfully signed in.</p>
        <p className="text-gray-500">Redirecting to home page in {countdown} seconds...</p>
        <button 
          onClick={() => navigate('/')}
          className="mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Go to Home Page
        </button>
      </div>
    );
  }
  
  // Render error state
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="bg-red-100 text-red-800 p-4 rounded-lg mb-4">
        <svg className="w-6 h-6 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span className="font-bold">Authentication Failed</span>
      </div>
      <h1 className="text-2xl font-bold mb-2">Sign-In Error</h1>
      <p className="text-gray-600 mb-4">{error || 'An error occurred during authentication.'}</p>
      <div className="flex space-x-4">
        <button 
          onClick={() => navigate('/login')}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Try Again
        </button>
        <button 
          onClick={() => navigate('/')}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
        >
          Go to Home Page
        </button>
      </div>
    </div>
  );
};

export default ClerkOAuthCallback;
