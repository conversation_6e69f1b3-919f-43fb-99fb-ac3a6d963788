/**
 * Memory management for the Sales Assistant
 */

import { BufferMemory } from "langchain/memory";

/**
 * Create a new memory instance for storing conversation history
 * @returns {BufferMemory} Memory instance
 */
export function createMemory() {
  return new BufferMemory({
    memoryKey: "chat_history",
    returnMessages: true,
    inputKey: "input",
    outputKey: "output",
  });
}

/**
 * In-memory storage for conversation sessions
 * In a production environment, this would be replaced with a database
 */
export const sessionMemory = new Map();

/**
 * Get or create a memory instance for a session
 * @param {string} sessionId - Session identifier
 * @returns {BufferMemory} Memory instance for the session
 */
export function getSessionMemory(sessionId) {
  if (!sessionMemory.has(sessionId)) {
    sessionMemory.set(sessionId, createMemory());
  }
  return sessionMemory.get(sessionId);
}

/**
 * Clear the memory for a session
 * @param {string} sessionId - Session identifier
 */
export function clearSessionMemory(sessionId) {
  if (sessionMemory.has(sessionId)) {
    sessionMemory.delete(sessionId);
  }
}

export default {
  createMemory,
  getSessionMemory,
  clearSessionMemory,
  sessionMemory,
};
