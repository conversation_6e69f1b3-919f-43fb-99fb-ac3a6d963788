#!/usr/bin/env node

/**
 * 404 Verification Script
 * 
 * This script verifies that all URLs in the sitemap return proper content
 * instead of 404 errors, both locally and on the live site.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const publicDir = path.resolve(__dirname, '../public');

// Base URLs
const localBaseUrl = 'http://localhost:3000';
const liveBaseUrl = 'https://housegoing.com.au';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Extract URLs from sitemap
function extractUrlsFromSitemap() {
  const sitemapPath = path.join(publicDir, 'sitemap_comprehensive.xml');
  const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
  
  const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
  if (!urlMatches) return [];
  
  return urlMatches.map(match => {
    const url = match.replace('<loc>', '').replace('</loc>', '');
    return url.replace(liveBaseUrl, '') || '/';
  });
}

// Check if static file exists locally
function checkLocalFile(urlPath) {
  let filePath;
  
  if (urlPath === '/') {
    filePath = path.join(publicDir, 'index.html');
  } else {
    const cleanPath = urlPath.replace(/^\//, '');
    if (cleanPath.includes('/')) {
      filePath = path.join(publicDir, `${cleanPath}.html`);
    } else {
      filePath = path.join(publicDir, `${cleanPath}.html`);
    }
  }
  
  return {
    exists: fs.existsSync(filePath),
    path: filePath,
    size: fs.existsSync(filePath) ? fs.statSync(filePath).size : 0
  };
}

// Verify all URLs
function verifyAllUrls() {
  console.log('🔍 Verifying all sitemap URLs for 404 prevention...\n');
  
  const urls = extractUrlsFromSitemap();
  console.log(`Found ${urls.length} URLs to verify\n`);
  
  let successCount = 0;
  let errorCount = 0;
  const errors = [];
  
  console.log('📁 LOCAL FILE VERIFICATION:');
  console.log('=' .repeat(50));
  
  for (const urlPath of urls) {
    const fileCheck = checkLocalFile(urlPath);
    
    if (fileCheck.exists) {
      const sizeKB = (fileCheck.size / 1024).toFixed(1);
      log(`✅ ${urlPath} → ${path.basename(fileCheck.path)} (${sizeKB} KB)`, 'green');
      successCount++;
    } else {
      log(`❌ ${urlPath} → Missing file: ${path.basename(fileCheck.path)}`, 'red');
      errors.push({
        url: urlPath,
        issue: 'Missing static file',
        file: fileCheck.path
      });
      errorCount++;
    }
  }
  
  console.log('\n📊 VERIFICATION SUMMARY:');
  console.log('=' .repeat(50));
  log(`✅ Files exist: ${successCount}`, 'green');
  log(`❌ Missing files: ${errorCount}`, errorCount > 0 ? 'red' : 'green');
  log(`📈 Success rate: ${((successCount / urls.length) * 100).toFixed(1)}%`, successCount === urls.length ? 'green' : 'yellow');
  
  if (errors.length > 0) {
    console.log('\n🚨 ISSUES FOUND:');
    console.log('=' .repeat(50));
    errors.forEach((error, index) => {
      log(`${index + 1}. ${error.url}`, 'red');
      log(`   Issue: ${error.issue}`, 'yellow');
      log(`   Expected file: ${error.file}`, 'blue');
    });
    
    console.log('\n🔧 RECOMMENDED FIXES:');
    console.log('1. Run: node scripts/generate-all-pages.js');
    console.log('2. Check for any file generation errors');
    console.log('3. Verify directory permissions');
    console.log('4. Re-deploy static files to production');
  }
  
  console.log('\n🚀 DEPLOYMENT CHECKLIST:');
  console.log('=' .repeat(50));
  console.log('□ All static HTML files generated locally');
  console.log('□ Files deployed to production server');
  console.log('□ Test live URLs manually');
  console.log('□ Submit updated sitemap to Google Search Console');
  console.log('□ Request indexing for key pages');
  console.log('□ Monitor Google Search Console for 404 reduction');
  
  console.log('\n📋 CRITICAL URLS TO TEST FIRST:');
  const criticalUrls = [
    '/',
    '/find-venues',
    '/contact',
    '/help',
    '/safety',
    '/venue/venue-001',
    '/locations/sydney-cbd'
  ];
  
  criticalUrls.forEach(url => {
    console.log(`🔗 ${liveBaseUrl}${url}`);
  });
  
  console.log('\n💡 TESTING INSTRUCTIONS:');
  console.log('1. Open each URL above in your browser');
  console.log('2. Verify you see content, not "404 - Page Not Found"');
  console.log('3. Check that pages have proper titles and descriptions');
  console.log('4. Ensure pages redirect to SPA after 2 seconds for interactivity');
  
  return {
    total: urls.length,
    success: successCount,
    errors: errorCount,
    successRate: (successCount / urls.length) * 100
  };
}

// Generate deployment report
function generateDeploymentReport(results) {
  const reportContent = `# 404 Prevention Deployment Report

## Summary
- **Total URLs**: ${results.total}
- **Static files generated**: ${results.success}
- **Missing files**: ${results.errors}
- **Success rate**: ${results.successRate.toFixed(1)}%
- **Generated on**: ${new Date().toISOString()}

## Status
${results.errors === 0 ? '✅ ALL URLS HAVE STATIC FILES' : '❌ SOME FILES ARE MISSING'}

## Next Steps
${results.errors === 0 ? 
  `1. Deploy all files to production server
2. Test critical URLs manually
3. Submit sitemap to Google Search Console
4. Monitor for 404 error reduction` :
  `1. Fix missing files by running generation script
2. Verify file permissions and paths
3. Re-run verification
4. Deploy once all files exist`
}

## Critical URLs to Test
- https://housegoing.com.au/
- https://housegoing.com.au/find-venues
- https://housegoing.com.au/contact
- https://housegoing.com.au/help
- https://housegoing.com.au/safety
- https://housegoing.com.au/venue/venue-001

## Expected Results
- All URLs should show content instead of 404 errors
- Pages should have proper SEO meta tags
- Google Search Console should show reduced 404 errors within 24-48 hours
- Discovered pages should increase from 19 to 48+
`;

  fs.writeFileSync('404-prevention-report.md', reportContent);
  console.log('\n📄 Report saved: 404-prevention-report.md');
}

// Main execution
function main() {
  log('🛡️  HouseGoing 404 Prevention Verification', 'bold');
  log('==========================================\n', 'bold');
  
  const results = verifyAllUrls();
  generateDeploymentReport(results);
  
  if (results.errors === 0) {
    log('\n🎉 SUCCESS! All URLs have static files ready for deployment!', 'green');
    log('Deploy these files to eliminate all 404 errors.', 'green');
  } else {
    log('\n⚠️  WARNING! Some files are missing.', 'yellow');
    log('Fix the issues above before deploying.', 'yellow');
  }
}

// Run the script
main();
