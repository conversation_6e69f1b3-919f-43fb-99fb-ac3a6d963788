/**
 * NSW Party Planning Tool - Local Storage API
 * 
 * This module provides functions to store and retrieve NSW Party Planning data
 * using localStorage as a fallback when database connections fail.
 */

import { saveToLocalStorage, getFromLocalStorage } from '../../utils/localStorageUtils';

// Storage keys
const STORAGE_KEYS = {
  RECENT_ADDRESSES: 'nsw_party_planning_recent_addresses',
  SAVED_RESULTS: 'nsw_party_planning_saved_results',
  USER_PREFERENCES: 'nsw_party_planning_user_preferences'
};

// Types
export interface SavedAddress {
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  council: string;
  zoning: string;
  timestamp: number;
}

export interface SavedResult {
  id: string;
  address: string;
  propertyType: string;
  zoneCode: string;
  zoneName: string;
  lgaName: string;
  date: string;
  curfewStart: string;
  curfewEnd: string;
  bassRestrictionStart: string | null;
  bassRestrictionEnd: string | null;
  outdoorCutoff: string | null;
  specialConditions: string;
  recommendations: Array<{
    timePeriod: string;
    recommendation: string;
  }>;
  timestamp: number;
}

export interface UserPreferences {
  defaultPropertyType: string;
  showBassRestrictions: boolean;
  showOutdoorCutoff: boolean;
  showSpecialConditions: boolean;
  darkMode: boolean;
}

// Default values
const DEFAULT_RECENT_ADDRESSES: SavedAddress[] = [];
const DEFAULT_SAVED_RESULTS: SavedResult[] = [];
const DEFAULT_USER_PREFERENCES: UserPreferences = {
  defaultPropertyType: 'House',
  showBassRestrictions: true,
  showOutdoorCutoff: true,
  showSpecialConditions: true,
  darkMode: false
};

/**
 * Save a recent address
 * @param address The address to save
 */
export function saveRecentAddress(address: SavedAddress): void {
  const recentAddresses = getRecentAddresses();
  
  // Check if address already exists
  const existingIndex = recentAddresses.findIndex(a => 
    a.address === address.address
  );
  
  if (existingIndex !== -1) {
    // Update existing address
    recentAddresses[existingIndex] = {
      ...address,
      timestamp: Date.now()
    };
  } else {
    // Add new address
    recentAddresses.push({
      ...address,
      timestamp: Date.now()
    });
  }
  
  // Keep only the 10 most recent addresses
  const sortedAddresses = recentAddresses
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 10);
  
  saveToLocalStorage(STORAGE_KEYS.RECENT_ADDRESSES, sortedAddresses);
}

/**
 * Get recent addresses
 * @returns Array of recent addresses
 */
export function getRecentAddresses(): SavedAddress[] {
  return getFromLocalStorage<SavedAddress[]>(
    STORAGE_KEYS.RECENT_ADDRESSES,
    DEFAULT_RECENT_ADDRESSES
  );
}

/**
 * Save a result
 * @param result The result to save
 */
export function saveResult(result: SavedResult): void {
  const savedResults = getSavedResults();
  
  // Check if result already exists
  const existingIndex = savedResults.findIndex(r => r.id === result.id);
  
  if (existingIndex !== -1) {
    // Update existing result
    savedResults[existingIndex] = {
      ...result,
      timestamp: Date.now()
    };
  } else {
    // Add new result
    savedResults.push({
      ...result,
      timestamp: Date.now()
    });
  }
  
  saveToLocalStorage(STORAGE_KEYS.SAVED_RESULTS, savedResults);
}

/**
 * Get saved results
 * @returns Array of saved results
 */
export function getSavedResults(): SavedResult[] {
  return getFromLocalStorage<SavedResult[]>(
    STORAGE_KEYS.SAVED_RESULTS,
    DEFAULT_SAVED_RESULTS
  );
}

/**
 * Delete a saved result
 * @param id The ID of the result to delete
 */
export function deleteSavedResult(id: string): void {
  const savedResults = getSavedResults();
  const filteredResults = savedResults.filter(r => r.id !== id);
  saveToLocalStorage(STORAGE_KEYS.SAVED_RESULTS, filteredResults);
}

/**
 * Save user preferences
 * @param preferences The preferences to save
 */
export function saveUserPreferences(preferences: UserPreferences): void {
  saveToLocalStorage(STORAGE_KEYS.USER_PREFERENCES, preferences);
}

/**
 * Get user preferences
 * @returns User preferences
 */
export function getUserPreferences(): UserPreferences {
  return getFromLocalStorage<UserPreferences>(
    STORAGE_KEYS.USER_PREFERENCES,
    DEFAULT_USER_PREFERENCES
  );
}
