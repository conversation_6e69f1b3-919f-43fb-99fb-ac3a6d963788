import { storeFeedback, getPromptTemplate, updatePromptTemplateDirectly } from '../../lib/langchain/feedback';

/**
 * API endpoint for AI feedback
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { action, data } = req.body;

    if (!action || !data) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    switch (action) {
      case 'submit-feedback':
        // Validate feedback data
        if (!data.messageId || !data.rating || !data.agentType) {
          return res.status(400).json({ error: 'Invalid feedback data' });
        }
        
        // Store feedback
        const result = await storeFeedback({
          messageId: data.messageId,
          conversationId: data.conversationId || `conv_${Date.now()}`,
          userMessage: data.userMessage || '',
          assistantResponse: data.assistantResponse || '',
          rating: data.rating,
          notes: data.notes || '',
          agentType: data.agentType
        });
        
        if (!result.success) {
          return res.status(500).json({ error: 'Failed to store feedback' });
        }
        
        return res.status(200).json({ 
          success: true,
          message: 'Feedback submitted successfully'
        });
        
      case 'get-prompt':
        // Validate request
        if (!data.agentType) {
          return res.status(400).json({ error: 'Agent type is required' });
        }
        
        // Get prompt template
        const promptTemplate = await getPromptTemplate(data.agentType);
        
        return res.status(200).json({ 
          success: true,
          promptTemplate
        });
        
      case 'update-prompt':
        // Validate request
        if (!data.agentType || !data.promptTemplate) {
          return res.status(400).json({ error: 'Agent type and prompt template are required' });
        }
        
        // Update prompt template
        const updateResult = await updatePromptTemplateDirectly(
          data.agentType,
          data.promptTemplate
        );
        
        if (!updateResult.success) {
          return res.status(500).json({ error: 'Failed to update prompt template' });
        }
        
        return res.status(200).json({ 
          success: true,
          message: 'Prompt template updated successfully'
        });
        
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Error in AI feedback API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
