CREATE TABLE confidence_scoring (
  id SERIAL PRIMARY KEY,
  factor VARCHAR(100) NOT NULL,
  points INTEGER NOT NULL,
  notes TEXT
);

INSERT INTO confidence_scoring (factor, points, notes)
VALUES
  ('Property type clearly identifiable', 3, 'Address pattern clearly indicates property type'),
  ('LGA known from postcode', 2, 'Postcode uniquely maps to LGA'),
  ('Suburb fully within one zone type', 2, 'Some suburbs have consistent zoning'),
  ('Property on known street type', 1, 'Some street types correlate with zoning'),
  ('Suburb has mixed zoning', -1, 'Reduces confidence in automatic classification'),
  ('Multiple LGAs share postcode', -1, 'Reduces confidence in regulatory authority'),
  ('Tourism or seasonal area', -1, 'Rules may vary by season');
