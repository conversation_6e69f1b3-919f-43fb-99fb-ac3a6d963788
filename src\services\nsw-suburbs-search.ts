/**
 * NSW Suburbs Search Service
 *
 * Provides auto-suggestions for NSW suburbs with postcode information
 * for venue location search functionality.
 */

import { supabase } from '../lib/supabase-client';

// Cache for search results
const searchCache: Record<string, { results: NSWSuburb[]; timestamp: number }> = {};
const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes

export interface NSWSuburb {
  id: string;
  suburb: string;
  postcode: string;
  lga_name: string;
  state: string;
  lat?: number;
  lng?: number;
  display_name: string;
}

/**
 * Comprehensive NSW suburbs database with major locations
 * This serves as fallback data when Supabase is not available
 */
const NSW_SUBURBS_FALLBACK: NSWSuburb[] = [
  // Sydney Metro
  { id: '1', suburb: 'Sydney', postcode: '2000', lga_name: 'City of Sydney', state: 'NSW', lat: -33.8688, lng: 151.2093, display_name: 'Sydney, NSW 2000' },
  { id: '2', suburb: 'Bondi Beach', postcode: '2026', lga_name: 'Waverley Council', state: 'NSW', lat: -33.8915, lng: 151.2767, display_name: 'Bondi Beach, NSW 2026' },
  { id: '3', suburb: 'Newtown', postcode: '2042', lga_name: 'Inner West Council', state: 'NSW', lat: -33.8978, lng: 151.1795, display_name: 'Newtown, NSW 2042' },
  { id: '4', suburb: 'Parramatta', postcode: '2150', lga_name: 'City of Parramatta', state: 'NSW', lat: -33.8150, lng: 151.0011, display_name: 'Parramatta, NSW 2150' },
  { id: '5', suburb: 'Manly', postcode: '2095', lga_name: 'Northern Beaches Council', state: 'NSW', lat: -33.7969, lng: 151.2840, display_name: 'Manly, NSW 2095' },
  { id: '6', suburb: 'Surry Hills', postcode: '2010', lga_name: 'City of Sydney', state: 'NSW', lat: -33.8886, lng: 151.2094, display_name: 'Surry Hills, NSW 2010' },
  { id: '7', suburb: 'Paddington', postcode: '2021', lga_name: 'City of Sydney', state: 'NSW', lat: -33.8841, lng: 151.2291, display_name: 'Paddington, NSW 2021' },
  { id: '8', suburb: 'Darlinghurst', postcode: '2010', lga_name: 'City of Sydney', state: 'NSW', lat: -33.8774, lng: 151.2167, display_name: 'Darlinghurst, NSW 2010' },
  { id: '9', suburb: 'Bondi Junction', postcode: '2022', lga_name: 'Waverley Council', state: 'NSW', lat: -33.8947, lng: 151.2477, display_name: 'Bondi Junction, NSW 2022' },
  { id: '10', suburb: 'Randwick', postcode: '2031', lga_name: 'Randwick City Council', state: 'NSW', lat: -33.9159, lng: 151.2415, display_name: 'Randwick, NSW 2031' },

  // Northern Beaches
  { id: '11', suburb: 'Dee Why', postcode: '2099', lga_name: 'Northern Beaches Council', state: 'NSW', lat: -33.7516, lng: 151.2844, display_name: 'Dee Why, NSW 2099' },
  { id: '12', suburb: 'Avalon Beach', postcode: '2107', lga_name: 'Northern Beaches Council', state: 'NSW', lat: -33.6356, lng: 151.3242, display_name: 'Avalon Beach, NSW 2107' },
  { id: '13', suburb: 'Palm Beach', postcode: '2108', lga_name: 'Northern Beaches Council', state: 'NSW', lat: -33.5994, lng: 151.3242, display_name: 'Palm Beach, NSW 2108' },

  // Inner West
  { id: '14', suburb: 'Marrickville', postcode: '2204', lga_name: 'Inner West Council', state: 'NSW', lat: -33.9115, lng: 151.1555, display_name: 'Marrickville, NSW 2204' },
  { id: '15', suburb: 'Enmore', postcode: '2042', lga_name: 'Inner West Council', state: 'NSW', lat: -33.8978, lng: 151.1795, display_name: 'Enmore, NSW 2042' },
  { id: '16', suburb: 'Leichhardt', postcode: '2040', lga_name: 'Inner West Council', state: 'NSW', lat: -33.8836, lng: 151.1561, display_name: 'Leichhardt, NSW 2040' },

  // Eastern Suburbs
  { id: '17', suburb: 'Coogee', postcode: '2034', lga_name: 'Randwick City Council', state: 'NSW', lat: -33.9208, lng: 151.2559, display_name: 'Coogee, NSW 2034' },
  { id: '18', suburb: 'Maroubra', postcode: '2035', lga_name: 'Randwick City Council', state: 'NSW', lat: -33.9506, lng: 151.2364, display_name: 'Maroubra, NSW 2035' },
  { id: '19', suburb: 'Double Bay', postcode: '2028', lga_name: 'Woollahra Council', state: 'NSW', lat: -33.8774, lng: 151.2408, display_name: 'Double Bay, NSW 2028' },
  { id: '19a', suburb: 'Cronulla', postcode: '2230', lga_name: 'Sutherland Shire Council', state: 'NSW', lat: -34.0581, lng: 151.1543, display_name: 'Cronulla, NSW 2230' },

  // North Shore
  { id: '20', suburb: 'North Sydney', postcode: '2060', lga_name: 'North Sydney Council', state: 'NSW', lat: -33.8403, lng: 151.2065, display_name: 'North Sydney, NSW 2060' },
  { id: '21', suburb: 'Chatswood', postcode: '2067', lga_name: 'Willoughby City Council', state: 'NSW', lat: -33.7967, lng: 151.1800, display_name: 'Chatswood, NSW 2067' },
  { id: '22', suburb: 'Hornsby', postcode: '2077', lga_name: 'Hornsby Shire Council', state: 'NSW', lat: -33.7047, lng: 151.0993, display_name: 'Hornsby, NSW 2077' },

  // Western Sydney
  { id: '23', suburb: 'Penrith', postcode: '2750', lga_name: 'City of Penrith', state: 'NSW', lat: -33.7506, lng: 150.6944, display_name: 'Penrith, NSW 2750' },
  { id: '24', suburb: 'Blacktown', postcode: '2148', lga_name: 'City of Blacktown', state: 'NSW', lat: -33.7681, lng: 150.9072, display_name: 'Blacktown, NSW 2148' },
  { id: '25', suburb: 'Liverpool', postcode: '2170', lga_name: 'Liverpool City Council', state: 'NSW', lat: -33.9213, lng: 150.9218, display_name: 'Liverpool, NSW 2170' },

  // Regional NSW
  { id: '26', suburb: 'Newcastle', postcode: '2300', lga_name: 'City of Newcastle', state: 'NSW', lat: -32.9267, lng: 151.7789, display_name: 'Newcastle, NSW 2300' },
  { id: '27', suburb: 'Wollongong', postcode: '2500', lga_name: 'Wollongong City Council', state: 'NSW', lat: -34.4278, lng: 150.8931, display_name: 'Wollongong, NSW 2500' },
  { id: '28', suburb: 'Central Coast', postcode: '2250', lga_name: 'Central Coast Council', state: 'NSW', lat: -33.4269, lng: 151.3428, display_name: 'Central Coast, NSW 2250' },
  { id: '29', suburb: 'Blue Mountains', postcode: '2780', lga_name: 'Blue Mountains City Council', state: 'NSW', lat: -33.7122, lng: 150.3111, display_name: 'Blue Mountains, NSW 2780' },
  { id: '30', suburb: 'Byron Bay', postcode: '2481', lga_name: 'Byron Shire Council', state: 'NSW', lat: -28.6474, lng: 153.6020, display_name: 'Byron Bay, NSW 2481' },

  // Additional popular NSW suburbs
  { id: '31', suburb: 'Ryde', postcode: '2112', lga_name: 'City of Ryde', state: 'NSW', lat: -33.8149, lng: 151.1056, display_name: 'Ryde, NSW 2112' },
  { id: '32', suburb: 'Hornsby', postcode: '2077', lga_name: 'Hornsby Shire Council', state: 'NSW', lat: -33.7047, lng: 151.0983, display_name: 'Hornsby, NSW 2077' },
  { id: '33', suburb: 'Blacktown', postcode: '2148', lga_name: 'Blacktown City Council', state: 'NSW', lat: -33.7688, lng: 150.9062, display_name: 'Blacktown, NSW 2148' },
  { id: '34', suburb: 'Liverpool', postcode: '2170', lga_name: 'Liverpool City Council', state: 'NSW', lat: -33.9249, lng: 150.9136, display_name: 'Liverpool, NSW 2170' },
  { id: '35', suburb: 'Campbelltown', postcode: '2560', lga_name: 'Campbelltown City Council', state: 'NSW', lat: -34.0639, lng: 150.8150, display_name: 'Campbelltown, NSW 2560' },
  { id: '36', suburb: 'Penrith', postcode: '2750', lga_name: 'Penrith City Council', state: 'NSW', lat: -33.7506, lng: 150.6944, display_name: 'Penrith, NSW 2750' },
  { id: '37', suburb: 'Bankstown', postcode: '2200', lga_name: 'Canterbury-Bankstown Council', state: 'NSW', lat: -33.9181, lng: 151.0350, display_name: 'Bankstown, NSW 2200' },
  { id: '38', suburb: 'Fairfield', postcode: '2165', lga_name: 'Fairfield City Council', state: 'NSW', lat: -33.8697, lng: 150.9539, display_name: 'Fairfield, NSW 2165' },
  { id: '39', suburb: 'Hurstville', postcode: '2220', lga_name: 'Georges River Council', state: 'NSW', lat: -33.9676, lng: 151.1030, display_name: 'Hurstville, NSW 2220' },
  { id: '40', suburb: 'Sutherland', postcode: '2232', lga_name: 'Sutherland Shire Council', state: 'NSW', lat: -34.0311, lng: 151.0569, display_name: 'Sutherland, NSW 2232' },
  { id: '41', suburb: 'Randwick', postcode: '2031', lga_name: 'Randwick City Council', state: 'NSW', lat: -33.9159, lng: 151.2415, display_name: 'Randwick, NSW 2031' },
  { id: '42', suburb: 'Woollahra', postcode: '2025', lga_name: 'Woollahra Municipal Council', state: 'NSW', lat: -33.8847, lng: 151.2444, display_name: 'Woollahra, NSW 2025' },
  { id: '43', suburb: 'Leichhardt', postcode: '2040', lga_name: 'Inner West Council', state: 'NSW', lat: -33.8833, lng: 151.1569, display_name: 'Leichhardt, NSW 2040' },
  { id: '44', suburb: 'Marrickville', postcode: '2204', lga_name: 'Inner West Council', state: 'NSW', lat: -33.9111, lng: 151.1556, display_name: 'Marrickville, NSW 2204' },
  { id: '45', suburb: 'Ashfield', postcode: '2131', lga_name: 'Inner West Council', state: 'NSW', lat: -33.8889, lng: 151.1264, display_name: 'Ashfield, NSW 2131' },
  { id: '46', suburb: 'Burwood', postcode: '2134', lga_name: 'Burwood Council', state: 'NSW', lat: -33.8772, lng: 151.1044, display_name: 'Burwood, NSW 2134' },
  { id: '47', suburb: 'Strathfield', postcode: '2135', lga_name: 'Strathfield Municipal Council', state: 'NSW', lat: -33.8708, lng: 151.0944, display_name: 'Strathfield, NSW 2135' },
  { id: '48', suburb: 'Auburn', postcode: '2144', lga_name: 'Cumberland Council', state: 'NSW', lat: -33.8489, lng: 151.0322, display_name: 'Auburn, NSW 2144' },
  { id: '49', suburb: 'Holroyd', postcode: '2142', lga_name: 'Cumberland Council', state: 'NSW', lat: -33.8167, lng: 150.9833, display_name: 'Holroyd, NSW 2142' },
  { id: '50', suburb: 'Baulkham Hills', postcode: '2153', lga_name: 'The Hills Shire Council', state: 'NSW', lat: -33.7608, lng: 150.9889, display_name: 'Baulkham Hills, NSW 2153' }
];

/**
 * Search NSW suburbs using Maps.co API with comprehensive coverage
 * @param query Search query (suburb name or postcode)
 * @param limit Maximum number of results to return
 * @returns Promise<NSWSuburb[]>
 */
export async function searchNSWSuburbs(query: string, limit: number = 10): Promise<NSWSuburb[]> {
  if (!query || query.length < 2) {
    return [];
  }

  const cacheKey = `${query.toLowerCase()}_${limit}`;

  // Check cache first
  if (searchCache[cacheKey] && Date.now() - searchCache[cacheKey].timestamp < CACHE_EXPIRATION) {
    console.log('Using cached results for:', query);
    return searchCache[cacheKey].results;
  }

  console.log('Searching NSW suburbs for:', query);

  // Use comprehensive local search with fuzzy matching
  const results = searchLocalNSWSuburbs(query, limit);

  if (results.length > 0) {
    console.log(`Found ${results.length} local results for "${query}"`);

    // Cache the results
    searchCache[cacheKey] = {
      results,
      timestamp: Date.now()
    };

    return results;
  }

  console.log(`No results found for "${query}"`);

  // Cache empty results to avoid repeated API calls for a short time
  searchCache[cacheKey] = {
    results: [],
    timestamp: Date.now()
  };

  return [];
}

/**
 * Search local NSW suburbs data with fuzzy matching
 */
function searchLocalNSWSuburbs(query: string, limit: number): NSWSuburb[] {
  const lowerQuery = query.toLowerCase().trim();

  // Exact matches first
  const exactMatches = NSW_SUBURBS_FALLBACK.filter(suburb =>
    suburb.suburb.toLowerCase() === lowerQuery ||
    suburb.postcode === query
  );

  // Starts with matches
  const startsWithMatches = NSW_SUBURBS_FALLBACK.filter(suburb =>
    suburb.suburb.toLowerCase().startsWith(lowerQuery) &&
    !exactMatches.some(exact => exact.id === suburb.id)
  );

  // Contains matches
  const containsMatches = NSW_SUBURBS_FALLBACK.filter(suburb =>
    (suburb.suburb.toLowerCase().includes(lowerQuery) ||
     suburb.display_name.toLowerCase().includes(lowerQuery)) &&
    !exactMatches.some(exact => exact.id === suburb.id) &&
    !startsWithMatches.some(starts => starts.id === suburb.id)
  );

  // Combine results in order of relevance
  const allResults = [...exactMatches, ...startsWithMatches, ...containsMatches];

  return allResults.slice(0, limit);
}

/**
 * Enhance local results with API data in background
 */
async function enhanceWithAPIResults(query: string, limit: number, cacheKey: string) {
  try {
    const apiResults = await searchNSWSuburbsWithMapsAPI(query, limit);
    if (apiResults.length > 0) {
      console.log(`Enhanced with ${apiResults.length} API results for "${query}"`);
      // Update cache with enhanced results
      searchCache[cacheKey] = {
        results: apiResults,
        timestamp: Date.now()
      };
    }
  } catch (error) {
    console.warn('Background API enhancement failed:', error);
  }
}

/**
 * Search NSW suburbs using Maps.co API
 * @param query Search query
 * @param limit Maximum results
 * @returns Promise<NSWSuburb[]>
 */
async function searchNSWSuburbsWithMapsAPI(query: string, limit: number): Promise<NSWSuburb[]> {
  const API_KEY = '67ea5a7e84615836239135wpc5a6d73';

  // Simple, reliable search strategy
  const searchQuery = `${query}, NSW, Australia`;

  try {
    console.log(`Searching Maps.co API for: "${searchQuery}"`);

    const response = await fetch(
      `https://geocode.maps.co/search?q=${encodeURIComponent(searchQuery)}&api_key=${API_KEY}&limit=${Math.min(limit * 2, 20)}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        // Add timeout
        signal: AbortSignal.timeout(10000) // 10 second timeout
      }
    );

    if (!response.ok) {
      throw new Error(`Maps.co API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data || !Array.isArray(data)) {
      console.log('Maps.co API returned invalid data format');
      return [];
    }

    console.log(`Maps.co API returned ${data.length} results`);

    // Filter and process results for NSW suburbs
    const nswResults = data
      .filter(item => {
        if (!item.display_name) return false;

        const displayName = item.display_name.toLowerCase();
        const hasNSW = displayName.includes('nsw') || displayName.includes('new south wales');
        const hasAustralia = displayName.includes('australia');
        const isValidType = item.type !== 'country' && item.type !== 'state';

        return hasNSW && hasAustralia && isValidType;
      })
      .slice(0, limit)
      .map((item, index) => {
        // Extract suburb name from the first part of display_name
        const displayParts = item.display_name.split(',').map((part: string) => part.trim());
        let suburb = displayParts[0] || query;

        // Clean up suburb name
        suburb = suburb.replace(/\s+(suburb|town|city|village)$/i, '').trim();

        // Extract postcode
        const postcodeMatch = item.display_name.match(/\b(\d{4})\b/);
        const postcode = postcodeMatch ? postcodeMatch[1] : '0000';

        // Extract LGA name (usually the part before NSW)
        let lgaName = 'Unknown Council';
        const nswIndex = displayParts.findIndex(part =>
          part.toLowerCase().includes('nsw') || part.toLowerCase().includes('new south wales')
        );
        if (nswIndex > 1) {
          lgaName = displayParts[nswIndex - 1];
        }

        return {
          id: `maps_${item.place_id || `${suburb}_${postcode}_${index}`}`,
          suburb: suburb,
          postcode: postcode,
          lga_name: lgaName,
          state: 'NSW',
          lat: parseFloat(item.lat) || -33.8688,
          lng: parseFloat(item.lon) || 151.2093,
          display_name: `${suburb}, NSW ${postcode}`
        };
      });

    console.log(`Processed ${nswResults.length} NSW suburbs from Maps.co API`);
    return nswResults;

  } catch (error) {
    console.error('Maps.co API search failed:', error);
    return [];
  }
}

/**
 * Get popular NSW locations for quick selection
 */
export function getPopularNSWSuburbs(): NSWSuburb[] {
  return [
    NSW_SUBURBS_FALLBACK[0], // Sydney
    NSW_SUBURBS_FALLBACK[1], // Bondi Beach
    NSW_SUBURBS_FALLBACK[2], // Newtown
    NSW_SUBURBS_FALLBACK[3], // Parramatta
    NSW_SUBURBS_FALLBACK[4], // Manly
    NSW_SUBURBS_FALLBACK[25], // Newcastle
    NSW_SUBURBS_FALLBACK[26], // Wollongong
    NSW_SUBURBS_FALLBACK[29]  // Byron Bay
  ];
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param lat1 Latitude of first point
 * @param lng1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lng2 Longitude of second point
 * @returns Distance in kilometers
 */
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Search NSW suburbs within a specific radius of a location
 * @param centerLat Center latitude
 * @param centerLng Center longitude
 * @param radiusKm Radius in kilometers
 * @param limit Maximum number of results
 * @returns Promise<NSWSuburb[]>
 */
export async function searchNSWSuburbsWithinRadius(
  centerLat: number,
  centerLng: number,
  radiusKm: number,
  limit: number = 20
): Promise<NSWSuburb[]> {
  const cacheKey = `radius_${centerLat}_${centerLng}_${radiusKm}_${limit}`;

  // Check cache first
  if (searchCache[cacheKey] && Date.now() - searchCache[cacheKey].timestamp < CACHE_EXPIRATION) {
    return searchCache[cacheKey].results;
  }

  try {
    // Try Supabase first with spatial query
    const { data, error } = await supabase
      .rpc('get_suburbs_within_radius', {
        center_lat: centerLat,
        center_lng: centerLng,
        radius_km: radiusKm,
        result_limit: limit
      });

    if (!error && data && data.length > 0) {
      const results = data.map((item: any) => ({
        id: item.id || `${item.suburb}_${item.postcode}`,
        suburb: item.suburb,
        postcode: item.postcode,
        lga_name: item.lga_name || 'Unknown Council',
        state: 'NSW',
        lat: item.lat || item.latitude,
        lng: item.lng || item.longitude,
        display_name: `${item.suburb}, NSW ${item.postcode}`
      }));

      // Cache the results
      searchCache[cacheKey] = {
        results,
        timestamp: Date.now()
      };

      return results;
    }
  } catch (error) {
    console.warn('Supabase radius search failed, using fallback:', error);
  }

  // Fallback to local calculation
  const results = NSW_SUBURBS_FALLBACK
    .filter(suburb => {
      if (!suburb.lat || !suburb.lng) return false;
      const distance = calculateDistance(centerLat, centerLng, suburb.lat, suburb.lng);
      return distance <= radiusKm;
    })
    .sort((a, b) => {
      const distA = calculateDistance(centerLat, centerLng, a.lat!, a.lng!);
      const distB = calculateDistance(centerLat, centerLng, b.lat!, b.lng!);
      return distA - distB;
    })
    .slice(0, limit);

  // Cache the results
  searchCache[cacheKey] = {
    results,
    timestamp: Date.now()
  };

  return results;
}

/**
 * Get suburb details by ID
 * @param suburbId Suburb ID
 * @returns Promise<NSWSuburb | null>
 */
export async function getSuburbById(suburbId: string): Promise<NSWSuburb | null> {
  try {
    // Try Supabase first
    const { data, error } = await supabase
      .from('nsw_suburbs')
      .select('*')
      .eq('id', suburbId)
      .single();

    if (!error && data) {
      return {
        id: data.id,
        suburb: data.suburb,
        postcode: data.postcode,
        lga_name: data.lga_name || 'Unknown Council',
        state: 'NSW',
        lat: data.lat || data.latitude,
        lng: data.lng || data.longitude,
        display_name: `${data.suburb}, NSW ${data.postcode}`
      };
    }
  } catch (error) {
    console.warn('Supabase suburb lookup failed, using fallback:', error);
  }

  // Fallback to local data
  return NSW_SUBURBS_FALLBACK.find(suburb => suburb.id === suburbId) || null;
}

/**
 * Clear the search cache
 */
export function clearSuburbsCache(): void {
  Object.keys(searchCache).forEach(key => delete searchCache[key]);
}
