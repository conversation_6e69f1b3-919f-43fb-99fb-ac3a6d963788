import React, { useState } from 'react';
import { Upload, X, Image as ImageIcon, CloudCog } from 'lucide-react';
import { uploadMultipleToCloudinary } from '../../lib/cloudinaryUploadService';

interface PhotoUploadProps {
  onImagesUploaded: (imageUrls: string[]) => void;
  existingImages?: string[];
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({ onImagesUploaded, existingImages = [] }) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const newFiles = Array.from(e.target.files);

    // Validate file types and sizes
    const invalidFiles = newFiles.filter(file => {
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type);
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB max
      return !isValidType || !isValidSize;
    });

    if (invalidFiles.length > 0) {
      setError('Some files were not added. Please only upload JPG or PNG images under 5MB.');
      // Filter out invalid files
      const validFiles = newFiles.filter(file => {
        const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type);
        const isValidSize = file.size <= 5 * 1024 * 1024;
        return isValidType && isValidSize;
      });

      // Add valid files
      addFiles(validFiles);
    } else {
      // All files are valid
      setError(null);
      addFiles(newFiles);
    }
  };

  // Add files to state and create preview URLs
  const addFiles = (files: File[]) => {
    // Create preview URLs for the new files
    const newPreviewUrls = files.map(file => URL.createObjectURL(file));

    setSelectedFiles(prev => [...prev, ...files]);
    setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
  };

  // Remove a file
  const removeFile = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(previewUrls[index]);

    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  // Upload files to Cloudinary
  const uploadFiles = async () => {
    if (selectedFiles.length === 0) return;

    setUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      console.log('Starting Cloudinary upload process...');

      // Upload files to Cloudinary
      const results = await uploadMultipleToCloudinary(
        selectedFiles,
        'property-images',
        (progress) => {
          setUploadProgress(progress);
        }
      );

      // Filter out failed uploads
      const successfulUploads = results.filter(result => !result.error);
      const failedUploads = results.filter(result => result.error);

      console.log(`Upload complete: ${successfulUploads.length}/${selectedFiles.length} files uploaded successfully`);

      if (failedUploads.length > 0) {
        console.warn('Failed uploads:', failedUploads);
      }

      if (successfulUploads.length === 0) {
        throw new Error('No files were uploaded successfully. Please try again.');
      }

      // Extract URLs from successful uploads
      const uploadedUrls = successfulUploads.map(result => result.url);

      // Call the callback with the uploaded URLs
      onImagesUploaded(uploadedUrls);

      // Clear the selected files and previews
      setSelectedFiles([]);
      setPreviewUrls([]);

      // Show success message if some uploads failed
      if (failedUploads.length > 0) {
        setError(`${failedUploads.length} file(s) failed to upload. The rest were uploaded successfully.`);
      } else {
        setError(null);
      }

    } catch (error: any) {
      console.error('Error in Cloudinary upload process:', error);
      setError(`Failed to upload images: ${error.message || 'Please try again.'}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <input
          type="file"
          multiple
          accept="image/jpeg,image/png,image/jpg"
          className="hidden"
          onChange={handleFileSelect}
          id="photo-upload"
          disabled={uploading}
        />
        <label
          htmlFor="photo-upload"
          className="cursor-pointer flex flex-col items-center"
        >
          <div className="flex items-center mb-4">
            <CloudCog className="h-12 w-12 text-blue-500 mr-2" />
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Powered by Cloudinary</span>
          </div>
          <span className="text-gray-600">
            Drag and drop photos here, or click to browse
          </span>
          <span className="text-sm text-gray-500 mt-2">
            Supported formats: JPG, PNG (max 5MB each)
          </span>
        </label>
      </div>

      {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}

      {/* Preview of selected files */}
      {previewUrls.length > 0 && (
        <div>
          <h3 className="font-medium mb-2">Selected Photos ({previewUrls.length})</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {previewUrls.map((url, index) => (
              <div key={index} className="relative group">
                <img
                  src={url}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-32 object-cover rounded-md"
                />
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Existing images */}
      {existingImages.length > 0 && (
        <div>
          <h3 className="font-medium mb-2">Existing Photos ({existingImages.length})</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {existingImages.map((url, index) => (
              <div key={index} className="relative">
                <img
                  src={url}
                  alt={`Existing ${index + 1}`}
                  className="w-full h-32 object-cover rounded-md"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload button */}
      {previewUrls.length > 0 && (
        <div>
          <button
            type="button"
            onClick={uploadFiles}
            disabled={uploading}
            className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:bg-purple-300 flex items-center"
          >
            {uploading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Uploading... {uploadProgress}%
              </>
            ) : (
              <>
                <ImageIcon className="mr-2 h-4 w-4" />
                Upload Photos
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default PhotoUpload;
