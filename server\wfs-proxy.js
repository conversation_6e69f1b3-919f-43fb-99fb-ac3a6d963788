/**
 * WFS Proxy Server
 *
 * This server proxies requests to NSW Planning Portal WFS services to avoid CORS issues.
 * It also provides caching to improve performance and reduce API calls.
 */

import express from 'express';
import fetch from 'node-fetch';
import cors from 'cors';

const app = express();
const PORT = process.env.WFS_PROXY_PORT || 3004;

// Enable CORS and JSON parsing
app.use(cors());
app.use(express.json());

// Simple in-memory cache
const cache = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// WFS proxy endpoint
app.post('/api/wfs', async (req, res) => {
  try {
    const { serviceUrl, params } = req.body;

    if (!serviceUrl) {
      return res.status(400).json({ error: 'Service URL is required' });
    }

    console.log('WFS Proxy received request:', {
      serviceUrl,
      params
    });

    // Generate cache key from request parameters
    const cacheKey = JSON.stringify({ serviceUrl, params });

    // Check cache first
    const cachedItem = cache.get(cacheKey);
    if (cachedItem && Date.now() - cachedItem.timestamp < CACHE_DURATION) {
      console.log('Cache hit for WFS request');
      return res.json(cachedItem.data);
    }

    // Build URL with parameters
    const url = new URL(serviceUrl);

    // Special handling for CQL_FILTER parameter
    if (params.CQL_FILTER) {
      console.log('Using CQL_FILTER:', params.CQL_FILTER);
      url.searchParams.append('CQL_FILTER', params.CQL_FILTER);
      delete params.CQL_FILTER;
    }

    // Special handling for filter parameter (XML)
    if (params.filter) {
      console.log('Using XML filter:', params.filter);
      url.searchParams.append('filter', params.filter);
      delete params.filter;
    }

    // Add remaining parameters
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    const fullUrl = url.toString();
    console.log(`Proxying WFS request to: ${fullUrl}`);

    // Make the request
    const response = await fetch(fullUrl, {
      headers: { 'Accept': 'application/json' }
    });

    // Check content type
    const contentType = response.headers.get('content-type');
    console.log(`WFS response status: ${response.status}, content-type: ${contentType}`);

    // Handle different response types
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();

      console.log(`WFS response data: ${data.features ? data.features.length : 0} features`);
      if (data.features && data.features.length > 0) {
        console.log('First feature properties:', data.features[0].properties);
      } else {
        console.log('No features found in response');
      }

      // Cache the result
      cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return res.json(data);
    } else {
      // Handle XML or other response types
      const text = await response.text();
      console.log('Non-JSON response:', text.substring(0, 500));

      if (response.ok) {
        return res.status(415).json({
          error: 'Unexpected response format',
          contentType,
          details: text.substring(0, 500) // Truncate long responses
        });
      } else {
        return res.status(response.status).json({
          error: `WFS Error: ${response.status} ${response.statusText}`,
          details: text.substring(0, 500) // Truncate long responses
        });
      }
    }
  } catch (error) {
    console.error('WFS proxy error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint
app.get('/api/wfs/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`WFS Proxy Server running on port ${PORT}`);
});

export default app;
