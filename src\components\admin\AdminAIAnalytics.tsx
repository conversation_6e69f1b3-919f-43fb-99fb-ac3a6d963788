import React, { useState } from 'react';
import { Brain, Send, TrendingUp, Users, DollarSign, Calendar, Loader, AlertCircle } from 'lucide-react';
import { aiService } from '../../services/aiService';

interface AdminAIAnalyticsProps {
  websiteData?: {
    totalUsers?: number;
    totalBookings?: number;
    totalRevenue?: string;
    topVenues?: string[];
    userGrowth?: string;
    recentActivity?: string[];
  };
}

export default function AdminAIAnalytics({ websiteData }: AdminAIAnalyticsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Mock website data for demonstration
  const defaultData = {
    totalUsers: 1250,
    totalBookings: 450,
    totalRevenue: '$125,000',
    topVenues: ['Harbour View Terrace', 'Urban Loft Space', 'Garden Pavilion'],
    userGrowth: '+15% this month',
    recentActivity: [
      'New host registration: <PERSON>',
      'Booking confirmed: Heritage Hall',
      'Property approved: Beachside Cabana',
      'User feedback: 5-star review'
    ]
  };

  const data = websiteData || defaultData;

  const handleAnalyzeData = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    setError('');
    setResponse('');

    try {
      const result = await aiService.getAdminAnalytics(query, data);

      if (result.success && result.message) {
        setResponse(result.message);
      } else {
        setError(result.error || 'Failed to analyze data. Please try again.');
      }
    } catch (err) {
      console.error('Admin AI Analytics Error:', err);
      setError('An error occurred while analyzing the data.');
    } finally {
      setIsLoading(false);
      setQuery('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAnalyzeData();
    }
  };

  const quickQuestions = [
    "What trends do you see in our booking data?",
    "How can we improve user engagement?",
    "What's driving our revenue growth?",
    "Which venues are performing best?",
    "What marketing strategies should we focus on?"
  ];

  if (!isOpen) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-3 rounded-lg">
              <Brain className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">AI Analytics Assistant</h3>
              <p className="text-sm text-gray-600">Get insights from your platform data</p>
            </div>
          </div>
          <button
            onClick={() => setIsOpen(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Analyze Data
          </button>
        </div>

        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-600">Total Users</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{data.totalUsers?.toLocaleString()}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-gray-600">Total Bookings</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{data.totalBookings?.toLocaleString()}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-600">Total Revenue</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{data.totalRevenue}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-gray-600">Growth</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{data.userGrowth}</p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> AI analysis uses OpenRouter API credits. Only use when you need specific insights to avoid unnecessary costs.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="h-6 w-6" />
          <div>
            <h3 className="font-semibold">AI Analytics Assistant</h3>
            <p className="text-sm text-blue-100">Ask questions about your platform data</p>
          </div>
        </div>
        <button
          onClick={() => setIsOpen(false)}
          className="text-blue-100 hover:text-white transition-colors"
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Quick Questions */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Questions:</h4>
          <div className="flex flex-wrap gap-2">
            {quickQuestions.map((question, index) => (
              <button
                key={index}
                onClick={() => setQuery(question)}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
              >
                {question}
              </button>
            ))}
          </div>
        </div>

        {/* Query Input */}
        <div className="mb-6">
          <div className="flex space-x-2">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask about trends, performance, opportunities..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
            <button
              onClick={handleAnalyzeData}
              disabled={!query.trim() || isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {isLoading ? (
                <Loader className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        {/* Response */}
        {response && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">AI Analysis:</h4>
            <div className="text-green-700 whitespace-pre-wrap">{response}</div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-800">Error</h4>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Loading */}
        {isLoading && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Loader className="h-5 w-5 text-blue-600 animate-spin" />
              <span className="text-blue-700">Analyzing your data...</span>
            </div>
          </div>
        )}

        {/* Data Summary */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2">Current Data Summary:</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <p>• {data.totalUsers} total users ({data.userGrowth})</p>
            <p>• {data.totalBookings} total bookings</p>
            <p>• {data.totalRevenue} total revenue</p>
            <p>• Top venues: {data.topVenues?.join(', ')}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
