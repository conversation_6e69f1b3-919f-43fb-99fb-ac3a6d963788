/**
 * Booking Messaging Integration
 *
 * Integrates messaging functionality into the booking flow:
 * - Pre-booking inquiries
 * - Post-booking communication
 * - Host-guest coordination
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../providers/AuthProvider';
import { sendMessage, getConversationMessages } from '../../api/userAccount';
import { MessageCircle, Send, User, Calendar, MapPin, Clock } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface BookingMessagingIntegrationProps {
  bookingId?: string;
  hostId: string;
  hostName: string;
  venueName: string;
  venueId: string;
  bookingDate?: string;
  bookingStatus?: string;
  className?: string;
  mode?: 'inquiry' | 'booking' | 'compact';
}

interface Message {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  read: boolean;
  sender_name: string;
}

export default function BookingMessagingIntegration({
  bookingId,
  hostId,
  hostName,
  venueName,
  venueId,
  bookingDate,
  bookingStatus,
  className = '',
  mode = 'booking'
}: BookingMessagingIntegrationProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [showMessages, setShowMessages] = useState(false);
  const [loading, setLoading] = useState(false);

  // Load existing messages
  useEffect(() => {
    if (!user || !hostId || !showMessages) return;

    const loadMessages = async () => {
      try {
        setLoading(true);
        const messageData = await getConversationMessages(user.id, hostId, bookingId);
        setMessages(messageData);
      } catch (error) {
        console.error('Error loading messages:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [user, hostId, bookingId, showMessages]);

  // Send message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || sending) return;

    try {
      setSending(true);

      const messageData = await sendMessage(
        user.id,
        hostId,
        newMessage.trim(),
        bookingId,
        `${user.first_name || ''} ${user.last_name || ''}`.trim(),
        venueName
      );

      setMessages(prev => [...prev, messageData]);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  // Quick message templates
  const getQuickMessages = () => {
    if (mode === 'inquiry') {
      return [
        "Hi! I'm interested in booking your venue. Is it available for my event?",
        "Could you tell me more about the amenities and what's included?",
        "What are your policies regarding decorations and setup time?",
        "Do you have any availability for a different date?"
      ];
    } else if (mode === 'booking') {
      return [
        "Hi! I have a confirmed booking. Could we discuss the event details?",
        "What time can we start setting up on the day?",
        "Are there any specific rules or guidelines I should know about?",
        "Could you provide your contact details for the event day?"
      ];
    }
    return [];
  };

  const quickMessages = getQuickMessages();

  // Compact mode for venue detail pages
  if (mode === 'compact') {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900 flex items-center">
            <MessageCircle className="w-4 h-4 mr-2 text-purple-600" />
            Message Host
          </h4>
          <div className="flex items-center text-sm text-gray-600">
            <User className="w-4 h-4 mr-1" />
            {hostName}
          </div>
        </div>

        <div className="space-y-3">
          <textarea
            placeholder={`Ask ${hostName} about this venue...`}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
            rows={3}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
          />

          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-500">
              Send a message to inquire about availability and details
            </p>
            <button
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || sending || !user}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm"
            >
              <Send className="w-4 h-4 mr-1" />
              {sending ? 'Sending...' : 'Send'}
            </button>
          </div>

          {!user && (
            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
              Please log in to message the host
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <MessageCircle className="w-4 h-4 text-purple-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">
                {mode === 'inquiry' ? 'Contact Host' : 'Message Host'}
              </h3>
              <p className="text-sm text-gray-600 flex items-center">
                <User className="w-3 h-3 mr-1" />
                {hostName}
              </p>
            </div>
          </div>

          {!showMessages && (
            <button
              onClick={() => setShowMessages(true)}
              className="text-purple-600 hover:text-purple-700 text-sm font-medium"
            >
              {messages.length > 0 ? `View Messages (${messages.length})` : 'Start Conversation'}
            </button>
          )}
        </div>

        {/* Booking Info */}
        <div className="mt-3 flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center">
            <MapPin className="w-3 h-3 mr-1" />
            {venueName}
          </div>
          {bookingDate && (
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              {new Date(bookingDate).toLocaleDateString()}
            </div>
          )}
          {bookingStatus && (
            <span className={`px-2 py-1 rounded-full text-xs ${
              bookingStatus === 'confirmed' ? 'bg-green-100 text-green-800' :
              bookingStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {bookingStatus}
            </span>
          )}
        </div>
      </div>

      {/* Messages or Quick Actions */}
      {showMessages ? (
        <div className="h-96 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {loading ? (
              <div className="text-center text-gray-500">Loading messages...</div>
            ) : messages.length === 0 ? (
              <div className="text-center text-gray-500">
                <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No messages yet. Start the conversation!</p>
              </div>
            ) : (
              messages.map((message) => {
                const isCurrentUser = message.sender_id === user?.id;
                return (
                  <div
                    key={message.id}
                    className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        isCurrentUser
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="whitespace-pre-wrap break-words">{message.content}</p>
                      <div className={`flex items-center justify-end mt-1 text-xs ${
                        isCurrentUser ? 'text-purple-200' : 'text-gray-500'
                      }`}>
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder={`Message ${hostName}...`}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                disabled={sending || !user}
              />
              <button
                onClick={handleSendMessage}
                disabled={!newMessage.trim() || sending || !user}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>

            {!user && (
              <p className="text-sm text-amber-600 mt-2">
                Please log in to send messages
              </p>
            )}
          </div>
        </div>
      ) : (
        <div className="p-4">
          {/* Quick Message Templates */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Quick Messages:</h4>
            {quickMessages.map((template, index) => (
              <button
                key={index}
                onClick={() => {
                  setNewMessage(template);
                  setShowMessages(true);
                }}
                className="w-full text-left p-3 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                disabled={!user}
              >
                <p className="text-sm text-gray-700">{template}</p>
              </button>
            ))}

            <button
              onClick={() => setShowMessages(true)}
              className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-purple-400 hover:text-purple-600 transition-colors"
              disabled={!user}
            >
              + Write custom message
            </button>

            {!user && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <p className="text-sm text-amber-800">
                  Please log in to message the host about this venue
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
