# Redirect Chain Analysis Report

Generated on: 2025-06-21T03:28:19.880Z

## Current Redirects Configuration

Found 12 redirects in the configuration.

### Potential Redirect Chains

No redirect chains found. Good job!

### Redirects to index.html

Found 7 redirects to index.html. This is generally fine for SPA routing but make sure they're actually intended:

- `/*` → `/index.html` (status: 200)
- `/nsw-curfew-zoning` → `/index.html` (status: 200)
- `/nsw-party-planning` → `/index.html` (status: 200)
- `/nsw-party-planning-updated` → `/index.html` (status: 200)
- `/nsw-address-v2` → `/index.html` (status: 200)
- `/precise-party-planning` → `/index.html` (status: 200)
- `/precise-address` → `/index.html` (status: 200)

Recommendation: Ensure these redirects are intended for client-side routing and not accidental.

## Suggested Optimized Configuration

Replace your current redirects with this optimized configuration:

```toml
[[redirects]]
  from = "/sign-up"
  to = "/sign-up.html"
  status = 200

[[redirects]]
  from = "/sign-in"
  to = "/sign-in.html"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/nsw-curfew-zoning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-party-planning-updated"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/nsw-address-v2"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-party-planning"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/precise-address"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/api/stripe/payment-intent"
  to = "/.netlify/functions/create-payment-intent"
  status = 200

[[redirects]]
  from = "/api/stripe/create-customer"
  to = "/.netlify/functions/create-customer"
  status = 200

[[redirects]]
  from = "/api/stripe/webhook"
  to = "/.netlify/functions/stripe-webhook"
  status = 200

```

## Next Steps

1. Review the suggested optimized configuration
2. Update your `netlify.toml` file with the optimized redirects
3. Deploy the changes
4. Test the fixed redirects
5. Submit affected URLs for reindexing in Google Search Console
