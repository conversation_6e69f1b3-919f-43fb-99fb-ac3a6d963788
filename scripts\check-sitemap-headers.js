/**
 * Check Sitemap Headers Script
 * 
 * This script checks the HTTP headers of the sitemap files to ensure they're being served correctly.
 */

import fetch from 'node-fetch';

const BASE_URL = 'https://housegoing.com.au';
const SITEMAP_PATHS = [
  '/sitemap.xml',
  '/sitemap_index.xml',
  '/sitemap_main.xml'
];

async function checkHeaders(url) {
  try {
    console.log(`Checking headers for ${url}...`);
    
    // Make a HEAD request to check headers without downloading the content
    const response = await fetch(url, { method: 'HEAD' });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    // Get all headers
    const headers = response.headers;
    console.log('Headers:');
    headers.forEach((value, name) => {
      console.log(`  ${name}: ${value}`);
    });
    
    // Check content type specifically
    const contentType = headers.get('content-type');
    if (contentType && contentType.includes('application/xml')) {
      console.log('✅ Content-Type is correctly set to application/xml');
    } else {
      console.log(`❌ Content-Type is not correctly set: ${contentType}`);
    }
    
    console.log('-----------------------------------');
    
    return {
      url,
      status: response.status,
      contentType,
      isValid: contentType && contentType.includes('application/xml')
    };
  } catch (error) {
    console.error(`Error checking ${url}:`, error.message);
    return {
      url,
      status: 'Error',
      contentType: null,
      isValid: false,
      error: error.message
    };
  }
}

async function main() {
  console.log('Checking sitemap headers...\n');
  
  const results = [];
  
  for (const path of SITEMAP_PATHS) {
    const url = `${BASE_URL}${path}`;
    const result = await checkHeaders(url);
    results.push(result);
  }
  
  // Summary
  console.log('\nSummary:');
  const allValid = results.every(r => r.isValid);
  
  if (allValid) {
    console.log('✅ All sitemap files have correct Content-Type headers');
  } else {
    console.log('❌ Some sitemap files have incorrect Content-Type headers:');
    results.filter(r => !r.isValid).forEach(r => {
      console.log(`  - ${r.url}: ${r.contentType || r.error}`);
    });
  }
}

main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
