@import url('https://rsms.me/inter/inter.css');
@import url('https://api.fontshare.com/v2/css?f[]=clash-display@600,700&display=swap');
@import './styles/clerk-overrides.css';
@import './styles/clerk-complete-override.css'; /* New comprehensive overrides */
@import './styles/search-overrides.css';
@import './styles/utilities.css';
@import './styles/booking-overrides.css';
@import './styles/mobile-optimizations.css'; /* Mobile-first optimizations */

@tailwind base;
@tailwind components;
@tailwind utilities;

.search-control {
  padding: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.2);
  width: 300px;
}

.search-control input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 5px;
}

.search-control button {
  width: 100%;
  padding: 8px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-control button:hover {
  background-color: #2563eb;
}

@layer base {
  html {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply font-sans text-gray-900 leading-relaxed;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display tracking-tight text-gray-900 leading-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold;
  }

  h3 {
    @apply text-xl md:text-2xl font-semibold;
  }

  h4 {
    @apply text-lg md:text-xl font-semibold;
  }

  p {
    @apply text-base text-gray-600 leading-relaxed;
  }

  .text-large {
    @apply text-lg md:text-xl leading-relaxed;
  }

  .text-small {
    @apply text-sm leading-relaxed;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply btn bg-purple-600 text-white shadow-sm hover:bg-purple-700 hover:shadow-md active:scale-95;
  }

  .btn-secondary {
    @apply btn bg-white text-gray-700 border border-gray-300 shadow-sm hover:border-purple-300 hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100;
  }

  .input {
    @apply w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200;
  }

  /* Consistent spacing utilities */
  .section-padding {
    @apply py-12 px-4 sm:px-6;
  }

  .section-padding-sm {
    @apply py-8 px-4 sm:px-6;
  }

  .section-padding-xs {
    @apply py-6 px-4 sm:px-6;
  }

  .container-width {
    @apply max-w-7xl mx-auto;
  }

  .container-width-sm {
    @apply max-w-6xl mx-auto;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: theme('colors.gray.100');
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: theme('colors.gray.300');
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.400');
}
