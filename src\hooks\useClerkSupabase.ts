import { useSupabase } from '../providers/SupabaseProvider';

/**
 * Official Clerk + Supabase Integration Hook
 * 
 * This hook provides a clean way to access the Supabase client
 * that is managed by the SupabaseProvider.
 * 
 * Usage:
 * ```tsx
 * import { useClerkSupabase } from '@/hooks/useClerkSupabase';
 * 
 * function MyComponent() {
 *   const { supabase, isReady } = useClerkSupabase();
 *   
 *   useEffect(() => {
 *     if (isReady) {
 *       // Use supabase client with Clerk authentication
 *       supabase.from('tasks').select('*').then(console.log);
 *     }
 *   }, [isReady, supabase]);
 * }
 * ```
 */
export function useClerkSupabase() {
  const { supabase, isLoading, isAuthenticated } = useSupabase();

  return {
    supabase,
    isReady: !isLoading && isAuthenticated,
    isLoading,
  };
}
