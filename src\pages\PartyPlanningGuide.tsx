import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import { MapPin, Users, Clock, Shield, DollarSign, Star, CheckCircle, AlertTriangle, Calendar, Home } from 'lucide-react';

export default function PartyPlanningGuide() {
  return (
    <>
      <Helmet>
        <title>Ultimate Party Planning Guide Australia 2024 | Venue Booking Tips | HouseGoing</title>
        <meta 
          name="description" 
          content="Complete party planning guide for Australia. Learn about venue booking, noise restrictions, permits, insurance, and costs. Expert tips for successful events in NSW, VIC, QLD." 
        />
        <meta 
          name="keywords" 
          content="party planning guide Australia, venue booking tips, party permits Australia, event planning NSW, party insurance, noise restrictions, venue rental guide" 
        />
        <meta property="og:title" content="Ultimate Party Planning Guide Australia 2024 | HouseGoing" />
        <meta 
          property="og:description" 
          content="Everything you need to know about planning parties in Australia. From venue selection to legal requirements and cost management." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://housegoing.com.au/party-planning-guide" />
        <link rel="canonical" href="https://housegoing.com.au/party-planning-guide" />
      </Helmet>
      
      <div className="pt-32 px-4 sm:px-6 max-w-6xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Ultimate Party Planning Guide for Australia
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            Everything you need to know about planning successful parties in Australia. From finding the perfect venue to understanding legal requirements, insurance, and managing costs.
          </p>
        </div>

        {/* Quick Navigation */}
        <div className="bg-purple-50 rounded-lg p-6 mb-12">
          <h2 className="text-xl font-semibold mb-4 text-center">Quick Navigation</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <a href="#venue-types" className="text-purple-700 hover:text-purple-900 font-medium">Venue Types</a>
            <a href="#legal-requirements" className="text-purple-700 hover:text-purple-900 font-medium">Legal Requirements</a>
            <a href="#cost-planning" className="text-purple-700 hover:text-purple-900 font-medium">Cost Planning</a>
            <a href="#tips-tricks" className="text-purple-700 hover:text-purple-900 font-medium">Tips & Tricks</a>
          </div>
        </div>

        {/* Section 1: Types of Party Venues */}
        <section id="venue-types" className="mb-16">
          <div className="flex items-center mb-8">
            <MapPin className="h-8 w-8 text-purple-600 mr-4" />
            <h2 className="text-3xl font-bold text-gray-900">Types of Party Venues in Australia</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <Home className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="text-xl font-semibold">Residential Properties</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Private homes, apartments, and townhouses perfect for intimate gatherings and family celebrations.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Capacity:</span>
                  <span className="font-medium">10-50 guests</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Average cost:</span>
                  <span className="font-medium">$200-800/day</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Best for:</span>
                  <span className="font-medium">Birthdays, anniversaries</span>
                </div>
              </div>
            </div>
            
            <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <Users className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="text-xl font-semibold">Function Centres</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Purpose-built venues with professional facilities, catering kitchens, and event equipment.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Capacity:</span>
                  <span className="font-medium">50-300 guests</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Average cost:</span>
                  <span className="font-medium">$800-3000/day</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Best for:</span>
                  <span className="font-medium">Weddings, corporate events</span>
                </div>
              </div>
            </div>
            
            <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <Star className="h-6 w-6 text-purple-600 mr-3" />
                <h3 className="text-xl font-semibold">Unique Venues</h3>
              </div>
              <p className="text-gray-600 mb-4">
                Warehouses, rooftops, boats, and other distinctive spaces for memorable events.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Capacity:</span>
                  <span className="font-medium">20-500 guests</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Average cost:</span>
                  <span className="font-medium">$500-5000/day</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Best for:</span>
                  <span className="font-medium">Special occasions</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 2: Legal Requirements */}
        <section id="legal-requirements" className="mb-16">
          <div className="flex items-center mb-8">
            <Shield className="h-8 w-8 text-purple-600 mr-4" />
            <h2 className="text-3xl font-bold text-gray-900">Legal Requirements & Permits</h2>
          </div>
          
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-6 mb-8">
            <div className="flex items-center mb-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
              <h3 className="text-lg font-semibold text-yellow-800">Important Legal Notice</h3>
            </div>
            <p className="text-yellow-700">
              Regulations vary by state and local council. Always check with your local authorities for specific requirements before hosting an event.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-green-700">✅ When Permits Are Required</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Events with more than 100 guests</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Outdoor events in public spaces</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Events serving alcohol commercially</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Events with amplified music after curfew</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Events requiring road closures</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 text-blue-700">📋 Required Documentation</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">For Event Hosts:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Public liability insurance (minimum $10M)</li>
                    <li>• Property ownership or rental agreement</li>
                    <li>• Council approval (if required)</li>
                    <li>• Noise management plan</li>
                    <li>• Emergency contact information</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">For Guests:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Valid identification</li>
                    <li>• Event details and guest list</li>
                    <li>• Emergency contact information</li>
                    <li>• Damage deposit (if required)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 3: Cost Planning */}
        <section id="cost-planning" className="mb-16">
          <div className="flex items-center mb-8">
            <DollarSign className="h-8 w-8 text-purple-600 mr-4" />
            <h2 className="text-3xl font-bold text-gray-900">Understanding Party Costs in Australia</h2>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Venue Costs by Location</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Sydney CBD</span>
                  <span className="text-gray-600">$150-500/hour</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Melbourne CBD</span>
                  <span className="text-gray-600">$120-450/hour</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Brisbane CBD</span>
                  <span className="text-gray-600">$100-350/hour</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Inner Suburbs</span>
                  <span className="text-gray-600">$80-250/hour</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Outer Suburbs</span>
                  <span className="text-gray-600">$50-150/hour</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="font-medium">Regional Areas</span>
                  <span className="text-gray-600">$30-100/hour</span>
                </div>
              </div>
            </div>
            
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Additional Costs to Consider</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Cleaning Fee</span>
                  <span className="text-gray-600">$50-200</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Security Deposit</span>
                  <span className="text-gray-600">$200-1000</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Service Fees</span>
                  <span className="text-gray-600">5-15%</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Insurance</span>
                  <span className="text-gray-600">$20-100/event</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="font-medium">Equipment Rental</span>
                  <span className="text-gray-600">$100-500</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="font-medium">Catering</span>
                  <span className="text-gray-600">$20-80/person</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 4: Tips & Tricks */}
        <section id="tips-tricks" className="mb-16">
          <div className="flex items-center mb-8">
            <Star className="h-8 w-8 text-purple-600 mr-4" />
            <h2 className="text-3xl font-bold text-gray-900">Expert Tips for Successful Parties</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <Clock className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="text-lg font-semibold">Perfect Timing</h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-700">
                <li>• Book venues 4-8 weeks in advance</li>
                <li>• Start events earlier to end before curfew</li>
                <li>• Consider afternoon parties for families</li>
                <li>• Check for local events that might conflict</li>
                <li>• Weekend events have more flexibility</li>
              </ul>
            </div>
            
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <Users className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="text-lg font-semibold">Guest Management</h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-700">
                <li>• Send invitations 2-3 weeks early</li>
                <li>• Request RSVPs for accurate planning</li>
                <li>• Plan for 10-15% more guests than expected</li>
                <li>• Provide clear directions and parking info</li>
                <li>• Set clear start and end times</li>
              </ul>
            </div>
            
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <Shield className="h-6 w-6 text-purple-600 mr-3" />
                <h3 className="text-lg font-semibold">Risk Management</h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-700">
                <li>• Always have public liability insurance</li>
                <li>• Inform neighbors about your event</li>
                <li>• Have a backup plan for weather</li>
                <li>• Keep emergency contact numbers handy</li>
                <li>• Monitor noise levels throughout</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8 rounded-lg text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Plan Your Perfect Party?</h2>
          <p className="text-xl mb-6">Browse party venues across NSW and connect directly with hosts.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/find-venues" 
              className="inline-block bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Browse Venues Now
            </Link>
            <Link 
              to="/nsw-party-planning" 
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors"
            >
              Check NSW Noise Rules
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
