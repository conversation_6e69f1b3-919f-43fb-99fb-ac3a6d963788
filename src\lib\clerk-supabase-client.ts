import { useSession } from '@clerk/clerk-react';
import { createClient } from '@supabase/supabase-js';

// Utility hook to get a Supabase client with Clerk session token
type ClerkSupabaseClientOptions = {
  supabaseUrl?: string;
  supabaseKey?: string;
};

export function useClerkSupabaseClient(options?: ClerkSupabaseClientOptions) {
  const { session } = useSession();
  const supabaseUrl = options?.supabaseUrl || import.meta.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = options?.supabaseKey || import.meta.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Supabase URL and Key are required');
  }

  return createClient(
    supabaseUrl,
    supabaseKey,
    {
      global: {
        headers: {
          Authorization: `Bearer ${session?.getToken() ?? ''}`,
        },
      },
    }
  );
}
