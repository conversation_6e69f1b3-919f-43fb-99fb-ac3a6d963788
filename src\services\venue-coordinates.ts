/**
 * Comprehensive venue coordinate management service
 * Ensures all venues have accurate coordinates for location-based search
 */

import { searchNSWSuburbs } from './nsw-suburbs-search';
import { geocodeLocation } from './geocoding';

export interface VenueCoordinates {
  latitude: number;
  longitude: number;
  source: 'nsw_suburbs' | 'geocoding' | 'manual' | 'fallback';
  accuracy: 'exact' | 'suburb' | 'approximate' | 'fallback';
}

export interface VenueLocation {
  address: string;
  suburb: string;
  postcode: string;
  state: string;
  coordinates?: VenueCoordinates;
}

/**
 * Automatically assign coordinates to a venue based on its location
 * This is the main function that should be called for all new venues
 */
export async function assignVenueCoordinates(location: VenueLocation): Promise<VenueCoordinates> {
  console.log(`🎯 Assigning coordinates for venue in ${location.suburb}, ${location.state}`);

  // Strategy 1: Try NSW suburbs search first (most accurate for NSW locations)
  try {
    const suburbResults = await searchNSWSuburbs(location.suburb, 1);
    if (suburbResults.length > 0 && suburbResults[0].lat && suburbResults[0].lng) {
      const result = suburbResults[0];
      console.log(`✅ Found coordinates via NSW suburbs: ${result.lat}, ${result.lng}`);
      
      return {
        latitude: result.lat,
        longitude: result.lng,
        source: 'nsw_suburbs',
        accuracy: 'exact'
      };
    }
  } catch (error) {
    console.warn('NSW suburbs search failed:', error);
  }

  // Strategy 2: Try geocoding the full address
  try {
    const geocodeResult = await geocodeLocation(location.address);
    console.log(`✅ Found coordinates via geocoding: ${geocodeResult.lat}, ${geocodeResult.lng}`);
    
    return {
      latitude: geocodeResult.lat,
      longitude: geocodeResult.lng,
      source: 'geocoding',
      accuracy: 'approximate'
    };
  } catch (error) {
    console.warn('Geocoding failed:', error);
  }

  // Strategy 3: Try geocoding just the suburb
  try {
    const suburbAddress = `${location.suburb}, ${location.state}, Australia`;
    const geocodeResult = await geocodeLocation(suburbAddress);
    console.log(`✅ Found coordinates via suburb geocoding: ${geocodeResult.lat}, ${geocodeResult.lng}`);
    
    return {
      latitude: geocodeResult.lat,
      longitude: geocodeResult.lng,
      source: 'geocoding',
      accuracy: 'suburb'
    };
  } catch (error) {
    console.warn('Suburb geocoding failed:', error);
  }

  // Strategy 4: Use hardcoded fallback coordinates for known suburbs
  const fallbackCoords = getFallbackCoordinatesForSuburb(location.suburb);
  if (fallbackCoords) {
    console.log(`⚠️ Using fallback coordinates for ${location.suburb}: ${fallbackCoords.latitude}, ${fallbackCoords.longitude}`);
    
    return {
      latitude: fallbackCoords.latitude,
      longitude: fallbackCoords.longitude,
      source: 'fallback',
      accuracy: 'fallback'
    };
  }

  // Strategy 5: Default to Sydney CBD as last resort
  console.error(`❌ Could not find coordinates for ${location.suburb}, using Sydney CBD as fallback`);
  
  return {
    latitude: -33.8688,
    longitude: 151.2093,
    source: 'fallback',
    accuracy: 'fallback'
  };
}

/**
 * Validate and fix existing venue coordinates
 * Use this to audit and fix existing venues in the database
 */
export async function validateVenueCoordinates(
  venue: { id: string; location: VenueLocation }
): Promise<{
  isValid: boolean;
  currentCoordinates?: VenueCoordinates;
  suggestedCoordinates?: VenueCoordinates;
  needsUpdate: boolean;
}> {
  const current = venue.location.coordinates;
  
  // If no coordinates exist, definitely needs update
  if (!current) {
    const suggested = await assignVenueCoordinates(venue.location);
    return {
      isValid: false,
      suggestedCoordinates: suggested,
      needsUpdate: true
    };
  }

  // Check if coordinates are reasonable for NSW
  const isInNSW = (
    current.latitude >= -37.5 && current.latitude <= -28.0 &&
    current.longitude >= 140.0 && current.longitude <= 154.0
  );

  if (!isInNSW) {
    console.warn(`⚠️ Venue ${venue.id} has coordinates outside NSW: ${current.latitude}, ${current.longitude}`);
    const suggested = await assignVenueCoordinates(venue.location);
    return {
      isValid: false,
      currentCoordinates: current,
      suggestedCoordinates: suggested,
      needsUpdate: true
    };
  }

  // For venues with fallback accuracy, try to get better coordinates
  if (current.accuracy === 'fallback') {
    const suggested = await assignVenueCoordinates(venue.location);
    if (suggested.accuracy !== 'fallback') {
      return {
        isValid: true,
        currentCoordinates: current,
        suggestedCoordinates: suggested,
        needsUpdate: true
      };
    }
  }

  return {
    isValid: true,
    currentCoordinates: current,
    needsUpdate: false
  };
}

/**
 * Batch update coordinates for multiple venues
 * Useful for fixing existing venue data
 */
export async function batchUpdateVenueCoordinates(
  venues: Array<{ id: string; location: VenueLocation }>
): Promise<{
  updated: Array<{ id: string; coordinates: VenueCoordinates }>;
  failed: Array<{ id: string; error: string }>;
}> {
  const updated: Array<{ id: string; coordinates: VenueCoordinates }> = [];
  const failed: Array<{ id: string; error: string }> = [];

  for (const venue of venues) {
    try {
      const validation = await validateVenueCoordinates(venue);
      if (validation.needsUpdate && validation.suggestedCoordinates) {
        updated.push({
          id: venue.id,
          coordinates: validation.suggestedCoordinates
        });
      }
    } catch (error) {
      failed.push({
        id: venue.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  return { updated, failed };
}

/**
 * Fallback coordinates for major NSW suburbs
 * These are manually curated for accuracy
 */
function getFallbackCoordinatesForSuburb(suburb: string): { latitude: number; longitude: number } | null {
  const fallbackMap: Record<string, { latitude: number; longitude: number }> = {
    // Sydney Metro
    'sydney': { latitude: -33.8688, longitude: 151.2093 },
    'bondi beach': { latitude: -33.8915, longitude: 151.2767 },
    'newtown': { latitude: -33.8978, longitude: 151.1795 },
    'parramatta': { latitude: -33.8150, longitude: 151.0011 },
    'chatswood': { latitude: -33.7967, longitude: 151.1800 },
    'cronulla': { latitude: -34.0581, longitude: 151.1543 },
    'ryde': { latitude: -33.8149, longitude: 151.1056 },
    'penrith': { latitude: -33.7506, longitude: 150.6944 },
    'manly': { latitude: -33.7969, longitude: 151.2502 },
    'surry hills': { latitude: -33.8886, longitude: 151.2094 },
    
    // North Shore
    'north sydney': { latitude: -33.8403, longitude: 151.2065 },
    'hornsby': { latitude: -33.7047, longitude: 151.0993 },
    
    // Eastern Suburbs
    'coogee': { latitude: -33.9208, longitude: 151.2559 },
    'maroubra': { latitude: -33.9506, longitude: 151.2364 },
    'double bay': { latitude: -33.8774, longitude: 151.2408 },
    
    // Inner West
    'marrickville': { latitude: -33.9115, longitude: 151.1555 },
    'leichhardt': { latitude: -33.8836, longitude: 151.1561 },
    
    // Western Sydney
    'blacktown': { latitude: -33.7681, longitude: 150.9072 },
    'liverpool': { latitude: -33.9213, longitude: 150.9218 },
    'campbelltown': { latitude: -34.0639, longitude: 150.8150 },
    
    // Regional NSW
    'newcastle': { latitude: -32.9267, longitude: 151.7789 },
    'wollongong': { latitude: -34.4278, longitude: 150.8931 },
    'central coast': { latitude: -33.4269, longitude: 151.3428 },
    'blue mountains': { latitude: -33.7122, longitude: 150.3111 },
    'byron bay': { latitude: -28.6474, longitude: 153.6020 }
  };

  const key = suburb.toLowerCase().trim();
  return fallbackMap[key] || null;
}

/**
 * Extract suburb from address string
 * Handles various address formats
 */
export function extractSuburbFromAddress(address: string): string | null {
  // Common patterns for NSW addresses
  const patterns = [
    // "123 Main St, Newtown NSW 2042"
    /,\s*([A-Za-z\s]+)\s+NSW\s+\d{4}/,
    // "123 Main St, Newtown, NSW 2042"
    /,\s*([A-Za-z\s]+),\s*NSW\s+\d{4}/,
    // "Newtown NSW 2042"
    /^([A-Za-z\s]+)\s+NSW\s+\d{4}/,
    // "Newtown, NSW"
    /^([A-Za-z\s]+),\s*NSW/
  ];

  for (const pattern of patterns) {
    const match = address.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }

  return null;
}

/**
 * Test function for coordinate assignment
 * Available in browser console as window.testCoordinateAssignment()
 */
export async function testCoordinateAssignment() {
  console.log('🧪 Testing coordinate assignment system...');
  
  const testVenues = [
    { suburb: 'Parramatta', address: '88 Macquarie Street, Parramatta NSW 2150' },
    { suburb: 'Chatswood', address: '234 Victoria Avenue, Chatswood NSW 2067' },
    { suburb: 'Cronulla', address: '67 Kingsway, Cronulla NSW 2230' },
    { suburb: 'Unknown Suburb', address: '123 Test Street, Unknown Suburb NSW 9999' }
  ];

  for (const venue of testVenues) {
    console.log(`\n🔍 Testing ${venue.suburb}...`);
    try {
      const coordinates = await assignVenueCoordinates({
        address: venue.address,
        suburb: venue.suburb,
        postcode: '2000',
        state: 'NSW'
      });
      
      console.log(`✅ ${venue.suburb}: ${coordinates.latitude}, ${coordinates.longitude}`);
      console.log(`   Source: ${coordinates.source}, Accuracy: ${coordinates.accuracy}`);
    } catch (error) {
      console.error(`❌ ${venue.suburb}: Failed -`, error);
    }
  }
}

// Make test function available globally
if (typeof window !== 'undefined') {
  (window as any).testCoordinateAssignment = testCoordinateAssignment;
}
