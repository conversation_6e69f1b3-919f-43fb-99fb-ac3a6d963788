import dotenv from 'dotenv';
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { PromptTemplate } from "@langchain/core/prompts";
import { LLMChain } from "langchain/chains";
import { <PERSON>uffer<PERSON><PERSON><PERSON> } from "langchain/memory";

// Load environment variables
dotenv.config();

// Configure <PERSON>Smith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;

// Initialize the Hugging Face model
const model = new HuggingFaceInference({
  model: "mistralai/Mistral-7B-Instruct-v0.3", // Updated to the latest version
  apiKey: process.env.HUGGINGFACE_API_KEY,
  temperature: 0.7,
  maxTokens: 1024,
});

// Create a prompt template for the host acquisition agent
const promptTemplate = PromptTemplate.fromTemplate(`
You are Alex, the AI host acquisition specialist for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.

Your goal is to help potential hosts successfully list their venues on HouseGoing.
The user has signed up to our host enquiry list and needs guidance.

GUIDELINES:
- Be warm and welcoming but not overly casual
- Be enthusiastic about helping hosts find success
- Be confident and knowledgeable about venue options
- Use Australian-friendly language with occasional local phrases
- Keep initial greetings to 1-2 sentences
- Format lists with bullet points for easy scanning
- Bold important information
- Personalize responses by referencing specific venue types
- After identifying key details, share a brief benefit specific to that venue type
- Occasionally mention trending venue types or features in their area

CONVERSATION HISTORY:
{chat_history}

Human: {input}
AI: `);

// Set up memory to store conversation history
const memory = new BufferMemory({
  memoryKey: "chat_history",
  returnMessages: true,
});

// Create the LangChain
const chain = new LLMChain({
  llm: model,
  prompt: promptTemplate,
  memory: memory,
});

// Function to generate a response from the AI agent
async function generateResponse(userInput) {
  try {
    console.log("Generating response for:", userInput);
    const response = await chain.call({ input: userInput });
    console.log("Response generated successfully");
    return response.text;
  } catch (error) {
    console.error("Error generating response:", error);
    return "I'm sorry, I encountered an error while processing your request. Please try again.";
  }
}

// Interactive console interface for testing
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log("HouseGoing AI Host Acquisition Agent");
console.log("===================================");
console.log("Type 'exit' to quit the conversation");
console.log("");

function askQuestion() {
  rl.question("You: ", async (input) => {
    if (input.toLowerCase() === 'exit') {
      console.log("Goodbye!");
      rl.close();
      return;
    }

    console.log("AI is thinking...");
    const response = await generateResponse(input);
    console.log("\nAlex: " + response + "\n");

    askQuestion();
  });
}

// Start the conversation
askQuestion();
