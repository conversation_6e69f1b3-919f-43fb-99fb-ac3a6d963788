import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import { MapPin, Clock, Shield, DollarSign, Users, Star, Calendar, Home, AlertTriangle, CheckCircle } from 'lucide-react';

export default function VenueGuideHub() {
  const guideCategories = [
    {
      title: "NSW Party Planning & Regulations",
      description: "Complete guide to noise laws, curfew times, and legal requirements for events in NSW",
      icon: Clock,
      link: "/nsw-party-planning",
      topics: [
        "Noise curfew times by council area",
        "Property type restrictions",
        "Permit requirements",
        "Penalty information"
      ]
    },
    {
      title: "Venue Types & Selection",
      description: "Understanding different venue types and choosing the right space for your event",
      icon: Home,
      link: "/party-planning-guide#venue-types",
      topics: [
        "Residential properties vs function centres",
        "Capacity planning",
        "Amenity requirements",
        "Location considerations"
      ]
    },
    {
      title: "Booking Process & Requirements",
      description: "Step-by-step guide to booking venues and understanding requirements",
      icon: CheckCircle,
      link: "/how-it-works",
      topics: [
        "Documentation needed",
        "Insurance requirements",
        "Security deposits",
        "Cancellation policies"
      ]
    },
    {
      title: "Pricing & Cost Management",
      description: "Understanding venue costs and managing your event budget effectively",
      icon: DollarSign,
      link: "/party-planning-guide#cost-planning",
      topics: [
        "Hourly rates by location",
        "Additional fees and charges",
        "Seasonal pricing variations",
        "Budget planning tips"
      ]
    },
    {
      title: "Safety & Legal Compliance",
      description: "Ensuring your event meets safety standards and legal requirements",
      icon: Shield,
      link: "/safety",
      topics: [
        "Public liability insurance",
        "Safety equipment requirements",
        "Emergency procedures",
        "Host responsibilities"
      ]
    },
    {
      title: "Event Planning Best Practices",
      description: "Expert tips for planning successful events and managing guests",
      icon: Star,
      link: "/party-planning-guide#tips-tricks",
      topics: [
        "Timeline planning",
        "Guest management",
        "Noise control strategies",
        "Neighbor relations"
      ]
    }
  ];

  const popularLocations = [
    { name: "Sydney CBD", venues: 45, avgPrice: "$300/hour" },
    { name: "Eastern Suburbs", venues: 32, avgPrice: "$250/hour" },
    { name: "Inner West", venues: 28, avgPrice: "$200/hour" },
    { name: "Northern Beaches", venues: 24, avgPrice: "$280/hour" },
    { name: "Central Coast", venues: 18, avgPrice: "$150/hour" },
    { name: "Newcastle", venues: 15, avgPrice: "$120/hour" },
    { name: "Wollongong", venues: 12, avgPrice: "$100/hour" },
    { name: "Blue Mountains", venues: 8, avgPrice: "$180/hour" }
  ];

  return (
    <>
      <Helmet>
        <title>Complete Venue Guide Hub | Party Planning Resources NSW | HouseGoing</title>
        <meta 
          name="description" 
          content="Comprehensive venue guide covering NSW party planning, regulations, booking process, pricing, and safety requirements. Everything you need for successful event planning." 
        />
        <meta 
          name="keywords" 
          content="venue guide NSW, party planning guide, venue booking help, NSW noise regulations, event planning resources, venue selection guide" 
        />
        <meta property="og:title" content="Complete Venue Guide Hub | HouseGoing" />
        <meta 
          property="og:description" 
          content="Your complete resource for venue booking and party planning in NSW. Expert guides, regulations, and tips." 
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://housegoing.com.au/venue-guide" />
        <link rel="canonical" href="https://housegoing.com.au/venue-guide" />
      </Helmet>
      
      <div className="pt-32 px-4 sm:px-6 max-w-6xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Complete Venue & Party Planning Guide
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto mb-8">
            Everything you need to know about finding, booking, and hosting successful events in NSW. 
            From understanding noise regulations to choosing the perfect venue for your celebration.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium">
              <MapPin className="h-4 w-4 inline mr-2" />
              NSW Specific Information
            </div>
            <div className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
              <Clock className="h-4 w-4 inline mr-2" />
              Updated December 2024
            </div>
            <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
              <CheckCircle className="h-4 w-4 inline mr-2" />
              Expert Verified
            </div>
          </div>
        </div>

        {/* Guide Categories */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Comprehensive Guides</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {guideCategories.map((category, index) => (
              <div key={index} className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="p-3 bg-purple-100 rounded-lg mr-4">
                    <category.icon className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">{category.title}</h3>
                </div>
                
                <p className="text-gray-600 mb-4">{category.description}</p>
                
                <ul className="space-y-2 mb-6">
                  {category.topics.map((topic, topicIndex) => (
                    <li key={topicIndex} className="text-sm text-gray-600 flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      {topic}
                    </li>
                  ))}
                </ul>
                
                <Link 
                  to={category.link}
                  className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium"
                >
                  Read Full Guide
                  <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            ))}
          </div>
        </section>

        {/* Popular Locations */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Popular Venue Locations in NSW</h2>
          <div className="bg-gray-50 rounded-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {popularLocations.map((location, index) => (
                <div key={index} className="bg-white rounded-lg p-4 text-center">
                  <h3 className="font-semibold text-gray-900 mb-2">{location.name}</h3>
                  <p className="text-sm text-gray-600 mb-1">{location.venues} venues available</p>
                  <p className="text-sm font-medium text-purple-600">{location.avgPrice}</p>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <Link 
                to="/find-venues"
                className="inline-flex items-center bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors"
              >
                <MapPin className="h-5 w-5 mr-2" />
                Browse All Venues
              </Link>
            </div>
          </div>
        </section>

        {/* Quick Links */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Quick Access Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link 
              to="/nsw-party-planning"
              className="bg-blue-50 border border-blue-200 rounded-lg p-6 hover:bg-blue-100 transition-colors"
            >
              <Clock className="h-8 w-8 text-blue-600 mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">NSW Noise Checker</h3>
              <p className="text-gray-600 text-sm">Check curfew times and noise restrictions for any NSW address</p>
            </Link>
            
            <Link 
              to="/venue-assistant"
              className="bg-green-50 border border-green-200 rounded-lg p-6 hover:bg-green-100 transition-colors"
            >
              <Users className="h-8 w-8 text-green-600 mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">Venue Assistant</h3>
              <p className="text-gray-600 text-sm">Get personalized venue recommendations based on your needs</p>
            </Link>
            
            <Link 
              to="/host"
              className="bg-purple-50 border border-purple-200 rounded-lg p-6 hover:bg-purple-100 transition-colors"
            >
              <Home className="h-8 w-8 text-purple-600 mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">List Your Venue</h3>
              <p className="text-gray-600 text-sm">Start earning by hosting events at your property</p>
            </Link>
          </div>
        </section>

        {/* FAQ Preview */}
        <section className="mb-16">
          <div className="bg-white border rounded-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">What are the noise restrictions in NSW?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Generally 10 PM - 7 AM weekdays, 11 PM - 8 AM weekends, but varies by council area and property type.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Do I need insurance for my event?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Public liability insurance is recommended for events with 100+ guests and may be required by some venues.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">How far in advance should I book?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Most venues require 48-72 hours notice, but popular venues may need 1-2 weeks advance booking.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">What's included in venue pricing?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Basic venue access is included. Additional costs may include cleaning, security deposits, and equipment rental.
                </p>
              </div>
            </div>
            
            <div className="text-center mt-6">
              <Link 
                to="/faq"
                className="text-purple-600 hover:text-purple-700 font-medium"
              >
                View All FAQs →
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8 rounded-lg text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Find Your Perfect Venue?</h2>
          <p className="text-xl mb-6">Use our comprehensive guides to plan your event with confidence</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/find-venues" 
              className="inline-block bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Start Searching Venues
            </Link>
            <Link 
              to="/nsw-party-planning" 
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors"
            >
              Check NSW Regulations
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
