/**
 * Clerk Authentication Configuration
 *
 * This file contains the configuration for Clerk authentication.
 * We use production keys for all environments to ensure consistent behavior.
 */

// Helper function to determine if we're in development mode
export const isDevelopmentMode = () => {
  // Only consider development mode if explicitly on localhost
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    return hostname === 'localhost' || hostname === '127.0.0.1';
  }

  // Server-side: only if NODE_ENV is explicitly development
  return process.env.NODE_ENV === 'development';
};

// Clerk API configuration
export const CLERK_CONFIG = {
  // API Keys - Use environment variables only
  publishableKey: import.meta.env.VITE_CLERK_PUBLISHABLE_KEY,

  // OAuth callback URLs
  oauthCallbackURL: '/auth/callback',
  ssoCallbackURL: '/sso-callback',
  googleOAuthCallbackURL: '/auth/callback',

  // Full URL for Google OAuth callback (including domain)
  googleOAuthFullCallbackURL: typeof window !== 'undefined'
    ? `${window.location.origin}/auth/callback`
    : 'https://housegoing.com.au/auth/callback',

  // Redirect URLs configuration
  redirectUrls: {
    signIn: '/',
    signUp: '/',
    afterSignIn: '/',
    afterSignUp: '/dashboard',
    hostSignIn: '/host/dashboard',
    hostSignUp: '/host/dashboard'
  },

  // Fallback URL for redirects
  fallbackRedirectUrl: '/',

  // Use development mode for localhost
  developmentMode: isDevelopmentMode(),

  // Clerk domain - using production domain
  clerkDomain: 'clerk.housegoing.com.au',

  // OAuth providers configuration
  oauth: {
    google: {
      strategy: 'oauth_google',
      scopes: ['email', 'profile'],
    }
  }
};

// Export a function to check if we're in development mode
export const isDevMode = () => isDevelopmentMode();

// Export a function to create a mock user for development
export const createMockUser = (type: 'host' | 'guest' = 'guest') => {
  if (!isDevelopmentMode()) return null;

  return {
    id: 'mock_user_' + type,
    email: type === 'host' ? '<EMAIL>' : '<EMAIL>',
    firstName: type === 'host' ? 'Hui' : 'Test',
    lastName: type === 'host' ? 'Man' : 'User',
    fullName: type === 'host' ? 'Hui Man' : 'Test User',
    primaryEmailAddress: {
      emailAddress: type === 'host' ? '<EMAIL>' : '<EMAIL>'
    },
    publicMetadata: {
      role: type === 'host' ? 'admin' : 'guest'
    }
  };
};

// Helper function to get the full URL including domain
export const getFullURL = (path: string): string => {
  const baseURL = typeof window !== 'undefined'
    ? window.location.origin
    : 'https://housegoing.netlify.app';

  return `${baseURL}${path}`;
};

// Get OAuth callback URLs for Clerk Dashboard configuration
export const getOAuthCallbackURLs = (): string[] => {
  // Base URLs for different environments
  const baseUrls = [
    window.location.origin,
    'https://housegoing.netlify.app',
    'https://housegoing.com.au',
    'https://www.housegoing.com.au',
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5177',
    'http://localhost:5178',
    'http://localhost:5179'
  ];

  // Callback paths
  const callbackPaths = [
    '/auth/callback',
    '/sso-callback',
    '/google-callback',
    '/oauth/google/callback',
    '/clerk/oauth/google/callback',
    '/auth/google/callback',
    '/callback/google',
    '/callback'
  ];

  // Parameter variations
  const paramVariations = [
    '',
    '?registrationType=guest',
    '?registrationType=host',
    '?destination=host-portal'
  ];

  // Generate all combinations
  const callbackUrls: string[] = [];

  baseUrls.forEach(baseUrl => {
    callbackPaths.forEach(path => {
      paramVariations.forEach(params => {
        callbackUrls.push(`${baseUrl}${path}${params}`);
      });
    });
  });

  // Add the current URL as well
  callbackUrls.push(getFullURL('/auth/callback'));

  // Return unique URLs
  return [...new Set(callbackUrls)];
};
