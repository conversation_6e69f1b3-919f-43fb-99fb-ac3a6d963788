/**
 * Diagnostic utility for Clerk-Supabase integration
 * 
 * Helps troubleshoot issues with the Clerk-Supabase integration, specifically
 * focusing on the "Loading authentication..." stuck state and TypeError issues.
 */

// Helper to check if Clerk is properly loaded
export function isClerkLoaded(): boolean {
  return typeof window !== 'undefined' && !!window.Clerk;
}

// Helper to check if we have a valid Clerk session
export function hasValidClerkSession(): boolean {
  return typeof window !== 'undefined' && 
    !!window.Clerk?.session && 
    !!window.Clerk.session.id;
}

// Helper to check if Supabase client exists
export function hasSupabaseClient(): boolean {
  // @ts-ignore
  return typeof window !== 'undefined' && !!window.__SUPABASE_CLIENT_INSTANCE;
}

// Get diagnostic information about the current state
export async function getDiagnosticInfo() {
  const info: any = {
    timestamp: new Date().toISOString(),
    clerk: {
      loaded: isClerkLoaded(),
      hasSession: hasValidClerkSession(),
      sessionId: typeof window !== 'undefined' && window.Clerk?.session?.id,
      userId: typeof window !== 'undefined' && window.Clerk?.user?.id,
    },
    supabase: {
      clientExists: hasSupabaseClient(),
      // @ts-ignore
      clientCount: typeof window !== 'undefined' ? window.__SUPABASE_CLIENT_COUNT || 0 : 0,
    },
    localStorage: {},
    environment: {
      isDev: import.meta.env.DEV,
      mode: import.meta.env.MODE,
      hasSupabaseUrl: !!import.meta.env.VITE_SUPABASE_URL,
      hasSupabaseKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
    },
    location: {
      pathname: window.location.pathname,
      hash: window.location.hash,
      search: window.location.search,
    }
  };

  // Add localStorage items
  if (typeof window !== 'undefined') {
    try {
      info.localStorage = {
        force_account_proceed: localStorage.getItem('force_account_proceed'),
        google_oauth_flow: localStorage.getItem('google_oauth_flow'),
        google_oauth_detected: localStorage.getItem('google_oauth_detected'),
        google_user_profile_loaded: localStorage.getItem('google_user_profile_loaded'),
        google_user_profile_time: localStorage.getItem('google_user_profile_time'),
      };
    } catch (e) {
      info.localStorage = { error: 'Could not access localStorage' };
    }
  }

  // Try to get a token - this helps check if there's an issue with getToken
  if (typeof window !== 'undefined' && window.Clerk?.session) {
    try {
      const token = await window.Clerk.session.getToken();
      info.clerk.tokenAvailable = !!token;
      info.clerk.tokenLength = token ? token.length : 0;
    } catch (e) {
      info.clerk.tokenError = e instanceof Error ? e.message : 'Unknown error';
    }
  }

  return info;
}

// Inject diagnostic button into the page (for debugging)
export function injectDiagnosticButton() {
  if (typeof document === 'undefined') return;
  
  const existingButton = document.getElementById('clerk-supabase-debug-btn');
  if (existingButton) return;
  
  const button = document.createElement('button');
  button.id = 'clerk-supabase-debug-btn';
  button.innerText = 'Debug Auth';
  button.style.position = 'fixed';
  button.style.bottom = '10px';
  button.style.right = '10px';
  button.style.zIndex = '9999';
  button.style.padding = '8px 12px';
  button.style.backgroundColor = '#6366f1';
  button.style.color = 'white';
  button.style.borderRadius = '4px';
  button.style.border = 'none';
  button.style.cursor = 'pointer';
  
  button.onclick = async () => {
    const info = await getDiagnosticInfo();
    console.log('Clerk-Supabase Diagnostic Info:', info);
    alert('Diagnostic info logged to console. Press F12 to view.');
  };
  
  document.body.appendChild(button);
}

// Fix utility - attempts to fix the stuck loading state
export function fixStuckLoadingState() {
  if (typeof window === 'undefined') return false;
  
  // Clear potentially problematic flags
  localStorage.removeItem('force_account_proceed');
  
  // Try to force reload the Clerk session
  if (window.Clerk) {
    try {
      window.Clerk.session.reload();
      return true;
    } catch (e) {
      console.error('Error reloading Clerk session:', e);
    }
  }
  
  return false;
}

// Initialize diagnostics in development or when manually triggered
export function initDiagnostics() {
  if (import.meta.env.DEV || localStorage.getItem('enable_auth_diagnostics') === 'true') {
    injectDiagnosticButton();
    
    // Log initial diagnostic info
    setTimeout(async () => {
      const info = await getDiagnosticInfo();
      console.log('Initial Clerk-Supabase Diagnostic Info:', info);
    }, 2000);
  }
}
