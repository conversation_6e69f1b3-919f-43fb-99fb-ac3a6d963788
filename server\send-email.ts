import nodemailer from 'nodemailer';

export default async function handler(req: any, res: any) {
  if (req.method === 'POST') {
    try {
      const { propertyName, propertyId } = req.body;

      // Configure your email transport
      const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: '<EMAIL>',
          pass: 'ztjj vkeu foty iyeg', // Replace with your actual App Password
        },
      });

      // Email content
      const mailOptions = {
        from: '<EMAIL>',
        to: '<EMAIL>', // Or your admin email address
        subject: `New Property Submission: ${propertyName}`,
        text: `A new property listing "${propertyName}" has been submitted for approval. Review it here: [Link to Admin Portal Property Page]`,
        html: `<p>A new property listing "<strong>${propertyName}</strong>" has been submitted for approval.</p><p>Review it here: <a href="[Link to Admin Portal Property Page]">Admin Portal</a></p>`,
      };

      // Send email
      await transporter.sendMail(mailOptions);

      res.status(200).json({ message: 'Email sent successfully' });
    } catch (error) {
      console.error('Error sending email:', error);
      res.status(500).json({ message: 'Failed to send email' });
    }
  } else {
    res.status(405).json({ message: 'Method Not Allowed' });
  }
}
