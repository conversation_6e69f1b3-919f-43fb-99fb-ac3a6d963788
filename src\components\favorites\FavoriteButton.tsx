import React from 'react';
import { Heart } from 'lucide-react';
import { useFavorites } from '../../hooks/useFavorites';

interface FavoriteButtonProps {
  venueId: string;
}

export default function FavoriteButton({ venueId }: FavoriteButtonProps) {
  const { favorites, toggleFavorite } = useFavorites();
  const isFavorite = favorites.includes(venueId);

  return (
    <button
      onClick={() => toggleFavorite(venueId)}
      className="absolute top-4 right-4 p-2 rounded-full bg-white shadow-md hover:scale-110 transition-transform"
    >
      <Heart
        className={`h-5 w-5 ${
          isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'
        }`}
      />
    </button>
  );
}