/**
 * Google OAuth Debug Utility
 * 
 * This utility provides tools for diagnosing and fixing Google OAuth issues
 * in the Clerk-Supabase integration. It collects diagnostics information
 * and can help users troubleshoot authentication problems.
 */

import { logAuthEvent } from './auth-debug';

/**
 * Collect all Google OAuth diagnostics information
 */
export function collectGoogleOAuthDiagnostics() {
  const diagnostics = {
    // Authentication flow flags
    google_oauth_flow: localStorage.getItem('google_oauth_flow'),
    google_oauth_timestamp: localStorage.getItem('google_oauth_timestamp'),
    google_oauth_detected: localStorage.getItem('google_oauth_detected'),
    google_oauth_path: localStorage.getItem('google_oauth_path'),
    google_oauth_referrer: localStorage.getItem('google_oauth_referrer'),
    is_gmail_user: localStorage.getItem('is_gmail_user'),
    
    // Auth state
    auth_success: localStorage.getItem('auth_success'),
    auth_success_time: localStorage.getItem('auth_success_time'),
    auth_attempts: localStorage.getItem('auth_attempts'),
    auth_loop_detected: localStorage.getItem('auth_loop_detected'),
    auth_loop_timestamp: localStorage.getItem('auth_loop_timestamp'),
    auth_timeout_google: localStorage.getItem('auth_timeout_google'),
    
    // Token information
    google_auth_token_set: localStorage.getItem('google_auth_token_set'),
    google_auth_token_time: localStorage.getItem('google_auth_token_time'),
    google_auth_token_error: localStorage.getItem('google_auth_token_error'),
    google_auth_token_error_time: localStorage.getItem('google_auth_token_error_time'),
    google_auth_token_missing: localStorage.getItem('google_auth_token_missing'),
    google_auth_token_get_error: localStorage.getItem('google_auth_token_get_error'),
    google_auth_token_error_message: localStorage.getItem('google_auth_token_error_message'),
    
    // Session handling
    google_auth_exception: localStorage.getItem('google_auth_exception'),
    google_auth_exception_time: localStorage.getItem('google_auth_exception_time'),
    google_auth_no_session: localStorage.getItem('google_auth_no_session'),
    
    // UserProfile handling
    google_user_profile_loaded: localStorage.getItem('google_user_profile_loaded'),
    google_user_profile_time: localStorage.getItem('google_user_profile_time'),
    google_no_session_fallback: localStorage.getItem('google_no_session_fallback'),
    google_no_gettoken_fallback: localStorage.getItem('google_no_gettoken_fallback'),
    google_skipped_test_query: localStorage.getItem('google_skipped_test_query'),
    
    // Fallback handling
    using_auth_fallback: localStorage.getItem('using_auth_fallback'),
    auth_fallback_timestamp: localStorage.getItem('auth_fallback_timestamp'),
    auth_fallback_used: localStorage.getItem('auth_fallback_used'),
    
    // User information
    clerk_user_id: localStorage.getItem('clerk_user_id'),
    clerk_user_email: localStorage.getItem('clerk_user_email'),
    user_role: localStorage.getItem('user_role'),
    
    // Browser information
    user_agent: navigator.userAgent,
    referrer: document.referrer,
    current_url: window.location.href,
    host: window.location.host,
    timestamp: new Date().toISOString()
  };
  
  return diagnostics;
}

/**
 * Log all Google OAuth diagnostics to console
 */
export function logGoogleOAuthDiagnostics() {
  const diagnostics = collectGoogleOAuthDiagnostics();
  console.log('=== Google OAuth Diagnostics ===');
  console.log(JSON.stringify(diagnostics, null, 2));
  
  // Also log through the auth event system
  logAuthEvent('google_oauth_diagnostics', diagnostics);
  
  return diagnostics;
}

/**
 * Attempt to fix common Google OAuth issues
 */
export function attemptGoogleOAuthFix() {
  // Check if we're in a loop
  const authAttempts = parseInt(localStorage.getItem('auth_attempts') || '0', 10);
  
  if (authAttempts > 3) {
    console.warn('Detected potential auth loop, clearing auth state');
    localStorage.setItem('auth_loop_fixed', 'true');
    localStorage.removeItem('auth_attempts');
    localStorage.removeItem('google_oauth_flow');
    return { fixed: true, type: 'auth_loop' };
  }
  
  // Check for missing token
  if (localStorage.getItem('google_auth_token_missing') === 'true') {
    console.log('Attempting to fix missing token issue');
    localStorage.setItem('token_issue_fixed', 'true');
    return { fixed: true, type: 'missing_token' };
  }
  
  // No fixes applied
  return { fixed: false, type: 'none' };
}

/**
 * Create a diagnostic report for support
 */
export function createGoogleOAuthReport() {
  const diagnostics = collectGoogleOAuthDiagnostics();
  const report = {
    diagnostics,
    timestamp: new Date().toISOString(),
    reportId: `google-oauth-report-${Date.now()}`
  };
  
  // Store the report in localStorage for later retrieval
  try {
    localStorage.setItem('google_oauth_report', JSON.stringify(report));
  } catch (e) {
    console.error('Error storing Google OAuth report:', e);
  }
  
  return report;
}

/**
 * Check if the current user is likely a Google OAuth user
 */
export function isLikelyGoogleOAuthUser() {
  return (
    localStorage.getItem('google_oauth_flow') === 'true' ||
    localStorage.getItem('google_oauth_detected') === 'true' ||
    localStorage.getItem('is_gmail_user') === 'true' ||
    (localStorage.getItem('clerk_user_email') || '').endsWith('@gmail.com') ||
    document.referrer.includes('google') ||
    document.referrer.includes('accounts.google.com')
  );
}

export default {
  collectGoogleOAuthDiagnostics,
  logGoogleOAuthDiagnostics,
  attemptGoogleOAuthFix,
  createGoogleOAuthReport,
  isLikelyGoogleOAuthUser
};
