/**
 * OptimizedImage Component
 * 
 * A React component for displaying optimized images with lazy loading,
 * responsive sizing, and modern image formats.
 */

import React, { useState, useEffect } from 'react';
import { getOptimizedCloudinaryUrl, generateSrcSet } from '../services/imageOptimizationService';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  placeholder?: 'blur' | 'empty';
  onLoad?: () => void;
  onError?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 80,
  objectFit = 'cover',
  placeholder = 'empty',
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);

  // Generate optimized image URLs
  const optimizedSrc = getOptimizedCloudinaryUrl(
    src,
    { width, height },
    { quality }
  );
  
  const srcSet = generateSrcSet(src, { quality });

  // Handle image load event
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // Handle image error event
  const handleError = () => {
    setError(true);
    if (onError) onError();
  };

  // Determine placeholder style
  const showPlaceholder = placeholder === 'blur' && !isLoaded;
  const placeholderStyle = showPlaceholder
    ? {
        filter: 'blur(20px)',
        transition: 'filter 0.3s ease-out',
      }
    : {};

  return (
    <div 
      className={\
elative overflow-hidden \\} 
      style={{ width, height: height || 'auto' }}
    >
      {error ? (
        <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
          <span>Image not available</span>
        </div>
      ) : (
        <img
          src={optimizedSrc}
          srcSet={srcSet}
          sizes={\(max-width: \px) 100vw, \px\}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          onLoad={handleLoad}
          onError={handleError}
          className={\w-full h-full transition-opacity duration-300 \\}
          style={{
            objectFit,
            ...placeholderStyle,
          }}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
