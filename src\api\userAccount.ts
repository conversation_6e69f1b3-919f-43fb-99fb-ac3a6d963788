import { getSupabaseClient } from '../services/api';
import { sendMessageNotification, sendReviewNotification } from '../services/emailNotifications';

export interface Message {
  id: string;
  sender_id: string;
  receiver_id: string;
  booking_id?: string;
  content: string;
  created_at: string;
  read: boolean;
  sender_name: string;
  receiver_name: string;
}

export interface Review {
  id: string;
  booking_id: string;
  reviewer_id: string;
  reviewee_id: string;
  rating: number;
  comment: string;
  created_at: string;
  reviewer_name: string;
  venue_name?: string;
  type: 'guest_to_host' | 'host_to_guest';
}

export interface SavedVenue {
  id: string;
  user_id: string;
  venue_id: string;
  created_at: string;
  venue: {
    id: string;
    title: string;
    location: string;
    price_per_hour: number;
    images: string[];
  };
}

export interface PaymentMethod {
  id: string;
  user_id: string;
  type: 'card' | 'bank_account';
  last_four: string;
  brand?: string;
  is_default: boolean;
  created_at: string;
}

// Enhanced Messages API
export const getMessages = async (userId: string): Promise<Message[]> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('messages')
    .select(`
      *,
      sender:sender_id(first_name, last_name, email),
      receiver:receiver_id(first_name, last_name, email)
    `)
    .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
    .order('created_at', { ascending: false });

  if (error) throw error;

  return data?.map(msg => ({
    ...msg,
    sender_name: `${msg.sender?.first_name || ''} ${msg.sender?.last_name || ''}`.trim(),
    receiver_name: `${msg.receiver?.first_name || ''} ${msg.receiver?.last_name || ''}`.trim()
  })) || [];
};

// Get conversation messages between two users
export const getConversationMessages = async (
  userId: string,
  otherUserId: string,
  bookingId?: string
): Promise<Message[]> => {
  const supabase = getSupabaseClient();

  let query = supabase
    .from('messages')
    .select(`
      *,
      sender:sender_id(first_name, last_name, email),
      receiver:receiver_id(first_name, last_name, email)
    `)
    .or(`and(sender_id.eq.${userId},receiver_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},receiver_id.eq.${userId})`)
    .order('created_at', { ascending: true });

  if (bookingId) {
    query = query.eq('booking_id', bookingId);
  }

  const { data, error } = await query;

  if (error) throw error;

  return data?.map(msg => ({
    ...msg,
    sender_name: `${msg.sender?.first_name || ''} ${msg.sender?.last_name || ''}`.trim(),
    receiver_name: `${msg.receiver?.first_name || ''} ${msg.receiver?.last_name || ''}`.trim()
  })) || [];
};

export const sendMessage = async (
  senderId: string,
  receiverId: string,
  content: string,
  bookingId?: string,
  senderName?: string,
  venueName?: string
): Promise<Message> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('messages')
    .insert({
      sender_id: senderId,
      receiver_id: receiverId,
      content,
      booking_id: bookingId,
      read: false
    })
    .select()
    .single();

  if (error) throw error;

  // Send email notification
  if (senderName) {
    try {
      await sendMessageNotification(
        senderId,
        receiverId,
        content,
        senderName,
        bookingId,
        venueName
      );
    } catch (notificationError) {
      console.error('Failed to send message notification:', notificationError);
      // Don't throw - message was sent successfully even if notification failed
    }
  }

  return data;
};

export const markMessageAsRead = async (messageId: string): Promise<void> => {
  const supabase = getSupabaseClient();

  const { error } = await supabase
    .from('messages')
    .update({ read: true })
    .eq('id', messageId);

  if (error) throw error;
};

// Mark all messages in a conversation as read
export const markConversationAsRead = async (
  userId: string,
  otherUserId: string,
  bookingId?: string
): Promise<void> => {
  const supabase = getSupabaseClient();

  let query = supabase
    .from('messages')
    .update({ read: true })
    .eq('receiver_id', userId)
    .eq('sender_id', otherUserId);

  if (bookingId) {
    query = query.eq('booking_id', bookingId);
  }

  const { error } = await query;
  if (error) throw error;
};

// Get unread message count for a user
export const getUnreadMessageCount = async (userId: string): Promise<number> => {
  const supabase = getSupabaseClient();

  const { count, error } = await supabase
    .from('messages')
    .select('*', { count: 'exact', head: true })
    .eq('receiver_id', userId)
    .eq('read', false);

  if (error) throw error;
  return count || 0;
};

// Get conversations list for a user
export const getConversations = async (userId: string) => {
  const supabase = getSupabaseClient();

  // Get all messages where user is sender or receiver
  const { data: messages, error } = await supabase
    .from('messages')
    .select(`
      *,
      sender:sender_id(id, first_name, last_name, email),
      receiver:receiver_id(id, first_name, last_name, email),
      booking:booking_id(id, venue_id, start_date, end_date, status)
    `)
    .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
    .order('created_at', { ascending: false });

  if (error) throw error;

  // Group messages by conversation (other user + booking)
  const conversationsMap = new Map();

  messages?.forEach(message => {
    const otherUser = message.sender_id === userId ? message.receiver : message.sender;
    const conversationKey = `${otherUser.id}_${message.booking_id || 'general'}`;

    if (!conversationsMap.has(conversationKey)) {
      conversationsMap.set(conversationKey, {
        otherUser: {
          id: otherUser.id,
          name: `${otherUser.first_name || ''} ${otherUser.last_name || ''}`.trim(),
          email: otherUser.email
        },
        booking: message.booking,
        messages: [],
        lastMessage: null,
        unreadCount: 0
      });
    }

    const conversation = conversationsMap.get(conversationKey);
    conversation.messages.push(message);

    if (!conversation.lastMessage || new Date(message.created_at) > new Date(conversation.lastMessage.created_at)) {
      conversation.lastMessage = message;
    }

    if (!message.read && message.receiver_id === userId) {
      conversation.unreadCount++;
    }
  });

  return Array.from(conversationsMap.values()).sort((a, b) =>
    new Date(b.lastMessage.created_at).getTime() - new Date(a.lastMessage.created_at).getTime()
  );
};

// Reviews API
export const getReviews = async (userId: string): Promise<Review[]> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('reviews')
    .select(`
      *,
      reviewer:reviewer_id(first_name, last_name),
      booking:booking_id(venue:venue_id(title))
    `)
    .or(`reviewer_id.eq.${userId},reviewee_id.eq.${userId}`)
    .order('created_at', { ascending: false });

  if (error) throw error;

  return data?.map(review => ({
    ...review,
    reviewer_name: `${review.reviewer?.first_name || ''} ${review.reviewer?.last_name || ''}`.trim(),
    venue_name: review.booking?.venue?.title || 'Unknown Venue'
  })) || [];
};

export const submitReview = async (
  bookingId: string,
  reviewerId: string,
  revieweeId: string,
  rating: number,
  comment: string,
  type: 'guest_to_host' | 'host_to_guest',
  reviewerName?: string,
  venueName?: string
): Promise<Review> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('reviews')
    .insert({
      booking_id: bookingId,
      reviewer_id: reviewerId,
      reviewee_id: revieweeId,
      rating,
      comment,
      type
    })
    .select()
    .single();

  if (error) throw error;

  // Send email notification
  if (reviewerName) {
    try {
      await sendReviewNotification(
        reviewerId,
        revieweeId,
        rating,
        comment,
        reviewerName,
        venueName
      );
    } catch (notificationError) {
      console.error('Failed to send review notification:', notificationError);
      // Don't throw - review was submitted successfully even if notification failed
    }
  }

  return data;
};

// Saved Venues API
export const getSavedVenues = async (userId: string): Promise<SavedVenue[]> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('saved_venues')
    .select(`
      *,
      venue:venue_id(id, title, location, price_per_hour, images)
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
};

export const saveVenue = async (userId: string, venueId: string): Promise<SavedVenue> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('saved_venues')
    .insert({
      user_id: userId,
      venue_id: venueId
    })
    .select(`
      *,
      venue:venue_id(id, title, location, price_per_hour, images)
    `)
    .single();

  if (error) throw error;
  return data;
};

export const removeSavedVenue = async (userId: string, venueId: string): Promise<void> => {
  const supabase = getSupabaseClient();

  const { error } = await supabase
    .from('saved_venues')
    .delete()
    .eq('user_id', userId)
    .eq('venue_id', venueId);

  if (error) throw error;
};

// Payment Methods API
export const getPaymentMethods = async (userId: string): Promise<PaymentMethod[]> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('payment_methods')
    .select('*')
    .eq('user_id', userId)
    .order('is_default', { ascending: false });

  if (error) throw error;
  return data || [];
};

export const addPaymentMethod = async (
  userId: string,
  type: 'card' | 'bank_account',
  lastFour: string,
  brand?: string
): Promise<PaymentMethod> => {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('payment_methods')
    .insert({
      user_id: userId,
      type,
      last_four: lastFour,
      brand,
      is_default: false
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const setDefaultPaymentMethod = async (paymentMethodId: string, userId: string): Promise<void> => {
  const supabase = getSupabaseClient();

  // First, unset all other default payment methods
  await supabase
    .from('payment_methods')
    .update({ is_default: false })
    .eq('user_id', userId);

  // Then set the selected one as default
  const { error } = await supabase
    .from('payment_methods')
    .update({ is_default: true })
    .eq('id', paymentMethodId);

  if (error) throw error;
};

export const removePaymentMethod = async (paymentMethodId: string): Promise<void> => {
  const supabase = getSupabaseClient();

  const { error } = await supabase
    .from('payment_methods')
    .delete()
    .eq('id', paymentMethodId);

  if (error) throw error;
};
