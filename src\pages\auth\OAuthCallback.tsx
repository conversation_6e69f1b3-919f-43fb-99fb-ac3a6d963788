import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser, useAuth } from '@clerk/clerk-react';
import { isPreregisteredHost } from '../../data/preregisteredHosts';
import { isHost } from '../../utils/user-roles';
import { CLERK_CONFIG } from '../../config/clerk';
import { useSupabase } from '../../providers/SupabaseProvider';
import { syncUserWithSupabase, UserRole } from '../../services/auth';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';

export default function OAuthCallback() {
  const { user, isLoaded } = useUser();
  const { isSignedIn } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserRole } = useSupabase();
  const [status, setStatus] = useState('Processing your sign-in...');
  const [error, setError] = useState('');
  const [processingComplete, setProcessingComplete] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 8; // Increased retries for better reliability

  // Force redirect after 10 seconds if stuck
  useEffect(() => {
    const forceRedirectTimer = setTimeout(() => {
      if (!processingComplete && user && isSignedIn) {
        console.log('OAuthCallback: Force redirecting due to timeout');
        const urlParams = new URLSearchParams(location.search);
        const userTypeFromUrl = urlParams.get('userType');
        const isHostUser = userTypeFromUrl === 'host' ||
                          localStorage.getItem('registering_as_host') === 'true' ||
                          localStorage.getItem('user_type') === 'host';

        const redirectPath = isHostUser ? '/host/dashboard' : '/my-account';

        // Set processed flag
        const processedFlag = `oauth_processed_${user.id}`;
        localStorage.setItem(processedFlag, 'true');

        // Clear localStorage flags
        localStorage.removeItem('registering_as_host');
        localStorage.removeItem('user_type');
        localStorage.removeItem('auth_user_type');

        // Use window.location.href for force redirect
        console.log('OAuthCallback: Force redirecting with window.location.href');
        window.location.href = redirectPath;
      }
    }, 10000); // 10 seconds timeout

    return () => clearTimeout(forceRedirectTimer);
  }, [user, isSignedIn, processingComplete, location.search, navigate]);

  // Single useEffect to handle OAuth callback
  useEffect(() => {
    let isMounted = true;
    let retryTimeout: NodeJS.Timeout;

    const processOAuthCallback = async () => {
      console.log('OAuthCallback: Component mounted');
      console.log('OAuthCallback: Auth state:', { user: !!user, isLoaded, isSignedIn });
      console.log('OAuthCallback: Current URL:', window.location.href);
      console.log('OAuthCallback: Search params:', location.search);
      console.log('OAuthCallback: Pathname:', location.pathname);

      // Debug Clerk configuration
      console.log('OAuthCallback: Clerk config:', {
        redirectUrl: CLERK_CONFIG.oauthCallbackURL,
        redirectUrls: CLERK_CONFIG.redirectUrls,
        publishableKey: CLERK_CONFIG.publishableKey?.substring(0, 20) + '...',
        domain: CLERK_CONFIG.clerkDomain,
        developmentMode: CLERK_CONFIG.developmentMode
      });

      // Check for last OAuth attempt
      const lastOAuthAttempt = localStorage.getItem('last_oauth_attempt');
      if (lastOAuthAttempt) {
        console.log('Last OAuth attempt:', JSON.parse(lastOAuthAttempt));
      }

      // If already processing or completed, don't process again
      if (processingComplete) {
        console.log('OAuthCallback: Already processed, skipping');
        return;
      }

      // Check if we've already processed this user to prevent infinite loops
      const processedFlag = `oauth_processed_${user?.id}`;
      if (user && localStorage.getItem(processedFlag) === 'true') {
        console.log('OAuthCallback: User already processed, redirecting directly');
        const urlParams = new URLSearchParams(location.search);
        const userTypeFromUrl = urlParams.get('userType');
        const isHostUser = userTypeFromUrl === 'host' ||
                          localStorage.getItem('registering_as_host') === 'true' ||
                          localStorage.getItem('user_type') === 'host';

        const redirectPath = isHostUser ? '/host/dashboard' : '/my-account';
        setProcessingComplete(true);
        console.log('OAuthCallback: Early redirect with window.location.href');
        window.location.href = redirectPath;
        return;
      }

      // Wait for Clerk to load
      if (!isLoaded) {
        console.log('OAuthCallback: Clerk not loaded yet, waiting...');
        return;
      }

      // Check if we're in the middle of an OAuth flow
      const urlParams = new URLSearchParams(location.search);
      const hasOAuthParams = urlParams.has('code') || urlParams.has('state') || urlParams.has('session_id');

      // Check for OAuth errors
      const oauthError = urlParams.get('error');
      const oauthErrorDescription = urlParams.get('error_description');

      if (oauthError) {
        console.error('OAuth error detected:', oauthError, oauthErrorDescription);
        setError(`Authentication failed: ${oauthErrorDescription || oauthError}`);
        setStatus('Authentication failed');
        setTimeout(() => {
          if (isMounted) {
            navigate('/login');
          }
        }, 3000);
        return;
      }

      if (hasOAuthParams) {
        console.log('OAuthCallback: OAuth parameters detected, waiting for Clerk to process...');
        setStatus('Completing authentication...');

        // Give Clerk more time to process OAuth callback
        if (retryCount < 3) {
          const delay = 2000; // 2 seconds for OAuth processing
          console.log(`OAuthCallback: Waiting ${delay}ms for OAuth processing...`);

          retryTimeout = setTimeout(() => {
            if (isMounted) {
              setRetryCount(prev => prev + 1);
            }
          }, delay);
          return;
        }
      }

      // If we have Google OAuth callback but no user yet, detect the callback
      if (location.pathname === '/auth/callback' && !user) {
        console.log('Google OAuth callback detected');
        console.warn('Fallback triggered: No user data available after Google OAuth callback');

        // For Clerk OAuth callbacks, we just need to wait for the user state to update
        // The Clerk provider will handle the OAuth flow automatically
        console.log('OAuthCallback: Waiting for Clerk to complete OAuth flow...');
      }

      // If no user after Clerk is loaded, handle the retry logic
      if (!user || !isSignedIn) {
        console.log('OAuthCallback: No user data available yet');

        // Check if we have OAuth parameters in the URL - if so, give Clerk more time
        const urlParams = new URLSearchParams(location.search);
        const hasOAuthParams = urlParams.has('code') || urlParams.has('state') || urlParams.has('session_id');

        // Increase max retries if we have OAuth params (Clerk is processing)
        const effectiveMaxRetries = hasOAuthParams ? maxRetries + 5 : maxRetries;

        // Retry logic with exponential backoff
        if (retryCount < effectiveMaxRetries && isMounted) {
          const delay = Math.min(1000 * Math.pow(1.5, retryCount), 10000); // Max 10 seconds
          console.log(`Retrying user data fetch...`);
          console.log(`OAuthCallback: Retrying in ${delay}ms (attempt ${retryCount + 1}/${effectiveMaxRetries})`);

          retryTimeout = setTimeout(() => {
            if (isMounted) {
              setRetryCount(prev => prev + 1);
            }
          }, delay);
          return;
        } else {
          // Max retries reached, show error
          console.warn('OAuthCallback: Max retries reached, redirecting to sign-in');
          setError('Authentication failed. Please try signing in again.');
          setStatus('Authentication failed');
          setTimeout(() => {
            if (isMounted) {
              navigate('/login');
            }
          }, 3000);
          return;
        }
      }

      // User is available, process the authentication
      try {
        console.log('OAuthCallback: Processing user authentication');
        setStatus('Setting up your account...');

        // Check if registering as host from URL parameter or localStorage
        const urlParams = new URLSearchParams(location.search);
        const userTypeFromUrl = urlParams.get('userType');
        const registeringAsHost = localStorage.getItem('registering_as_host') === 'true' ||
                                 localStorage.getItem('user_type') === 'host' ||
                                 localStorage.getItem('auth_user_type') === 'host' ||
                                 userTypeFromUrl === 'host';
        const userEmail = user.primaryEmailAddress?.emailAddress;

        let userRole: UserRole = 'guest';
        let redirectPath = '/my-account';

        if (registeringAsHost) {
          console.log('OAuthCallback: User registering as host');
          userRole = 'host';
          redirectPath = '/host/dashboard';
          // Clear all host registration flags
          localStorage.removeItem('registering_as_host');
          localStorage.removeItem('user_type');
          localStorage.removeItem('auth_user_type');
        } else if (userEmail && isPreregisteredHost(userEmail)) {
          console.log('OAuthCallback: User is preregistered host');
          userRole = 'host';
          redirectPath = '/host/dashboard';
        } else if (userEmail && isHost(userEmail)) {
          console.log('OAuthCallback: User is existing host');
          userRole = 'host';
          redirectPath = '/host/dashboard';
        }

        // Set user role in context
        setUserRole(userRole);

        // Sync with Supabase (with error handling to prevent blocking)
        console.log('OAuthCallback: Syncing user with Supabase');
        try {
          await syncUserWithSupabase(user, userRole);
          console.log('OAuthCallback: Supabase sync successful');
        } catch (syncError) {
          console.warn('OAuthCallback: Supabase sync failed, but continuing with authentication:', syncError);
          // Don't block the authentication flow if Supabase sync fails
        }

        // Simulate webhook event for consistency
        try {
          simulateWebhookEvent('user.created', {
            id: user.id,
            email_addresses: user.emailAddresses,
            first_name: user.firstName,
            last_name: user.lastName,
            created_at: user.createdAt
          });
        } catch (webhookError) {
          console.warn('OAuthCallback: Webhook simulation failed:', webhookError);
          // Don't fail the entire process for webhook errors
        }

        // Mark as complete and redirect
        setProcessingComplete(true);
        setStatus('Authentication successful! Redirecting...');

        // Set processed flag to prevent loops
        const processedFlag = `oauth_processed_${user.id}`;
        localStorage.setItem(processedFlag, 'true');

        console.log(`OAuthCallback: Redirecting to ${redirectPath}`);

        // Clear the processed flag after a delay to allow for re-authentication if needed
        setTimeout(() => {
          localStorage.removeItem(processedFlag);
        }, 30000); // Clear after 30 seconds

        // Use window.location.href for more reliable redirect
        console.log('OAuthCallback: Using window.location.href for redirect');
        setTimeout(() => {
          window.location.href = redirectPath;
        }, 1000);

      } catch (error) {
        console.error('OAuthCallback: Error processing authentication:', error);
        setError('Failed to complete authentication. Please try again.');
        setStatus('Authentication failed');
        
        setTimeout(() => {
          if (isMounted) {
            navigate('/sign-in');
          }
        }, 3000);
      }
    };

    processOAuthCallback();

    // Cleanup function
    return () => {
      isMounted = false;
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, [user, isLoaded, retryCount, processingComplete, navigate, setUserRole]);

  // Render loading/error UI
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        {error ? (
          <>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Authentication Error</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="text-sm text-gray-500">
              Redirecting to sign-in page...
            </div>
          </>
        ) : (
          <>
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-600 mx-auto mb-6"></div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Almost There!</h1>
            <p className="text-gray-600 mb-4">{status}</p>
            {retryCount > 0 && (
              <div className="text-sm text-gray-500">
                Retry attempt {retryCount}/{maxRetries}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
