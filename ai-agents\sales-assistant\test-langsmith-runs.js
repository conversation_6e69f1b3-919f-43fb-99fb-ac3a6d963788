import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const apiKey = process.env.LANGSMITH_API_KEY;
const projectName = process.env.LANGSMITH_PROJECT;

console.log('Using API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');
console.log('Project Name:', projectName);

async function testLangSmithRuns() {
  try {
    // Test API connection by fetching runs
    const response = await fetch('https://api.smith.langchain.com/runs', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey
      }
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('\nRuns found:', data.length);
    
    if (data.length > 0) {
      console.log('\nLatest run:', data[0]);
    }
    
  } catch (error) {
    console.error('Error testing LangSmith API:', error);
  }
}

testLangSmithRuns();
