#!/usr/bin/env node
// Run the Supabase setup script to initialize database

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Initialize supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || '';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: { persistSession: false }
});

const setupDatabase = async () => {
  try {
    console.log('Running Supabase setup script...');
    console.log(`Connecting to Supabase at: ${supabaseUrl}`);
    
    // Get the directory of the current module
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    
    // Load SQL setup file
    const sqlFilePath = path.resolve(__dirname, '../supabase-setup.sql');
    console.log(`Loading SQL file from: ${sqlFilePath}`);
    const sqlFile = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split into individual commands (crude but effective)
    const commands = sqlFile.split(';').filter(cmd => cmd.trim().length > 0);
    
    // Execute commands sequentially
    for (const command of commands) {
      console.log(`Executing SQL command: ${command.substring(0, 50)}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: command });
      
      if (error) {
        // If exec_sql doesn't exist yet, we need to create it first
        if (error.message.includes('function "exec_sql" does not exist')) {
          console.log('Creating exec_sql function...');
          
          // Create the exec_sql function manually
          const { error: createFnError } = await supabase.rpc('exec_sql_create_function');
          
          if (createFnError) {
            // Direct SQL execution via the REST API
            const createFnSql = `
              CREATE OR REPLACE FUNCTION exec_sql(sql text)
              RETURNS VOID
              LANGUAGE plpgsql
              SECURITY DEFINER
              SET search_path = public
              AS $$
              BEGIN
                EXECUTE sql;
              END;
              $$;
              
              GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated;
              GRANT EXECUTE ON FUNCTION exec_sql(text) TO service_role;
            `;
            
            // Try to execute via direct REST API call
            const response = await fetch(`${process.env.VITE_SUPABASE_URL}/rest/v1/rpc/exec_direct`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'apikey': process.env.VITE_SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`
              },
              body: JSON.stringify({
                sql: createFnSql
              })
            });
            
            if (!response.ok) {
              throw new Error(`Failed to create exec_sql function: ${await response.text()}`);
            }
            
            console.log('exec_sql function created successfully');
            
            // Try running the original command again
            const { error: retryError } = await supabase.rpc('exec_sql', { sql: command });
            if (retryError) {
              console.error(`Error executing command after creating exec_sql: ${retryError.message}`);
            }
          }
        } else {
          console.error(`Error executing command: ${error.message}`);
        }
      }
    }
    
    console.log('Supabase setup completed successfully');
  } catch (error) {
    console.error('Error setting up Supabase:', error);
  }
};

setupDatabase();
