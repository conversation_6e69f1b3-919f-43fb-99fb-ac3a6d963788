import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Shield, AlertTriangle, Phone, FileText, Building, Flame, Users, ExternalLink } from 'lucide-react';

const SafetyPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Safety Guidelines | HouseGoing</title>
        <meta name="description" content="Comprehensive safety guidelines for hosts and guests. NSW safety regulations, insurance requirements, and emergency procedures." />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://housegoing.com.au/safety" />
        
        {/* Open Graph */}
        <meta property="og:title" content="Safety Guidelines | HouseGoing" />
        <meta property="og:description" content="Comprehensive safety guidelines for hosts and guests. NSW safety regulations, insurance requirements, and emergency procedures." />
        <meta property="og:url" content="https://housegoing.com.au/safety" />
        <meta property="og:type" content="website" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Safety Guidelines</h1>
            <p className="text-lg text-gray-600">
              Comprehensive safety requirements for hosts and guests in NSW, Australia
            </p>
          </div>

          {/* Important Safety Notice */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-red-900 mb-2 flex items-center">
              <AlertTriangle className="h-6 w-6 mr-2" />
              Important Safety Notice
            </h2>
            <p className="text-red-800">
              All venues must comply with NSW safety regulations, local council requirements, and Australian building codes. 
              Hosts are responsible for ensuring their property meets all safety standards.
            </p>
          </div>

          {/* Host Safety Requirements */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <Shield className="h-6 w-6 text-green-600 mr-2" />
              Host Safety Requirements
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8 mb-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Flame className="h-5 w-5 text-red-600 mr-2" />
                  Fire Safety
                </h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Working smoke alarms in all required areas</li>
                  <li>Fire extinguishers accessible and serviced</li>
                  <li>Clear emergency exit routes marked</li>
                  <li>Fire blanket in kitchen areas</li>
                  <li>Emergency lighting where required</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Building className="h-5 w-5 text-blue-600 mr-2" />
                  Building Safety
                </h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Current building compliance certificate</li>
                  <li>Safe electrical installations</li>
                  <li>Secure railings and balustrades</li>
                  <li>Non-slip surfaces in wet areas</li>
                  <li>Adequate lighting in all areas</li>
                </ul>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-4">📋 Mandatory Requirements</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li><strong>Public Liability Insurance:</strong> Minimum $10 million coverage</li>
                <li><strong>Council Approval:</strong> Check if short-term rental permits required</li>
                <li><strong>Noise Compliance:</strong> Adhere to Protection of the Environment Operations Act 1997</li>
                <li><strong>Capacity Limits:</strong> Do not exceed safe occupancy numbers</li>
                <li><strong>Safety Equipment:</strong> First aid kit, emergency contact information</li>
              </ul>
            </div>
          </div>

          {/* Guest Responsibilities */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <Users className="h-6 w-6 text-purple-600 mr-2" />
              Guest Responsibilities
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">🎉 During Events</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Respect maximum capacity limits</li>
                  <li>Follow noise restrictions and curfews</li>
                  <li>Use designated smoking areas only</li>
                  <li>Report any safety hazards immediately</li>
                  <li>Keep emergency exits clear</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-4">🚨 Emergency Procedures</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Know location of emergency exits</li>
                  <li>Familiarize with fire safety equipment</li>
                  <li>Have emergency contact numbers ready</li>
                  <li>Call 000 for immediate emergencies</li>
                  <li>Contact host for non-emergency issues</li>
                </ul>
              </div>
            </div>
          </div>

          {/* NSW Legal Requirements */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <FileText className="h-6 w-6 text-blue-600 mr-2" />
              NSW Legal Requirements
            </h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">🏛️ Relevant Legislation</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li><strong>Environmental Planning and Assessment Act 1979:</strong> Development consent requirements</li>
                  <li><strong>Protection of the Environment Operations Act 1997:</strong> Noise pollution controls</li>
                  <li><strong>Building Code of Australia:</strong> Building safety standards</li>
                  <li><strong>Work Health and Safety Act 2011:</strong> Workplace safety obligations</li>
                  <li><strong>Local Government Act 1993:</strong> Council regulations and permits</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-3">📞 Regulatory Contacts</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <p><strong>NSW Fair Trading:</strong> 13 32 20</p>
                      <p><strong>Fire & Rescue NSW:</strong> 1300 729 579</p>
                      <p><strong>SafeWork NSW:</strong> 13 10 50</p>
                    </div>
                    <div>
                      <p><strong>Local Council:</strong> Contact your area council</p>
                      <p><strong>EPA NSW:</strong> 131 555</p>
                      <p><strong>Emergency Services:</strong> 000</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Emergency Procedures */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-orange-900 mb-4 flex items-center">
              <Phone className="h-6 w-6 mr-2" />
              Emergency Procedures
            </h2>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h3 className="font-semibold text-orange-900 mb-2">🔥 Fire Emergency</h3>
                <ol className="list-decimal list-inside text-orange-800 space-y-1">
                  <li>Call 000 immediately</li>
                  <li>Evacuate all persons</li>
                  <li>Use fire equipment if safe</li>
                  <li>Meet at assembly point</li>
                  <li>Do not re-enter building</li>
                </ol>
              </div>
              
              <div>
                <h3 className="font-semibold text-orange-900 mb-2">🚑 Medical Emergency</h3>
                <ol className="list-decimal list-inside text-orange-800 space-y-1">
                  <li>Call 000 for ambulance</li>
                  <li>Provide first aid if trained</li>
                  <li>Clear access for paramedics</li>
                  <li>Contact venue host</li>
                  <li>Document incident details</li>
                </ol>
              </div>
              
              <div>
                <h3 className="font-semibold text-orange-900 mb-2">🚨 Security Issue</h3>
                <ol className="list-decimal list-inside text-orange-800 space-y-1">
                  <li>Call 000 if immediate danger</li>
                  <li>Contact venue host</li>
                  <li>Document incident</li>
                  <li>Preserve evidence if safe</li>
                  <li>Report to HouseGoing</li>
                </ol>
              </div>
            </div>
          </div>

          {/* Resources and Links */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Safety Resources</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3">Government Resources</h3>
                <ul className="space-y-2">
                  <li>
                    <a href="https://www.safework.nsw.gov.au" target="_blank" rel="noopener noreferrer" className="text-purple-600 hover:underline flex items-center">
                      SafeWork NSW
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </li>
                  <li>
                    <a href="https://www.fire.nsw.gov.au" target="_blank" rel="noopener noreferrer" className="text-purple-600 hover:underline flex items-center">
                      Fire & Rescue NSW
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </li>
                  <li>
                    <a href="https://www.fairtrading.nsw.gov.au" target="_blank" rel="noopener noreferrer" className="text-purple-600 hover:underline flex items-center">
                      NSW Fair Trading
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-3">HouseGoing Resources</h3>
                <ul className="space-y-2">
                  <li><a href="/help" className="text-purple-600 hover:underline">Help Center</a></li>
                  <li><a href="/contact" className="text-purple-600 hover:underline">Contact Support</a></li>
                  <li><a href="/nsw-party-planning" className="text-purple-600 hover:underline">NSW Party Planning Guide</a></li>
                </ul>
              </div>
            </div>
          </div>

          {/* Legal Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              <strong>Last Updated:</strong> January 6, 2025<br />
              <strong>Effective Date:</strong> January 6, 2025<br />
              <strong>Governing Law:</strong> New South Wales, Australia
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default SafetyPage;
