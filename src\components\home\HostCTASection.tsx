import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { DollarSign, <PERSON>, <PERSON>, Sparkles, ArrowRight } from 'lucide-react';

export default function HostCTASection() {
  return (
    <section className="pt-6 pb-8 px-4 sm:px-6 relative overflow-hidden bg-white">
      <div className="container-width-sm relative z-10">
        <div className="bg-gray-50 rounded-lg overflow-hidden p-5 border border-gray-100 relative">
          <div className="relative">
            <div className="inline-block bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-small font-medium mb-3">Venue Owners</div>

            <h2 className="mb-3 max-w-2xl">
              Turn Your Space Into a Party Venue
            </h2>

            <p className="text-large text-gray-600 mb-5 max-w-3xl">
              Join our community of hosts and start earning from your venue. We'll help
              you reach party planners looking for spaces just like yours.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-5">
              <div className="card p-4 group">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3 text-purple-600">
                    <DollarSign className="h-5 w-5" />
                  </div>
                  <h3 className="font-semibold text-gray-900">$0 Listing Fee</h3>
                </div>
                <p className="text-small text-gray-600">
                  Always free to list your space.
                </p>
              </div>

              <div className="card p-4 group">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3 text-purple-600">
                    <Clock className="h-5 w-5" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Your Schedule</h3>
                </div>
                <p className="text-small text-gray-600">
                  Host only when you want—total flexibility.
                </p>
              </div>

              <div className="card p-4 group">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3 text-purple-600">
                    <Shield className="h-5 w-5" />
                  </div>
                  <h3 className="font-semibold text-gray-900">No Upfront Risk</h3>
                </div>
                <p className="text-small text-gray-600">
                  Only pay a small fee if you get booked—nothing to lose.
                </p>
              </div>

              <div className="card p-4 group">
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3 text-purple-600">
                    <Sparkles className="h-5 w-5" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Hosts Are In Control</h3>
                </div>
                <p className="text-small text-gray-600">
                  Set your own price, rules, and approve every booking request.
                </p>
              </div>
            </div>

            <div className="flex justify-center md:justify-start">
              <Link
                to="/host/portal"
                className="btn-primary"
              >
                Start Hosting Today <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
