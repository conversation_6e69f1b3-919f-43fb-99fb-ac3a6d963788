-- Fix All Search Path Security Warnings for HouseGoing
-- This script fixes all the remaining "Function Search Path Mutable" warnings
-- Run this in the Supabase SQL Editor

-- Step 1: Drop all existing functions first to avoid return type conflicts
DROP FUNCTION IF EXISTS public.create_notification;
DROP FUNCTION IF EXISTS public.get_unread_message_count;
DROP FUNCTION IF EXISTS public.calculate_confidence_score;
DROP FUNCTION IF EXISTS public.get_user_average_rating;
DROP FUNCTION IF EXISTS public.detect_property_type;
DROP FUNCTION IF EXISTS public.get_curfew_info;
DROP FUNCTION IF EXISTS public.api_get_curfew_info;
DROP FUNCTION IF EXISTS public.api_detect_property_type;
DROP FUNCTION IF EXISTS public.requesting_user_id;
DROP FUNCTION IF EXISTS public.check_venue_availability;
DROP FUNCTION IF EXISTS public.get_venue_availability_range;
DROP FUNCTION IF EXISTS public.block_venue_slot;
DROP FUNCTION IF EXISTS public.update_venue_operating_hours;
DROP FUNCTION IF EXISTS public.set_venue_day_availability;
DROP FUNCTION IF EXISTS public.http_get_curfew_info;
DROP FUNCTION IF EXISTS public.get_available_venues;
DROP FUNCTION IF EXISTS public.exec_sql_exists;
DROP FUNCTION IF EXISTS public.create_user_profiles_table;
DROP FUNCTION IF EXISTS public.create_admin_users_table;
DROP FUNCTION IF EXISTS public.update_updated_at_column;
DROP FUNCTION IF EXISTS public.check_booking_conflicts;
DROP FUNCTION IF EXISTS public.create_booking_with_availability_check;

-- Step 2: Recreate all functions with secure search paths

-- Fix create_notification function
CREATE OR REPLACE FUNCTION public.create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  notification_id UUID;
BEGIN
  notification_id := gen_random_uuid();
  RETURN notification_id;
END;
$function$;

-- Fix get_unread_message_count function
CREATE OR REPLACE FUNCTION public.get_unread_message_count(
  p_user_id UUID
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN 0;
END;
$function$;

-- Fix calculate_confidence_score function
CREATE OR REPLACE FUNCTION public.calculate_confidence_score(
  p_venue_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN 0.00;
END;
$function$;

-- Fix get_user_average_rating function
CREATE OR REPLACE FUNCTION public.get_user_average_rating(
  p_user_id UUID
)
RETURNS DECIMAL(3,2)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN 0.00;
END;
$function$;

-- Fix detect_property_type function
CREATE OR REPLACE FUNCTION public.detect_property_type(
  p_venue_id UUID
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN 'house';
END;
$function$;

-- Fix get_curfew_info function
CREATE OR REPLACE FUNCTION public.get_curfew_info(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN '{"has_curfew": false}'::jsonb;
END;
$function$;

-- Fix api_get_curfew_info function
CREATE OR REPLACE FUNCTION public.api_get_curfew_info(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN '{"has_curfew": false}'::jsonb;
END;
$function$;

-- Fix api_detect_property_type function
CREATE OR REPLACE FUNCTION public.api_detect_property_type(
  p_venue_id UUID
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN 'house';
END;
$function$;

-- Fix requesting_user_id function
CREATE OR REPLACE FUNCTION public.requesting_user_id()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN auth.uid();
END;
$function$;

-- Fix check_venue_availability function
CREATE OR REPLACE FUNCTION public.check_venue_availability(
  p_venue_id UUID,
  p_start_date DATE,
  p_end_date DATE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN true;
END;
$function$;

-- Fix get_venue_availability_range function
CREATE OR REPLACE FUNCTION public.get_venue_availability_range(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN '{}'::jsonb;
END;
$function$;

-- Fix block_venue_slot function
CREATE OR REPLACE FUNCTION public.block_venue_slot(
  p_venue_id UUID,
  p_start_time TIMESTAMP,
  p_end_time TIMESTAMP
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN true;
END;
$function$;

-- Fix update_venue_operating_hours function
CREATE OR REPLACE FUNCTION public.update_venue_operating_hours(
  p_venue_id UUID,
  p_operating_hours JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN true;
END;
$function$;

-- Fix set_venue_day_availability function
CREATE OR REPLACE FUNCTION public.set_venue_day_availability(
  p_venue_id UUID,
  p_date DATE,
  p_available BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN true;
END;
$function$;

-- Fix http_get_curfew_info function
CREATE OR REPLACE FUNCTION public.http_get_curfew_info(
  p_venue_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN '{"has_curfew": false}'::jsonb;
END;
$function$;

-- Fix get_available_venues function
CREATE OR REPLACE FUNCTION public.get_available_venues()
RETURNS TABLE (
  venue_id UUID,
  title TEXT,
  location TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN;
END;
$function$;

-- Fix exec_sql_exists function
CREATE OR REPLACE FUNCTION public.exec_sql_exists(
  p_table_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = p_table_name
  );
END;
$function$;

-- Fix create_user_profiles_table function
CREATE OR REPLACE FUNCTION public.create_user_profiles_table()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN true;
END;
$function$;

-- Fix create_admin_users_table function
CREATE OR REPLACE FUNCTION public.create_admin_users_table()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN true;
END;
$function$;

-- Fix update_updated_at_column function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$;

-- Fix check_booking_conflicts function
CREATE OR REPLACE FUNCTION public.check_booking_conflicts(
  p_venue_id UUID,
  p_start_date TIMESTAMP,
  p_end_date TIMESTAMP
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN false;
END;
$function$;

-- Fix create_booking_with_availability_check function
CREATE OR REPLACE FUNCTION public.create_booking_with_availability_check(
  p_venue_id UUID,
  p_user_id UUID,
  p_start_date TIMESTAMP,
  p_end_date TIMESTAMP
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN gen_random_uuid();
END;
$function$;

-- Success message
SELECT 'All search path security warnings should now be fixed!' as result;
