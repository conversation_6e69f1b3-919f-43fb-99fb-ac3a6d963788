/**
 * Utility functions to test and diagnose Clerk-Supabase integration issues
 */

/**
 * Tests the Clerk-Supabase integration
 */
export async function testClerkSupabaseIntegration(): Promise<{
  success: boolean;
  error?: string;
  details?: any;
}> {
  try {
    console.log('Testing Clerk-Supabase integration...');
    
    // Get the authenticated client - handle potential undefined return
    const supabase = await getAuthenticatedSupabaseClient();
    
    if (!supabase) {
      console.error('Failed to get authenticated Supabase client');
      return {
        success: false,
        error: 'Authentication client initialization failed',
        details: { reason: 'No Supabase client returned' }
      };
    }
    
    // Try a simple query to verify authentication
    const { data: testData, error } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('Error testing Clerk-Supabase integration:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
    
    return {
      success: true,
      details: {
        dataReceived: !!testData,
        endpoint: 'supabase-connection'
      }
    };
  } catch (err) {
    console.error('Exception testing Clerk-Supabase integration:', err);
    return {
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error',
      details: err
    };
  }
}

/**
 * Creates or updates a user profile in Supabase
 */
export async function ensureUserProfileExists(
  clerkId: string, 
  email: string, 
  profileData: ProfileData = {}
): Promise<{
  success: boolean;
  error?: string;
  data?: any;
  details?: any;
}> {  try {
    console.log('Ensuring user profile exists for:', email);
    
    // Get the authenticated client
    const supabase = await getAuthenticatedSupabaseClient();
    
    if (!supabase) {
      console.error('Failed to get authenticated Supabase client');
      return {
        success: false,
        error: 'Authentication client initialization failed',
        details: { reason: 'No Supabase client returned' }
      };
    }
    
    // Check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('clerk_id', clerkId)
      .maybeSingle();
    
    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error checking for existing profile:', fetchError);
      return {
        success: false,
        error: fetchError.message,
        details: fetchError
      };
    }
    
    // Prepare profile data
    const userData = {
      clerk_id: clerkId,
      email,
      role: profileData.role || 'guest',
      is_host: profileData.role === 'host',
      updated_at: new Date().toISOString(),
      ...(existingProfile ? {} : { created_at: new Date().toISOString() }),
      ...profileData
    };
    
    // Insert or update - with simplified options to avoid type issues
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert(userData, { onConflict: 'clerk_id' })
      .select();
    
    if (error) {
      console.error('Error upserting user profile:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
    
    return {
      success: true,
      data: data[0] || data
    };
  } catch (err) {
    console.error('Exception ensuring user profile exists:', err);
    return {
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error',
      details: err
    };
  }
}

export default {
  testClerkSupabaseIntegration,
  ensureUserProfileExists
};
