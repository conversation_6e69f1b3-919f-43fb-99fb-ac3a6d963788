import React, { forwardRef } from 'react';

interface SimpleCheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
  label: string;
  error?: string;
  helperText?: string;
  containerClassName?: string;
  labelClassName?: string;
  onChange?: (checked: boolean) => void;
}

const SimpleCheckbox = forwardRef<HTMLInputElement, SimpleCheckboxProps>(({
  label,
  error,
  helperText,
  className = '',
  containerClassName = '',
  labelClassName = '',
  disabled,
  required,
  id,
  checked,
  onChange,
  ...props
}, ref) => {
  // Generate a unique ID if none is provided
  const checkboxId = id || `checkbox-${label.toLowerCase().replace(/\s+/g, '-')}`;
  
  // Handle change event
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };
  
  return (
    <div className={`mb-4 ${containerClassName}`}>
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id={checkboxId}
            ref={ref}
            type="checkbox"
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className={`
              h-4 w-4 rounded border-gray-300 text-purple-600 
              focus:ring-purple-500 focus:ring-offset-0
              transition-colors duration-200
              disabled:opacity-50 disabled:cursor-not-allowed
              ${error ? 'border-red-500' : ''}
              ${className}
            `}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={error ? `${checkboxId}-error` : helperText ? `${checkboxId}-helper` : undefined}
            {...props}
          />
        </div>
        <div className="ml-3 text-sm">
          <label 
            htmlFor={checkboxId}
            className={`font-medium text-gray-700 ${disabled ? 'opacity-50' : ''} ${labelClassName}`}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
          
          {helperText && !error && (
            <p id={`${checkboxId}-helper`} className="text-gray-500 mt-1">
              {helperText}
            </p>
          )}
          
          {error && (
            <p 
              id={`${checkboxId}-error`} 
              className="text-red-600 mt-1"
              role="alert"
            >
              {error}
            </p>
          )}
        </div>
      </div>
    </div>
  );
});

SimpleCheckbox.displayName = 'SimpleCheckbox';

export default SimpleCheckbox;
