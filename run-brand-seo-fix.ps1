# run-brand-seo-fix.ps1
# PowerShell script to implement all brand search visibility fixes

Write-Host "HouseGoing Brand Search Visibility Fix" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "This script will apply all recommended fixes to improve brand search visibility." -ForegroundColor Yellow
Write-Host ""

# Function to check if a command exists
function Test-CommandExists {
    param ($command)
    $exists = $null -ne (Get-Command $command -ErrorAction SilentlyContinue)
    return $exists
}

# Check if Node.js is installed
if (-not (Test-CommandExists "node")) {
    Write-Host "Error: Node.js is not installed or not in PATH. Please install Node.js and try again." -ForegroundColor Red
    exit 1
}

# Check if required scripts exist
$scripts = @(
    ".\scripts\canonical-fix.js",
    ".\scripts\soft-404-fixer.js",
    ".\scripts\redirect-chain-analyzer.js",
    ".\scripts\submit-sitemap.js"
)

foreach ($script in $scripts) {
    if (-not (Test-Path $script)) {
        Write-Host "Warning: Script not found at $script" -ForegroundColor Yellow
    }
}

# Step 1: Run existing SEO scripts
Write-Host "Step 1: Running existing SEO fix scripts..." -ForegroundColor Green

try {
    if (Test-Path ".\scripts\canonical-fix.js") {
        Write-Host "Running canonical tag fixes..." -ForegroundColor Yellow
        node .\scripts\canonical-fix.js
    }
    
    if (Test-Path ".\scripts\soft-404-fixer.js") {
        Write-Host "Checking for soft 404 pages..." -ForegroundColor Yellow
        node .\scripts\soft-404-fixer.js
    }
    
    if (Test-Path ".\scripts\redirect-chain-analyzer.js") {
        Write-Host "Analyzing redirect chains..." -ForegroundColor Yellow
        node .\scripts\redirect-chain-analyzer.js
    }
} catch {
    Write-Host "Error running SEO scripts: $_" -ForegroundColor Red
}

# Step 2: Verify sitemap includes brand pages
Write-Host "`nStep 2: Verifying brand pages in sitemap..." -ForegroundColor Green

$sitemapPath = ".\public\sitemap_comprehensive.xml"
if (Test-Path $sitemapPath) {
    $sitemapContent = Get-Content $sitemapPath -Raw
    $brandPages = @(
        "about-housegoing",
        "housegoing-brand.html",
        "housegoing-faq.html"
    )
    
    $allPagesFound = $true
    foreach ($page in $brandPages) {
        if ($sitemapContent -notmatch [regex]::Escape($page)) {
            Write-Host "Warning: $page not found in sitemap" -ForegroundColor Red
            $allPagesFound = $false
        } else {
            Write-Host "✓ $page found in sitemap" -ForegroundColor Green
        }
    }
    
    if ($allPagesFound) {
        Write-Host "All brand pages found in sitemap!" -ForegroundColor Green
    }
} else {
    Write-Host "Warning: Sitemap not found at $sitemapPath" -ForegroundColor Red
}

# Step 3: Check Netlify redirects
Write-Host "`nStep 3: Checking Netlify redirects..." -ForegroundColor Green

$netlifyTomlPath = ".\netlify.toml"
if (Test-Path $netlifyTomlPath) {
    $netlifyContent = Get-Content $netlifyTomlPath -Raw
    $redirectsToCheck = @(
        "/housegoing-brand",
        "/housegoing-faq",
        "/about-housegoing"
    )
    
    $allRedirectsFound = $true
    foreach ($redirect in $redirectsToCheck) {
        if ($netlifyContent -notmatch [regex]::Escape($redirect)) {
            Write-Host "Warning: Redirect for $redirect not found in netlify.toml" -ForegroundColor Red
            $allRedirectsFound = $false
        } else {
            Write-Host "✓ Redirect for $redirect found in netlify.toml" -ForegroundColor Green
        }
    }
    
    if ($allRedirectsFound) {
        Write-Host "All required redirects found in netlify.toml!" -ForegroundColor Green
    }
} else {
    Write-Host "Warning: netlify.toml not found at $netlifyTomlPath" -ForegroundColor Red
}

# Step 4: Verify brand pages exist
Write-Host "`nStep 4: Verifying brand pages exist..." -ForegroundColor Green

$brandFiles = @(
    ".\public\housegoing-brand.html",
    ".\public\housegoing-faq.html",
    ".\src\pages\AboutHouseGoing.jsx"
)

$allFilesExist = $true
foreach ($file in $brandFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "Warning: Brand file not found at $file" -ForegroundColor Red
        $allFilesExist = $false
    } else {
        Write-Host "✓ Brand file found at $file" -ForegroundColor Green
    }
}

if ($allFilesExist) {
    Write-Host "All brand files exist!" -ForegroundColor Green
}

# Step 5: Prompt to run sitemap submission tool
Write-Host "`nStep 5: Submit updated sitemap to Google" -ForegroundColor Green
$runSubmitTool = Read-Host "Do you want to run the sitemap submission tool now? (y/n)"

if ($runSubmitTool -eq "y") {
    Write-Host "Running sitemap submission tool..." -ForegroundColor Yellow
    node .\scripts\submit-sitemap.js
} else {
    Write-Host "Skipping sitemap submission. You can run it later with: node .\scripts\submit-sitemap.js" -ForegroundColor Yellow
}

# Final steps and summary
Write-Host "`n====================================" -ForegroundColor Cyan
Write-Host "Brand Search Visibility Fix Complete!" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "`nNext steps:"
Write-Host "1. Deploy the changes to your production site" -ForegroundColor Yellow
Write-Host "2. Submit your sitemap to Google Search Console" -ForegroundColor Yellow
Write-Host "3. Request indexing for your brand pages in Google Search Console" -ForegroundColor Yellow
Write-Host "4. Monitor your brand search visibility weekly" -ForegroundColor Yellow
Write-Host "`nFor detailed guidance, see docs\brand-search-status-update.md" -ForegroundColor Green
