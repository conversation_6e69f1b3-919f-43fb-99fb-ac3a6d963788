/**
 * WFS API Proxy
 *
 * This API endpoint acts as a proxy for W<PERSON> (Web Feature Service) requests to the NSW Planning Portal.
 * It helps avoid CORS issues when making requests from the browser.
 */

export default async function handler(req, res) {
  // Handle both GET and POST requests
  const isPost = req.method === 'POST';

  // Extract parameters based on request method
  const { serviceUrl, params } = isPost ? req.body : req.query;

  if (!serviceUrl) {
    return res.status(400).json({ error: 'serviceUrl parameter is required' });
  }

  try {
    // Build the full URL with parameters
    let fullUrl = serviceUrl;

    if (params) {
      // For POST requests, params is an object
      // For GET requests, params might be a string that needs parsing
      const searchParams = new URLSearchParams(
        typeof params === 'string' ? JSON.parse(params) : params
      );

      fullUrl += `?${searchParams.toString()}`;
    }

    console.log(`Proxying WFS request to: ${fullUrl}`);

    // Make the request to the WFS service
    const response = await fetch(fullUrl, {
      method: 'GET', // Always use GET for WFS
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`WFS request failed with status: ${response.status} - ${errorText}`);
    }

    // Get the response data
    const data = await response.json();

    // Return the data
    return res.status(200).json(data);
  } catch (error) {
    console.error('WFS proxy error:', error);
    return res.status(500).json({ error: error.message });
  }
}
