/**
 * Setup Secure Cloudinary Configuration
 * Creates upload presets for secure document verification
 */

import https from 'https';
import { stringify } from 'querystring';
import { config } from 'dotenv';

// Load environment variables
config();

const cloudName = process.env.CLOUDINARY_CLOUD_NAME || 'dcdjxfnud';
const apiKey = process.env.CLOUDINARY_API_KEY || '565724643666687';
const apiSecret = process.env.CLOUDINARY_API_SECRET;

if (!apiSecret) {
  console.error('❌ CLOUDINARY_API_SECRET environment variable is required');
  console.log('Please add your Cloudinary API secret to your .env file:');
  console.log('CLOUDINARY_API_SECRET=your_secret_here');
  process.exit(1);
}

console.log('🔧 Setting up secure Cloudinary configuration...');
console.log(`Cloud Name: ${cloudName}`);
console.log(`API Key: ${apiKey}`);

// Create secure upload preset for document verification
const createSecureUploadPreset = (presetName, folder, options = {}) => {
  return new Promise((resolve, reject) => {
    const data = stringify({
      name: presetName,
      unsigned: false, // Requires signature for security
      folder: folder,
      allowed_formats: 'jpg,png,jpeg,pdf',
      max_file_size: 10000000, // 10MB
      access_mode: 'authenticated', // Secure access
      type: 'authenticated',
      use_filename: false,
      unique_filename: true,
      overwrite: false,
      resource_type: 'auto',
      ...options
    });

    const requestOptions = {
      hostname: 'api.cloudinary.com',
      path: `/v1_1/${cloudName}/upload_presets`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': data.length,
        'Authorization': 'Basic ' + Buffer.from(apiKey + ':' + apiSecret).toString('base64')
      }
    };

    const req = https.request(requestOptions, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          if (res.statusCode === 200) {
            console.log(`✅ Created upload preset: ${presetName}`);
            resolve(result);
          } else if (res.statusCode === 400 && result.error && result.error.message.includes('already exists')) {
            console.log(`ℹ️  Upload preset "${presetName}" already exists`);
            resolve({ name: presetName, exists: true });
          } else {
            console.error(`❌ Error creating preset ${presetName}:`, result);
            reject(new Error(result.error?.message || 'Unknown error'));
          }
        } catch (error) {
          console.error(`❌ Error parsing response for ${presetName}:`, error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Request error for ${presetName}:`, error);
      reject(error);
    });

    req.write(data);
    req.end();
  });
};

// Create transformation preset for document processing
const createTransformationPreset = (presetName, transformations) => {
  return new Promise((resolve, reject) => {
    const data = stringify({
      name: presetName,
      transformation: transformations
    });

    const requestOptions = {
      hostname: 'api.cloudinary.com',
      path: `/v1_1/${cloudName}/image/upload_presets`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': data.length,
        'Authorization': 'Basic ' + Buffer.from(apiKey + ':' + apiSecret).toString('base64')
      }
    };

    const req = https.request(requestOptions, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          if (res.statusCode === 200) {
            console.log(`✅ Created transformation preset: ${presetName}`);
            resolve(result);
          } else {
            console.error(`❌ Error creating transformation preset ${presetName}:`, result);
            reject(new Error(result.error?.message || 'Unknown error'));
          }
        } catch (error) {
          console.error(`❌ Error parsing transformation response for ${presetName}:`, error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Transformation request error for ${presetName}:`, error);
      reject(error);
    });

    req.write(data);
    req.end();
  });
};

// Setup function
const setupCloudinary = async () => {
  try {
    console.log('\n📋 Creating upload presets...\n');

    // 1. Secure document verification preset
    await createSecureUploadPreset('housegoing_verification_secure', 'verification/documents', {
      access_mode: 'authenticated',
      type: 'authenticated',
      tags: 'verification,secure,documents'
    });

    // 2. Property images preset (existing, but ensure it exists)
    await createSecureUploadPreset('housegoing_uploads', 'property-images', {
      access_mode: 'public',
      type: 'upload',
      tags: 'property,images'
    });

    // 3. Profile pictures preset
    await createSecureUploadPreset('housegoing_profiles', 'user-profiles', {
      access_mode: 'public',
      type: 'upload',
      tags: 'profile,avatar',
      transformation: [
        { width: 400, height: 400, crop: 'fill', gravity: 'face' },
        { quality: 'auto', fetch_format: 'auto' }
      ]
    });

    // 4. Backup documents preset (for when main storage fails)
    await createSecureUploadPreset('housegoing_backup_docs', 'backup/documents', {
      access_mode: 'authenticated',
      type: 'authenticated',
      tags: 'backup,documents,verification'
    });

    console.log('\n🔐 Setting up access control...\n');

    // Note: Access control is handled by the authenticated upload type
    // and the signed URLs generated by our Netlify functions

    console.log('\n✅ Cloudinary setup completed successfully!\n');
    
    console.log('📝 Configuration Summary:');
    console.log('- Secure document uploads: ✅ Enabled');
    console.log('- Authenticated access: ✅ Enabled');
    console.log('- Backup storage: ✅ Configured');
    console.log('- File size limit: 10MB');
    console.log('- Allowed formats: JPG, PNG, JPEG, PDF');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Add CLOUDINARY_API_SECRET to your Netlify environment variables');
    console.log('2. Deploy the Netlify functions for signature generation');
    console.log('3. Run the SQL script to create verification tables in Supabase');
    console.log('4. Test the document upload functionality');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
};

// Test Cloudinary connection
const testConnection = () => {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: 'api.cloudinary.com',
      path: `/v1_1/${cloudName}/resources/image`,
      method: 'GET',
      headers: {
        'Authorization': 'Basic ' + Buffer.from(apiKey + ':' + apiSecret).toString('base64')
      }
    };

    const req = https.request(requestOptions, (res) => {
      if (res.statusCode === 200) {
        console.log('✅ Cloudinary connection successful');
        resolve(true);
      } else {
        console.error('❌ Cloudinary connection failed:', res.statusCode);
        reject(new Error(`Connection failed: ${res.statusCode}`));
      }
    });

    req.on('error', (error) => {
      console.error('❌ Connection error:', error);
      reject(error);
    });

    req.end();
  });
};

// Run setup
const main = async () => {
  try {
    console.log('🧪 Testing Cloudinary connection...\n');
    await testConnection();
    
    console.log('\n🚀 Starting Cloudinary setup...\n');
    await setupCloudinary();
    
  } catch (error) {
    console.error('\n💥 Setup failed:', error.message);
    console.log('\n🔍 Troubleshooting:');
    console.log('1. Check your CLOUDINARY_API_SECRET is correct');
    console.log('2. Verify your Cloudinary account has API access enabled');
    console.log('3. Ensure your API key and cloud name are correct');
    process.exit(1);
  }
};

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  setupCloudinary,
  createSecureUploadPreset,
  testConnection
};
