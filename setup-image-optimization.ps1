# Install and configure image optimization tools for the project

# Define necessary packages
$imageOptimizationPackages = @(
    "vite-plugin-imagemin",
    "imagemin-mozjpeg",
    "imagemin-pngquant",
    "imagemin-svgo"
)

# Step 1: Install required packages
Write-Host "Installing image optimization packages..."
npm install --save-dev $imageOptimizationPackages

# Step 2: Update vite.config.ts to include image optimization
Write-Host "Updating Vite configuration for image optimization..."

# Check if vite.config.ts exists
if (Test-Path -Path ".\vite.config.ts") {
    # Read the current vite config
    $viteConfig = Get-Content -Path ".\vite.config.ts" -Raw
    
    # Check if imagemin is already configured
    if ($viteConfig -match "vite-plugin-imagemin") {
        Write-Host "Image optimization is already configured in vite.config.ts"
    } else {
        # Create backup of original file
        Copy-Item -Path ".\vite.config.ts" -Destination ".\vite.config.ts.bak" -Force
        Write-Host "Created backup of vite.config.ts at vite.config.ts.bak"
        
        # Add the import statement for imagemin
        $viteConfig = $viteConfig -replace "import dotenv from 'dotenv';", "import dotenv from 'dotenv';`nimport imagemin from 'vite-plugin-imagemin';"
        
        # Add the imagemin plugin configuration
        $viteConfig = $viteConfig -replace "plugins: \[react\(\)\],", @"
plugins: [
    react(),
    imagemin({
      gifsicle: {
        optimizationLevel: 7,
        interlaced: false,
      },
      optipng: {
        optimizationLevel: 7,
      },
      mozjpeg: {
        quality: 80,
      },
      pngquant: {
        quality: [0.8, 0.9],
        speed: 4,
      },
      svgo: {
        plugins: [
          {
            name: 'removeViewBox',
            active: false,
          },
          {
            name: 'removeEmptyAttrs',
            active: false,
          },
        ],
      },
    }),
  ],
"@
        
        # Write the updated config back to the file
        Set-Content -Path ".\vite.config.ts" -Value $viteConfig
        Write-Host "Updated vite.config.ts with image optimization configuration"
    }
} else {
    Write-Host "vite.config.ts not found. Please check your project structure."
}

# Step 3: Create an image optimization service for dynamic images
Write-Host "Creating image optimization service for dynamic images..."

$imageServiceContent = @"
/**
 * Image Optimization Service
 * 
 * This service provides utilities for optimizing and loading images efficiently.
 * It works with both static assets and Cloudinary-based dynamic images.
 */

interface ImageDimensions {
  width: number;
  height?: number;
}

interface ImageOptions {
  quality?: number;
  format?: 'webp' | 'avif' | 'jpg' | 'png' | 'auto';
  blur?: number;
}

/**
 * Generate an optimized image URL for Cloudinary
 */
export function getOptimizedCloudinaryUrl(
  imageUrl: string,
  dimensions: ImageDimensions,
  options: ImageOptions = {}
): string {
  if (!imageUrl || !imageUrl.includes('cloudinary.com')) {
    return imageUrl; // Return original if not a Cloudinary URL
  }

  const { width, height } = dimensions;
  const { quality = 80, format = 'auto', blur = 0 } = options;

  // Find the upload part of the URL to insert transformations
  const uploadIndex = imageUrl.indexOf('/upload/');
  if (uploadIndex === -1) return imageUrl;

  // Build the transformation string
  const transformations = [
    'c_fill', // Crop mode
    \`w_\${width}\`,
    height ? \`h_\${height}\` : '',
    \`q_\${quality}\`,
    format !== 'auto' ? \`f_\${format}\` : 'f_auto',
    blur > 0 ? \`e_blur:\${blur}\` : '',
  ].filter(Boolean).join(',');

  // Insert transformations
  const optimizedUrl = \`\${imageUrl.substring(0, uploadIndex + 8)}\${transformations}/\${imageUrl.substring(uploadIndex + 8)}\`;
  
  return optimizedUrl;
}

/**
 * Generate srcSet for responsive images
 */
export function generateSrcSet(
  imageUrl: string,
  options: ImageOptions = {}
): string {
  if (!imageUrl) return '';

  const widths = [320, 640, 960, 1280, 1920];
  
  return widths
    .map(width => {
      const optimizedUrl = getOptimizedCloudinaryUrl(
        imageUrl,
        { width },
        options
      );
      return \`\${optimizedUrl} \${width}w\`;
    })
    .join(', ');
}

/**
 * Get correct image dimensions based on aspect ratio
 */
export function calculateAspectRatioDimensions(
  width: number,
  aspectRatio: number
): ImageDimensions {
  return {
    width,
    height: Math.round(width / aspectRatio)
  };
}

/**
 * Generate a low-quality image placeholder
 */
export function getLowQualityPlaceholder(imageUrl: string): string {
  return getOptimizedCloudinaryUrl(
    imageUrl,
    { width: 20 },
    { quality: 20, blur: 10 }
  );
}
"@

# Create the service file
$imageServicePath = ".\src\services\imageOptimizationService.ts"
Set-Content -Path $imageServicePath -Value $imageServiceContent
Write-Host "Created image optimization service at $imageServicePath"

# Step 4: Create an optimized image component
Write-Host "Creating optimized image component..."

$imageComponentContent = @"
/**
 * OptimizedImage Component
 * 
 * A React component for displaying optimized images with lazy loading,
 * responsive sizing, and modern image formats.
 */

import React, { useState, useEffect } from 'react';
import { getOptimizedCloudinaryUrl, generateSrcSet } from '../services/imageOptimizationService';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  placeholder?: 'blur' | 'empty';
  onLoad?: () => void;
  onError?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 80,
  objectFit = 'cover',
  placeholder = 'empty',
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);

  // Generate optimized image URLs
  const optimizedSrc = getOptimizedCloudinaryUrl(
    src,
    { width, height },
    { quality }
  );
  
  const srcSet = generateSrcSet(src, { quality });

  // Handle image load event
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // Handle image error event
  const handleError = () => {
    setError(true);
    if (onError) onError();
  };

  // Determine placeholder style
  const showPlaceholder = placeholder === 'blur' && !isLoaded;
  const placeholderStyle = showPlaceholder
    ? {
        filter: 'blur(20px)',
        transition: 'filter 0.3s ease-out',
      }
    : {};

  return (
    <div 
      className={\`relative overflow-hidden \${className}\`} 
      style={{ width, height: height || 'auto' }}
    >
      {error ? (
        <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
          <span>Image not available</span>
        </div>
      ) : (
        <img
          src={optimizedSrc}
          srcSet={srcSet}
          sizes={\`(max-width: \${width}px) 100vw, \${width}px\`}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          onLoad={handleLoad}
          onError={handleError}
          className={\`w-full h-full transition-opacity duration-300 \${isLoaded ? 'opacity-100' : 'opacity-0'}\`}
          style={{
            objectFit,
            ...placeholderStyle,
          }}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
"@

# Create the component file
$imageComponentPath = ".\src\components\ui\OptimizedImage.tsx"
Set-Content -Path $imageComponentPath -Value $imageComponentContent
Write-Host "Created optimized image component at $imageComponentPath"

# Step 5: Create documentation on image optimization
Write-Host "Creating image optimization guide..."

$optimizationGuideContent = @"
# Image Optimization Guide for HouseGoing

This guide outlines best practices for optimizing images in the HouseGoing website to improve performance.

## Tools Implemented

1. **vite-plugin-imagemin**: Automatically optimizes images during build
2. **ImageOptimizationService**: Service for generating optimized image URLs
3. **OptimizedImage Component**: React component for displaying optimized images

## Best Practices

### 1. Use the OptimizedImage Component

Always use the \`OptimizedImage\` component instead of standard \`<img>\` tags:

\`\`\`jsx
import OptimizedImage from '../components/ui/OptimizedImage';

// Instead of <img src="/image.jpg" alt="Description" />
<OptimizedImage 
  src="https://res.cloudinary.com/your-cloud-name/image/upload/v1234/image.jpg"
  alt="Description"
  width={800}
  height={600}
  quality={80}
/>
\`\`\`

### 2. Image Formats

- Use **SVG** for logos, icons, and simple illustrations
- Use **WebP** or **AVIF** for photographs (Cloudinary will serve these automatically)
- Avoid using PNG for photographs

### 3. Image Dimensions

- Never serve images larger than needed
- Use responsive images with appropriate sizes
- Maintain consistent aspect ratios

### 4. Performance Tips

- Set \`priority\` to \`true\` for above-the-fold images
- Use \`placeholder="blur"\` for important images
- Lazy load below-the-fold images (default behavior)

### 5. Cloudinary Best Practices

When uploading to Cloudinary:

- Use descriptive filenames
- Organize images in folders by purpose
- Apply appropriate tags for easier management

## Implementation Examples

### Venue Card Example

\`\`\`jsx
<OptimizedImage
  src={venue.images[0]}
  alt={venue.name}
  width={400}
  height={300}
  className="rounded-t-lg"
/>
\`\`\`

### Hero Image Example

\`\`\`jsx
<OptimizedImage
  src={heroImage}
  alt="HouseGoing Hero"
  width={1920}
  height={1080}
  priority={true}
  placeholder="blur"
  className="w-full"
/>
\`\`\`

## Further Optimization

Consider implementing:

1. Next-gen image formats (WebP, AVIF) for all images
2. Content-aware image cropping
3. Responsive art direction for different screen sizes
"@

# Create the guide file
$optimizationGuidePath = ".\docs\image-optimization-guide.md"
New-Item -ItemType Directory -Path ".\docs" -Force | Out-Null
Set-Content -Path $optimizationGuidePath -Value $optimizationGuideContent
Write-Host "Created image optimization guide at $optimizationGuidePath"

Write-Host "`nImage optimization setup completed successfully!"
Write-Host "1. Installed necessary packages"
Write-Host "2. Updated Vite configuration"
Write-Host "3. Created image optimization service"
Write-Host "4. Created optimized image component"
Write-Host "5. Created documentation"
Write-Host "`nPlease check the documentation at docs/image-optimization-guide.md for usage instructions."
