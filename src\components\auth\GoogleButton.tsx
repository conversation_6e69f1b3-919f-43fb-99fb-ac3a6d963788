import { useSignIn, useSignUp } from '@clerk/clerk-react';
import { useState } from 'react';
import { CLERK_CONFIG } from '../../config/clerk';

interface GoogleButtonProps {
  registrationType: 'host' | 'guest';
  className?: string;
  label?: string;
}

export default function GoogleButton({
  registrationType,
  className = '',
  label = registrationType === 'host' ? 'Continue with Google (Owner Portal)' : 'Continue with Google'
}: GoogleButtonProps) {
  // Use Clerk's built-in hooks for sign-in and sign-up
  const { signIn, isLoaded: isSignInLoaded } = useSignIn();
  const { signUp, isLoaded: isSignUpLoaded } = useSignUp();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if ((registrationType === 'host' && !isSignUpLoaded) ||
        (registrationType === 'guest' && !isSignInLoaded)) {
      alert('Authentication system is still initializing. Please try again.');
      return;
    }

    setIsLoading(true);

    try {
      // Clear any existing auth flags to start fresh
      localStorage.removeItem('auth_success');
      localStorage.removeItem('auth_success_time');
      localStorage.removeItem('auth_processing');
      localStorage.removeItem('reload_prevented');
      localStorage.removeItem('clerk_user_email');
      localStorage.removeItem('clerk_user_id');
      localStorage.removeItem('clerk_user_name');
      localStorage.removeItem('clerk_auth_time');

      // Store the registration type in localStorage
      localStorage.setItem('registering_as_host', registrationType === 'host' ? 'true' : 'false');
      localStorage.setItem('auth_user_type', registrationType);
      localStorage.setItem('user_role', registrationType === 'host' ? 'host' : 'guest');

      // Store redirect information
      localStorage.setItem('auth_redirect_to', registrationType === 'host' ? '/host/dashboard' : '/');

      // Determine the appropriate redirect URL based on registration type
      const redirectUrl = registrationType === 'host'
        ? '/auth/callback?destination=host-portal'
        : '/auth/callback';

      // Log the OAuth attempt for debugging
      console.log('Starting Google OAuth flow:', {
        registrationType,
        redirectUrl,
        isSignInLoaded,
        isSignUpLoaded,
        hostname: window.location.hostname,
        developmentMode: CLERK_CONFIG.developmentMode
      });

      // Ensure signIn/signUp are defined
      if ((registrationType === 'host' && !signUp) ||
          (registrationType === 'guest' && !signIn)) {
        throw new Error('Authentication system not properly initialized');
      }

      // Store additional debug information
      sessionStorage.setItem('last_oauth_attempt', JSON.stringify({
        timestamp: new Date().toISOString(),
        registrationType,
        redirectUrl,
        method: 'clerk-native',
        origin: window.location.origin
      }));

      // In development mode or on localhost, we'll use a special approach to handle the domain mismatch
      if (CLERK_CONFIG.developmentMode) {
        console.log('Development mode detected - using development fallback for Google OAuth');

        // Generate a unique ID for this session
        const uniqueId = `dev-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        // Create more realistic mock user data
        const mockEmail = `dev-${registrationType}-${uniqueId}@housegoing.example.com`;
        const mockUserId = `dev-user-${uniqueId}`;
        const mockUserName = registrationType === 'host' ? 'Development Host' : 'Development User';

        // Store the user data in localStorage
        localStorage.setItem('user_role', registrationType === 'host' ? 'host' : 'guest');
        localStorage.setItem('clerk_user_email', mockEmail);
        localStorage.setItem('clerk_user_id', mockUserId);
        localStorage.setItem('clerk_user_name', mockUserName);
        localStorage.setItem('clerk_auth_time', new Date().toISOString());

        // Set auth success flags
        localStorage.setItem('auth_success', 'true');
        localStorage.setItem('auth_success_time', new Date().toISOString());

        // Create a mock user object that mimics Clerk's user structure
        const mockClerkUser = {
          id: mockUserId,
          firstName: registrationType === 'host' ? 'Development' : 'Development',
          lastName: registrationType === 'host' ? 'Host' : 'User',
          primaryEmailAddress: { emailAddress: mockEmail },
          createdAt: new Date().toISOString(),
          metadata: {
            role: registrationType,
            is_host: registrationType === 'host'
          }
        };

        // Dispatch auth complete event with the mock user
        window.dispatchEvent(new CustomEvent('auth_complete', {
          detail: {
            success: true,
            provider: 'clerk-dev',
            user: mockClerkUser
          }
        }));

        // In development mode, we'll redirect to the callback handler first
        // This ensures the proper flow is followed
        const callbackUrl = registrationType === 'host'
          ? '/auth/callback?destination=host-portal&dev_mode=true'
          : '/auth/callback?dev_mode=true';

        console.log('Redirecting to development callback:', callbackUrl);

        // Add a small delay to ensure the event is processed
        setTimeout(() => {
          window.location.href = callbackUrl;
        }, 500);

        return;
      }

      // Use the appropriate method based on the current page context
      // If we're on a sign-up page, use signUp; if on sign-in page, use signIn
      const isSignUpPage = window.location.pathname.includes('/signup') ||
                          window.location.pathname.includes('/host/signup');

      console.log(`Starting ${registrationType} Google OAuth flow with Clerk...`, {
        isSignUpPage,
        currentPath: window.location.pathname
      });

      if (isSignUpPage) {
        // Use signUp for new user registration
        if (!signUp) {
          throw new Error('Sign up functionality not available');
        }

        console.log('Using signUp flow for new user registration');
        await signUp.authenticateWithRedirect({
          strategy: 'oauth_google',
          redirectUrl,
          redirectUrlComplete: registrationType === 'host' ? '/host/dashboard' : '/',
          unsafeMetadata: {
            role: registrationType,
            isHost: registrationType === 'host'
          }
        });
      } else {
        // Use signIn for existing users, with fallback to signUp for new users
        if (!signIn) {
          throw new Error('Sign in functionality not available');
        }

        try {
          console.log('Using signIn flow for existing users');
          await signIn.authenticateWithRedirect({
            strategy: 'oauth_google',
            redirectUrl,
            redirectUrlComplete: registrationType === 'host' ? '/host/dashboard' : '/',
          });
        } catch (signInError) {
          console.log('SignIn failed, trying signUp for new user:', signInError);

          // If signIn fails and signUp is available, try signUp for new users
          if (signUp) {
            await signUp.authenticateWithRedirect({
              strategy: 'oauth_google',
              redirectUrl,
              redirectUrlComplete: registrationType === 'host' ? '/host/dashboard' : '/',
              unsafeMetadata: {
                role: registrationType,
                isHost: registrationType === 'host'
              }
            });
          } else {
            throw signInError;
          }
        }
      }
    } catch (error) {
      console.error('Error initiating Google OAuth:', error);
      setIsLoading(false);

      // Only show alert if we're actually on an auth page, not on homepage
      const isAuthPage = window.location.pathname.includes('/login') ||
                        window.location.pathname.includes('/signup') ||
                        window.location.pathname.includes('/auth');

      if (isAuthPage) {
        alert('There was a problem connecting to Google. Please try again or use email sign-in instead.');
      }
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`flex items-center justify-center w-full mx-auto px-4 py-3
        border border-gray-300 rounded-md
        text-sm font-medium text-gray-700
        bg-white hover:bg-gray-50
        transition-colors relative
        ${isLoading ? 'opacity-75 cursor-not-allowed' : ''}
        ${className}`}
      type="button"
    >
      {isLoading ? (
        <>
          <svg className="animate-spin h-5 w-5 mr-3 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Connecting to Google...</span>
        </>
      ) : (
        <>
          <GoogleIcon className="w-5 h-5 mr-3" />
          <span>{label}</span>
        </>
      )}
    </button>
  );
}

// Google icon component
function GoogleIcon({ className = '' }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path
        fill="#4285F4"
        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      />
      <path
        fill="#34A853"
        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      />
      <path
        fill="#FBBC05"
        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      />
      <path
        fill="#EA4335"
        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      />
    </svg>
  );
}
