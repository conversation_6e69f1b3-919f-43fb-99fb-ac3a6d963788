  // Render Basic Info section
  const renderBasicInfo = () => (
    <div className="border-b pb-6 mb-6">
      <h2 className="text-xl font-bold mb-4">Basic Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block mb-2 font-medium">Australian Phone Number*</label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Phone className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => setFormData({...formData, phoneNumber: e.target.value})}
              className="pl-10 w-full p-2 border rounded"
              placeholder="e.g. 0412 345 678"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Enter your Australian mobile or landline number</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Venue Name*</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            className="w-full p-2 border rounded"
            placeholder="e.g. Harbour View Terrace"
          />
        </div>

        <div>
          <label className="block mb-2 font-medium">Venue Type*</label>
          <select
            value={formData.type}
            onChange={(e) => setFormData({...formData, type: e.target.value})}
            className="w-full p-2 border rounded"
          >
            <option value="house">House</option>
            <option value="apartment">Apartment</option>
            <option value="backyard">Backyard</option>
            <option value="rooftop">Rooftop</option>
            <option value="commercial">Commercial Space</option>
            <option value="function">Function Room</option>
            <option value="studio">Studio</option>
            <option value="warehouse">Warehouse</option>
            <option value="bar">Bar</option>
            <option value="restaurant">Restaurant</option>
          </select>
        </div>

        <div>
          <label className="block mb-2 font-medium">Address*</label>
          <AddressLookup
            value={formData.address}
            onChange={(address, location) => {
              setFormData({
                ...formData,
                address,
                location: location as LatLngTuple
              });
              setShowMap(true);
            }}
            placeholder="Enter your venue address"
            className="w-full p-2 border rounded"
          />
          <button
            type="button"
            onClick={() => setShowMap(!showMap)}
            className="mt-2 text-sm text-blue-600"
          >
            {showMap ? 'Hide Map' : 'Show Map'}
          </button>
        </div>
      </div>

      <div className="mt-6 bg-amber-50 border border-amber-200 rounded-md p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">Important Notice</h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                HouseGoing is a party-focused platform. By listing your venue, you acknowledge that it may be used for parties and events.
                If your space doesn't allow parties, you can still submit your listing, but it may not be approved for the platform.
              </p>
            </div>
            <div className="mt-4">
              <div className="flex items-center">
                <input
                  id="party-acknowledgment"
                  type="checkbox"
                  checked={formData.partyAcknowledgment}
                  onChange={(e) => setFormData({...formData, partyAcknowledgment: e.target.checked})}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <label htmlFor="party-acknowledgment" className="ml-2 block text-sm text-amber-700">
                  I understand that HouseGoing is a party-focused platform
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {showMap && (
        <div className="h-64 border rounded overflow-hidden mt-4">
          <React.Suspense fallback={<div className="h-64 bg-gray-100 rounded flex items-center justify-center">Loading map...</div>}>
            <LeafletMap
              center={formData.location}
              zoom={15}
              onClick={handleMapClick}
              ref={mapRef}
              className="h-full w-full"
            >
              <Marker position={formData.location}>
                <Popup>Venue Location</Popup>
              </Marker>
            </LeafletMap>
          </React.Suspense>
        </div>
      )}
    </div>
  );
  
  // Render Venue Details section
  const renderVenueDetails = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Venue Details</h2>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Venue Description*</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          className="w-full p-2 border rounded"
          rows={4}
          placeholder="Describe your venue, its atmosphere, and what makes it special..."
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block mb-2 font-medium">Venue Size (m²)*</label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <SquareIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="number"
              min="0"
              value={formData.size || ''}
              onChange={(e) => setFormData({...formData, size: parseInt(e.target.value) || 0})}
              className="pl-10 w-full p-2 border rounded"
              placeholder="e.g. 200 m²"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Enter the total area in square metres</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Function Rooms</label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Building className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="number"
              min="0"
              value={formData.functionRooms || ''}
              onChange={(e) => setFormData({...formData, functionRooms: parseInt(e.target.value) || 0})}
              className="pl-10 w-full p-2 border rounded"
              placeholder="e.g. 2"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Optional - number of private function rooms</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Event Spaces*</label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Ruler className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="number"
              min="1"
              value={formData.eventSpaces || ''}
              onChange={(e) => setFormData({...formData, eventSpaces: parseInt(e.target.value) || 0})}
              className="pl-10 w-full p-2 border rounded"
              placeholder="e.g. 1"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Number of separate event areas</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Maximum Guests*</label>
          <input
            type="number"
            min="1"
            value={formData.maxGuests || ''}
            onChange={(e) => setFormData({...formData, maxGuests: parseInt(e.target.value) || 0})}
            className="w-full p-2 border rounded"
            placeholder="e.g. 50"
          />
          <p className="text-xs text-gray-500 mt-1">Maximum number of guests allowed</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Hourly Rate (AUD)*</label>
          <input
            type="number"
            min="0"
            step="10"
            value={formData.price || ''}
            onChange={(e) => setFormData({...formData, price: parseInt(e.target.value) || 0})}
            className="w-full p-2 border rounded"
            placeholder="e.g. 150"
          />
          <p className="text-xs text-gray-500 mt-1">Hourly rate in Australian dollars</p>
        </div>
      </div>
    </div>
  );
