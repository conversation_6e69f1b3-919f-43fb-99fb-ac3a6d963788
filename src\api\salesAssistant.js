/**
 * API for the Sales Assistant
 */

// Base URL for the Sales Assistant API
const API_URL = 'https://housegoing.com.au/api/sales-assistant';

/**
 * Send a message to the Sales Assistant
 * @param {string} message - The message to send
 * @param {string} sessionId - The session ID (optional)
 * @returns {Promise<Object>} - The response from the Sales Assistant
 */
export async function sendMessage(message, sessionId) {
  try {
    const response = await fetch(`${API_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        sessionId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending message to Sales Assistant:', error);
    throw error;
  }
}

/**
 * Reset the conversation with the Sales Assistant
 * @param {string} sessionId - The session ID
 * @returns {Promise<Object>} - The response from the Sales Assistant
 */
export async function resetConversation(sessionId) {
  try {
    const response = await fetch(`${API_URL}/reset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error resetting conversation with Sales Assistant:', error);
    throw error;
  }
}

export default {
  sendMessage,
  resetConversation,
};
