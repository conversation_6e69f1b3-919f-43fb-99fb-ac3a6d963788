/**
 * NSW Planning Map Component
 * 
 * Integrates address lookup, mapping, and spatial data layers using:
 * - Leaflet.js for the map interface
 * - Mapbox tiles for the basemap
 * - Maps.co API for geocoding
 * - NSW Planning Portal WFS/WMS for zoning and LGA data
 */

import { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { geocodeLocation } from '../../services/geocoding';

// Mapbox access token
const MAPBOX_TOKEN = 'pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA';

// Default center coordinates (Sydney)
const DEFAULT_CENTER = [-33.8688, 151.2093];
const DEFAULT_ZOOM = 12;

// NSW Planning Portal WFS/WMS endpoints
const ZONING_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer';
const LGA_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer';
const ZONING_WMS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer';
const LGA_WMS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer';

// Layer styles
const zoningStyle = {
  color: '#ff7800',
  weight: 2,
  opacity: 0.65,
  fillOpacity: 0.2
};

const lgaStyle = {
  color: '#0078ff',
  weight: 2,
  opacity: 0.65,
  fillOpacity: 0.1
};

const NSWPlanningMap = ({ onDataLoaded }) => {
  const mapRef = useRef(null);
  const mapContainerRef = useRef(null);
  const markerRef = useRef(null);
  const zoningLayerRef = useRef(null);
  const lgaLayerRef = useRef(null);
  
  const [address, setAddress] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Initialize map
  useEffect(() => {
    if (!mapContainerRef.current || mapRef.current) return;

    // Create map instance
    mapRef.current = L.map(mapContainerRef.current).setView(DEFAULT_CENTER, DEFAULT_ZOOM);

    // Add Mapbox tile layer
    L.tileLayer(`https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=${MAPBOX_TOKEN}`, {
      attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
      maxZoom: 19
    }).addTo(mapRef.current);

    // Add WMS layers (initially not visible)
    const zoningWMS = L.tileLayer.wms(ZONING_WMS_URL, {
      layers: '2',
      transparent: true,
      format: 'image/png',
      opacity: 0.7
    });

    const lgaWMS = L.tileLayer.wms(LGA_WMS_URL, {
      layers: '1',
      transparent: true,
      format: 'image/png',
      opacity: 0.7
    });

    // Add layer control
    L.control.layers(
      {}, // Base layers (none additional)
      {
        'Zoning Overlay': zoningWMS,
        'LGA Boundaries': lgaWMS
      }
    ).addTo(mapRef.current);

    // Add scale control
    L.control.scale().addTo(mapRef.current);

    // Cleanup on unmount
    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, []);

  // Geocode address and update map
  const geocodeAddress = async (addressText) => {
    if (!addressText.trim() || !mapRef.current) return;

    setLoading(true);
    setError('');
    
    try {
      // Call Maps.co API via our geocoding service
      const result = await geocodeLocation(addressText);
      
      // Update map view
      mapRef.current.setView([result.lat, result.lng], 15);
      
      // Add or update marker
      if (markerRef.current) {
        markerRef.current.setLatLng([result.lat, result.lng]);
      } else {
        markerRef.current = L.marker([result.lat, result.lng])
          .addTo(mapRef.current)
          .bindPopup(result.displayName)
          .openPopup();
      }
      
      // Load zoning and LGA data
      loadZoningAndLGA(result.lat, result.lng);
      
      // Update search results
      setSearchResults({
        address: result.displayName,
        coordinates: { lat: result.lat, lng: result.lng }
      });
    } catch (error) {
      console.error('Geocoding error:', error);
      setError('Failed to find address. Please try a different search term.');
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  // Load zoning and LGA data using WFS
  const loadZoningAndLGA = async (lat, lng) => {
    try {
      // Clear previous layers
      if (zoningLayerRef.current) {
        mapRef.current.removeLayer(zoningLayerRef.current);
        zoningLayerRef.current = null;
      }
      
      if (lgaLayerRef.current) {
        mapRef.current.removeLayer(lgaLayerRef.current);
        lgaLayerRef.current = null;
      }
      
      // Fetch zoning data
      const zoningData = await fetchWFSData(
        ZONING_WFS_URL,
        'EPI_Primary_Planning_Layers:2',
        lat,
        lng
      );
      
      // Fetch LGA data
      const lgaData = await fetchWFSData(
        LGA_WFS_URL,
        'EDP_Administrative_Boundaries:1',
        lat,
        lng
      );
      
      // Process and display zoning data
      if (zoningData && zoningData.features && zoningData.features.length > 0) {
        zoningLayerRef.current = L.geoJSON(zoningData, {
          style: zoningStyle,
          onEachFeature: (feature, layer) => {
            const zoneCode = feature.properties.ZONE_CODE || 'Unknown';
            const zoneName = feature.properties.ZONE_NAME || 'Unknown Zone';
            layer.bindPopup(`<strong>Zone:</strong> ${zoneCode}<br><strong>Description:</strong> ${zoneName}`);
          }
        }).addTo(mapRef.current);
      }
      
      // Process and display LGA data
      if (lgaData && lgaData.features && lgaData.features.length > 0) {
        lgaLayerRef.current = L.geoJSON(lgaData, {
          style: lgaStyle,
          onEachFeature: (feature, layer) => {
            const lgaName = feature.properties.LGA_NAME || 'Unknown Council';
            layer.bindPopup(`<strong>Council:</strong> ${lgaName}`);
          }
        }).addTo(mapRef.current);
      }
      
      // Extract and return data for parent component
      const result = {
        coordinates: { lat, lng },
        zoning: zoningData?.features?.[0]?.properties ? {
          code: zoningData.features[0].properties.ZONE_CODE,
          name: zoningData.features[0].properties.ZONE_NAME
        } : null,
        lga: lgaData?.features?.[0]?.properties ? {
          name: lgaData.features[0].properties.LGA_NAME
        } : null
      };
      
      // Notify parent component
      if (onDataLoaded) {
        onDataLoaded(result);
      }
      
      return result;
    } catch (error) {
      console.error('Error loading spatial data:', error);
      setError('Failed to load zoning or council data.');
      return null;
    }
  };

  // Fetch WFS data from NSW Planning Portal
  const fetchWFSData = async (serviceUrl, typeName, lat, lng) => {
    try {
      // Use the proxy server to avoid CORS issues
      const response = await fetch('/api/wfs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serviceUrl,
          params: {
            service: 'WFS',
            version: '1.1.0',
            request: 'GetFeature',
            typeNames: typeName,
            outputFormat: 'application/json',
            CQL_FILTER: `INTERSECTS(Shape, POINT(${lng} ${lat}))`
          }
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`WFS request failed: ${errorText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error fetching WFS data from ${serviceUrl}:`, error);
      throw error;
    }
  };

  // Handle map click to select location
  useEffect(() => {
    if (!mapRef.current) return;
    
    const handleMapClick = (e) => {
      const { lat, lng } = e.latlng;
      
      // Add or update marker
      if (markerRef.current) {
        markerRef.current.setLatLng([lat, lng]);
      } else {
        markerRef.current = L.marker([lat, lng])
          .addTo(mapRef.current)
          .bindPopup(`Selected location: ${lat.toFixed(5)}, ${lng.toFixed(5)}`)
          .openPopup();
      }
      
      // Load zoning and LGA data
      loadZoningAndLGA(lat, lng);
      
      // Update search results
      setSearchResults({
        address: `Selected location: ${lat.toFixed(5)}, ${lng.toFixed(5)}`,
        coordinates: { lat, lng }
      });
    };
    
    mapRef.current.on('click', handleMapClick);
    
    return () => {
      if (mapRef.current) {
        mapRef.current.off('click', handleMapClick);
      }
    };
  }, [onDataLoaded]);

  // Handle address search form submission
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    geocodeAddress(address);
  };

  return (
    <div className="nsw-planning-map">
      <div className="search-container mb-4">
        <form onSubmit={handleSearchSubmit} className="flex gap-2">
          <input
            type="text"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="Enter NSW address..."
            className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            disabled={loading}
          />
          <button
            type="submit"
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
            disabled={loading || !address.trim()}
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </form>
        
        {error && (
          <div className="mt-2 text-red-600">{error}</div>
        )}
      </div>
      
      <div 
        ref={mapContainerRef} 
        className="map-container h-[500px] w-full rounded-lg overflow-hidden border border-gray-300"
      ></div>
      
      {searchResults && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md border border-gray-200">
          <h3 className="text-lg font-semibold mb-2">Location Information</h3>
          <p><strong>Address:</strong> {searchResults.address}</p>
          <p><strong>Coordinates:</strong> {searchResults.coordinates.lat.toFixed(5)}, {searchResults.coordinates.lng.toFixed(5)}</p>
        </div>
      )}
    </div>
  );
};

export default NSWPlanningMap;
