# fix-netlify-redirects.ps1
# A very simple script to fix the redirects in netlify.toml

Write-Host "Fixing Netlify redirects configuration..."

$netlifyPath = "netlify.toml"

if (Test-Path $netlifyPath) {
    $content = Get-Content $netlifyPath -Raw
    
    # Look for the problematic redirect
    if ($content -match '\[\[redirects\]\]\s+from\s+=\s+"\/about-housegoing"\s+to\s+=\s+"\/index.html"\s+status\s+=\s+200\s+force\s+=\s+true') {
        Write-Host "Found problematic about-housegoing redirect - removing it"
        
        # Simple replacement to comment out the problematic redirect
        $newContent = $content -replace '(\[\[redirects\]\]\s+from\s+=\s+"\/about-housegoing"\s+to\s+=\s+"\/index.html"\s+status\s+=\s+200\s+force\s+=\s+true)', '# REMOVED FOR INDEXING: $1'
        
        # Write the updated content back to the file
        Set-Content -Path $netlifyPath -Value $newContent
        
        Write-Host "Fixed Netlify redirects configuration - removed about-housegoing redirect"
    } else {
        Write-Host "Did not find problematic redirect in netlify.toml"
    }
} else {
    Write-Host "Error: netlify.toml not found"
}

Write-Host "`nNext steps:"
Write-Host "1. Deploy the changes to Netlify"
Write-Host "2. Submit your sitemap to Google Search Console"
Write-Host "3. Request indexing for your main pages"
