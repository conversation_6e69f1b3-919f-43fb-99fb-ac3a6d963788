import React from 'react';
import { useUser } from '@clerk/clerk-react';
import { isHost } from '../utils/user-roles';

export default function ProfilePage() {
  const { user, isLoaded } = useUser();

  if (!isLoaded) {
    return <div className="pt-32 flex justify-center">Loading...</div>;
  }

  if (!user) {
    return <div className="pt-32 flex justify-center">Please sign in to view your profile</div>;
  }

  const userIsHost = isHost(user);

  return (
    <div className="pt-32 px-4 max-w-4xl mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6">
        <div className="flex items-center space-x-4 mb-6">
          <img
            src={user.imageUrl}
            alt={user.fullName || 'User'}
            className="w-16 h-16 rounded-full"
          />
          <div>
            <h1 className="text-2xl font-bold">{user.fullName || 'User'}</h1>
            <p className="text-gray-600">{user.primaryEmailAddress?.emailAddress}</p>
            <div className="mt-1">
              <span className={`px-2 py-1 text-xs rounded-full ${userIsHost ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}>
                {userIsHost ? 'Host' : 'User'}
              </span>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <h2 className="text-lg font-semibold mb-4">Account Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Full Name</label>
              <div className="mt-1 text-gray-900">{user.fullName || 'Not provided'}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Email</label>
              <div className="mt-1 text-gray-900">{user.primaryEmailAddress?.emailAddress}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Account Created</label>
              <div className="mt-1 text-gray-900">
                {new Date(user.createdAt).toLocaleDateString()}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Last Updated</label>
              <div className="mt-1 text-gray-900">
                {new Date(user.updatedAt).toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>

        {userIsHost && (
          <div className="border-t mt-6 pt-4">
            <h2 className="text-lg font-semibold mb-4">Host Information</h2>
            <p className="text-gray-600">
              You are registered as a host. You can manage your venues and bookings from the host dashboard.
            </p>
            <button
              className="mt-4 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md"
              onClick={() => window.location.href = '/host/onboarding'}
            >
              Go to Host Dashboard
            </button>
          </div>
        )}

        <div className="border-t mt-6 pt-4">
          <h2 className="text-lg font-semibold mb-4">Account Management</h2>
          <div className="flex flex-wrap gap-4">
            <button
              className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md"
              onClick={() => window.location.href = '/settings'}
            >
              Account Settings
            </button>
            <button
              className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md"
              onClick={() => window.location.href = '/reset-password'}
            >
              Reset Password
            </button>
            {!userIsHost && (
              <button
                className="bg-purple-100 hover:bg-purple-200 text-purple-800 px-4 py-2 rounded-md"
                onClick={() => window.location.href = '/become-host'}
              >
                Become a Host
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
