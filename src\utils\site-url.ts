/**
 * Get the site URL based on the environment
 * 
 * This function returns the appropriate site URL for authentication redirects
 * based on whether we're in development or production.
 */
export function getSiteUrl(): string {
  // Check if we're in a production environment
  const isProduction = 
    window.location.hostname === 'housegoing.com.au' || 
    window.location.hostname === 'www.housegoing.com.au';
  
  // Use the production URL in production, otherwise use the current origin
  return isProduction 
    ? 'https://housegoing.com.au' 
    : window.location.origin;
}

/**
 * Get the authentication callback URL
 * 
 * This function returns the full URL for authentication callbacks
 */
export function getAuthCallbackUrl(): string {
  return `${getSiteUrl()}/auth/callback`;
}
