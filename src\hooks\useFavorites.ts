import { useState, useEffect } from 'react';
import { useAuth } from '../providers/AuthProvider';
import { getSavedVenues, saveVenue, removeSavedVenue } from '../api/userAccount';

export function useFavorites() {
  const [favorites, setFavorites] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      loadFavorites();
    }
  }, [user]);

  const loadFavorites = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const savedVenues = await getSavedVenues(user.id);
      const favoriteIds = savedVenues.map(sv => sv.venue_id);
      setFavorites(favoriteIds);
    } catch (error) {
      console.error('Error loading favorites:', error);
      // Fallback to localStorage for development
      const storedFavorites = localStorage.getItem(`favorites_${user.id}`);
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      }
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (venueId: string) => {
    if (!user) return;

    const isFavorite = favorites.includes(venueId);

    try {
      if (isFavorite) {
        await removeSavedVenue(user.id, venueId);
        setFavorites(prev => prev.filter(id => id !== venueId));
      } else {
        await saveVenue(user.id, venueId);
        setFavorites(prev => [...prev, venueId]);
      }

      // Also update localStorage as backup
      const newFavorites = isFavorite
        ? favorites.filter(id => id !== venueId)
        : [...favorites, venueId];
      localStorage.setItem(`favorites_${user.id}`, JSON.stringify(newFavorites));
    } catch (error) {
      console.error('Error toggling favorite:', error);
      // Fallback to localStorage only
      const newFavorites = isFavorite
        ? favorites.filter(id => id !== venueId)
        : [...favorites, venueId];
      setFavorites(newFavorites);
      localStorage.setItem(`favorites_${user.id}`, JSON.stringify(newFavorites));
    }
  };

  return { favorites, toggleFavorite, loading };
}