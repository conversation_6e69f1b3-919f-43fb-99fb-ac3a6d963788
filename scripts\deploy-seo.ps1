# HouseGoing SEO Deployment Script (PowerShell)
# This script should be run after each deployment to ensure optimal SEO

param(
    [switch]$Verbose
)

Write-Host "🚀 HouseGoing SEO Deployment Script" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

try {
    # Step 1: Generate fresh sitemap
    Write-Status "Generating fresh sitemap..."
    $result = & node scripts/generate-sitemap.js
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Sitemap generated successfully"
    } else {
        Write-Error "Failed to generate sitemap"
        exit 1
    }

    # Step 2: Validate sitemap files
    Write-Status "Validating sitemap files..."
    $requiredFiles = @("sitemap.xml", "sitemap_index.xml", "sitemap_main.xml", "sitemap_blog.xml", "sitemap_venues.xml")
    
    foreach ($file in $requiredFiles) {
        $filePath = "public/$file"
        if (Test-Path $filePath) {
            Write-Success "✓ $file exists"
        } else {
            Write-Error "✗ $file missing"
            exit 1
        }
    }

    # Step 3: Check robots.txt
    Write-Status "Checking robots.txt..."
    if (Test-Path "public/robots.txt") {
        Write-Success "✓ robots.txt exists"
        
        # Check if it contains sitemap references
        $robotsContent = Get-Content "public/robots.txt" -Raw
        $sitemapCount = ($robotsContent | Select-String "Sitemap:" -AllMatches).Matches.Count
        if ($sitemapCount -gt 0) {
            Write-Success "robots.txt contains $sitemapCount sitemap references"
        } else {
            Write-Warning "robots.txt does not contain sitemap references"
        }
    } else {
        Write-Error "✗ robots.txt missing"
        exit 1
    }

    # Step 4: Count total URLs
    Write-Status "Counting sitemap URLs..."
    $totalUrls = 0
    
    $sitemapFiles = @("sitemap_main.xml", "sitemap_blog.xml", "sitemap_venues.xml")
    foreach ($file in $sitemapFiles) {
        $filePath = "public/$file"
        if (Test-Path $filePath) {
            $content = Get-Content $filePath -Raw
            $count = ($content | Select-String "<loc>" -AllMatches).Matches.Count
            $totalUrls += $count
            Write-Status "  $file`: $count URLs"
        }
    }
    
    Write-Success "Total URLs in sitemap: $totalUrls"

    # Step 5: SEO recommendations
    Write-Status "SEO Deployment Checklist:"
    Write-Host "  □ Submit updated sitemap to Google Search Console"
    Write-Host "  □ Submit updated sitemap to Bing Webmaster Tools"
    Write-Host "  □ Check Google Search Console for crawl errors"
    Write-Host "  □ Monitor indexing status over next 24-48 hours"
    Write-Host "  □ Verify all important pages are accessible"

    # Step 6: Generate deployment report
    Write-Status "Generating deployment report..."
    $reportContent = @"
HouseGoing SEO Deployment Report
================================
Date: $(Get-Date)
Total URLs: $totalUrls
Sitemap files: $($requiredFiles.Count)

Files generated:
"@

    foreach ($file in $requiredFiles) {
        $filePath = "public/$file"
        if (Test-Path $filePath) {
            $size = (Get-Item $filePath).Length
            $reportContent += "`n- $file ($size bytes)"
        }
    }

    $reportContent += @"

Next actions required:
1. Submit sitemap to Google Search Console
2. Submit sitemap to Bing Webmaster Tools
3. Monitor indexing status
4. Check for crawl errors

Sitemap URLs:
- https://housegoing.com.au/sitemap.xml
- https://housegoing.com.au/sitemap_index.xml
- https://housegoing.com.au/sitemap_main.xml
- https://housegoing.com.au/sitemap_blog.xml
- https://housegoing.com.au/sitemap_venues.xml
"@

    $reportContent | Out-File -FilePath "seo-deployment-report.txt" -Encoding UTF8
    Write-Success "Deployment report saved to seo-deployment-report.txt"

    # Step 7: Final summary
    Write-Host ""
    Write-Host "🎉 SEO deployment completed successfully!" -ForegroundColor Green
    Write-Host "📊 Total URLs in sitemap: $totalUrls" -ForegroundColor Cyan
    Write-Host "📝 Report saved: seo-deployment-report.txt" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔗 Important URLs to submit to search engines:" -ForegroundColor Yellow
    Write-Host "   • https://housegoing.com.au/sitemap.xml"
    Write-Host "   • https://housegoing.com.au/robots.txt"
    Write-Host ""
    Write-Host "📈 Monitor your indexing progress at:" -ForegroundColor Yellow
    Write-Host "   • Google Search Console: https://search.google.com/search-console"
    Write-Host "   • Bing Webmaster Tools: https://www.bing.com/webmasters"
    Write-Host ""

    # Optional: Ping search engines
    Write-Status "Notifying search engines about sitemap update..."
    
    try {
        # Google
        $googleResponse = Invoke-WebRequest -Uri "https://www.google.com/ping?sitemap=https://housegoing.com.au/sitemap.xml" -UseBasicParsing -TimeoutSec 10
        Write-Success "Google notified"
    } catch {
        Write-Warning "Could not notify Google (this is normal, submit manually)"
    }

    try {
        # Bing
        $bingResponse = Invoke-WebRequest -Uri "https://www.bing.com/ping?sitemap=https://housegoing.com.au/sitemap.xml" -UseBasicParsing -TimeoutSec 10
        Write-Success "Bing notified"
    } catch {
        Write-Warning "Could not notify Bing (this is normal, submit manually)"
    }

    Write-Host ""
    Write-Success "SEO deployment script completed! 🚀"

} catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    exit 1
}
