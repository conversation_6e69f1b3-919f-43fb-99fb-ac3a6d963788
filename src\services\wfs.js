/**
 * WFS Service
 * 
 * Utility functions for working with Web Feature Service (WFS) endpoints
 * from the NSW Planning Portal.
 */

// NSW Planning Portal WFS endpoints
const ZONING_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer';
const LGA_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer';

// Cache for WFS responses
const wfsCache = new Map();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Fetch data from a WFS endpoint
 */
export async function fetchWFSData(serviceUrl, typeName, lat, lng) {
  // Generate cache key
  const cacheKey = `${serviceUrl}-${typeName}-${lat.toFixed(6)}-${lng.toFixed(6)}`;
  
  // Check cache first
  const cachedItem = wfsCache.get(cacheKey);
  if (cachedItem && Date.now() - cachedItem.timestamp < CACHE_DURATION) {
    return cachedItem.data;
  }
  
  try {
    // Use the proxy server to avoid CORS issues
    const response = await fetch('/api/wfs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        serviceUrl,
        params: {
          service: 'WFS',
          version: '1.1.0',
          request: 'GetFeature',
          typeNames: typeName,
          outputFormat: 'application/json',
          CQL_FILTER: `INTERSECTS(Shape, POINT(${lng} ${lat}))`
        }
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`WFS request failed: ${errorText}`);
    }
    
    const data = await response.json();
    
    // Cache the result
    wfsCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  } catch (error) {
    console.error(`Error fetching WFS data from ${serviceUrl}:`, error);
    throw error;
  }
}

/**
 * Get zoning information for a location
 */
export async function getZoningData(lat, lng) {
  try {
    const data = await fetchWFSData(
      ZONING_WFS_URL,
      'EPI_Primary_Planning_Layers:2',
      lat,
      lng
    );
    
    if (!data?.features?.length) {
      return null;
    }
    
    return {
      code: data.features[0].properties.ZONE_CODE,
      name: data.features[0].properties.ZONE_NAME,
      description: data.features[0].properties.ZONE_DESC || '',
      geometry: data.features[0].geometry
    };
  } catch (error) {
    console.error('Error getting zoning data:', error);
    return null;
  }
}

/**
 * Get LGA (Local Government Area) information for a location
 */
export async function getLGAData(lat, lng) {
  try {
    const data = await fetchWFSData(
      LGA_WFS_URL,
      'EDP_Administrative_Boundaries:1',
      lat,
      lng
    );
    
    if (!data?.features?.length) {
      return null;
    }
    
    return {
      name: data.features[0].properties.LGA_NAME,
      code: data.features[0].properties.LGA_CODE || '',
      geometry: data.features[0].geometry
    };
  } catch (error) {
    console.error('Error getting LGA data:', error);
    return null;
  }
}

/**
 * Get both zoning and LGA data for a location
 */
export async function getSpatialData(lat, lng) {
  try {
    const [zoning, lga] = await Promise.all([
      getZoningData(lat, lng),
      getLGAData(lat, lng)
    ]);
    
    return { zoning, lga };
  } catch (error) {
    console.error('Error getting spatial data:', error);
    return { zoning: null, lga: null };
  }
}

/**
 * Get curfew time based on zoning code
 */
export function getCurfewByZoning(zoneCode) {
  if (!zoneCode) return '10:00 PM'; // Default
  
  const code = zoneCode.toUpperCase();
  
  // Business zones
  if (code.startsWith('B3') || code.startsWith('B4')) {
    return '12:00 AM'; // Midnight
  } else if (code.startsWith('B')) {
    return '11:00 PM';
  }
  // Residential zones
  else if (code.startsWith('R1') || code.startsWith('R2')) {
    return '10:00 PM';
  } else if (code.startsWith('R')) {
    return '10:30 PM';
  }
  // Industrial zones
  else if (code.startsWith('IN')) {
    return '11:00 PM';
  }
  // Special purpose zones
  else if (code.startsWith('SP')) {
    return '10:00 PM';
  }
  // Rural zones
  else if (code.startsWith('RU')) {
    return '10:00 PM';
  }
  // Environmental zones
  else if (code.startsWith('E')) {
    return '9:00 PM';
  }
  // Default
  else {
    return '10:00 PM';
  }
}

/**
 * Get party suitability rating based on zoning code
 */
export function getPartySuitability(zoneCode) {
  if (!zoneCode) return 'Unknown';
  
  const code = zoneCode.toUpperCase();
  
  // Business zones - generally good for parties
  if (code.startsWith('B3') || code.startsWith('B4')) {
    return 'Excellent';
  } else if (code.startsWith('B')) {
    return 'Good';
  }
  // Residential zones - more restrictions
  else if (code.startsWith('R1') || code.startsWith('R2')) {
    return 'Limited';
  } else if (code.startsWith('R')) {
    return 'Moderate';
  }
  // Industrial zones - can be good for parties
  else if (code.startsWith('IN')) {
    return 'Good';
  }
  // Special purpose zones - varies
  else if (code.startsWith('SP')) {
    return 'Varies';
  }
  // Rural zones - depends on proximity to neighbors
  else if (code.startsWith('RU')) {
    return 'Moderate';
  }
  // Environmental zones - generally not suitable
  else if (code.startsWith('E')) {
    return 'Poor';
  }
  // Default
  else {
    return 'Unknown';
  }
}
