import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '../../lib/supabase-client';
import { useSupabase } from '../../providers/SupabaseProvider';

export default function SupabaseAuthCallback() {
  const navigate = useNavigate();
  const location = useLocation();
  const { refreshUserProfile } = useSupabase();
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get the auth code from the URL
        const params = new URLSearchParams(location.hash.substring(1));
        const accessToken = params.get('access_token');
        const refreshToken = params.get('refresh_token');
        const expiresIn = params.get('expires_in');
        const tokenType = params.get('token_type');
        const type = params.get('type');

        if (accessToken && refreshToken) {
          console.log('Auth tokens found in URL, setting session');
          
          // Set the session manually
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });
          
          if (error) {
            console.error('Error setting session:', error);
            setError(error.message);
            setProcessing(false);
            return;
          }
          
          if (data?.session) {
            console.log('Session set successfully');
            
            // Get user type from localStorage
            const userType = localStorage.getItem('auth_user_type') || 'guest';
            
            // Dispatch auth_complete event
            const authCompleteEvent = new CustomEvent('auth_complete', {
              detail: {
                user: data.session.user,
                userType
              }
            });
            window.dispatchEvent(authCompleteEvent);
            
            // Set auth_success flag
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());
            
            // Refresh user profile
            await refreshUserProfile();
            
            // Redirect to the appropriate page
            const redirectTo = localStorage.getItem('auth_redirect_to') || '/';
            localStorage.removeItem('auth_redirect_to');
            
            // Redirect to the appropriate page based on user type
            if (userType === 'host') {
              navigate('/host/dashboard');
            } else {
              navigate(redirectTo);
            }
          }
        } else {
          console.error('No auth tokens found in URL');
          setError('Authentication failed. No tokens found in the URL.');
        }
      } catch (err) {
        console.error('Error in auth callback:', err);
        setError('An unexpected error occurred during authentication.');
      } finally {
        setProcessing(false);
      }
    };

    handleCallback();
  }, [location, navigate, refreshUserProfile]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900">Authentication Error</h2>
            <p className="mt-2 text-sm text-red-600">{error}</p>
            <div className="mt-6">
              <button
                onClick={() => navigate('/login')}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Return to Login
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Completing Authentication</h2>
          <p className="mt-2 text-sm text-gray-600">Please wait while we complete your authentication...</p>
          <div className="mt-6 flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
