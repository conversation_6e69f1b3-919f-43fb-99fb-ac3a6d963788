import React, { useState, useEffect } from 'react';
import { Tab } from '@headlessui/react';
import { ThumbsUp, ThumbsDown, Save, RefreshCw, MessageSquare, Bot, Users } from 'lucide-react';
import { useUser } from '@clerk/clerk-react';

// Import our LangChain chat components
import LangChainC<PERSON> from '../../components/chat/LangChainChat';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

function AITrainingPage() {
  const { user, isLoaded } = useUser();

  // DIRECT OVERRIDE: List of admin emails
  const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

  // Check if user is admin
  if (!isLoaded) {
    return (
      <div className="pt-32 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        <p className="ml-3 text-purple-600">Loading...</p>
      </div>
    );
  }

  if (!user) {
    return <div className="pt-32 flex justify-center">Please sign in to access the AI training dashboard</div>;
  }

  // Get user email
  const userEmail = user.primaryEmailAddress?.emailAddress || '';

  // Simple check: user is admin if email is in the list or we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isAdmin = isDevelopment || ADMIN_EMAILS.includes(userEmail.toLowerCase());

  // Log for debugging
  console.log('AITraining admin check:', { email: userEmail, isDevelopment, isAdmin });

  if (!isAdmin) {
    return (
      <div className="pt-32 px-4 flex flex-col items-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-8">
          You don't have permission to access the AI training dashboard.
          Your email: <span className="font-mono">{userEmail}</span>
        </p>
        <a href="/" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md">
          Return to Home
        </a>
      </div>
    );
  }
  const [conversations, setConversations] = useState<any[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<any>(null);
  const [feedback, setFeedback] = useState<Record<string, { rating: 'good' | 'bad', notes: string }>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [trainingNotes, setTrainingNotes] = useState('');
  const [savedFeedback, setSavedFeedback] = useState<string[]>([]);
  const [salesPrompt, setSalesPrompt] = useState('');
  const [hostPrompt, setHostPrompt] = useState('');
  const [promptLoading, setPromptLoading] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<any>(null);
  const [aiImprovements, setAiImprovements] = useState<any>(null);
  const [aiTrainingInProgress, setAiTrainingInProgress] = useState(false);
  const [selectedAgentType, setSelectedAgentType] = useState('sales');

  // Fetch conversations from LangSmith (mock for now)
  useEffect(() => {
    // This would be replaced with actual API call to LangSmith
    const mockConversations = [
      { id: 'conv1', title: 'Venue inquiry - Sydney', type: 'sales', timestamp: new Date().toISOString() },
      { id: 'conv2', title: 'Party Score question', type: 'host', timestamp: new Date().toISOString() },
      { id: 'conv3', title: 'Booking for 50 people', type: 'sales', timestamp: new Date().toISOString() },
    ];

    setConversations(mockConversations);

    // Fetch prompt templates
    fetchPromptTemplates();
  }, []);

  // Fetch prompt templates
  const fetchPromptTemplates = async () => {
    setPromptLoading(true);
    try {
      // Fetch sales prompt
      const salesResponse = await fetch('/api/ai-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get-prompt',
          data: {
            agentType: 'sales'
          }
        }),
      });

      if (salesResponse.ok) {
        const salesData = await salesResponse.json();
        if (salesData.success && salesData.promptTemplate) {
          setSalesPrompt(salesData.promptTemplate);
        }
      }

      // Fetch host prompt
      const hostResponse = await fetch('/api/ai-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get-prompt',
          data: {
            agentType: 'host'
          }
        }),
      });

      if (hostResponse.ok) {
        const hostData = await hostResponse.json();
        if (hostData.success && hostData.promptTemplate) {
          setHostPrompt(hostData.promptTemplate);
        }
      }
    } catch (error) {
      console.error('Error fetching prompt templates:', error);
    } finally {
      setPromptLoading(false);
    }
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (messageId: string, rating: 'good' | 'bad') => {
    setFeedback(prev => ({
      ...prev,
      [messageId]: {
        ...prev[messageId],
        rating
      }
    }));
  };

  // Handle feedback notes
  const handleFeedbackNotes = (messageId: string, notes: string) => {
    setFeedback(prev => ({
      ...prev,
      [messageId]: {
        ...prev[messageId],
        notes
      }
    }));
  };

  // Save all feedback
  const saveAllFeedback = () => {
    setIsLoading(true);

    // This would be an API call to save feedback
    setTimeout(() => {
      const feedbackId = `feedback_${Date.now()}`;
      setSavedFeedback(prev => [...prev, feedbackId]);
      setIsLoading(false);

      // Show success message
      alert('Feedback saved successfully! This would update the AI training data.');
    }, 1000);
  };

  // Save training notes
  const saveTrainingNotes = () => {
    if (!trainingNotes.trim()) return;

    setIsLoading(true);

    // This would be an API call to save training notes
    setTimeout(() => {
      setIsLoading(false);
      setTrainingNotes('');

      // Show success message
      alert('Training notes saved successfully! These will be used to improve the AI models.');
    }, 1000);
  };

  // Save prompt template
  const savePromptTemplate = async (agentType: string, promptTemplate: string) => {
    if (!promptTemplate.trim()) return;

    setIsLoading(true);

    try {
      const response = await fetch('/api/ai-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update-prompt',
          data: {
            agentType,
            promptTemplate
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update prompt template');
      }

      const data = await response.json();

      if (data.success) {
        alert(`${agentType.charAt(0).toUpperCase() + agentType.slice(1)} prompt template updated successfully!`);
      } else {
        throw new Error(data.error || 'Failed to update prompt template');
      }
    } catch (error) {
      console.error('Error updating prompt template:', error);
      alert('Failed to update prompt template. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // AI-assisted training functions
  const analyzeConversations = async () => {
    setAiTrainingInProgress(true);
    try {
      // Mock conversation for now - would be replaced with actual conversations
      const mockConversation = [
        { role: 'user', content: "I'm looking for a venue in Sydney for about 50 people" },
        { role: 'assistant', content: "I'd be happy to help you find a venue in Sydney for 50 people. What type of event are you planning?" },
        { role: 'user', content: "It's for a birthday party next month" },
        { role: 'assistant', content: "Great! A birthday party sounds fun. Do you have a specific date in mind?" }
      ];

      const response = await fetch('/api/ai-trainer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'analyze-conversation',
          data: {
            conversation: mockConversation,
            agentType: selectedAgentType
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze conversation');
      }

      const data = await response.json();

      if (data.success) {
        setAiAnalysis(data.analysis);
        alert('AI analysis complete!');
      } else {
        throw new Error(data.error || 'Failed to analyze conversation');
      }
    } catch (error) {
      console.error('Error analyzing conversation:', error);
      alert('Failed to analyze conversation. Please try again.');
    } finally {
      setAiTrainingInProgress(false);
    }
  };

  const autoImprovePrompt = async () => {
    setAiTrainingInProgress(true);
    try {
      const response = await fetch('/api/ai-trainer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'auto-improve',
          data: {
            agentType: selectedAgentType
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to auto-improve prompt');
      }

      const data = await response.json();

      if (data.success) {
        // Update the prompt in the UI
        if (selectedAgentType === 'sales') {
          setSalesPrompt(data.result.newPrompt);
        } else if (selectedAgentType === 'host') {
          setHostPrompt(data.result.newPrompt);
        }

        setAiImprovements({
          oldPrompt: data.result.oldPrompt,
          newPrompt: data.result.newPrompt,
          changes: data.result.improvements
        });

        alert('Prompt auto-improved successfully!');
      } else {
        throw new Error(data.error || 'Failed to auto-improve prompt');
      }
    } catch (error) {
      console.error('Error auto-improving prompt:', error);
      alert('Failed to auto-improve prompt. Please try again.');
    } finally {
      setAiTrainingInProgress(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">AI Assistant Training</h1>
          <button
            onClick={saveAllFeedback}
            disabled={isLoading || Object.keys(feedback).length === 0}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
          >
            {isLoading ? (
              <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
            ) : (
              <Save className="-ml-1 mr-2 h-4 w-4" />
            )}
            Save All Feedback
          </button>
        </div>

        <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg leading-6 font-medium text-gray-900">Training Dashboard</h2>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Use this interface to train and improve the AI assistants by providing feedback on conversations.
            </p>
          </div>

          <Tab.Group>
            <Tab.List className="flex border-b border-gray-200">
              <Tab
                className={({ selected }) =>
                  classNames(
                    'w-full py-4 px-1 text-center text-sm font-medium',
                    selected
                      ? 'text-purple-600 border-b-2 border-purple-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )
                }
              >
                <div className="flex items-center justify-center">
                  <MessageSquare className="mr-2 h-5 w-5" />
                  Live Training
                </div>
              </Tab>
              <Tab
                className={({ selected }) =>
                  classNames(
                    'w-full py-4 px-1 text-center text-sm font-medium',
                    selected
                      ? 'text-purple-600 border-b-2 border-purple-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )
                }
              >
                <div className="flex items-center justify-center">
                  <Bot className="mr-2 h-5 w-5" />
                  Conversation History
                </div>
              </Tab>
              <Tab
                className={({ selected }) =>
                  classNames(
                    'w-full py-4 px-1 text-center text-sm font-medium',
                    selected
                      ? 'text-purple-600 border-b-2 border-purple-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )
                }
              >
                <div className="flex items-center justify-center">
                  <Users className="mr-2 h-5 w-5" />
                  Training Notes
                </div>
              </Tab>
              <Tab
                className={({ selected }) =>
                  classNames(
                    'w-full py-4 px-1 text-center text-sm font-medium',
                    selected
                      ? 'text-purple-600 border-b-2 border-purple-600'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )
                }
              >
                <div className="flex items-center justify-center">
                  <RefreshCw className="mr-2 h-5 w-5" />
                  AI-Assisted Training
                </div>
              </Tab>
            </Tab.List>
            <Tab.Panels>
              {/* Live Training Panel */}
              <Tab.Panel className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Assistant</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Interact with the customer assistant and provide feedback to improve its responses.
                    </p>
                    <div className="h-[600px] relative">
                      <LangChainChat agentType="sales" context="training" />
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Host Assistant</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Interact with the host assistant and provide feedback to improve its responses.
                    </p>
                    <div className="h-[600px] relative">
                      <LangChainChat agentType="host" context="training" />
                    </div>
                  </div>
                </div>
              </Tab.Panel>

              {/* Conversation History Panel */}
              <Tab.Panel className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="col-span-1 border rounded-lg overflow-hidden">
                    <div className="bg-gray-100 px-4 py-3 border-b">
                      <h3 className="text-sm font-medium text-gray-900">Recent Conversations</h3>
                    </div>
                    <ul className="divide-y divide-gray-200 max-h-[600px] overflow-y-auto">
                      {conversations.map((conversation) => (
                        <li
                          key={conversation.id}
                          className={`px-4 py-3 hover:bg-gray-50 cursor-pointer ${
                            selectedConversation?.id === conversation.id ? 'bg-purple-50' : ''
                          }`}
                          onClick={() => setSelectedConversation(conversation)}
                        >
                          <div className="flex justify-between">
                            <p className="text-sm font-medium text-gray-900">{conversation.title}</p>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              conversation.type === 'sales' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                            }`}>
                              {conversation.type === 'sales' ? 'Customer' : 'Host'}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(conversation.timestamp).toLocaleString()}
                          </p>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="col-span-2 border rounded-lg overflow-hidden">
                    {selectedConversation ? (
                      <div>
                        <div className="bg-gray-100 px-4 py-3 border-b flex justify-between items-center">
                          <h3 className="text-sm font-medium text-gray-900">
                            {selectedConversation.title}
                          </h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            selectedConversation.type === 'sales' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {selectedConversation.type === 'sales' ? 'Customer' : 'Host'}
                          </span>
                        </div>

                        <div className="p-4 max-h-[500px] overflow-y-auto">
                          {/* Mock conversation messages */}
                          {[
                            { id: 'm1', role: 'user', content: 'Hi, I\'m looking for a venue in Sydney for about 50 people.' },
                            { id: 'm2', role: 'assistant', content: 'Great! I\'d be happy to help you find a venue in Sydney for 50 people. What type of event are you planning, and do you have a specific date in mind?' },
                            { id: 'm3', role: 'user', content: 'It\'s for a birthday party next month, probably on a Saturday.' },
                            { id: 'm4', role: 'assistant', content: 'Perfect! A birthday party for 50 people in Sydney next month on a Saturday sounds fun. Here are some venues that might work well for you:\n\n1. Harbour View Terrace: Beautiful waterfront venue with capacity for 80 people, $250/hour\n2. Urban Loft Space: Industrial chic warehouse with capacity for 150 people, $380/hour\n3. The Garden Pavilion: Elegant garden venue with capacity for 120 people, $320/hour\n\nWould you like more details about any of these options?' }
                          ].map((message) => (
                            <div key={message.id} className="mb-4">
                              <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                                <div className={`max-w-[80%] p-3 rounded-lg ${
                                  message.role === 'user'
                                    ? 'bg-purple-600 text-white'
                                    : 'bg-gray-200 text-gray-800'
                                }`}>
                                  {message.content}
                                </div>
                              </div>

                              {message.role === 'assistant' && (
                                <div className="mt-2 flex items-start">
                                  <div className="flex space-x-2">
                                    <button
                                      onClick={() => handleFeedbackSubmit(message.id, 'good')}
                                      className={`p-1 rounded ${
                                        feedback[message.id]?.rating === 'good'
                                          ? 'bg-green-100 text-green-800'
                                          : 'text-gray-400 hover:text-green-600'
                                      }`}
                                    >
                                      <ThumbsUp size={16} />
                                    </button>
                                    <button
                                      onClick={() => handleFeedbackSubmit(message.id, 'bad')}
                                      className={`p-1 rounded ${
                                        feedback[message.id]?.rating === 'bad'
                                          ? 'bg-red-100 text-red-800'
                                          : 'text-gray-400 hover:text-red-600'
                                      }`}
                                    >
                                      <ThumbsDown size={16} />
                                    </button>
                                  </div>

                                  {feedback[message.id] && (
                                    <div className="ml-4 flex-1">
                                      <textarea
                                        value={feedback[message.id]?.notes || ''}
                                        onChange={(e) => handleFeedbackNotes(message.id, e.target.value)}
                                        placeholder="Add notes about this response..."
                                        className="w-full text-sm border border-gray-300 rounded-md p-2 focus:ring-purple-500 focus:border-purple-500"
                                        rows={2}
                                      />
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="p-8 text-center text-gray-500">
                        <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2">Select a conversation to view details</p>
                      </div>
                    )}
                  </div>
                </div>
              </Tab.Panel>

              {/* Training Notes Panel */}
              <Tab.Panel className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Add Training Notes</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Add specific instructions or examples to improve the AI assistants.
                    </p>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Training Notes
                      </label>
                      <textarea
                        value={trainingNotes}
                        onChange={(e) => setTrainingNotes(e.target.value)}
                        rows={8}
                        className="w-full border border-gray-300 rounded-md p-2 focus:ring-purple-500 focus:border-purple-500"
                        placeholder="Example: When users ask about venues in Sydney, always mention the Harbour View Terrace first as it's our featured venue."
                      />
                    </div>

                    <button
                      onClick={saveTrainingNotes}
                      disabled={isLoading || !trainingNotes.trim()}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
                    >
                      {isLoading ? (
                        <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                      ) : (
                        <Save className="-ml-1 mr-2 h-4 w-4" />
                      )}
                      Save Training Notes
                    </button>

                    <div className="mt-8">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Prompt Templates</h3>
                      <p className="text-sm text-gray-500 mb-4">
                        Directly edit the prompt templates used by the AI assistants.
                      </p>

                      <div className="space-y-6">
                        {/* Sales Assistant Prompt */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Sales Assistant Prompt
                          </label>
                          <textarea
                            value={salesPrompt}
                            onChange={(e) => setSalesPrompt(e.target.value)}
                            rows={10}
                            className="w-full border border-gray-300 rounded-md p-2 focus:ring-purple-500 focus:border-purple-500 font-mono text-sm"
                          />
                          <button
                            onClick={() => savePromptTemplate('sales', salesPrompt)}
                            disabled={isLoading || !salesPrompt.trim() || promptLoading}
                            className="mt-2 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                          >
                            {isLoading || promptLoading ? (
                              <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                            ) : (
                              <Save className="-ml-1 mr-2 h-4 w-4" />
                            )}
                            Update Sales Prompt
                          </button>
                        </div>

                        {/* Host Assistant Prompt */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Host Assistant Prompt
                          </label>
                          <textarea
                            value={hostPrompt}
                            onChange={(e) => setHostPrompt(e.target.value)}
                            rows={10}
                            className="w-full border border-gray-300 rounded-md p-2 focus:ring-purple-500 focus:border-purple-500 font-mono text-sm"
                          />
                          <button
                            onClick={() => savePromptTemplate('host', hostPrompt)}
                            disabled={isLoading || !hostPrompt.trim() || promptLoading}
                            className="mt-2 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                          >
                            {isLoading || promptLoading ? (
                              <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                            ) : (
                              <Save className="-ml-1 mr-2 h-4 w-4" />
                            )}
                            Update Host Prompt
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Training Statistics</h3>

                    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dl className="grid grid-cols-1 gap-5 sm:grid-cols-2">
                          <div className="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              Total Conversations
                            </dt>
                            <dd className="mt-1 text-3xl font-semibold text-gray-900">
                              {conversations.length}
                            </dd>
                          </div>

                          <div className="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              Feedback Provided
                            </dt>
                            <dd className="mt-1 text-3xl font-semibold text-gray-900">
                              {Object.keys(feedback).length}
                            </dd>
                          </div>

                          <div className="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              Positive Ratings
                            </dt>
                            <dd className="mt-1 text-3xl font-semibold text-green-600">
                              {Object.values(feedback).filter(f => f.rating === 'good').length}
                            </dd>
                          </div>

                          <div className="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              Negative Ratings
                            </dt>
                            <dd className="mt-1 text-3xl font-semibold text-red-600">
                              {Object.values(feedback).filter(f => f.rating === 'bad').length}
                            </dd>
                          </div>
                        </dl>
                      </div>
                    </div>

                    <h4 className="text-md font-medium text-gray-900 mt-6 mb-2">Recent Training Updates</h4>
                    <ul className="divide-y divide-gray-200 border rounded-lg overflow-hidden">
                      {savedFeedback.length > 0 ? (
                        savedFeedback.map((id) => (
                          <li key={id} className="px-4 py-3 bg-green-50 border-l-4 border-green-500">
                            <div className="flex justify-between">
                              <p className="text-sm font-medium text-gray-900">Feedback saved</p>
                              <p className="text-xs text-gray-500">
                                {new Date().toLocaleString()}
                              </p>
                            </div>
                            <p className="text-xs text-gray-600 mt-1">
                              AI model updated with new training data
                            </p>
                          </li>
                        ))
                      ) : (
                        <li className="px-4 py-3 text-sm text-gray-500">
                          No recent training updates
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </Tab.Panel>

              {/* AI-Assisted Training Panel */}
              <Tab.Panel className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">AI-Assisted Training</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Let AI analyze conversations and suggest improvements to the assistant prompts.
                    </p>

                    <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
                      <h4 className="text-md font-medium text-gray-900 mb-2">Select Assistant Type</h4>
                      <div className="flex space-x-4">
                        <label className="inline-flex items-center">
                          <input
                            type="radio"
                            className="form-radio h-4 w-4 text-purple-600"
                            checked={selectedAgentType === 'sales'}
                            onChange={() => setSelectedAgentType('sales')}
                          />
                          <span className="ml-2 text-gray-700">Sales Assistant</span>
                        </label>
                        <label className="inline-flex items-center">
                          <input
                            type="radio"
                            className="form-radio h-4 w-4 text-purple-600"
                            checked={selectedAgentType === 'host'}
                            onChange={() => setSelectedAgentType('host')}
                          />
                          <span className="ml-2 text-gray-700">Host Assistant</span>
                        </label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <button
                        onClick={analyzeConversations}
                        disabled={aiTrainingInProgress}
                        className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
                      >
                        {aiTrainingInProgress ? (
                          <>
                            <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                            Analyzing...
                          </>
                        ) : (
                          <>Analyze Recent Conversations</>
                        )}
                      </button>

                      <button
                        onClick={autoImprovePrompt}
                        disabled={aiTrainingInProgress}
                        className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        {aiTrainingInProgress ? (
                          <>
                            <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                            Improving...
                          </>
                        ) : (
                          <>Auto-Improve Prompt</>
                        )}
                      </button>
                    </div>

                    {aiAnalysis && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 className="text-md font-medium text-blue-800 mb-2">AI Analysis</h4>

                        <div className="space-y-4">
                          <div>
                            <h5 className="text-sm font-medium text-blue-700">Strengths:</h5>
                            <p className="text-sm text-gray-600">{aiAnalysis.strengths}</p>
                          </div>

                          <div>
                            <h5 className="text-sm font-medium text-blue-700">Weaknesses:</h5>
                            <p className="text-sm text-gray-600">{aiAnalysis.weaknesses}</p>
                          </div>

                          <div>
                            <h5 className="text-sm font-medium text-blue-700">Suggestions:</h5>
                            <p className="text-sm text-gray-600">{aiAnalysis.suggestions}</p>
                          </div>

                          <div>
                            <h5 className="text-sm font-medium text-blue-700">Rating:</h5>
                            <div className="flex items-center">
                              <span className="text-2xl font-bold text-blue-800">{aiAnalysis.rating}</span>
                              <span className="text-sm text-gray-500 ml-2">/ 10</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    {aiImprovements && (
                      <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                        <h4 className="text-md font-medium text-green-800 mb-4">AI-Suggested Improvements</h4>

                        <div className="mb-4">
                          <h5 className="text-sm font-medium text-green-700 mb-2">Changes Made:</h5>
                          <div className="bg-white p-3 rounded border border-green-100 text-sm text-gray-700 max-h-[200px] overflow-y-auto">
                            <pre className="whitespace-pre-wrap">{aiImprovements.changes}</pre>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="text-sm font-medium text-green-700 mb-2">Previous Prompt:</h5>
                            <div className="bg-white p-3 rounded border border-green-100 text-xs text-gray-700 h-[300px] overflow-y-auto">
                              <pre className="whitespace-pre-wrap">{aiImprovements.oldPrompt}</pre>
                            </div>
                          </div>

                          <div>
                            <h5 className="text-sm font-medium text-green-700 mb-2">New Prompt:</h5>
                            <div className="bg-white p-3 rounded border border-green-100 text-xs text-gray-700 h-[300px] overflow-y-auto">
                              <pre className="whitespace-pre-wrap">{aiImprovements.newPrompt}</pre>
                            </div>
                          </div>
                        </div>

                        <div className="mt-4 flex justify-end">
                          <button
                            onClick={() => {
                              if (selectedAgentType === 'sales') {
                                setSalesPrompt(aiImprovements.newPrompt);
                              } else {
                                setHostPrompt(aiImprovements.newPrompt);
                              }
                              alert('Prompt updated in editor. You can now save it.');
                            }}
                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            Apply Changes
                          </button>
                        </div>
                      </div>
                    )}

                    {!aiImprovements && (
                      <div className="h-full flex items-center justify-center">
                        <div className="text-center p-8">
                          <RefreshCw className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-gray-500">Use the buttons on the left to analyze conversations and generate AI-suggested improvements</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Tab.Panel>
            </Tab.Panels>
          </Tab.Group>
        </div>
      </div>
    </div>
  );
}

// Export the AITrainingPage component directly
export default AITrainingPage;
