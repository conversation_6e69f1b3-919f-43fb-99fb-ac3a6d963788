/**
 * <PERSON><PERSON><PERSON> to set up Cloudinary upload preset using the Cloudinary SDK
 * 
 * Run this script once to create the upload preset for unsigned uploads
 * 
 * Usage: node scripts/setup-cloudinary-sdk.cjs
 */
const cloudinary = require('cloudinary').v2;

// Cloudinary configuration
cloudinary.config({
  cloud_name: 'dcdjxfnud',
  api_key: '565724643666687',
  api_secret: 'B23KQdOOeZVu-ylR0TQfZXF8YAU'
});

const uploadPresetName = 'housegoing_uploads';

// Create the upload preset
const createUploadPreset = async () => {
  try {
    const result = await cloudinary.api.create_upload_preset({
      name: uploadPresetName,
      unsigned: true,
      folder: 'property-images',
      allowed_formats: 'jpg,png,jpeg',
      max_file_size: 5000000 // 5MB
    });
    
    console.log('Upload preset created successfully:', result.name);
    return result;
  } catch (error) {
    // If the preset already exists, this is not an error
    if (error.error && error.error.message.includes('already exists')) {
      console.log(`Upload preset "${uploadPresetName}" already exists.`);
      return { name: uploadPresetName };
    }
    
    console.error('Error creating upload preset:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    console.log('Setting up Cloudinary upload preset...');
    await createUploadPreset();
    console.log('Cloudinary setup completed successfully!');
    console.log(`Upload preset "${uploadPresetName}" is ready to use.`);
    console.log('You can now use the Cloudinary upload functionality in your application.');
  } catch (error) {
    console.error('Error setting up Cloudinary:', error);
    process.exit(1);
  }
};

// Run the script
main();
