import React, { useState, useEffect } from 'react';

interface AuthToggleProps {
  onToggle: (useClerk: boolean) => void;
}

export default function AuthToggle({ onToggle }: AuthToggleProps) {
  const [useClerk, setUseClerk] = useState(() => {
    // Check if there's a saved preference in localStorage
    const savedPreference = localStorage.getItem('auth_provider');
    return savedPreference === 'clerk';
  });

  // Update the parent component when the toggle changes
  useEffect(() => {
    onToggle(useClerk);
    // Save the preference to localStorage
    localStorage.setItem('auth_provider', useClerk ? 'clerk' : 'supabase');
  }, [useClerk, onToggle]);

  return (
    <div className="fixed bottom-4 left-4 bg-white p-2 rounded-lg shadow-lg border border-gray-200 z-50">
      <div className="flex items-center space-x-2">
        <span className={`text-xs font-medium ${!useClerk ? 'text-purple-600' : 'text-gray-500'}`}>
          Supabase
        </span>
        <button
          onClick={() => setUseClerk(!useClerk)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
            useClerk ? 'bg-purple-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              useClerk ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
        <span className={`text-xs font-medium ${useClerk ? 'text-purple-600' : 'text-gray-500'}`}>
          Clerk
        </span>
      </div>
    </div>
  );
}
