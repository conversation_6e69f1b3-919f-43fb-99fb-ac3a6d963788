// HouseGoing Email Notification Server
import express from 'express';
import nodemailer from 'nodemailer';
import cors from 'cors';
import bodyParser from 'body-parser';

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS
app.use(cors({
  origin: ['https://housegoing.com.au', 'http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));

app.use(bodyParser.json());

// Email transports
const transports = {
  // Brevo transport for customer emails
  brevo: nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    auth: {
      user: '<EMAIL>',
      pass: 'pcdxRPbvhkZ82zIY'
    }
  }),

  // Gmail transport for internal emails
  gmail: nodemailer.createTransport({
    service: 'Gmail',
    auth: {
      user: '<EMAIL>',
      pass: 'ztjj vkeu foty iyeg'
    }
  })
};

// Get appropriate transport based on email type
function getTransport(emailType) {
  switch(emailType) {
    case 'customer':
    case 'booking':
    case 'password':
    case 'user_message':
      return transports.brevo;
    case 'admin':
    case 'internal':
    case 'alert':
      return transports.gmail;
    default:
      return transports.gmail;
  }
}

// Email sending endpoint
app.post('/api/send-email', async (req, res) => {
  try {
    const { to, from, subject, text, html, emailType } = req.body;
    const transport = getTransport(emailType);

    const info = await transport.sendMail({
      from: from || '<EMAIL>',
      to,
      subject,
      text,
      html
    });

    res.status(200).json({
      success: true,
      message: 'Email sent',
      messageId: info.messageId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to send email',
      error: error.message
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK' });
});

app.listen(PORT, () => {
  console.log(`Email server running on port ${PORT}`);
});
