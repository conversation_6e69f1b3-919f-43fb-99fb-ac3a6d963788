import { WebhookEvent } from '@clerk/clerk-react';
import { getClerkClient } from '../clerk';
import { notifyAdminOfSignup } from '../../hooks/useSignupNotifications';

// Define the types of events we want to handle
type WebhookHandlers = {
  [K in WebhookEvent['type']]?: (event: Extract<WebhookEvent, { type: K }>) => Promise<void>;
};

// Create handlers for specific webhook events
const webhookHandlers: WebhookHandlers = {
  // User events
  'user.created': async (event) => {
    const { data } = event;
    console.log(`User created: ${data.id}`);

    // Determine user type from metadata or URL
    let userType: 'host' | 'customer' = 'customer';
    const signUpUrl = data.last_sign_in_url || '';
    const roleFromMetadata = data.unsafe_metadata?.role || data.public_metadata?.role;

    if (signUpUrl.includes('/host/signup') || signUpUrl.includes('/host/portal') || roleFromMetadata === 'host') {
      userType = 'host';
      // Set the user role to 'host'
      try {
        const clerk = getClerkClient();
        await clerk.users.updateUser(data.id, {
          publicMetadata: {
            ...data.public_metadata,
            role: 'host'
          }
        });
        console.log(`Set role to 'host' for user: ${data.id}`);
      } catch (error) {
        console.error(`Failed to set role for user: ${data.id}`, error);
      }
    }

    // Send admin signup notification
    try {
      const email = data.email_addresses?.[0]?.email_address || '';
      const firstName = data.first_name || '';
      const lastName = data.last_name || '';
      const fullName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || undefined;

      if (email) {
        await notifyAdminOfSignup(email, fullName, userType);
        console.log(`Admin notified of new ${userType} signup: ${fullName || email}`);
      }
    } catch (error) {
      console.error('Failed to send signup notification from webhook:', error);
    }

    // Here you can add custom logic, such as:
    // - Creating a user profile in your database
    // - Sending a welcome email
    // - Setting up default preferences
  },

  'user.updated': async (event) => {
    const { data } = event;
    console.log(`User updated: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Updating user profile in your database
    // - Syncing user data with other systems
  },

  'user.deleted': async (event) => {
    const { data } = event;
    console.log(`User deleted: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Removing user data from your database
    // - Cancelling subscriptions
    // - Cleaning up user resources
  },

  // Session events
  'session.created': async (event) => {
    const { data } = event;
    console.log(`Session created: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Logging sign-ins for security purposes
    // - Tracking user activity
  },

  'session.ended': async (event) => {
    const { data } = event;
    console.log(`Session ended: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Updating user's last active timestamp
    // - Cleaning up session-specific resources
  },

  // Organization events
  'organization.created': async (event) => {
    const { data } = event;
    console.log(`Organization created: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Creating organization records in your database
    // - Setting up default organization settings
  },

  'organization.updated': async (event) => {
    const { data } = event;
    console.log(`Organization updated: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Updating organization records in your database
  },

  'organization.deleted': async (event) => {
    const { data } = event;
    console.log(`Organization deleted: ${data.id}`);

    // Here you can add custom logic, such as:
    // - Removing organization data from your database
    // - Cleaning up organization resources
  },
};

// Main webhook handler function
export async function handleClerkWebhook(event: WebhookEvent): Promise<Response> {
  const eventType = event.type as WebhookEvent['type'];

  // Get the appropriate handler for this event type
  const handler = webhookHandlers[eventType];

  if (handler) {
    try {
      // Call the handler with the event
      await handler(event as any);
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error(`Error handling webhook event ${eventType}:`, error);
      return new Response(JSON.stringify({ success: false, error: 'Internal server error' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  } else {
    // No handler for this event type
    console.log(`No handler for webhook event: ${eventType}`);
    return new Response(JSON.stringify({ success: true, message: 'Event type not handled' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
