# Setting Up Environment Variables in Netlify

This guide explains how to properly set up and manage environment variables for your HouseGoing application in Netlify.

## Security Best Practices

1. **Never commit sensitive API keys to Git repositories**
2. **Always use environment variables for secrets**
3. **Use `.env.example` files with placeholders to document required variables**
4. **Add all `.env` files to `.gitignore`**

## Setting Up Environment Variables in Netlify

1. Go to the [Netlify Dashboard](https://app.netlify.com/)
2. Select your site
3. Go to **Site settings** > **Build & deploy** > **Environment**
4. Add the following environment variables:

| Variable | Description | Source |
|----------|-------------|--------|
| `CLERK_SECRET_KEY` | Clerk authentication secret key | Clerk Dashboard |
| `CLERK_WEBHOOK_SECRET` | Secret for Clerk webhooks | Clerk Dashboard |
| `VITE_CLERK_PUBLISHABLE_KEY` | Public Clerk key | Clerk Dashboard |
| `VITE_CLERK_DOMAIN` | Clerk domain | Clerk Dashboard |
| `VITE_SUPABASE_URL` | Supabase project URL | Supabase Dashboard |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | Supabase Dashboard |
| `NEXT_PUBLIC_SUPABASE_URL` | Same as VITE_SUPABASE_URL | Supabase Dashboard |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Same as VITE_SUPABASE_ANON_KEY | Supabase Dashboard |
| `VITE_STRIPE_PUBLISHABLE_KEY` | Stripe publishable key | Stripe Dashboard |
| `VITE_BACKEND_URL` | URL of your backend service | Your backend service |

## Local Development

For local development, copy `.env.example` to a new `.env` file:

```bash
cp .env.example .env
```

Then fill in the actual values for your local environment.

## Troubleshooting Secrets Scanning

If you encounter secrets scanning errors during Netlify builds:

1. Make sure no actual secrets are committed to your repository
2. Update the `SECRETS_SCAN_OMIT_PATHS` in `netlify.toml` if needed
3. Update the `SECRETS_SCAN_OMIT_KEYS` in `netlify.toml` if needed
4. Set the environment variables in Netlify dashboard instead of in files

## Additional Security Measures

- Rotate API keys periodically
- Use different API keys for development and production
- Consider using Netlify's "sensitive variables" feature for production secrets
