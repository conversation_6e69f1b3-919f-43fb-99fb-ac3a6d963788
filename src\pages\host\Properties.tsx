import React, { useState } from 'react';
import { AIAssistantButton } from '../../components/host/AIAssistantButton.jsx';
import { Link } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Star
} from 'lucide-react';

// Mock data for properties
const mockProperties = [
  {
    id: 1,
    name: 'Beachside Villa',
    location: 'Bondi Beach, NSW',
    type: 'House',
    price: 350,
    rating: 4.9,
    reviews: 28,
    bookings: 12,
    status: 'active',
    image: 'https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80',
  },
  {
    id: 2,
    name: 'Mountain Cabin',
    location: 'Blue Mountains, NSW',
    type: 'Cabin',
    price: 275,
    rating: 4.7,
    reviews: 15,
    bookings: 8,
    status: 'active',
    image: 'https://images.unsplash.com/photo-1518780664697-55e3ad937233?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=765&q=80',
  },
  {
    id: 3,
    name: 'Downtown Loft',
    location: 'Sydney CBD, NSW',
    type: 'Apartment',
    price: 420,
    rating: 4.8,
    reviews: 32,
    bookings: 18,
    status: 'active',
    image: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
  },
];

export default function Properties() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // Filter properties based on search term and filter type
  const filteredProperties = mockProperties.filter(property => {
    const matchesSearch = property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterType === 'all' || property.type.toLowerCase() === filterType.toLowerCase();

    return matchesSearch && matchesFilter;
  });

  return (
    <>
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Your Properties</h1>
          <p className="mt-1 text-gray-600">Manage and monitor all your listed properties</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link
            to="/host/properties/new"
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            <Plus className="mr-2 h-5 w-5" />
            Add Property
          </Link>
        </div>
      </div>

      {/* Search and filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search properties..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="relative sm:w-48">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Filter className="h-5 w-5 text-gray-400" />
          </div>
          <select
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="house">House</option>
            <option value="apartment">Apartment</option>
            <option value="cabin">Cabin</option>
          </select>
        </div>
      </div>

      {/* Properties grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProperties.map((property) => (
          <div key={property.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="relative h-48">
              <img
                src={property.image}
                alt={property.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-2 right-2">
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  {property.status}
                </span>
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900">{property.name}</h3>
              <p className="text-sm text-gray-500">{property.location}</p>
              <div className="mt-2 flex items-center">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="ml-1 text-sm text-gray-700">{property.rating}</span>
                <span className="mx-1 text-gray-400">•</span>
                <span className="text-sm text-gray-500">{property.reviews} reviews</span>
              </div>
              <div className="mt-1 flex items-center">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="ml-1 text-sm text-gray-700">{property.bookings} bookings</span>
              </div>
              <div className="mt-3 flex items-center justify-between">
                <span className="text-lg font-bold text-gray-900">${property.price}</span>
                <span className="text-sm text-gray-500">per night</span>
              </div>
              <div className="mt-4 flex justify-between">
                <Link
                  to={`/host/properties/${property.id}`}
                  className="inline-flex items-center px-3 py-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  <Eye className="mr-1 h-4 w-4 text-gray-500" />
                  View
                </Link>
                <Link
                  to={`/host/properties/${property.id}/edit`}
                  className="inline-flex items-center px-3 py-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  <Edit className="mr-1 h-4 w-4 text-gray-500" />
                  Edit
                </Link>
                <button
                  className="inline-flex items-center px-3 py-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50 hover:text-red-600"
                >
                  <Trash2 className="mr-1 h-4 w-4 text-gray-500" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredProperties.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterType !== 'all'
              ? "Try adjusting your search or filters"
              : "You haven't added any properties yet"}
          </p>
          <div className="flex justify-center">
            <Link
              to="/host/properties/new"
              className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              <Plus className="mr-2 h-5 w-5" />
              Add Property
            </Link>
          </div>
        </div>
      )}
    </div>
      <AIAssistantButton context="properties" position="bottom-right" />
    </>
  );
}
