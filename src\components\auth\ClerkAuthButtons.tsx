import React from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { useAuth, useClerk } from '@clerk/clerk-react';

interface ClerkAuthButtonsProps {
  className?: string;
}

export default function ClerkAuthButtons({ className = '' }: ClerkAuthButtonsProps) {
  const { isSignedIn, isLoaded } = useAuth();
  const { signOut } = useClerk();

  // Don't render anything while loading to prevent flashing
  if (!isLoaded) {
    return null;
  }

  // Show authenticated state
  if (isSignedIn) {
    return (
      <div className={`flex items-center ${className}`}>
        <Link to="/my-account">
          <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm flex items-center">
            <User className="w-4 h-4 mr-1.5 text-purple-600" />
            <span>My Account</span>
          </button>
        </Link>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Link to="/login">
        <button className="text-sm font-medium px-4 py-2 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors flex items-center">
          <User className="w-4 h-4 mr-1" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to="/signup">
        <button className="text-sm font-medium px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700 transition-colors">
          Sign Up
        </button>
      </Link>
    </div>
  );
}
