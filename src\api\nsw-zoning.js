/**
 * NSW Zoning API
 *
 * This API endpoint provides zoning and LGA information for NSW addresses.
 * It queries multiple NSW government APIs and falls back to pattern recognition
 * if the APIs fail.
 */

import { getNSWZoningInfo, getNSWLGAInfo, getSuburbToLGAMapping } from '../lib/supabase';

export async function post({ request }) {
  try {
    const { address, lat, lng } = await request.json();

    console.log('NSW Zoning API request:', { address, lat, lng });

    // Step 1: Query NSW Planning Portal API for zoning information
    let zoneCode = '';
    let zoneName = '';

    try {
      // Try Supabase function first
      const zoningData = await getNSWZoningInfo(lat, lng);

      if (zoningData) {
        zoneCode = zoningData.zone_code;
        zoneName = zoningData.zone_name;
        console.log('Zoning from Supabase:', zoneCode, zoneName);
      }

      // If Supabase fails, try direct API calls
      if (!zoneCode) {
        // Try the NSW Planning Portal API first
        try {
          const zoningUrl = `https://maps.six.nsw.gov.au/arcgis/rest/services/public/PlanningInformation/MapServer/10/query`;

          const zoningParams = new URLSearchParams({
            geometry: `${lng},${lat}`, // longitude first, then latitude
            geometryType: 'esriGeometryPoint',
            inSR: '4326', // WGS84 coordinate system
            outFields: 'ZONE_CODE,ZONE_NAME',
            f: 'json'
          });

          const zoningResponse = await fetch(`${zoningUrl}?${zoningParams.toString()}`);

          if (zoningResponse.ok) {
            const zoningData = await zoningResponse.json();
            if (zoningData.features && zoningData.features.length > 0) {
              zoneCode = zoningData.features[0].attributes.ZONE_CODE;
              zoneName = zoningData.features[0].attributes.ZONE_NAME;
              console.log('Zoning from NSW Planning Portal:', zoneCode, zoneName);
            } else {
              console.log('No zoning features found from NSW Planning Portal API');
            }
          } else {
            console.error('NSW Planning Portal API response not OK:', zoningResponse.statusText);
          }
        } catch (error) {
          console.error('Error with NSW Planning Portal API:', error);
        }

        // If still no zoning, try to load from local GeoJSON file
        if (!zoneCode) {
          try {
            // This would need to be implemented with a local GeoJSON file
            console.log('Attempting to use local zoning GeoJSON data');
            // Implementation would depend on how the local file is accessed
          } catch (localError) {
            console.error('Error using local zoning data:', localError);
          }
        }
      }
    } catch (zoningError) {
      console.error('Error fetching zoning information:', zoningError);
    }

    // Step 2: Query NSW Spatial Services for LGA information
    let lgaName = null;

    try {
      // Try Supabase function first
      const lgaData = await getNSWLGAInfo(lat, lng);

      if (lgaData) {
        lgaName = lgaData.lga_name;
        console.log('LGA from Supabase:', lgaName);
      }

      // If Supabase fails, try direct API call to Layer 8
      if (!lgaName) {
        const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;

        const lgaParams = new URLSearchParams({
          geometry: `${lng},${lat}`, // longitude first, then latitude
          geometryType: 'esriGeometryPoint',
          inSR: '4326', // WGS84 coordinate system
          outFields: 'lganame', // We know the field name is 'lganame'
          f: 'json'
        });

        const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);

        if (lgaResponse.ok) {
          const lgaData = await lgaResponse.json();

          if (lgaData.features && lgaData.features.length > 0) {
            lgaName = lgaData.features[0].attributes.lganame;
            console.log('LGA from NSW Spatial Services (Layer 8):', lgaName);

            // Format the LGA name to title case for consistency
            if (lgaName) {
              // Convert to title case (e.g., "CITY OF SYDNEY" to "City of Sydney")
              lgaName = lgaName.toLowerCase().split(' ').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ');

              // Special case for "Shire" which should be capitalized in "The Hills Shire"
              lgaName = lgaName.replace('The Hills Shire', 'The Hills Shire');

              console.log('Formatted LGA name:', lgaName);
            }
          } else {
            console.log('No LGA features found from NSW Spatial Services API');
          }
        } else {
          console.error('NSW Spatial Services API response not OK:', lgaResponse.statusText);
        }
      }
    } catch (lgaError) {
      console.error('Error fetching LGA information:', lgaError);
    }

    // Step 3: If we couldn't get LGA from API, try to extract it from address
    if (!lgaName && address) {
      // Extract suburb from address
      const addressParts = address.toLowerCase().split(',').map(part => part.trim());
      let suburb = null;

      // Try to find the suburb part (usually the second part in "street, suburb, state postcode" format)
      if (addressParts.length >= 2) {
        suburb = addressParts[1];

        // Clean up suburb (remove "nsw" and postal code if present)
        suburb = suburb.replace(/\s*nsw\s*\d*\s*$/i, '').trim();

        console.log('Extracted suburb:', suburb);

        // Try to get LGA from suburb using Supabase
        if (suburb) {
          const mappedLGA = await getSuburbToLGAMapping(suburb);
          if (mappedLGA) {
            lgaName = mappedLGA;
            console.log('LGA from suburb mapping:', lgaName);
          }
        }
      }

      // If still no LGA, use pattern recognition
      if (!lgaName) {
        const lowerAddress = address.toLowerCase();

        if (lowerAddress.includes('dural')) {
          lgaName = 'The Hills Shire Council';
        } else if (lowerAddress.includes('glendenning')) {
          lgaName = 'City of Blacktown';
        } else if (lowerAddress.includes('newtown') || lowerAddress.includes('marrickville')) {
          lgaName = 'Inner West Council';
        } else if (lowerAddress.includes('sydney')) {
          lgaName = 'City of Sydney';
        } else if (lowerAddress.includes('mulgoa')) {
          lgaName = 'City of Penrith';
        } else {
          lgaName = 'Unknown Council';
        }

        console.log('LGA from pattern recognition:', lgaName);
      }
    }

    // Step 4: If we couldn't get zoning from API, use pattern recognition
    if (!zoneCode && address) {
      const lowerAddress = address.toLowerCase();

      // Commercial streets
      if (lowerAddress.includes('king street') && lowerAddress.includes('newtown')) {
        zoneCode = 'B2';
        zoneName = 'Local Centre';
      } else if (lowerAddress.includes('glendenning')) {
        zoneCode = 'IN1';
        zoneName = 'General Industrial';
      } else if (lowerAddress.includes('dural')) {
        zoneCode = 'RU2';
        zoneName = 'Rural Landscape';
      } else if (lowerAddress.includes('mulgoa')) {
        zoneCode = 'RU2';
        zoneName = 'Rural Landscape';
      } else if (/^\d+\//.test(address)) {
        // If address starts with unit number (e.g., 1/123), likely medium density
        zoneCode = 'R3';
        zoneName = 'Medium Density Residential';
      } else {
        // Default to low density residential
        zoneCode = 'R2';
        zoneName = 'Low Density Residential';
      }

      console.log('Zoning from pattern recognition:', zoneCode, zoneName);
    }

    // Step 5: Determine property type
    let propertyType = 'House'; // Default

    // Check for apartment/unit
    if (/^\d+\//.test(address)) {
      propertyType = 'Apartment/Unit';
    }
    // Check for commercial property
    else if (zoneCode && zoneCode.startsWith('B')) {
      propertyType = 'Commercial Property';
    }
    // Check for industrial property
    else if (zoneCode && zoneCode.startsWith('IN')) {
      propertyType = 'Industrial Property';
    }
    // Check for rural property
    else if (zoneCode && zoneCode.startsWith('RU')) {
      propertyType = 'Rural Property';
    }

    console.log('Determined property type:', propertyType);

    // Step 6: Determine curfew times based on zoning and property type
    let curfewStart = '22:00:00'; // Default: 10:00 PM
    let curfewEnd = '07:00:00';   // Default: 7:00 AM

    if (zoneCode && zoneCode.startsWith('B')) {
      // Commercial zones
      curfewStart = '00:00:00'; // 12:00 AM (midnight)
      curfewEnd = '07:00:00';   // 7:00 AM
    } else if (zoneCode && zoneCode.startsWith('IN')) {
      // Industrial zones
      curfewStart = '23:00:00'; // 11:00 PM
      curfewEnd = '07:00:00';   // 7:00 AM
    } else if (propertyType === 'Apartment/Unit') {
      // Apartments have earlier curfew
      curfewStart = '21:00:00'; // 9:00 PM
      curfewEnd = '07:00:00';   // 7:00 AM
    }

    // Step 7: Return the result
    return {
      body: {
        address,
        coordinates: { lat, lng },
        lga: lgaName || 'Unknown Council',
        zoning: `${zoneCode} - ${zoneName}`,
        curfew: `${formatCurfewTime(curfewStart)} to ${formatCurfewTime(curfewEnd)}`,
        property_type: propertyType,
        zone_code: zoneCode,
        zone_name: zoneName,
        lga_name: lgaName || 'Unknown Council',
        curfew_start: curfewStart,
        curfew_end: curfewEnd
      }
    };
  } catch (error) {
    console.error('Error in NSW Zoning API:', error);

    return {
      status: 500,
      body: {
        error: 'Failed to process zoning request',
        message: error.message
      }
    };
  }
}

// Helper function to format curfew time
function formatCurfewTime(timeString) {
  if (!timeString) return 'N/A';

  try {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const minute = parseInt(minutes, 10);

    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    const displayMinute = minute.toString().padStart(2, '0');

    return `${displayHour}:${displayMinute} ${period}`;
  } catch (error) {
    console.error('Error formatting curfew time:', error);
    return timeString;
  }
}
