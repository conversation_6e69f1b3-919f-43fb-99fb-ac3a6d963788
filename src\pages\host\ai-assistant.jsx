import React from 'react';
import { Archive, Clock, Settings } from 'lucide-react';

const HostAIAssistantPage = () => {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-gray-100 rounded-full">
            <Archive className="h-12 w-12 text-gray-400" />
          </div>
        </div>

        <h1 className="text-2xl font-bold text-gray-900 mb-4">AI Host Assistant - Temporarily Archived</h1>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-center mb-3">
            <Clock className="h-5 w-5 text-yellow-600 mr-2" />
            <h2 className="text-lg font-medium text-yellow-800">Feature Under Development</h2>
          </div>
          <p className="text-yellow-700 mb-4">
            We're currently enhancing the AI Host Assistant to provide you with even better personalized guidance
            for optimizing your venue listings and bookings.
          </p>
          <div className="text-sm text-yellow-600">
            <strong>What's coming:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Advanced pricing optimization recommendations</li>
              <li>Personalized marketing suggestions</li>
              <li>Automated guest communication templates</li>
              <li>Performance analytics and insights</li>
              <li>Seasonal demand forecasting</li>
            </ul>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-center mb-3">
            <Settings className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-blue-800">In the Meantime</h3>
          </div>
          <p className="text-blue-700 mb-4">
            You can still access all your essential host tools and features:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-white rounded-md p-3 border border-blue-200">
              <strong className="text-blue-900">📊 Dashboard</strong>
              <p className="text-blue-700">View your bookings and performance</p>
            </div>
            <div className="bg-white rounded-md p-3 border border-blue-200">
              <strong className="text-blue-900">🏠 Properties</strong>
              <p className="text-blue-700">Manage your venue listings</p>
            </div>
            <div className="bg-white rounded-md p-3 border border-blue-200">
              <strong className="text-blue-900">💰 Earnings</strong>
              <p className="text-blue-700">Track your payouts and revenue</p>
            </div>
            <div className="bg-white rounded-md p-3 border border-blue-200">
              <strong className="text-blue-900">💬 Messages</strong>
              <p className="text-blue-700">Communicate with guests</p>
            </div>
          </div>
        </div>

        <div className="text-gray-600">
          <p className="mb-4">
            We appreciate your patience as we work to deliver an exceptional AI-powered hosting experience.
          </p>
          <p className="text-sm">
            <strong>Expected reactivation:</strong> Q2 2025
          </p>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Have questions or suggestions for the AI Assistant?
            <a href="/contact" className="text-purple-600 hover:text-purple-800 ml-1">Contact our support team</a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default HostAIAssistantPage;
