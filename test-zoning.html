<!DOCTYPE html>
<html>
<head>
    <title>NSW Zoning Test</title>
    <script>
        async function testZoning() {
            const address = '10 Darvall Road, Eastwood, NSW 2122';
            const lat = -33.791;
            const lng = 151.080;
            
            try {
                const response = await fetch('/api/nsw-zoning', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address, lat, lng })
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = 
                    `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    `Error: ${error.message}`;
            }
        }
    </script>
</head>
<body>
    <button onclick="testZoning()">Test Zoning API</button>
    <div id="result"></div>
</body>
</html>
