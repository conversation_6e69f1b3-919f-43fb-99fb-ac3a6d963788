/**
 * Setup script for admin_users table in Supabase
 */
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupAdminUsersTable() {
  try {
    console.log('Setting up admin_users table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'src', 'lib', 'supabase', 'admin-users-table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      throw error;
    }
    
    console.log('Admin users table created successfully!');
    
    // Verify the table was created
    const { data, error: fetchError } = await supabase
      .from('admin_users')
      .select('email, role')
      .order('role');
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log('Admin users in the database:');
    console.table(data || []);
    console.log('Setup complete!');
  } catch (error) {
    console.error('Error setting up admin_users table:', error);
  }
}

// Run the setup
setupAdminUsersTable();
