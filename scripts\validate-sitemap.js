/**
 * Sitemap Validation Script
 *
 * This script validates the sitemap.xml file to ensure it's properly formatted.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { DOMParser } from 'xmldom';

// Get the directory name using ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');
const publicDir = path.join(rootDir, 'public');
const sitemapIndexPath = path.join(publicDir, 'sitemap_index.xml');
const sitemapMainPath = path.join(publicDir, 'sitemap_main.xml');

// Function to validate sitemap XML
function validateSitemapXML(xmlString) {
  try {
    // Create a new DOMParser
    const parser = new DOMParser({
      errorHandler: {
        warning: (w) => console.warn(w),
        error: (e) => { throw e; },
        fatalError: (e) => { throw e; }
      }
    });

    // Parse the XML
    const doc = parser.parseFromString(xmlString, 'application/xml');

    // Check for parse errors
    const errors = doc.getElementsByTagName('parsererror');
    if (errors.length > 0) {
      throw new Error('XML parsing error: ' + errors[0].textContent);
    }

    // Check for urlset element
    const urlset = doc.getElementsByTagName('urlset');
    if (urlset.length === 0) {
      throw new Error('Missing urlset element');
    }

    // Check for url elements
    const urls = doc.getElementsByTagName('url');
    if (urls.length === 0) {
      throw new Error('No URL entries found in sitemap');
    }

    // Count URLs
    console.log(`Sitemap contains ${urls.length} URL entries`);

    // Validate each URL
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const loc = url.getElementsByTagName('loc');

      if (loc.length === 0) {
        throw new Error(`URL at index ${i} is missing loc element`);
      }

      const locValue = loc[0].textContent;
      if (!locValue || !locValue.startsWith('http')) {
        throw new Error(`URL at index ${i} has invalid loc: ${locValue}`);
      }
    }

    return true;
  } catch (error) {
    console.error('Validation error:', error.message);
    return false;
  }
}

// Function to validate sitemap index XML
function validateSitemapIndexXML(xmlString) {
  try {
    // Create a new DOMParser
    const parser = new DOMParser({
      errorHandler: {
        warning: (w) => console.warn(w),
        error: (e) => { throw e; },
        fatalError: (e) => { throw e; }
      }
    });

    // Parse the XML
    const doc = parser.parseFromString(xmlString, 'application/xml');

    // Check for parse errors
    const errors = doc.getElementsByTagName('parsererror');
    if (errors.length > 0) {
      throw new Error('XML parsing error: ' + errors[0].textContent);
    }

    // Check for sitemapindex element
    const sitemapindex = doc.getElementsByTagName('sitemapindex');
    if (sitemapindex.length === 0) {
      throw new Error('Missing sitemapindex element');
    }

    // Check for sitemap elements
    const sitemaps = doc.getElementsByTagName('sitemap');
    if (sitemaps.length === 0) {
      throw new Error('No sitemap entries found in sitemap index');
    }

    // Count sitemaps
    console.log(`Sitemap index contains ${sitemaps.length} sitemap entries`);

    // Validate each sitemap entry
    for (let i = 0; i < sitemaps.length; i++) {
      const sitemap = sitemaps[i];
      const loc = sitemap.getElementsByTagName('loc');

      if (loc.length === 0) {
        throw new Error(`Sitemap at index ${i} is missing loc element`);
      }

      const locValue = loc[0].textContent;
      if (!locValue || !locValue.startsWith('http')) {
        throw new Error(`Sitemap at index ${i} has invalid loc: ${locValue}`);
      }
    }

    return true;
  } catch (error) {
    console.error('Validation error:', error.message);
    return false;
  }
}

// Main function
async function validateSitemap() {
  console.log('Validating sitemap files...');

  // Validate sitemap index
  if (!fs.existsSync(sitemapIndexPath)) {
    console.error('Error: sitemap_index.xml not found at', sitemapIndexPath);
    process.exit(1);
  }

  const sitemapIndexContent = fs.readFileSync(sitemapIndexPath, 'utf8');

  // Check for XML declaration in index
  if (!sitemapIndexContent.trim().startsWith('<?xml')) {
    console.error('Error: Missing XML declaration in sitemap index');
    process.exit(1);
  }

  // Validate XML structure of index
  console.log('Validating sitemap index...');
  const isIndexValid = validateSitemapIndexXML(sitemapIndexContent);

  if (!isIndexValid) {
    console.error('Sitemap index validation failed');
    process.exit(1);
  }

  // Validate main sitemap
  if (!fs.existsSync(sitemapMainPath)) {
    console.error('Error: sitemap_main.xml not found at', sitemapMainPath);
    process.exit(1);
  }

  const sitemapMainContent = fs.readFileSync(sitemapMainPath, 'utf8');

  // Check for XML declaration in main sitemap
  if (!sitemapMainContent.trim().startsWith('<?xml')) {
    console.error('Error: Missing XML declaration in main sitemap');
    process.exit(1);
  }

  // Validate XML structure of main sitemap
  console.log('Validating main sitemap...');
  const isMainValid = validateSitemapXML(sitemapMainContent);

  if (!isMainValid) {
    console.error('Main sitemap validation failed');
    process.exit(1);
  }

  console.log('All sitemap files validation successful!');

  // Write clean versions of the sitemaps
  const cleanSitemapIndex = sitemapIndexContent
    .replace(/<!--.*?-->/gs, '') // Remove comments
    .replace(/>\s+</g, '>\n<')   // Normalize line breaks
    .replace(/\s+$/gm, '');      // Remove trailing whitespace

  const cleanSitemapMain = sitemapMainContent
    .replace(/<!--.*?-->/gs, '') // Remove comments
    .replace(/>\s+</g, '>\n<')   // Normalize line breaks
    .replace(/\s+$/gm, '');      // Remove trailing whitespace

  fs.writeFileSync(sitemapIndexPath, cleanSitemapIndex);
  fs.writeFileSync(sitemapMainPath, cleanSitemapMain);

  // Also update the backward compatibility file
  fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), cleanSitemapIndex);

  console.log('Cleaned sitemap files written to:');
  console.log('- public/sitemap_index.xml');
  console.log('- public/sitemap_main.xml');
  console.log('- public/sitemap.xml (copy of index for backward compatibility)');
}

// Run the validation
validateSitemap().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
