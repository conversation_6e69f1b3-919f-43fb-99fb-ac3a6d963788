import { CLERK_CONFIG } from '../config/clerk';
import { setUserRole } from '../lib/supabase';

/**
 * Initiates the Clerk OAuth flow for different registration types
 * @param registrationType The type of registration (host or guest)
 * @param provider The OAuth provider to use (default: 'google')
 */
export function initiateClerkOAuth(
  registrationType: 'host' | 'guest',
  provider: 'google' | 'github' | 'apple' = 'google'
): void {
  // Store the registration type in localStorage for the callback to use
  localStorage.setItem('registering_as_host', registrationType === 'host' ? 'true' : 'false');

  // Determine the callback URL based on registration type
  const callbackUrl = encodeURIComponent(
    `${window.location.origin}/auth/callback?registrationType=${registrationType}`
  );

  // Log the OAuth initiation for debugging
  console.log('Initiating Clerk OAuth flow:', {
    registrationType,
    provider,
    callbackUrl
  });

  // Construct the OAuth URL using the Clerk domain from config
  const oauthUrl = `https://${CLERK_CONFIG.clerkDomain}/oauth/${provider}?redirect_url=${callbackUrl}`;

  // Redirect to the OAuth URL
  window.location.href = oauthUrl;
}

/**
 * Handles the OAuth callback
 * @param params The URL search params from the callback
 */
export async function handleOAuthCallback(params: URLSearchParams): Promise<{
  success: boolean;
  redirectTo: string;
  message: string;
}> {
  // Get the registration type from the URL params or localStorage
  const registrationType = params.get('registrationType') ||
    (localStorage.getItem('registering_as_host') === 'true' ? 'host' : 'guest');

  // Get the destination from URL params
  const destination = params.get('destination');

  // Log the callback for debugging
  console.log('Handling OAuth callback:', {
    registrationType,
    destination,
    params: Object.fromEntries(params.entries())
  });

  // Determine the redirect URL and message based on registration type
  let redirectTo = '/';
  let message = 'Authentication successful';

  if (registrationType === 'host' || destination === 'host-portal') {
    // Update role in Supabase
    try {
      await setUserRole('host');
      console.log('OAuth callback: User role updated to host in Supabase');

      // Store in localStorage as backup
      localStorage.setItem('registering_as_host', 'true');

      // Add to registered hosts in localStorage if email is available
      const userEmail = localStorage.getItem('userEmail');
      if (userEmail) {
        try {
          const registeredHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
          if (!registeredHosts.includes(userEmail)) {
            registeredHosts.push(userEmail);
            localStorage.setItem('registeredHosts', JSON.stringify(registeredHosts));
          }
        } catch (err) {
          console.error('Failed to update registeredHosts in localStorage:', err);
        }
      }
    } catch (error) {
      console.error('OAuth callback: Error updating role in Supabase:', error);
    }

    redirectTo = '/host/dashboard?registration=success';
    message = 'Host registration successful';
  } else {
    // For regular users
    redirectTo = '/my-account';
    message = 'Sign in successful';

    // Clear any host-related flags to ensure clean state
    localStorage.removeItem('registering_as_host');
  }

  return {
    success: true,
    redirectTo,
    message
  };
}

/**
 * Checks if the current user is authenticated as a host
 * This is a client-side check that combines multiple sources
 */
export function isAuthenticatedAsHost(): boolean {
  // Check localStorage first (fastest)
  const isHostFromLocalStorage = localStorage.getItem('registering_as_host') === 'true';

  // Check if the user is in the registered hosts list
  let isInRegisteredHosts = false;
  try {
    const registeredHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
    const userEmail = localStorage.getItem('userEmail');
    isInRegisteredHosts = userEmail ? registeredHosts.includes(userEmail) : false;
  } catch (err) {
    console.error('Error checking registered hosts:', err);
  }

  return isHostFromLocalStorage || isInRegisteredHosts;
}
