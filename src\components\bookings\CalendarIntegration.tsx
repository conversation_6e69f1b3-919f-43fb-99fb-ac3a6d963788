import React from 'react';
import { Calendar, Download } from 'lucide-react';

interface CalendarEvent {
  title: string;
  description: string;
  location: string;
  startDate: Date;
  endDate: Date;
  url?: string;
}

interface CalendarIntegrationProps {
  event: CalendarEvent;
  className?: string;
}

export const CalendarIntegration: React.FC<CalendarIntegrationProps> = ({
  event,
  className = ''
}) => {
  // Format dates for calendar services
  const formatDate = (date: Date) => {
    return date.toISOString().replace(/-|:|\.\d+/g, '');
  };
  
  // Generate Google Calendar URL
  const getGoogleCalendarUrl = () => {
    const startDate = formatDate(event.startDate);
    const endDate = formatDate(event.endDate);
    
    return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${startDate}/${endDate}&details=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}&sprop=website:${encodeURIComponent(event.url || window.location.href)}`;
  };
  
  // Generate iCal file content
  const getICalContent = () => {
    const startDate = formatDate(event.startDate);
    const endDate = formatDate(event.endDate);
    const now = formatDate(new Date());
    
    return `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//HouseGoing//Calendar Integration//EN
CALSCALE:GREGORIAN
METHOD:PUBLISH
BEGIN:VEVENT
SUMMARY:${event.title}
DTSTART:${startDate}
DTEND:${endDate}
DTSTAMP:${now}
LOCATION:${event.location}
DESCRIPTION:${event.description}
URL:${event.url || window.location.href}
STATUS:CONFIRMED
SEQUENCE:0
END:VEVENT
END:VCALENDAR`;
  };
  
  // Generate Outlook.com URL
  const getOutlookUrl = () => {
    const startDate = event.startDate.toISOString().slice(0, 19);
    const endDate = event.endDate.toISOString().slice(0, 19);
    
    return `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(event.title)}&startdt=${startDate}&enddt=${endDate}&body=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}`;
  };
  
  // Download iCal file
  const downloadICalFile = () => {
    const content = getICalContent();
    const element = document.createElement('a');
    const file = new Blob([content], { type: 'text/calendar' });
    
    element.href = URL.createObjectURL(file);
    element.download = `${event.title.replace(/\s+/g, '-').toLowerCase()}.ics`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };
  
  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      <h3 className="text-sm font-medium text-gray-700 mb-2">Add to Calendar</h3>
      
      <div className="flex flex-wrap gap-2">
        {/* Google Calendar */}
        <a
          href={getGoogleCalendarUrl()}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center px-3 py-1.5 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <Calendar className="w-4 h-4 mr-2 text-red-500" />
          <span>Google</span>
        </a>
        
        {/* Outlook.com */}
        <a
          href={getOutlookUrl()}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center px-3 py-1.5 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <Calendar className="w-4 h-4 mr-2 text-blue-500" />
          <span>Outlook</span>
        </a>
        
        {/* iCal Download */}
        <button
          onClick={downloadICalFile}
          className="inline-flex items-center px-3 py-1.5 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <Download className="w-4 h-4 mr-2 text-purple-500" />
          <span>iCal</span>
        </button>
      </div>
    </div>
  );
};
