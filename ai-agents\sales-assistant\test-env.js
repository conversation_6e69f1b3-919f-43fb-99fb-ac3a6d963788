import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('Environment variables:');
console.log('HUGGINGFACE_API_KEY:', process.env.HUGGINGFACE_API_KEY);
console.log('LANGSMITH_API_KEY:', process.env.LANGSMITH_API_KEY);
console.log('LANGSMITH_PROJECT:', process.env.LANGSMITH_PROJECT);
console.log('LANGSMITH_TRACING:', process.env.LANGSMITH_TRACING);
console.log('LANGSMITH_ENDPOINT:', process.env.LANGSMITH_ENDPOINT);

// Configure <PERSON><PERSON>mith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;

console.log('\nLangChain environment variables:');
console.log('LANGCHAIN_TRACING_V2:', process.env.LANGCHAIN_TRACING_V2);
console.log('LANGCHAIN_ENDPOINT:', process.env.LANGCHAIN_ENDPOINT);
console.log('LANGCHAIN_API_KEY:', process.env.LANGCHAIN_API_KEY);
console.log('LANGCHAIN_PROJECT:', process.env.LANGCHAIN_PROJECT);
