-- SMART RLS SETUP for HouseGoing - Lock Only Security-Critical Tables
-- This script enables <PERSON><PERSON> only on tables that contain sensitive user data
-- Public venue data remains accessible to anonymous users

-- Step 1: Check current state
SELECT 'BEFORE CHANGES:' as status;
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- Step 2: Define which tables need RLS (security-critical tables only)
DO $$
DECLARE
    security_tables TEXT[] := ARRAY[
        'user_profiles',        -- User personal data
        'profiles',            -- User profile information
        'bookings',            -- User booking history
        'messages',            -- Private conversations
        'saved_venues',        -- User's saved venues
        'notifications',       -- User notifications
        'payment_methods',     -- Payment information
        'reviews',             -- User reviews (may need user-specific policies)
        'property_submissions', -- Owner property submissions
        'admin_users'          -- Admin access data
    ];
    table_name TEXT;
BEGIN
    RAISE NOTICE 'Starting SMART RLS setup for security-critical tables...';

    -- Enable RLS and create policies for security-critical tables only
    FOREACH table_name IN ARRAY security_tables
    LOOP
        -- Check if table exists
        IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = table_name) THEN
            BEGIN
                -- Enable RLS
                EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_name);
                RAISE NOTICE 'Enabled RLS on public.%', table_name;

                -- Create appropriate policies based on table type
                IF table_name = 'admin_users' THEN
                    -- Admin table - only admins can access
                    EXECUTE format('
                        CREATE POLICY "admin_only_policy" ON public.%I
                        FOR ALL
                        TO authenticated
                        USING (auth.jwt() ->> ''role'' = ''admin'')
                        WITH CHECK (auth.jwt() ->> ''role'' = ''admin'')
                    ', table_name);
                    RAISE NOTICE 'Created admin-only policy on public.%', table_name;

                ELSIF table_name IN ('user_profiles', 'profiles', 'bookings', 'saved_venues', 'notifications', 'payment_methods') THEN
                    -- User-specific tables - users can only see their own data
                    EXECUTE format('
                        CREATE POLICY "user_own_data_policy" ON public.%I
                        FOR ALL
                        TO authenticated
                        USING (auth.uid() = user_id OR auth.uid() = id)
                        WITH CHECK (auth.uid() = user_id OR auth.uid() = id)
                    ', table_name);
                    RAISE NOTICE 'Created user-own-data policy on public.%', table_name;

                ELSE
                    -- Default authenticated user policy for other sensitive tables
                    EXECUTE format('
                        CREATE POLICY "authenticated_users_policy" ON public.%I
                        FOR ALL
                        TO authenticated
                        USING (true)
                        WITH CHECK (true)
                    ', table_name);
                    RAISE NOTICE 'Created authenticated-users policy on public.%', table_name;
                END IF;

            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Error on public.%: %', table_name, SQLERRM;
            END;
        ELSE
            RAISE NOTICE 'Table public.% does not exist, skipping...', table_name;
        END IF;
    END LOOP;

    RAISE NOTICE 'SMART RLS setup completed!';
    RAISE NOTICE 'Security-critical tables now have RLS enabled';
    RAISE NOTICE 'Public venue data remains accessible to anonymous users';
END;
$$;

-- Step 3: Verify changes
SELECT 'AFTER CHANGES:' as status;
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- Step 4: Show all policies created
SELECT 'POLICIES CREATED:' as status;
SELECT schemaname, tablename, policyname, cmd, roles
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
