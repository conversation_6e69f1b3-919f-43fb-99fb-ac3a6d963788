-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  clerk_id TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  role TEXT NOT NULL DEFAULT 'guest',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on clerk_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON user_profiles(clerk_id);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- <PERSON>reate function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON> trigger to update updated_at timestamp
CREATE TRIGGER update_user_profiles_updated_at
BEFORE UPDATE ON user_profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insert pre-registered hosts
INSERT INTO user_profiles (clerk_id, email, role)
VALUES 
  ('pre-registered-host-1', '<EMAIL>', 'host')
ON CONFLICT (email) 
DO UPDATE SET role = 'host', updated_at = NOW();

-- Create RLS policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own profile
CREATE POLICY read_own_profile ON user_profiles
  FOR SELECT
  USING (auth.uid()::text = clerk_id);

-- Policy for users to update their own profile
CREATE POLICY update_own_profile ON user_profiles
  FOR UPDATE
  USING (auth.uid()::text = clerk_id);

-- Policy for service role to read all profiles
CREATE POLICY service_read_all_profiles ON user_profiles
  FOR SELECT
  TO service_role
  USING (true);

-- Policy for service role to update all profiles
CREATE POLICY service_update_all_profiles ON user_profiles
  FOR UPDATE
  TO service_role
  USING (true);

-- Policy for service role to insert profiles
CREATE POLICY service_insert_profiles ON user_profiles
  FOR INSERT
  TO service_role
  WITH CHECK (true);
