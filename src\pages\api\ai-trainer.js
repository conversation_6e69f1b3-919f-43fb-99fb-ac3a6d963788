/**
 * API endpoint for AI-assisted training
 */
import { analyzeConversation, suggestPromptImprovements, autoImprovePrompt } from '../../lib/langchain/ai-trainer';
import { getFeedbackByAgentType } from '../../lib/langchain/feedback';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { action, data } = req.body;

    if (!action) {
      return res.status(400).json({ error: 'Action is required' });
    }

    switch (action) {
      case 'analyze-conversation':
        // Validate request
        if (!data?.conversation || !data?.agentType) {
          return res.status(400).json({ error: 'Conversation and agent type are required' });
        }
        
        // Analyze the conversation
        const analysis = await analyzeConversation(data.conversation, data.agentType);
        
        return res.status(200).json({ 
          success: true,
          analysis
        });
        
      case 'suggest-improvements':
        // Validate request
        if (!data?.agentType) {
          return res.status(400).json({ error: 'Agent type is required' });
        }
        
        // Get feedback for this agent type
        const feedback = await getFeedbackByAgentType(data.agentType);
        
        // If no feedback, return error
        if (!feedback || feedback.length === 0) {
          return res.status(400).json({ 
            success: false,
            error: 'No feedback available for this agent type' 
          });
        }
        
        // Get current prompt
        const currentPrompt = data.currentPrompt;
        
        // Suggest improvements
        const improvements = await suggestPromptImprovements(
          currentPrompt,
          feedback,
          data.agentType
        );
        
        return res.status(200).json({ 
          success: true,
          improvements
        });
        
      case 'auto-improve':
        // Validate request
        if (!data?.agentType) {
          return res.status(400).json({ error: 'Agent type is required' });
        }
        
        // Auto-improve the prompt
        const result = await autoImprovePrompt(data.agentType);
        
        return res.status(200).json({ 
          success: result.success,
          result
        });
        
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Error in AI trainer API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
