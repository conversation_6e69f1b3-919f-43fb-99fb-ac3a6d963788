import { useState, useEffect, useCallback } from 'react';
import { GeminiSalesAgent, Message, ConversationStage } from '../lib/salesgpt/gemini-agent';

// Define the return type for the hook
interface UseGeminiAgentReturn {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string) => Promise<void>;
  resetConversation: () => Promise<void>;
  currentStage: ConversationStage | null;
}

export function useGeminiAgent(apiKey?: string): UseGeminiAgentReturn {
  const [agent, setAgent] = useState<GeminiSalesAgent | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStage, setCurrentStage] = useState<ConversationStage | null>(null);

  // Initialize the agent
  useEffect(() => {
    const initializeAgent = async () => {
      try {
        // Use the provided API key or get it from environment variables
        const key = apiKey || import.meta.env.VITE_GEMINI_API_KEY || 'AIzaSyDVad8ViigUUeKIqdO4cl-slUa5_1UrA8w';
        
        if (!key) {
          setError('Gemini API key is required');
          return;
        }
        
        const newAgent = new GeminiSalesAgent(key);
        setAgent(newAgent);
        setCurrentStage(newAgent.getCurrentStage());
        
        // Add initial AI message
        const initialMessage: Message = {
          id: Date.now().toString(),
          role: 'ai',
          content: 'Hi there! I\'m Alex from HouseGoing. How can I help you find the perfect venue for your event today?',
          timestamp: new Date(),
        };
        
        setMessages([initialMessage]);
      } catch (err: any) {
        setError(err.message || 'Failed to initialize Gemini agent');
        console.error('Error initializing Gemini agent:', err);
      }
    };

    initializeAgent();
  }, [apiKey]);

  // Send a message to the agent
  const sendMessage = useCallback(async (message: string) => {
    if (!agent) {
      setError('Agent not initialized');
      return;
    }

    try {
      setIsLoading(true);
      
      // Add user message to the list
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'human',
        content: message,
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, userMessage]);
      
      // Process the message with the agent
      const response = await agent.processMessage(message);
      
      // Add AI response to the list
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'ai',
        content: response,
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, aiMessage]);
      
      // Update the current stage
      setCurrentStage(agent.getCurrentStage());
    } catch (err: any) {
      setError(err.message || 'Failed to process message');
      console.error('Error processing message:', err);
    } finally {
      setIsLoading(false);
    }
  }, [agent]);

  // Reset the conversation
  const resetConversation = useCallback(async () => {
    if (!agent) {
      return;
    }

    try {
      await agent.resetConversation();
      setCurrentStage(agent.getCurrentStage());
      
      // Add initial AI message
      const initialMessage: Message = {
        id: Date.now().toString(),
        role: 'ai',
        content: 'Hi there! I\'m Alex from HouseGoing. How can I help you find the perfect venue for your event today?',
        timestamp: new Date(),
      };
      
      setMessages([initialMessage]);
    } catch (err: any) {
      setError(err.message || 'Failed to reset conversation');
      console.error('Error resetting conversation:', err);
    }
  }, [agent]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    resetConversation,
    currentStage,
  };
}
