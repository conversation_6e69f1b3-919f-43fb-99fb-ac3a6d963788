import { supabase } from '../../supabase';
import { Migration } from './index';

/**
 * Migration to add AI analytics tables
 */
const migration: Migration = {
  id: 2,
  name: 'add_ai_analytics',
  
  up: async () => {
    // Create ai_sessions table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_sessions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES profiles(id),
          agent_type TEXT NOT NULL,
          session_id TEXT NOT NULL,
          started_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          ended_at TIMESTAMP WITH TIME ZONE,
          rating INTEGER,
          feedback TEXT,
          
          CONSTRAINT valid_agent_type CHECK (agent_type IN ('sales', 'host', 'booking', 'support'))
        );
        
        CREATE INDEX IF NOT EXISTS ai_sessions_user_id_idx ON ai_sessions(user_id);
        CREATE INDEX IF NOT EXISTS ai_sessions_agent_type_idx ON ai_sessions(agent_type);
      `
    });
    
    // Create ai_messages table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_messages (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          session_id UUID NOT NULL REFERENCES ai_sessions(id),
          role TEXT NOT NULL,
          content TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
          
          CONSTRAINT valid_role CHECK (role IN ('user', 'assistant'))
        );
        
        CREATE INDEX IF NOT EXISTS ai_messages_session_id_idx ON ai_messages(session_id);
      `
    });
    
    // Create ai_intents table
    await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_intents (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          message_id UUID NOT NULL REFERENCES ai_messages(id),
          intent TEXT NOT NULL,
          confidence DECIMAL NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
        );
        
        CREATE INDEX IF NOT EXISTS ai_intents_message_id_idx ON ai_intents(message_id);
        CREATE INDEX IF NOT EXISTS ai_intents_intent_idx ON ai_intents(intent);
      `
    });
  },
  
  down: async () => {
    // Drop tables in reverse order
    await supabase.rpc('execute_sql', {
      sql: `
        DROP TABLE IF EXISTS ai_intents;
        DROP TABLE IF EXISTS ai_messages;
        DROP TABLE IF EXISTS ai_sessions;
      `
    });
  }
};

export default migration;
