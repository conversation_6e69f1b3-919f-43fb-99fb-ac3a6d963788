import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { clerkAppearance } from '../../utils/clerk-theme';
import { CLERK_CONFIG } from '../../config/clerk';
import GoogleButton from '../../components/auth/GoogleButton';
import EmailPasswordSignIn from '../../components/auth/EmailPasswordSignIn';

export default function ClerkSignInPage() {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirectTo = searchParams.get('redirectTo') || '/';
  const userType = searchParams.get('userType') as 'host' | 'guest' || 'guest';
  const isHost = userType === 'host';

  const [showEmailPasswordForm, setShowEmailPasswordForm] = useState(false);
  const [showDirectOAuth, setShowDirectOAuth] = useState(false);

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link to="/">
          <img
            className="mx-auto h-12 w-auto"
            src="/images/housegoing-logo.svg"
            alt="HouseGoing"
          />
        </Link>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {isHost ? 'Owner Portal Sign In' : 'Sign in to your account'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {isHost
            ? 'Access your Owner Portal dashboard and manage your properties'
            : 'Find and book the perfect venue for your next event'}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {showDirectOAuth ? (
            <div className="space-y-6">
              <ClerkSignIn
                appearance={clerkAppearance}
                routing="path"
                path={isHost ? "/host/login" : "/login"}
                redirectUrl={isHost ? CLERK_CONFIG.hostSignInRedirectURL : CLERK_CONFIG.signInRedirectURL}
                signUpUrl={isHost ? "/host/signup" : "/signup"}
                afterSignInUrl={isHost ? CLERK_CONFIG.hostSignInRedirectURL : CLERK_CONFIG.afterSignInURL}
              />

              <div className="mt-4 text-center">
                <button
                  onClick={() => setShowDirectOAuth(false)}
                  className="text-sm text-purple-600 hover:text-purple-800"
                >
                  Back to standard sign in
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="mb-8">
                <p className="text-center text-base text-gray-600 mb-4">Sign in with:</p>
                <GoogleButton
                  registrationType={isHost ? "host" : "guest"}
                  label="Continue with Google"
                />
              </div>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center">
                  <span className="px-4 py-1 bg-white text-gray-500 text-base">Or sign in with email</span>
                </div>
              </div>

              {showEmailPasswordForm ? (
                <>
                  <EmailPasswordSignIn
                    role={isHost ? "host" : "guest"}
                    onError={(error) => console.error('Sign in error:', error)}
                  />
                  <div className="mt-4 text-center">
                    <button
                      onClick={() => setShowEmailPasswordForm(false)}
                      className="text-sm text-purple-600 hover:text-purple-800"
                    >
                      Back to standard sign in
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setShowEmailPasswordForm(true)}
                    className="w-full bg-white border border-gray-300 rounded-full shadow-md hover:shadow-lg text-lg font-medium text-gray-700 py-4 px-8 transition-all duration-200 transform hover:-translate-y-0.5 flex items-center justify-center mb-4 relative overflow-hidden"
                  >
                    <span className="mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </span>
                    Sign in with Email
                  </button>
                </>
              )}

              <div className="mt-6 text-center">
                <button
                  onClick={() => setShowDirectOAuth(true)}
                  className="text-sm text-purple-600 hover:text-purple-800"
                >
                  Having trouble? Try direct sign in
                </button>
              </div>
            </div>
          )}

          {isHost ? (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Looking to book a venue instead?{' '}
                <Link to="/login" className="font-medium text-purple-600 hover:text-purple-500">
                  Sign in as a guest
                </Link>
              </p>
            </div>
          ) : (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Are you a venue owner?{' '}
                <Link to="/host/login" className="font-medium text-purple-600 hover:text-purple-500">
                  Go to Owner Portal
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
