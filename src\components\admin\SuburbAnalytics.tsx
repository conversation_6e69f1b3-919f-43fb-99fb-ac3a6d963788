/**
 * Suburb Analytics Component
 * Shows suburb-wise search and booking analytics with IP/user deduplication
 */

import React, { useState, useEffect } from 'react';
import { 
  MapPin, 
  Search, 
  Calendar, 
  TrendingUp, 
  Users, 
  DollarSign,
  BarChart3,
  RefreshCw,
  Filter
} from 'lucide-react';
import {
  getTopSearchedSuburbs,
  getTopBookedSuburbs,
  getSuburbConversionRates,
  SuburbSearchAnalytics,
  SuburbBookingAnalytics,
  SuburbConversionAnalytics
} from '../../api/analytics';

export default function SuburbAnalytics() {
  const [searchData, setSearchData] = useState<SuburbSearchAnalytics[]>([]);
  const [bookingData, setBookingData] = useState<SuburbBookingAnalytics[]>([]);
  const [conversionData, setConversionData] = useState<SuburbConversionAnalytics[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'searches' | 'bookings' | 'conversions'>('searches');
  const [timeRange, setTimeRange] = useState(30); // days
  const [limit, setLimit] = useState(10);

  // Load analytics data
  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const [searches, bookings, conversions] = await Promise.all([
        getTopSearchedSuburbs(limit, timeRange),
        getTopBookedSuburbs(limit, timeRange),
        getSuburbConversionRates(limit, timeRange)
      ]);

      setSearchData(searches);
      setBookingData(bookings);
      setConversionData(conversions);

      console.log('✅ Suburb analytics loaded:', {
        searches: searches.length,
        bookings: bookings.length,
        conversions: conversions.length
      });
    } catch (error) {
      console.error('Error loading suburb analytics:', error);
      // Show sample data in development mode
      if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log('🔧 Development mode: Using sample analytics data');
        setSearchData([
          { suburb: 'Ryde', state: 'NSW', search_count: 45, unique_users: 32, unique_ips: 38, last_search: new Date().toISOString() },
          { suburb: 'Parramatta', state: 'NSW', search_count: 38, unique_users: 28, unique_ips: 35, last_search: new Date().toISOString() },
          { suburb: 'Bondi', state: 'NSW', search_count: 32, unique_users: 25, unique_ips: 30, last_search: new Date().toISOString() },
          { suburb: 'Circular Quay', state: 'NSW', search_count: 28, unique_users: 22, unique_ips: 26, last_search: new Date().toISOString() },
          { suburb: 'Manly', state: 'NSW', search_count: 24, unique_users: 18, unique_ips: 22, last_search: new Date().toISOString() }
        ]);
        setBookingData([
          { suburb: 'Ryde', state: 'NSW', booking_count: 12, total_revenue: 14400, unique_users: 12, avg_booking_amount: 1200, last_booking: new Date().toISOString() },
          { suburb: 'Parramatta', state: 'NSW', booking_count: 8, total_revenue: 12000, unique_users: 8, avg_booking_amount: 1500, last_booking: new Date().toISOString() },
          { suburb: 'Bondi', state: 'NSW', booking_count: 6, total_revenue: 10800, unique_users: 6, avg_booking_amount: 1800, last_booking: new Date().toISOString() }
        ]);
        setConversionData([
          { suburb: 'Ryde', state: 'NSW', search_count: 45, booking_count: 12, conversion_rate: 26.67, revenue_per_search: 320 },
          { suburb: 'Parramatta', state: 'NSW', search_count: 38, booking_count: 8, conversion_rate: 21.05, revenue_per_search: 315.79 },
          { suburb: 'Bondi', state: 'NSW', search_count: 32, booking_count: 6, conversion_rate: 18.75, revenue_per_search: 337.50 }
        ]);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeRange, limit]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Suburb Analytics</h2>
          <p className="text-gray-600">Track search and booking patterns by suburb with IP/user deduplication</p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Time Range Filter */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
            <option value={365}>Last year</option>
          </select>

          {/* Limit Filter */}
          <select
            value={limit}
            onChange={(e) => setLimit(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value={5}>Top 5</option>
            <option value={10}>Top 10</option>
            <option value={20}>Top 20</option>
            <option value={50}>Top 50</option>
          </select>

          {/* Refresh Button */}
          <button
            onClick={loadAnalytics}
            disabled={loading}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('searches')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'searches'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Search className="h-4 w-4 inline mr-2" />
            Top Searched Suburbs
          </button>
          <button
            onClick={() => setActiveTab('bookings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'bookings'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Calendar className="h-4 w-4 inline mr-2" />
            Top Booked Suburbs
          </button>
          <button
            onClick={() => setActiveTab('conversions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'conversions'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <TrendingUp className="h-4 w-4 inline mr-2" />
            Conversion Rates
          </button>
        </nav>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          <span className="ml-3 text-purple-600">Loading analytics...</span>
        </div>
      )}

      {/* Search Analytics Tab */}
      {activeTab === 'searches' && !loading && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Most Searched Suburbs</h3>
            <p className="text-sm text-gray-500">Unique searches by different users/IPs</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <MapPin className="h-4 w-4 inline mr-1" />
                    Suburb
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Search className="h-4 w-4 inline mr-1" />
                    Total Searches
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Users className="h-4 w-4 inline mr-1" />
                    Unique Users
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Unique IPs
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Search
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {searchData.map((item, index) => (
                  <tr key={`${item.suburb}-${item.state}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">#{index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{item.suburb}</div>
                        <div className="text-sm text-gray-500">{item.state}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 font-medium">{item.search_count}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{item.unique_users}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{item.unique_ips}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-500">{formatDate(item.last_search)}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {searchData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No search data available for the selected time period</p>
            </div>
          )}
        </div>
      )}

      {/* Booking Analytics Tab */}
      {activeTab === 'bookings' && !loading && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Most Booked Suburbs</h3>
            <p className="text-sm text-gray-500">Revenue and booking data by suburb</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <MapPin className="h-4 w-4 inline mr-1" />
                    Suburb
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Calendar className="h-4 w-4 inline mr-1" />
                    Bookings
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <DollarSign className="h-4 w-4 inline mr-1" />
                    Total Revenue
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Users className="h-4 w-4 inline mr-1" />
                    Unique Users
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Booking
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Booking
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {bookingData.map((item, index) => (
                  <tr key={`${item.suburb}-${item.state}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">#{index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{item.suburb}</div>
                        <div className="text-sm text-gray-500">{item.state}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 font-medium">{item.booking_count}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 font-medium text-green-600">
                        {formatCurrency(item.total_revenue)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{item.unique_users}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{formatCurrency(item.avg_booking_amount)}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-500">{formatDate(item.last_booking)}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {bookingData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No booking data available for the selected time period</p>
            </div>
          )}
        </div>
      )}

      {/* Conversion Analytics Tab */}
      {activeTab === 'conversions' && !loading && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Suburb Conversion Rates</h3>
            <p className="text-sm text-gray-500">Search to booking conversion by suburb</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <MapPin className="h-4 w-4 inline mr-1" />
                    Suburb
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Search className="h-4 w-4 inline mr-1" />
                    Searches
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Calendar className="h-4 w-4 inline mr-1" />
                    Bookings
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <TrendingUp className="h-4 w-4 inline mr-1" />
                    Conversion Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <DollarSign className="h-4 w-4 inline mr-1" />
                    Revenue/Search
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {conversionData.map((item, index) => (
                  <tr key={`${item.suburb}-${item.state}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">#{index + 1}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{item.suburb}</div>
                        <div className="text-sm text-gray-500">{item.state}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{item.search_count}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{item.booking_count}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-medium ${
                        item.conversion_rate >= 10 ? 'text-green-600' :
                        item.conversion_rate >= 5 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {item.conversion_rate}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">{formatCurrency(item.revenue_per_search)}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {conversionData.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No conversion data available for the selected time period</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
