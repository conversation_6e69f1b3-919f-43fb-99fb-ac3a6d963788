/**
 * Test NSW Party Planning & Noise Guide with Fixed LGA Detection
 * 
 * This script tests the NSW Party Planning & Noise Guide API
 * to verify that the LGA detection is working correctly with Layer 8.
 */

// Test addresses
const testAddresses = [
  '23 John Radley Avenue, Dural, NSW 2158',
  '36 Earle St, Doonside NSW 2767',
  '10 Carlingford Road, Carlingford',
  '10 Darvall Road, Eastwood, NSW 2122',
  '123 King Street, Newtown, NSW 2042',
  '456 George Street, Sydney, NSW 2000'
];

// Test the NSW Spatial Services API Layer 8 for LGA detection
async function testLGADetection(address) {
  try {
    console.log(`Testing address: ${address}`);
    
    // First, geocode the address
    const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA&country=AU&limit=1`;
    
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    if (geocodeData.features && geocodeData.features.length > 0) {
      const [lng, lat] = geocodeData.features[0].center;
      console.log(`Geocoded coordinates: ${lat}, ${lng}`);
      
      // NSW Spatial Services API endpoint for LGA (Layer 8)
      const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
      
      const lgaParams = new URLSearchParams({
        geometry: `${lng},${lat}`, // longitude first, then latitude
        geometryType: 'esriGeometryPoint',
        inSR: '4326', // WGS84 coordinate system
        outFields: 'lganame', // We know the field name is 'lganame'
        f: 'json'
      });
      
      const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
      
      if (lgaResponse.ok) {
        const lgaData = await lgaResponse.json();
        
        if (lgaData.features && lgaData.features.length > 0) {
          const lgaName = lgaData.features[0].attributes.lganame;
          console.log('LGA from NSW Spatial Services (Layer 8):', lgaName);
          
          // Format the LGA name to title case for consistency
          if (lgaName) {
            // Convert to title case (e.g., "CITY OF SYDNEY" to "City of Sydney")
            const formattedLgaName = lgaName.toLowerCase().split(' ').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');
            
            console.log('Formatted LGA name:', formattedLgaName);
            return formattedLgaName;
          }
        } else {
          console.log('No LGA information found from Layer 8 API');
        }
      } else {
        console.error('NSW Spatial Services API response not OK:', lgaResponse.statusText);
      }
    } else {
      console.error('Geocoding failed:', geocodeData);
    }
  } catch (error) {
    console.error('Error testing LGA detection:', error);
  }
  
  return null;
}

// Run the tests
async function runTests() {
  console.log('Testing NSW Party Planning & Noise Guide with Fixed LGA Detection...\n');
  
  for (const address of testAddresses) {
    console.log(`\n=== Testing address: ${address} ===`);
    
    const lgaName = await testLGADetection(address);
    
    if (lgaName) {
      console.log(`\nResult: "${address}" belongs to "${lgaName}"\n`);
    } else {
      console.log(`\nResult: Could not determine council for "${address}"\n`);
    }
    
    console.log('='.repeat(50));
  }
}

// Run the tests
runTests().catch(console.error);
