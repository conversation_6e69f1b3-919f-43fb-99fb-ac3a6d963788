// Temporary fix for UserProfile.tsx to solve the Netlify build error
// This file will be used to identify the exact issue causing the build failure

import React, { useState, useEffect } from 'react';
import { useUser, useClerk, useSession } from '@clerk/clerk-react';

export default function UserProfileTemp() {
  const { user } = useUser();
  const { session } = useSession();
  const [message, setMessage] = useState<string>('');
  
  // Test useEffect with proper dependency array
  useEffect(() => {
    if (user) {
      console.log('User loaded');
    }
  }, [user]); // This is a clean dependency array without comments
  
  return (
    <div>
      <h1>User Profile (Temporary Fix)</h1>
      <p>This is a simplified version to fix the Netlify build error.</p>
      <p>User: {user?.firstName} {user?.lastName}</p>
      <button onClick={() => setMessage('Test clicked')}>Test Button</button>
      {message && <p>{message}</p>}
    </div>
  );
}
