<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Define the gradient -->
  <defs>
    <linearGradient id="purplePinkGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7C3AED" /> <!-- purple-600 -->
      <stop offset="100%" style="stop-color:#DB2777" /> <!-- pink-600 -->
    </linearGradient>
  </defs>
  
  <!-- House outline -->
  <path d="M100 20L20 90V180H180V90L100 20Z" stroke="url(#purplePinkGradient)" stroke-width="8" fill="white"/>
  
  <!-- Left eye -->
  <circle cx="70" cy="100" r="10" fill="url(#purplePinkGradient)"/>
  
  <!-- Right eye -->
  <circle cx="130" cy="100" r="10" fill="url(#purplePinkGradient)"/>
  
  <!-- Smile -->
  <path d="M70 130C70 130 85 150 130 130" stroke="url(#purplePinkGradient)" stroke-width="8" stroke-linecap="round"/>
</svg>
