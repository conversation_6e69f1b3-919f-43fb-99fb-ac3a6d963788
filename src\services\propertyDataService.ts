/**
 * Property Data Service
 *
 * Comprehensive service for managing property data flow:
 * Submission → Admin Review → Approval → Live Venue → Owner Management
 */

import { getSupabaseClient } from '../lib/supabase-client';
import { trackEvent, EVENT_TYPES } from './dataTracking';

export interface PropertySubmission {
  id: string;
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  ownerId: string;
  ownerEmail?: string;
  ownerName?: string;
  images?: string[];
  amenities?: string[];
  rejection_reason?: string;
  admin_notes?: string;
  approved_by?: string;
  approved_at?: string;
  venue_id?: string; // Link to created venue
}

export interface LiveVenue {
  id: string;
  title: string;
  description: string;
  location: string;
  address: string;
  host_id: string;
  price_per_hour: number;
  capacity: number;
  images: string[];
  amenities: string[];
  approval_status: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  submission_id?: string; // Link back to original submission
}

/**
 * Get all property submissions for admin review
 */
export async function getAllPropertySubmissions(): Promise<PropertySubmission[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching property submissions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching property submissions:', error);
    return [];
  }
}

/**
 * Get property submissions by owner
 */
export async function getOwnerPropertySubmissions(ownerId: string): Promise<PropertySubmission[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('ownerId', ownerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching owner property submissions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching owner property submissions:', error);
    return [];
  }
}

/**
 * Get all live venues for admin management
 */
export async function getAllLiveVenues(): Promise<LiveVenue[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching live venues:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching live venues:', error);
    return [];
  }
}

/**
 * Get live venues by owner
 */
export async function getOwnerLiveVenues(ownerId: string): Promise<LiveVenue[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .eq('host_id', ownerId)
      .eq('is_published', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching owner live venues:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching owner live venues:', error);
    return [];
  }
}

/**
 * Approve property and create live venue
 */
export async function approvePropertyAndCreateVenue(
  propertyId: string,
  adminEmail: string,
  adminNotes?: string
): Promise<{ success: boolean; venueId?: string; error?: string }> {
  try {
    const supabase = getSupabaseClient();

    // 1. Get the property submission
    const { data: propertyData, error: fetchError } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('id', propertyId)
      .single();

    if (fetchError || !propertyData) {
      return { success: false, error: 'Property submission not found' };
    }

    // 2. Create live venue
    const { data: venueData, error: venueError } = await supabase
      .from('venues')
      .insert({
        title: propertyData.name,
        description: propertyData.description || `A beautiful ${propertyData.type} venue`,
        location: propertyData.address,
        address: propertyData.address,
        host_id: propertyData.ownerId,
        price_per_hour: propertyData.price,
        capacity: propertyData.maxGuests,
        images: propertyData.images || [],
        amenities: propertyData.amenities || [],
        approval_status: 'approved',
        is_published: true,
        created_at: propertyData.created_at,
        updated_at: new Date().toISOString(),
        submission_id: propertyId // Link back to submission
      })
      .select()
      .single();

    if (venueError || !venueData) {
      return { success: false, error: 'Failed to create live venue' };
    }

    // 3. Update property submission status
    const { error: updateError } = await supabase
      .from('property_submissions')
      .update({
        status: 'approved',
        is_published: true,
        admin_notes: adminNotes,
        approved_by: adminEmail,
        approved_at: new Date().toISOString(),
        venue_id: venueData.id, // Link to created venue
        updated_at: new Date().toISOString()
      })
      .eq('id', propertyId);

    if (updateError) {
      // Rollback: delete the created venue
      await supabase.from('venues').delete().eq('id', venueData.id);
      return { success: false, error: 'Failed to update submission status' };
    }

    // 4. Track business event - Property Approval
    try {
      await trackEvent(
        EVENT_TYPES.PROPERTY_SUBMISSION,
        'conversion',
        {
          property_id: propertyId,
          venue_id: venueData.id,
          property_type: propertyData.type,
          price_per_hour: propertyData.price,
          capacity: propertyData.maxGuests,
          location: propertyData.address,
          approved_by: adminEmail,
          admin_notes: adminNotes,
          approval_flow: 'admin_manual_review',
          time_to_approval_hours: calculateApprovalTime(propertyData.created_at)
        },
        propertyData.ownerId,
        propertyData.price * 0.10 // Estimated commission impact
      );

      // Track host performance milestone
      await trackEvent(
        'property_approved',
        'retention',
        {
          host_id: propertyData.ownerId,
          property_count: await getHostPropertyCount(propertyData.ownerId),
          venue_type: propertyData.type,
          listing_quality_score: calculateListingQuality(propertyData)
        },
        propertyData.ownerId
      );

      console.log('📊 Property approval events tracked successfully');
    } catch (trackingError) {
      console.error('⚠️ Error tracking approval events:', trackingError);
      // Don't fail the approval if tracking fails
    }

    return { success: true, venueId: venueData.id };
  } catch (error) {
    console.error('Exception approving property:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Reject property submission
 */
export async function rejectPropertySubmission(
  propertyId: string,
  adminEmail: string,
  rejectionReason: string,
  adminNotes?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = getSupabaseClient();

    // Get property data before rejection for tracking
    const { data: propertyData, error: fetchError } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('id', propertyId)
      .single();

    if (fetchError || !propertyData) {
      return { success: false, error: 'Property submission not found' };
    }

    const { error } = await supabase
      .from('property_submissions')
      .update({
        status: 'rejected',
        is_published: false,
        rejection_reason: rejectionReason,
        admin_notes: adminNotes,
        approved_by: adminEmail,
        approved_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', propertyId);

    if (error) {
      return { success: false, error: 'Failed to reject property submission' };
    }

    // Track business event - Property Rejection
    try {
      await trackEvent(
        'property_rejected',
        'churn',
        {
          property_id: propertyId,
          property_type: propertyData.type,
          price_per_hour: propertyData.price,
          capacity: propertyData.maxGuests,
          location: propertyData.address,
          rejection_reason: rejectionReason,
          rejected_by: adminEmail,
          admin_notes: adminNotes,
          time_to_rejection_hours: calculateApprovalTime(propertyData.created_at),
          rejection_category: categorizeRejectionReason(rejectionReason)
        },
        propertyData.ownerId
      );

      // Track potential host churn risk
      await trackEvent(
        'host_rejection_risk',
        'churn',
        {
          host_id: propertyData.ownerId,
          rejection_count: await getHostRejectionCount(propertyData.ownerId),
          first_submission: await isFirstSubmission(propertyData.ownerId),
          churn_risk_level: await calculateHostChurnRisk(propertyData.ownerId)
        },
        propertyData.ownerId
      );

      console.log('📊 Property rejection events tracked successfully');
    } catch (trackingError) {
      console.error('⚠️ Error tracking rejection events:', trackingError);
      // Don't fail the rejection if tracking fails
    }

    return { success: true };
  } catch (error) {
    console.error('Exception rejecting property:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get property submission statistics
 */
export async function getPropertySubmissionStats() {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select('status');

    if (error) {
      console.error('Error fetching submission stats:', error);
      return { total: 0, pending: 0, approved: 0, rejected: 0 };
    }

    const stats = {
      total: data.length,
      pending: data.filter(item => item.status === 'pending').length,
      approved: data.filter(item => item.status === 'approved').length,
      rejected: data.filter(item => item.status === 'rejected').length
    };

    return stats;
  } catch (error) {
    console.error('Exception fetching submission stats:', error);
    return { total: 0, pending: 0, approved: 0, rejected: 0 };
  }
}

/**
 * Get venue management statistics
 */
export async function getVenueManagementStats() {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venues')
      .select('is_published, approval_status');

    if (error) {
      console.error('Error fetching venue stats:', error);
      return { total: 0, published: 0, unpublished: 0, active: 0 };
    }

    const stats = {
      total: data.length,
      published: data.filter(item => item.is_published === true).length,
      unpublished: data.filter(item => item.is_published === false).length,
      active: data.filter(item => item.approval_status === 'approved').length
    };

    return stats;
  } catch (error) {
    console.error('Exception fetching venue stats:', error);
    return { total: 0, published: 0, unpublished: 0, active: 0 };
  }
}

/**
 * Initialize business events table
 */
export async function initializeBusinessEventsTable(): Promise<void> {
  try {
    const supabase = getSupabaseClient();

    // Create business_events table if it doesn't exist
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS business_events (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          event_type VARCHAR(100) NOT NULL,
          event_category VARCHAR(50) NOT NULL CHECK (event_category IN ('acquisition', 'conversion', 'revenue', 'retention', 'churn')),
          user_id UUID,
          session_id VARCHAR(100),
          properties JSONB DEFAULT '{}',
          revenue_impact DECIMAL(10,2),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes for better query performance
        CREATE INDEX IF NOT EXISTS idx_business_events_type ON business_events(event_type);
        CREATE INDEX IF NOT EXISTS idx_business_events_category ON business_events(event_category);
        CREATE INDEX IF NOT EXISTS idx_business_events_user_id ON business_events(user_id);
        CREATE INDEX IF NOT EXISTS idx_business_events_created_at ON business_events(created_at);
        CREATE INDEX IF NOT EXISTS idx_business_events_revenue ON business_events(revenue_impact) WHERE revenue_impact IS NOT NULL;
      `
    });

    if (error) {
      console.error('Error creating business_events table:', error);
    } else {
      console.log('✅ Business events table initialized successfully');
    }
  } catch (error) {
    console.error('Exception initializing business events table:', error);
  }
}

// Helper functions for business analytics tracking

/**
 * Calculate time from submission to approval/rejection
 */
function calculateApprovalTime(submissionDate: string): number {
  const submitted = new Date(submissionDate);
  const now = new Date();
  const diffMs = now.getTime() - submitted.getTime();
  return Math.round(diffMs / (1000 * 60 * 60)); // Convert to hours
}

/**
 * Get host's total property count
 */
async function getHostPropertyCount(hostId: string): Promise<number> {
  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('property_submissions')
      .select('id')
      .eq('ownerId', hostId);

    return data?.length || 0;
  } catch (error) {
    console.error('Error getting host property count:', error);
    return 0;
  }
}

/**
 * Calculate listing quality score based on property data
 */
function calculateListingQuality(propertyData: any): number {
  let score = 0;

  // Description quality (0-30 points)
  if (propertyData.description) {
    const descLength = propertyData.description.length;
    score += Math.min(30, descLength / 10); // 1 point per 10 characters, max 30
  }

  // Images (0-25 points)
  const imageCount = propertyData.images?.length || 0;
  score += Math.min(25, imageCount * 5); // 5 points per image, max 25

  // Amenities (0-20 points)
  const amenityCount = propertyData.amenities?.length || 0;
  score += Math.min(20, amenityCount * 2); // 2 points per amenity, max 20

  // Price competitiveness (0-15 points)
  const price = propertyData.price || 0;
  if (price > 0 && price <= 200) score += 15; // Reasonable pricing
  else if (price <= 300) score += 10;
  else if (price <= 500) score += 5;

  // Capacity (0-10 points)
  const capacity = propertyData.maxGuests || 0;
  if (capacity >= 10) score += 10;
  else if (capacity >= 5) score += 7;
  else if (capacity >= 2) score += 5;

  return Math.round(score);
}

/**
 * Get host's rejection count
 */
async function getHostRejectionCount(hostId: string): Promise<number> {
  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('property_submissions')
      .select('id')
      .eq('ownerId', hostId)
      .eq('status', 'rejected');

    return data?.length || 0;
  } catch (error) {
    console.error('Error getting host rejection count:', error);
    return 0;
  }
}

/**
 * Check if this is host's first submission
 */
async function isFirstSubmission(hostId: string): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('property_submissions')
      .select('id')
      .eq('ownerId', hostId);

    return (data?.length || 0) <= 1;
  } catch (error) {
    console.error('Error checking first submission:', error);
    return false;
  }
}

/**
 * Calculate host churn risk level
 */
async function calculateHostChurnRisk(hostId: string): Promise<'low' | 'medium' | 'high'> {
  try {
    const rejectionCount = await getHostRejectionCount(hostId);
    const totalCount = await getHostPropertyCount(hostId);
    const isFirst = await isFirstSubmission(hostId);

    if (isFirst && rejectionCount > 0) return 'high';
    if (rejectionCount >= 2) return 'high';
    if (rejectionCount === 1 && totalCount <= 2) return 'medium';
    return 'low';
  } catch (error) {
    console.error('Error calculating churn risk:', error);
    return 'low';
  }
}

/**
 * Categorize rejection reason for analytics
 */
function categorizeRejectionReason(reason: string): string {
  const lowerReason = reason.toLowerCase();

  if (lowerReason.includes('photo') || lowerReason.includes('image')) return 'photos';
  if (lowerReason.includes('description') || lowerReason.includes('detail')) return 'description';
  if (lowerReason.includes('price') || lowerReason.includes('cost')) return 'pricing';
  if (lowerReason.includes('location') || lowerReason.includes('address')) return 'location';
  if (lowerReason.includes('safety') || lowerReason.includes('security')) return 'safety';
  if (lowerReason.includes('quality') || lowerReason.includes('standard')) return 'quality';
  if (lowerReason.includes('compliance') || lowerReason.includes('legal')) return 'compliance';

  return 'other';
}
