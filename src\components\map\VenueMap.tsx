import React from 'react';
import Map, { Marker, Popup } from 'react-map-gl';
import { MapPin } from 'lucide-react';
import { Venue } from '../../types/venue';

interface VenueMapProps {
  venues: Venue[];
  onVenueSelect: (venue: Venue) => void;
  selectedVenue?: Venue;
}

export default function VenueMap({ venues, onVenueSelect, selectedVenue }: VenueMapProps) {
  return (
    <div className="relative h-[calc(100vh-6rem)] rounded-lg overflow-hidden">
      <Map
        initialViewState={{
          longitude: 151.2093,
          latitude: -33.8688,
          zoom: 11
        }}
        style={{ width: '100%', height: '100%' }}
        mapStyle="mapbox://styles/mapbox/streets-v11"
      >
        {venues.map((venue) => (
          <Marker
            key={venue.id}
            longitude={venue.coordinates.longitude}
            latitude={venue.coordinates.latitude}
            onClick={() => onVenueSelect(venue)}
          >
            <MapPin 
              className={`h-6 w-6 cursor-pointer ${
                selectedVenue?.id === venue.id 
                  ? 'text-purple-600' 
                  : 'text-gray-600'
              }`} 
            />
          </Marker>
        ))}

        {selectedVenue && (
          <Popup
            longitude={selectedVenue.coordinates.longitude}
            latitude={selectedVenue.coordinates.latitude}
            anchor="bottom"
            onClose={() => onVenueSelect(selectedVenue)}
          >
            <div className="p-2">
              <h3 className="font-semibold">{selectedVenue.title}</h3>
              <p className="text-sm text-gray-600">${selectedVenue.price}/night</p>
            </div>
          </Popup>
        )}
      </Map>
    </div>
  );
}