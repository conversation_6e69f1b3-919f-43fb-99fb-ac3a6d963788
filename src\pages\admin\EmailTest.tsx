import React, { useState } from 'react';
import { sendTestEmail } from '../../services/emailService.js';

/**
 * Email Test Page
 * This page allows admins to test the email service
 */
export default function EmailTest() {
  const [sending, setSending] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleTestEmail = async () => {
    setSending(true);
    setResult(null);

    try {
      const success = await sendTestEmail();

      if (success) {
        setResult({
          success: true,
          message: 'Test email sent successfully! Check your inbox.'
        });
      } else {
        setResult({
          success: false,
          message: 'Failed to send test email. Check console for details.'
        });
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      setResult({
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Email Service Test</h1>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <p className="mb-4">
          Use this page to test the email notification service. This will send a test email to the admin email address.
        </p>

        <button
          onClick={handleTestEmail}
          disabled={sending}
          className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:bg-purple-300"
        >
          {sending ? 'Sending...' : 'Send Test Email'}
        </button>

        {result && (
          <div className={`mt-4 p-3 rounded ${result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
            {result.message}
          </div>
        )}
      </div>

      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-bold mb-4">Email Service Configuration</h2>

        <div className="space-y-2">
          <p><strong>Service:</strong> Gmail</p>
          <p><strong>From:</strong> <EMAIL></p>
          <p><strong>To:</strong> <EMAIL></p>
          <p><strong>Authentication:</strong> Using App Password (16 characters)</p>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700">
          <h3 className="font-bold">Security Note</h3>
          <p>
            The Gmail App Password is stored in the code for demonstration purposes.
            In a production environment, this should be stored in environment variables.
          </p>
        </div>
      </div>
    </div>
  );
}
