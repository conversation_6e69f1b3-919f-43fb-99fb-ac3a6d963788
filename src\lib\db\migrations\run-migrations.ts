import { applyMigrations } from './index';
import initialSchema from './001_initial_schema';
import addAiAnalytics from './002_add_ai_analytics';

// Collect all migrations
const migrations = [
  initialSchema,
  addAiAnalytics
];

/**
 * Run all pending migrations
 */
export async function runMigrations() {
  console.log('Running database migrations...');
  
  try {
    await applyMigrations(migrations);
    console.log('All migrations applied successfully');
  } catch (error) {
    console.error('Error applying migrations:', error);
    process.exit(1);
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations();
}
