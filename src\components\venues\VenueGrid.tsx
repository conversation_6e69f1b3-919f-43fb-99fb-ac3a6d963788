import React, { useState, useMemo } from 'react';
import VenueCard from './VenueCard';
import { Venue } from '../../types/venue';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface VenueGridProps {
  venues: Venue[];
  venuesPerPage?: number;
}

// Simple Pagination Component
interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

function SimplePagination({ currentPage, totalPages, onPageChange }: SimplePaginationProps) {
  if (totalPages <= 1) return null;

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex items-center justify-center space-x-2">
      {/* Previous Button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500"
      >
        <ChevronLeft className="w-4 h-4 mr-1" />
        Previous
      </button>

      {/* Page Numbers */}
      <div className="flex items-center space-x-1">
        {visiblePages.map((page, index) => (
          <React.Fragment key={index}>
            {page === '...' ? (
              <span className="px-3 py-2 text-sm font-medium text-gray-500">...</span>
            ) : (
              <button
                onClick={() => onPageChange(page as number)}
                className={`px-3 py-2 text-sm font-medium rounded-lg ${
                  currentPage === page
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700'
                }`}
              >
                {page}
              </button>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Next Button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-500"
      >
        Next
        <ChevronRight className="w-4 h-4 ml-1" />
      </button>
    </div>
  );
}

export default function VenueGrid({
  venues,
  venuesPerPage = 12
}: VenueGridProps) {
  const [exactMatchesPage, setExactMatchesPage] = useState(1);
  const [suggestionsPage, setSuggestionsPage] = useState(1);

  if (venues.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🏠</div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">No venues found</h3>
        <p className="text-gray-600">
          Try adjusting your search criteria or budget to see more results.
        </p>
      </div>
    );
  }

  // Separate exact matches from suggestions
  const exactMatches = venues.filter(venue => venue.isExactMatch);
  const suggestions = venues.filter(venue => venue.isSuggestion);

  // Calculate pagination for exact matches
  const exactMatchesTotalPages = Math.ceil(exactMatches.length / venuesPerPage);
  const exactMatchesStartIndex = (exactMatchesPage - 1) * venuesPerPage;
  const exactMatchesEndIndex = exactMatchesStartIndex + venuesPerPage;
  const displayedExactMatches = useMemo(() =>
    exactMatches.slice(exactMatchesStartIndex, exactMatchesEndIndex),
    [exactMatches, exactMatchesStartIndex, exactMatchesEndIndex]
  );

  // Calculate pagination for suggestions
  const suggestionsTotalPages = Math.ceil(suggestions.length / venuesPerPage);
  const suggestionsStartIndex = (suggestionsPage - 1) * venuesPerPage;
  const suggestionsEndIndex = suggestionsStartIndex + venuesPerPage;
  const displayedSuggestions = useMemo(() =>
    suggestions.slice(suggestionsStartIndex, suggestionsEndIndex),
    [suggestions, suggestionsStartIndex, suggestionsEndIndex]
  );

  // Pagination handlers
  const handleExactMatchesPageChange = (newPage: number) => {
    setExactMatchesPage(newPage);
    // Scroll to top of page when pagination changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSuggestionsPageChange = (newPage: number) => {
    setSuggestionsPage(newPage);
    // Scroll to top of page when pagination changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div>
      {/* Exact Matches Section */}
      {exactMatches.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-gray-900">
                {exactMatches.length} {exactMatches.length === 1 ? 'Venue' : 'Venues'} Found
              </h2>
              <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                Within 10km radius ✨
              </div>
            </div>
            {exactMatchesTotalPages > 1 && (
              <div className="text-sm text-gray-600">
                Showing {exactMatchesStartIndex + 1}-{Math.min(exactMatchesEndIndex, exactMatches.length)} of {exactMatches.length}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {displayedExactMatches.map((venue) => (
              <VenueCard key={venue.id} venue={venue} />
            ))}
          </div>

          {/* Pagination for Exact Matches */}
          {exactMatchesTotalPages > 1 && (
            <div className="mt-8">
              <SimplePagination
                currentPage={exactMatchesPage}
                totalPages={exactMatchesTotalPages}
                onPageChange={handleExactMatchesPageChange}
              />
            </div>
          )}
        </div>
      )}

      {/* Suggestions Section */}
      {suggestions.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <h2 className="text-xl font-semibold text-gray-700">
                Areas outside your search that you might like
              </h2>
              <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                Other locations 📍
              </div>
            </div>
            {suggestionsTotalPages > 1 && (
              <div className="text-sm text-gray-600">
                Showing {suggestionsStartIndex + 1}-{Math.min(suggestionsEndIndex, suggestions.length)} of {suggestions.length}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {displayedSuggestions.map((venue) => (
              <VenueCard key={venue.id} venue={venue} />
            ))}
          </div>

          {/* Pagination for Suggestions */}
          {suggestionsTotalPages > 1 && (
            <div className="mt-8">
              <SimplePagination
                currentPage={suggestionsPage}
                totalPages={suggestionsTotalPages}
                onPageChange={handleSuggestionsPageChange}
              />
            </div>
          )}
        </div>
      )}

      {/* Fallback for when no metadata is available */}
      {exactMatches.length === 0 && suggestions.length === 0 && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-gray-900">
                {venues.length} Amazing Venues
              </h2>
              <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                Ready to party! 🎉
              </div>
            </div>
            {Math.ceil(venues.length / venuesPerPage) > 1 && (
              <div className="text-sm text-gray-600">
                Showing {(exactMatchesPage - 1) * venuesPerPage + 1}-{Math.min(exactMatchesPage * venuesPerPage, venues.length)} of {venues.length}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {venues.slice((exactMatchesPage - 1) * venuesPerPage, exactMatchesPage * venuesPerPage).map((venue) => (
              <VenueCard key={venue.id} venue={venue} />
            ))}
          </div>

          {/* Pagination for All Venues */}
          {Math.ceil(venues.length / venuesPerPage) > 1 && (
            <div className="mt-8">
              <SimplePagination
                currentPage={exactMatchesPage}
                totalPages={Math.ceil(venues.length / venuesPerPage)}
                onPageChange={handleExactMatchesPageChange}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}