/**
 * Generate Redirects Script
 *
 * This script generates a _redirects file for Netlify deployment.
 * It ensures that all routes are properly handled by the SPA.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Define the routes that need specific redirects
const routes = [
  '/nsw-curfew-zoning',
  '/nsw-party-planning',
  '/nsw-party-planning-updated',
  '/nsw-address-v2',
  '/precise-party-planning',
  '/precise-address',
];

// Create the redirects content
let redirectsContent = '';

// Add specific route redirects
routes.forEach(route => {
  redirectsContent += `${route} /index.html 200\n`;
});

// Add the catch-all redirect
redirectsContent += '/* /index.html 200\n';

// Get the directory name using ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Write to the _redirects file in the dist directory
const distDir = path.join(__dirname, '..', 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

fs.writeFileSync(path.join(distDir, '_redirects'), redirectsContent);

console.log('Generated _redirects file for Netlify deployment');
