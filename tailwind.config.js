/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter var', 'system-ui', 'sans-serif'],
        display: ['Clash Display', 'system-ui', 'sans-serif'],
      },
      spacing: {
        'golden-1': 'calc(1rem * 1.618)',
        'golden-2': 'calc(2rem * 1.618)',
        'golden-3': 'calc(3rem * 1.618)',
        'golden-4': 'calc(4rem * 1.618)',
      },
      maxWidth: {
        'golden': 'calc(1rem * 1.618 * 38)', // ~987px
      },
      fontSize: {
        'golden-h1': ['4.25rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        'golden-h2': ['2.625rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
        'golden-h3': ['1.625rem', { lineHeight: '1.3' }],
        'body-lg': ['1.125rem', { lineHeight: '1.618' }],
        'body': ['1rem', { lineHeight: '1.618' }],
        'body-sm': ['0.875rem', { lineHeight: '1.618' }],
      },
      lineHeight: {
        'golden': '1.618',
      },
      gridTemplateColumns: {
        'golden': '1fr 1.618fr',
      },
      aspectRatio: {
        'golden': '1.618',
      },
      colors: {
        brand: {
          50: '#F5F3FF',
          100: '#EDE9FE',
          200: '#DDD6FE',
          300: '#C4B5FD',
          400: '#A78BFA',
          500: '#8B5CF6',
          600: '#7C3AED',
          700: '#6D28D9',
          800: '#5B21B6',
          900: '#4C1D95',
        },
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'glow': '0 0 15px rgba(124, 58, 237, 0.1)',
      },
      animation: {
        'fadeIn': 'fadeIn 0.2s ease-in-out',
        'slideDown': 'slideDown 0.3s ease-in-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};