/**
 * Hook for creating venues with automatic coordinate assignment
 * Ensures all new venues have proper coordinates for location-based search
 */

import { useState } from 'react';
import { assignVenueCoordinates, VenueLocation, VenueCoordinates, extractSuburbFromAddress } from '../services/venue-coordinates';
import { supabase } from '../lib/supabase-client';
import {
  updateVenueAvailabilitySettings,
  updateVenueOperatingHours,
  VenueAvailabilitySettings,
  VenueOperatingHours
} from '../api/availability';

export interface VenueCreationData {
  // Basic Info
  title: string;
  description: string;
  address: string;
  
  // Location (will be auto-parsed from address if not provided)
  suburb?: string;
  postcode?: string;
  state?: string;
  
  // Venue Details
  price: number;
  capacity: number;
  amenities?: string[];
  images?: string[];
  
  // Host Info
  host_id: string;
  
  // Optional metadata
  venue_type?: string;
  house_rules?: string;
}

export interface VenueCreationResult {
  success: boolean;
  venue_id?: string;
  coordinates?: VenueCoordinates;
  error?: string;
}

export function useVenueCreation() {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Create a new venue with automatic coordinate assignment
   */
  const createVenue = async (venueData: VenueCreationData): Promise<VenueCreationResult> => {
    setIsCreating(true);
    setError(null);

    try {
      console.log('🏠 Creating new venue:', venueData.title);

      // Step 1: Parse location from address if not provided
      const location = await parseVenueLocation(venueData);
      console.log('📍 Parsed location:', location);

      // Step 2: Assign coordinates automatically
      console.log('🎯 Assigning coordinates...');
      const coordinates = await assignVenueCoordinates(location);
      console.log('✅ Coordinates assigned:', coordinates);

      // Step 3: Create venue in database
      const venueRecord = {
        title: venueData.title,
        description: venueData.description,
        location: `${location.suburb}, ${location.state}`,
        address: location.address,
        city: location.suburb,
        state: location.state,
        zip_code: location.postcode,
        coordinates: {
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          source: coordinates.source,
          accuracy: coordinates.accuracy
        },
        price: venueData.price,
        capacity: venueData.capacity,
        amenities: venueData.amenities || [],
        house_rules: venueData.house_rules || '',
        images: venueData.images || [],
        owner_id: venueData.host_id,
        is_active: true
      };

      console.log('💾 Saving venue to database...');
      const { data, error: dbError } = await supabase
        .from('venues')
        .insert(venueRecord)
        .select()
        .single();

      if (dbError) {
        throw new Error(`Database error: ${dbError.message}`);
      }

      console.log('✅ Venue created successfully:', data.id);

      // Step 4: Set up default availability settings for the new venue
      try {
        console.log('🕒 Setting up default availability for venue:', data.id);
        await setupDefaultAvailability(data.id);
        console.log('✅ Default availability settings created');
      } catch (availabilityError) {
        console.warn('⚠️ Failed to set up default availability (venue still created):', availabilityError);
        // Don't fail the entire venue creation if availability setup fails
      }

      return {
        success: true,
        venue_id: data.id,
        coordinates
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Venue creation failed:', errorMessage);
      setError(errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Update existing venue coordinates
   */
  const updateVenueCoordinates = async (venueId: string, address: string): Promise<VenueCreationResult> => {
    setIsCreating(true);
    setError(null);

    try {
      console.log('🔄 Updating coordinates for venue:', venueId);

      // Parse location from address
      const location = await parseLocationFromAddress(address);
      
      // Assign new coordinates
      const coordinates = await assignVenueCoordinates(location);

      // Update venue in database
      const { error: dbError } = await supabase
        .from('venues')
        .update({
          coordinates: {
            latitude: coordinates.latitude,
            longitude: coordinates.longitude,
            source: coordinates.source,
            accuracy: coordinates.accuracy
          },
          city: location.suburb,
          state: location.state,
          zip_code: location.postcode
        })
        .eq('id', venueId);

      if (dbError) {
        throw new Error(`Database error: ${dbError.message}`);
      }

      console.log('✅ Venue coordinates updated successfully');

      return {
        success: true,
        venue_id: venueId,
        coordinates
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Coordinate update failed:', errorMessage);
      setError(errorMessage);

      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Set up default availability settings for a new venue
   */
  const setupDefaultAvailability = async (venueId: string): Promise<void> => {
    try {
      // Default availability settings
      const defaultSettings: VenueAvailabilitySettings = {
        venue_id: venueId,
        available_days: [1, 2, 3, 4, 5, 6, 0], // All days available
        default_start_time: '09:00:00',
        default_end_time: '23:00:00',
        min_booking_hours: 4,
        max_booking_hours: 12,
        lead_time_hours: 24,
        max_advance_days: 365,
        instant_booking_enabled: false
      };

      // Default operating hours (same for all days initially)
      const defaultOperatingHours: VenueOperatingHours[] = [
        { venue_id: venueId, day_of_week: 0, start_time: '10:00:00', end_time: '22:00:00', is_available: true }, // Sunday
        { venue_id: venueId, day_of_week: 1, start_time: '09:00:00', end_time: '23:00:00', is_available: true }, // Monday
        { venue_id: venueId, day_of_week: 2, start_time: '09:00:00', end_time: '23:00:00', is_available: true }, // Tuesday
        { venue_id: venueId, day_of_week: 3, start_time: '09:00:00', end_time: '23:00:00', is_available: true }, // Wednesday
        { venue_id: venueId, day_of_week: 4, start_time: '09:00:00', end_time: '23:00:00', is_available: true }, // Thursday
        { venue_id: venueId, day_of_week: 5, start_time: '18:00:00', end_time: '02:00:00', is_available: true }, // Friday
        { venue_id: venueId, day_of_week: 6, start_time: '18:00:00', end_time: '02:00:00', is_available: true }, // Saturday
      ];

      // Create availability settings
      await updateVenueAvailabilitySettings(defaultSettings);

      // Create operating hours for each day
      for (const hours of defaultOperatingHours) {
        await updateVenueOperatingHours(hours);
      }

      console.log('✅ Default availability settings created for venue:', venueId);
    } catch (error) {
      console.error('❌ Error setting up default availability:', error);
      throw error;
    }
  };

  return {
    createVenue,
    updateVenueCoordinates,
    isCreating,
    error
  };
}

/**
 * Parse venue location from creation data
 */
async function parseVenueLocation(venueData: VenueCreationData): Promise<VenueLocation> {
  // If location details are provided, use them
  if (venueData.suburb && venueData.postcode && venueData.state) {
    return {
      address: venueData.address,
      suburb: venueData.suburb,
      postcode: venueData.postcode,
      state: venueData.state
    };
  }

  // Otherwise, parse from address
  return parseLocationFromAddress(venueData.address);
}

/**
 * Parse location details from address string
 */
async function parseLocationFromAddress(address: string): Promise<VenueLocation> {
  // Extract suburb from address
  const suburb = extractSuburbFromAddress(address);
  
  if (!suburb) {
    throw new Error('Could not extract suburb from address. Please provide suburb manually.');
  }

  // Extract postcode
  const postcodeMatch = address.match(/\b(\d{4})\b/);
  const postcode = postcodeMatch ? postcodeMatch[1] : '2000'; // Default to Sydney

  // Extract state (assume NSW for now)
  const state = address.toLowerCase().includes('nsw') ? 'NSW' : 'NSW';

  return {
    address,
    suburb,
    postcode,
    state
  };
}

/**
 * Validate venue creation data
 */
export function validateVenueData(data: VenueCreationData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.title || data.title.length < 5) {
    errors.push('Title must be at least 5 characters long');
  }

  if (!data.description || data.description.length < 20) {
    errors.push('Description must be at least 20 characters long');
  }

  if (!data.address || data.address.length < 10) {
    errors.push('Address must be at least 10 characters long');
  }

  if (!data.price || data.price <= 0) {
    errors.push('Price must be greater than 0');
  }

  if (!data.capacity || data.capacity <= 0) {
    errors.push('Capacity must be greater than 0');
  }

  if (!data.host_id) {
    errors.push('Host ID is required');
  }

  // Validate address format for NSW
  if (!data.address.toLowerCase().includes('nsw') && !data.state) {
    errors.push('Address must include NSW or state must be specified');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Test function for venue creation
 */
export async function testVenueCreation() {
  console.log('🧪 Testing venue creation system...');
  
  const testVenue: VenueCreationData = {
    title: 'Test Venue in Parramatta',
    description: 'A beautiful test venue for parties and events in Parramatta',
    address: '123 Test Street, Parramatta NSW 2150',
    price: 150,
    capacity: 50,
    host_id: 'test-host-123',
    amenities: ['Sound System', 'Parking'],
    venue_type: 'Function Centre'
  };

  // Validate data
  const validation = validateVenueData(testVenue);
  if (!validation.isValid) {
    console.error('❌ Validation failed:', validation.errors);
    return;
  }

  console.log('✅ Validation passed');
  console.log('📍 This would create a venue with automatic coordinate assignment');
  console.log('🎯 Coordinates would be assigned using the venue-coordinates service');
}

// Make test function available globally
if (typeof window !== 'undefined') {
  (window as any).testVenueCreation = testVenueCreation;
}
