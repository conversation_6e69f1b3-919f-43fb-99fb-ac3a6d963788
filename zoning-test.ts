import { getCurfewInfo } from './src/lib/nsw-party-planning/curfew-api';
import { parseNSWAddress } from './src/utils/addressUtils';
import { loadGeoJSON, findZoneForPoint, findLGAForPoint } from './src/utils/spatialUtils';

async function runTest() {
  try {
    const address = '10 Darvall Road, Eastwood, NSW 2122';
    const lat = -33.791;
    const lng = 151.080;

    const [zoningData, lgaData] = await Promise.all([
      loadGeoJSON('./src/data/nswZoningData.json'),
      loadGeoJSON('./src/data/lga.geojson')
    ]);

    const point = { type: 'Point' as const, coordinates: [lng, lat] };
    const zoneCode = findZoneForPoint(point, zoningData) || 'R2';
    const lgaName = findLGAForPoint(point, lgaData) || 'Unknown';

    const curfewInfo = await getCurfewInfo({
      address: parseNSWAddress(address),
      propertyType: address.includes('/') ? 'Apartment/Unit' : null,
      zoneCode,
      lgaName
    });

    console.log('Zoning Analysis Results:');
    console.log('Address:', address);
    console.log('Zoning:', zoneCode);
    console.log('LGA:', lgaName);
    console.log('Curfew:', `${curfewInfo.curfew_start} to ${curfewInfo.curfew_end}`);
    console.log('Additional Rules:', curfewInfo.additional_rules || 'None');
  } catch (error) {
    console.error('Analysis failed:', error);
  }
}

runTest();
