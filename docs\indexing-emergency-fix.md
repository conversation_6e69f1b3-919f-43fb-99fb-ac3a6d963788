# HouseGoing Indexing Emergency Fix

**Current Status (June 21, 2025):**
- The site is not appearing in Google Search results for either "housegoing" or "housegoing au"
- Google is crawling the site and detecting valid structured data
- But the site itself is not being properly indexed

## Root Causes Identified

After thorough investigation, we identified several critical issues preventing proper indexing:

1. **Netlify Redirect Configuration Issue**: 
   - The `/about-housegoing` route was being explicitly redirected to `/index.html`
   - This prevents Google from seeing it as a unique, indexable page

2. **Missing Brand Pages in Sitemap Generator**:
   - The sitemap generator script wasn't including brand-specific pages
   - Google prioritizes indexing pages that appear in sitemaps

3. **Inconsistent URL Handling**:
   - Some pages were using `.html` extensions while others weren't
   - This creates confusion for search engines about canonical URLs

4. **No Direct Indexing Verification**:
   - No simple HTML page specifically designed for search verification

## Immediate Fixes Applied

1. **Fixed Netlify Redirects**:
   - Removed the explicit redirect for `/about-housegoing`
   - Let the SPA router handle this path naturally

2. **Updated Sitemap Generator**:
   - Added brand pages to the static routes list
   - Ensured all brand pages appear in sitemaps

3. **Created Verification Page**:
   - Added `indexing-verification.html` page
   - Includes clear statements about the site and its purpose

4. **Streamlined Build Process**:
   - Created a special rebuild script to ensure all fixes are applied
   - Added verification steps to confirm brand pages are in the build

## Deployment Plan

1. Run the rebuild script:
```powershell
.\rebuild-with-indexing-fixes.ps1
```

2. Deploy to Netlify:
```
# Use your normal deployment process
```

3. Submit URLs to Google:
   - Use Google Search Console's URL Inspection tool
   - Request indexing for the main domain and all brand pages
   - Also submit the verification page

4. Verify Indexing:
   - Search Google for `site:housegoing.com.au`
   - This should show all indexed pages from your domain

## Additional Recommended Actions

### 1. Create a Google Business Profile

Setting up a Google Business Profile significantly improves brand search visibility:

- Go to [Google Business](https://business.google.com/)
- Create a profile for HouseGoing
- Verify your business (usually via postcard or phone)
- Add comprehensive business information and photos

### 2. Technical SEO Check

Run a complete technical SEO audit:

```powershell
# Install lighthouse if not already installed
npm install -g lighthouse

# Run lighthouse audit
lighthouse https://housegoing.com.au --output html --output-path ./lighthouse-report.html --view
```

### 3. Implement Google Site Verification

If not already done:

1. Go to [Google Search Console](https://search.google.com/search-console)
2. Add your property if not already added
3. Get the HTML verification tag
4. Add it to your site's `<head>` section

### 4. Monitor Indexing Progress

Create a simple indexing monitoring script:

```powershell
# install-module -Name PSGSuite  # Uncomment if you need to install the module
# Or monitor manually by searching regularly
```

## Expected Timeline

- Site verification: 1-3 days
- Initial indexing: 3-7 days
- Brand search appearance: 2-4 weeks

## Emergency Contacts

If these fixes don't resolve the issue within one week:

1. Contact Google Search Console support
2. Consider using the [Request Indexing tool](https://developers.google.com/search/docs/monitor-debug/tools/request-indexing) (Beta)
3. Post on Google Search Central Community for help
