import React from 'react';
import { AIAssistantButton } from '../host/AIAssistantButton';
import { useLocation } from 'react-router-dom';

/**
 * Layout component for host portal pages
 * Includes the AI Assistant button on all pages
 */
export default function HostLayout({ children }) {
  const location = useLocation();
  
  // Determine the context based on the current path
  const getContext = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard')) return 'dashboard';
    if (path.includes('/properties')) return 'properties';
    if (path.includes('/bookings')) return 'bookings';
    if (path.includes('/earnings')) return 'earnings';
    if (path.includes('/messages')) return 'messages';
    if (path.includes('/settings')) return 'settings';
    
    return 'general';
  };
  
  return (
    <div className="relative">
      {children}
      
      {/* AI Assistant Button - available on all host portal pages */}
      <AIAssistantButton context={getContext()} />
    </div>
  );
}
