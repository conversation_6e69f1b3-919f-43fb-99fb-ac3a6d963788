// Test script for Hugging Face API integration
import fetch from 'node-fetch';

// API token
const API_TOKEN = '*************************************';

// Models to test
const MODELS = [
  "deepseek-ai/DeepSeek-V3-0324",
  "Qwen/Qwen2.5-Omni-7B",
  "mistralai/Mistral-7B-Instruct-v0.3"
];

// Test prompt
const TEST_PROMPT = `
You are <PERSON>, the AI host assistant for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.
You use Australian English spelling (e.g., 'organise' not 'organize').

Current context: general

Your goal is to help hosts optimize their venue listings, understand Party Scores, set pricing strategies, and maximize bookings.
Provide specific, actionable advice that hosts can implement right away.

User message: How can I improve my venue's Party Score?

Your response:`;

// Function to test a model
async function testModel(model) {
  console.log(`Testing model: ${model}`);

  try {
    const response = await fetch(`https://api-inference.huggingface.co/models/${model}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: JSON.stringify({
        inputs: TEST_PROMPT,
        parameters: {
          temperature: 0.7,
          max_new_tokens: 1024,
          return_full_text: false,
          ...(model.includes('deepseek') ? { trust_remote_code: true } : {})
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API error: ${error.error || 'Unknown error'}`);
    }

    const data = await response.json();
    console.log(`✅ Success for ${model}`);
    console.log('Response preview:', data[0]?.generated_text.substring(0, 150) + '...');
    console.log('-----------------------------------');
    return true;
  } catch (error) {
    console.error(`❌ Error with ${model}:`, error.message);
    console.log('-----------------------------------');
    return false;
  }
}

// Main function to test all models
async function testAllModels() {
  console.log('Starting Hugging Face API tests...');
  console.log('===================================');

  let successCount = 0;

  for (const model of MODELS) {
    const success = await testModel(model);
    if (success) successCount++;
  }

  console.log(`Test complete: ${successCount}/${MODELS.length} models working`);
}

// Run the tests
testAllModels();
