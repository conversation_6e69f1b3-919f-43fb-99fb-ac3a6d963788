/**
 * Create Test Property Submissions
 *
 * Utility to create test property submissions for testing the admin approval workflow
 */

import { getSupabaseClient } from '../services/api';

export interface TestPropertySubmission {
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  ownerId: string;
  ownerEmail: string;
  ownerName: string;
  images?: string[];
  amenities?: string[];
  status: 'pending' | 'approved' | 'rejected';
}

const TEST_SUBMISSIONS: TestPropertySubmission[] = [
  {
    name: 'Harbour View Rooftop',
    address: '123 Circular Quay, Sydney NSW 2000',
    type: 'Rooftop',
    description: 'Beautiful rooftop venue with stunning harbour views, perfect for parties and events. Features modern amenities and professional lighting.',
    maxGuests: 50,
    price: 150,
    ownerId: 'dev_host_001',
    ownerEmail: '<EMAIL>',
    ownerName: 'Tom Chen',
    images: [
      'https://images.unsplash.com/photo-1566737236500-c8ac43014a67',
      'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8'
    ],
    amenities: ['Sound System', 'Lighting', 'Bar Area', 'Harbour Views', 'Parking'],
    status: 'pending'
  },
  {
    name: 'Industrial Warehouse Space',
    address: '456 Industrial Lane, Marrickville NSW 2204',
    type: 'Warehouse',
    description: 'Spacious industrial warehouse with exposed brick walls and high ceilings. Perfect for large events and creative parties.',
    maxGuests: 200,
    price: 200,
    ownerId: 'dev_host_001',
    ownerEmail: '<EMAIL>',
    ownerName: 'Tom Chen',
    images: [
      'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
      'https://images.unsplash.com/photo-1571902943202-507ec2618e8f'
    ],
    amenities: ['Sound System', 'High Ceilings', 'Loading Dock', 'Parking', 'Kitchen'],
    status: 'pending'
  },
  {
    name: 'Beachside Garden Villa',
    address: '789 Beach Road, Bondi NSW 2026',
    type: 'Villa',
    description: 'Elegant beachside villa with beautiful gardens and ocean views. Ideal for intimate gatherings and celebrations.',
    maxGuests: 30,
    price: 120,
    ownerId: 'dev_customer_001',
    ownerEmail: '<EMAIL>',
    ownerName: 'Sarah Johnson',
    images: [
      'https://images.unsplash.com/photo-1564013799919-ab600027ffc6',
      'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9'
    ],
    amenities: ['Garden', 'Ocean Views', 'Pool', 'BBQ Area', 'Parking'],
    status: 'pending'
  },
  {
    name: 'Urban Loft Space',
    address: '321 King Street, Newtown NSW 2042',
    type: 'Loft',
    description: 'Modern urban loft with contemporary design and city views. Perfect for stylish parties and corporate events.',
    maxGuests: 80,
    price: 180,
    ownerId: 'dev_host_001',
    ownerEmail: '<EMAIL>',
    ownerName: 'Tom Chen',
    images: [
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7',
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2'
    ],
    amenities: ['City Views', 'Modern Kitchen', 'Sound System', 'Elevator', 'Parking'],
    status: 'pending'
  },
  {
    name: 'Backyard Pool Paradise',
    address: '654 Suburban Street, Manly NSW 2095',
    type: 'Backyard',
    description: 'Beautiful backyard with swimming pool and entertainment area. Great for pool parties and summer celebrations.',
    maxGuests: 40,
    price: 100,
    ownerId: 'dev_customer_001',
    ownerEmail: '<EMAIL>',
    ownerName: 'Sarah Johnson',
    images: [
      'https://images.unsplash.com/photo-1544551763-46a013bb70d5',
      'https://images.unsplash.com/photo-1571896349842-33c89424de2d'
    ],
    amenities: ['Swimming Pool', 'BBQ Area', 'Outdoor Seating', 'Garden', 'Parking'],
    status: 'pending'
  }
];

/**
 * Create test property submissions in the database
 */
export async function createTestSubmissions(): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    console.log('🔧 Creating test property submissions...');

    // First, ensure the table exists with the correct schema
    await ensurePropertySubmissionsTable(supabase);

    // Insert test submissions
    const { data, error } = await supabase
      .from('property_submissions')
      .insert(
        TEST_SUBMISSIONS.map(submission => ({
          ...submission,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }))
      )
      .select();

    if (error) {
      console.error('Error creating test submissions:', error);
      return false;
    }

    console.log(`✅ Successfully created ${data?.length || 0} test property submissions`);
    console.log('Test submissions:', data?.map(s => s.name));

    return true;
  } catch (error) {
    console.error('Exception creating test submissions:', error);
    return false;
  }
}

/**
 * Ensure the property_submissions table exists with correct schema
 */
async function ensurePropertySubmissionsTable(supabase: any): Promise<void> {
  try {
    console.log('🔧 Ensuring property_submissions table exists...');

    // First try to query the table to see if it exists
    const { error: queryError } = await supabase
      .from('property_submissions')
      .select('id')
      .limit(1);

    if (queryError && queryError.code === 'PGRST116') {
      // Table doesn't exist, try to create it
      console.log('Table does not exist, attempting to create...');

      try {
        // Try using exec_sql function
        const { error } = await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS property_submissions (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              name TEXT NOT NULL,
              address TEXT NOT NULL,
              type TEXT NOT NULL,
              description TEXT,
              maxGuests INTEGER,
              price DECIMAL,
              ownerId TEXT,
              ownerEmail TEXT,
              ownerName TEXT,
              images TEXT[],
              amenities TEXT[],
              status TEXT DEFAULT 'pending',
              rejection_reason TEXT,
              admin_notes TEXT,
              approved_by TEXT,
              approved_at TIMESTAMP WITH TIME ZONE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            CREATE INDEX IF NOT EXISTS property_submissions_status_idx ON property_submissions(status);
            CREATE INDEX IF NOT EXISTS property_submissions_owner_idx ON property_submissions(ownerId);
          `
        });

        if (error) {
          console.error('Error creating property_submissions table with exec_sql:', error);
          console.log('⚠️ Please create the property_submissions table manually in Supabase SQL editor');
        } else {
          console.log('✅ Property submissions table created successfully');
        }
      } catch (execError) {
        console.error('exec_sql function not available:', execError);
        console.log('⚠️ Please create the property_submissions table manually in Supabase SQL editor');
      }
    } else if (queryError) {
      console.error('Error checking table existence:', queryError);
    } else {
      console.log('✅ Property submissions table already exists');
    }
  } catch (error) {
    console.error('Exception ensuring table exists:', error);
  }
}

/**
 * Clear all test property submissions
 */
export async function clearTestSubmissions(): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    console.log('🧹 Clearing test property submissions...');

    // Delete test submissions (those with dev user IDs)
    const { error } = await supabase
      .from('property_submissions')
      .delete()
      .in('ownerId', ['dev_host_001', 'dev_customer_001']);

    if (error) {
      console.error('Error clearing test submissions:', error);
      return false;
    }

    console.log('✅ Successfully cleared test property submissions');
    return true;
  } catch (error) {
    console.error('Exception clearing test submissions:', error);
    return false;
  }
}

/**
 * Get count of existing submissions
 */
export async function getSubmissionCount(): Promise<number> {
  try {
    const supabase = getSupabaseClient();

    const { count, error } = await supabase
      .from('property_submissions')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error getting submission count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Exception getting submission count:', error);
    return 0;
  }
}
