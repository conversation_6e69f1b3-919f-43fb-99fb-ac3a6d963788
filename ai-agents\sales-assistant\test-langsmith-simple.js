import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const apiKey = process.env.LANGSMITH_API_KEY;
const projectName = process.env.LANGSMITH_PROJECT;

console.log('Using API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');
console.log('Project Name:', projectName);

async function testLangSmithCreate() {
  try {
    const runId = Math.random().toString(36).substring(2, 15);
    const startTime = new Date().toISOString();
    
    // Create a test run
    const runData = {
      id: runId,
      name: 'Test Run',
      run_type: 'chain',
      inputs: { question: 'What is <PERSON><PERSON>mith?' },
      start_time: startTime,
      project_name: projectName
    };
    
    console.log('Creating run with data:', runData);
    
    const response = await fetch('https://api.smith.langchain.com/runs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey
      },
      body: JSON.stringify(runData)
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('\nRun created:', data);
    
  } catch (error) {
    console.error('Error testing LangSmith API:', error);
  }
}

testLangSmithCreate();
