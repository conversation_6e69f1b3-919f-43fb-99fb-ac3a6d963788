#!/bin/bash

# HouseGoing SEO Deployment Script
# This script should be run after each deployment to ensure optimal SEO

set -e

echo "🚀 HouseGoing SEO Deployment Script"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Generate fresh sitemap
print_status "Generating fresh sitemap..."
if node scripts/generate-sitemap.js; then
    print_success "Sitemap generated successfully"
else
    print_error "Failed to generate sitemap"
    exit 1
fi

# Step 2: Validate sitemap files
print_status "Validating sitemap files..."
required_files=("sitemap.xml" "sitemap_index.xml" "sitemap_main.xml" "sitemap_blog.xml" "sitemap_venues.xml")

for file in "${required_files[@]}"; do
    if [ -f "public/$file" ]; then
        print_success "✓ $file exists"
    else
        print_error "✗ $file missing"
        exit 1
    fi
done

# Step 3: Check robots.txt
print_status "Checking robots.txt..."
if [ -f "public/robots.txt" ]; then
    print_success "✓ robots.txt exists"
    
    # Check if it contains sitemap references
    sitemap_count=$(grep -c "Sitemap:" public/robots.txt || true)
    if [ "$sitemap_count" -gt 0 ]; then
        print_success "✓ robots.txt contains $sitemap_count sitemap references"
    else
        print_warning "⚠ robots.txt doesn't contain sitemap references"
    fi
else
    print_error "✗ robots.txt missing"
    exit 1
fi

# Step 4: Count total URLs
print_status "Counting sitemap URLs..."
total_urls=0

for file in "sitemap_main.xml" "sitemap_blog.xml" "sitemap_venues.xml"; do
    if [ -f "public/$file" ]; then
        count=$(grep -c "<loc>" "public/$file" || true)
        total_urls=$((total_urls + count))
        print_status "  $file: $count URLs"
    fi
done

print_success "Total URLs in sitemap: $total_urls"

# Step 5: SEO recommendations
print_status "SEO Deployment Checklist:"
echo "  □ Submit updated sitemap to Google Search Console"
echo "  □ Submit updated sitemap to Bing Webmaster Tools"
echo "  □ Check Google Search Console for crawl errors"
echo "  □ Monitor indexing status over next 24-48 hours"
echo "  □ Verify all important pages are accessible"

# Step 6: Generate deployment report
print_status "Generating deployment report..."
cat > seo-deployment-report.txt << EOF
HouseGoing SEO Deployment Report
================================
Date: $(date)
Total URLs: $total_urls
Sitemap files: ${#required_files[@]}

Files generated:
$(ls -la public/sitemap*.xml | awk '{print "- " $9 " (" $5 " bytes)"}')

Next actions required:
1. Submit sitemap to Google Search Console
2. Submit sitemap to Bing Webmaster Tools
3. Monitor indexing status
4. Check for crawl errors

Sitemap URLs:
- https://housegoing.com.au/sitemap.xml
- https://housegoing.com.au/sitemap_index.xml
- https://housegoing.com.au/sitemap_main.xml
- https://housegoing.com.au/sitemap_blog.xml
- https://housegoing.com.au/sitemap_venues.xml
EOF

print_success "Deployment report saved to seo-deployment-report.txt"

# Step 7: Final summary
echo ""
echo "🎉 SEO deployment completed successfully!"
echo "📊 Total URLs in sitemap: $total_urls"
echo "📝 Report saved: seo-deployment-report.txt"
echo ""
echo "🔗 Important URLs to submit to search engines:"
echo "   • https://housegoing.com.au/sitemap.xml"
echo "   • https://housegoing.com.au/robots.txt"
echo ""
echo "📈 Monitor your indexing progress at:"
echo "   • Google Search Console: https://search.google.com/search-console"
echo "   • Bing Webmaster Tools: https://www.bing.com/webmasters"
echo ""

# Optional: Ping search engines (if curl is available)
if command -v curl &> /dev/null; then
    print_status "Notifying search engines about sitemap update..."
    
    # Google
    if curl -s "https://www.google.com/ping?sitemap=https://housegoing.com.au/sitemap.xml" > /dev/null; then
        print_success "✓ Google notified"
    else
        print_warning "⚠ Could not notify Google (this is normal, submit manually)"
    fi
    
    # Bing
    if curl -s "https://www.bing.com/ping?sitemap=https://housegoing.com.au/sitemap.xml" > /dev/null; then
        print_success "✓ Bing notified"
    else
        print_warning "⚠ Could not notify Bing (this is normal, submit manually)"
    fi
else
    print_warning "curl not available - submit sitemap manually to search engines"
fi

echo ""
print_success "SEO deployment script completed! 🚀"
