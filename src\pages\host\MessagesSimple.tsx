import React, { useState, useEffect, useRef } from 'react';
import { Search, Send, ArrowLeft, MessageSquare, User } from 'lucide-react';
import { messageStore, type Conversation } from '../../stores/messageStore';

export default function MessagesSimple() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [newMessage, setNewMessage] = useState('');
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);
  const [showConversationList, setShowConversationList] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load conversations from message store
  useEffect(() => {
    const loadConversations = () => {
      const allConversations = messageStore.getConversations();
      setConversations(allConversations);
      
      // Auto-select first conversation if none selected
      if (!selectedConversation && allConversations.length > 0) {
        setSelectedConversation(allConversations[0]);
      }
    };

    // Initial load
    loadConversations();

    // Subscribe to changes
    const unsubscribe = messageStore.subscribe(loadConversations);
    return unsubscribe;
  }, [selectedConversation]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setShowConversationList(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [selectedConversation?.messages]);

  // Filter conversations based on search term
  const filteredConversations = conversations.filter(conversation => {
    return conversation.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           conversation.propertyName.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Handle conversation selection
  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    messageStore.markAsRead(conversation.id);
    if (isMobileView) {
      setShowConversationList(false);
    }
  };

  // Handle back button in mobile view
  const handleBackToList = () => {
    setShowConversationList(true);
  };

  // Handle sending a new message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedConversation) return;

    messageStore.addMessage({
      conversationId: selectedConversation.id,
      from: 'host',
      to: 'guest',
      message: newMessage,
      timestamp: new Date().toISOString(),
      read: false,
      bookingId: selectedConversation.bookingId,
      guestName: selectedConversation.guestName,
      hostName: selectedConversation.hostName,
      propertyName: selectedConversation.propertyName
    });

    setNewMessage('');
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
        <p className="mt-1 text-gray-600">Communicate with your guests</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="flex h-[70vh]">
          {/* Conversation List */}
          {(showConversationList || !isMobileView) && (
            <div className="w-full md:w-1/3 border-r border-gray-200 flex flex-col">
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search conversations..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                </div>
              </div>

              <div className="flex-1 overflow-y-auto">
                {filteredConversations.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 p-4">
                    <MessageSquare className="h-12 w-12 text-gray-300 mb-4" />
                    <p className="text-center">
                      {searchTerm ? 'No conversations match your search' : 'No conversations yet'}
                    </p>
                    <p className="text-sm text-center mt-2">
                      Messages from your bookings will appear here
                    </p>
                  </div>
                ) : (
                  filteredConversations.map((conversation) => (
                    <button
                      key={conversation.id}
                      className={`w-full text-left px-4 py-3 border-b border-gray-200 hover:bg-gray-50 flex items-start transition-colors ${
                        selectedConversation?.id === conversation.id ? 'bg-purple-50 border-purple-200' : ''
                      }`}
                      onClick={() => handleSelectConversation(conversation)}
                    >
                      <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-purple-600" />
                      </div>
                      <div className="ml-3 flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {conversation.guestName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatTimestamp(conversation.lastMessageTime)}
                          </p>
                        </div>
                        <p className="text-sm text-gray-600 truncate">
                          {conversation.propertyName}
                        </p>
                        <p className="text-xs text-gray-500 truncate mt-1">
                          {conversation.lastMessage}
                        </p>
                        {conversation.unreadCount > 0 && (
                          <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-purple-600 rounded-full mt-1">
                            {conversation.unreadCount}
                          </span>
                        )}
                      </div>
                    </button>
                  ))
                )}
              </div>
            </div>
          )}

          {/* Chat Window */}
          {(!showConversationList || !isMobileView) && (
            <div className="w-full md:w-2/3 flex flex-col">
              {selectedConversation ? (
                <>
                  <div className="p-4 border-b border-gray-200 flex items-center">
                    {isMobileView && (
                      <button
                        onClick={handleBackToList}
                        className="mr-2 p-1 rounded-full hover:bg-gray-100"
                      >
                        <ArrowLeft className="h-5 w-5 text-gray-600" />
                      </button>
                    )}
                    <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="ml-3">
                      <p className="font-medium text-gray-900">
                        {selectedConversation.guestName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedConversation.propertyName}
                        {selectedConversation.bookingId && ` • Booking #${selectedConversation.bookingId}`}
                      </p>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                    {selectedConversation.messages.length === 0 ? (
                      <div className="flex items-center justify-center h-full text-gray-500">
                        <div className="text-center">
                          <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <p>No messages yet. Start the conversation!</p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {selectedConversation.messages.map((message) => {
                          const isFromHost = message.from === 'host';
                          return (
                            <div
                              key={message.id}
                              className={`flex ${isFromHost ? 'justify-end' : 'justify-start'}`}
                            >
                              <div
                                className={`max-w-xs sm:max-w-md px-4 py-2 rounded-lg ${
                                  isFromHost
                                    ? 'bg-blue-100 text-blue-900 border border-blue-200'
                                    : 'bg-white text-gray-900 border border-gray-200'
                                }`}
                              >
                                <p className="text-sm">{message.message}</p>
                                <p className={`text-xs mt-1 ${isFromHost ? 'text-blue-600' : 'text-gray-500'}`}>
                                  {formatTimestamp(message.timestamp)}
                                </p>
                              </div>
                            </div>
                          );
                        })}
                        <div ref={messagesEndRef} />
                      </div>
                    )}
                  </div>

                  {/* Message input */}
                  <div className="p-4 border-t border-gray-200">
                    <form onSubmit={handleSendMessage} className="flex items-center">
                      <input
                        type="text"
                        placeholder="Type a message..."
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                      />
                      <button
                        type="submit"
                        disabled={!newMessage.trim()}
                        className="px-4 py-2 bg-purple-600 text-white rounded-r-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Send className="h-5 w-5" />
                      </button>
                    </form>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg">Select a conversation to start messaging</p>
                    <p className="text-gray-400 text-sm mt-2">
                      Messages from your bookings will appear in the left panel
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
