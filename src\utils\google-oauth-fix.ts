/**
 * Google OAuth Specific Fix
 * 
 * This utility helps fix issues with the Clerk-Supabase integration
 * specifically for users who sign up with Google OAuth.
 */

import { supabase } from '../lib/supabase-client';

/**
 * Debug Google OAuth integration issues and attempt fixes
 */
export async function debugGoogleOAuthIntegration(userId: string, userEmail: string): Promise<{
  success: boolean;
  fixes: string[];
  errors: string[];
}> {
  const fixes: string[] = [];
  const errors: string[] = [];
  
  try {
    console.log('Running Google OAuth integration diagnostics for:', userEmail);
    
    // Check if profile exists in Supabase
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('clerk_id', userId)
      .maybeSingle();
      
    if (profileError) {
      console.error('Error checking for user profile:', profileError);
      errors.push(`Profile check error: ${profileError.message}`);
    }
    
    // If no profile exists, create one with minimal data
    if (!profile) {
      console.log('No profile found for Google OAuth user, creating one');
      
      try {
        const { error: createError } = await supabase
          .from('user_profiles')
          .insert({
            clerk_id: userId,
            email: userEmail,
            first_name: '', // These can be updated later
            last_name: '',
            role: 'guest',
            is_host: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
          
        if (createError) {
          console.error('Failed to create profile for Google user:', createError);
          errors.push(`Profile creation error: ${createError.message}`);
        } else {
          fixes.push('Created missing user profile');
        }
      } catch (err) {
        console.error('Exception creating profile:', err);
        errors.push(`Profile creation exception: ${err instanceof Error ? err.message : String(err)}`);
      }
    } else {
      fixes.push('User profile exists');
    }
    
    // Check for other required tables/associations
    // Adjust these based on your schema requirements
    
    // Check for session in auth.sessions
    try {
      const { data: session, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Session error:', sessionError);
        errors.push(`Session error: ${sessionError.message}`);
      } else if (!session?.session) {
        console.warn('No active Supabase session found');
        errors.push('No active Supabase session');
        
        // Try to force session creation
        try {
          const { data: anonData, error: anonError } = await supabase.auth.signInAnonymously();
          
          if (anonError) {
            console.error('Failed to create fallback session:', anonError);
          } else {
            console.log('Created fallback anonymous session');
            fixes.push('Created fallback session');
          }
        } catch (sessionErr) {
          console.error('Exception creating fallback session:', sessionErr);
        }
      } else {
        fixes.push('Supabase session exists');
      }
    } catch (err) {
      console.error('Exception checking session:', err);
      errors.push(`Session check exception: ${err instanceof Error ? err.message : String(err)}`);
    }
    
    // Return results
    return {
      success: errors.length === 0,
      fixes,
      errors
    };
  } catch (err) {
    console.error('Google OAuth fix failed:', err);
    return {
      success: false,
      fixes,
      errors: [...errors, `Global error: ${err instanceof Error ? err.message : String(err)}`]
    };
  }
}
