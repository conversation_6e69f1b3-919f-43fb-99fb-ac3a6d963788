import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

interface Booking {
  id: string;
  venue: {
    title: string;
    images: string[];
  };
  date: string;
  guests: number;
  totalPrice: number;
  status: string;
}

export function useBookings() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    // Replace with actual API call
    const fetchBookings = async () => {
      setIsLoading(true);
      try {
        // Mock data
        setBookings([
          {
            id: '1',
            venue: {
              title: 'Beach House',
              images: ['https://images.unsplash.com/photo-1533929736458-ca588d08c8be']
            },
            date: '2024-04-15',
            guests: 20,
            totalPrice: 1500,
            status: 'Confirmed'
          }
        ]);
      } catch (error) {
        console.error('Error fetching bookings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchBookings();
    }
  }, [user]);

  return { bookings, isLoading };
}