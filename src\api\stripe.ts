/**
 * Stripe API handlers
 *
 * These server-side endpoints handle Stripe API interactions securely,
 * keeping the Stripe secret key on the server side.
 */

import { STRIPE_CONFIG } from '../config/stripe';

// Types
export interface CreatePaymentIntentRequest {
  amount: number;
  currency?: string;
  description?: string;
  customer_email?: string;
  metadata?: Record<string, string>;
  payment_method_types?: string[];
}

export interface CreatePaymentIntentResponse {
  clientSecret: string;
  amount: number;
  id: string;
  currency: string;
  status: string;
  created: number;
}

export interface CreateCustomerRequest {
  email: string;
  name?: string;
  phone?: string;
  metadata?: Record<string, string>;
}

export interface CreateCustomerResponse {
  id: string;
  email: string;
  name?: string;
  created: number;
}

/**
 * Create a payment intent
 *
 * This function creates a Stripe payment intent using the Stripe API.
 * It validates the input and handles errors appropriately.
 */
export async function createPaymentIntent(
  request: Request
): Promise<Response> {
  try {
    // Parse the request body
    const requestData = await request.json() as CreatePaymentIntentRequest;

    // Validate the request
    if (!requestData.amount || requestData.amount <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid payment amount',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Log the request for debugging
    console.log('Creating payment intent with details:', {
      amount: requestData.amount,
      currency: requestData.currency || STRIPE_CONFIG.currency,
      description: requestData.description,
    });

    try {
      // Create the payment intent using the Stripe API
      const stripe = require('stripe')(STRIPE_CONFIG.secretKey);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: requestData.amount,
        currency: requestData.currency || STRIPE_CONFIG.currency,
        description: requestData.description,
        receipt_email: requestData.customer_email,
        metadata: requestData.metadata,
      });

      // Return the payment intent
      return new Response(
        JSON.stringify({
          clientSecret: paymentIntent.client_secret,
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
          created: paymentIntent.created,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (stripeError: any) {
      // Handle Stripe API errors
      console.error('Stripe API error:', stripeError);

      return new Response(
        JSON.stringify({
          error: stripeError.message || 'Failed to create payment intent',
          code: stripeError.code || 'unknown',
          type: stripeError.type || 'api_error',
        }),
        {
          status: stripeError.statusCode || 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  } catch (error: any) {
    // Handle general errors
    console.error('Error creating payment intent:', error);

    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to create payment intent',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

/**
 * Create a Stripe customer
 *
 * This function creates a Stripe customer using the Stripe API.
 * It validates the input and handles errors appropriately.
 */
export async function createCustomer(
  request: Request
): Promise<Response> {
  try {
    // Parse the request body
    const requestData = await request.json() as CreateCustomerRequest;

    // Validate the request
    if (!requestData.email) {
      return new Response(
        JSON.stringify({
          error: 'Email is required',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Log the request for debugging
    console.log('Creating customer with details:', {
      email: requestData.email,
      name: requestData.name,
    });

    try {
      // Create the customer using the Stripe API
      const stripe = require('stripe')(STRIPE_CONFIG.secretKey);

      const customer = await stripe.customers.create({
        email: requestData.email,
        name: requestData.name,
        phone: requestData.phone,
        metadata: requestData.metadata,
      });

      // Return the customer
      return new Response(
        JSON.stringify({
          id: customer.id,
          email: customer.email,
          name: customer.name,
          created: customer.created,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (stripeError: any) {
      // Handle Stripe API errors
      console.error('Stripe API error:', stripeError);

      return new Response(
        JSON.stringify({
          error: stripeError.message || 'Failed to create customer',
          code: stripeError.code || 'unknown',
          type: stripeError.type || 'api_error',
        }),
        {
          status: stripeError.statusCode || 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  } catch (error: any) {
    // Handle general errors
    console.error('Error creating customer:', error);

    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to create customer',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

/**
 * Handle Stripe webhook events
 *
 * This function verifies and processes Stripe webhook events.
 */
export async function handleStripeWebhook(request: Request): Promise<Response> {
  try {
    // Get the signature from the headers
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return new Response(
        JSON.stringify({
          error: 'Missing Stripe signature',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Get the raw body
    const rawBody = await request.text();

    // Verify the webhook signature
    const stripe = require('stripe')(STRIPE_CONFIG.secretKey);

    try {
      const event = stripe.webhooks.constructEvent(
        rawBody,
        signature,
        STRIPE_CONFIG.webhookSecret
      );

      // Process the event based on its type
      switch (event.type) {
        case 'payment_intent.succeeded':
          await handlePaymentIntentSucceeded(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await handlePaymentIntentFailed(event.data.object);
          break;
        // Add more event types as needed
      }

      return new Response(
        JSON.stringify({
          received: true,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (verificationError: any) {
      console.error('Webhook signature verification failed:', verificationError);

      return new Response(
        JSON.stringify({
          error: 'Webhook signature verification failed',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  } catch (error) {
    console.error('Error handling Stripe webhook:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to process webhook',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

/**
 * Handle successful payment intent
 */
async function handlePaymentIntentSucceeded(paymentIntent: any): Promise<void> {
  // Update booking status in the database
  console.log('Payment succeeded:', paymentIntent);

  // Extract booking ID from metadata
  const bookingId = paymentIntent.metadata?.booking_id;

  if (bookingId) {
    try {
      // Update booking status to 'confirmed'
      const { supabase } = await import('../lib/supabase');

      const { error } = await supabase
        .from('bookings')
        .update({
          payment_status: 'paid',
          payment_id: paymentIntent.id,
          payment_amount: paymentIntent.amount,
          payment_date: new Date().toISOString(),
          status: 'confirmed',
          updated_at: new Date().toISOString(),
        })
        .eq('id', bookingId);

      if (error) {
        console.error('Error updating booking status:', error);
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  }
}

/**
 * Handle failed payment intent
 */
async function handlePaymentIntentFailed(paymentIntent: any): Promise<void> {
  // Update booking status in the database
  console.log('Payment failed:', paymentIntent);

  // Extract booking ID from metadata
  const bookingId = paymentIntent.metadata?.booking_id;

  if (bookingId) {
    try {
      // Update booking status to 'payment_failed'
      const { supabase } = await import('../lib/supabase');

      const { error } = await supabase
        .from('bookings')
        .update({
          payment_status: 'failed',
          payment_id: paymentIntent.id,
          payment_error: paymentIntent.last_payment_error?.message || 'Payment failed',
          updated_at: new Date().toISOString(),
        })
        .eq('id', bookingId);

      if (error) {
        console.error('Error updating booking status:', error);
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  }
}
