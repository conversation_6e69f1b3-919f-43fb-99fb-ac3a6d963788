// Test script for <PERSON><PERSON>hai<PERSON> integration
import dotenv from 'dotenv';
import { generateHostAssistantResponse, generateSalesAssistantResponse } from './src/lib/langchain/index.js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Test messages
const hostMessage = "How can I improve my venue's Party Score?";
const salesMessage = "I'm looking for a venue in Sydney for a birthday party with about 50 people.";

// Test history
const mockHistory = [
  { role: 'assistant', content: "Hi there! I'm Homie from HouseGoing. How can I help you today?" },
  { role: 'user', content: "I'm interested in finding a venue for an event." }
];

// Test host assistant
async function testHostAssistant() {
  console.log('Testing Host Assistant...');
  console.log(`User message: "${hostMessage}"`);
  
  try {
    const response = await generateHostAssistantResponse(hostMessage, mockHistory, 'properties');
    console.log('Host Assistant Response:');
    console.log(response);
    console.log('-----------------------------------');
    return true;
  } catch (error) {
    console.error('Error testing Host Assistant:', error);
    console.log('-----------------------------------');
    return false;
  }
}

// Test sales assistant
async function testSalesAssistant() {
  console.log('Testing Sales Assistant...');
  console.log(`User message: "${salesMessage}"`);
  
  try {
    const response = await generateSalesAssistantResponse(salesMessage, mockHistory);
    console.log('Sales Assistant Response:');
    console.log(response);
    console.log('-----------------------------------');
    return true;
  } catch (error) {
    console.error('Error testing Sales Assistant:', error);
    console.log('-----------------------------------');
    return false;
  }
}

// Main function to run all tests
async function runTests() {
  console.log('Starting LangChain integration tests...');
  console.log('===================================');
  
  let successCount = 0;
  
  // Test host assistant
  const hostSuccess = await testHostAssistant();
  if (hostSuccess) successCount++;
  
  // Test sales assistant
  const salesSuccess = await testSalesAssistant();
  if (salesSuccess) successCount++;
  
  console.log(`Test complete: ${successCount}/2 assistants working`);
}

// Run the tests
runTests();
