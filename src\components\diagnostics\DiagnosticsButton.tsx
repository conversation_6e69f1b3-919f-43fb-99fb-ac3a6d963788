import React from 'react';
import { useSession } from '@clerk/clerk-react';
import { logDiagnostics } from '../../utils/integration-diagnostics';

/**
 * This component provides a diagnostic tool for troubleshooting Clerk-Supabase integration issues
 */
export default function DiagnosticsButton() {
  const { session } = useSession();
  const [isRunning, setIsRunning] = React.useState(false);
  const [result, setResult] = React.useState<{ type: 'success' | 'error', message: string } | null>(null);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResult(null);
    
    try {
      console.log('Running Clerk-Supabase integration diagnostics...');
      await logDiagnostics(session);
      setResult({ 
        type: 'success', 
        message: 'Diagnostics complete. Check browser console (F12) for detailed results.'
      });
    } catch (err) {
      console.error('Error running diagnostics:', err);
      setResult({ 
        type: 'error', 
        message: 'Failed to run diagnostics. See console for details.'
      });
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="flex flex-col items-end">
        {result && (
          <div className={`mb-2 p-3 rounded-lg shadow-lg text-sm ${
            result.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {result.message}
          </div>
        )}
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className={`px-4 py-2 rounded-lg shadow-lg font-medium transition-colors ${
            isRunning 
              ? 'bg-gray-400 text-white cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isRunning ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Running...
            </span>
          ) : (
            'Run Integration Diagnostics'
          )}
        </button>
      </div>
    </div>
  );
}
