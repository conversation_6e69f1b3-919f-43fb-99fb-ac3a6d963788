import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import {
  getPropertySubmissionById,
  approvePropertySubmission,
  rejectPropertySubmission,
  PropertyStatus,
  PropertySubmission
} from '../../services/propertySubmissionService';

export default function AdminPropertyDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [property, setProperty] = useState<PropertySubmission | null>(null);
  const [loading, setLoading] = useState(true);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionForm, setShowRejectionForm] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  useEffect(() => {
    async function loadProperty() {
      if (!id) return;
      
      setLoading(true);
      try {
        const data = await getPropertySubmissionById(id);
        setProperty(data);
      } catch (error) {
        console.error('Error loading property:', error);
        setError('Failed to load property details');
      } finally {
        setLoading(false);
      }
    }
    
    loadProperty();
  }, [id]);
  
  const handleApprove = async () => {
    if (!id) return;
    
    setProcessing(true);
    setError('');
    setSuccess('');
    
    try {
      const result = await approvePropertySubmission(id);
      if (result) {
        setSuccess('Property has been approved and is now live on the platform');
        // Refresh property data
        const updatedProperty = await getPropertySubmissionById(id);
        setProperty(updatedProperty);
      } else {
        setError('Failed to approve property');
      }
    } catch (error) {
      console.error('Error approving property:', error);
      setError('An error occurred while approving the property');
    } finally {
      setProcessing(false);
    }
  };
  
  const handleReject = async () => {
    if (!id || !rejectionReason.trim()) {
      setError('Please provide a reason for rejection');
      return;
    }
    
    setProcessing(true);
    setError('');
    setSuccess('');
    
    try {
      const result = await rejectPropertySubmission(id, rejectionReason);
      if (result) {
        setSuccess('Property has been rejected and the host has been notified');
        setShowRejectionForm(false);
        // Refresh property data
        const updatedProperty = await getPropertySubmissionById(id);
        setProperty(updatedProperty);
      } else {
        setError('Failed to reject property');
      }
    } catch (error) {
      console.error('Error rejecting property:', error);
      setError('An error occurred while rejecting the property');
    } finally {
      setProcessing(false);
    }
  };
  
  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          <p className="ml-2">Loading property details...</p>
        </div>
      </AdminLayout>
    );
  }
  
  if (!property) {
    return (
      <AdminLayout>
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg leading-6 font-medium text-gray-900">Property Not Found</h2>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              The property you're looking for doesn't exist or has been removed.
            </p>
          </div>
          <div className="px-4 py-5 sm:p-6">
            <button
              type="button"
              onClick={() => navigate('/admin/properties')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              Back to Properties
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }
  
  return (
    <AdminLayout>
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h2 className="text-lg leading-6 font-medium text-gray-900">Property Details</h2>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Review the property submission details
            </p>
          </div>
          <div>
            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
              property.status === PropertyStatus.APPROVED
                ? 'bg-green-100 text-green-800'
                : property.status === PropertyStatus.REJECTED
                ? 'bg-red-100 text-red-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
            </span>
          </div>
        </div>
        
        {error && (
          <div className="mx-4 my-2 p-2 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mx-4 my-2 p-2 bg-green-50 text-green-700 rounded-md">
            {success}
          </div>
        )}
        
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Property Name</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{property.property_name}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Address</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{property.property_address}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Property Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{property.property_type}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Max Guests</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{property.max_guests}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Hourly Rate</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">${property.hourly_rate} AUD</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{property.description}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Amenities</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <ul className="border border-gray-200 rounded-md divide-y divide-gray-200">
                  {property.amenities.map((amenity, index) => (
                    <li key={index} className="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                      {amenity}
                    </li>
                  ))}
                </ul>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Rules</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <ul className="border border-gray-200 rounded-md divide-y divide-gray-200">
                  {property.rules.map((rule, index) => (
                    <li key={index} className="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                      {rule}
                    </li>
                  ))}
                </ul>
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Host Information</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <p><strong>Name:</strong> {property.host_name}</p>
                <p><strong>Email:</strong> {property.host_email}</p>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Photos</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {property.photos.map((photo, index) => (
                    <div key={index} className="relative h-24 rounded-md overflow-hidden">
                      <img
                        src={photo}
                        alt={`Property photo ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </dd>
            </div>
            
            {property.status === PropertyStatus.REJECTED && property.rejection_reason && (
              <div className="bg-red-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-red-500">Rejection Reason</dt>
                <dd className="mt-1 text-sm text-red-700 sm:mt-0 sm:col-span-2">
                  {property.rejection_reason}
                </dd>
              </div>
            )}
          </dl>
        </div>
      </div>
      
      {property.status === PropertyStatus.PENDING && (
        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Review Decision
            </h3>
            <div className="mt-5 sm:flex sm:items-center">
              {showRejectionForm ? (
                <div className="w-full">
                  <div className="mb-4">
                    <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700">
                      Reason for Rejection
                    </label>
                    <textarea
                      id="rejectionReason"
                      name="rejectionReason"
                      rows={3}
                      className="shadow-sm focus:ring-purple-500 focus:border-purple-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"
                      placeholder="Explain why this property is being rejected..."
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                    />
                  </div>
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={handleReject}
                      disabled={processing || !rejectionReason.trim()}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                      {processing ? 'Processing...' : 'Confirm Rejection'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowRejectionForm(false)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div className="sm:flex sm:items-center sm:space-x-4">
                  <button
                    type="button"
                    onClick={handleApprove}
                    disabled={processing}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    {processing ? 'Processing...' : 'Approve Property'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowRejectionForm(true)}
                    disabled={processing}
                    className="mt-3 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    Reject Property
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
