import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontSize: {
        // Golden ratio typography scale (base: 1rem = 16px)
        'display': ['4.236rem', { lineHeight: '1.15', letterSpacing: '-0.02em' }], // 67.77px
        'h1': ['2.618rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }], // 41.89px
        'h2': ['1.618rem', { lineHeight: '1.3' }], // 25.89px
        'h3': ['1.236rem', { lineHeight: '1.4' }], // 19.77px
        'body': ['1rem', { lineHeight: '1.5' }], // 16px
        'small': ['0.618rem', { lineHeight: '1.5' }], // 9.89px
      },
      spacing: {
        // Golden ratio spacing scale (base: 1rem = 16px)
        'gr1': '0.618rem',    // 9.89px
        'gr2': '1rem',        // 16px
        'gr3': '1.618rem',    // 25.89px
        'gr4': '2.618rem',    // 41.89px
        'gr5': '4.236rem',    // 67.77px
        'gr6': '6.854rem',    // 109.66px
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "#6B4E71", // Soft muted purple
          foreground: "#ffffff",
          light: "#F3E5F5",
          darker: "#4A3650", // Darker shade for contrast
        },
        secondary: {
          DEFAULT: "#8E7F85", // Muted mauve
          foreground: "#ffffff",
          light: "#F5E6EA",
          darker: "#635860", // Darker shade for contrast
        },
        accent: {
          DEFAULT: "#F97316", // Vibrant orange for CTAs
          hover: "#EA580C",
          light: "#FFF7ED",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        sans: ["Inter", "sans-serif"],
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
