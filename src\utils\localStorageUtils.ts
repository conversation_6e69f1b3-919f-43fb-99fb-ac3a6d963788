/**
 * Local Storage Utilities
 * 
 * This module provides functions for storing and retrieving data from localStorage
 * as a fallback when database connections fail.
 */

/**
 * Save data to localStorage
 * @param key The key to store the data under
 * @param data The data to store
 */
export function saveToLocalStorage<T>(key: string, data: T): void {
  try {
    const serializedData = JSON.stringify(data);
    localStorage.setItem(key, serializedData);
    console.log(`Data saved to localStorage with key: ${key}`);
  } catch (error) {
    console.error(`Error saving to localStorage with key ${key}:`, error);
  }
}

/**
 * Retrieve data from localStorage
 * @param key The key to retrieve data from
 * @param defaultValue Default value to return if the key doesn't exist
 * @returns The retrieved data or the default value
 */
export function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const serializedData = localStorage.getItem(key);
    if (serializedData === null) {
      return defaultValue;
    }
    return JSON.parse(serializedData) as T;
  } catch (error) {
    console.error(`Error retrieving from localStorage with key ${key}:`, error);
    return defaultValue;
  }
}

/**
 * Remove data from localStorage
 * @param key The key to remove
 */
export function removeFromLocalStorage(key: string): void {
  try {
    localStorage.removeItem(key);
    console.log(`Data removed from localStorage with key: ${key}`);
  } catch (error) {
    console.error(`Error removing from localStorage with key ${key}:`, error);
  }
}

/**
 * Clear all data from localStorage
 */
export function clearLocalStorage(): void {
  try {
    localStorage.clear();
    console.log('localStorage cleared');
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }
}

/**
 * Check if a key exists in localStorage
 * @param key The key to check
 * @returns True if the key exists, false otherwise
 */
export function existsInLocalStorage(key: string): boolean {
  try {
    return localStorage.getItem(key) !== null;
  } catch (error) {
    console.error(`Error checking if key ${key} exists in localStorage:`, error);
    return false;
  }
}
