import { getSupabaseClient, isSupabaseAvailable } from './api';
import { User } from '@clerk/clerk-react';

// Admin roles with different permission levels
export enum AdminRole {
  SUPER_ADMIN = 'super_admin',  // Full access to everything
  ADMIN = 'admin',              // General admin access
  SUPPORT = 'support'           // Limited admin access for support staff
}

// Admin user interface
export interface AdminUser {
  id: string;
  email: string;
  role: AdminRole;
  name: string;
  lastActive: Date;
}

// Hardcoded admin emails for initial setup and fallback
// In production, this should be moved to a secure environment variable or database
const ADMIN_EMAILS = [
  '<EMAIL>',    // Primary admin
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

/**
 * Check if a user is an admin based on their Clerk user object
 * Uses multiple verification methods for redundancy
 */
export async function isUserAdmin(user: User | null): Promise<boolean> {
  if (!user) return false;

  try {
    // Method 1: Check if email is in the hardcoded admin list (fallback)
    const userEmail = user.primaryEmailAddress?.emailAddress || '';
    const isAdminByEmail = ADMIN_EMAILS.includes(userEmail.toLowerCase());

    // Method 2: Check if user has admin role in Clerk metadata
    const hasAdminRole = user.publicMetadata?.role === 'admin' ||
                         user.publicMetadata?.role === 'super_admin';

    // Method 3: Check admin status in database
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('admin_users')
      .select('role')
      .eq('email', userEmail.toLowerCase())
      .single();

    const isAdminInDatabase = !error && !!data;

    // Log the admin check for debugging
    console.log('Admin check:', {
      email: userEmail,
      isAdminByEmail,
      hasAdminRole,
      isAdminInDatabase,
      result: isAdminByEmail || hasAdminRole || isAdminInDatabase
    });

    // User is admin if any of the methods return true
    return isAdminByEmail || hasAdminRole || isAdminInDatabase;
  } catch (error) {
    console.error('Error checking admin status:', error);

    // Fallback to email check if database check fails
    const userEmail = user.primaryEmailAddress?.emailAddress || '';
    return ADMIN_EMAILS.includes(userEmail.toLowerCase());
  }
}

/**
 * Get admin details from the database
 */
export async function getAdminDetails(user: User): Promise<AdminUser | null> {
  try {
    const userEmail = user.primaryEmailAddress?.emailAddress || '';

    // Try to get admin details from database
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('admin_users')
      .select('id, email, role, name, last_active')
      .eq('email', userEmail.toLowerCase())
      .single();

    if (error || !data) {
      // If not in database but in hardcoded list, return basic admin details
      if (ADMIN_EMAILS.includes(userEmail.toLowerCase())) {
        return {
          id: user.id,
          email: userEmail.toLowerCase(),
          role: AdminRole.ADMIN,
          name: user.fullName || user.firstName || 'Admin User',
          lastActive: new Date()
        };
      }
      return null;
    }

    return {
      id: data.id,
      email: data.email,
      role: data.role as AdminRole,
      name: data.name,
      lastActive: new Date(data.last_active)
    };
  } catch (error) {
    console.error('Error fetching admin details:', error);
    return null;
  }
}

/**
 * Update admin last active timestamp
 */
export async function updateAdminActivity(user: User): Promise<void> {
  try {
    const userEmail = user.primaryEmailAddress?.emailAddress || '';

    const supabase = getSupabaseClient();
    const { error } = await supabase
      .from('admin_users')
      .update({ last_active: new Date().toISOString() })
      .eq('email', userEmail.toLowerCase());

    if (error) {
      console.error('Error updating admin activity:', error);
    }
  } catch (error) {
    console.error('Error updating admin activity:', error);
  }
}

/**
 * Create admin_users table if it doesn't exist
 * This should be called during application initialization
 */
export async function ensureAdminTable(): Promise<void> {
  // Skip if Supabase is not available
  if (!isSupabaseAvailable) {
    console.log('Skipping admin table check - Supabase is not available');
    return;
  }

  try {
    console.log('Checking admin table existence...');

    // Set a timeout for the operation
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('Admin table check timed out')), 5000);
    });

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Check if table exists
    const checkTablePromise = supabase
      .from('admin_users')
      .select('id')
      .limit(1)
      .then(({ error }) => {
        // If table doesn't exist or there's an error, create it
        if (error && error.code === 'PGRST116') {
          const createTableSQL = `
            CREATE TABLE IF NOT EXISTS admin_users (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              email VARCHAR(255) UNIQUE NOT NULL,
              role VARCHAR(50) NOT NULL,
              name VARCHAR(255) NOT NULL,
              last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            -- Insert default admin users
            INSERT INTO admin_users (email, role, name)
            VALUES
              ('<EMAIL>', 'super_admin', 'Tom'),
              ('<EMAIL>', 'admin', 'Admin User'),
              ('<EMAIL>', 'support', 'Support User')
            ON CONFLICT (email) DO NOTHING;
          `;

          // Execute SQL to create table
          return supabase.rpc('exec_sql', { sql: createTableSQL })
            .then(() => {
              console.log('Admin users table created successfully');
            })
            .catch(err => {
              console.error('Error creating admin table:', err);
              throw err;
            });
        } else if (error) {
          console.warn('Admin table check returned error:', error.message);
        } else {
          console.log('Admin table exists');
        }
      });

    // Race the check against the timeout
    await Promise.race([checkTablePromise, timeoutPromise])
      .catch(error => {
        if (error.message === 'Admin table check timed out') {
          console.warn('Admin table check timed out - continuing without admin table');
        } else {
          throw error;
        }
      });
  } catch (error) {
    console.error('Error ensuring admin table exists:', error);
    // Continue application initialization despite error
  }
}
