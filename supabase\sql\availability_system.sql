-- Comprehensive Availability Management System for HouseGoing
-- This creates tables for venue owners to manage their availability

-- 1. Venue Availability Settings (General availability rules)
CREATE TABLE IF NOT EXISTS venue_availability_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  
  -- Days of week availability (0 = Sunday, 1 = Monday, etc.)
  available_days INTEGER[] DEFAULT '{1,2,3,4,5,6,0}', -- Default: all days
  
  -- Default time slots for each day
  default_start_time TIME DEFAULT '09:00:00',
  default_end_time TIME DEFAULT '23:00:00',
  
  -- Minimum booking duration (in hours)
  min_booking_hours INTEGER DEFAULT 4,
  
  -- Maximum booking duration (in hours)
  max_booking_hours INTEGER DEFAULT 12,
  
  -- Lead time required (in hours)
  lead_time_hours INTEGER DEFAULT 24,
  
  -- Maximum advance booking (in days)
  max_advance_days INTEGER DEFAULT 365,
  
  -- Instant booking enabled
  instant_booking_enabled BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venue_id)
);

-- 2. Custom Day Availability (Override default settings for specific days)
CREATE TABLE IF NOT EXISTS venue_day_availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  
  -- Specific date
  date DATE NOT NULL,
  
  -- Available on this date
  is_available BOOLEAN DEFAULT true,
  
  -- Custom time slots for this specific date
  start_time TIME,
  end_time TIME,
  
  -- Special pricing for this date (optional)
  special_price DECIMAL(10,2),
  
  -- Notes for this date
  notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venue_id, date)
);

-- 3. Blocked Time Slots (Specific times that are unavailable)
CREATE TABLE IF NOT EXISTS venue_blocked_slots (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  
  -- Date and time range that's blocked
  start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
  end_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Reason for blocking
  reason TEXT,
  
  -- Type of block
  block_type VARCHAR(20) DEFAULT 'manual', -- 'manual', 'maintenance', 'personal', 'holiday'
  
  -- Recurring block settings
  is_recurring BOOLEAN DEFAULT false,
  recurrence_pattern VARCHAR(20), -- 'weekly', 'monthly', 'yearly'
  recurrence_end_date DATE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT valid_datetime_range CHECK (end_datetime > start_datetime)
);

-- 4. Venue Operating Hours (Different hours for different days)
CREATE TABLE IF NOT EXISTS venue_operating_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  
  -- Day of week (0 = Sunday, 1 = Monday, etc.)
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
  
  -- Operating hours for this day
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  
  -- Is this day available for bookings
  is_available BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(venue_id, day_of_week),
  CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- 5. Seasonal Availability (Holiday periods, seasonal closures)
CREATE TABLE IF NOT EXISTS venue_seasonal_availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  venue_id UUID NOT NULL REFERENCES venues(id) ON DELETE CASCADE,
  
  -- Season name/description
  season_name VARCHAR(100) NOT NULL,
  
  -- Date range
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  
  -- Available during this period
  is_available BOOLEAN DEFAULT true,
  
  -- Special pricing during this period
  price_multiplier DECIMAL(3,2) DEFAULT 1.00, -- 1.5 = 50% increase, 0.8 = 20% discount
  
  -- Recurring yearly
  is_yearly_recurring BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT valid_date_range CHECK (end_date >= start_date),
  CONSTRAINT valid_price_multiplier CHECK (price_multiplier > 0)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_venue_availability_settings_venue_id ON venue_availability_settings(venue_id);
CREATE INDEX IF NOT EXISTS idx_venue_day_availability_venue_date ON venue_day_availability(venue_id, date);
CREATE INDEX IF NOT EXISTS idx_venue_blocked_slots_venue_datetime ON venue_blocked_slots(venue_id, start_datetime, end_datetime);
CREATE INDEX IF NOT EXISTS idx_venue_operating_hours_venue_day ON venue_operating_hours(venue_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_venue_seasonal_availability_venue_dates ON venue_seasonal_availability(venue_id, start_date, end_date);

-- Create indexes for date range queries
CREATE INDEX IF NOT EXISTS idx_venue_blocked_slots_datetime_range ON venue_blocked_slots USING GIST (
  venue_id, 
  tstzrange(start_datetime, end_datetime)
);

-- Row Level Security (RLS) Policies

-- Venue Availability Settings
ALTER TABLE venue_availability_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Venue owners can manage their availability settings"
  ON venue_availability_settings
  FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

CREATE POLICY "Public can view availability settings"
  ON venue_availability_settings
  FOR SELECT
  USING (true);

-- Venue Day Availability
ALTER TABLE venue_day_availability ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Venue owners can manage their day availability"
  ON venue_day_availability
  FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

CREATE POLICY "Public can view day availability"
  ON venue_day_availability
  FOR SELECT
  USING (true);

-- Venue Blocked Slots
ALTER TABLE venue_blocked_slots ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Venue owners can manage their blocked slots"
  ON venue_blocked_slots
  FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

CREATE POLICY "Public can view blocked slots"
  ON venue_blocked_slots
  FOR SELECT
  USING (true);

-- Venue Operating Hours
ALTER TABLE venue_operating_hours ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Venue owners can manage their operating hours"
  ON venue_operating_hours
  FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

CREATE POLICY "Public can view operating hours"
  ON venue_operating_hours
  FOR SELECT
  USING (true);

-- Venue Seasonal Availability
ALTER TABLE venue_seasonal_availability ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Venue owners can manage their seasonal availability"
  ON venue_seasonal_availability
  FOR ALL
  USING (
    venue_id IN (
      SELECT id FROM venues WHERE owner_id = auth.jwt() ->> 'sub'
    )
  );

CREATE POLICY "Public can view seasonal availability"
  ON venue_seasonal_availability
  FOR SELECT
  USING (true);
