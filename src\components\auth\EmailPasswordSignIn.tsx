import React, { useState } from 'react';
import { useSignIn } from '@clerk/clerk-react';
import { CLERK_CONFIG } from '../../config/clerk';

interface EmailPasswordSignInProps {
  role: 'host' | 'guest';
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export default function EmailPasswordSignIn({ role, onSuccess, onError }: EmailPasswordSignInProps) {
  const isOwnerPortal = role === 'host';
  const { isLoaded, signIn, setActive } = useSignIn();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Authentication system is not ready yet. Please try again in a moment.');
      return;
    }

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Attempt to sign in with email and password
      const result = await signIn.create({
        identifier: email,
        password,
      });

      if (result.status === 'complete') {
        // Set the user session as active
        await setActive({ session: result.createdSessionId });

        // Redirect to the appropriate page
        const redirectUrl = role === 'host'
          ? CLERK_CONFIG.hostSignInRedirectURL
          : CLERK_CONFIG.signInRedirectURL;

        if (onSuccess) {
          onSuccess();
        } else {
          window.location.href = redirectUrl;
        }
      } else if (result.status === 'needs_second_factor') {
        // Handle 2FA if needed
        setError('Two-factor authentication is required but not supported in this form.');
      } else if (result.status === 'needs_identifier') {
        setError('Please enter your email address.');
      } else if (result.status === 'needs_password') {
        setError('Please enter your password.');
      } else if (result.status === 'needs_email_verification') {
        // Handle email verification
        await signIn.prepareEmailAddressVerification({ strategy: 'email_code' });
        setVerifying(true);
      } else {
        setError('An unexpected error occurred. Please try again.');
      }

      setLoading(false);
    } catch (err) {
      console.error('Error during sign in:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during sign in');
      setLoading(false);
      if (onError && err instanceof Error) {
        onError(err);
      }
    }
  };

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!verificationCode) {
      setError('Please enter the verification code sent to your email');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Attempt to verify the email
      const result = await signIn.attemptEmailAddressVerification({
        code: verificationCode
      });

      if (result.status !== 'complete') {
        // Handle incomplete sign in
        setError('Verification failed. Please try again.');
        setLoading(false);
        return;
      }

      // Set the user session as active
      await setActive({ session: result.createdSessionId });

      // Redirect to the appropriate page
      const redirectUrl = role === 'host'
        ? CLERK_CONFIG.hostSignInRedirectURL
        : CLERK_CONFIG.signInRedirectURL;

      if (onSuccess) {
        onSuccess();
      } else {
        window.location.href = redirectUrl;
      }
    } catch (err) {
      console.error('Error during verification:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during verification');
      setLoading(false);
      if (onError && err instanceof Error) {
        onError(err);
      }
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      setError('Please enter your email address to reset your password');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await signIn.create({
        identifier: email,
        strategy: 'reset_password_email_code',
      });

      setError('');
      alert(`Password reset instructions have been sent to ${email}`);
      setLoading(false);
    } catch (err) {
      console.error('Error during password reset:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during password reset');
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return <div className="text-center py-4">Loading...</div>;
  }

  if (verifying) {
    return (
      <div className="w-full max-w-md mx-auto">
        <h3 className="text-xl font-semibold text-center mb-4">
          {isOwnerPortal ? 'Verify your Owner Portal email' : 'Verify your email'}
        </h3>
        <p className="text-gray-600 text-center mb-6">
          We've sent a verification code to {email}. Please enter it below.
        </p>

        {error && (
          <div className="bg-red-50 text-red-600 p-3 rounded-lg mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleVerify} className="space-y-4">
          <div>
            <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
              Verification Code
            </label>
            <input
              id="verificationCode"
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
              placeholder="Enter code"
              disabled={loading}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-800 text-white py-3 rounded-lg font-medium hover:from-purple-700 hover:to-purple-900 transition-all duration-200 flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Verifying...
              </>
            ) : (
              'Verify Email'
            )}
          </button>
        </form>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {error && (
        <div className="bg-red-50 text-red-600 p-3 rounded-lg mb-4 text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
            placeholder="Your email"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
            placeholder="Your password"
            disabled={loading}
          />
        </div>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-sm text-purple-600 hover:text-purple-800"
            disabled={loading}
          >
            Forgot password?
          </button>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-gradient-to-r from-purple-600 to-purple-800 text-white py-3 rounded-lg font-medium hover:from-purple-700 hover:to-purple-900 transition-all duration-200 flex items-center justify-center"
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing In...
            </>
          ) : (
            isOwnerPortal ? 'Sign In to Owner Portal' : 'Sign In'
          )}
        </button>
      </form>
    </div>
  );
}
