CREATE TABLE property_patterns (
  id SERIAL PRIMARY KEY,
  property_type VARCHAR(50) NOT NULL,
  pattern VARCHAR(100) NOT NULL,
  example VARCHAR(100) NOT NULL
);

INSERT INTO property_patterns (property_type, pattern, example)
VALUES
  ('Apartment/Unit', '^\\d+/\\d+', '1/89 Smith St'),
  ('Apartment/Unit', '^Unit \\d+', 'Unit 4 89 Smith St'),
  ('Apartment/Unit', '^Apartment \\d+', 'Apartment 4 89 Smith St'),
  ('Apartment/Unit', '^Flat \\d+', 'Flat 4 89 Smith St'),
  ('Townhouse', '^Townhouse \\d+', 'Townhouse 4 89 Smith St'),
  ('Townhouse', '^Villa \\d+', 'Villa 4 89 Smith St'),
  ('Commercial', '^Suite \\d+', 'Suite 4 89 Smith St'),
  ('Commercial', '^Level \\d+', 'Level 4 89 Smith St'),
  ('Commercial', '^Office \\d+', 'Office 4 89 Smith St'),
  ('Industrial', '^Factory Unit \\d+', 'Factory Unit 4 89 Smith St'),
  ('Industrial', '^Warehouse \\d+', 'Warehouse 4 89 Smith St');
