import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

export default function RedirectTest() {
  const navigate = useNavigate();
  const [method, setMethod] = useState<'navigate' | 'window'>('navigate');
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          if (method === 'navigate') {
            console.log('RedirectTest: Using navigate()');
            navigate('/host/dashboard', { replace: true });
          } else {
            console.log('RedirectTest: Using window.location.href');
            window.location.href = '/host/dashboard';
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [method, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-center mb-4">Redirect Test</h1>
        
        <div className="text-center mb-6">
          <p className="text-gray-600 mb-2">Testing redirect methods</p>
          <p className="text-lg font-semibold">Redirecting in {countdown} seconds...</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Redirect Method:
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="navigate"
                  checked={method === 'navigate'}
                  onChange={(e) => setMethod(e.target.value as 'navigate')}
                  className="mr-2"
                />
                React Router navigate()
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="window"
                  checked={method === 'window'}
                  onChange={(e) => setMethod(e.target.value as 'window')}
                  className="mr-2"
                />
                window.location.href
              </label>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => {
                if (method === 'navigate') {
                  navigate('/host/dashboard', { replace: true });
                } else {
                  window.location.href = '/host/dashboard';
                }
              }}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
            >
              Test Now
            </button>
            
            <button
              onClick={() => navigate('/debug/auth')}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700"
            >
              Back to Debug
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
