import React from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { useAuth } from '../../providers/AuthProvider';
import MessageNotifications from '../messaging/MessageNotifications';

interface HeaderAuthButtonsProps {
  className?: string;
  variant?: 'default' | 'owner';
}

export default function HeaderAuthButtons({ className = '', variant = 'default' }: HeaderAuthButtonsProps) {
  const { isAuthenticated, isLoading, user, signOut, isAdmin } = useAuth();

  // Show a loading state instead of nothing
  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="h-9 w-20 bg-gray-100 rounded-md animate-pulse"></div>
        <div className="h-9 w-20 bg-purple-100 rounded-md animate-pulse"></div>
      </div>
    );
  }

  // Determine the login/signup URLs based on the variant
  const loginUrl = variant === 'owner' ? '/host/login?userType=host' : '/login?userType=guest';
  const signupUrl = variant === 'owner' ? '/host/signup?userType=host' : '/signup?userType=guest';
  const accountUrl = variant === 'owner' ? '/host/dashboard' : '/my-account';

  // Show authenticated state
  if (isAuthenticated && user) {
    // Get user name from the authenticated user object
    let userName = 'User';

    // Extract user information from the user object
    const firstName = user.firstName;
    const lastName = user.lastName;
    const email = user.primaryEmailAddress?.emailAddress;

    // Set the short name for the button
    if (firstName) {
      userName = firstName;
    } else if (email) {
      userName = email.split('@')[0];
    }

    // Determine the correct account URL based on admin status
    let finalAccountUrl = accountUrl;
    if (isAdmin) {
      finalAccountUrl = '/admin/dashboard';
    }

    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {/* Message Notifications - only show for regular users, not owners */}
        {variant === 'default' && !isAdmin && <MessageNotifications />}

        <Link to={finalAccountUrl}>
          <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm flex items-center">
            <User className="w-4 h-4 mr-1.5 text-purple-600" />
            <span>{isAdmin ? 'Admin Panel' : variant === 'owner' ? 'Dashboard' : 'My Account'}</span>
          </button>
        </Link>

        <button
          onClick={signOut}
          className="text-sm font-medium px-3 py-2.5 rounded-md text-gray-500 hover:text-gray-700 transition-colors flex items-center"
          title="Sign Out"
        >
          <LogOut className="w-4 h-4" />
        </button>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <Link to={loginUrl}>
        <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 transition-colors flex items-center shadow-sm">
          <User className="w-4 h-4 mr-1.5" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to={signupUrl}>
        <button className="text-sm font-medium px-5 py-2.5 rounded-md bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 transition-colors shadow-sm">
          Sign Up
        </button>
      </Link>
    </div>
  );
}
