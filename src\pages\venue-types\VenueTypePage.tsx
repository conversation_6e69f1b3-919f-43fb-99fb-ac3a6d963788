import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Wine, Music, PartyPopper, Users, Clock, Star, ArrowRight } from 'lucide-react';

// Venue type data
const venueTypeData: Record<string, {
  name: string;
  description: string;
  longDescription: string;
  features: string[];
  averagePrice: string;
  capacity: string;
  bestFor: string[];
  seoTitle: string;
  seoDescription: string;
  icon: any;
}> = {
  'cocktail-lounges': {
    name: 'Cocktail Lounges',
    description: 'Sophisticated cocktail lounges perfect for upscale events and celebrations',
    longDescription: 'Cocktail lounges offer sophisticated, upscale environments perfect for elegant celebrations, corporate events, and intimate gatherings. These venues typically feature premium bar service, stylish decor, ambient lighting, and comfortable seating arrangements that create the perfect atmosphere for socializing.',
    features: ['Premium bar service', 'Stylish decor', 'Ambient lighting', 'Comfortable seating', 'Professional bartenders'],
    averagePrice: '$400-800/hour',
    capacity: '30-100 people',
    bestFor: ['Corporate events', 'Cocktail parties', 'Product launches', 'Networking events'],
    seoTitle: 'Cocktail Lounge Hire Sydney NSW | Upscale Cocktail Venues',
    seoDescription: 'Book sophisticated cocktail lounges in Sydney for your upscale events. Premium bar service, stylish venues perfect for corporate events & celebrations.',
    icon: Wine
  },
  'dance-venues': {
    name: 'Dance Venues',
    description: 'High-energy venues with professional sound systems and dance floors',
    longDescription: 'Dance venues are designed for high-energy celebrations with professional-grade sound systems, spacious dance floors, and lighting that creates the perfect party atmosphere. These venues are ideal for birthday parties, celebrations, and any event where dancing is the main focus.',
    features: ['Professional sound system', 'Large dance floor', 'DJ booth', 'Party lighting', 'High-energy atmosphere'],
    averagePrice: '$300-600/hour',
    capacity: '50-200 people',
    bestFor: ['Birthday parties', 'Celebrations', 'Dance parties', 'Youth events'],
    seoTitle: 'Dance Venue Hire Sydney NSW | Party Venues with Dance Floors',
    seoDescription: 'Hire dance venues in Sydney with professional sound systems and dance floors. Perfect party venues for birthdays, celebrations & dance events.',
    icon: Music
  },
  'wedding-after-parties': {
    name: 'Wedding After-Party Venues',
    description: 'Continue the celebration with venues perfect for wedding after-parties',
    longDescription: 'Wedding after-party venues provide the perfect space to continue your wedding celebration in a more relaxed, party atmosphere. These venues often feature late-night access, bar service, dance floors, and a fun environment where your wedding party can let loose and celebrate.',
    features: ['Late-night access', 'Bar service', 'Dance floor', 'Relaxed atmosphere', 'Party lighting'],
    averagePrice: '$350-700/hour',
    capacity: '40-120 people',
    bestFor: ['Wedding after-parties', 'Reception extensions', 'Bridal parties', 'Wedding celebrations'],
    seoTitle: 'Wedding After-Party Venue Hire Sydney NSW | Reception After-Party Spaces',
    seoDescription: 'Book wedding after-party venues in Sydney to continue your celebration. Perfect spaces for reception extensions with dance floors & bar service.',
    icon: PartyPopper
  }
};

export default function VenueTypePage() {
  const { venueType } = useParams<{ venueType: string }>();
  const venueInfo = venueType ? venueTypeData[venueType] : null;

  if (!venueInfo) {
    return <Navigate to="/find-venues" replace />;
  }

  const IconComponent = venueInfo.icon;

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: venueInfo.name,
    description: venueInfo.description,
    provider: {
      '@type': 'Organization',
      name: 'HouseGoing',
      url: 'https://housegoing.com.au'
    },
    areaServed: {
      '@type': 'State',
      name: 'New South Wales',
      addressCountry: 'AU'
    },
    priceRange: venueInfo.averagePrice,
    serviceType: 'Venue Rental'
  };

  return (
    <>
      <Helmet>
        <title>{venueInfo.seoTitle}</title>
        <meta name="description" content={venueInfo.seoDescription} />
        <meta property="og:title" content={venueInfo.seoTitle} />
        <meta property="og:description" content={venueInfo.seoDescription} />
        <meta property="og:url" content={`https://housegoing.com.au/venue-types/${venueType}`} />
        <link rel="canonical" href={`https://housegoing.com.au/venue-types/${venueType}`} />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      <div className="pt-20 pb-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <IconComponent className="h-16 w-16 mx-auto mb-4" />
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {venueInfo.name} in NSW
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {venueInfo.description}
              </p>
              <Link
                to={`/find-venues?type=${venueType}`}
                className="inline-flex items-center px-8 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
              >
                Browse Venues
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="prose prose-lg max-w-none">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  About {venueInfo.name}
                </h2>
                <p className="text-gray-600 mb-8">
                  {venueInfo.longDescription}
                </p>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Key Features
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  {venueInfo.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Star className="h-5 w-5 text-purple-600 mr-3" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Perfect For
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  {venueInfo.bestFor.map((eventType, index) => (
                    <div key={index} className="flex items-center p-4 border border-gray-200 rounded-lg">
                      <PartyPopper className="h-5 w-5 text-purple-600 mr-3" />
                      <span className="text-gray-700">{eventType}</span>
                    </div>
                  ))}
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Booking Tips
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <ul className="space-y-2 text-gray-700">
                    <li>• Book well in advance, especially for weekend events</li>
                    <li>• Confirm sound system capabilities and restrictions</li>
                    <li>• Ask about catering options and kitchen access</li>
                    <li>• Check parking availability for your guests</li>
                    <li>• Understand the venue's noise and time restrictions</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Venue Details
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Average Price</div>
                      <div className="text-gray-600">{venueInfo.averagePrice}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Capacity</div>
                      <div className="text-gray-600">{venueInfo.capacity}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Need Help Choosing?
                </h3>
                <p className="text-gray-600 mb-4">
                  Our venue experts can help you find the perfect {venueInfo.name.toLowerCase()} for your event.
                </p>
                <Link
                  to="/contact"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Get Expert Help
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Book Your {venueInfo.name}?
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Browse our selection of {venueInfo.name.toLowerCase()} and find the perfect space for your event.
            </p>
            <Link
              to={`/find-venues?type=${venueType}`}
              className="inline-flex items-center px-8 py-4 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors text-lg"
            >
              Browse {venueInfo.name}
              <ArrowRight className="ml-2 h-6 w-6" />
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
