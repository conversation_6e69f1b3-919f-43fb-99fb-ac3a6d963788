import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Menu, Home, Search, Calendar, User } from 'lucide-react';
import Logo from './navigation/Logo';
import MainNav from './navigation/MainNav';
import MobileMenu from './navigation/MobileMenu';
import HeaderAuthButtons from './auth/HeaderAuthButtons';
import HeaderMobileAuthStatus from './auth/HeaderMobileAuthStatus';

interface HeaderProps {
  isMobile?: boolean;
}

export default function Header({ isMobile = false }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  React.useEffect(() => {
    console.log('Header component rendered - Should include MainNav with NSW Party Planning link');
    console.log('Current location:', location.pathname);
  }, [location.pathname]);

  return (
    <>
      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
          <div className="flex justify-around items-center h-16">
            <button className="flex flex-col items-center justify-center w-full h-full touch-target">
              <Home className="h-6 w-6" />
              <span className="text-xs mt-1">Home</span>
            </button>
            <button className="flex flex-col items-center justify-center w-full h-full touch-target">
              <Search className="h-6 w-6" />
              <span className="text-xs mt-1">Search</span>
            </button>
            <button className="flex flex-col items-center justify-center w-full h-full touch-target">
              <Calendar className="h-6 w-6" />
              <span className="text-xs mt-1">Bookings</span>
            </button>
            <button className="flex flex-col items-center justify-center w-full h-full touch-target">
              <User className="h-6 w-6" />
              <span className="text-xs mt-1">Account</span>
            </button>
          </div>
        </nav>
      )}

      {/* Main Header */}
      <header className="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md z-50 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="flex justify-between items-center py-4">
            {/* Left side - Logo */}
            <Logo />

            {/* Center - Main Navigation (Desktop only) */}
            <div className="hidden md:flex flex-grow justify-center">
              <MainNav />
            </div>

            {/* Right side - Auth buttons and mobile menu */}
            <div className="flex items-center space-x-4">
              {/* Desktop auth buttons */}
              {!isMobile && (
                <div className="hidden md:block">
                  <HeaderAuthButtons />
                </div>
              )}

              {/* Mobile menu button - Enhanced for touch */}
              {isMobile && (
                <button
                  className="md:hidden btn-reactive p-3 rounded-xl hover:bg-gray-100 active:bg-gray-200 touch-target focus-enhanced min-h-[52px] min-w-[52px] flex items-center justify-center transition-all duration-200"
                  aria-label="Open menu"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  <Menu className="h-7 w-7 text-gray-700" />
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        currentPath={location.pathname}
      />
    </>
  );
}
