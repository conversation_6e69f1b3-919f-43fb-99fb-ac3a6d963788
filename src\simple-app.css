/* Styles for the SimpleApp component */

/* General layout */
.app-container {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #111827;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header styles */
.app-header {
  background-color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #7c3aed;
}

.main-nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.main-nav a {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
}

.main-nav a:hover {
  color: #7c3aed;
}

.main-nav li.active a {
  color: #7c3aed;
}

.main-nav li.active a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #7c3aed;
}

.user-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.login-button, .signup-button, .host-button, .logout-button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.login-button {
  background-color: transparent;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.login-button:hover {
  background-color: #f9fafb;
}

.signup-button, .host-button {
  background-color: #7c3aed;
  color: white;
  border: none;
}

.signup-button:hover, .host-button:hover {
  background-color: #6d28d9;
}

.logout-button {
  background-color: #ef4444;
  color: white;
  border: none;
}

.logout-button:hover {
  background-color: #dc2626;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-name {
  font-weight: 500;
  color: #111827;
}

/* Main content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Hero section */
.hero-section {
  text-align: center;
  padding: 3rem 0;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #111827;
}

.hero-section .highlight {
  color: #7c3aed;
}

.hero-section .subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  margin-bottom: 2rem;
}

/* Search form */
.search-form {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-bottom: 3rem;
}

.search-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.search-group {
  flex: 1;
  min-width: 200px;
}

.search-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.search-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
}

.search-button {
  background-color: #7c3aed;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  align-self: flex-end;
}

.search-button:hover {
  background-color: #6d28d9;
}

/* Venues section */
.venues-section {
  margin-bottom: 4rem;
}

.venues-section h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #111827;
}

.venues-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.venue-card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s, box-shadow 0.2s;
}

.venue-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.venue-image {
  height: 200px;
  background-size: cover;
  background-position: center;
}

.venue-details {
  padding: 1.5rem;
}

.venue-details h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #111827;
}

.venue-location {
  color: #6b7280;
  margin-bottom: 1rem;
}

.venue-description {
  color: #4b5563;
  margin-bottom: 1rem;
}

.venue-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.venue-rating {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stars {
  color: #f59e0b;
  margin-right: 0.5rem;
}

.reviews {
  color: #6b7280;
  font-size: 0.875rem;
}

.view-button {
  background-color: #7c3aed;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.view-button:hover {
  background-color: #6d28d9;
}

/* Features section */
.features-section {
  margin-bottom: 4rem;
}

.features-section h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #111827;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.feature-card h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: #111827;
}

.feature-card p {
  color: #4b5563;
}

/* How it works section */
.how-it-works-section {
  margin-bottom: 4rem;
}

.how-it-works-section h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #111827;
  text-align: center;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.step {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.step-number {
  background-color: #7c3aed;
  color: white;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1.5rem;
}

.step h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: #111827;
}

.step p {
  color: #4b5563;
}

/* AI Assistant section */
.ai-assistant-section {
  margin-bottom: 4rem;
}

.ai-assistant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.ai-assistant-section h2 {
  font-size: 2rem;
  color: #111827;
  margin: 0;
}

.reset-chat-button {
  background-color: #f3f4f6;
  color: #7c3aed;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.reset-chat-button:hover {
  background-color: #7c3aed;
  color: white;
  border-color: #7c3aed;
}

.chat-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  max-width: 800px;
  margin: 0 auto;
  height: 600px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
}

.message-content {
  padding: 1rem;
  border-radius: 0.5rem;
  max-width: 80%;
  position: relative;
}

.message-content p {
  margin: 0;
  line-height: 1.5;
}

.message-content p strong {
  font-weight: 600;
}

.message.assistant {
  justify-content: flex-start;
}

.message.assistant .message-content {
  background-color: #f3f4f6;
  margin-left: 40px;
  color: #111827;
}

.message.assistant .message-content::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 15px;
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent #f3f4f6 transparent transparent;
}

.message.user {
  justify-content: flex-end;
}

.message.user .message-content {
  background-color: #7c3aed;
  color: white;
  margin-right: 40px;
}

.message.user .message-content::before {
  content: '';
  position: absolute;
  right: -10px;
  top: 15px;
  border-width: 10px 0 10px 10px;
  border-style: solid;
  border-color: transparent transparent transparent #7c3aed;
}

.assistant-avatar, .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  position: absolute;
}

.assistant-avatar {
  background-color: #7c3aed;
  color: white;
  left: 0;
}

.user-avatar {
  background-color: #e5e7eb;
  color: #4b5563;
  right: 0;
}

/* Quick replies */
.quick-replies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

/* Venue previews in chat */
.venue-previews {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
  max-width: 100%;
}

.venue-preview {
  display: flex;
  background-color: white;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  max-width: 100%;
  border: 1px solid #e5e7eb;
}

.venue-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.venue-preview-image {
  width: 100px;
  height: 80px;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

.venue-preview-details {
  padding: 0.5rem 0.75rem;
  flex: 1;
  min-width: 0;
}

.venue-preview-details h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.venue-preview-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.venue-preview-rating {
  font-size: 0.75rem;
}

.venue-preview-rating .stars {
  color: #f59e0b;
  margin-right: 0.25rem;
}

.venue-preview-rating .reviews {
  color: #6b7280;
}

.quick-reply-button {
  background-color: #f3f4f6;
  color: #7c3aed;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 0.5rem;
}

.quick-reply-button:hover {
  background-color: #7c3aed;
  color: white;
  border-color: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Typing indicator */
.message.typing .message-content {
  padding: 0.75rem;
  min-width: 60px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: #7c3aed;
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.4s infinite ease-in-out both;
  opacity: 0.7;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
  }
  28% {
    transform: translateY(-7px);
  }
  44% {
    transform: translateY(0);
  }
}

.chat-input {
  display: flex;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background-color: white;
}

.chat-input input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  margin-right: 0.5rem;
}

.chat-input button {
  background-color: #7c3aed;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-input button:hover {
  background-color: #6d28d9;
}

/* Host dashboard */
.host-dashboard {
  margin-bottom: 4rem;
}

.host-dashboard h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #111827;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.stat-card h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: #111827;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #7c3aed;
}

.dashboard-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.action-button {
  background-color: #7c3aed;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #6d28d9;
}

/* Footer */
.app-footer {
  background-color: #1f2937;
  color: white;
  padding: 3rem 2rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  flex: 1;
  min-width: 200px;
}

.footer-section h4 {
  font-size: 1.125rem;
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #d1d5db;
  text-decoration: none;
}

.footer-section a:hover {
  color: white;
}

.copyright {
  text-align: center;
  margin-top: 2rem;
  color: #9ca3af;
  font-size: 0.875rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Login modal */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.login-modal.show {
  opacity: 1;
  visibility: visible;
}

.login-content {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 400px;
  position: relative;
}

.login-content h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #111827;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
}

.login-button {
  background-color: #7c3aed;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.login-button:hover {
  background-color: #6d28d9;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: transparent;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
}

.close-button:hover {
  color: #111827;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    padding: 1rem;
  }

  .logo {
    margin-bottom: 1rem;
  }

  .main-nav {
    margin-bottom: 1rem;
  }

  .main-nav ul {
    gap: 1rem;
  }

  .search-inputs {
    flex-direction: column;
  }

  .search-group {
    width: 100%;
  }

  .hero-section h1 {
    font-size: 2.5rem;
  }

  .venues-grid, .features-grid, .steps-container, .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
  }
}
