/**
 * Cloudinary configuration and utility functions
 */
import { Cloudinary } from 'cloudinary-core';

// Cloudinary configuration
export const cloudinaryConfig = {
  cloudName: 'dcdjxfnud',
  uploadPreset: 'housegoing_uploads', // Created by the setup-cloudinary.js script
};

// Create a Cloudinary instance
export const cloudinary = new Cloudinary({
  cloud_name: cloudinaryConfig.cloudName,
  secure: true,
});

/**
 * Generate a Cloudinary URL for an image
 * @param publicId The public ID of the image
 * @param options Transformation options
 * @returns The Cloudinary URL
 */
export const getCloudinaryUrl = (publicId: string, options: any = {}) => {
  return cloudinary.url(publicId, options);
};

/**
 * Generate a Cloudinary URL with responsive transformations
 * @param publicId The public ID of the image
 * @param options Transformation options
 * @returns The Cloudinary URL
 */
export const getResponsiveUrl = (publicId: string, options: any = {}) => {
  return cloudinary.url(publicId, {
    width: 'auto',
    crop: 'scale',
    quality: 'auto',
    fetch_format: 'auto',
    ...options,
  });
};

/**
 * Generate a Cloudinary URL for a thumbnail
 * @param publicId The public ID of the image
 * @param width The width of the thumbnail
 * @param height The height of the thumbnail
 * @returns The Cloudinary URL
 */
export const getThumbnailUrl = (publicId: string, width = 200, height = 200) => {
  return cloudinary.url(publicId, {
    width,
    height,
    crop: 'fill',
    gravity: 'auto',
    quality: 'auto',
    fetch_format: 'auto',
  });
};

/**
 * Extract the public ID from a Cloudinary URL
 * @param url The Cloudinary URL
 * @returns The public ID
 */
export const getPublicIdFromUrl = (url: string): string => {
  // Example URL: https://res.cloudinary.com/dcdjxfnud/image/upload/v1234567890/folder/image.jpg
  const regex = /\/v\d+\/(.+)$/;
  const match = url.match(regex);
  if (match && match[1]) {
    // Remove file extension
    return match[1].replace(/\.[^/.]+$/, '');
  }
  return '';
};

export default cloudinary;
