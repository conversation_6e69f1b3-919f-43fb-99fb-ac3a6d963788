import { findZoneForPoint, findLGAForPoint } from './spatialUtils';
import { FeatureCollection, Point, Polygon } from 'geojson';

const mockZones: FeatureCollection<Polygon> = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { ZONE_CODE: 'R1' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [151.0, -33.8], [151.5, -33.8], 
          [151.5, -34.0], [151.0, -34.0], [151.0, -33.8]
        ]]
      }
    } as any,
    {
      type: 'Feature',
      properties: { ZONE_CODE: 'B1' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [151.2, -33.85], [151.3, -33.85],
          [151.3, -33.9], [151.2, -33.9], [151.2, -33.85]
        ]]
      }
    } as any
  ]
};

const mockLGAs: FeatureCollection<Polygon> = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { LGA_NAME: 'Sydney' },
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [150.9, -33.7], [151.6, -33.7],
          [151.6, -34.1], [150.9, -34.1], [150.9, -33.7]
        ]]
      }
    } as any
  ]
};

describe('spatialUtils', () => {
  test('finds residential zone', () => {
    const point: Point = {
      type: 'Point',
      coordinates: [151.1, -33.9]
    };
    expect(findZoneForPoint(point, mockZones)).toBe('R1');
  });

  test('finds business zone', () => {
    const point: Point = {
      type: 'Point',
      coordinates: [151.25, -33.87]
    };
    expect(findZoneForPoint(point, mockZones)).toBe('B1');
  });

  test('finds LGA', () => {
    const point: Point = {
      type: 'Point',
      coordinates: [151.2, -33.8]
    };
    expect(findLGAForPoint(point, mockLGAs)).toBe('Sydney');
  });
});
