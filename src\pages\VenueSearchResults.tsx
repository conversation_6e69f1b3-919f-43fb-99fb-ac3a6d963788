import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { MapPin, Users, DollarSign, Star, Clock, Shield, Zap } from 'lucide-react';
import { mockVenues, searchVenues, MockVenue } from '../data/mockVenues';

export default function VenueSearchResults() {
  const [searchParams] = useSearchParams();
  const [exactMatches, setExactMatches] = useState<MockVenue[]>([]);
  const [suggestions, setSuggestions] = useState<MockVenue[]>([]);
  const [searchType, setSearchType] = useState<string>('exact');
  const [searchMessage, setSearchMessage] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [searchedLocation, setSearchedLocation] = useState<string>('');

  useEffect(() => {
    const performSearch = async () => {
      const filters = {
        location: searchParams.get('location') || undefined,
        maxBudget: searchParams.get('budget') ? parseInt(searchParams.get('budget')!) : undefined,
        guestCount: searchParams.get('guests') ? parseInt(searchParams.get('guests')!) : undefined,
        venueType: searchParams.get('type') || undefined,
      };

      const searchResult = await searchVenues(mockVenues, filters);
      console.log('🔍 VENUE SEARCH RESULTS DEBUG:', searchResult);
      console.log('🎯 Exact matches found:', searchResult.exactMatches.length);
      console.log('💡 Suggestions found:', searchResult.suggestions.length);

      // Set exact matches and suggestions separately for two-section display
      setExactMatches(searchResult.exactMatches);
      setSuggestions(searchResult.suggestions); // Show ALL suggestions
      setSearchedLocation(filters.location || '');
      setSearchType(searchResult.searchType);
      setSearchMessage(searchResult.message || '');
      setLoading(false);
    };

    // Simulate API call delay
    setTimeout(performSearch, 1000);
  }, [searchParams]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-32">
        <div className="container-width">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Searching for perfect venues...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-32">
      <div className="container-width">
        {/* Search Results Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <h1 className="text-3xl font-bold text-gray-900">
              {exactMatches.length + suggestions.length} Amazing Venues
              {searchedLocation && (
                <span className="text-lg font-normal text-gray-600 ml-2">
                  for {searchedLocation}
                </span>
              )}
            </h1>
            <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
              Ready to party! 🎉
            </div>
          </div>

          {/* Smart Search Message */}
          {searchMessage && searchType !== 'exact' && (
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <div className="text-blue-600 mt-0.5">
                  {searchType === 'nearby' && '📍'}
                  {searchType === 'expanded_budget' && '💰'}
                  {searchType === 'expanded_capacity' && '👥'}
                  {searchType === 'all_venues' && '🏠'}
                </div>
                <div>
                  <p className="text-blue-800 font-medium text-sm">{searchMessage}</p>
                  {searchType === 'nearby' && (
                    <p className="text-blue-600 text-xs mt-1">
                      We've expanded your search to include venues across NSW
                    </p>
                  )}
                  {searchType === 'expanded_budget' && (
                    <p className="text-blue-600 text-xs mt-1">
                      Consider adjusting your budget or contact venues for custom pricing
                    </p>
                  )}
                  {searchType === 'expanded_capacity' && (
                    <p className="text-blue-600 text-xs mt-1">
                      Many venues can accommodate larger groups with special arrangements
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-wrap gap-2 text-sm text-gray-600">
            {searchParams.get('location') && (
              <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                📍 {searchParams.get('location')}
              </span>
            )}
            {searchParams.get('guests') && (
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                👥 {searchParams.get('guests')} guests
              </span>
            )}
            {searchParams.get('budget') && (
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                💰 Up to {formatPrice(parseInt(searchParams.get('budget')!))}
              </span>
            )}
          </div>
        </div>

        {/* Exact Matches Section */}
        {exactMatches.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Venues in {searchedLocation}, NSW
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {exactMatches.map((venue) => (
                <VenueCard key={venue.id} venue={venue} isExactMatch={true} formatPrice={formatPrice} />
              ))}
            </div>
          </div>
        )}

        {/* Suggestions Section */}
        {suggestions.length > 0 && (
          <div className="mb-12">
            <div className="flex items-center gap-3 mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Areas outside your search that you might like
              </h2>
              <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                Other locations 📍
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {suggestions.map((venue) => (
                <VenueCard key={venue.id} venue={venue} isExactMatch={false} formatPrice={formatPrice} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Venue Card Component
function VenueCard({ venue, isExactMatch, formatPrice }: { venue: MockVenue; isExactMatch: boolean; formatPrice: (price: number) => string }) {
  return (
    <div className={`bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 group cursor-pointer ${
      isExactMatch ? 'border-2 border-purple-200' : 'border border-gray-100'
    }`}>
      {/* Venue Image */}
      <div className={`relative h-64 overflow-hidden ${
        venue.venueType === 'Rooftop Terrace' ? 'bg-gradient-to-br from-purple-100 to-purple-300' :
        venue.venueType === 'Beach House' ? 'bg-gradient-to-br from-blue-100 to-cyan-300' :
        venue.venueType === 'Warehouse' ? 'bg-gradient-to-br from-gray-100 to-gray-400' :
        venue.venueType === 'Pavilion' ? 'bg-gradient-to-br from-teal-100 to-blue-300' :
        venue.venueType === 'Gallery' ? 'bg-gradient-to-br from-pink-100 to-purple-300' :
        venue.venueType === 'Function Centre' ? 'bg-gradient-to-br from-indigo-100 to-purple-300' :
        venue.venueType === 'Restaurant' ? 'bg-gradient-to-br from-green-100 to-emerald-300' :
        venue.venueType === 'Event Space' ? 'bg-gradient-to-br from-orange-100 to-yellow-300' :
        venue.venueType === 'Community Hall' ? 'bg-gradient-to-br from-red-100 to-pink-300' :
        'bg-gradient-to-br from-purple-100 to-purple-300'
      }`}>
        <div className="absolute inset-0 bg-gradient-to-br from-black/10 to-black/30"></div>

        {/* Placeholder for venue image */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="text-4xl mb-2">
              {venue.venueType === 'Rooftop Terrace' && '🏙️'}
              {venue.venueType === 'Beach House' && '🏖️'}
              {venue.venueType === 'Warehouse' && '🏭'}
              {venue.venueType === 'Pavilion' && '🌊'}
              {venue.venueType === 'Gallery' && '🎨'}
              {venue.venueType === 'Function Centre' && '🏢'}
              {venue.venueType === 'Restaurant' && '🌿'}
              {venue.venueType === 'Event Space' && '🏄‍♂️'}
              {venue.venueType === 'Community Hall' && '🏛️'}
              {venue.venueType === 'Function Room' && '⛰️'}
            </div>
            <div className="text-sm font-medium opacity-90">{venue.venueType}</div>
          </div>
        </div>

        {/* Top badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {isExactMatch && (
            <span className="bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
              📍 Perfect Match
            </span>
          )}
          {venue.verified && (
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
              <Shield className="h-3 w-3" />
              Verified
            </span>
          )}
          {venue.instantBook && (
            <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
              <Zap className="h-3 w-3" />
              Instant Book
            </span>
          )}
        </div>

        {/* Party Score */}
        <div className="absolute top-3 right-3">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 text-xs font-semibold text-purple-600">
            🎉 {venue.partyScore.toFixed(1)}
          </div>
        </div>

        {/* Price overlay */}
        <div className="absolute bottom-3 right-3">
          <div className="bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 text-right">
            <div className="text-lg font-bold text-gray-900">
              {formatPrice(venue.pricing.hourlyRate)}<span className="text-sm font-normal">/hr</span>
            </div>
            <div className="text-xs text-gray-600">
              Min {formatPrice(venue.pricing.totalMinimum)}
            </div>
          </div>
        </div>
      </div>

      {/* Venue Content */}
      <div className="p-5">
        {/* Location & Title */}
        <div className="mb-3">
          <div className="flex items-center text-gray-500 text-sm mb-1">
            <MapPin className="h-4 w-4 mr-1" />
            {venue.location.suburb}, NSW
          </div>
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
            {venue.name}
          </h3>
        </div>

        {/* Capacity */}
        <div className="flex items-center text-gray-600 text-sm mb-3">
          <Users className="h-4 w-4 mr-1" />
          Up to {venue.capacity.recommended} guests
        </div>

        {/* Key Features */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {venue.features.slice(0, 3).map((feature, index) => (
              <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-md text-xs">
                {feature}
              </span>
            ))}
            {venue.features.length > 3 && (
              <span className="text-gray-500 text-xs px-2 py-1">
                +{venue.features.length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Rating & Response */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className="text-sm font-medium text-gray-900">{venue.host.rating}</span>
            <span className="text-sm text-gray-500">({venue.host.reviewCount})</span>
          </div>
          <div className="flex items-center text-gray-500 text-sm">
            <Clock className="h-4 w-4 mr-1" />
            {venue.host.responseTime}
          </div>
        </div>

        {/* Action Button */}
        <button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
          {venue.instantBook ? 'Book Now' : 'View Details'}
        </button>
      </div>
    </div>
  );
}
