import React from 'react';
import { Users, Minus, Plus } from 'lucide-react';
import SearchDropdown from './SearchDropdown';

interface GuestPickerProps {
  isOpen: boolean;
  onClose: () => void;
  guests: number;
  onGuestsChange: (count: number) => void;
}

export default function GuestPicker({ isOpen, onClose, guests, onGuestsChange }: GuestPickerProps) {
  const guestRanges = [
    { min: 1, max: 5, label: '1-5 guests', icon: '🎉' },
    { min: 6, max: 10, label: '6-10 guests', icon: '🥳' },
    { min: 11, max: 20, label: '11-20 guests', icon: '👥' },
    { min: 21, max: 40, label: '21-40 guests', icon: '🎪' },
    { min: 41, max: 100, label: '41+ guests', icon: '🏰' }
  ];

  return (
    <SearchDropdown isOpen={isOpen} onClose={onClose} width="320px">
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-6 text-gray-800">Number of Guests</h3>
        <div className="space-y-3">
          {guestRanges.map((range) => (
            <button
              key={range.label}
              onClick={() => {
                onGuestsChange(range.min);
                onClose();
              }}
              className={`w-full flex items-center justify-between p-4 rounded-lg hover:bg-purple-50 transition-all duration-200 ${
                guests >= range.min && guests <= range.max
                  ? 'bg-purple-50 text-purple-600 border border-purple-200'
                  : 'text-gray-700 border border-transparent hover:border-purple-100'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{range.icon}</span>
                <span className="font-medium">{range.label}</span>
              </div>
              <Users className="h-5 w-5" />
            </button>
          ))}
        </div>
      </div>
    </SearchDropdown>
  );
}