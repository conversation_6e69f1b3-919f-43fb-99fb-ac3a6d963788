import React, { useState, useEffect } from 'react';
import { Star, Calendar, User } from 'lucide-react';
import { getReviews, Review } from '../../api/userAccount';

interface ReviewsSectionProps {
  userId: string;
}

export default function ReviewsSection({ userId }: ReviewsSectionProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'received' | 'written'>('received');

  useEffect(() => {
    fetchReviews();
  }, [userId]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const data = await getReviews(userId);
      setReviews(data);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const receivedReviews = reviews.filter(review => review.reviewee_id === userId);
  const writtenReviews = reviews.filter(review => review.reviewer_id === userId);

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const renderReviewCard = (review: Review, isReceived: boolean) => (
    <div key={review.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 font-inter">
              {isReceived ? review.reviewer_name : 'You'}
            </h4>
            <p className="text-sm text-gray-600 font-inter">{review.venue_name}</p>
          </div>
        </div>
        <div className="text-right">
          {renderStars(review.rating)}
          <div className="flex items-center gap-1 mt-1 text-xs text-gray-500">
            <Calendar className="w-3 h-3" />
            <span className="font-inter">
              {new Date(review.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>
      </div>
      
      {review.comment && (
        <p className="text-gray-700 leading-relaxed font-inter">{review.comment}</p>
      )}
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium font-inter ${
          review.type === 'guest_to_host' 
            ? 'bg-blue-100 text-blue-800' 
            : 'bg-green-100 text-green-800'
        }`}>
          {review.type === 'guest_to_host' ? 'Guest Review' : 'Host Review'}
        </span>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  const averageRating = receivedReviews.length > 0 
    ? receivedReviews.reduce((sum, review) => sum + review.rating, 0) / receivedReviews.length 
    : 0;

  return (
    <div className="space-y-6">
      {/* Rating Summary */}
      {receivedReviews.length > 0 && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
          <div className="flex items-center gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 font-inter">
                {averageRating.toFixed(1)}
              </div>
              {renderStars(Math.round(averageRating))}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 font-inter">Your Rating</h3>
              <p className="text-gray-600 font-inter">
                Based on {receivedReviews.length} review{receivedReviews.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('received')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors font-inter ${
              activeTab === 'received'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Reviews about you ({receivedReviews.length})
          </button>
          <button
            onClick={() => setActiveTab('written')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors font-inter ${
              activeTab === 'written'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Reviews by you ({writtenReviews.length})
          </button>
        </nav>
      </div>

      {/* Reviews Content */}
      <div className="space-y-4">
        {activeTab === 'received' ? (
          receivedReviews.length > 0 ? (
            receivedReviews.map(review => renderReviewCard(review, true))
          ) : (
            <div className="text-center py-12">
              <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">No reviews yet</h3>
              <p className="text-gray-600 font-inter">
                Reviews from hosts will appear here after your bookings
              </p>
            </div>
          )
        ) : (
          writtenReviews.length > 0 ? (
            writtenReviews.map(review => renderReviewCard(review, false))
          ) : (
            <div className="text-center py-12">
              <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">No reviews written</h3>
              <p className="text-gray-600 font-inter">
                Reviews you write for venues will appear here
              </p>
            </div>
          )
        )}
      </div>
    </div>
  );
}
