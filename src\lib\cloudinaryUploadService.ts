/**
 * Service for uploading images to Cloudinary
 */
import { cloudinaryConfig } from './cloudinary';

/**
 * Upload a file to Cloudinary using the unsigned upload method
 * @param file The file to upload
 * @param folder Optional folder to upload to
 * @returns Promise with the upload result
 */
export const uploadToCloudinary = async (
  file: File,
  folder = 'property-images'
): Promise<CloudinaryUploadResult> => {
  try {
    console.log('Uploading file to Cloudinary:', file.name);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', cloudinaryConfig.uploadPreset);
    formData.append('folder', folder);
    formData.append('cloud_name', cloudinaryConfig.cloudName);

    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Failed to upload image');
    }

    const data = await response.json();
    return {
      publicId: data.public_id,
      url: data.secure_url,
      format: data.format,
      width: data.width,
      height: data.height,
      originalFilename: data.original_filename,
      resourceType: data.resource_type,
      error: null,
    };
  } catch (error: any) {
    console.error('Error uploading to Cloudinary:', error);
    return {
      publicId: '',
      url: '',
      format: '',
      width: 0,
      height: 0,
      originalFilename: '',
      resourceType: '',
      error: error.message || 'Failed to upload image',
    };
  }
};

/**
 * Upload multiple files to Cloudinary
 * @param files Array of files to upload
 * @param folder Optional folder to upload to
 * @param onProgress Optional callback for upload progress
 * @returns Promise with array of upload results
 */
export const uploadMultipleToCloudinary = async (
  files: File[],
  folder = 'property-images',
  onProgress?: (progress: number) => void
): Promise<CloudinaryUploadResult[]> => {
  const results: CloudinaryUploadResult[] = [];
  let completedUploads = 0;

  for (const file of files) {
    try {
      const result = await uploadToCloudinary(file, folder);
      results.push(result);

      completedUploads++;
      if (onProgress) {
        onProgress(Math.round((completedUploads / files.length) * 100));
      }
    } catch (error) {
      console.error(`Error uploading file ${file.name}:`, error);
      // Continue with next file instead of failing the entire batch
    }
  }

  return results;
};

/**
 * Interface for Cloudinary upload result
 */
export interface CloudinaryUploadResult {
  publicId: string;
  url: string;
  format: string;
  width: number;
  height: number;
  originalFilename: string;
  resourceType: string;
  error: string | null;
}

export default {
  uploadToCloudinary,
  uploadMultipleToCloudinary,
};
