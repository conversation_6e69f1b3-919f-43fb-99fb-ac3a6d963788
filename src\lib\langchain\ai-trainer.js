/**
 * AI-assisted training system for HouseGoing assistants
 * This module uses AI to analyze conversations, provide feedback, and suggest prompt improvements
 */
import { createHuggingFaceModel } from './models';
import { getPromptTemplate, updatePromptTemplateDirectly } from './feedback';
import { mockAnalyzeConversation, mockSuggestPromptImprovements } from './mock-trainer';

/**
 * Analyzes a conversation and provides AI-generated feedback
 * @param {Array} conversation - The conversation history
 * @param {string} agentType - The type of agent (sales, host)
 * @returns {Promise<Object>} - AI-generated feedback
 */
export async function analyzeConversation(conversation, agentType) {
  try {
    // For development, use a mock analysis instead of calling the actual model
    // This avoids the need for a Hugging Face API key during development
    if (process.env.NODE_ENV === 'development') {
      return mockAnalyzeConversation(conversation, agentType);
    }

    // Create the model
    const model = createHuggingFaceModel();

    // Format the conversation for analysis
    const formattedConversation = conversation.map(msg =>
      `${msg.role.toUpperCase()}: ${msg.content}`
    ).join('\n\n');

    // Create the analysis prompt
    const analysisPrompt = `
You are an expert AI trainer for ${agentType === 'sales' ? 'sales assistants' : 'host assistants'} at HouseGoing, a platform for booking party venues.

Below is a conversation between a user and the ${agentType} assistant. Analyze this conversation and provide:

1. STRENGTHS: What the assistant did well
2. WEAKNESSES: Where the assistant could improve
3. SUGGESTIONS: Specific suggestions for improvement
4. RATING: A rating from 1-10 on the assistant's performance

Conversation:
${formattedConversation}

Your analysis:
`;

    // Generate the analysis
    const analysis = await model.invoke(analysisPrompt);

    // Parse the analysis
    const strengths = extractSection(analysis, 'STRENGTHS');
    const weaknesses = extractSection(analysis, 'WEAKNESSES');
    const suggestions = extractSection(analysis, 'SUGGESTIONS');
    const rating = extractRating(analysis);

    return {
      strengths,
      weaknesses,
      suggestions,
      rating,
      fullAnalysis: analysis
    };
  } catch (error) {
    console.error('Error analyzing conversation:', error);
    return {
      strengths: 'Unable to analyze strengths due to an error.',
      weaknesses: 'Unable to analyze weaknesses due to an error.',
      suggestions: 'Unable to provide suggestions due to an error.',
      rating: 0,
      fullAnalysis: 'Error analyzing conversation.'
    };
  }
}

/**
 * Suggests improvements to a prompt template based on feedback
 * @param {string} currentPrompt - The current prompt template
 * @param {Array} feedbackItems - Array of feedback items
 * @param {string} agentType - The type of agent (sales, host)
 * @returns {Promise<Object>} - Suggested prompt improvements
 */
export async function suggestPromptImprovements(currentPrompt, feedbackItems, agentType) {
  try {
    // For development, use a mock suggestion instead of calling the actual model
    // This avoids the need for a Hugging Face API key during development
    if (process.env.NODE_ENV === 'development') {
      return mockSuggestPromptImprovements(currentPrompt, feedbackItems, agentType);
    }

    // Create the model
    const model = createHuggingFaceModel();

    // Format the feedback for analysis
    const formattedFeedback = feedbackItems.map(item =>
      `USER MESSAGE: ${item.userMessage}\nASSISTANT RESPONSE: ${item.assistantResponse}\nRATING: ${item.rating}\nNOTES: ${item.notes || 'No notes provided.'}`
    ).join('\n\n');

    // Create the improvement prompt
    const improvementPrompt = `
You are an expert AI prompt engineer. Your task is to improve a prompt template based on user feedback.

CURRENT PROMPT TEMPLATE:
${currentPrompt}

USER FEEDBACK ON ASSISTANT RESPONSES:
${formattedFeedback}

Based on this feedback, suggest specific improvements to the prompt template. Focus on:
1. Addressing common issues mentioned in negative feedback
2. Reinforcing behaviors praised in positive feedback
3. Making the prompt more specific and actionable
4. Maintaining the assistant's core purpose and tone

Your suggested improvements:
`;

    // Generate the improvements
    const improvements = await model.invoke(improvementPrompt);

    // Generate a new prompt version
    const newPromptPrompt = `
Based on the current prompt and the suggested improvements, create a new version of the prompt template.

CURRENT PROMPT TEMPLATE:
${currentPrompt}

SUGGESTED IMPROVEMENTS:
${improvements}

NEW PROMPT TEMPLATE:
`;

    const newPrompt = await model.invoke(newPromptPrompt);

    return {
      improvements,
      newPrompt,
      currentPrompt
    };
  } catch (error) {
    console.error('Error suggesting prompt improvements:', error);
    return {
      improvements: 'Unable to suggest improvements due to an error.',
      newPrompt: currentPrompt,
      currentPrompt
    };
  }
}

/**
 * Automatically applies prompt improvements based on feedback
 * @param {string} agentType - The type of agent (sales, host)
 * @returns {Promise<Object>} - Result of the operation
 */
export async function autoImprovePrompt(agentType) {
  try {
    // Get the current prompt
    const currentPrompt = await getPromptTemplate(agentType);

    // Get recent feedback (this would be replaced with a database call)
    const recentFeedback = [
      {
        userMessage: "I'm looking for a venue in Sydney for about 50 people",
        assistantResponse: "I'd be happy to help you find a venue in Sydney for 50 people. What type of event are you planning?",
        rating: "good",
        notes: "Good follow-up question, but could have shown some venue options right away."
      },
      {
        userMessage: "It's for a birthday party next month",
        assistantResponse: "Great! A birthday party sounds fun. Do you have a specific date in mind?",
        rating: "bad",
        notes: "Should have shown venue options instead of asking another question."
      }
    ];

    // Suggest improvements
    const { newPrompt } = await suggestPromptImprovements(currentPrompt, recentFeedback, agentType);

    // Update the prompt
    const result = await updatePromptTemplateDirectly(agentType, newPrompt);

    return {
      success: result.success,
      oldPrompt: currentPrompt,
      newPrompt,
      message: result.success ? 'Prompt automatically improved' : 'Failed to update prompt'
    };
  } catch (error) {
    console.error('Error auto-improving prompt:', error);
    return {
      success: false,
      message: 'Error auto-improving prompt'
    };
  }
}

// Helper functions
function extractSection(text, sectionName) {
  const regex = new RegExp(`${sectionName}[:\\s]+(.*?)(?=\\n\\n|\\n[A-Z]+:|$)`, 's');
  const match = text.match(regex);
  return match ? match[1].trim() : `No ${sectionName.toLowerCase()} found.`;
}

function extractRating(text) {
  const regex = /RATING[:\\s]+(\d+)/;
  const match = text.match(regex);
  return match ? parseInt(match[1], 10) : 0;
}
