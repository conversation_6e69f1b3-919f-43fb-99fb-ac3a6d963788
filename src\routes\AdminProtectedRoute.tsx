import React from 'react';
import { Navigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import AdminNav from '../components/admin/AdminNav';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

export default function AdminProtectedRoute({ children }: AdminProtectedRouteProps) {
  const { user, isLoaded, isSignedIn } = useUser();

  // Production authentication required

  // Production mode: normal authentication flow
  // DIRECT OVERRIDE: List of admin emails
  const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

  // Get user email from Clerk
  const userEmail = user?.primaryEmailAddress?.emailAddress || '';

  // Check if user is admin
  const isAdmin = ADMIN_EMAILS.includes(userEmail.toLowerCase());

  // Log for debugging
  console.log('Admin check:', { email: userEmail, isAdmin, isSignedIn });

  // Show loading state
  if (!isLoaded) {
    return (
      <div className="pt-32 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        <p className="ml-3 text-purple-600">Loading...</p>
      </div>
    );
  }

  // Redirect to login if not signed in
  if (!isSignedIn) {
    return <Navigate to="/login" />;
  }

  // Redirect to unauthorized page if not admin
  if (!isAdmin) {
    return <Navigate to="/unauthorized" />;
  }

  return (
    <>
      <AdminNav />
      {children}
    </>
  );
}
