/**
 * Simple proxy API to handle CORS issues with NSW Planning Portal APIs
 * This file should be used with a server-side framework like Next.js or Express
 */

export async function proxyRequest(url: string) {
  try {
    console.log(`Proxying request to: ${url}`);
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Proxy request failed with status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Proxy request error:', error);
    throw error;
  }
}

// Example implementation for a Next.js API route
// This would be placed in pages/api/proxy.ts in a Next.js project
/*
import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { url } = req.query;
  
  if (!url || typeof url !== 'string') {
    return res.status(400).json({ error: 'URL parameter is required' });
  }
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    return res.status(200).json(data);
  } catch (error) {
    console.error('Proxy error:', error);
    return res.status(500).json({ error: 'Failed to proxy request' });
  }
}
*/
