import { useEffect, useState, useRef } from 'react';
import { useAuth, useUser, useClerk, useSession } from '@clerk/clerk-react';
import { createClerkSupabaseClient } from '../../lib/clerk-supabase-official';
import DiagnosticsButton from '../diagnostics/DiagnosticsButton';
import GoogleOAuthDiagnosticsButton from '../diagnostics/GoogleOAuthDiagnosticsButton';
import { logAuthEvent } from '../../utils/auth-debug';

export default function ClerkSupabaseInitializer({ children }: { children: React.ReactNode }) {
  const { isLoaded, userId } = useAuth();
  const { user } = useUser();
  const { session } = useSession();
  const clerk = useClerk();
  const [initialized, setInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initAttempts, setInitAttempts] = useState(0);
  const maxAttempts = 5;
  const [isTimeout, setIsTimeout] = useState(false);
  const [isGoogleOAuth, setIsGoogleOAuth] = useState(false);
  const supabaseClientRef = useRef<any>(null);

  // Google OAuth detection
  useEffect(() => {
    const isGoogleCallback = window.location.pathname.includes('/auth/callback') &&
      (localStorage.getItem('google_oauth_flow') === 'true' ||
       document.referrer.includes('google') ||
       document.referrer.includes('accounts.google.com'));
    
    if (isGoogleCallback) {
      setIsGoogleOAuth(true);
      localStorage.setItem('google_oauth_detected', 'true');
    }
  }, []);

  // Timeout handling - more aggressive to prevent stuck loading
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!initialized && !error) {
        console.log('Authentication initialization timed out, proceeding anyway');
        setIsTimeout(true);
        setInitialized(true);
        
        // Force a proceed on account page if we're on that page
        if (window.location.pathname.includes('/my-account')) {
          console.log('Detected we are on account page, forcing initialization');
          localStorage.setItem('force_account_proceed', 'true');
        }
      }
    }, 5000); // Reduced timeout to 5 seconds for better UX

    return () => clearTimeout(timeoutId);
  }, [initialized, error]);

  // Main initialization effect
  useEffect(() => {
    const initializeClerkSupabase = async () => {
      if (!isLoaded) return;

      if (initAttempts >= maxAttempts) {
        setInitialized(true);
        return;
      }

      setInitAttempts(prev => prev + 1);

      if (userId && user && session) {
        try {
          if (!supabaseClientRef.current) {
            console.log('Creating Clerk-Supabase client...');
            supabaseClientRef.current = await createClerkSupabaseClient(session);
            console.log('Clerk-Supabase client created successfully');
          }

          const supabase = supabaseClientRef.current;
          
          // Query the user_profiles table
          let querySuccess = false;
          let data, error;
          
          try {
            const result = await supabase
              .from('user_profiles')
              .select('*')
              .limit(1);
              
            data = result.data;
            error = result.error;
            
            if (!error) {
              querySuccess = true;
              console.log('user_profiles query successful:', data);
            } else {
              console.warn('user_profiles query failed:', error);
              
              // Check for UUID format errors
              if (error.code === '22P02' && error.message.includes('invalid input syntax for type uuid')) {
                console.warn('UUID format error detected. This may indicate a mismatch between UUID and Clerk ID formats.');
              }
            }
          } catch (e) {
            console.error('Error querying user_profiles table:', e);
            error = e;
          }

          if (querySuccess) {
            console.log('Supabase connection successful, found records:', data);
            setInitialized(true);
          } else {
            console.error('All Supabase table queries failed:', error);
            if (initAttempts < maxAttempts - 1) {
              console.log(`Retrying in 1.5s (attempt ${initAttempts + 1}/${maxAttempts})...`);
              setTimeout(initializeClerkSupabase, 1500);
            } else {
              console.log('Max attempts reached, proceeding anyway');
              setInitialized(true);
            }
          }
        } catch (err) {
          console.error('Initialization error:', err);
          if (initAttempts >= maxAttempts - 1) {
            setError('Failed to initialize authentication');
          } else {
            setTimeout(initializeClerkSupabase, 1500);
          }
        }
      } else {
        setInitialized(true); // Allow anonymous access
      }
    };

    initializeClerkSupabase();
  }, [isLoaded, userId, user, session, initAttempts]);

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error && !isGoogleOAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-white shadow-md rounded-lg p-8 max-w-md text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold mb-4">Authentication Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <a href="/" className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-md inline-block">
            Return to Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <>
      {isTimeout && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Some features may not work properly. If you experience issues, please refresh the page.
              </p>
            </div>
          </div>
        </div>
      )}
      {!import.meta.env.DEV && <DiagnosticsButton />}
      {isGoogleOAuth && <GoogleOAuthDiagnosticsButton />}
      {children}
    </>
  );
}
