import { getSupabaseClient } from '../lib/supabase-client';
import { transformedMockVenues } from '../data/mockVenues';

/**
 * Populate Supabase with venues from mock data
 * This is a utility function to add more venues to the database
 */
export async function populateSupabaseVenues() {
  try {
    const supabase = getSupabaseClient();
    
    console.log('🏠 POPULATE: Starting to populate Supabase with mock venues...');
    console.log('🏠 POPULATE: Total mock venues to add:', transformedMockVenues.length);

    // First, check what venues already exist
    const { data: existingVenues, error: fetchError } = await supabase
      .from('venues')
      .select('id, title, name');

    if (fetchError) {
      console.error('🏠 POPULATE: Error fetching existing venues:', fetchError);
      return;
    }

    console.log('🏠 POPULATE: Existing venues in DB:', existingVenues?.length || 0);
    const existingIds = new Set(existingVenues?.map(v => v.id) || []);

    // Convert mock venues to Supabase format
    const venuesToInsert = transformedMockVenues
      .filter(mockVenue => !existingIds.has(mockVenue.id))
      .map(mockVenue => ({
        id: mockVenue.id,
        title: mockVenue.title,
        name: mockVenue.title, // Fallback for compatibility
        description: mockVenue.description,
        location: mockVenue.location.suburb,
        address: mockVenue.location.address,
        coordinates: {
          latitude: mockVenue.location.latitude,
          longitude: mockVenue.location.longitude
        },
        price: mockVenue.price || mockVenue.pricing.totalMinimum,
        capacity: mockVenue.capacity.recommended,
        amenities: mockVenue.amenities,
        features: mockVenue.features,
        images: mockVenue.images,
        venue_type: mockVenue.venueType,
        event_types: mockVenue.eventTypes,
        rules: mockVenue.rules,
        host_name: mockVenue.host.name,
        host_rating: mockVenue.host.rating,
        host_response_time: mockVenue.host.responseTime,
        rating: mockVenue.rating,
        review_count: mockVenue.reviews,
        verified: mockVenue.verified,
        instant_book: mockVenue.instantBook,
        is_published: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

    if (venuesToInsert.length === 0) {
      console.log('🏠 POPULATE: No new venues to insert - all mock venues already exist in DB');
      return;
    }

    console.log('🏠 POPULATE: Inserting', venuesToInsert.length, 'new venues...');
    console.log('🏠 POPULATE: New venue titles:', venuesToInsert.map(v => v.title));

    // Insert venues in batches to avoid timeout
    const batchSize = 5;
    let insertedCount = 0;

    for (let i = 0; i < venuesToInsert.length; i += batchSize) {
      const batch = venuesToInsert.slice(i, i + batchSize);
      
      const { data, error } = await supabase
        .from('venues')
        .insert(batch)
        .select();

      if (error) {
        console.error('🏠 POPULATE: Error inserting batch:', error);
        console.error('🏠 POPULATE: Failed batch:', batch.map(v => v.title));
      } else {
        insertedCount += batch.length;
        console.log('🏠 POPULATE: Successfully inserted batch:', batch.map(v => v.title));
      }
    }

    console.log('🏠 POPULATE: Completed! Inserted', insertedCount, 'venues');
    
    // Verify the final count
    const { data: finalVenues, error: finalError } = await supabase
      .from('venues')
      .select('id, title')
      .eq('is_published', true);

    if (!finalError) {
      console.log('🏠 POPULATE: Final venue count in DB:', finalVenues?.length || 0);
      console.log('🏠 POPULATE: All venue titles:', finalVenues?.map(v => v.title));
    }

  } catch (error) {
    console.error('🏠 POPULATE: Unexpected error:', error);
  }
}

/**
 * Clear all venues from Supabase (for testing)
 */
export async function clearSupabaseVenues() {
  try {
    const supabase = getSupabaseClient();
    
    console.log('🏠 CLEAR: Clearing all venues from Supabase...');
    
    const { error } = await supabase
      .from('venues')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all except impossible ID

    if (error) {
      console.error('🏠 CLEAR: Error clearing venues:', error);
    } else {
      console.log('🏠 CLEAR: Successfully cleared all venues');
    }
  } catch (error) {
    console.error('🏠 CLEAR: Unexpected error:', error);
  }
}
