import { useState } from 'react';

interface UseFormSubmitOptions<T, R> {
  onSubmit: (data: T) => Promise<R>;
  onSuccess?: (result: R) => void;
  onError?: (error: Error) => void;
  resetOnSuccess?: boolean;
}

export function useFormSubmit<T, R>({ 
  onSubmit, 
  onSuccess, 
  onError,
  resetOnSuccess = false
}: UseFormSubmitOptions<T, R>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [result, setResult] = useState<R | null>(null);

  const handleSubmit = async (data: T) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      const result = await onSubmit(data);
      setResult(result);
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      if (resetOnSuccess) {
        reset();
      }
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      
      if (onError) {
        onError(error);
      }
      
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const reset = () => {
    setError(null);
    setResult(null);
  };

  return {
    handleSubmit,
    isSubmitting,
    error,
    result,
    reset
  };
}
