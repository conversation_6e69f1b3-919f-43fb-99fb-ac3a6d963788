import React from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { useSupabase } from '../../providers/SupabaseProvider';

interface SupabaseAuthButtonProps {
  className?: string;
}

export default function SupabaseAuthButton({ className = '' }: SupabaseAuthButtonProps) {
  const { isAuthenticated, isLoading, userProfile, signOut } = useSupabase();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (isLoading) {
    return <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse"></div>;
  }

  if (isAuthenticated && userProfile) {
    // User is signed in
    return (
      <div className="flex items-center space-x-4">
        <Link to="/my-account">
          <button className="inline-flex items-center justify-center px-4 py-2 rounded-lg font-semibold transition-all duration-200 bg-white text-gray-800 border border-gray-200 shadow-sm hover:border-purple-600 hover:shadow-md">
            My Account
          </button>
        </Link>
        <Link to="/messages">
          <button className="inline-flex items-center justify-center px-4 py-2 rounded-lg font-semibold transition-all duration-200 bg-white text-gray-800 border border-gray-200 shadow-sm hover:border-purple-600 hover:shadow-md">
            Messages
          </button>
        </Link>
        <button
          onClick={handleSignOut}
          className="inline-flex items-center justify-center p-2 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100"
        >
          <LogOut className="w-5 h-5" />
        </button>
      </div>
    );
  }

  // Not signed in - show sign in and sign up buttons
  return (
    <div className="flex items-center space-x-3">
      <Link to="/login">
        <button className="inline-flex items-center justify-center px-4 py-2 rounded-lg font-semibold transition-all duration-200 bg-white text-gray-800 border border-gray-200 shadow-sm hover:border-purple-600 hover:shadow-md">
          <User className="w-4 h-4 mr-2" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to="/signup">
        <button className="inline-flex items-center justify-center px-4 py-2 rounded-lg font-semibold transition-all duration-200 bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-sm hover:shadow-md hover:opacity-90">
          <span>Sign Up</span>
        </button>
      </Link>
    </div>
  );
}
