import express from 'express';
import { renderToString } from 'react-dom/server';
import { StaticRouter } from 'react-router-dom/server';
import { ServerStyleSheet } from 'styled-components';
import fs from 'fs';
import path from 'path';
import App from '../App';
import { supabase } from '../lib/supabase';
import { setupSitemapMiddleware } from './sitemap-middleware';

const app = express();
const PORT = process.env.PORT || 3000;
const distDir = path.resolve(__dirname, '../../dist');

// Serve static files
app.use(express.static(distDir));

// Setup sitemap middleware to ensure correct content type
setupSitemapMiddleware(app, distDir);

// Handle API routes
app.use('/api', (req, res, next) => {
  // Forward to API handlers
  next();
});

// Server-side rendering middleware
app.get('*', async (req, res) => {
  try {
    // Get the HTML template
    const template = fs.readFileSync(
      path.resolve(__dirname, '../../dist/index.html'),
      'utf8'
    );

    // Create a styled-components sheet
    const sheet = new ServerStyleSheet();

    // Initial state for hydration
    const initialState = {
      venues: [],
      user: null
    };

    // Fetch data based on the route
    if (req.url.startsWith('/venue/')) {
      const venueId = req.url.split('/').pop();
      const { data: venue } = await supabase
        .from('venues')
        .select('*')
        .eq('id', venueId)
        .single();

      if (venue) {
        initialState.venues = [venue];
      }
    } else if (req.url === '/' || req.url === '/find-venues') {
      const { data: venues } = await supabase
        .from('venues')
        .select('*')
        .limit(12);

      if (venues) {
        initialState.venues = venues;
      }
    }

    // Render the app to string
    const appHtml = renderToString(
      sheet.collectStyles(
        <StaticRouter location={req.url}>
          <App />
        </StaticRouter>
      )
    );

    // Get the styled-components styles
    const styleTags = sheet.getStyleTags();

    // Inject the app HTML, styles, and initial state into the template
    const html = template
      .replace('<div id="root"></div>', `<div id="root">${appHtml}</div>`)
      .replace('</head>', `${styleTags}</head>`)
      .replace(
        '<script id="initial-state"></script>',
        `<script id="initial-state">window.__INITIAL_STATE__ = ${JSON.stringify(
          initialState
        )};</script>`
      );

    // Send the rendered HTML
    res.send(html);
  } catch (error) {
    console.error('Error rendering app:', error);
    res.status(500).send('Server error');
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
