import React from 'react';
import { Link } from 'react-router-dom';
import { User } from 'lucide-react';
import { useSupabase } from '../../providers/SupabaseProvider';

interface HeaderMobileAuthStatusProps {
  variant?: 'default' | 'owner';
}

export default function HeaderMobileAuthStatus({ variant = 'default' }: HeaderMobileAuthStatusProps) {
  const { isAuthenticated, isLoading } = useSupabase();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center mr-3">
        <div className="h-8 w-20 bg-gray-100 rounded-full animate-pulse"></div>
      </div>
    );
  }

  // Determine the login/account URLs based on the variant
  const loginUrl = variant === 'owner' ? '/host/login?userType=host' : '/login?userType=guest';
  const accountUrl = variant === 'owner' ? '/host/dashboard' : '/my-account';
  const accountLabel = variant === 'owner' ? 'Dashboard' : 'Account';

  // Show authenticated state
  if (isAuthenticated) {
    return (
      <div className="flex items-center mr-3">
        <Link to={accountUrl} className="flex items-center">
          <div className="bg-purple-100 text-purple-800 rounded-full h-8 w-8 flex items-center justify-center font-medium mr-2 shadow-sm">
            <User className="w-4 h-4" />
          </div>
          <span className="text-sm text-purple-700 font-medium">
            {accountLabel}
          </span>
        </Link>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className="flex items-center mr-3">
      <Link to={loginUrl} className="flex items-center px-3 py-1.5 bg-purple-50 rounded-full border border-purple-100 shadow-sm">
        <User className="w-4 h-4 mr-1.5 text-purple-600" />
        <span className="text-sm text-purple-700 font-medium">
          Sign In
        </span>
      </Link>
    </div>
  );
}
