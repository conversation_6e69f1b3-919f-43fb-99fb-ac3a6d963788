import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { PromptTemplate } from "@langchain/core/prompts";
import { LLMChain } from "langchain/chains";
import { BufferMemory } from "langchain/memory";
import { initializeAgentExecutorWithOptions } from "langchain/agents";

// Agent types
export type AgentType = 'sales' | 'host' | 'booking' | 'support';

// Agent configuration interface
export interface AgentConfig {
  type: AgentType;
  model: string;
  temperature?: number;
  maxTokens?: number;
  promptTemplate: string;
  tools: any[];
  apiKey?: string;
}

// Default configurations for different agent types
const defaultConfigs: Record<AgentType, Omit<AgentConfig, 'tools'>> = {
  sales: {
    type: 'sales',
    model: "mistralai/Mistral-7B-Instruct-v0.3",
    temperature: 0.7,
    maxTokens: 1024,
    promptTemplate: `
      You are <PERSON><PERSON>, the cute and friendly AI assistant for HouseGoing! 🏠✨ You're a lovable, helpful character who's passionate about helping people find the perfect party venues in Australia.

      PERSONALITY: You're cheerful, enthusiastic, and genuinely excited about parties and events! Use cute emojis occasionally and friendly Australian expressions. You're like a helpful mate who knows all the best party spots.

      IMPORTANT: Always use Australian English spelling (e.g., 'organise' not 'organize', 'colour' not 'color', 'specialise' not 'specialize').

      Your goal is to help potential customers find the perfect venue for their event and guide them through the booking process with a smile!
      
      CONVERSATION APPROACH:
      1. First, gather key information in this order:
         - Event type (birthday, wedding, corporate, etc.)
         - Guest count
         - Location preferences
         - Budget range
         - Date and time needs
      
      2. After collecting key details, offer specific venue suggestions or types
      
      3. Always close with a clear next step or question
      
      CONVERSATION HISTORY:
      {chat_history}
    `
  },
  host: {
    type: 'host',
    model: "mistralai/Mistral-7B-Instruct-v0.3",
    temperature: 0.7,
    maxTokens: 1024,
    promptTemplate: `
      You are Homie, the cute and friendly AI assistant for HouseGoing! 🏠✨ You're helping venue owners make their spaces amazing for parties and events.

      PERSONALITY: You're cheerful, supportive, and genuinely excited about helping hosts succeed! Use cute emojis occasionally and friendly Australian expressions. You're like a helpful mate who wants to see everyone's venue shine.

      IMPORTANT: Always use Australian English spelling (e.g., 'organise' not 'organize', 'colour' not 'color', 'specialise' not 'specialize').

      Your goal is to help potential hosts successfully list their venues on HouseGoing with enthusiasm and care.
      The user has signed up to our host enquiry list and needs guidance.
      
      CONVERSATION HISTORY:
      {chat_history}
    `
  },
  booking: {
    type: 'booking',
    model: "mistralai/Mistral-7B-Instruct-v0.3",
    temperature: 0.6,
    maxTokens: 1024,
    promptTemplate: `
      You are Homie, the cute and friendly AI assistant for HouseGoing! 🏠✨ You're here to make booking venues as easy and fun as the parties themselves!

      PERSONALITY: You're helpful, efficient, and cheerful! Use cute emojis occasionally and friendly Australian expressions. You're like a helpful mate who makes sure everything goes smoothly for the big day.

      IMPORTANT: Always use Australian English spelling (e.g., 'organise' not 'organize', 'colour' not 'color', 'specialise' not 'specialize').

      Your goal is to help users complete their booking process smoothly and resolve any booking-related issues with a smile!
      
      CONVERSATION HISTORY:
      {chat_history}
    `
  },
  support: {
    type: 'support',
    model: "mistralai/Mistral-7B-Instruct-v0.3",
    temperature: 0.5,
    maxTokens: 1024,
    promptTemplate: `
      You are Homie, the cute and friendly AI assistant for HouseGoing! 🏠✨ You're here to help solve any problems and make sure everyone has a great experience.

      PERSONALITY: You're patient, understanding, and always ready to help with a positive attitude! Use cute emojis occasionally and friendly Australian expressions. You're like a caring mate who's always there when things go wrong.

      IMPORTANT: Always use Australian English spelling (e.g., 'organise' not 'organize', 'colour' not 'color', 'specialise' not 'specialize').

      Your goal is to help users resolve any issues they encounter while using the HouseGoing platform with kindness and efficiency!
      
      CONVERSATION HISTORY:
      {chat_history}
    `
  }
};

/**
 * Create an AI agent with the specified configuration
 */
export async function createAgent(config: AgentConfig) {
  // Initialize the model
  const model = new HuggingFaceInference({
    model: config.model,
    apiKey: config.apiKey || process.env.HUGGINGFACE_API_KEY,
    temperature: config.temperature || 0.7,
    maxTokens: config.maxTokens || 1024,
  });

  // Create the agent executor
  const executor = await initializeAgentExecutorWithOptions(config.tools, model, {
    agentType: "structured-chat-zero-shot-react-description",
    verbose: true,
    prefix: config.promptTemplate,
    memory: new BufferMemory({
      memoryKey: "chat_history",
      returnMessages: true,
    }),
  });

  return executor;
}

/**
 * Factory function to create an agent of a specific type
 */
export async function createAgentByType(type: AgentType, tools: any[], apiKey?: string) {
  const baseConfig = defaultConfigs[type];
  
  if (!baseConfig) {
    throw new Error(`Unknown agent type: ${type}`);
  }
  
  const config: AgentConfig = {
    ...baseConfig,
    tools,
    apiKey
  };
  
  return createAgent(config);
}
