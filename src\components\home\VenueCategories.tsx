import { Music, Wine, Cake, PartyPopper } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

const categories = [
  { 
    name: "Wedding After-Parties", 
    icon: PartyPopper,
    description: "Keep the celebration going",
    features: ["Dance floor", "Bar service", "Late-night access"],
    path: "/category/afterparty"
  },
  { 
    name: "Cocktail Lounges", 
    icon: Wine,
    description: "Sophisticated party spaces",
    features: ["Full bar", "Lounge seating", "Ambient lighting"],
    path: "/category/lounge"
  },
  { 
    name: "Dance Venues", 
    icon: Music,
    description: "Perfect for dancing the night away",
    features: ["Pro sound system", "Dance floor", "DJ booth"],
    path: "/category/dance"
  },
  { 
    name: "Party Houses", 
    icon: Cake,
    description: "Private party locations",
    features: ["Kitchen access", "Indoor/outdoor space", "Sound system"],
    path: "/category/house"
  },
];

export function VenueCategories() {
  return (
    <div className="container mx-auto py-16 px-4">
      <h2 className="text-2xl font-semibold mb-8">Browse Party Spaces</h2>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {categories.map((category) => {
          const IconComponent = category.icon;
          return (
            <Link
              to={category.path}
              key={category.name}
              className="group block"
            >
              <div className="p-6 rounded-xl bg-white border hover:border-primary/50 transition-all duration-300 hover:shadow-lg h-full">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4 group-hover:bg-primary/20">
                  <IconComponent className="w-6 h-6 text-primary" />
                </div>
                <h3 className="font-semibold text-lg mb-2 group-hover:text-primary transition-colors">
                  {category.name}
                </h3>
                <p className="text-gray-600 text-sm mb-4">{category.description}</p>
                <ul className="space-y-2">
                  {category.features.map((feature, index) => (
                    <li key={index} className="text-sm text-gray-500 flex items-center">
                      <span className="w-1.5 h-1.5 bg-primary/60 rounded-full mr-2"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
}