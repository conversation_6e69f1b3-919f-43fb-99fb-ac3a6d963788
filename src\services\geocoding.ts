/**
 * Geocoding service using Maps.co API
 */

const API_KEY = '67ea5a7e84615836239135wpc5a6d73';
const BASE_URL = 'https://geocode.maps.co';

// Cache for geocoding results to reduce API calls
interface GeocodingCache {
  [key: string]: {
    lat: number;
    lng: number;
    displayName: string;
    timestamp: number;
  };
}

// Initialize cache
const forwardCache: GeocodingCache = {};
const reverseCache: GeocodingCache = {};

// Cache expiration time (24 hours in milliseconds)
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

/**
 * Convert address to coordinates (forward geocoding)
 */
export async function geocodeLocation(address: string): Promise<{ lat: number; lng: number; displayName: string }> {
  // Check cache first
  const cacheKey = address.toLowerCase().trim();
  const cachedResult = forwardCache[cacheKey];

  if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRATION) {
    return {
      lat: cachedResult.lat,
      lng: cachedResult.lng,
      displayName: cachedResult.displayName
    };
  }

  try {
    // Add NSW, Australia to the search query if not already specified
    let searchQuery = address;
    if (!searchQuery.toLowerCase().includes('nsw') && !searchQuery.toLowerCase().includes('new south wales')) {
      searchQuery += ', NSW, Australia';
    } else if (!searchQuery.toLowerCase().includes('australia')) {
      searchQuery += ', Australia';
    }

    const response = await fetch(
      `${BASE_URL}/search?q=${encodeURIComponent(searchQuery)}&api_key=${API_KEY}`
    );

    if (!response.ok) {
      throw new Error(`Geocoding API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data || data.length === 0) {
      throw new Error('No results found for this address');
    }

    // Filter results to prioritize NSW locations
    const nswResults = data.filter(item =>
      item.display_name.toLowerCase().includes('nsw') ||
      item.display_name.toLowerCase().includes('new south wales')
    );

    // Use NSW result if available, otherwise use the first result
    const bestResult = nswResults.length > 0 ? nswResults[0] : data[0];

    const result = {
      lat: parseFloat(bestResult.lat),
      lng: parseFloat(bestResult.lon),
      displayName: bestResult.display_name
    };

    // Cache the result
    forwardCache[cacheKey] = {
      ...result,
      timestamp: Date.now()
    };

    return result;
  } catch (error) {
    console.error('Geocoding error:', error);
    throw new Error('Failed to geocode address. Please try again.');
  }
}

/**
 * Convert coordinates to address (reverse geocoding)
 */
export async function reverseGeocode(lat: number, lng: number): Promise<string> {
  // Check cache first
  const cacheKey = `${lat.toFixed(6)},${lng.toFixed(6)}`;
  const cachedResult = reverseCache[cacheKey];

  if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRATION) {
    return cachedResult.displayName;
  }

  try {
    const response = await fetch(
      `${BASE_URL}/reverse?lat=${lat}&lon=${lng}&api_key=${API_KEY}`
    );

    if (!response.ok) {
      throw new Error(`Reverse geocoding API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data || !data.display_name) {
      throw new Error('No results found for these coordinates');
    }

    const displayName = data.display_name;

    // Cache the result
    reverseCache[cacheKey] = {
      lat,
      lng,
      displayName,
      timestamp: Date.now()
    };

    return displayName;
  } catch (error) {
    console.error('Reverse geocoding error:', error);
    // Return a formatted fallback for display
    return `${lat.toFixed(2)}, ${lng.toFixed(2)}`;
  }
}

/**
 * Get a simplified location name from a full address
 * This extracts just the locality/suburb and state
 */
export function simplifyLocationName(fullAddress: string): string {
  if (!fullAddress) return '';

  // Try to extract suburb and state from the full address
  // Example: "123 Main St, Sydney, NSW 2000, Australia" -> "Sydney, NSW"
  const parts = fullAddress.split(',').map(part => part.trim());

  if (parts.length >= 2) {
    // Look for NSW in the address parts
    const stateIndex = parts.findIndex(part =>
      /^(NSW)(\s+\d{4})?$/i.test(part.trim())
    );

    if (stateIndex > 0) {
      // Get the suburb (part before the state) and the state
      const suburb = parts[stateIndex - 1];
      return `${suburb}, NSW`;
    }
  }

  // If we can't find NSW specifically, look for any location in the address
  // and append NSW to it
  if (parts.length >= 1) {
    // Try to find a part that looks like a suburb or city (not a street number or country)
    const potentialSuburb = parts.find(part =>
      !part.match(/^\d+/) && // Not starting with a number
      !part.match(/Australia/i) && // Not the country
      !part.match(/^\d{4}$/) // Not a postal code
    );

    if (potentialSuburb) {
      return `${potentialSuburb}, NSW`;
    }
  }

  // Last resort: return the full address or a default
  return fullAddress || 'NSW, Australia';
}

/**
 * Pre-cached popular NSW locations to reduce API calls
 */
export const popularNSWLocations = [
  {
    name: 'Sydney CBD',
    lat: -33.8688,
    lng: 151.2093,
    displayName: 'Sydney, NSW'
  },
  {
    name: 'Bondi Beach',
    lat: -33.8915,
    lng: 151.2767,
    displayName: 'Bondi Beach, NSW'
  },
  {
    name: 'Manly',
    lat: -33.7969,
    lng: 151.2502,
    displayName: 'Manly, NSW'
  },
  {
    name: 'Parramatta',
    lat: -33.8150,
    lng: 151.0011,
    displayName: 'Parramatta, NSW'
  },
  {
    name: 'Chatswood',
    lat: -33.7967,
    lng: 151.1800,
    displayName: 'Chatswood, NSW'
  },
  {
    name: 'North Sydney',
    lat: -33.8389,
    lng: 151.2071,
    displayName: 'North Sydney, NSW'
  },
  {
    name: 'Cronulla',
    lat: -34.0581,
    lng: 151.1543,
    displayName: 'Cronulla, NSW'
  },
  {
    name: 'Newcastle',
    lat: -32.9283,
    lng: 151.7817,
    displayName: 'Newcastle, NSW'
  },
  {
    name: 'Wollongong',
    lat: -34.4278,
    lng: 150.8931,
    displayName: 'Wollongong, NSW'
  },
  {
    name: 'Byron Bay',
    lat: -28.6474,
    lng: 153.6020,
    displayName: 'Byron Bay, NSW'
  }
];

// Pre-populate the cache with popular locations
popularNSWLocations.forEach(location => {
  const forwardKey = location.name.toLowerCase().trim();
  forwardCache[forwardKey] = {
    lat: location.lat,
    lng: location.lng,
    displayName: location.displayName,
    timestamp: Date.now()
  };

  const reverseKey = `${location.lat.toFixed(6)},${location.lng.toFixed(6)}`;
  reverseCache[reverseKey] = {
    lat: location.lat,
    lng: location.lng,
    displayName: location.displayName,
    timestamp: Date.now()
  };
});
