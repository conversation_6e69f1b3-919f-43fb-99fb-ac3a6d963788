import React, { useState } from 'react';
import { Sliders, Home, Users, DollarSign, Calendar, Search, MapPin } from 'lucide-react';
import Button from '../ui/Button';
import LocationRadiusFilter from '../search/LocationRadiusFilter';

interface FilterProps {
  onFilterChange: (filters: any) => void;
  selectedLocation?: { lat: number; lng: number; displayName: string } | null;
}

export default function VenueFilters({ onFilterChange, selectedLocation }: FilterProps) {
  const [filters, setFilters] = useState({
    eventTypes: [] as string[],
    priceRange: '',
    amenities: [] as string[],
    instantBook: false,
    guestRange: '',
    dates: { start: '', end: '' },
    locationRadius: 10
  });

  const eventTypes = ['Wedding', 'Birthday', 'Corporate', 'Social'];
  const amenities = ['WiFi', 'Kitchen', 'Parking', 'Sound System', 'Catering'];
  const priceRanges = ['500-1000', '1000-1500', '1500-2000', '2000+']; // Updated to match actual venue prices
  const guestRanges = ['1-20', '21-50', '51-100', '100+'];

  const handleChange = (type: string, value: any) => {
    setFilters(prev => {
      const newFilters = { ...prev, [type]: value };
      onFilterChange(newFilters);
      return newFilters;
    });
  };

  const handleCheckboxChange = (type: string, value: string) => {
    setFilters(prev => {
      const list = prev[type as keyof typeof prev] as string[];
      const newList = list.includes(value)
        ? list.filter(item => item !== value)
        : [...list, value];

      const newFilters = { ...prev, [type]: newList };
      onFilterChange(newFilters);
      return newFilters;
    });
  };

  const clearFilters = () => {
    const emptyFilters = {
      eventTypes: [],
      priceRange: '',
      amenities: [],
      instantBook: false,
      guestRange: '',
      dates: { start: '', end: '' },
      locationRadius: 10
    };
    setFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Sliders className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold text-gray-900">Filters</h3>
        </div>
        <button
          onClick={clearFilters}
          className="text-sm text-purple-600 hover:text-purple-700"
        >
          Clear all
        </button>
      </div>

      <div className="space-y-6">
        {/* Location Radius Filter */}
        {selectedLocation && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="h-4 w-4 inline mr-1" />
              Search Radius
            </label>
            <div className="text-xs text-gray-500 mb-2">
              From {selectedLocation.displayName}
            </div>
            <LocationRadiusFilter
              selectedRadius={filters.locationRadius}
              onRadiusChange={(radius) => handleChange('locationRadius', radius)}
              disabled={!selectedLocation}
            />
          </div>
        )}

        <div className={selectedLocation ? "border-t pt-4" : ""}>
          <label className="block text-sm font-medium text-gray-700 mb-2">Event Type</label>
          <div className="grid grid-cols-2 gap-2">
            {eventTypes.map((type) => (
              <label key={type} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.eventTypes.includes(type)}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  onChange={() => handleCheckboxChange('eventTypes', type)}
                />
                <span className="ml-2 text-sm text-gray-600">{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="border-t pt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Guest Count</label>
          <select
            value={filters.guestRange}
            onChange={(e) => handleChange('guestRange', e.target.value)}
            className="w-full rounded-lg border-gray-300 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Any size</option>
            {guestRanges.map(range => (
              <option key={range} value={range}>{range} guests</option>
            ))}
          </select>
        </div>

        <div className="border-t pt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
          <div className="space-y-2">
            {priceRanges.map((range) => (
              <label key={range} className="flex items-center">
                <input
                  type="radio"
                  name="price"
                  checked={filters.priceRange === range}
                  className="border-gray-300 text-purple-600 focus:ring-purple-500"
                  onChange={() => handleChange('priceRange', range)}
                />
                <span className="ml-2 text-sm text-gray-600">${range}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="border-t pt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Amenities</label>
          <div className="grid grid-cols-2 gap-2">
            {amenities.map((amenity) => (
              <label key={amenity} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.amenities.includes(amenity)}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  onChange={() => handleCheckboxChange('amenities', amenity)}
                />
                <span className="ml-2 text-sm text-gray-600">{amenity}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="border-t pt-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.instantBook}
              onChange={(e) => handleChange('instantBook', e.target.checked)}
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <span className="ml-2 text-sm text-gray-600">Instant Book only</span>
          </label>
        </div>

        <Button
          variant="primary"
          fullWidth
          className="mt-6"
          onClick={() => onFilterChange(filters)}
        >
          <Search className="h-4 w-4 mr-2" />
          Apply Filters
        </Button>
      </div>
    </div>
  );
}