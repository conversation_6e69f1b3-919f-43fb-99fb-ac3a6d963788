import React from 'react';
import { Link } from 'react-router-dom';
import { X, LogOut, User, ArrowRight } from 'lucide-react';
import { useSupabase } from '../../providers/SupabaseProvider';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath: string;
}

export default function MobileMenu({ isOpen, onClose, currentPath }: MobileMenuProps) {
  const { isAuthenticated, userProfile, isLoading, signOut } = useSupabase();

  // Don't render anything if the menu is not open
  if (!isOpen) return null;

  // Define menu items at the top of the component
  const menuItems = [
    { path: '/find-venues', label: 'Find Venues', className: '' },
    { path: '/venue-guide', label: 'Venue Guide', className: '' },
    { path: '/nsw-party-planning', label: 'NSW Party Planning', className: '' },
  ];

  // Quick links (moved from main nav)
  const quickLinks = [
    { path: '/how-it-works', label: 'How It Works', className: 'text-gray-500 text-sm' },
    { path: '/venue-assistant', label: 'Venue Assistant', className: 'text-gray-500 text-sm' },
    { path: '/host/portal', label: 'Owner Portal', className: 'text-gray-500 text-sm' },
  ];

  // Log when menu is opened
  React.useEffect(() => {
    if (isOpen) {
      console.log('Mobile menu opened - Menu items:', menuItems);
    }
  }, [isOpen, menuItems]);

  return (
    <div className="fixed inset-0 bg-gray-900/60 backdrop-blur-sm z-50 mobile-menu animate-fade-in">
      <div className="fixed inset-y-0 right-0 w-full max-w-sm bg-white shadow-2xl mobile-menu-content animate-slide-in-right">
        <div className="p-6 flex justify-between items-center border-b border-gray-100 min-h-[72px]">
          <h2 className="font-bold text-xl text-gray-900">Menu</h2>
          <button
            onClick={onClose}
            className="btn-reactive focus-enhanced p-3 rounded-xl hover:bg-gray-100 touch-target min-h-[48px] min-w-[48px] flex items-center justify-center"
            aria-label="Close menu"
          >
            <X className="h-6 w-6 text-gray-600" />
          </button>
        </div>
        <nav className="p-6 section-mobile">
          <div className="space-y-2">
            {menuItems.map(({ path, label, className }) => (
              <Link
                key={path}
                to={path}
                className={`nav-link btn-reactive focus-enhanced block py-4 px-5 rounded-xl mb-1 min-h-[56px] flex items-center text-lg font-semibold touch-target transition-all duration-200 ${
                  currentPath === path
                    ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg transform scale-[1.02]'
                    : className || 'text-gray-700 hover:bg-purple-50 hover:text-purple-600 active:bg-purple-100 hover:transform hover:scale-[1.01]'
                } ${className || ''}`}
                onClick={onClose}
              >
                <span className="flex-1">{label}</span>
                <ArrowRight className="h-5 w-5 opacity-60" />
              </Link>
            ))}
          </div>

          {/* Quick Links Section */}
          <div className="border-t border-gray-100 my-6 pt-6">
            <h3 className="text-sm font-bold text-gray-500 uppercase tracking-wider mb-4 px-2">Quick Access</h3>
            <div className="space-y-1">
              {quickLinks.map(({ path, label, className }) => (
                <Link
                  key={path}
                  to={path}
                  className={`nav-link btn-reactive focus-enhanced block py-3 px-4 rounded-lg min-h-[48px] flex items-center text-base touch-target transition-all duration-200 ${
                    currentPath === path
                      ? 'bg-purple-50 text-purple-600 font-semibold'
                      : className || 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 active:bg-purple-50'
                  } ${className || ''}`}
                  onClick={onClose}
                >
                  <span className="flex-1">{label}</span>
                  <ArrowRight className="h-4 w-4 opacity-40" />
                </Link>
              ))}
            </div>
          </div>

          <div className="border-t border-gray-200 my-4 pt-4">
            {isLoading ? (
              <div className="py-2 px-4">
                <div className="animate-pulse bg-gray-200 h-8 w-full rounded mb-2"></div>
                <div className="animate-pulse bg-gray-200 h-8 w-full rounded"></div>
              </div>
            ) : isAuthenticated && userProfile ? (
              <>
                <div className="py-2 px-4 text-gray-700 font-medium">
                  {userProfile.first_name || userProfile.email}
                </div>
                <Link
                  to="/my-account"
                  className="flex items-center py-2 px-4 rounded-lg mb-2 text-gray-600 hover:bg-gray-50"
                  onClick={onClose}
                >
                  <User className="w-4 h-4 mr-2" />
                  My Account
                </Link>
                <button
                  onClick={() => {
                    signOut();
                    onClose();
                  }}
                  className="flex items-center w-full text-left py-2 px-4 rounded-lg mb-2 text-purple-600 hover:bg-purple-50"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </button>
              </>
            ) : (
              <>
                <Link
                  to="/login"
                  className="flex items-center py-2 px-4 rounded-lg mb-2 text-gray-600 hover:bg-gray-50"
                  onClick={onClose}
                >
                  <User className="w-4 h-4 mr-2" />
                  Sign In
                </Link>
                <Link
                  to="/signup"
                  className="block py-2 px-4 rounded-lg mb-2 text-purple-600 hover:bg-purple-50"
                  onClick={onClose}
                >
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </nav>
      </div>
    </div>
  );
}