/**
 * API Server for the Sales Assistant
 * 
 * This file provides an Express server that exposes the Sales Assistant
 * as a REST API that can be integrated with the HouseGoing website.
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { LL<PERSON>hain } from "langchain/chains";
import { salesAssistantPrompt } from './prompt.js';
import { getSessionMemory, clearSessionMemory } from './memory.js';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Configure LangSmith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;

// Initialize Express app
const app = express();
app.use(cors());
app.use(express.json());

// Initialize the Hugging Face model
const model = new HuggingFaceInference({
  model: "mistralai/Mistral-7B-Instruct-v0.3",
  apiKey: process.env.HUGGINGFACE_API_KEY,
  temperature: 0.7,
  maxTokens: 1024,
});

// Store assistant instances for each session
const assistantInstances = new Map();

/**
 * Get or create a Sales Assistant instance for a session
 * @param {string} sessionId - Session identifier
 * @returns {LLMChain} Sales Assistant chain
 */
function getAssistantForSession(sessionId) {
  if (!assistantInstances.has(sessionId)) {
    const memory = getSessionMemory(sessionId);
    
    const chain = new LLMChain({
      llm: model,
      prompt: salesAssistantPrompt,
      memory: memory,
      outputKey: "output",
    });
    
    assistantInstances.set(sessionId, chain);
  }
  
  return assistantInstances.get(sessionId);
}

/**
 * Generate a session ID if one is not provided
 * @param {Object} req - Express request object
 * @returns {string} Session ID
 */
function getOrCreateSessionId(req) {
  // Check if session ID is provided in the request
  let sessionId = req.body.sessionId || req.query.sessionId;
  
  // If no session ID is provided, generate a new one
  if (!sessionId) {
    sessionId = crypto.randomUUID();
  }
  
  return sessionId;
}

/**
 * Chat endpoint - Send a message to the Sales Assistant
 */
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    const sessionId = getOrCreateSessionId(req);
    
    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }
    
    // Get or create assistant for this session
    const assistant = getAssistantForSession(sessionId);
    
    // Process the message
    console.log(`Processing message from session ${sessionId}: ${message}`);
    const response = await assistant.call({ input: message });
    
    res.json({
      response: response.output,
      sessionId: sessionId
    });
  } catch (error) {
    console.error("Error processing message:", error);
    res.status(500).json({
      error: "Failed to process message",
      details: error.message
    });
  }
});

/**
 * Reset conversation endpoint - Clear conversation history
 */
app.post('/api/reset', async (req, res) => {
  try {
    const sessionId = getOrCreateSessionId(req);
    
    // Remove the assistant instance and clear memory
    if (assistantInstances.has(sessionId)) {
      assistantInstances.delete(sessionId);
    }
    
    clearSessionMemory(sessionId);
    
    res.json({
      success: true,
      message: "Conversation reset successfully",
      sessionId: sessionId
    });
  } catch (error) {
    console.error("Error resetting conversation:", error);
    res.status(500).json({
      error: "Failed to reset conversation",
      details: error.message
    });
  }
});

/**
 * Health check endpoint
 */
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    version: '1.0.0'
  });
});

// Start the server
const PORT = process.env.PORT || 3002;

function startServer() {
  app.listen(PORT, () => {
    console.log(`Sales Assistant API running on port ${PORT}`);
  });
}

// If this file is run directly, start the server
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export {
  app,
  startServer
};
