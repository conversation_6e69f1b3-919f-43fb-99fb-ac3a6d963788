# Environment variables
.env

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Mac files
.DS_Store

# Windows files
Thumbs.db
ehthumbs.db
Desktop.ini
