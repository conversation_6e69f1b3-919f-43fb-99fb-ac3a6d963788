import React from 'react';
import { Calendar, Users } from 'lucide-react';
import { formatDate } from '../../utils/dates';
import { useBookings } from '../../hooks/useBookings';

export default function BookingsList() {
  const { bookings, isLoading } = useBookings();

  if (isLoading) {
    return <div>Loading bookings...</div>;
  }

  return (
    <div className="space-y-4">
      {bookings?.map((booking) => (
        <div key={booking.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <img
              src={booking.venue.images[0]}
              alt={booking.venue.title}
              className="w-16 h-16 rounded-lg object-cover"
            />
            <div>
              <h3 className="font-semibold">{booking.venue.title}</h3>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(booking.date)}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Users className="w-4 h-4 mr-1" />
                {booking.guests} guests
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="font-semibold">${booking.totalPrice}</div>
            <div className="text-sm text-gray-600">{booking.status}</div>
          </div>
        </div>
      ))}
    </div>
  );
}