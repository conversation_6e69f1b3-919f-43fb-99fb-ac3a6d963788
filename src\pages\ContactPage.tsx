import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Mail, Phone, Clock, MapPin, AlertCircle, ExternalLink } from 'lucide-react';

const ContactPage: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Contact Us | HouseGoing</title>
        <meta name="description" content="Contact HouseGoing for venue rental inquiries, support, or complaints. We comply with Australian Consumer Law and ACMA requirements." />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://housegoing.com.au/contact" />
        
        {/* Open Graph */}
        <meta property="og:title" content="Contact Us | HouseGoing" />
        <meta property="og:description" content="Contact HouseGoing for venue rental inquiries, support, or complaints. We comply with Australian Consumer Law and ACMA requirements." />
        <meta property="og:url" content="https://housegoing.com.au/contact" />
        <meta property="og:type" content="website" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 pt-24 pb-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Contact HouseGoing</h1>
            <p className="text-lg text-gray-600">
              Get in touch with our team for support, inquiries, or feedback
            </p>
          </div>

          {/* Quick Contact Form - Moved to Top */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Quick Contact</h2>
            <form className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                  Subject
                </label>
                <select
                  id="subject"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="">Select a topic</option>
                  <option value="general">General Inquiry</option>
                  <option value="booking">Booking Support</option>
                  <option value="host">Host Support</option>
                  <option value="complaint">Complaint</option>
                </select>
              </div>
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <textarea
                  id="message"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="How can we help you?"
                ></textarea>
              </div>
              <button
                type="submit"
                className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
              >
                Send Message
              </button>
            </form>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            {/* General Support */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-2xl font-semibold mb-4 flex items-center">
                <Mail className="h-6 w-6 text-purple-600 mr-2" />
                General Support
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <Mail className="h-4 w-4 mr-2" />
                    Customer Support
                  </h3>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">For all customer inquiries and support</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Response Time
                  </h3>
                  <p className="text-gray-600">Within 24 hours</p>
                  <p className="text-sm text-gray-500">Monday - Friday business days</p>
                </div>
              </div>
            </div>

            {/* Complaints & Disputes */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-2xl font-semibold mb-4 flex items-center">
                <AlertCircle className="h-6 w-6 text-red-600 mr-2" />
                Complaints & Disputes
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Complaints</h3>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">We aim to respond within 2 business days</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">External Dispute Resolution</h3>
                  <p className="text-gray-600">NSW Fair Trading</p>
                  <p className="text-gray-600">Phone: 13 32 20</p>
                  <p className="text-gray-600">Website: fairtrading.nsw.gov.au</p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Privacy Complaints</h3>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">For Privacy Act 1988 related concerns</p>
                </div>
              </div>
            </div>
          </div>

          {/* Specialized Contact */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4">Specialized Contact</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Venue Owners</h3>
                <p className="text-gray-600"><EMAIL></p>
                <p className="text-sm text-gray-500">Listing support, payments, policies</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Legal & Compliance</h3>
                <p className="text-gray-600"><EMAIL></p>
                <p className="text-sm text-gray-500">Legal notices, compliance matters</p>
              </div>
            </div>
          </div>

          {/* Consumer Rights */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-yellow-900 mb-2">Consumer Rights</h2>
            <p className="text-yellow-800 mb-4">
              Under Australian Consumer Law, you have rights that cannot be excluded. 
              These include the right to a refund, replacement, or compensation for major failures.
            </p>
            <ul className="list-disc list-inside text-yellow-800 space-y-1">
              <li>Right to receive services with due care and skill</li>
              <li>Right to receive services fit for purpose</li>
              <li>Right to receive services within reasonable time</li>
              <li>Right to seek remedies for misleading or deceptive conduct</li>
            </ul>
            <p className="text-sm text-yellow-700 mt-4">
              For more information, visit: 
              <a href="https://www.accc.gov.au/consumers" className="underline ml-1" target="_blank" rel="noopener noreferrer">
                accc.gov.au/consumers
                <ExternalLink className="h-3 w-3 inline ml-1" />
              </a>
            </p>
          </div>



          {/* Legal Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              <strong>Last Updated:</strong> January 6, 2025<br />
              <strong>Effective Date:</strong> January 6, 2025<br />
              <strong>Governing Law:</strong> New South Wales, Australia
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactPage;
