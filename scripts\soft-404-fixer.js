/**
 * soft-404-fixer.js
 * 
 * This script identifies and helps fix "soft 404" issues that Google Search Console detected.
 * It scans pages with minimal content and suggests fixes.
 * 
 * Usage:
 * 1. Run with Node.js: node soft-404-fixer.js
 * 2. Review the generated report and implement suggested fixes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { globSync } from 'glob';
import * as cheerio from 'cheerio';

// Get current file directory with ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');
const REPORT_FILE = path.join(__dirname, '..', 'soft-404-fix-report.md');

// Content length threshold (characters) below which a page might be considered thin
const CONTENT_THRESHOLD = 300;

// Load the sitemap to get list of all URLs
console.log(`Scanning for potential soft 404 pages...`);

// Get all HTML files
const htmlFiles = globSync(`${DIST_DIR}/**/*.html`);

let report = `# Soft 404 Fix Report\n\n`;
report += `Generated on: ${new Date().toISOString()}\n\n`;
report += `## Pages with Potential Soft 404 Issues\n\n`;

const potentialSoft404s = [];

// Analyze each HTML file
htmlFiles.forEach(file => {
  const relPath = path.relative(DIST_DIR, file);
  const html = fs.readFileSync(file, 'utf-8');
  const $ = cheerio.load(html);
  
  // Extract main content (exclude navigation, footer, etc.)
  const mainContent = $('main').text() || $('body').text();
  const cleanContent = mainContent.replace(/\s+/g, ' ').trim();
  
  // Check if content is too thin
  if (cleanContent.length < CONTENT_THRESHOLD) {
    const title = $('title').text() || 'Unknown';
    
    potentialSoft404s.push({
      file: relPath,
      title,
      contentLength: cleanContent.length,
      snippet: cleanContent.substring(0, 100) + '...'
    });
  }
});

// Sort by content length (ascending)
potentialSoft404s.sort((a, b) => a.contentLength - b.contentLength);

// Add to report
if (potentialSoft404s.length === 0) {
  report += `No potential soft 404 pages found.\n\n`;
} else {
  report += `Found ${potentialSoft404s.length} potential soft 404 pages:\n\n`;
  
  potentialSoft404s.forEach(page => {
    report += `### ${page.title}\n`;
    report += `- **File:** \`${page.file}\`\n`;
    report += `- **Content Length:** ${page.contentLength} characters\n`;
    report += `- **Content Snippet:** "${page.snippet}"\n\n`;
    report += `**Recommended Action:**\n`;
    
    // Suggest appropriate action based on page
    if (page.contentLength < 100) {
      report += `- This page has extremely thin content. Consider:\n`;
      report += `  - Implementing a proper 404 page if this URL doesn't need to exist\n`;
      report += `  - Adding substantial unique content (at least 500 words)\n`;
      report += `  - Setting up a 301 redirect to a relevant page\n`;
    } else {
      report += `- This page has minimal content. Consider:\n`;
      report += `  - Expanding content with useful information\n`;
      report += `  - Adding images, videos, or interactive elements\n`;
      report += `  - Making sure it has a unique meta title and description\n`;
    }
    
    report += `\n---\n\n`;
  });
}

// Add next steps
report += `## Next Steps\n\n`;
report += `1. Review each page listed above\n`;
report += `2. For pages that should exist: add substantial unique content\n`;
report += `3. For pages that shouldn't exist: implement proper 404 status codes or 301 redirects\n`;
report += `4. After fixing, submit URLs for reindexing in Google Search Console\n`;

fs.writeFileSync(REPORT_FILE, report);

console.log(`
✅ Soft 404 analysis complete!
   - ${potentialSoft404s.length} potential soft 404 pages identified
   - Full report saved to ${REPORT_FILE}
`);
