import React from 'react';
import { AlertCircle } from 'lucide-react';

interface FormErrorProps {
  error: string | null | Error;
  className?: string;
}

export default function FormError({ error, className = '' }: FormErrorProps) {
  if (!error) return null;
  
  const errorMessage = error instanceof Error ? error.message : error;
  
  return (
    <div className={`flex items-start p-3 mt-2 text-sm bg-red-50 border border-red-200 rounded-md text-red-600 ${className}`}>
      <AlertCircle className="w-4 h-4 mt-0.5 mr-2 flex-shrink-0" />
      <span>{errorMessage}</span>
    </div>
  );
}
