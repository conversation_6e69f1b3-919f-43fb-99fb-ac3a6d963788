import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSupabase } from '../providers/SupabaseProvider';
import { useUser } from '@clerk/clerk-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export default function ProtectedRoute({ children, redirectTo = '/login' }: ProtectedRouteProps) {
  const location = useLocation();

  // Check if we're in development mode first
  const isDevelopmentMode = typeof window !== 'undefined' &&
                           (window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('local'));

  // In development mode, bypass all authentication checks immediately
  if (isDevelopmentMode) {
    console.log('🔧 ProtectedRoute: Development mode - bypassing all authentication checks');
    return <>{children}</>;
  }

  // Production mode: use normal authentication flow
  const { isAuthenticated, isLoading: isSupabaseLoading } = useSupabase();
  const { user: clerkUser, isLoaded: isClerkLoaded, isSignedIn } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [localAuthenticated, setLocalAuthenticated] = useState(false);

  useEffect(() => {
    // Check for authentication from multiple sources
    const checkAuth = () => {
      // Check if auth_success flag is set as a fallback
      const hasAuthSuccess = localStorage.getItem('auth_success') === 'true';
      const hasClerkUser = clerkUser !== null && clerkUser !== undefined;
      const hasClerkId = localStorage.getItem('clerk_user_id') !== null;

      // Determine if user is authenticated from any source
      const isUserAuthenticated = isAuthenticated || isSignedIn || hasAuthSuccess || hasClerkUser || hasClerkId;

      console.log('ProtectedRoute auth check:', {
        path: location.pathname,
        isAuthenticated,
        isSignedIn,
        hasAuthSuccess,
        hasClerkUser,
        hasClerkId,
        isUserAuthenticated
      });

      setLocalAuthenticated(isUserAuthenticated);
      setIsLoading(false);
    };

    // Check auth when both Supabase and Clerk have loaded
    if (!isSupabaseLoading && isClerkLoaded) {
      checkAuth();
    }
  }, [isAuthenticated, isSignedIn, isSupabaseLoading, isClerkLoaded, clerkUser, location.pathname]);

  // Also listen for auth_complete events
  useEffect(() => {
    const handleAuthComplete = (event: Event) => {
      console.log('Auth complete event detected in ProtectedRoute', event);
      setLocalAuthenticated(true);
      setIsLoading(false);
    };

    window.addEventListener('auth_complete', handleAuthComplete);

    return () => {
      window.removeEventListener('auth_complete', handleAuthComplete);
    };
  }, []);

  if (isLoading) {
    return <div className="pt-32 flex justify-center">
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
        <p className="text-gray-600">Loading authentication...</p>
      </div>
    </div>;
  }

  // Check if the user is authenticated from any source
  const finalAuthState = isAuthenticated || isSignedIn || localAuthenticated || clerkUser !== null;

  if (!finalAuthState) {
    // Store the current path to redirect back after login
    try {
      localStorage.setItem('auth_redirect_to', window.location.pathname);
    } catch (e) {
      console.error('Error storing redirect path:', e);
    }

    console.log('ProtectedRoute redirecting to login:', {
      path: location.pathname,
      redirectTo,
      isAuthenticated,
      isSignedIn,
      localAuthenticated,
      hasClerkUser: clerkUser !== null
    });

    // Add userType parameter to the redirect URL
    const redirectToWithParams = redirectTo.includes('?')
      ? `${redirectTo}&userType=guest`
      : `${redirectTo}?userType=guest`;

    return <Navigate to={redirectToWithParams} />;
  }

  return <>{children}</>;
}