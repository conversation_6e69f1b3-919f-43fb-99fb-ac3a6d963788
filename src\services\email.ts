// Mock email service for HouseGoing
// This is a placeholder until a real email service is integrated

// Commented out until Supabase API is properly configured
// import { supabase } from './api';

/**
 * Get email templates from site settings
 */
async function getEmailTemplates() {
  // Return default templates without querying Supabase
  return {
    enableEmailNotifications: true,
    senderName: 'HouseGoing',
    senderEmail: '<EMAIL>',
    bookingConfirmationTemplate: 'Thank you for booking with HouseGoing. Your booking for {{venue}} on {{date}} has been confirmed.',
    hostNotificationTemplate: 'You have a new booking for {{venue}} on {{date}}. The guest has booked for {{guests}} people.'
  };
}

/**
 * Replace template variables with actual values
 */
function replaceTemplateVariables(template: string, variables: Record<string, string | number>) {
  let result = template;

  for (const [key, value] of Object.entries(variables)) {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
  }

  return result;
}

/**
 * Format date for email display
 */
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-AU', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format time for email display
 */
function formatTime(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-AU', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Send booking confirmation email to guest
 */
export async function sendBookingConfirmationEmail(booking: any, user: any, venue: any) {
  try {
    const templates = await getEmailTemplates();

    if (!templates.enableEmailNotifications) {
      console.log('Email notifications are disabled');
      return false;
    }

    const template = templates.bookingConfirmationTemplate;
    const formattedDate = formatDate(booking.start_date);
    const startTime = formatTime(booking.start_date);
    const endTime = formatTime(booking.end_date);

    const emailContent = replaceTemplateVariables(template, {
      name: user.first_name || user.name || 'Guest',
      venue: venue.title,
      date: formattedDate,
      time: `${startTime} - ${endTime}`,
      guests: booking.guests_count,
      total: booking.total_price
    });

    // In a production environment, you would integrate with an email service
    // like SendGrid, Mailgun, AWS SES, etc. to actually send the email

    // For now, we'll just log the email content
    console.log('Sending booking confirmation email to:', user.email);
    console.log('Email content:', emailContent);
    console.log('From:', `${templates.senderName} <${templates.senderEmail}>`);

    // TODO: Integrate with an email service

    return true;
  } catch (error) {
    console.error('Error sending booking confirmation email:', error);
    return false;
  }
}

/**
 * Send booking notification email to host
 */
export async function sendHostNotificationEmail(booking: any, host: any, venue: any, guest: any) {
  try {
    const templates = await getEmailTemplates();

    if (!templates.enableEmailNotifications) {
      console.log('Email notifications are disabled');
      return false;
    }

    const template = templates.hostNotificationTemplate;
    const formattedDate = formatDate(booking.start_date);
    const startTime = formatTime(booking.start_date);
    const endTime = formatTime(booking.end_date);

    const emailContent = replaceTemplateVariables(template, {
      host: host.first_name || host.name || 'Host',
      guest: guest.first_name || guest.name || 'Guest',
      venue: venue.title,
      date: formattedDate,
      time: `${startTime} - ${endTime}`,
      guests: booking.guests_count,
      total: booking.total_price
    });

    // In a production environment, you would integrate with an email service
    // like SendGrid, Mailgun, AWS SES, etc. to actually send the email

    // For now, we'll just log the email content
    console.log('Sending host notification email to:', host.email);
    console.log('Email content:', emailContent);
    console.log('From:', `${templates.senderName} <${templates.senderEmail}>`);

    // TODO: Integrate with an email service

    return true;
  } catch (error) {
    console.error('Error sending host notification email:', error);
    return false;
  }
}
