import React from 'react';
import FormError from './FormError';

interface SimpleFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  error?: string | Error | null;
  isSubmitting?: boolean;
  children: React.ReactNode;
}

export default function SimpleForm({
  onSubmit,
  error,
  isSubmitting = false,
  children,
  className = '',
  ...props
}: SimpleFormProps) {
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!isSubmitting) {
      onSubmit(e);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={`space-y-4 ${className}`}
      noValidate
      {...props}
    >
      {error && <FormError error={error} />}
      
      {/* Form content */}
      {children}
    </form>
  );
}
