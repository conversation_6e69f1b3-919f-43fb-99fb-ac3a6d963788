/**
 * Test Data Manager
 * 
 * Admin utility to create and manage test data for development
 */

import React, { useState, useEffect } from 'react';
import { createTestSubmissions, clearTestSubmissions, getSubmissionCount } from '../../utils/createTestSubmissions';
import { Database, Trash2, Plus, RefreshCw } from 'lucide-react';

export default function TestDataManager() {
  const [loading, setLoading] = useState(false);
  const [submissionCount, setSubmissionCount] = useState(0);
  const [results, setResults] = useState<Array<{
    type: string;
    success: boolean;
    message: string;
    timestamp: string;
  }>>([]);

  // Load current submission count
  useEffect(() => {
    loadSubmissionCount();
  }, []);

  const loadSubmissionCount = async () => {
    const count = await getSubmissionCount();
    setSubmissionCount(count);
  };

  const addResult = (type: string, success: boolean, message: string) => {
    setResults(prev => [...prev, {
      type,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const handleCreateTestSubmissions = async () => {
    setLoading(true);
    try {
      const success = await createTestSubmissions();
      addResult(
        'Create Test Submissions',
        success,
        success ? 'Successfully created 5 test property submissions' : 'Failed to create test submissions'
      );
      
      if (success) {
        await loadSubmissionCount();
      }
    } catch (error: any) {
      addResult('Create Test Submissions', false, error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClearTestSubmissions = async () => {
    setLoading(true);
    try {
      const success = await clearTestSubmissions();
      addResult(
        'Clear Test Submissions',
        success,
        success ? 'Successfully cleared all test submissions' : 'Failed to clear test submissions'
      );
      
      if (success) {
        await loadSubmissionCount();
      }
    } catch (error: any) {
      addResult('Clear Test Submissions', false, error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshCount = async () => {
    await loadSubmissionCount();
    addResult('Refresh Count', true, `Current submission count: ${submissionCount}`);
  };

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Test Data Manager</h1>
          <p className="text-gray-600">
            Create and manage test data for development and testing
          </p>
        </div>

        {/* Current Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-1">Current Database Status</h2>
              <p className="text-gray-600">Property submissions in database</p>
            </div>
            <div className="text-right">
              <p className="text-3xl font-bold text-purple-600">{submissionCount}</p>
              <button
                onClick={handleRefreshCount}
                className="text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1 mt-1"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Create Test Data */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-green-100 rounded-lg">
                <Plus className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Create Test Submissions</h3>
                <p className="text-sm text-gray-600">Add 5 sample property submissions</p>
              </div>
            </div>
            
            <div className="space-y-3 mb-4">
              <div className="text-sm text-gray-600">
                <p><strong>Will create:</strong></p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Harbour View Rooftop (50 guests, $150/hr)</li>
                  <li>Industrial Warehouse Space (200 guests, $200/hr)</li>
                  <li>Beachside Garden Villa (30 guests, $120/hr)</li>
                  <li>Urban Loft Space (80 guests, $180/hr)</li>
                  <li>Backyard Pool Paradise (40 guests, $100/hr)</li>
                </ul>
              </div>
            </div>

            <button
              onClick={handleCreateTestSubmissions}
              disabled={loading}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg font-medium transition-colors"
            >
              <Plus className="w-4 h-4" />
              {loading ? 'Creating...' : 'Create Test Submissions'}
            </button>
          </div>

          {/* Clear Test Data */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <Trash2 className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Clear Test Data</h3>
                <p className="text-sm text-gray-600">Remove all test submissions</p>
              </div>
            </div>
            
            <div className="space-y-3 mb-4">
              <div className="text-sm text-gray-600">
                <p><strong>Will remove:</strong></p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>All submissions from dev users</li>
                  <li>Test property data</li>
                  <li>Sample images and amenities</li>
                </ul>
              </div>
            </div>

            <button
              onClick={handleClearTestSubmissions}
              disabled={loading}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg font-medium transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              {loading ? 'Clearing...' : 'Clear Test Submissions'}
            </button>
          </div>
        </div>

        {/* Results Log */}
        {results.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Database className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Action Results</h3>
            </div>
            
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    result.success
                      ? 'bg-green-50 border-green-200 text-green-800'
                      : 'bg-red-50 border-red-200 text-red-800'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{result.type}</p>
                      <p className="text-sm mt-1">{result.message}</p>
                    </div>
                    <span className="text-xs opacity-75">{result.timestamp}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Testing Instructions</h3>
          <div className="space-y-2 text-blue-800 text-sm">
            <p>• <strong>Step 1:</strong> Create test submissions using the button above</p>
            <p>• <strong>Step 2:</strong> Go to <a href="/admin/approval" className="underline">Admin Approval Dashboard</a></p>
            <p>• <strong>Step 3:</strong> Test approving and rejecting properties</p>
            <p>• <strong>Step 4:</strong> Check email notifications in your inbox</p>
            <p>• <strong>Step 5:</strong> Clear test data when finished testing</p>
          </div>
        </div>
      </div>
    </div>
  );
}
