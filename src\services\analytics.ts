// Analytics service for tracking user authentication patterns

// Define event types
export enum AnalyticsEventType {
  SIGN_UP = 'sign_up',
  SIGN_IN = 'sign_in',
  SIGN_OUT = 'sign_out',
  PASSWORD_RESET = 'password_reset',
  EMAIL_VERIFICATION = 'email_verification',
  PROFILE_UPDATE = 'profile_update',
  SOCIAL_SIGN_IN = 'social_sign_in',
}

// Define event properties
export interface AnalyticsEventProperties {
  userId?: string;
  email?: string;
  provider?: string;
  method?: string;
  success?: boolean;
  error?: string;
  [key: string]: any;
}

// Analytics service
class AnalyticsService {
  private enabled: boolean = true;

  // Initialize analytics
  constructor() {
    // In a real application, you would initialize your analytics provider here
    console.log('Analytics service initialized');
  }

  // Track an event
  public track(eventType: AnalyticsEventType, properties: AnalyticsEventProperties = {}): void {
    if (!this.enabled) return;

    // Add timestamp
    const eventData = {
      ...properties,
      timestamp: new Date().toISOString(),
    };

    // In a real application, you would send this data to your analytics provider
    console.log(`Analytics event: ${eventType}`, eventData);

    // Example implementation with a real analytics provider:
    // if (window.analytics) {
    //   window.analytics.track(eventType, eventData);
    // }
  }

  // Enable analytics
  public enable(): void {
    this.enabled = true;
  }

  // Disable analytics
  public disable(): void {
    this.enabled = false;
  }

  // Track sign up event
  public trackSignUp(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.SIGN_UP, properties);
  }

  // Track sign in event
  public trackSignIn(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.SIGN_IN, properties);
  }

  // Track sign out event
  public trackSignOut(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.SIGN_OUT, properties);
  }

  // Track password reset event
  public trackPasswordReset(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.PASSWORD_RESET, properties);
  }

  // Track email verification event
  public trackEmailVerification(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.EMAIL_VERIFICATION, properties);
  }

  // Track profile update event
  public trackProfileUpdate(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.PROFILE_UPDATE, properties);
  }

  // Track social sign in event
  public trackSocialSignIn(properties: AnalyticsEventProperties = {}): void {
    this.track(AnalyticsEventType.SOCIAL_SIGN_IN, properties);
  }
}

// Create a singleton instance
export const analytics = new AnalyticsService();
