# Supabase Email Configuration Guide

## Problem
- Emails are being sent with "Supabase Auth" as the sender instead of "HouseGoing Team"
- Verification links point to localhost instead of your production domain
- The email template lacks proper branding

## Solution

### 1. Update Site URL in Supabase Project Settings

1. Go to the [Supabase Dashboard](https://app.supabase.io/)
2. Select your project
3. Go to **Project Settings** → **General**
4. Find the **Site URL** field
5. Update it from `http://localhost:3000` to your production URL: `https://housegoing.com.au`
6. Click **Save**

### 2. Customize Email Templates

1. In the Supabase Dashboard, go to **Authentication** → **Email Templates**
2. You'll see templates for:
   - Confirmation
   - Invite
   - Magic Link
   - Recovery (Reset Password)
   - Change Email

3. For each template:
   - Update the **Sender Name** from "Supabase" to "HouseGoing Team"
   - Update the **Subject Line** to include your brand
   - Customize the email content to match your brand voice

### 3. Example Template for Confirmation Email

```html
<h2>Welcome to HouseGoing!</h2>
<p>Please confirm your email address by clicking the button below:</p>
<p>
  <a href="{{ .ConfirmationURL }}" style="display: inline-block; color: white; background-color: #8A2BE2; border-radius: 4px; padding: 12px 24px; text-decoration: none;">
    Confirm Your Email
  </a>
</p>
<p>If you didn't sign up for HouseGoing, you can safely ignore this email.</p>
<p>Thanks,<br>The HouseGoing Team</p>
```

### 4. Example Template for Magic Link Email

```html
<h2>Sign In to HouseGoing</h2>
<p>Click the button below to sign in to your HouseGoing account:</p>
<p>
  <a href="{{ .SiteURL }}/auth/confirm?token={{ .Token }}&type=magiclink&redirect_to={{ .RedirectTo }}" style="display: inline-block; color: white; background-color: #8A2BE2; border-radius: 4px; padding: 12px 24px; text-decoration: none;">
    Sign In
  </a>
</p>
<p>If you didn't request this email, you can safely ignore it.</p>
<p>Thanks,<br>The HouseGoing Team</p>
```

### 5. Example Template for Reset Password Email

```html
<h2>Reset Your HouseGoing Password</h2>
<p>Click the button below to reset your password:</p>
<p>
  <a href="{{ .SiteURL }}/auth/recovery?token={{ .Token }}&type=recovery&redirect_to={{ .RedirectTo }}" style="display: inline-block; color: white; background-color: #8A2BE2; border-radius: 4px; padding: 12px 24px; text-decoration: none;">
    Reset Password
  </a>
</p>
<p>If you didn't request a password reset, you can safely ignore this email.</p>
<p>Thanks,<br>The HouseGoing Team</p>
```

## Important Variables in Templates

- `{{ .ConfirmationURL }}` - The full URL for email confirmation
- `{{ .SiteURL }}` - Your site URL (set in Project Settings)
- `{{ .Token }}` - The authentication token
- `{{ .RedirectTo }}` - Where to redirect after authentication

## Update the Code

Now let's update the code to ensure the correct redirect URL is used:

```typescript
// When signing up
const { data, error } = await supabase.auth.signUp({
  email,
  password,
  options: {
    data: {
      first_name: firstName,
      last_name: lastName,
      role: userType,
    },
    emailRedirectTo: 'https://housegoing.com.au/auth/callback'
  },
});

// When resending confirmation
const { error } = await supabase.auth.resend({
  type: 'signup',
  email: emailToUse,
  options: {
    emailRedirectTo: 'https://housegoing.com.au/auth/callback'
  }
});
```

Replace `https://housegoing.com.au` with your actual production domain.
