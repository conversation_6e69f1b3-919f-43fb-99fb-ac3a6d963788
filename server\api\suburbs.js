import axios from 'axios';
import { createProxyMiddleware } from 'http-proxy-middleware';

// NSW API endpoints
const NSW_SUBURB_SEARCH_URL = 'https://api.planningportal.nsw.gov.au/suburbs';

export default function suburbsApi(app) {
  // Search suburbs endpoint
  app.get('/api/suburbs/search', async (req, res) => {
    try {
      const { q } = req.query;
      
      if (!q || q.length < 3) {
        return res.status(400).json({ error: 'Search query must be at least 3 characters' });
      }

      const response = await axios.get(NSW_SUBURB_SEARCH_URL, {
        params: { q },
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${process.env.NSW_API_KEY}`
        }
      });

      // Transform NSW API response to our format
      const suburbs = response.data.features.map(feature => ({
        name: feature.properties.suburbName,
        postcode: feature.properties.postcode,
        lga: feature.properties.lgaName,
        centroid: feature.geometry.coordinates
      }));

      res.json({ suburbs });
      
    } catch (error) {
      console.error('Suburb search error:', error);
      res.status(500).json({ error: 'Failed to search suburbs' });
    }
  });

  // Proxy middleware for NSW planning APIs
  app.use('/api/nsw', createProxyMiddleware({
    target: 'https://api.planningportal.nsw.gov.au',
    changeOrigin: true,
    pathRewrite: { '^/api/nsw': '' },
    onProxyReq: (proxyReq) => {
      proxyReq.setHeader('Authorization', `Bearer ${process.env.NSW_API_KEY}`);
    }
  }));
}
