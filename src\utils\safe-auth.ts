/**
 * Special handler for Clerk-Supabase authentication errors
 * This function wraps any function that might cause a "_ is not a function" error
 * and provides a safe fallback
 */

/**
 * Safely execute a function that might be undefined
 * This prevents the "_ is not a function" error
 */
export function safeExec<T>(fn: any, fallback: T): T {
  try {
    if (typeof fn === 'function') {
      return fn();
    }
    return fallback;
  } catch (err) {
    console.error('Error executing function:', err);
    return fallback;
  }
}

/**
 * Safely access a property that might be undefined
 * This prevents the "Cannot read property of undefined" error
 */
export function safeAccess<T>(obj: any, path: string, fallback: T): T {
  try {
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current === undefined || current === null) {
        return fallback;
      }
      current = current[part];
    }
    
    return current !== undefined && current !== null ? current : fallback;
  } catch (err) {
    console.error(`Error accessing property "${path}":`, err);
    return fallback;
  }
}

/**
 * Checks if the current environment is a Google OAuth session
 */
export function isGoogleOAuthSession(): boolean {
  // Check the URL for Google OAuth parameters
  if (typeof window !== 'undefined') {
    const url = window.location.href;
    return url.includes('google') && 
           (url.includes('oauth') || url.includes('callback'));
  }
  return false;
}

/**
 * Debug information about authentication
 */
export function getAuthDebugInfo(): Record<string, any> {
  const info: Record<string, any> = {
    timestamp: new Date().toISOString(),
    url: typeof window !== 'undefined' ? window.location.href : 'SSR',
    isGoogleOAuth: isGoogleOAuthSession()
  };
  
  // Add Clerk info if available
  if (typeof window !== 'undefined' && window.Clerk) {
    info.clerkLoaded = window.Clerk.loaded;
    info.hasUser = !!window.Clerk.user;
    info.hasSession = !!window.Clerk.session;
    
    if (window.Clerk.session) {
      info.sessionId = safeAccess(window.Clerk.session, 'id', null);
      info.sessionStatus = safeAccess(window.Clerk.session, 'status', null);
    }
    
    if (window.Clerk.user) {
      info.userId = safeAccess(window.Clerk.user, 'id', null);
      info.userEmail = safeAccess(
        window.Clerk.user, 
        'primaryEmailAddress.emailAddress', 
        null
      );
    }
  }
  
  return info;
}
