import React, { useState, useEffect } from 'react';
import { Heart, MapPin, DollarSign, Trash2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { getSavedVenues, removeSavedVenue, SavedVenue } from '../../api/userAccount';

interface SavedVenuesSectionProps {
  userId: string;
}

export default function SavedVenuesSection({ userId }: SavedVenuesSectionProps) {
  const [savedVenues, setSavedVenues] = useState<SavedVenue[]>([]);
  const [loading, setLoading] = useState(true);
  const [removingId, setRemovingId] = useState<string | null>(null);

  useEffect(() => {
    fetchSavedVenues();
  }, [userId]);

  const fetchSavedVenues = async () => {
    try {
      setLoading(true);
      const data = await getSavedVenues(userId);
      setSavedVenues(data);
    } catch (error) {
      console.error('Error fetching saved venues:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveVenue = async (venueId: string) => {
    try {
      setRemovingId(venueId);
      await removeSavedVenue(userId, venueId);
      setSavedVenues(prev => prev.filter(sv => sv.venue_id !== venueId));
    } catch (error) {
      console.error('Error removing saved venue:', error);
    } finally {
      setRemovingId(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (savedVenues.length === 0) {
    return (
      <div className="text-center py-12">
        <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">No saved venues yet</h3>
        <p className="text-gray-600 mb-6 font-inter">
          Save venues you like to easily find them later
        </p>
        <Link 
          to="/" 
          className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm font-medium transition-colors font-inter"
        >
          Explore Venues
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 font-inter">
            Your Saved Venues ({savedVenues.length})
          </h3>
          <p className="text-gray-600 text-sm font-inter">
            Venues you've saved for future bookings
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {savedVenues.map((savedVenue) => {
          const venue = savedVenue.venue;
          const mainImage = venue.images && venue.images.length > 0 
            ? venue.images[0] 
            : 'https://via.placeholder.com/300x200?text=No+Image';

          return (
            <div 
              key={savedVenue.id} 
              className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow group"
            >
              {/* Image */}
              <div className="relative">
                <img
                  src={mainImage}
                  alt={venue.title}
                  className="w-full h-48 object-cover"
                />
                <button
                  onClick={() => handleRemoveVenue(venue.id)}
                  disabled={removingId === venue.id}
                  className="absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-full shadow-md transition-colors group-hover:opacity-100 opacity-0"
                  title="Remove from saved"
                >
                  {removingId === venue.id ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-red-500"></div>
                  ) : (
                    <Trash2 className="w-4 h-4 text-red-500" />
                  )}
                </button>
                <div className="absolute top-3 left-3 p-2 bg-white/90 rounded-full shadow-md">
                  <Heart className="w-4 h-4 text-red-500 fill-current" />
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h4 className="font-semibold text-gray-900 mb-2 font-inter line-clamp-2">
                  {venue.title}
                </h4>
                
                <div className="flex items-center gap-1 text-gray-600 mb-2">
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm font-inter line-clamp-1">{venue.location}</span>
                </div>

                <div className="flex items-center gap-1 text-gray-900 mb-4">
                  <DollarSign className="w-4 h-4" />
                  <span className="font-semibold font-inter">${venue.price_per_hour}</span>
                  <span className="text-sm text-gray-600 font-inter">per hour</span>
                </div>

                <div className="flex gap-2">
                  <Link
                    to={`/venue/${venue.id}`}
                    className="flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-center rounded-md text-sm font-medium transition-colors font-inter"
                  >
                    View Details
                  </Link>
                  <Link
                    to={`/venue/${venue.id}/book`}
                    className="flex-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-900 text-center rounded-md text-sm font-medium transition-colors font-inter"
                  >
                    Book Now
                  </Link>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-100">
                  <p className="text-xs text-gray-500 font-inter">
                    Saved on {new Date(savedVenue.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Load More Button (for future pagination) */}
      {savedVenues.length >= 9 && (
        <div className="text-center">
          <button className="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-900 rounded-md font-medium transition-colors font-inter">
            Load More
          </button>
        </div>
      )}
    </div>
  );
}
