/**
 * Admin Reminder Test Page
 *
 * Test page for the daily reminder system
 */

import React, { useState } from 'react';
import {
  Mail,
  Clock,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Users,
  Send,
  RefreshCw
} from 'lucide-react';
import {
  getPendingSubmissions,
  sendDailyReminderEmail,
  shouldSendReminderToday,
  runDailyReminderCheck,
  generateReminderEmailTemplate
} from '../../services/adminReminderService';

export default function AdminReminderTest() {
  const [loading, setLoading] = useState(false);
  const [pendingSubmissions, setPendingSubmissions] = useState<any[]>([]);
  const [lastCheck, setLastCheck] = useState<string | null>(null);
  const [emailPreview, setEmailPreview] = useState<string>('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const loadPendingSubmissions = async () => {
    setLoading(true);
    addTestResult('Loading pending submissions...');

    try {
      const submissions = await getPendingSubmissions();
      setPendingSubmissions(submissions);
      setLastCheck(new Date().toLocaleString());
      addTestResult(`Found ${submissions.length} pending submissions`);

      // Generate email preview
      if (submissions.length > 0) {
        const urgentCount = submissions.filter(s => s.daysPending > 3).length;
        const emailData = {
          totalPending: submissions.length,
          urgentCount,
          submissions,
          adminEmails: ['<EMAIL>', '<EMAIL>']
        };
        const template = generateReminderEmailTemplate(emailData);
        setEmailPreview(template);
      }
    } catch (error) {
      addTestResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testSendReminder = async () => {
    setLoading(true);
    addTestResult('Testing reminder email send...');

    try {
      if (pendingSubmissions.length === 0) {
        addTestResult('No pending submissions to send reminder for');
        return;
      }

      const success = await sendDailyReminderEmail(pendingSubmissions);
      if (success) {
        addTestResult('✅ Reminder email sent successfully!');
      } else {
        addTestResult('❌ Failed to send reminder email');
      }
    } catch (error) {
      addTestResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testFullReminderCheck = async () => {
    setLoading(true);
    addTestResult('Running full daily reminder check...');

    try {
      await runDailyReminderCheck();
      addTestResult('✅ Daily reminder check completed');
      await loadPendingSubmissions(); // Refresh data
    } catch (error) {
      addTestResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const shouldSendToday = shouldSendReminderToday();
  const urgentCount = pendingSubmissions.filter(s => s.daysPending > 3).length;

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Reminder System Test</h1>
          <p className="text-gray-600">Test and monitor the daily reminder system for pending property approvals</p>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Pending</p>
                <p className="text-2xl font-bold text-gray-900">{pendingSubmissions.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Urgent (&gt;3 days)</p>
                <p className="text-2xl font-bold text-red-600">{urgentCount}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Send Today?</p>
                <p className="text-lg font-bold text-green-600">{shouldSendToday ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Check</p>
                <p className="text-sm font-medium text-gray-900">{lastCheck || 'Never'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Actions</h3>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={loadPendingSubmissions}
              disabled={loading}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Load Pending Submissions
            </button>

            <button
              onClick={testSendReminder}
              disabled={loading || pendingSubmissions.length === 0}
              className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg font-medium transition-colors"
            >
              <Send className="w-4 h-4" />
              Test Send Reminder
            </button>

            <button
              onClick={testFullReminderCheck}
              disabled={loading}
              className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-lg font-medium transition-colors"
            >
              <Mail className="w-4 h-4" />
              Run Full Check
            </button>
          </div>
        </div>

        {/* Pending Submissions List */}
        {pendingSubmissions.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Pending Submissions</h3>
            <div className="space-y-4">
              {pendingSubmissions.map((submission) => (
                <div
                  key={submission.id}
                  className={`p-4 rounded-lg border-l-4 ${
                    submission.daysPending > 3
                      ? 'bg-red-50 border-red-400'
                      : 'bg-blue-50 border-blue-400'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{submission.name}</h4>
                      <p className="text-sm text-gray-600">{submission.address}</p>
                      <p className="text-sm text-gray-500">
                        {submission.type} | {submission.ownerName} ({submission.ownerEmail})
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        submission.daysPending > 3
                          ? 'bg-red-100 text-red-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {submission.daysPending} days pending
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        Submitted: {new Date(submission.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Test Results Log */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results Log</h3>
          <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 text-sm">No test results yet. Run a test to see results here.</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
          <button
            onClick={() => setTestResults([])}
            className="mt-3 text-sm text-gray-500 hover:text-gray-700"
          >
            Clear Log
          </button>
        </div>

        {/* Email Preview */}
        {emailPreview && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Email Preview</h3>
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <iframe
                srcDoc={emailPreview}
                className="w-full h-96"
                title="Email Preview"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
