/**
 * Sitemap Middleware
 *
 * This middleware ensures that sitemap files are served with the correct content type.
 */

import fs from 'fs';
import path from 'path';

export function setupSitemapMiddleware(app, distDir) {
  // Helper function to serve XML files with the correct content type
  const serveXmlFile = (filePath, res) => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');

      // Clear any previous headers
      res.removeHeader('Content-Type');
      res.removeHeader('X-Powered-By');

      // Set the correct content type for XML with charset
      res.setHeader('Content-Type', 'application/xml; charset=utf-8');

      // Add cache control headers
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 24 hours

      // Prevent content sniffing
      res.setHeader('X-Content-Type-Options', 'nosniff');

      // Send the XML content
      res.send(content);
    } else {
      res.status(404).send('File not found');
    }
  };

  // Serve sitemap_index.xml with the correct content type
  app.get('/sitemap_index.xml', (req, res) => {
    const filePath = path.join(distDir, 'sitemap_index.xml');
    serveXmlFile(filePath, res);
  });

  // Serve sitemap_main.xml with the correct content type
  app.get('/sitemap_main.xml', (req, res) => {
    const filePath = path.join(distDir, 'sitemap_main.xml');
    serveXmlFile(filePath, res);
  });

  // Serve sitemap_blog.xml with the correct content type
  app.get('/sitemap_blog.xml', (req, res) => {
    const filePath = path.join(distDir, 'sitemap_blog.xml');
    serveXmlFile(filePath, res);
  });

  // Serve sitemap_venues.xml with the correct content type
  app.get('/sitemap_venues.xml', (req, res) => {
    const filePath = path.join(distDir, 'sitemap_venues.xml');
    serveXmlFile(filePath, res);
  });

  // Serve sitemap_comprehensive.xml with the correct content type
  app.get('/sitemap_comprehensive.xml', (req, res) => {
    const filePath = path.join(distDir, 'sitemap_comprehensive.xml');
    serveXmlFile(filePath, res);
  });

  // For backward compatibility, serve sitemap.xml with proper headers
  app.get('/sitemap.xml', (req, res) => {
    const filePath = path.join(distDir, 'sitemap_comprehensive.xml');

    // Log the request for debugging
    console.log(`Serving sitemap.xml from ${filePath}`);

    // Explicitly set content type before anything else
    res.type('application/xml');

    serveXmlFile(filePath, res);
  });

  // Serve robots.txt with the correct content type
  app.get('/robots.txt', (req, res) => {
    const robotsPath = path.join(distDir, 'robots.txt');

    if (fs.existsSync(robotsPath)) {
      const robotsContent = fs.readFileSync(robotsPath, 'utf8');

      // Set the correct content type for text files
      res.setHeader('Content-Type', 'text/plain');
      res.send(robotsContent);
    } else {
      res.status(404).send('Robots.txt not found');
    }
  });
}
