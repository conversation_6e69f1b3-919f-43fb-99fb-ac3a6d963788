import React, { useEffect, useState } from 'react';
import { useAuth } from '@clerk/clerk-react';

export default function ClerkSupabaseInitializer({ children }: { children: React.ReactNode }) {
  const { isLoaded } = useAuth();
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    // Simple timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.log('ClerkSupabaseInitializer: Timeout reached, proceeding');
      setInitialized(true);
    }, 2000);

    // If Clerk is loaded, proceed immediately
    if (isLoaded) {
      console.log('ClerkSupabaseInitializer: Clerk loaded, proceeding');
      clearTimeout(timeout);
      setInitialized(true);
    }

    return () => clearTimeout(timeout);
  }, [isLoaded]);

  // Show loading while initializing
  if (!initialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
          <p className="text-gray-600 mb-2">Initializing...</p>
          <p className="text-gray-400 text-sm">Please wait a moment</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
