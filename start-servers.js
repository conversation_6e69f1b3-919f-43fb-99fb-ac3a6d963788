/**
 * Start Servers Script
 * 
 * This script starts both the main application and the WFS proxy server.
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration
const servers = [
  {
    name: 'WFS Proxy Server',
    command: 'node',
    args: ['server/wfs-proxy.js'],
    color: '\x1b[36m' // Cyan
  },
  {
    name: 'NSW Planning Server',
    command: 'node',
    args: ['mcp-servers/nsw-planning/server.js'],
    color: '\x1b[35m' // Magenta
  }
];

// Start all servers
function startServers() {
  console.log('\x1b[32m%s\x1b[0m', '=== Starting Servers ===');
  
  servers.forEach(server => {
    const process = spawn(server.command, server.args, {
      cwd: __dirname,
      stdio: 'pipe',
      shell: true
    });
    
    console.log(`${server.color}%s\x1b[0m`, `[${server.name}] Starting...`);
    
    process.stdout.on('data', (data) => {
      console.log(`${server.color}%s\x1b[0m`, `[${server.name}] ${data.toString().trim()}`);
    });
    
    process.stderr.on('data', (data) => {
      console.error(`${server.color}%s\x1b[31m`, `[${server.name}] ERROR:`, data.toString().trim());
    });
    
    process.on('close', (code) => {
      if (code !== 0) {
        console.error(`${server.color}%s\x1b[31m`, `[${server.name}] Process exited with code ${code}`);
      } else {
        console.log(`${server.color}%s\x1b[0m`, `[${server.name}] Process exited normally`);
      }
    });
  });
  
  console.log('\x1b[32m%s\x1b[0m', '=== All Servers Started ===');
  console.log('\x1b[33m%s\x1b[0m', 'Press Ctrl+C to stop all servers');
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\x1b[33m%s\x1b[0m', 'Shutting down all servers...');
  process.exit();
});

// Start the servers
startServers();
