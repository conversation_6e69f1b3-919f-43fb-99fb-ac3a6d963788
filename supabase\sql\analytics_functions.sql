-- Analytics Functions for Suburb Data
-- Run this in Supabase SQL Editor

-- 1. Function to get top searched suburbs
CREATE OR REPLACE FUNCTION get_top_searched_suburbs(
    limit_count INTEGER DEFAULT 10,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    suburb TEXT,
    state TEXT,
    search_count BIGINT,
    unique_users BIGINT,
    unique_ips BIGINT,
    last_search TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sa.suburb,
        sa.state,
        COUNT(*) as search_count,
        COUNT(DISTINCT sa.user_id) FILTER (WHERE sa.user_id IS NOT NULL) as unique_users,
        COUNT(DISTINCT sa.ip_address) as unique_ips,
        MAX(sa.created_at) as last_search
    FROM search_analytics sa
    WHERE sa.suburb IS NOT NULL 
        AND sa.created_at >= NOW() - INTERVAL '%s days' % days_back
    GROUP BY sa.suburb, sa.state
    ORDER BY search_count DESC, unique_ips DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 2. Function to get top booked suburbs
CREATE OR REPLACE FUNCTION get_top_booked_suburbs(
    limit_count INTEGER DEFAULT 10,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    suburb TEXT,
    state TEXT,
    booking_count BIGINT,
    total_revenue DECIMAL(12,2),
    unique_users BIGINT,
    avg_booking_amount DECIMAL(10,2),
    last_booking TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ba.venue_suburb as suburb,
        ba.venue_state as state,
        COUNT(*) as booking_count,
        COALESCE(SUM(ba.booking_amount), 0) as total_revenue,
        COUNT(DISTINCT ba.user_id) as unique_users,
        COALESCE(AVG(ba.booking_amount), 0) as avg_booking_amount,
        MAX(ba.created_at) as last_booking
    FROM booking_analytics ba
    WHERE ba.booking_status IN ('confirmed', 'pending')
        AND ba.created_at >= NOW() - INTERVAL '%s days' % days_back
    GROUP BY ba.venue_suburb, ba.venue_state
    ORDER BY booking_count DESC, total_revenue DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 3. Function to get suburb conversion rates (searches to bookings)
CREATE OR REPLACE FUNCTION get_suburb_conversion_rates(
    limit_count INTEGER DEFAULT 10,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    suburb TEXT,
    state TEXT,
    search_count BIGINT,
    booking_count BIGINT,
    conversion_rate DECIMAL(5,2),
    revenue_per_search DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH search_data AS (
        SELECT 
            sa.suburb,
            sa.state,
            COUNT(DISTINCT sa.ip_address) as searches
        FROM search_analytics sa
        WHERE sa.suburb IS NOT NULL 
            AND sa.created_at >= NOW() - INTERVAL '%s days' % days_back
        GROUP BY sa.suburb, sa.state
    ),
    booking_data AS (
        SELECT 
            ba.venue_suburb as suburb,
            ba.venue_state as state,
            COUNT(*) as bookings,
            COALESCE(SUM(ba.booking_amount), 0) as revenue
        FROM booking_analytics ba
        WHERE ba.booking_status IN ('confirmed', 'pending')
            AND ba.created_at >= NOW() - INTERVAL '%s days' % days_back
        GROUP BY ba.venue_suburb, ba.venue_state
    )
    SELECT 
        COALESCE(s.suburb, b.suburb) as suburb,
        COALESCE(s.state, b.state) as state,
        COALESCE(s.searches, 0) as search_count,
        COALESCE(b.bookings, 0) as booking_count,
        CASE 
            WHEN COALESCE(s.searches, 0) > 0 
            THEN ROUND((COALESCE(b.bookings, 0)::DECIMAL / s.searches * 100), 2)
            ELSE 0
        END as conversion_rate,
        CASE 
            WHEN COALESCE(s.searches, 0) > 0 
            THEN ROUND((COALESCE(b.revenue, 0) / s.searches), 2)
            ELSE 0
        END as revenue_per_search
    FROM search_data s
    FULL OUTER JOIN booking_data b ON s.suburb = b.suburb AND s.state = b.state
    WHERE COALESCE(s.searches, 0) > 0 OR COALESCE(b.bookings, 0) > 0
    ORDER BY conversion_rate DESC, search_count DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 4. Function to track a search event
CREATE OR REPLACE FUNCTION track_search_event(
    p_user_id TEXT,
    p_ip_address INET,
    p_search_query TEXT,
    p_suburb TEXT,
    p_state TEXT,
    p_search_filters JSONB DEFAULT '{}',
    p_results_count INTEGER DEFAULT 0,
    p_user_agent TEXT DEFAULT '',
    p_referrer TEXT DEFAULT '',
    p_session_id TEXT DEFAULT ''
)
RETURNS UUID AS $$
DECLARE
    search_id UUID;
BEGIN
    -- Insert search analytics record
    INSERT INTO search_analytics (
        user_id, ip_address, search_query, suburb, state,
        search_filters, results_count, user_agent, referrer, session_id
    ) VALUES (
        p_user_id, p_ip_address, p_search_query, p_suburb, p_state,
        p_search_filters, p_results_count, p_user_agent, p_referrer, p_session_id
    ) RETURNING id INTO search_id;

    -- Update or insert suburb popularity
    INSERT INTO suburb_popularity (suburb, state, search_count, last_search_date)
    VALUES (p_suburb, p_state, 1, NOW())
    ON CONFLICT (suburb, state) 
    DO UPDATE SET 
        search_count = suburb_popularity.search_count + 1,
        last_search_date = NOW(),
        updated_at = NOW();

    -- Update session tracking
    INSERT INTO user_sessions (session_id, user_id, ip_address, searches_count, user_agent)
    VALUES (p_session_id, p_user_id, p_ip_address, 1, p_user_agent)
    ON CONFLICT (session_id)
    DO UPDATE SET
        last_seen = NOW(),
        searches_count = user_sessions.searches_count + 1;

    RETURN search_id;
END;
$$ LANGUAGE plpgsql;

-- 5. Function to track a booking event
CREATE OR REPLACE FUNCTION track_booking_event(
    p_booking_id UUID,
    p_user_id TEXT,
    p_ip_address INET,
    p_venue_id UUID,
    p_venue_suburb TEXT,
    p_venue_state TEXT,
    p_booking_date DATE,
    p_booking_amount DECIMAL(10,2),
    p_guest_count INTEGER,
    p_booking_status TEXT DEFAULT 'pending',
    p_user_agent TEXT DEFAULT '',
    p_session_id TEXT DEFAULT ''
)
RETURNS UUID AS $$
DECLARE
    analytics_id UUID;
BEGIN
    -- Insert booking analytics record
    INSERT INTO booking_analytics (
        booking_id, user_id, ip_address, venue_id, venue_suburb, venue_state,
        booking_date, booking_amount, guest_count, booking_status, user_agent, session_id
    ) VALUES (
        p_booking_id, p_user_id, p_ip_address, p_venue_id, p_venue_suburb, p_venue_state,
        p_booking_date, p_booking_amount, p_guest_count, p_booking_status, p_user_agent, p_session_id
    ) RETURNING id INTO analytics_id;

    -- Update suburb popularity
    INSERT INTO suburb_popularity (suburb, state, booking_count, last_booking_date)
    VALUES (p_venue_suburb, p_venue_state, 1, NOW())
    ON CONFLICT (suburb, state) 
    DO UPDATE SET 
        booking_count = suburb_popularity.booking_count + 1,
        last_booking_date = NOW(),
        updated_at = NOW();

    -- Update session tracking
    INSERT INTO user_sessions (session_id, user_id, ip_address, bookings_count, user_agent)
    VALUES (p_session_id, p_user_id, p_ip_address, 1, p_user_agent)
    ON CONFLICT (session_id)
    DO UPDATE SET
        last_seen = NOW(),
        bookings_count = user_sessions.bookings_count + 1;

    RETURN analytics_id;
END;
$$ LANGUAGE plpgsql;
