/**
 * Initialization service for the application
 * Handles database setup and other initialization tasks
 */
import { ensureAdminTable } from './admin-auth';
import { isSupabaseAvailable } from './api';
import { initSupabaseClientChecker } from '../utils/supabase-client-checker';
import { supabase } from '../lib/supabase-client';
import { checkSupabaseHealth, fixSupabaseIssues } from '../utils/supabase-health-check';
import { initOAuthHelper } from '../utils/clerk-oauth-helper';
import { initializeDatabase } from '../utils/database-initializer';
import { ensureUserProfilesSchema } from '../lib/database/ensure-user-profiles-schema';

// Track initialization status
let initializationComplete = false;
let initializationError: Error | null = null;

/**
 * Initialize the application
 * This should be called when the app starts
 */
export async function initializeApp() {
  // Don't initialize twice
  if (initializationComplete) {
    console.log('Application already initialized');
    return;
  }

  try {
    console.log('Initializing application...');

    // Set a timeout for initialization
    const initTimeout = setTimeout(() => {
      if (!initializationComplete) {
        const timeoutError = new Error('Application initialization timed out');
        console.error(timeoutError);
        initializationError = timeoutError;
        // Force initialization to complete despite timeout
        initializationComplete = true;
        document.dispatchEvent(new CustomEvent('app-initialized', { detail: { success: false, error: timeoutError } }));
      }
    }, 10000); // 10 second timeout

    // Initialize Supabase client checker to detect multiple clients
    initSupabaseClientChecker();

    // Initialize OAuth helper
    initOAuthHelper();

    // Check Supabase availability
    if (!isSupabaseAvailable) {
      console.warn('Supabase is not available - continuing with limited functionality');
    } else {
      // Run Supabase health check
      try {
        console.log('Running Supabase health check...');
        const healthCheck = await checkSupabaseHealth();

        if (!healthCheck.isHealthy) {
          console.warn('Supabase health check failed:', healthCheck.errors);
          console.log('Attempting to fix Supabase issues...');

          const fixResult = await fixSupabaseIssues();
          if (fixResult.success) {
            console.log('Successfully fixed Supabase issues:', fixResult.actionsPerformed);
          } else {
            console.warn('Failed to fix all Supabase issues:', fixResult.errors);
          }
        } else {
          console.log('Supabase health check passed');
        }
      } catch (e) {
        console.warn('Supabase health check exception:', e);
      }
    }

    // Initialize database with our new initializer
    try {
      console.log('Running Supabase migrations...');
      const dbInitResult = await initializeDatabase();

      if (dbInitResult) {
        console.log('Database initialization successful');
      } else {
        console.warn('Database initialization partially successful or failed');
      }
    } catch (dbError) {
      console.error('Database initialization error, but continuing:', dbError);
    }

    // Ensure user_profiles table has correct schema
    try {
      console.log('Ensuring user_profiles table schema...');
      const schemaResult = await ensureUserProfilesSchema();
      if (schemaResult) {
        console.log('user_profiles schema verification successful');
      } else {
        console.warn('user_profiles schema verification failed, but continuing');
      }
    } catch (schemaError) {
      console.error('user_profiles schema error, but continuing:', schemaError);
    }

    // Ensure admin table exists (with built-in timeout)
    await ensureAdminTable();

    // Clear the timeout since initialization completed successfully
    clearTimeout(initTimeout);

    console.log('Application initialization complete');
    initializationComplete = true;
    document.dispatchEvent(new CustomEvent('app-initialized', { detail: { success: true } }));
  } catch (error) {
    console.error('Error initializing application:', error);
    initializationError = error instanceof Error ? error : new Error(String(error));
    initializationComplete = true; // Mark as complete even though it failed
    document.dispatchEvent(new CustomEvent('app-initialized', { detail: { success: false, error } }));
  }
}

// Get initialization status
export function getInitializationStatus() {
  return {
    complete: initializationComplete,
    error: initializationError,
    supabaseAvailable: isSupabaseAvailable
  };
}

// Export a default object for easier importing
export default {
  initializeApp,
  getInitializationStatus
};
