
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Home, Building2, Trees, Beer, UtensilsCrossed, Music, Wine, Cloud, DollarSign } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";

const venueTypes = [
  { 
    id: 'house', 
    label: 'House', 
    Icon: Home,
    description: 'Perfect for intimate gatherings and house parties',
    features: ['Flexible layout', 'Kitchen access', 'Indoor/outdoor options']
  },
  { 
    id: 'apartment', 
    label: 'Apartment', 
    Icon: Building2,
    description: 'Urban spaces ideal for modern celebrations',
    features: ['City views', 'Modern amenities', 'Easy access']
  },
  { 
    id: 'cabin', 
    label: 'Cabin', 
    Icon: Trees,
    description: 'Rustic charm for unique party experiences',
    features: ['Natural setting', 'Privacy', 'Outdoor space']
  },
  { 
    id: 'farmhouse', 
    label: 'Farmhouse', 
    Icon: Home,
    description: 'Spacious venues with countryside appeal',
    features: ['Large grounds', 'Rustic charm', 'Parking space']
  },
  { 
    id: 'pub', 
    label: 'Pub', 
    Icon: Beer,
    description: 'Traditional pub atmosphere for casual events',
    features: ['Bar service', 'Entertainment setup', 'Catering options']
  },
  { 
    id: 'restaurant', 
    label: 'Restaurant', 
    Icon: UtensilsCrossed,
    description: 'Elegant dining spaces for sophisticated parties',
    features: ['Professional kitchen', 'Table service', 'Custom menus']
  },
  { 
    id: 'club', 
    label: 'Club', 
    Icon: Music,
    description: 'High-energy venues for ultimate party experiences',
    features: ['Sound system', 'Dance floor', 'VIP areas']
  },
  { 
    id: 'bar', 
    label: 'Bar', 
    Icon: Wine,
    description: 'Stylish bars for social gatherings',
    features: ['Premium drinks', 'Lounge areas', 'Entertainment']
  },
  { 
    id: 'backyard', 
    label: 'Backyard', 
    Icon: Trees,
    description: 'Open-air spaces for garden parties',
    features: ['Outdoor setup', 'BBQ areas', 'Natural ambiance']
  },
  { 
    id: 'rooftop', 
    label: 'Rooftop', 
    Icon: Cloud,
    description: 'Elevated spaces with stunning views',
    features: ['City views', 'Open air', 'Unique atmosphere']
  }
];

const ListVenue = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleVenueTypeSelect = (venueType: string) => {
    localStorage.setItem('selectedVenueType', venueType);
    toast({
      title: "Venue type selected!",
      description: "Let's get some details about your location.",
    });
    navigate('/list-venue/location');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            Start Your Hosting Journey
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Transform your space into an unforgettable party venue
          </p>
        </div>
        
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-yellow-100 rounded-full">
              <DollarSign className="h-6 w-6 text-yellow-700" />
            </div>
            <div>
              <h3 className="font-semibold text-lg text-yellow-800 mb-1">
                Maximize Your Venue's Potential
              </h3>
              <p className="text-yellow-800">
                Venues that allow parties can earn <span className="font-semibold">20%-50% more per booking</span>. 
                Join thousands of successful hosts on HouseGoing!
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {venueTypes.map((type) => {
            const { Icon } = type;
            return (
              <Card 
                key={type.id}
                className="p-6 cursor-pointer hover:border-primary transition-all duration-300 hover:shadow-lg group"
                onClick={() => handleVenueTypeSelect(type.id)}
              >
                <div className="flex flex-col space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <span className="text-lg font-semibold group-hover:text-primary transition-colors">
                      {type.label}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm">{type.description}</p>
                  
                  <div className="flex flex-wrap gap-2">
                    {type.features.map((feature, index) => (
                      <Badge 
                        key={index}
                        variant="outline" 
                        className="bg-primary/5 hover:bg-primary/10 transition-colors"
                      >
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ListVenue;
