/**
 * Location Radius Filter Component
 * 
 * Provides radius filtering options for location-based venue search
 */

import React, { useState } from 'react';
import { MapPin, ChevronDown } from 'lucide-react';

interface LocationRadiusFilterProps {
  selectedRadius: number;
  onRadiusChange: (radius: number) => void;
  disabled?: boolean;
}

const RADIUS_OPTIONS = [
  { value: 5, label: '+5km' },
  { value: 10, label: '10km' },
  { value: 15, label: '15km' },
  { value: 20, label: '20km' },
  { value: 50, label: '50km' },
  { value: 100, label: '100km' }
];

export default function LocationRadiusFilter({ 
  selectedRadius, 
  onRadiusChange, 
  disabled = false 
}: LocationRadiusFilterProps) {
  const [isOpen, setIsOpen] = useState(false);

  const selectedOption = RADIUS_OPTIONS.find(option => option.value === selectedRadius);

  const handleRadiusSelect = (radius: number) => {
    onRadiusChange(radius);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full flex items-center justify-between px-3 py-2 border rounded-lg text-sm
          ${disabled 
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200' 
            : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent'
          }
        `}
      >
        <div className="flex items-center">
          <MapPin className={`h-4 w-4 mr-2 ${disabled ? 'text-gray-400' : 'text-purple-500'}`} />
          <span>
            {disabled 
              ? 'Select location first' 
              : selectedOption 
                ? `Within ${selectedOption.label}` 
                : 'Select radius'
            }
          </span>
        </div>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''} ${disabled ? 'text-gray-400' : 'text-gray-500'}`} />
      </button>

      {isOpen && !disabled && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
            {RADIUS_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleRadiusSelect(option.value)}
                className={`
                  w-full px-3 py-2 text-left text-sm hover:bg-purple-50 transition-colors
                  ${selectedRadius === option.value 
                    ? 'bg-purple-50 text-purple-700 font-medium' 
                    : 'text-gray-700'
                  }
                `}
              >
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-purple-500" />
                  <span>Within {option.label}</span>
                  {selectedRadius === option.value && (
                    <span className="ml-auto text-purple-600">✓</span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}

/**
 * Hook for managing location radius state
 */
export function useLocationRadius(initialRadius: number = 10) {
  const [radius, setRadius] = useState(initialRadius);
  const [location, setLocation] = useState<{ lat: number; lng: number; name: string } | null>(null);

  const updateLocation = (newLocation: { lat: number; lng: number; name: string }) => {
    setLocation(newLocation);
  };

  const updateRadius = (newRadius: number) => {
    setRadius(newRadius);
  };

  const clearLocation = () => {
    setLocation(null);
  };

  return {
    radius,
    location,
    updateLocation,
    updateRadius,
    clearLocation,
    hasLocation: !!location
  };
}
