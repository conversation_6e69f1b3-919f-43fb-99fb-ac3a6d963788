import React from 'react';
import { Helmet } from 'react-helmet-async';

interface GoogleAnalyticsProps {
  trackingId?: string;
}

/**
 * Google Analytics component for tracking website performance
 * Add your Google Analytics tracking ID to enable analytics
 */
export default function GoogleAnalytics({ trackingId }: GoogleAnalyticsProps) {
  // Use environment variable or prop
  const gaTrackingId = trackingId || import.meta.env.VITE_GA_TRACKING_ID;

  if (!gaTrackingId) {
    console.warn('Google Analytics tracking ID not found. Add VITE_GA_TRACKING_ID to your environment variables.');
    return null;
  }

  return (
    <Helmet>
      {/* Google Analytics */}
      <script async src={`https://www.googletagmanager.com/gtag/js?id=${gaTrackingId}`} />
      <script>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${gaTrackingId}', {
            page_title: document.title,
            page_location: window.location.href,
            send_page_view: true
          });
        `}
      </script>
    </Helmet>
  );
}

/**
 * Track custom events with Google Analytics
 */
export function trackEvent(eventName: string, parameters: Record<string, any> = {}) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      event_category: parameters.category || 'engagement',
      event_label: parameters.label,
      value: parameters.value,
      ...parameters
    });
  }
}

/**
 * Track page views manually (for SPA navigation)
 */
export function trackPageView(path: string, title?: string) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', import.meta.env.VITE_GA_TRACKING_ID, {
      page_path: path,
      page_title: title || document.title,
      page_location: window.location.origin + path
    });
  }
}

/**
 * Track venue searches for SEO insights
 */
export function trackVenueSearch(searchTerm: string, location?: string, filters?: any) {
  trackEvent('venue_search', {
    category: 'search',
    label: searchTerm,
    search_term: searchTerm,
    location: location,
    filters: JSON.stringify(filters)
  });
}

/**
 * Track venue views for popularity insights
 */
export function trackVenueView(venueId: string, venueName: string, location: string) {
  trackEvent('venue_view', {
    category: 'venue',
    label: venueName,
    venue_id: venueId,
    venue_name: venueName,
    venue_location: location
  });
}

/**
 * Track booking attempts for conversion analysis
 */
export function trackBookingAttempt(venueId: string, venueName: string, amount: number) {
  trackEvent('booking_attempt', {
    category: 'booking',
    label: venueName,
    value: amount,
    venue_id: venueId,
    venue_name: venueName,
    booking_amount: amount
  });
}

/**
 * Track successful bookings for revenue analysis
 */
export function trackBookingSuccess(venueId: string, venueName: string, amount: number, bookingId: string) {
  trackEvent('purchase', {
    transaction_id: bookingId,
    value: amount,
    currency: 'AUD',
    items: [{
      item_id: venueId,
      item_name: venueName,
      category: 'venue_rental',
      quantity: 1,
      price: amount
    }]
  });
}

/**
 * Track host signups for growth analysis
 */
export function trackHostSignup(method: string = 'email') {
  trackEvent('sign_up', {
    category: 'host',
    method: method,
    user_type: 'host'
  });
}

/**
 * Track customer signups
 */
export function trackCustomerSignup(method: string = 'email') {
  trackEvent('sign_up', {
    category: 'customer',
    method: method,
    user_type: 'customer'
  });
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}
