import React, { useState, useEffect } from 'react';
import { DollarSign, Info, ChevronDown, ChevronUp, Calendar, Clock } from 'lucide-react';
import { stripePaymentService, PriceCalculation } from '../../services/stripe-payment';

interface PriceSummaryProps {
  pricePerHour: number;
  hours: number;
  serviceFee?: number;
  isMultiDay?: boolean;
  totalDays?: number;
  isWeekend?: boolean;
  isHoliday?: boolean;
  isLateNight?: boolean;
  additionalSurcharge?: number;
  discountPercentage?: number;
  venue?: any; // Add venue prop to access pricing configuration
}

export default function PriceSummary({
  pricePerHour,
  hours,
  serviceFee = 50,
  isMultiDay = false,
  totalDays = 1,
  isWeekend = false,
  isHoliday = false,
  isLateNight = false,
  additionalSurcharge = 0,
  discountPercentage = 0,
  venue
}: PriceSummaryProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);

  // Calculate price using the Stripe payment service with venue-specific pricing
  useEffect(() => {
    if (hours > 0 && pricePerHour > 0) {
      // For multi-day bookings, calculate based on days
      const effectiveHours = isMultiDay ? totalDays * 24 : hours;

      // Extract venue pricing configuration from venue data
      const venuePricingConfig = venue?.pricing ? {
        progressivePricing: venue.pricing.progressivePricing,
        surcharges: venue.pricing.surcharges
      } : undefined;

      const calculation = stripePaymentService.calculatePrice(
        pricePerHour,
        effectiveHours,
        {
          isWeekend,
          isHoliday,
          isLateNight,
          isOvertime: effectiveHours > 12, // Consider 12+ hours as overtime
          additionalSurcharge,
          discountPercentage,
          venuePricingConfig
        }
      );

      setPriceCalculation(calculation);
    }
  }, [pricePerHour, hours, isMultiDay, totalDays, isWeekend, isHoliday, isLateNight, additionalSurcharge, discountPercentage, venue]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <DollarSign className="h-5 w-5 text-purple-500 mr-1" />
          Price Details
        </h3>
        <button
          type="button"
          onClick={() => setShowDetails(!showDetails)}
          className="text-purple-600 hover:text-purple-800 flex items-center text-sm font-medium"
        >
          {showDetails ? (
            <>
              Hide details
              <ChevronUp className="h-4 w-4 ml-1" />
            </>
          ) : (
            <>
              Show details
              <ChevronDown className="h-4 w-4 ml-1" />
            </>
          )}
        </button>
      </div>

      {showDetails && priceCalculation && (
        <div className="space-y-3 mb-4 bg-gray-50 p-4 rounded-lg">
          {/* Base price */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              {isMultiDay ? (
                <span className="text-gray-600">
                  <Clock className="h-4 w-4 inline mr-1" />
                  {formatCurrency(priceCalculation.hourlyRate)} × {totalDays * 24} hours ({totalDays} day{totalDays !== 1 ? 's' : ''})
                </span>
              ) : (
                <span className="text-gray-600">
                  <Clock className="h-4 w-4 inline mr-1" />
                  {formatCurrency(priceCalculation.hourlyRate)} × {hours} hour{hours !== 1 ? 's' : ''}
                </span>
              )}
              <div className="group relative ml-1">
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                  {isMultiDay
                    ? `Daily rate (${formatCurrency(priceCalculation.hourlyRate * 24)}) multiplied by the number of days booked`
                    : `Progressive hourly rate based on booking duration. Original rate: ${formatCurrency(priceCalculation.basePrice)}`
                  }
                </div>
              </div>
            </div>
            <span className="font-medium">{formatCurrency(priceCalculation.subtotal)}</span>
          </div>

          {/* Surcharges */}
          {priceCalculation.surcharges.weekend > 0 && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-gray-600">Weekend surcharge</span>
                <div className="group relative ml-1">
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                    Additional fee for weekend bookings
                  </div>
                </div>
              </div>
              <span className="font-medium">{formatCurrency(priceCalculation.surcharges.weekend)}</span>
            </div>
          )}

          {priceCalculation.surcharges.holiday > 0 && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-gray-600">Holiday surcharge</span>
                <div className="group relative ml-1">
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                    Additional fee for holiday bookings
                  </div>
                </div>
              </div>
              <span className="font-medium">{formatCurrency(priceCalculation.surcharges.holiday)}</span>
            </div>
          )}

          {priceCalculation.surcharges.lateNight > 0 && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-gray-600">Late night surcharge</span>
                <div className="group relative ml-1">
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                    Additional fee for bookings after 10pm
                  </div>
                </div>
              </div>
              <span className="font-medium">{formatCurrency(priceCalculation.surcharges.lateNight)}</span>
            </div>
          )}

          {priceCalculation.surcharges.additional > 0 && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-gray-600">Additional surcharge</span>
                <div className="group relative ml-1">
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                    Additional fees for special services
                  </div>
                </div>
              </div>
              <span className="font-medium">{formatCurrency(priceCalculation.surcharges.additional)}</span>
            </div>
          )}

          {/* Discount */}
          {priceCalculation.discount > 0 && (
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="text-green-600">Discount</span>
                <div className="group relative ml-1">
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                    Special discount applied to your booking
                  </div>
                </div>
              </div>
              <span className="font-medium text-green-600">-{formatCurrency(priceCalculation.discount)}</span>
            </div>
          )}

          {/* Platform fee */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="text-gray-600">Platform fee</span>
              <div className="group relative ml-1">
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                  This helps us run our platform and provide services like 24/7 support
                </div>
              </div>
            </div>
            <span className="font-medium">{formatCurrency(priceCalculation.platformFee)}</span>
          </div>

          {/* Payment processing fee */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="text-gray-600">Payment processing</span>
              <div className="group relative ml-1">
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                  Fee charged by our payment processor
                </div>
              </div>
            </div>
            <span className="font-medium">{formatCurrency(priceCalculation.processingFee)}</span>
          </div>

          <div className="border-t border-gray-200 pt-3 mt-3"></div>
        </div>
      )}

      <div className="flex justify-between items-center text-lg font-bold">
        <span>Total</span>
        <span className="text-purple-700">
          {priceCalculation ? formatCurrency(priceCalculation.total) : 'Calculating...'}
        </span>
      </div>

      {!showDetails && (
        <div className="text-sm text-gray-500 mt-2">
          {priceCalculation ? (
            isMultiDay && totalDays > 0 ? (
              <span>
                {formatCurrency(priceCalculation.hourlyRate)} × {totalDays * 24} hours ({totalDays} day{totalDays !== 1 ? 's' : ''})
                {Object.values(priceCalculation.surcharges).some(v => v > 0) && ' + surcharges'}
                {priceCalculation.discount > 0 && ' - discount'}
                {' + fees'}
              </span>
            ) : hours > 0 ? (
              <span>
                {formatCurrency(priceCalculation.hourlyRate)} × {hours} hour{hours !== 1 ? 's' : ''}
                {Object.values(priceCalculation.surcharges).some(v => v > 0) && ' + surcharges'}
                {priceCalculation.discount > 0 && ' - discount'}
                {' + fees'}
              </span>
            ) : (
              <span>Select date and time to see total price</span>
            )
          ) : (
            <span>Select date and time to see total price</span>
          )}
        </div>
      )}
    </div>
  );
}