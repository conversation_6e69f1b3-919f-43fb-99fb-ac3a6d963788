/**
 * API route for the Host Assistant
 */

import { createAdvancedAgent, generateResponse } from '../../ai-agents/host-acquisition/advanced-agent';

// Store sessions in memory (in production, use a database)
const sessions = new Map();

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, sessionId } = req.body;

    // Validate message
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get or create session
    let session;
    if (sessionId && sessions.has(sessionId)) {
      session = sessions.get(sessionId);
    } else {
      // Create a new session with a host assistant
      session = {
        id: Math.random().toString(36).substring(2, 15),
        agent: await createAdvancedAgent(),
      };
      sessions.set(session.id, session);
    }

    // Generate response
    const response = await generateResponse(message, session.agent);

    // Return response
    return res.status(200).json({
      response,
      sessionId: session.id,
    });
  } catch (error) {
    console.error('Error in host assistant API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
