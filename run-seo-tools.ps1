# HouseGoing SEO Fix Tools PowerShell Script

Write-Host "===== HouseGoing SEO Fix Tools =====" -ForegroundColor Cyan
Write-Host

Write-Host "Step 1: Installing required packages..." -ForegroundColor Green
Push-Location scripts
npm install
Pop-Location
Write-Host "Dependencies installed." -ForegroundColor Green
Write-Host

Write-Host "Step 2: Running SEO analysis and fix scripts..." -ForegroundColor Green
Write-Host
Write-Host "2.1: Fixing canonical tag issues..." -ForegroundColor Yellow
node scripts/canonical-fix.js
Write-Host

Write-Host "2.2: Identifying soft 404 pages..." -ForegroundColor Yellow
node scripts/soft-404-fixer.js
Write-Host

Write-Host "2.3: Analyzing redirect chains..." -ForegroundColor Yellow
node scripts/redirect-chain-analyzer.js
Write-Host

Write-Host "===== Analysis Complete =====" -ForegroundColor Cyan
Write-Host
Write-Host "Three reports have been generated:" -ForegroundColor Green
Write-Host " - canonical-fix-report.md" -ForegroundColor White
Write-Host " - soft-404-fix-report.md" -ForegroundColor White
Write-Host " - redirect-chain-report.md" -ForegroundColor White
Write-Host
Write-Host "Please review these reports and implement the suggested changes." -ForegroundColor Yellow
Write-Host
Write-Host "Next Steps:" -ForegroundColor Green
Write-Host "1. Fix identified issues" -ForegroundColor White
Write-Host "2. Build your site with: npm run build:prod" -ForegroundColor White
Write-Host "3. Deploy the updated site" -ForegroundColor White
Write-Host "4. Submit your sitemap in Google Search Console" -ForegroundColor White
Write-Host

Read-Host "Press Enter to exit..."
