{"to": "<EMAIL>", "subject": "HouseGoing Email Test - Messaging System", "html": "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px;'><h2 style='color: #7c3aed; margin-bottom: 20px;'>🎉 HouseGoing Email System Test</h2><p>Hi <PERSON>,</p><p>This is a test email from the HouseGoing messaging system to verify that our email notifications are working correctly!</p><div style='background: #f8fafc; padding: 20px; border-left: 4px solid #7c3aed; margin: 20px 0; border-radius: 4px;'><p style='margin: 0; font-style: italic;'>✅ <strong>Email Service:</strong> Render deployment working<br>✅ <strong>SMTP:</strong> Gmail integration active<br>✅ <strong>Templates:</strong> HTML formatting working<br>✅ <strong>Messaging:</strong> Ready for real-time notifications</p></div><p>Key features now available:</p><ul><li><strong>Real-time messaging</strong> between guests and hosts</li><li><strong>Email notifications</strong> for new messages</li><li><strong>Booking integration</strong> with venue context</li><li><strong>Mobile-responsive</strong> chat interface</li></ul><p>The messaging system is now fully operational and ready for testing!</p><a href='https://housegoing.com.au/messages' style='background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;'>Test Messaging System</a><p style='color: #64748b; font-size: 14px; margin-top: 30px; border-top: 1px solid #e0e0e0; padding-top: 20px;'>This is a test email from the HouseGoing platform. The messaging system is now live and ready for use!</p></div>", "text": "HouseGoing Email System Test - Hi Tom, This is a test email from the HouseGoing messaging system to verify that our email notifications are working correctly! The messaging system is now fully operational with real-time messaging, email notifications, booking integration, and mobile-responsive chat interface. Test it at: https://housegoing.com.au/messages", "from": "<EMAIL>", "fromName": "HouseGoing Platform"}