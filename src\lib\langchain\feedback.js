import { supabase } from '../supabase';

/**
 * Stores feedback for an AI assistant response
 * @param {Object} feedbackData - The feedback data
 * @param {string} feedbackData.messageId - ID of the message being rated
 * @param {string} feedbackData.conversationId - ID of the conversation
 * @param {string} feedbackData.userMessage - The user's message
 * @param {string} feedbackData.assistantResponse - The assistant's response
 * @param {string} feedbackData.rating - The rating ('good' or 'bad')
 * @param {string} feedbackData.notes - Additional notes about the feedback
 * @param {string} feedbackData.agentType - Type of agent (sales, host, etc.)
 * @returns {Promise<Object>} - Result of the operation
 */
export async function storeFeedback(feedbackData) {
  try {
    // Insert feedback into Supabase
    const { data, error } = await supabase
      .from('ai_feedback')
      .insert([
        {
          message_id: feedbackData.messageId,
          conversation_id: feedbackData.conversationId,
          user_message: feedbackData.userMessage,
          assistant_response: feedbackData.assistantResponse,
          rating: feedbackData.rating,
          notes: feedbackData.notes,
          agent_type: feedbackData.agentType,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Error storing feedback:', error);
      return { success: false, error };
    }

    // After storing feedback, update the prompt template if needed
    if (feedbackData.rating === 'bad' && feedbackData.notes) {
      await updatePromptTemplate(feedbackData);
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in storeFeedback:', error);
    return { success: false, error };
  }
}

/**
 * Updates the prompt template based on feedback
 * @param {Object} feedbackData - The feedback data
 */
async function updatePromptTemplate(feedbackData) {
  try {
    // Get the current prompt template for this agent type
    const { data: promptData, error: promptError } = await supabase
      .from('ai_prompts')
      .select('*')
      .eq('agent_type', feedbackData.agentType)
      .single();

    if (promptError) {
      console.error('Error fetching prompt template:', promptError);
      return;
    }

    // If no prompt template exists, create one
    if (!promptData) {
      // Create a new prompt template with the feedback
      const { error: insertError } = await supabase
        .from('ai_prompts')
        .insert([
          {
            agent_type: feedbackData.agentType,
            prompt_template: getDefaultPrompt(feedbackData.agentType),
            feedback_notes: [feedbackData.notes],
            updated_at: new Date().toISOString()
          }
        ]);

      if (insertError) {
        console.error('Error creating prompt template:', insertError);
      }
      return;
    }

    // Update the existing prompt template with the feedback
    const updatedFeedbackNotes = [
      ...(promptData.feedback_notes || []),
      feedbackData.notes
    ];

    const { error: updateError } = await supabase
      .from('ai_prompts')
      .update({
        feedback_notes: updatedFeedbackNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', promptData.id);

    if (updateError) {
      console.error('Error updating prompt template:', updateError);
    }
  } catch (error) {
    console.error('Error in updatePromptTemplate:', error);
  }
}

/**
 * Gets all feedback for a specific agent type
 * @param {string} agentType - Type of agent (sales, host, etc.)
 * @returns {Promise<Array>} - Array of feedback items
 */
export async function getFeedbackByAgentType(agentType) {
  try {
    const { data, error } = await supabase
      .from('ai_feedback')
      .select('*')
      .eq('agent_type', agentType)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching feedback:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getFeedbackByAgentType:', error);
    return [];
  }
}

/**
 * Gets the current prompt template for an agent type
 * @param {string} agentType - Type of agent (sales, host, etc.)
 * @returns {Promise<string>} - The prompt template
 */
export async function getPromptTemplate(agentType) {
  try {
    const { data, error } = await supabase
      .from('ai_prompts')
      .select('*')
      .eq('agent_type', agentType)
      .single();

    if (error) {
      console.error('Error fetching prompt template:', error);
      return getDefaultPrompt(agentType);
    }

    return data?.prompt_template || getDefaultPrompt(agentType);
  } catch (error) {
    console.error('Error in getPromptTemplate:', error);
    return getDefaultPrompt(agentType);
  }
}

/**
 * Updates the prompt template for an agent type
 * @param {string} agentType - Type of agent (sales, host, etc.)
 * @param {string} promptTemplate - The new prompt template
 * @returns {Promise<Object>} - Result of the operation
 */
export async function updatePromptTemplateDirectly(agentType, promptTemplate) {
  try {
    // Check if a prompt template already exists
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('ai_prompts')
      .select('*')
      .eq('agent_type', agentType)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error fetching existing prompt:', fetchError);
      return { success: false, error: fetchError };
    }

    if (existingPrompt) {
      // Update existing prompt
      const { error: updateError } = await supabase
        .from('ai_prompts')
        .update({
          prompt_template: promptTemplate,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingPrompt.id);

      if (updateError) {
        console.error('Error updating prompt template:', updateError);
        return { success: false, error: updateError };
      }
    } else {
      // Create new prompt
      const { error: insertError } = await supabase
        .from('ai_prompts')
        .insert([
          {
            agent_type: agentType,
            prompt_template: promptTemplate,
            feedback_notes: [],
            updated_at: new Date().toISOString()
          }
        ]);

      if (insertError) {
        console.error('Error creating prompt template:', insertError);
        return { success: false, error: insertError };
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updatePromptTemplateDirectly:', error);
    return { success: false, error };
  }
}

/**
 * Gets the default prompt for an agent type
 * @param {string} agentType - Type of agent (sales, host, etc.)
 * @returns {string} - The default prompt
 */
function getDefaultPrompt(agentType) {
  switch (agentType) {
    case 'sales':
      return `
HouseGoing Assistant Prompt - Proactive Venue Search
Core Behavior
You are Alex, the helpful and efficient HouseGoing Assistant. Your primary purpose is to help users find and book the perfect venue for their events with minimal friction and maximum satisfaction.
Key Principles
Show Before You Ask: When a user provides enough initial information to conduct a search (location, date/timeframe, and event type/size), IMMEDIATELY show venue options before asking additional questions.
Extract Information Proactively: Parse user messages for key booking criteria (location, date, party size, event type, BYO preferences) without explicitly asking for each piece.
Progressive Disclosure: Only ask for additional information AFTER showing initial venue options.
Minimize Conversation Steps: Aim to show relevant venues within 1-2 conversation turns.

Search Trigger Conditions
Immediately search and display venue options when the user has provided AT MINIMUM:
A location (city, suburb, or area)
AND EITHER a timeframe OR event type

Response Structure When User Provides Sufficient Information
Great! Based on what you've shared, here are some venues in [LOCATION] that might work for your [EVENT TYPE]:


[3-5 VENUE OPTIONS WITH KEY DETAILS]
- [Venue Name 1]: [Brief description, capacity, key features, price range]
- [Venue Name 2]: [Brief description, capacity, key features, price range]
- [Venue Name 3]: [Brief description, capacity, key features, price range]


Would you like to:
1. See more details about any of these options?
2. Refine your search with more specific requirements?
3. See more venue options?


Follow-up Questions (ONLY AFTER showing venue options)
After showing venues, you can then ask 1-2 targeted questions to refine the search, such as:
"Do you have a specific budget range in mind?"
"Any particular venue features you're looking for?"
"Would you prefer indoor or outdoor spaces?"

Initial User Assessment
For the FIRST message only, quickly assess what information is provided and respond accordingly:
If minimal info (just "hi"): Friendly greeting + quick prompt for event details
If partial info: Acknowledge what's provided + ask for only crucial missing information
If sufficient info: Immediately show venue options matching criteria

Handling Specific Scenarios
When user provides location and date/timeframe: Immediately show venue options in that area for that timeframe.
When user mentions special requirements (BYO, capacity, etc.): Highlight venues that specifically match these requirements.
When user is vague: Show diverse venue options that cover different styles/budgets, then ask for preferences.

Example Flows
Good Flow (Proactive)
User: "I want to book for my bachelor party, ideally we got 50 people coming, in the venue, byo alcohol allowed"
Assistant: "Great! Here are some venues that allow BYO alcohol and can accommodate 50 people for your bachelor party:
[VENUE LIST]
Could you let me know which area you'd prefer and when you're planning to have the event so I can refine these options further?"
Good Flow (With Location)
User: "At sydney, around late november"
Assistant: "Perfect! Here are some venues in Sydney available in late November that allow BYO alcohol and can fit 50 people for your bachelor party:
[VENUE LIST]
Would you like more information about any of these venues? Or would you prefer something with specific features like outdoor space or a particular style?"
`;
    case 'host':
      return `
You are the HouseGoing Host Assistant, designed to help venue hosts optimize their listings, manage bookings, and maximize their earnings.

Your primary goals are to:
1. Help hosts create attractive and complete venue listings
2. Provide actionable advice on pricing strategy
3. Assist with booking management and guest communication
4. Share best practices for venue preparation and hosting
5. Help troubleshoot common hosting issues

When responding to hosts:
- Be practical and business-focused
- Provide specific, actionable advice
- Reference HouseGoing's hosting policies and best practices
- Highlight features that can help the host earn more
- Be encouraging but realistic about hosting expectations

Key areas of expertise:
- Venue listing optimization (photos, descriptions, amenities)
- Pricing strategy (hourly rates, special event pricing)
- Booking management (calendar, availability, cancellations)
- Guest communication (pre-booking, during stay, post-booking)
- Reviews and ratings (how to earn great reviews)
- Hosting regulations and requirements

When hosts ask about specific features or settings, provide step-by-step instructions on how to use them in the HouseGoing platform.
`;
    default:
      return 'You are a helpful assistant for HouseGoing, a platform for finding and booking venues for events and parties.';
  }
}
