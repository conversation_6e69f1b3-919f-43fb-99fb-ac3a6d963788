/**
 * NSW Planning Map Lookup Tool
 * 
 * A comprehensive tool for looking up zoning and council information
 * across New South Wales using official government data services.
 */

// Configuration
const CONFIG = {
  // Mapbox configuration
  mapbox: {
    accessToken: 'pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA',
    styleStreets: 'mapbox://styles/mapbox/streets-v11',
    styleSatellite: 'mapbox://styles/mapbox/satellite-streets-v11'
  },
  
  // Maps.co Geocoding API
  geocoding: {
    apiKey: '67ea5a7e84615836239135wpc5a6d73',
    baseUrl: 'https://geocode.maps.co'
  },
  
  // NSW Planning Portal services
  services: {
    // Zoning services
    zoning: {
      wmsUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer',
      wfsUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer',
      capabilitiesUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer?service=WMS&request=GetCapabilities',
      // Layer IDs based on the GetCapabilities response
      layers: {
        landApplication: 0,
        heightOfBuilding: 1,
        lotSize: 2,
        landReservation: 3,
        landZoning: 4,
        floorSpaceRatio: 5,
        heritage: 6
      }
    },
    
    // Administrative boundaries services
    admin: {
      wmsUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer',
      wfsUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer',
      capabilitiesUrl: 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer?service=WMS&request=GetCapabilities',
      // Layer IDs based on the GetCapabilities response
      layers: {
        statisticalRegion: 0,
        sa1: 1,
        sa2: 2,
        sa3: 3,
        sa4: 4,
        stateBorder: 5,
        parish: 6,
        county: 7,
        electoralDistrict: 8,
        lga: 9,
        suburb: 10
      }
    }
  },
  
  // Default map view (centered on NSW)
  defaultView: {
    lat: -33.8688,
    lng: 151.2093,
    zoom: 6
  },
  
  // Proxy server for handling CORS issues with WFS
  // If you have a proxy server, configure it here
  proxy: {
    enabled: false,
    url: '/api/proxy'
  }
};

// Main application class
class NSWPlanningMap {
  constructor() {
    // DOM elements
    this.elements = {
      map: document.getElementById('map'),
      addressInput: document.getElementById('address-input'),
      searchButton: document.getElementById('search-button'),
      errorMessage: document.getElementById('error-message'),
      resultsContainer: document.getElementById('results-container'),
      resultAddress: document.getElementById('result-address'),
      resultCoordinates: document.getElementById('result-coordinates'),
      resultZone: document.getElementById('result-zone'),
      resultZoneName: document.getElementById('result-zone-name'),
      resultCouncil: document.getElementById('result-council'),
      loadingIndicator: document.getElementById('loading-indicator'),
      zoningLayers: document.getElementById('zoning-layers'),
      adminLayers: document.getElementById('admin-layers'),
      basemapStreets: document.getElementById('basemap-streets'),
      basemapSatellite: document.getElementById('basemap-satellite')
    };
    
    // Map and layer references
    this.map = null;
    this.basemaps = {};
    this.zoningWmsLayers = {};
    this.adminWmsLayers = {};
    this.marker = null;
    
    // Initialize the application
    this.init();
  }
  
  /**
   * Initialize the application
   */
  init() {
    // Initialize the map
    this.initMap();
    
    // Initialize event listeners
    this.initEventListeners();
    
    // Initialize WMS layers
    this.initWmsLayers();
  }
  
  /**
   * Initialize the Leaflet map
   */
  initMap() {
    // Create the map
    this.map = L.map(this.elements.map, {
      center: [CONFIG.defaultView.lat, CONFIG.defaultView.lng],
      zoom: CONFIG.defaultView.zoom,
      zoomControl: true,
      attributionControl: true
    });
    
    // Add basemaps
    this.basemaps.streets = L.tileLayer(`https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=${CONFIG.mapbox.accessToken}`, {
      attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
      maxZoom: 19
    }).addTo(this.map);
    
    this.basemaps.satellite = L.tileLayer(`https://api.mapbox.com/styles/v1/mapbox/satellite-streets-v11/tiles/{z}/{x}/{y}?access_token=${CONFIG.mapbox.accessToken}`, {
      attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
      maxZoom: 19
    });
    
    // Add scale control
    L.control.scale({
      imperial: false,
      position: 'bottomright'
    }).addTo(this.map);
    
    // Add click handler for spatial queries
    this.map.on('click', (e) => {
      this.handleMapClick(e.latlng);
    });
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Search button click
    this.elements.searchButton.addEventListener('click', () => {
      this.handleSearch();
    });
    
    // Address input enter key
    this.elements.addressInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.handleSearch();
      }
    });
    
    // Basemap radio buttons
    this.elements.basemapStreets.addEventListener('change', () => {
      this.toggleBasemap('streets');
    });
    
    this.elements.basemapSatellite.addEventListener('change', () => {
      this.toggleBasemap('satellite');
    });
  }
  
  /**
   * Initialize WMS layers from the NSW Planning Portal
   */
  initWmsLayers() {
    // Initialize zoning WMS layers
    this.initZoningWmsLayers();
    
    // Initialize administrative boundaries WMS layers
    this.initAdminWmsLayers();
  }
  
  /**
   * Initialize zoning WMS layers
   */
  initZoningWmsLayers() {
    // Clear existing content
    this.elements.zoningLayers.innerHTML = '';
    
    // Create WMS layers for each zoning layer
    const zoningLayers = CONFIG.services.zoning.layers;
    const wmsUrl = CONFIG.services.zoning.wmsUrl;
    
    // Define layer colors and labels
    const layerConfig = {
      [zoningLayers.landApplication]: { color: 'rgba(200, 200, 200, 0.5)', label: 'Land Application' },
      [zoningLayers.heightOfBuilding]: { color: 'rgba(255, 255, 150, 0.7)', label: 'Height of Building' },
      [zoningLayers.lotSize]: { color: 'rgba(150, 255, 150, 0.7)', label: 'Lot Size' },
      [zoningLayers.landReservation]: { color: 'rgba(255, 150, 150, 0.7)', label: 'Land Reservation' },
      [zoningLayers.landZoning]: { color: 'rgba(255, 200, 200, 0.7)', label: 'Land Zoning' },
      [zoningLayers.floorSpaceRatio]: { color: 'rgba(150, 150, 255, 0.7)', label: 'Floor Space Ratio' },
      [zoningLayers.heritage]: { color: 'rgba(255, 150, 255, 0.7)', label: 'Heritage' }
    };
    
    // Create layers and controls
    Object.entries(zoningLayers).forEach(([key, layerId]) => {
      const config = layerConfig[layerId];
      
      // Create WMS layer
      const wmsLayer = L.tileLayer.wms(wmsUrl, {
        layers: layerId.toString(),
        format: 'image/png',
        transparent: true,
        opacity: 0.7
      });
      
      // Store reference to the layer
      this.zoningWmsLayers[key] = wmsLayer;
      
      // Create layer control
      const layerItem = document.createElement('div');
      layerItem.className = 'layer-item';
      layerItem.innerHTML = `
        <input type="checkbox" id="zoning-layer-${layerId}" ${key === 'landZoning' ? 'checked' : ''}>
        <label for="zoning-layer-${layerId}">
          <span class="color-box" style="background-color: ${config.color};"></span>
          ${config.label}
        </label>
      `;
      
      // Add to DOM
      this.elements.zoningLayers.appendChild(layerItem);
      
      // Add event listener
      const checkbox = layerItem.querySelector(`#zoning-layer-${layerId}`);
      checkbox.addEventListener('change', (e) => {
        if (e.target.checked) {
          wmsLayer.addTo(this.map);
        } else {
          this.map.removeLayer(wmsLayer);
        }
      });
      
      // Add Land Zoning layer by default
      if (key === 'landZoning') {
        wmsLayer.addTo(this.map);
      }
    });
  }
  
  /**
   * Initialize administrative boundaries WMS layers
   */
  initAdminWmsLayers() {
    // Clear existing content
    this.elements.adminLayers.innerHTML = '';
    
    // Create WMS layers for each admin layer
    const adminLayers = CONFIG.services.admin.layers;
    const wmsUrl = CONFIG.services.admin.wmsUrl;
    
    // Define layer colors and labels
    const layerConfig = {
      [adminLayers.statisticalRegion]: { color: 'rgba(200, 200, 255, 0.5)', label: 'Statistical Regions' },
      [adminLayers.sa1]: { color: 'rgba(200, 255, 200, 0.5)', label: 'Statistical Area Level 1' },
      [adminLayers.sa2]: { color: 'rgba(255, 200, 200, 0.5)', label: 'Statistical Area Level 2' },
      [adminLayers.sa3]: { color: 'rgba(255, 255, 200, 0.5)', label: 'Statistical Area Level 3' },
      [adminLayers.sa4]: { color: 'rgba(200, 255, 255, 0.5)', label: 'Statistical Area Level 4' },
      [adminLayers.stateBorder]: { color: 'rgba(255, 0, 0, 0.5)', label: 'NSW State Border' },
      [adminLayers.parish]: { color: 'rgba(200, 200, 200, 0.5)', label: 'Parish' },
      [adminLayers.county]: { color: 'rgba(150, 150, 150, 0.5)', label: 'County' },
      [adminLayers.electoralDistrict]: { color: 'rgba(255, 200, 150, 0.5)', label: 'Electoral Districts' },
      [adminLayers.lga]: { color: 'rgba(200, 200, 255, 0.7)', label: 'Local Government Areas' },
      [adminLayers.suburb]: { color: 'rgba(255, 255, 200, 0.7)', label: 'Suburbs' }
    };
    
    // Create layers and controls
    Object.entries(adminLayers).forEach(([key, layerId]) => {
      const config = layerConfig[layerId];
      
      // Create WMS layer
      const wmsLayer = L.tileLayer.wms(wmsUrl, {
        layers: layerId.toString(),
        format: 'image/png',
        transparent: true,
        opacity: 0.7
      });
      
      // Store reference to the layer
      this.adminWmsLayers[key] = wmsLayer;
      
      // Create layer control
      const layerItem = document.createElement('div');
      layerItem.className = 'layer-item';
      layerItem.innerHTML = `
        <input type="checkbox" id="admin-layer-${layerId}" ${key === 'lga' ? 'checked' : ''}>
        <label for="admin-layer-${layerId}">
          <span class="color-box" style="background-color: ${config.color};"></span>
          ${config.label}
        </label>
      `;
      
      // Add to DOM
      this.elements.adminLayers.appendChild(layerItem);
      
      // Add event listener
      const checkbox = layerItem.querySelector(`#admin-layer-${layerId}`);
      checkbox.addEventListener('change', (e) => {
        if (e.target.checked) {
          wmsLayer.addTo(this.map);
        } else {
          this.map.removeLayer(wmsLayer);
        }
      });
      
      // Add LGA layer by default
      if (key === 'lga') {
        wmsLayer.addTo(this.map);
      }
    });
  }
  
  /**
   * Handle address search
   */
  async handleSearch() {
    const address = this.elements.addressInput.value.trim();
    
    if (!address) {
      this.showError('Please enter an address to search');
      return;
    }
    
    this.showLoading(true);
    this.hideError();
    
    try {
      // Geocode the address
      const geocodeResult = await this.geocodeAddress(address);
      
      if (!geocodeResult) {
        throw new Error('No results found for this address');
      }
      
      // Update the map
      this.updateMapForLocation(geocodeResult);
      
      // Perform spatial queries
      await this.performSpatialQueries(geocodeResult.lat, geocodeResult.lng);
      
      // Show results
      this.showResults({
        address: geocodeResult.displayName,
        lat: geocodeResult.lat,
        lng: geocodeResult.lng
      });
    } catch (error) {
      console.error('Search error:', error);
      this.showError(error.message || 'Failed to search for this address');
      this.hideResults();
    } finally {
      this.showLoading(false);
    }
  }
  
  /**
   * Handle map click
   */
  async handleMapClick(latlng) {
    this.showLoading(true);
    this.hideError();
    
    try {
      // Update marker
      this.updateMarker(latlng.lat, latlng.lng);
      
      // Perform reverse geocoding
      const address = await this.reverseGeocode(latlng.lat, latlng.lng);
      
      // Perform spatial queries
      await this.performSpatialQueries(latlng.lat, latlng.lng);
      
      // Show results
      this.showResults({
        address: address || `Location at ${latlng.lat.toFixed(5)}, ${latlng.lng.toFixed(5)}`,
        lat: latlng.lat,
        lng: latlng.lng
      });
    } catch (error) {
      console.error('Map click error:', error);
      this.showError(error.message || 'Failed to get information for this location');
    } finally {
      this.showLoading(false);
    }
  }
  
  /**
   * Geocode an address using Maps.co API
   */
  async geocodeAddress(address) {
    try {
      // Add NSW, Australia to the search query if not already specified
      let searchQuery = address;
      if (!searchQuery.toLowerCase().includes('nsw') && !searchQuery.toLowerCase().includes('new south wales')) {
        searchQuery += ', NSW, Australia';
      } else if (!searchQuery.toLowerCase().includes('australia')) {
        searchQuery += ', Australia';
      }
      
      // Build the geocoding URL
      const url = `${CONFIG.geocoding.baseUrl}/search?q=${encodeURIComponent(searchQuery)}&api_key=${CONFIG.geocoding.apiKey}`;
      
      // Make the request
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Geocoding API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!data || data.length === 0) {
        throw new Error('No results found for this address');
      }
      
      // Filter results to prioritize NSW locations
      const nswResults = data.filter(item =>
        item.display_name.toLowerCase().includes('nsw') ||
        item.display_name.toLowerCase().includes('new south wales')
      );
      
      // Use NSW result if available, otherwise use the first result
      const bestResult = nswResults.length > 0 ? nswResults[0] : data[0];
      
      return {
        lat: parseFloat(bestResult.lat),
        lng: parseFloat(bestResult.lon),
        displayName: bestResult.display_name
      };
    } catch (error) {
      console.error('Geocoding error:', error);
      throw new Error('Failed to geocode address. Please try again.');
    }
  }
  
  /**
   * Reverse geocode coordinates using Maps.co API
   */
  async reverseGeocode(lat, lng) {
    try {
      // Build the reverse geocoding URL
      const url = `${CONFIG.geocoding.baseUrl}/reverse?lat=${lat}&lon=${lng}&api_key=${CONFIG.geocoding.apiKey}`;
      
      // Make the request
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Reverse geocoding API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!data || !data.display_name) {
        throw new Error('No results found for these coordinates');
      }
      
      return data.display_name;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  }
  
  /**
   * Perform spatial queries for zoning and LGA
   */
  async performSpatialQueries(lat, lng) {
    try {
      // Try different approaches to get zoning and LGA data
      const results = await Promise.allSettled([
        // Approach 1: WFS with CQL_FILTER
        this.fetchWFSWithCQL(
          CONFIG.services.zoning.wfsUrl,
          'EPI_Primary_Planning_Layers:4', // Land Zoning layer
          lng,
          lat
        ),
        this.fetchWFSWithCQL(
          CONFIG.services.admin.wfsUrl,
          'EDP_Administrative_Boundaries:9', // LGA layer
          lng,
          lat
        ),
        
        // Approach 2: WFS with XML Filter
        this.fetchWFSWithXML(
          CONFIG.services.zoning.wfsUrl,
          'EPI_Primary_Planning_Layers:4', // Land Zoning layer
          lng,
          lat
        ),
        this.fetchWFSWithXML(
          CONFIG.services.admin.wfsUrl,
          'EDP_Administrative_Boundaries:9', // LGA layer
          lng,
          lat
        ),
        
        // Approach 3: WMS GetFeatureInfo as fallback
        this.fetchWMSFeatureInfo(
          CONFIG.services.zoning.wmsUrl,
          '4', // Land Zoning layer
          lng,
          lat
        ),
        this.fetchWMSFeatureInfo(
          CONFIG.services.admin.wmsUrl,
          '9', // LGA layer
          lng,
          lat
        )
      ]);
      
      // Process zoning results
      let zoningData = null;
      for (const result of [results[0], results[2], results[4]]) {
        if (result.status === 'fulfilled' && result.value && result.value.features && result.value.features.length > 0) {
          zoningData = result.value.features[0].properties;
          break;
        }
      }
      
      // Process LGA results
      let lgaData = null;
      for (const result of [results[1], results[3], results[5]]) {
        if (result.status === 'fulfilled' && result.value && result.value.features && result.value.features.length > 0) {
          lgaData = result.value.features[0].properties;
          break;
        }
      }
      
      // Update UI with results
      if (zoningData) {
        this.elements.resultZone.textContent = zoningData.ZONE_CODE || zoningData.zone_code || '-';
        this.elements.resultZoneName.textContent = zoningData.ZONE_NAME || zoningData.zone_name || '-';
      } else {
        this.elements.resultZone.textContent = '-';
        this.elements.resultZoneName.textContent = '-';
      }
      
      if (lgaData) {
        this.elements.resultCouncil.textContent = lgaData.LGA_NAME || lgaData.lga_name || '-';
      } else {
        this.elements.resultCouncil.textContent = '-';
      }
      
      return { zoningData, lgaData };
    } catch (error) {
      console.error('Spatial query error:', error);
      throw new Error('Failed to get zoning or council information');
    }
  }
  
  /**
   * Fetch WFS data using CQL_FILTER
   */
  async fetchWFSWithCQL(serviceUrl, typeName, lng, lat) {
    try {
      console.log(`Fetching WFS with CQL_FILTER for ${typeName} at ${lng}, ${lat}`);
      
      // Build the WFS URL
      let url = new URL(serviceUrl);
      url.searchParams.append('service', 'WFS');
      url.searchParams.append('version', '1.1.0');
      url.searchParams.append('request', 'GetFeature');
      url.searchParams.append('typeNames', typeName);
      url.searchParams.append('outputFormat', 'application/json');
      url.searchParams.append('CQL_FILTER', `INTERSECTS(Shape, POINT(${lng} ${lat}))`);
      
      // Use proxy if enabled
      const requestUrl = CONFIG.proxy.enabled ? 
        `${CONFIG.proxy.url}?url=${encodeURIComponent(url.toString())}` : 
        url.toString();
      
      // Make the request
      const response = await fetch(requestUrl);
      
      if (!response.ok) {
        throw new Error(`WFS request failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error fetching WFS with CQL_FILTER for ${typeName}:`, error);
      return null;
    }
  }
  
  /**
   * Fetch WFS data using XML Filter
   */
  async fetchWFSWithXML(serviceUrl, typeName, lng, lat) {
    try {
      console.log(`Fetching WFS with XML Filter for ${typeName} at ${lng}, ${lat}`);
      
      // Build the WFS URL
      let url = new URL(serviceUrl);
      url.searchParams.append('service', 'WFS');
      url.searchParams.append('version', '1.1.0');
      url.searchParams.append('request', 'GetFeature');
      url.searchParams.append('typeNames', typeName);
      url.searchParams.append('outputFormat', 'application/json');
      
      // Create XML filter
      const filter = `
        <Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml">
          <Intersects>
            <PropertyName>Shape</PropertyName>
            <gml:Point srsName="EPSG:4326">
              <gml:coordinates>${lng},${lat}</gml:coordinates>
            </gml:Point>
          </Intersects>
        </Filter>
      `;
      
      url.searchParams.append('filter', filter);
      
      // Use proxy if enabled
      const requestUrl = CONFIG.proxy.enabled ? 
        `${CONFIG.proxy.url}?url=${encodeURIComponent(url.toString())}` : 
        url.toString();
      
      // Make the request
      const response = await fetch(requestUrl);
      
      if (!response.ok) {
        throw new Error(`WFS request failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error fetching WFS with XML Filter for ${typeName}:`, error);
      return null;
    }
  }
  
  /**
   * Fetch WMS GetFeatureInfo as a fallback
   */
  async fetchWMSFeatureInfo(serviceUrl, layerId, lng, lat) {
    try {
      console.log(`Fetching WMS GetFeatureInfo for layer ${layerId} at ${lng}, ${lat}`);
      
      // Convert geographic coordinates to pixel coordinates
      const point = this.map.latLngToContainerPoint(L.latLng(lat, lng));
      const size = this.map.getSize();
      
      // Build the GetFeatureInfo URL
      let url = new URL(serviceUrl);
      url.searchParams.append('SERVICE', 'WMS');
      url.searchParams.append('VERSION', '1.1.1');
      url.searchParams.append('REQUEST', 'GetFeatureInfo');
      url.searchParams.append('LAYERS', layerId);
      url.searchParams.append('QUERY_LAYERS', layerId);
      url.searchParams.append('STYLES', '');
      url.searchParams.append('BBOX', this.map.getBounds().toBBoxString());
      url.searchParams.append('HEIGHT', size.y.toString());
      url.searchParams.append('WIDTH', size.x.toString());
      url.searchParams.append('FORMAT', 'image/png');
      url.searchParams.append('INFO_FORMAT', 'application/json');
      url.searchParams.append('SRS', 'EPSG:4326');
      url.searchParams.append('X', Math.round(point.x).toString());
      url.searchParams.append('Y', Math.round(point.y).toString());
      url.searchParams.append('FEATURE_COUNT', '1');
      
      // Use proxy if enabled
      const requestUrl = CONFIG.proxy.enabled ? 
        `${CONFIG.proxy.url}?url=${encodeURIComponent(url.toString())}` : 
        url.toString();
      
      // Make the request
      const response = await fetch(requestUrl);
      
      if (!response.ok) {
        throw new Error(`WMS GetFeatureInfo request failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error fetching WMS GetFeatureInfo for layer ${layerId}:`, error);
      return null;
    }
  }
  
  /**
   * Update the map for a location
   */
  updateMapForLocation(location) {
    // Update the map view
    this.map.setView([location.lat, location.lng], 15);
    
    // Update the marker
    this.updateMarker(location.lat, location.lng);
  }
  
  /**
   * Update or create the marker
   */
  updateMarker(lat, lng) {
    if (this.marker) {
      this.marker.setLatLng([lat, lng]);
    } else {
      this.marker = L.marker([lat, lng]).addTo(this.map);
    }
  }
  
  /**
   * Toggle between basemaps
   */
  toggleBasemap(basemapId) {
    // Remove all basemaps
    Object.values(this.basemaps).forEach(basemap => {
      this.map.removeLayer(basemap);
    });
    
    // Add the selected basemap
    this.basemaps[basemapId].addTo(this.map);
  }
  
  /**
   * Show loading indicator
   */
  showLoading(show) {
    if (show) {
      this.elements.loadingIndicator.classList.add('active');
      this.elements.searchButton.disabled = true;
    } else {
      this.elements.loadingIndicator.classList.remove('active');
      this.elements.searchButton.disabled = false;
    }
  }
  
  /**
   * Show error message
   */
  showError(message) {
    this.elements.errorMessage.textContent = message;
    this.elements.errorMessage.classList.add('active');
  }
  
  /**
   * Hide error message
   */
  hideError() {
    this.elements.errorMessage.classList.remove('active');
  }
  
  /**
   * Show results
   */
  showResults(data) {
    this.elements.resultAddress.textContent = data.address;
    this.elements.resultCoordinates.textContent = `${data.lat.toFixed(5)}, ${data.lng.toFixed(5)}`;
    this.elements.resultsContainer.style.display = 'block';
  }
  
  /**
   * Hide results
   */
  hideResults() {
    this.elements.resultsContainer.style.display = 'none';
  }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new NSWPlanningMap();
});
