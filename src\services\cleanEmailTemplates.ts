// Clean, readable email templates for HouseGoing
// No emojis, simple design, dynamic content support

interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

interface NotificationData {
  recipientEmail: string;
  recipientName?: string;
  senderName?: string;
  venueName?: string;
  messageContent?: string;
  rating?: number;
  reviewComment?: string;
  bookingId?: string;
  // Booking-specific data
  bookingDate?: string;
  bookingTime?: string;
  guestCount?: number;
  totalPrice?: string;
  hostName?: string;
  guestName?: string;
  specialRequests?: string;
}

// Clean email templates with conditional content
export const CLEAN_EMAIL_TEMPLATES = {

  // 1. NEW MESSAGE TEMPLATE - Premium Design with Golden Ratio
  NEW_MESSAGE: (data: NotificationData): EmailTemplate => ({
    subject: `${data.senderName || 'Someone'} messaged you on HouseGoing`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Message - HouseGoing</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1f2937; margin: 0; padding: 0; background: #f9fafb; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }

          /* Golden ratio header: 600px / 1.618 ≈ 370px content width */
          .header { background: #1f2937; color: white; padding: 48px 32px 30px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; letter-spacing: -0.025em; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.8; font-weight: 400; }

          /* Golden ratio content padding: 48px top, 30px bottom (1.6:1) */
          .content { padding: 48px 32px 30px; }
          .greeting { font-size: 18px; color: #1f2937; margin-bottom: 32px; font-weight: 500; }

          /* Sender section with subtle hierarchy */
          .sender-section { margin: 32px 0; }
          .sender-info { display: flex; align-items: center; padding: 20px; background: #f8fafc; border-radius: 6px; border-left: 3px solid #6366f1; }
          .sender-avatar { width: 44px; height: 44px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 18px; margin-right: 16px; flex-shrink: 0; }
          .sender-details { flex: 1; }
          .sender-name { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 2px 0; }
          .sender-meta { font-size: 14px; color: #6b7280; margin: 0; }

          /* Message content with breathing room */
          .message-section { margin: 48px 0; }
          .message-content { background: white; border: 1px solid #e5e7eb; padding: 24px; border-radius: 6px; font-size: 16px; line-height: 1.6; color: #374151; font-style: italic; margin: 0; }

          /* CTA section with golden ratio spacing */
          .cta-section { text-align: center; margin: 48px 0 30px; }
          .button { display: inline-block; background: #6366f1; color: white; padding: 16px 32px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; letter-spacing: 0.025em; transition: background-color 0.2s ease; }
          .button:hover { background: #4f46e5; }

          /* Clean footer */
          .footer { background: #f8fafc; padding: 24px 32px; text-align: center; border-top: 1px solid #e5e7eb; }
          .footer-brand { font-size: 14px; font-weight: 600; color: #1f2937; margin-bottom: 8px; }
          .footer-text { font-size: 13px; color: #6b7280; margin: 4px 0; }
          .footer a { color: #6366f1; text-decoration: none; }
          .footer a:hover { text-decoration: underline; }

          @media (max-width: 600px) {
            .header { padding: 32px 20px 20px; }
            .content { padding: 32px 20px 20px; }
            .sender-info { flex-direction: column; text-align: center; padding: 24px 16px; }
            .sender-avatar { margin: 0 0 12px 0; }
            .footer { padding: 20px 16px; }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Message</h1>
            <p>You have a message waiting</p>
          </div>
          <div class="content">
            ${data.recipientName ? `<div class="greeting">Hi ${data.recipientName},</div>` : '<div class="greeting">Hi there,</div>'}

            <div class="sender-section">
              <div class="sender-info">
                <div class="sender-avatar">${(data.senderName || 'U').charAt(0).toUpperCase()}</div>
                <div class="sender-details">
                  <div class="sender-name">${data.senderName || 'A HouseGoing member'}</div>
                  <div class="sender-meta">Sent just now</div>
                </div>
              </div>
            </div>

            <div class="message-section">
              <div class="message-content">"${data.messageContent}"</div>
            </div>

            <div class="cta-section">
              <a href="https://housegoing.com.au/my-account?tab=messages" class="button">Reply Now</a>
            </div>
          </div>
          <div class="footer">
            <div class="footer-brand">HouseGoing</div>
            <div class="footer-text">© 2024 HouseGoing. All rights reserved.</div>
            <div class="footer-text"><a href="https://housegoing.com.au/my-account?tab=preferences">Manage preferences</a></div>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      ${data.recipientName ? `Hi ${data.recipientName},` : 'Hi there,'}

      ${data.senderName || 'A HouseGoing member'} sent you a message:

      "${data.messageContent}"

      Reply at: https://housegoing.com.au/my-account?tab=messages

      ---
      HouseGoing - Where every party finds its perfect home
      © 2024 HouseGoing. All rights reserved.
    `
  }),

  // 2. BOOKING MESSAGE TEMPLATE
  BOOKING_MESSAGE: (data: NotificationData): EmailTemplate => ({
    subject: `${data.senderName || 'Someone'} messaged you about your booking${data.venueName ? ` at ${data.venueName}` : ''}`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Booking Message - HouseGoing</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f7f7f7; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
          .header { background: #10b981; color: white; padding: 32px 24px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.9; }
          .content { padding: 32px 24px; }
          .greeting { font-size: 18px; color: #1f2937; margin-bottom: 24px; font-weight: 500; }
          .venue-info { background: #ecfdf5; border: 1px solid #10b981; padding: 16px; border-radius: 8px; margin: 24px 0; text-align: center; }
          .venue-name { font-size: 18px; font-weight: 600; color: #065f46; margin: 0; }
          .sender-info { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 24px 0; border-left: 4px solid #10b981; }
          .sender-name { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 4px 0; }
          .sender-meta { font-size: 14px; color: #6b7280; margin: 0; }
          .message-box { background: #f8fafc; border: 1px solid #e5e7eb; padding: 20px; margin: 24px 0; border-radius: 8px; }
          .message-text { color: #374151; font-size: 16px; line-height: 1.5; margin: 0; }
          .cta-section { text-align: center; margin: 32px 0; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; }
          .button:hover { background: #059669; }
          .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          .footer a { color: #7c3aed; text-decoration: none; }
          @media (max-width: 600px) { .content { padding: 24px 16px; } }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Booking Message</h1>
            <p>Message about your booking</p>
          </div>
          <div class="content">
            ${data.recipientName ? `<div class="greeting">Hi ${data.recipientName},</div>` : '<div class="greeting">Hi there,</div>'}

            ${data.venueName ? `
            <div class="venue-info">
              <div class="venue-name">${data.venueName}</div>
            </div>
            ` : ''}

            <div class="sender-info">
              <div class="sender-name">${data.senderName || 'A HouseGoing member'} sent you a message about your booking</div>
              <div class="sender-meta">Just now</div>
            </div>

            <div class="message-box">
              <div class="message-text">"${data.messageContent}"</div>
            </div>

            <p>Please respond promptly to ensure a smooth booking experience.</p>

            <div class="cta-section">
              <a href="https://housegoing.com.au/my-account?tab=messages" class="button">Reply Now</a>
            </div>
          </div>
          <div class="footer">
            <p><strong>HouseGoing</strong> - Where every party finds its perfect home</p>
            <p>© 2024 HouseGoing. All rights reserved.</p>
            <p><a href="https://housegoing.com.au/my-account?tab=preferences">Manage email preferences</a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      ${data.recipientName ? `Hi ${data.recipientName},` : 'Hi there,'}

      ${data.senderName || 'A HouseGoing member'} sent you a message about your booking${data.venueName ? ` at ${data.venueName}` : ''}:

      "${data.messageContent}"

      Reply at: https://housegoing.com.au/my-account?tab=messages

      ---
      HouseGoing - Where every party finds its perfect home
      © 2024 HouseGoing. All rights reserved.
    `
  }),

  // 3. NEW REVIEW TEMPLATE
  NEW_REVIEW: (data: NotificationData): EmailTemplate => ({
    subject: `${data.senderName || 'Someone'} left you a ${data.rating || 5}-star review${data.venueName ? ` for ${data.venueName}` : ''}`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Review - HouseGoing</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f7f7f7; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
          .header { background: #f59e0b; color: white; padding: 32px 24px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.9; }
          .content { padding: 32px 24px; }
          .greeting { font-size: 18px; color: #1f2937; margin-bottom: 24px; font-weight: 500; }
          .rating-box { background: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 24px 0; text-align: center; }
          .rating-stars { font-size: 24px; color: #f59e0b; margin-bottom: 8px; }
          .rating-text { font-size: 18px; font-weight: 600; color: #92400e; margin: 0; }
          .venue-info { background: #ecfdf5; border: 1px solid #10b981; padding: 16px; border-radius: 8px; margin: 24px 0; text-align: center; }
          .venue-name { font-size: 16px; font-weight: 600; color: #065f46; margin: 0; }
          .reviewer-info { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 24px 0; border-left: 4px solid #f59e0b; }
          .reviewer-name { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 4px 0; }
          .reviewer-meta { font-size: 14px; color: #6b7280; margin: 0; }
          .review-box { background: #f8fafc; border: 1px solid #e5e7eb; padding: 20px; margin: 24px 0; border-radius: 8px; }
          .review-text { color: #374151; font-size: 16px; line-height: 1.5; margin: 0; }
          .cta-section { text-align: center; margin: 32px 0; }
          .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; }
          .button:hover { background: #d97706; }
          .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          .footer a { color: #7c3aed; text-decoration: none; }
          @media (max-width: 600px) { .content { padding: 24px 16px; } }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Review</h1>
            <p>You received a review on HouseGoing</p>
          </div>
          <div class="content">
            ${data.recipientName ? `<div class="greeting">Hi ${data.recipientName},</div>` : '<div class="greeting">Hi there,</div>'}

            <div class="rating-box">
              <div class="rating-stars">${'★'.repeat(data.rating || 5)}${'☆'.repeat(5 - (data.rating || 5))}</div>
              <div class="rating-text">${data.rating || 5} out of 5 stars</div>
            </div>

            ${data.venueName ? `
            <div class="venue-info">
              <div class="venue-name">Review for ${data.venueName}</div>
            </div>
            ` : ''}

            <div class="reviewer-info">
              <div class="reviewer-name">${data.senderName || 'A guest'} left you a review</div>
              <div class="reviewer-meta">Just now</div>
            </div>

            <div class="review-box">
              <div class="review-text">"${data.reviewComment}"</div>
            </div>

            <p>Great reviews help build trust with future guests!</p>

            <div class="cta-section">
              <a href="https://housegoing.com.au/my-account?tab=reviews" class="button">View All Reviews</a>
            </div>
          </div>
          <div class="footer">
            <p><strong>HouseGoing</strong> - Where every party finds its perfect home</p>
            <p>© 2024 HouseGoing. All rights reserved.</p>
            <p><a href="https://housegoing.com.au/my-account?tab=preferences">Manage email preferences</a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      ${data.recipientName ? `Hi ${data.recipientName},` : 'Hi there,'}

      ${data.senderName || 'A guest'} left you a ${data.rating || 5}-star review${data.venueName ? ` for ${data.venueName}` : ''}:

      ${data.rating || 5}/5 Stars: "${data.reviewComment}"

      View all your reviews at: https://housegoing.com.au/my-account?tab=reviews

      ---
      HouseGoing - Where every party finds its perfect home
      © 2024 HouseGoing. All rights reserved.
    `
  }),

  // 4. BOOKING CONFIRMATION FOR GUESTS - Premium Design
  BOOKING_CONFIRMATION_GUEST: (data: NotificationData): EmailTemplate => ({
    subject: `Your booking is confirmed${data.venueName ? ` at ${data.venueName}` : ''}`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Booking Confirmed - HouseGoing</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1f2937; margin: 0; padding: 0; background: #f9fafb; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }

          /* Confirmation header with success feel */
          .header { background: #1f2937; color: white; padding: 48px 32px 30px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; letter-spacing: -0.025em; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.8; font-weight: 400; }

          /* Content with golden ratio spacing */
          .content { padding: 48px 32px 30px; }
          .greeting { font-size: 18px; color: #1f2937; margin-bottom: 32px; font-weight: 500; }

          /* Venue section - subtle highlight */
          .venue-section { margin: 32px 0; }
          .venue-name { font-size: 20px; font-weight: 600; color: #1f2937; text-align: center; padding: 20px; background: #f8fafc; border-radius: 6px; border-left: 3px solid #6366f1; margin: 0; }

          /* Booking details with clean grid */
          .booking-section { margin: 48px 0; }
          .section-title { font-size: 16px; font-weight: 600; color: #1f2937; margin-bottom: 20px; }
          .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
          .detail-item { background: #f8fafc; padding: 20px; border-radius: 6px; text-align: center; }
          .detail-label { font-size: 12px; color: #6b7280; text-transform: uppercase; font-weight: 600; margin-bottom: 8px; letter-spacing: 0.05em; }
          .detail-value { font-size: 16px; color: #1f2937; font-weight: 600; margin: 0; }

          /* Host information */
          .host-section { margin: 32px 0; }
          .host-info { padding: 20px; background: #f8fafc; border-radius: 6px; border-left: 3px solid #6366f1; }
          .host-name { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 4px 0; }
          .host-meta { font-size: 14px; color: #6b7280; margin: 0; }

          /* CTA section */
          .cta-section { text-align: center; margin: 48px 0 30px; }
          .button { display: inline-block; background: #6366f1; color: white; padding: 16px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; letter-spacing: 0.025em; margin: 0 8px 8px 0; transition: background-color 0.2s ease; }
          .button:hover { background: #4f46e5; }
          .button-secondary { background: #f8fafc; color: #6366f1; border: 1px solid #e5e7eb; }
          .button-secondary:hover { background: #f1f5f9; }

          /* Clean footer */
          .footer { background: #f8fafc; padding: 24px 32px; text-align: center; border-top: 1px solid #e5e7eb; }
          .footer-brand { font-size: 14px; font-weight: 600; color: #1f2937; margin-bottom: 8px; }
          .footer-text { font-size: 13px; color: #6b7280; margin: 4px 0; }
          .footer a { color: #6366f1; text-decoration: none; }
          .footer a:hover { text-decoration: underline; }

          @media (max-width: 600px) {
            .header { padding: 32px 20px 20px; }
            .content { padding: 32px 20px 20px; }
            .detail-grid { grid-template-columns: 1fr; }
            .footer { padding: 20px 16px; }
            .button { display: block; margin: 8px 0; }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Booking Confirmed</h1>
            <p>Your venue is ready for your event</p>
          </div>
          <div class="content">
            ${data.recipientName ? `<div class="greeting">Hi ${data.recipientName},</div>` : '<div class="greeting">Hi there,</div>'}

            ${data.venueName ? `
            <div class="venue-section">
              <div class="venue-name">${data.venueName}</div>
            </div>
            ` : ''}

            <div class="booking-section">
              <div class="section-title">Booking Details</div>
              <div class="detail-grid">
                ${data.bookingDate ? `
                <div class="detail-item">
                  <div class="detail-label">Date</div>
                  <div class="detail-value">${data.bookingDate}</div>
                </div>
                ` : ''}
                ${data.bookingTime ? `
                <div class="detail-item">
                  <div class="detail-label">Time</div>
                  <div class="detail-value">${data.bookingTime}</div>
                </div>
                ` : ''}
                ${data.guestCount ? `
                <div class="detail-item">
                  <div class="detail-label">Guests</div>
                  <div class="detail-value">${data.guestCount} people</div>
                </div>
                ` : ''}
                ${data.totalPrice ? `
                <div class="detail-item">
                  <div class="detail-label">Total</div>
                  <div class="detail-value">$${data.totalPrice}</div>
                </div>
                ` : ''}
              </div>
            </div>

            ${data.hostName ? `
            <div class="host-section">
              <div class="host-info">
                <div class="host-name">Your Host: ${data.hostName}</div>
                <div class="host-meta">Ready to help make your event amazing</div>
              </div>
            </div>
            ` : ''}

            <div class="cta-section">
              <a href="https://housegoing.com.au/my-account?tab=bookings" class="button">View Booking</a>
              <a href="https://housegoing.com.au/my-account?tab=messages" class="button button-secondary">Message Host</a>
            </div>
          </div>
          <div class="footer">
            <div class="footer-brand">HouseGoing</div>
            <div class="footer-text">© 2024 HouseGoing. All rights reserved.</div>
            <div class="footer-text"><a href="https://housegoing.com.au/my-account?tab=preferences">Manage preferences</a></div>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      ${data.recipientName ? `Hi ${data.recipientName},` : 'Hi there,'}

      Your booking is confirmed${data.venueName ? ` at ${data.venueName}` : ''}!

      Booking Details:
      ${data.bookingDate ? `Date: ${data.bookingDate}` : ''}
      ${data.bookingTime ? `Time: ${data.bookingTime}` : ''}
      ${data.guestCount ? `Guests: ${data.guestCount} people` : ''}
      ${data.totalPrice ? `Total: $${data.totalPrice}` : ''}
      ${data.hostName ? `Host: ${data.hostName}` : ''}

      View booking: https://housegoing.com.au/my-account?tab=bookings
      Message host: https://housegoing.com.au/my-account?tab=messages

      Get ready for an amazing celebration!

      ---
      HouseGoing - Where every party finds its perfect home
      © 2024 HouseGoing. All rights reserved.
    `
  }),

  // 5. BOOKING NOTIFICATION FOR HOSTS
  BOOKING_NOTIFICATION_HOST: (data: NotificationData): EmailTemplate => ({
    subject: `New booking${data.venueName ? ` at ${data.venueName}` : ''}${data.guestName ? ` from ${data.guestName}` : ''}`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Booking - HouseGoing</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f7f7f7; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
          .header { background: #7c3aed; color: white; padding: 32px 24px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.9; }
          .content { padding: 32px 24px; }
          .greeting { font-size: 18px; color: #1f2937; margin-bottom: 24px; font-weight: 500; }
          .guest-info { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 24px 0; border-left: 4px solid #10b981; }
          .guest-name { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 4px 0; }
          .guest-meta { font-size: 14px; color: #6b7280; margin: 0; }
          .booking-details { background: #ede9fe; border: 1px solid #7c3aed; padding: 24px; border-radius: 8px; margin: 24px 0; }
          .booking-title { font-size: 18px; font-weight: 600; color: #5b21b6; margin: 0 0 16px 0; }
          .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
          .detail-item { background: white; padding: 16px; border-radius: 6px; text-align: center; }
          .detail-label { font-size: 12px; color: #6b7280; text-transform: uppercase; font-weight: 600; margin-bottom: 4px; }
          .detail-value { font-size: 16px; color: #1f2937; font-weight: 600; }
          .earnings-highlight { background: white; padding: 20px; border-radius: 8px; text-align: center; margin-top: 16px; }
          .earnings-label { font-size: 14px; color: #6b7280; margin-bottom: 4px; }
          .earnings-amount { font-size: 24px; color: #10b981; font-weight: bold; }
          .special-requests { background: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 24px 0; }
          .special-title { font-size: 16px; font-weight: 600; color: #92400e; margin: 0 0 8px 0; }
          .special-text { color: #92400e; margin: 0; font-style: italic; }
          .cta-section { text-align: center; margin: 32px 0; }
          .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; margin: 0 8px 8px 0; }
          .button:hover { background: #6d28d9; }
          .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          .footer a { color: #7c3aed; text-decoration: none; }
          @media (max-width: 600px) { .detail-grid { grid-template-columns: 1fr; } .content { padding: 24px 16px; } }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Booking</h1>
            <p>Someone wants to party at your venue</p>
          </div>
          <div class="content">
            ${data.recipientName ? `<div class="greeting">Hi ${data.recipientName},</div>` : '<div class="greeting">Hi there,</div>'}

            ${data.guestName ? `
            <div class="guest-info">
              <div class="guest-name">${data.guestName} booked your venue</div>
              <div class="guest-meta">New guest • Just now</div>
            </div>
            ` : ''}

            <div class="booking-details">
              <div class="booking-title">Booking Details</div>
              <div class="detail-grid">
                ${data.venueName ? `
                <div class="detail-item">
                  <div class="detail-label">Venue</div>
                  <div class="detail-value">${data.venueName}</div>
                </div>
                ` : ''}
                ${data.bookingDate ? `
                <div class="detail-item">
                  <div class="detail-label">Date</div>
                  <div class="detail-value">${data.bookingDate}</div>
                </div>
                ` : ''}
                ${data.bookingTime ? `
                <div class="detail-item">
                  <div class="detail-label">Time</div>
                  <div class="detail-value">${data.bookingTime}</div>
                </div>
                ` : ''}
                ${data.guestCount ? `
                <div class="detail-item">
                  <div class="detail-label">Guests</div>
                  <div class="detail-value">${data.guestCount} people</div>
                </div>
                ` : ''}
              </div>
              ${data.totalPrice ? `
              <div class="earnings-highlight">
                <div class="earnings-label">You'll earn</div>
                <div class="earnings-amount">$${data.totalPrice}</div>
              </div>
              ` : ''}
            </div>

            ${data.specialRequests ? `
            <div class="special-requests">
              <div class="special-title">Special Requests:</div>
              <div class="special-text">"${data.specialRequests}"</div>
            </div>
            ` : ''}

            <p>Congratulations on your new booking!</p>

            <div class="cta-section">
              <a href="https://housegoing.com.au/my-account?tab=bookings" class="button">Manage Booking</a>
              <a href="https://housegoing.com.au/my-account?tab=messages" class="button">Message Guest</a>
            </div>
          </div>
          <div class="footer">
            <p><strong>HouseGoing</strong> - Where every party finds its perfect home</p>
            <p>© 2024 HouseGoing. All rights reserved.</p>
            <p><a href="https://housegoing.com.au/my-account?tab=preferences">Manage email preferences</a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      ${data.recipientName ? `Hi ${data.recipientName},` : 'Hi there,'}

      ${data.guestName ? `${data.guestName} just booked your venue!` : 'You have a new booking!'}

      Booking Details:
      ${data.venueName ? `Venue: ${data.venueName}` : ''}
      ${data.bookingDate ? `Date: ${data.bookingDate}` : ''}
      ${data.bookingTime ? `Time: ${data.bookingTime}` : ''}
      ${data.guestCount ? `Guests: ${data.guestCount} people` : ''}
      ${data.totalPrice ? `You'll earn: $${data.totalPrice}` : ''}

      ${data.specialRequests ? `Special requests: "${data.specialRequests}"` : ''}

      Manage booking: https://housegoing.com.au/my-account?tab=bookings
      Message guest: https://housegoing.com.au/my-account?tab=messages

      Congratulations on your new booking!

      ---
      HouseGoing - Where every party finds its perfect home
      © 2024 HouseGoing. All rights reserved.
    `
  }),

  // 6. CUSTOMER SIGNUP NOTIFICATION (Admin Alert)
  CUSTOMER_SIGNUP_ADMIN: (data: NotificationData): EmailTemplate => ({
    subject: `New customer signup: ${data.recipientName || 'New user'} joined HouseGoing`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Customer Signup - HouseGoing Admin</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f7f7f7; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
          .header { background: #10b981; color: white; padding: 32px 24px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.9; }
          .content { padding: 32px 24px; }
          .user-info { background: #ecfdf5; border: 1px solid #10b981; padding: 24px; border-radius: 8px; margin: 24px 0; }
          .user-title { font-size: 18px; font-weight: 600; color: #065f46; margin: 0 0 16px 0; }
          .detail-item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #d1fae5; }
          .detail-item:last-child { border-bottom: none; }
          .detail-label { font-weight: 600; color: #065f46; }
          .detail-value { color: #047857; }
          .stats-box { background: #f8fafc; border: 1px solid #e5e7eb; padding: 20px; border-radius: 8px; margin: 24px 0; text-align: center; }
          .stats-title { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 8px 0; }
          .stats-text { color: #6b7280; margin: 0; }
          .cta-section { text-align: center; margin: 32px 0; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; margin: 0 8px 8px 0; }
          .button:hover { background: #059669; }
          .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          @media (max-width: 600px) { .content { padding: 24px 16px; } }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Customer Signup</h1>
            <p>Someone just joined HouseGoing as a customer</p>
          </div>
          <div class="content">
            <div class="user-info">
              <div class="user-title">Customer Details</div>
              <div class="detail-item">
                <span class="detail-label">Name:</span>
                <span class="detail-value">${data.recipientName || 'Not provided'}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${data.recipientEmail || 'Not provided'}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">User Type:</span>
                <span class="detail-value">Customer (Party Planner)</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Signup Time:</span>
                <span class="detail-value">Just now</span>
              </div>
            </div>

            <div class="stats-box">
              <div class="stats-title">Growing Customer Base</div>
              <div class="stats-text">Another party planner ready to book amazing venues!</div>
            </div>

            <p>This customer can now browse venues, make bookings, and leave reviews. Keep an eye on their activity to ensure a great experience.</p>

            <div class="cta-section">
              <a href="https://housegoing.com.au/admin/users" class="button">View All Users</a>
              <a href="https://housegoing.com.au/admin/analytics" class="button">User Analytics</a>
            </div>
          </div>
          <div class="footer">
            <p><strong>HouseGoing Admin</strong> - User Signup Notification</p>
            <p>© 2024 HouseGoing. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      NEW CUSTOMER SIGNUP - HouseGoing

      Customer Details:
      Name: ${data.recipientName || 'Not provided'}
      Email: ${data.recipientEmail || 'Not provided'}
      User Type: Customer (Party Planner)
      Signup Time: Just now

      This customer can now browse venues, make bookings, and leave reviews.

      View admin panel: https://housegoing.com.au/admin/users

      ---
      HouseGoing Admin - User Signup Notification
      © 2024 HouseGoing. All rights reserved.
    `
  }),

  // 7. HOST SIGNUP NOTIFICATION (Admin Alert)
  HOST_SIGNUP_ADMIN: (data: NotificationData): EmailTemplate => ({
    subject: `New host signup: ${data.recipientName || 'New user'} wants to list venues on HouseGoing`,
    htmlContent: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Host Signup - HouseGoing Admin</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f7f7f7; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
          .header { background: #7c3aed; color: white; padding: 32px 24px; text-align: center; }
          .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
          .header p { margin: 8px 0 0; font-size: 16px; opacity: 0.9; }
          .content { padding: 32px 24px; }
          .user-info { background: #ede9fe; border: 1px solid #7c3aed; padding: 24px; border-radius: 8px; margin: 24px 0; }
          .user-title { font-size: 18px; font-weight: 600; color: #5b21b6; margin: 0 0 16px 0; }
          .detail-item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #ddd6fe; }
          .detail-item:last-child { border-bottom: none; }
          .detail-label { font-weight: 600; color: #5b21b6; }
          .detail-value { color: #6d28d9; }
          .priority-box { background: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 24px 0; text-align: center; }
          .priority-title { font-size: 16px; font-weight: 600; color: #92400e; margin: 0 0 8px 0; }
          .priority-text { color: #92400e; margin: 0; }
          .stats-box { background: #f8fafc; border: 1px solid #e5e7eb; padding: 20px; border-radius: 8px; margin: 24px 0; text-align: center; }
          .stats-title { font-size: 16px; font-weight: 600; color: #1f2937; margin: 0 0 8px 0; }
          .stats-text { color: #6b7280; margin: 0; }
          .cta-section { text-align: center; margin: 32px 0; }
          .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; margin: 0 8px 8px 0; }
          .button:hover { background: #6d28d9; }
          .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          @media (max-width: 600px) { .content { padding: 24px 16px; } }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Host Signup</h1>
            <p>Someone wants to list venues on HouseGoing</p>
          </div>
          <div class="content">
            <div class="user-info">
              <div class="user-title">Host Details</div>
              <div class="detail-item">
                <span class="detail-label">Name:</span>
                <span class="detail-value">${data.recipientName || 'Not provided'}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${data.recipientEmail || 'Not provided'}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">User Type:</span>
                <span class="detail-value">Host (Venue Owner)</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Signup Time:</span>
                <span class="detail-value">Just now</span>
              </div>
            </div>

            <div class="priority-box">
              <div class="priority-title">Action Required</div>
              <div class="priority-text">New hosts may need onboarding support and venue listing assistance</div>
            </div>

            <div class="stats-box">
              <div class="stats-title">Growing Host Network</div>
              <div class="stats-text">More venues mean more booking opportunities!</div>
            </div>

            <p>This host can now list venues, manage bookings, and earn from their properties. Consider reaching out to help them get started.</p>

            <div class="cta-section">
              <a href="https://housegoing.com.au/admin/hosts" class="button">View All Hosts</a>
              <a href="https://housegoing.com.au/admin/venues" class="button">Venue Listings</a>
            </div>
          </div>
          <div class="footer">
            <p><strong>HouseGoing Admin</strong> - Host Signup Notification</p>
            <p>© 2024 HouseGoing. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    textContent: `
      NEW HOST SIGNUP - HouseGoing

      Host Details:
      Name: ${data.recipientName || 'Not provided'}
      Email: ${data.recipientEmail || 'Not provided'}
      User Type: Host (Venue Owner)
      Signup Time: Just now

      ACTION REQUIRED: New hosts may need onboarding support and venue listing assistance.

      This host can now list venues, manage bookings, and earn from their properties.

      View admin panel: https://housegoing.com.au/admin/hosts

      ---
      HouseGoing Admin - Host Signup Notification
      © 2024 HouseGoing. All rights reserved.
    `
  })
};

// Test function to generate sample emails
export function generateSampleEmail(templateType: keyof typeof CLEAN_EMAIL_TEMPLATES, sampleData?: Partial<NotificationData>): EmailTemplate {
  const defaultData: NotificationData = {
    recipientEmail: '<EMAIL>',
    recipientName: 'Sarah',
    senderName: 'John',
    venueName: 'The Party House',
    messageContent: 'Hi! I have a question about the booking. Can we bring our own decorations?',
    rating: 5,
    reviewComment: 'Amazing venue! Perfect for our celebration. The host was super helpful and everything was clean and ready.',
    bookingDate: 'December 25, 2024',
    bookingTime: '7:00 PM - 11:00 PM',
    guestCount: 25,
    totalPrice: '450',
    hostName: 'Mike',
    guestName: 'Sarah',
    specialRequests: 'Need extra chairs for elderly guests and access to kitchen for cake cutting',
    ...sampleData
  };

  return CLEAN_EMAIL_TEMPLATES[templateType](defaultData);
}

// Admin notification functions
export async function sendCustomerSignupNotification(
  customerEmail: string,
  customerName?: string
): Promise<void> {
  try {
    const template = CLEAN_EMAIL_TEMPLATES.CUSTOMER_SIGNUP_ADMIN({
      recipientEmail: customerEmail,
      recipientName: customerName
    });

    // <NAME_EMAIL> for customer signups
    const response = await fetch(`${process.env.VITE_BACKEND_URL || 'https://housegoing.onrender.com'}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: '<EMAIL>',
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: 'HouseGoing Notifications <<EMAIL>>',
        replyTo: '<EMAIL>'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to send customer signup notification');
    }

    console.log('Customer signup notification sent successfully');
  } catch (error) {
    console.error('Error sending customer signup notification:', error);
  }
}

export async function sendHostSignupNotification(
  hostEmail: string,
  hostName?: string
): Promise<void> {
  try {
    const template = CLEAN_EMAIL_TEMPLATES.HOST_SIGNUP_ADMIN({
      recipientEmail: hostEmail,
      recipientName: hostName
    });

    // <NAME_EMAIL> for host signups
    const response = await fetch(`${process.env.VITE_BACKEND_URL || 'https://housegoing.onrender.com'}/api/send-notification-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: '<EMAIL>',
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        from: 'HouseGoing Notifications <<EMAIL>>',
        replyTo: '<EMAIL>'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to send host signup notification');
    }

    console.log('Host signup notification sent successfully');
  } catch (error) {
    console.error('Error sending host signup notification:', error);
  }
}

// Enhanced generateSampleEmail function with admin templates
export function generateSampleEmailEnhanced(templateType: string, data: any): EmailTemplate {
  const templates = {
    ...CLEAN_EMAIL_TEMPLATES,

    // Admin Email Templates
    PROPERTY_SUBMISSION_ADMIN: (data: any): EmailTemplate => ({
      subject: `New Property Submission: ${data.propertyName}`,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Property Submission - HouseGoing Admin</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1f2937; margin: 0; padding: 0; background: #f9fafb; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .header { background: #dc2626; color: white; padding: 32px 24px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
            .content { padding: 32px 24px; }
            .property-info { background: #fef2f2; border: 1px solid #dc2626; padding: 20px; border-radius: 8px; margin: 24px 0; }
            .property-name { font-size: 18px; font-weight: 600; color: #991b1b; margin: 0 0 8px 0; }
            .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin: 16px 0; }
            .detail-item { background: white; padding: 16px; border-radius: 6px; }
            .detail-label { font-size: 12px; color: #6b7280; text-transform: uppercase; font-weight: 600; margin-bottom: 4px; }
            .detail-value { font-size: 14px; color: #1f2937; font-weight: 500; }
            .cta-section { text-align: center; margin: 32px 0; }
            .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 8px; }
            .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏠 New Property Submission</h1>
              <p>Requires admin review</p>
            </div>
            <div class="content">
              <p>A new property has been submitted for review:</p>

              <div class="property-info">
                <div class="property-name">${data.propertyName}</div>
                <p style="margin: 0; color: #6b7280;">${data.propertyAddress}</p>

                <div class="detail-grid">
                  <div class="detail-item">
                    <div class="detail-label">Type</div>
                    <div class="detail-value">${data.propertyType}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">Max Guests</div>
                    <div class="detail-value">${data.maxGuests} people</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">Price</div>
                    <div class="detail-value">$${data.pricePerHour}/hour</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">Submitted</div>
                    <div class="detail-value">${data.submissionDate}</div>
                  </div>
                </div>

                <p><strong>Owner:</strong> ${data.ownerName} (${data.ownerEmail})</p>
              </div>

              <div class="cta-section">
                <a href="${data.adminDashboardUrl}" class="button">Review Submission</a>
              </div>
            </div>
            <div class="footer">
              <p><strong>HouseGoing Admin</strong></p>
              <p>Property submission notification</p>
            </div>
          </div>
        </body>
        </html>
      `,
      textContent: `
        New Property Submission - ${data.propertyName}

        Property: ${data.propertyName}
        Address: ${data.propertyAddress}
        Type: ${data.propertyType}
        Max Guests: ${data.maxGuests} people
        Price: $${data.pricePerHour}/hour
        Owner: ${data.ownerName} (${data.ownerEmail})
        Submitted: ${data.submissionDate}

        Review at: ${data.adminDashboardUrl}

        ---
        HouseGoing Admin Panel
      `
    }),

    PROPERTY_APPROVAL: (data: any): EmailTemplate => ({
      subject: `🎉 Your property "${data.propertyName}" has been approved!`,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Property Approved - HouseGoing</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1f2937; margin: 0; padding: 0; background: #f9fafb; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .header { background: #10b981; color: white; padding: 32px 24px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
            .content { padding: 32px 24px; }
            .approval-box { background: #ecfdf5; border: 1px solid #10b981; padding: 20px; border-radius: 8px; margin: 24px 0; text-align: center; }
            .property-name { font-size: 18px; font-weight: 600; color: #065f46; margin: 0 0 8px 0; }
            .cta-section { text-align: center; margin: 32px 0; }
            .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 8px; }
            .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Property Approved!</h1>
              <p>Your venue is now live on HouseGoing</p>
            </div>
            <div class="content">
              <p>Hi ${data.recipientName},</p>

              <p>Great news! Your property has been approved and is now live on HouseGoing:</p>

              <div class="approval-box">
                <div class="property-name">${data.propertyName}</div>
                <p style="margin: 8px 0 0; color: #6b7280;">${data.propertyAddress}</p>
              </div>

              <p><strong>Admin Notes:</strong> ${data.adminNotes}</p>

              <p>Your property is now visible to guests and ready to receive bookings. You can manage your listing and view bookings in your host dashboard.</p>

              <div class="cta-section">
                <a href="${data.dashboardUrl}" class="button">View Dashboard</a>
              </div>

              <p>If you have any questions, please contact us at ${data.supportEmail}.</p>
            </div>
            <div class="footer">
              <p><strong>HouseGoing Team</strong></p>
              <p>Welcome to our host community!</p>
            </div>
          </div>
        </body>
        </html>
      `,
      textContent: `
        Property Approved - ${data.propertyName}

        Hi ${data.recipientName},

        Great news! Your property "${data.propertyName}" at ${data.propertyAddress} has been approved and is now live on HouseGoing.

        Admin Notes: ${data.adminNotes}

        Your property is now visible to guests and ready to receive bookings.

        View your dashboard: ${data.dashboardUrl}

        Questions? Contact us at ${data.supportEmail}

        ---
        HouseGoing Team
      `
    }),

    PROPERTY_REJECTION: (data: any): EmailTemplate => ({
      subject: `Update on your property submission: "${data.propertyName}"`,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Property Submission Update - HouseGoing</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #1f2937; margin: 0; padding: 0; background: #f9fafb; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .header { background: #f59e0b; color: white; padding: 32px 24px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
            .content { padding: 32px 24px; }
            .feedback-box { background: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 24px 0; }
            .property-name { font-size: 18px; font-weight: 600; color: #92400e; margin: 0 0 8px 0; }
            .cta-section { text-align: center; margin: 32px 0; }
            .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 0 8px; }
            .footer { background: #f9fafb; padding: 24px; text-align: center; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Submission Update</h1>
              <p>Feedback on your property submission</p>
            </div>
            <div class="content">
              <p>Hi ${data.recipientName},</p>

              <p>Thank you for submitting your property to HouseGoing. After review, we need some adjustments before we can approve your listing:</p>

              <div class="feedback-box">
                <div class="property-name">${data.propertyName}</div>

                <p><strong>Feedback:</strong></p>
                <p>${data.rejectionReason}</p>

                <p><strong>Admin Notes:</strong> ${data.adminNotes}</p>
              </div>

              <p>Don't worry - this is common and we're here to help! Please review the feedback above and feel free to resubmit your property with the suggested changes.</p>

              <div class="cta-section">
                <a href="${data.resubmitUrl}" class="button">Resubmit Property</a>
              </div>

              <p>If you have any questions about the feedback, please contact us at ${data.supportEmail}.</p>
            </div>
            <div class="footer">
              <p><strong>HouseGoing Team</strong></p>
              <p>We're here to help you succeed!</p>
            </div>
          </div>
        </body>
        </html>
      `,
      textContent: `
        Property Submission Update - ${data.propertyName}

        Hi ${data.recipientName},

        Thank you for submitting "${data.propertyName}" to HouseGoing. After review, we need some adjustments before approval:

        Feedback: ${data.rejectionReason}
        Admin Notes: ${data.adminNotes}

        Please review the feedback and resubmit with the suggested changes.

        Resubmit at: ${data.resubmitUrl}
        Questions? Contact us at ${data.supportEmail}

        ---
        HouseGoing Team
      `
    })
  };

  const template = templates[templateType];
  if (!template) {
    throw new Error(`Unknown email template: ${templateType}`);
  }

  return template(data);
}
