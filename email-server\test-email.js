// Test script for the hybrid email system
import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:3001';

async function testEmailSystem() {
  console.log('🧪 Testing HouseGoing Hybrid Email System\n');

  // Test 1: Customer email via Brevo
  console.log('📧 Test 1: Customer email (should use Brevo)');
  try {
    const response1 = await fetch(`${BACKEND_URL}/api/send-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: '<EMAIL>',
        from: '<EMAIL>',
        subject: 'Test Customer Email - Brevo',
        text: 'This is a test customer email that should be sent via Brevo SMTP.',
        html: '<p>This is a test customer email that should be sent via <strong>Brevo SMTP</strong>.</p>',
        emailType: 'customer'
      })
    });

    const result1 = await response1.json();
    console.log('✅ Customer email result:', result1);
  } catch (error) {
    console.log('❌ Customer email failed:', error.message);
  }

  console.log('\n');

  // Test 2: Admin email via Gmail
  console.log('📧 Test 2: Admin email (should use Gmail)');
  try {
    const response2 = await fetch(`${BACKEND_URL}/api/send-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: '<EMAIL>', // Changed to working Gmail address
        from: '<EMAIL>',
        subject: 'Test Admin Email - Gmail',
        text: 'This is a test admin email that should be sent via Gmail SMTP.',
        html: '<p>This is a test admin email that should be sent via <strong>Gmail SMTP</strong>.</p>',
        emailType: 'admin'
      })
    });

    const result2 = await response2.json();
    console.log('✅ Admin email result:', result2);
  } catch (error) {
    console.log('❌ Admin email failed:', error.message);
  }

  console.log('\n');

  // Test 3: Health check
  console.log('🏥 Test 3: Health check');
  try {
    const response3 = await fetch(`${BACKEND_URL}/health`);
    const result3 = await response3.json();
    console.log('✅ Health check result:', result3);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  console.log('\n🎉 Email system testing complete!');
}

// Run the tests
testEmailSystem().catch(console.error);
