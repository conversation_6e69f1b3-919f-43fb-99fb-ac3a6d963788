import React, { useState } from 'react';
import { Shield, AlertTriangle, CheckCircle, FileText } from 'lucide-react';
import SafetyGuidelinesModal from './SafetyGuidelinesModal';

const SafetyChecklist = ({ safetyItems, onSafetyItemToggle, onAcknowledgmentChange, acknowledgment }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const safetyRequirements = [
    {
      id: 'smoke_alarms',
      title: 'Working Smoke Alarms',
      description: 'Smoke alarms installed and tested within last 30 days',
      required: true
    },
    {
      id: 'fire_extinguisher',
      title: 'Fire Extinguisher',
      description: 'At least one accessible fire extinguisher with valid inspection tag',
      required: true
    },
    {
      id: 'emergency_exits',
      title: 'Clear Emergency Exits',
      description: 'All emergency exits clearly marked and unobstructed',
      required: true
    },
    {
      id: 'structural_safety',
      title: 'Basic Structural Safety',
      description: 'Building in good condition with secure railings and no obvious hazards',
      required: true
    },
    {
      id: 'pool_safety',
      title: 'Pool Safety (if applicable)',
      description: 'Pool fence compliant with NSW regulations (1.2m height, self-closing gate)',
      required: false
    },
    {
      id: 'public_liability',
      title: 'Public Liability Insurance',
      description: 'Minimum $10 million public liability insurance coverage',
      required: true
    },
    {
      id: 'emergency_info',
      title: 'Emergency Information',
      description: 'Emergency contact numbers and venue address displayed prominently',
      required: true
    }
  ];

  const requiredItems = safetyRequirements.filter(item => item.required);
  const completedRequired = requiredItems.filter(item => safetyItems.includes(item.id));
  const allRequiredCompleted = completedRequired.length === requiredItems.length;

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Shield className="h-6 w-6 text-blue-600 mt-1 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Essential Safety Requirements
            </h3>
            <p className="text-blue-800 text-sm">
              Please confirm your venue meets these basic safety standards. This helps ensure guest safety and protects you as a host.
            </p>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Safety Requirements Progress
          </span>
          <span className="text-sm text-gray-600">
            {completedRequired.length} of {requiredItems.length} completed
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              allRequiredCompleted ? 'bg-green-500' : 'bg-blue-500'
            }`}
            style={{ width: `${(completedRequired.length / requiredItems.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Safety Checklist */}
      <div className="space-y-3">
        {safetyRequirements.map((item) => {
          const isChecked = safetyItems.includes(item.id);
          
          return (
            <div 
              key={item.id}
              className={`border rounded-lg p-4 transition-all duration-200 ${
                isChecked 
                  ? 'border-green-300 bg-green-50' 
                  : item.required 
                    ? 'border-red-200 bg-red-50' 
                    : 'border-gray-200 bg-white'
              }`}
            >
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id={item.id}
                    type="checkbox"
                    checked={isChecked}
                    onChange={() => onSafetyItemToggle(item.id)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 flex-1">
                  <label htmlFor={item.id} className="cursor-pointer">
                    <div className="flex items-center">
                      <span className={`font-medium ${
                        isChecked ? 'text-green-900' : 'text-gray-900'
                      }`}>
                        {item.title}
                      </span>
                      {item.required && (
                        <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                          Required
                        </span>
                      )}
                      {isChecked && (
                        <CheckCircle className="h-4 w-4 text-green-600 ml-2" />
                      )}
                    </div>
                    <p className={`text-sm mt-1 ${
                      isChecked ? 'text-green-700' : 'text-gray-600'
                    }`}>
                      {item.description}
                    </p>
                  </label>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Safety Information Document */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-gray-600 mt-1 mr-3" />
          <div className="flex-1">
            <h4 className="font-medium text-gray-900 mb-2">
              Safety Guidelines & Requirements
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              Please read our comprehensive safety guidelines before submitting your venue listing.
            </p>
            <button
              type="button"
              onClick={() => setIsModalOpen(true)}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium underline bg-transparent border-none cursor-pointer"
            >
              View Safety Guidelines
            </button>
          </div>
        </div>
      </div>

      {/* Acknowledgment Section */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-1 mr-3" />
          <div className="flex-1">
            <h4 className="font-medium text-yellow-900 mb-3">
              Safety Acknowledgment & Legal Declaration
            </h4>
            <div className="space-y-2 text-sm text-yellow-800 mb-4">
              <p>By checking the box below, I acknowledge and confirm that:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>All information provided in this safety checklist is true and accurate</li>
                <li>My venue meets all the required safety standards listed above</li>
                <li>I will maintain these safety standards throughout the listing period</li>
                <li>I understand that providing false information may result in removal from the platform</li>
                <li>I accept responsibility for guest safety and venue compliance with local regulations</li>
              </ul>
            </div>
            
            <div className="flex items-start">
              <input
                id="safety-acknowledgment"
                type="checkbox"
                checked={acknowledgment}
                onChange={(e) => onAcknowledgmentChange(e.target.checked)}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded mt-1"
              />
              <label htmlFor="safety-acknowledgment" className="ml-3 text-sm font-medium text-yellow-900 cursor-pointer">
                I acknowledge that all safety information provided is accurate and I accept full responsibility for venue safety and compliance
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Validation Message */}
      {!allRequiredCompleted && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-sm font-medium text-red-800">
              Please complete all required safety items before proceeding
            </span>
          </div>
        </div>
      )}

      {/* Safety Guidelines Modal */}
      <SafetyGuidelinesModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default SafetyChecklist;
