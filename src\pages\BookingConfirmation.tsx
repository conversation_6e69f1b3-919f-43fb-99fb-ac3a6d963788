import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { CheckCircle, Calendar, Users, DollarSign, Clock, MapPin, ArrowLeft, MessageSquare, Mail, Phone } from 'lucide-react';
import { getBookingById } from '../api/bookings';
import { useUser } from '@clerk/clerk-react';
import BookingMessageForm from '../components/messaging/BookingMessageForm';
import BookingMessages from '../components/messaging/BookingMessages';

export default function BookingConfirmation() {
  const { id } = useParams<{ id: string }>();
  const { user } = useUser();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<any>(null);
  const [venue, setVenue] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchBooking() {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getBookingById(id);

        if (!data) {
          setError('Booking not found');
          return;
        }

        setBooking(data);
        setVenue(data.venue);
      } catch (err) {
        console.error('Error fetching booking:', err);
        setError('Failed to load booking details');
      } finally {
        setLoading(false);
      }
    }

    fetchBooking();
  }, [id]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
          <p className="text-gray-600 mb-6">{error || 'Booking not found'}</p>
          <Link to="/" className="px-4 py-2 bg-purple-600 text-white rounded-md inline-block">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-purple-600 text-white p-6 flex items-center justify-center">
            <CheckCircle className="w-10 h-10 mr-4" />
            <h1 className="text-2xl font-bold">Booking Confirmed!</h1>
          </div>

          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">{venue.title}</h2>
              <p className="text-gray-600 flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {venue.address}
              </p>
            </div>

            <div className="border-t border-b py-6 my-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-start">
                  <Calendar className="w-5 h-5 text-purple-500 mr-3 mt-1" />
                  <div>
                    <p className="text-sm text-gray-500">Date</p>
                    <p className="font-medium">{formatDate(booking.start_date)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="w-5 h-5 text-purple-500 mr-3 mt-1" />
                  <div>
                    <p className="text-sm text-gray-500">Time</p>
                    <p className="font-medium">
                      {formatTime(booking.start_date)} - {formatTime(booking.end_date)}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Users className="w-5 h-5 text-purple-500 mr-3 mt-1" />
                  <div>
                    <p className="text-sm text-gray-500">Guests</p>
                    <p className="font-medium">{booking.guests_count} people</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <DollarSign className="w-5 h-5 text-purple-500 mr-3 mt-1" />
                  <div>
                    <p className="text-sm text-gray-500">Total Price</p>
                    <p className="font-medium">${booking.total_price.toFixed(2)}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-100 rounded-lg p-4 mb-6">
              <p className="text-green-800">
                We've sent a confirmation email with all the details. The host has been notified and will be in touch if needed.
              </p>
            </div>

            {/* Host Information */}
            <div className="border rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold mb-4">Host Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                    <Mail className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{venue.host?.email || "<EMAIL>"}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                    <Phone className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{venue.host?.phone || "+61 400 000 000"}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Messaging Component */}
            <div className="border rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold mb-4">Messages</h3>

              {/* Previous messages */}
              <div className="mb-6">
                <h4 className="text-md font-medium mb-2">Message History</h4>
                <div className="border rounded-lg bg-gray-50">
                  <BookingMessages bookingId={id || ''} />
                </div>
              </div>

              {/* New message form */}
              <h4 className="text-md font-medium mb-2">Send a Message</h4>
              <BookingMessageForm bookingId={id || ''} hostId={venue.host?.id || 'host-id'} />
            </div>

            <div className="flex flex-col sm:flex-row sm:justify-between gap-4">
              <Link
                to="/my-bookings"
                className="px-4 py-2 bg-purple-600 text-white rounded-md text-center hover:bg-purple-700 transition-colors"
              >
                View My Bookings
              </Link>

              <Link
                to="/"
                className="px-4 py-2 border border-gray-300 rounded-md text-center flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center text-gray-500 text-sm">
          <p>Booking Reference: {booking.id}</p>
          <p>Status: {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</p>
        </div>
      </div>
    </div>
  );
}
