<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct WFS Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    button {
      padding: 10px;
      margin: 10px 0;
      cursor: pointer;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow: auto;
      max-height: 400px;
    }
  </style>
</head>
<body>
  <h1>Direct WFS Test</h1>
  
  <button id="testZoning">Test Zoning WFS</button>
  <button id="testLGA">Test LGA WFS</button>
  
  <h2>Results:</h2>
  <pre id="results">Click a button to test...</pre>
  
  <script>
    // Test coordinates (Sydney CBD)
    const testLat = -33.8688;
    const testLng = 151.2093;
    
    // NSW Planning Portal WFS endpoints
    const ZONING_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer';
    const LGA_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer';
    
    // Results element
    const resultsElement = document.getElementById('results');
    
    // Test Zoning WFS
    document.getElementById('testZoning').addEventListener('click', async () => {
      resultsElement.textContent = 'Testing Zoning WFS...';
      
      try {
        // Build URL with parameters
        const url = new URL(ZONING_WFS_URL);
        url.searchParams.append('service', 'WFS');
        url.searchParams.append('version', '1.1.0');
        url.searchParams.append('request', 'GetCapabilities');
        
        // Make the request
        const response = await fetch(url.toString());
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const text = await response.text();
        resultsElement.textContent = `Zoning WFS GetCapabilities Response:\n\n${text}`;
      } catch (error) {
        resultsElement.textContent = `Error: ${error.message}`;
      }
    });
    
    // Test LGA WFS
    document.getElementById('testLGA').addEventListener('click', async () => {
      resultsElement.textContent = 'Testing LGA WFS...';
      
      try {
        // Build URL with parameters
        const url = new URL(LGA_WFS_URL);
        url.searchParams.append('service', 'WFS');
        url.searchParams.append('version', '1.1.0');
        url.searchParams.append('request', 'GetCapabilities');
        
        // Make the request
        const response = await fetch(url.toString());
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const text = await response.text();
        resultsElement.textContent = `LGA WFS GetCapabilities Response:\n\n${text}`;
      } catch (error) {
        resultsElement.textContent = `Error: ${error.message}`;
      }
    });
  </script>
</body>
</html>
