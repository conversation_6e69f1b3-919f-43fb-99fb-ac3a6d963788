import React from 'react';
import { useUser as useClerkUser, useAuth as useClerkAuth } from '@clerk/clerk-react';

// Simple AuthProvider that exports Clerk hooks for backward compatibility
export function useAuth() {
  const { user, isLoaded, isSignedIn } = useClerkUser();
  const { signOut, getToken } = useClerkAuth();
  
  return {
    user,
    isLoading: !isLoaded,
    isAuthenticated: isSignedIn,
    isSignedIn,
    signOut,
    getToken
  };
}

export function useAuthContext() {
  return useAuth();
}

// Default export for backward compatibility
export default function AuthProvider({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

// Named export
export { AuthProvider };
