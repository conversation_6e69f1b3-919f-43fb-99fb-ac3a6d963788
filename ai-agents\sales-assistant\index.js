/**
 * HouseGoing AI Sales Assistant
 *
 * This is the main entry point for the Sales Assistant.
 * It provides a conversational interface for helping customers
 * find and book venues on the HouseGoing platform.
 */

import dotenv from 'dotenv';
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { LL<PERSON>hain } from "langchain/chains";
import { salesAssistantPrompt } from './prompt.js';
import { createMemory } from './memory.js';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Configure LangSmith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;
process.env.LANGCHAIN_ORG_ID = process.env.LANGSMITH_ORG_ID;

// Initialize the Hugging Face model
const model = new HuggingFaceInference({
  model: "mistralai/Mistral-7B-Instruct-v0.3",
  apiKey: process.env.HUGGINGFACE_API_KEY,
  temperature: 0.7,
  maxTokens: 1024,
});

/**
 * Create a new Sales Assistant instance
 * @returns {LLMChain} The Sales Assistant chain
 */
function createSalesAssistant() {
  const memory = createMemory();

  const chain = new LLMChain({
    llm: model,
    prompt: salesAssistantPrompt,
    memory: memory,
    outputKey: "output",
  });

  return chain;
}

/**
 * Generate a response from the Sales Assistant
 * @param {string} input - User input
 * @param {LLMChain} assistant - Sales Assistant chain
 * @returns {Promise<string>} Assistant response
 */
async function generateResponse(input, assistant) {
  try {
    console.log("Generating response for:", input);
    const response = await assistant.call({ input });
    console.log("Response generated successfully");
    return response.output;
  } catch (error) {
    console.error("Error generating response:", error);
    return "I'm sorry, I encountered an error while processing your request. Please try again.";
  }
}

// If this file is run directly, start the interactive console
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log("HouseGoing AI Sales Assistant");
  console.log("=============================");
  console.log("Type 'exit' to quit the conversation");
  console.log("");

  const assistant = createSalesAssistant();
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  function askQuestion() {
    rl.question("You: ", async (input) => {
      if (input.toLowerCase() === 'exit') {
        console.log("Goodbye!");
        rl.close();
        return;
      }

      console.log("AI is thinking...");
      const response = await generateResponse(input, assistant);
      console.log("\nAlex: " + response + "\n");

      askQuestion();
    });
  }

  // Start the conversation
  askQuestion();
}

export {
  createSalesAssistant,
  generateResponse
};
