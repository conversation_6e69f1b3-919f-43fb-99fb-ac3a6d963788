import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import StripePaymentForm from '../payment/StripePaymentForm';
import { supabase } from '../../lib/supabase';
import { PriceCalculation } from '../../services/stripe-payment';

interface BookingPaymentProps {
  bookingId: string;
  amount: number;
  venueName: string;
  bookingDate: string;
  customerEmail?: string;
  priceCalculation?: PriceCalculation;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const BookingPayment: React.FC<BookingPaymentProps> = ({
  bookingId,
  amount,
  venueName,
  bookingDate,
  customerEmail,
  priceCalculation,
  onSuccess,
  onCancel,
}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Format the booking date
  const formattedDate = new Date(bookingDate).toLocaleDateString('en-AU', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Handle successful payment
  const handlePaymentSuccess = async (paymentId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Update the booking with payment information
      const { error: updateError } = await supabase
        .from('bookings')
        .update({
          payment_status: 'paid',
          payment_id: paymentId,
          payment_amount: amount,
          payment_date: new Date().toISOString(),
          payment_method: 'Stripe',
          payment_details: JSON.stringify(priceCalculation || {}),
          updated_at: new Date().toISOString(),
        })
        .eq('id', bookingId);

      if (updateError) {
        throw updateError;
      }

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      } else {
        // Navigate to the booking confirmation page
        navigate(`/booking-confirmation/${bookingId}`);
      }
    } catch (err: any) {
      console.error('Error updating booking:', err);
      setError('Payment was successful, but there was an error updating your booking. Please contact support.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle payment error
  const handlePaymentError = (err: any) => {
    console.error('Payment error:', err);
    setError('There was an error processing your payment. Please try again.');
  };

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="bg-purple-600 text-white p-6 sticky top-0 z-10 booking-page-header">
        <h1 className="text-2xl font-bold text-center">Complete Your Booking</h1>
      </div>

      <div className="p-6 md:p-8 booking-page-content booking-step-transition">
        <div className="mb-8">
          <div className="flex justify-center mb-6">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full font-bold">1</div>
              <div className="w-16 h-1 bg-purple-600"></div>
              <div className="flex items-center justify-center w-8 h-8 bg-purple-600 text-white rounded-full font-bold">2</div>
              <div className="w-16 h-1 bg-gray-200"></div>
              <div className="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-600 rounded-full font-bold">3</div>
            </div>
          </div>
        </div>

        <div className="mb-8 p-5 bg-gray-50 rounded-xl shadow-sm booking-section">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Booking Summary</h3>
          <ul className="space-y-3">
            <li className="flex justify-between items-center border-b border-gray-200 pb-3">
              <span className="font-medium text-gray-700">Venue:</span>
              <span className="text-gray-900 font-semibold">{venueName}</span>
            </li>
            <li className="flex justify-between items-center border-b border-gray-200 pb-3">
              <span className="font-medium text-gray-700">Date:</span>
              <span className="text-gray-900 font-semibold">{formattedDate}</span>
            </li>
            <li className="flex justify-between items-center pt-2">
              <span className="font-medium text-gray-700">Total Amount:</span>
              <span className="text-lg text-purple-700 font-bold">${amount.toFixed(2)} AUD</span>
            </li>
          </ul>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-red-500 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="font-medium">{error}</p>
              <p className="text-sm text-red-600 mt-1">Please try again or contact customer support if the problem persists.</p>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12 my-4">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500 mb-6"></div>
            <p className="text-lg font-medium text-gray-700">Processing your booking...</p>
            <p className="text-gray-500 mt-2">Please don't close this page. This may take a moment.</p>
          </div>
        ) : (
          <>
            <StripePaymentForm
              amount={amount}
              description={`Booking for ${venueName} on ${formattedDate}`}
              customerEmail={customerEmail}
              priceCalculation={priceCalculation}
              metadata={{
                booking_id: bookingId,
                venue_name: venueName,
                booking_date: bookingDate,
                payment_type: 'venue_booking',
              }}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              showTestCards={process.env.NODE_ENV !== 'production'}
            />

            <div className="mt-8 border-t border-gray-200 pt-6">
              <div className="flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                <span className="text-sm text-gray-600">Secure payment processing by Stripe</span>
              </div>

              {onCancel && (
                <button
                  onClick={onCancel}
                  className="w-full px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center justify-center font-medium booking-button"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                  Return to Review
                </button>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default BookingPayment;
