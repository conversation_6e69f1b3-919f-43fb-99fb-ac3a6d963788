  // Step 7: Review & Submit
  const renderReview = () => (
    <div className="space-y-6">
      <div className="bg-gray-50 p-6 rounded-lg space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Basic Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Venue Name</p>
              <p>{formData.name || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Phone Number</p>
              <p>{formData.phoneNumber || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Venue Type</p>
              <p>{formData.type || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Address</p>
              <p>{formData.address || 'Not provided'}</p>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Venue Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Size</p>
              <p>{formData.size ? `${formData.size} m²` : 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Maximum Guests</p>
              <p>{formData.maxGuests || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Hourly Rate</p>
              <p>{formData.price ? `$${formData.price}` : 'Not provided'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Function Rooms</p>
              <p>{formData.functionRooms || 'None'}</p>
            </div>
          </div>
          
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-500">Description</p>
            <p className="mt-1">{formData.description || 'Not provided'}</p>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Amenities</h3>
          {formData.amenities.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {formData.amenities.map(amenityId => {
                const amenity = amenityOptions.find(a => a.id === amenityId);
                return amenity ? (
                  <span key={amenityId} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    {amenity.label}
                  </span>
                ) : null;
              })}
            </div>
          ) : (
            <p className="text-gray-500">No amenities selected</p>
          )}
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Photos</h3>
          <div className="grid grid-cols-4 gap-2">
            {formData.images.length > 0 ? (
              formData.images.map((image, index) => (
                <img 
                  key={index} 
                  src={image} 
                  alt={`Venue ${index + 1}`} 
                  className="h-20 w-full object-cover rounded"
                />
              ))
            ) : (
              <p className="col-span-4 text-gray-500">No photos uploaded</p>
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <div className="flex items-center">
          <input
            id="terms-agreement"
            type="checkbox"
            required
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="terms-agreement" className="ml-2 block text-sm text-gray-700">
            I agree to HouseGoing's <a href="/terms" className="text-purple-600 hover:text-purple-500">Terms of Service</a> and <a href="/privacy" className="text-purple-600 hover:text-purple-500">Privacy Policy</a>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Add New Venue</h1>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div 
              key={index} 
              className="flex flex-col items-center cursor-pointer"
              onClick={() => index <= currentStep && setCurrentStep(index)}
            >
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep >= index ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
              <span className="text-xs mt-1 hidden md:block">{step}</span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute inset-0 flex items-center" aria-hidden="true">
            <div className="w-full border-t border-gray-300"></div>
          </div>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
          <p className="font-medium">Venue submitted successfully!</p>
          <p>Thank you for listing your venue with HouseGoing. Our team will review your submission and get back to you shortly.</p>
          <p>You'll be redirected to your properties dashboard in a moment...</p>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Current Step Content */}
        {renderStep()}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t">
          {currentStep > 0 ? (
            <button
              type="button"
              onClick={prevStep}
              className="flex items-center px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </button>
          ) : (
            <div></div>
          )}

          {currentStep < steps.length - 1 ? (
            <button
              type="button"
              onClick={nextStep}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </button>
          ) : (
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:bg-purple-300"
            >
              {submitting ? 'Submitting...' : 'Submit Venue'}
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
