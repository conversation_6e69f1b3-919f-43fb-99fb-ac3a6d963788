/**
 * Email Service for HouseGoing
 * Client-side service that communicates with the email server API
 */

// API base URL - adjust based on your server configuration
const API_BASE_URL = 'http://localhost:3001/api';

/**
 * Send a test email
 * @returns {Promise<Object>} Response from the server
 */
export const sendTestEmail = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/test-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Test email sent successfully:', data);
    return data.success;
  } catch (error) {
    console.error('Error sending test email:', error);
    return false;
  }
};

/**
 * Send property submission email
 * @param {Object} propertyData - Property submission data
 * @returns {Promise<Object>} Response from the server
 */
export const sendPropertySubmissionEmail = async (propertyData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/property-submission-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(propertyData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Property submission email sent successfully:', data);
    return data.success;
  } catch (error) {
    console.error('Error sending property submission email:', error);
    return false;
  }
};

// Mock functions for testing when server is not available
export const mockSendTestEmail = async () => {
  console.log('MOCK: Test email would be sent');
  return true;
};

export const mockSendPropertySubmissionEmail = async (propertyData) => {
  console.log('MOCK: Property submission email would be sent with data:', propertyData);
  return true;
};

export default {
  sendTestEmail,
  sendPropertySubmissionEmail,
  mockSendTestEmail,
  mockSendPropertySubmissionEmail
};
