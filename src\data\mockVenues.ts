// Mock venue data for testing search functionality
export interface MockVenue {
  id: string;
  title: string; // Changed from 'name' to match frontend
  location: {
    suburb: string;
    postcode: string;
    state: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  pricing: {
    hourlyRate: number;
    minimumHours: number;
    cleaningFee: number;
    securityDeposit: number;
    totalMinimum: number;
    // Owner-configurable progressive pricing
    progressivePricing?: {
      enabled: boolean;
      tiers: Array<{
        minHours: number;
        maxHours?: number;
        discountPercentage: number;
        label: string;
      }>;
    };
    // Owner-configurable surcharges
    surcharges?: {
      weekend?: number; // percentage
      holiday?: number; // percentage
      lateNight?: number; // percentage
      overtime?: number; // percentage for bookings over certain hours
    };
  };
  capacity: {
    standing: number;
    seated: number;
    recommended: number;
  };
  venueType: string; // Keep for internal use
  eventTypes: string[]; // Add to match filter expectations
  amenities: string[];
  features: string[];
  description: string;
  images: string[];
  availability: {
    daysAvailable: string[];
    timeSlots: string[];
    blackoutDates: string[];
  };
  host: {
    id: string; // Add ID for host
    name: string;
    responseTime: string;
    rating: number;
    reviewCount: number;
    image?: string; // Add image for host
  };
  rules: string[];
  partyScore: number;
  verified: boolean;
  instantBook: boolean;
  // Add frontend-compatible fields
  price: number; // Simplified price for display
  rating: number; // Venue rating
  reviews: number; // Review count
}

export const mockVenues: MockVenue[] = [
  // Sydney CBD
  {
    id: 'venue-001',
    title: 'Harbour View Rooftop Terrace',
    location: {
      suburb: 'Sydney',
      postcode: '2000',
      state: 'NSW',
      address: '123 George Street, Sydney NSW 2000',
      latitude: -33.8688,
      longitude: 151.2093
    },
    pricing: {
      hourlyRate: 180,
      minimumHours: 4,
      cleaningFee: 150,
      securityDeposit: 500,
      totalMinimum: 870,
      // Owner configured progressive pricing
      progressivePricing: {
        enabled: true,
        tiers: [
          { minHours: 1, maxHours: 3, discountPercentage: 0, label: "Standard Rate" },
          { minHours: 4, maxHours: 6, discountPercentage: 5, label: "4-6 Hours (5% off)" },
          { minHours: 7, maxHours: 10, discountPercentage: 10, label: "7-10 Hours (10% off)" },
          { minHours: 11, discountPercentage: 15, label: "11+ Hours (15% off)" }
        ]
      },
      surcharges: {
        weekend: 20, // 20% weekend surcharge
        holiday: 30, // 30% holiday surcharge
        lateNight: 15, // 15% late night surcharge (after 10 PM)
        overtime: 25 // 25% overtime for bookings over 12 hours
      }
    },
    capacity: {
      standing: 80,
      seated: 50,
      recommended: 60
    },
    venueType: 'Rooftop Terrace',
    eventTypes: ['Corporate', 'Social', 'Birthday'], // Match filter options
    amenities: ['WiFi', 'Sound System', 'Catering', 'Parking'], // Match filter options
    features: ['City Views', 'Professional Lighting', 'Dance Floor', 'Outdoor Space', 'Photo Opportunities'],
    description: '✨ Breathtaking 360° harbour views from this premium rooftop venue. Glass pavilion with city skyline backdrop, perfect for Instagram-worthy celebrations!',
    images: [
      'https://images.unsplash.com/photo-1566737236500-c8ac43014a67?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1551632436-cbf8dd35adfa?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Thursday', 'Friday', 'Saturday', 'Sunday'],
      timeSlots: ['6:00 PM - 12:00 AM', '2:00 PM - 10:00 PM'],
      blackoutDates: ['2024-12-31', '2024-01-01']
    },
    host: {
      id: 'host-001',
      name: 'Sarah Chen',
      responseTime: '1 hour',
      rating: 4.9,
      reviewCount: 127,
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200&q=80'
    },
    rules: ['No smoking indoors', 'Music must end by 12 AM', 'Maximum 80 guests', 'Professional security required for 60+ guests'],
    partyScore: 9.5,
    verified: true,
    instantBook: true,
    // Frontend-compatible fields
    price: 870, // Use totalMinimum as display price
    rating: 4.9, // Copy from host rating
    reviews: 127 // Copy from host reviewCount
  },

  // Bondi Beach
  {
    id: 'venue-002',
    title: 'Bondi Beach House', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Bondi Beach',
      postcode: '2026',
      state: 'NSW',
      address: '45 Campbell Parade, Bondi Beach NSW 2026',
      latitude: -33.8915,
      longitude: 151.2767
    },
    pricing: {
      hourlyRate: 120,
      minimumHours: 6,
      cleaningFee: 200,
      securityDeposit: 800,
      totalMinimum: 1720,
      // Different owner pricing strategy - no progressive pricing, just surcharges
      progressivePricing: {
        enabled: false,
        tiers: []
      },
      surcharges: {
        weekend: 25, // Higher weekend surcharge for beach location
        holiday: 40, // Premium holiday pricing
        lateNight: 20, // Beach house late night premium
      }
    },
    capacity: {
      standing: 100,
      seated: 70,
      recommended: 80
    },
    venueType: 'Beach House',
    eventTypes: ['Birthday', 'Social', 'Wedding'], // Added missing eventTypes field
    amenities: ['Sound System', 'Kitchen', 'Parking'], // Updated to match filter options
    features: ['Beach Access', 'Outdoor Deck', 'Pool Area', 'BBQ Facilities', 'Sunset Views'],
    description: '🏖️ Iconic beachfront paradise with private pool & ocean views. Steps from Bondi Beach with sunset deck - the ultimate summer party destination!',
    images: [
      'https://images.unsplash.com/photo-1533929736458-ca588d08c8be?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['12:00 PM - 10:00 PM', '4:00 PM - 11:00 PM'],
      blackoutDates: ['2024-12-25', '2024-12-26']
    },
    host: {
      id: 'host-002', // Added missing host id
      name: 'Mike Thompson',
      responseTime: '2 hours',
      rating: 4.7,
      reviewCount: 89,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['No glass on pool deck', 'Music ends at 10 PM', 'Beach access supervised', 'No smoking indoors'],
    partyScore: 8.8,
    verified: true,
    instantBook: false,
    // Frontend-compatible fields
    price: 1720, // Use totalMinimum as display price
    rating: 4.7, // Copy from host rating
    reviews: 89 // Copy from host reviewCount
  },

  // Newtown - Updated with precise coordinates
  {
    id: 'venue-003',
    title: 'Industrial Warehouse Loft', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Newtown',
      postcode: '2042',
      state: 'NSW',
      address: '78 King Street, Newtown NSW 2042',
      latitude: -33.8978,
      longitude: 151.1795
    },
    pricing: {
      hourlyRate: 90,
      minimumHours: 5,
      cleaningFee: 100,
      securityDeposit: 400,
      totalMinimum: 950
    },
    capacity: {
      standing: 120,
      seated: 80,
      recommended: 100
    },
    venueType: 'Warehouse',
    eventTypes: ['Corporate', 'Social', 'Birthday'], // Added missing eventTypes field
    amenities: ['Sound System', 'Parking'], // Updated to match filter options
    features: ['High Ceilings', 'Exposed Brick', 'Industrial Aesthetic', 'Flexible Layout', 'Art Gallery Space'],
    description: '🏭 Trendy warehouse with exposed brick & soaring ceilings. Industrial-chic vibes perfect for creative parties, art shows & underground events!',
    images: [
      'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1497366216548-37526070297c?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1540932239986-30128078f3c5?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Thursday', 'Friday', 'Saturday', 'Sunday'],
      timeSlots: ['7:00 PM - 2:00 AM', '2:00 PM - 12:00 AM'],
      blackoutDates: []
    },
    host: {
      id: 'host-003', // Added missing host id
      name: 'Alex Rodriguez',
      responseTime: '30 minutes',
      rating: 4.8,
      reviewCount: 156,
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['BYOB permitted', 'No open flames', 'Load-in via dock only', 'Sound levels monitored'],
    partyScore: 9.2,
    verified: true,
    instantBook: true,
    // Frontend-compatible fields
    price: 950, // Use totalMinimum as display price
    rating: 4.8, // Copy from host rating
    reviews: 156 // Copy from host reviewCount
  },

  // Manly
  {
    id: 'venue-004',
    title: 'Manly Pavilion', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Manly',
      postcode: '2095',
      state: 'NSW',
      address: '22 The Corso, Manly NSW 2095',
      latitude: -33.7969,
      longitude: 151.2502
    },
    pricing: {
      hourlyRate: 150,
      minimumHours: 4,
      cleaningFee: 180,
      securityDeposit: 600,
      totalMinimum: 1380
    },
    capacity: {
      standing: 90,
      seated: 60,
      recommended: 70
    },
    venueType: 'Pavilion',
    eventTypes: ['Wedding', 'Corporate', 'Social'], // Added missing eventTypes field
    amenities: ['Sound System', 'Kitchen', 'Parking'], // Updated to match filter options
    features: ['Ocean Breeze', 'Covered Outdoor Area', 'Professional Lighting', 'Beachside Location'],
    description: '🌊 Elegant beachfront pavilion with panoramic ocean views. Sophisticated coastal vibes perfect for dreamy celebrations & milestone events!',
    images: [
      'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1544148103-0773bf10d330?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['5:00 PM - 11:00 PM', '12:00 PM - 6:00 PM'],
      blackoutDates: ['2024-12-24', '2024-12-25']
    },
    host: {
      id: 'host-004', // Added missing host id
      name: 'Emma Wilson',
      responseTime: '1 hour',
      rating: 4.9,
      reviewCount: 203,
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['No BYO alcohol', 'Catering required', 'Music ends at 11 PM', 'Beach access included'],
    partyScore: 9.4,
    verified: true,
    instantBook: false,
    // Frontend-compatible fields
    price: 1380, // Use totalMinimum as display price
    rating: 4.9, // Copy from host rating
    reviews: 203 // Copy from host reviewCount
  },

  // Surry Hills
  {
    id: 'venue-005',
    title: 'Boutique Gallery Space', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Surry Hills',
      postcode: '2010',
      state: 'NSW',
      address: '156 Crown Street, Surry Hills NSW 2010',
      latitude: -33.8886,
      longitude: 151.2094
    },
    pricing: {
      hourlyRate: 110,
      minimumHours: 4,
      cleaningFee: 120,
      securityDeposit: 350,
      totalMinimum: 910
    },
    capacity: {
      standing: 60,
      seated: 40,
      recommended: 50
    },
    venueType: 'Gallery',
    eventTypes: ['Corporate', 'Social'], // Added missing eventTypes field
    amenities: ['Sound System', 'Parking'], // Updated to match filter options
    features: ['White Walls', 'Natural Light', 'Intimate Setting', 'Artistic Atmosphere', 'Flexible Layout'],
    description: 'Chic gallery space in the heart of Surry Hills. Features white walls, natural lighting, and an intimate courtyard. Perfect for art openings, product launches, and sophisticated gatherings.',
    images: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1559329007-40df8a9345d8?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Wednesday', 'Thursday', 'Friday', 'Saturday'],
      timeSlots: ['6:00 PM - 12:00 AM', '2:00 PM - 8:00 PM'],
      blackoutDates: []
    },
    host: {
      id: 'host-005', // Added missing host id
      name: 'David Kim',
      responseTime: '45 minutes',
      rating: 4.6,
      reviewCount: 74,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['No food near artworks', 'Wine service only', 'Quiet hours after 10 PM', 'Art handling fee applies'],
    partyScore: 8.5, // Fixed partyScore format (should be decimal, not integer)
    verified: true,
    instantBook: true,
    // Frontend-compatible fields
    price: 910, // Use totalMinimum as display price
    rating: 4.6, // Copy from host rating
    reviews: 74 // Copy from host reviewCount
  },

  // Parramatta - Venue 1 (Main exact match)
  {
    id: 'venue-006',
    title: 'Riverside Function Centre', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Parramatta',
      postcode: '2150',
      state: 'NSW',
      address: '88 Macquarie Street, Parramatta NSW 2150',
      latitude: -33.8150,
      longitude: 151.0011
    },
    pricing: {
      hourlyRate: 85,
      minimumHours: 6,
      cleaningFee: 150,
      securityDeposit: 500,
      totalMinimum: 1160
    },
    capacity: {
      standing: 150,
      seated: 120,
      recommended: 130
    },
    venueType: 'Function Centre',
    eventTypes: ['Wedding', 'Corporate', 'Social'], // Added missing eventTypes field
    amenities: ['Sound System', 'Kitchen', 'Parking'], // Updated to match filter options
    features: ['Riverside Location', 'Large Windows', 'Professional Catering', 'Flexible Seating', 'Garden Area'],
    description: '🏢 Modern function centre overlooking the Parramatta River. Features floor-to-ceiling windows, professional catering facilities, and beautiful garden areas. Perfect for large celebrations and corporate events.',
    images: [
      'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1566737236500-c8ac43014a67?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['6:00 PM - 12:00 AM', '12:00 PM - 10:00 PM'],
      blackoutDates: ['2024-12-31']
    },
    host: {
      id: 'host-006', // Added missing host id
      name: 'Jennifer Park',
      responseTime: '2 hours',
      rating: 4.5,
      reviewCount: 98,
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['Catering package required', 'No BYO alcohol', 'Music ends at midnight', 'Parking validation included'],
    partyScore: 8.7,
    verified: true,
    instantBook: false,
    // Frontend-compatible fields
    price: 1160, // Use totalMinimum as display price
    rating: 4.5, // Copy from host rating
    reviews: 98 // Copy from host reviewCount
  },

  // Parramatta - Venue 2 (Second exact match for testing)
  {
    id: 'venue-006b',
    title: 'Parramatta Park Pavilion', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Parramatta',
      postcode: '2150',
      state: 'NSW',
      address: '15 Pitt Street, Parramatta NSW 2150',
      latitude: -33.8134,
      longitude: 151.0042
    },
    pricing: {
      hourlyRate: 95,
      minimumHours: 4,
      cleaningFee: 120,
      securityDeposit: 400,
      totalMinimum: 900
    },
    capacity: {
      standing: 80,
      seated: 60,
      recommended: 70
    },
    venueType: 'Park Pavilion',
    eventTypes: ['Birthday', 'Social', 'Wedding'], // Added missing eventTypes field
    amenities: ['Sound System', 'Kitchen', 'Parking'], // Updated to match filter options
    features: ['Historic Setting', 'Natural Light', 'Outdoor Space', 'Heritage Building', 'Flexible Layout'],
    description: '🌳 Historic pavilion in beautiful Parramatta Park. Surrounded by heritage gardens with modern facilities. Perfect for garden parties, family celebrations, and outdoor events.',
    images: [
      'https://images.unsplash.com/photo-1544148103-0773bf10d330?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1533929736458-ca588d08c8be?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Thursday', 'Friday', 'Saturday', 'Sunday'],
      timeSlots: ['10:00 AM - 10:00 PM', '2:00 PM - 8:00 PM'],
      blackoutDates: []
    },
    host: {
      id: 'host-006b', // Added missing host id
      name: 'Robert Kim',
      responseTime: '1 hour',
      rating: 4.7,
      reviewCount: 142,
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['Park regulations apply', 'BYO permitted', 'Music ends at 10 PM', 'Garden care required'],
    partyScore: 8.4,
    verified: true,
    instantBook: true,
    // Frontend-compatible fields
    price: 900, // Use totalMinimum as display price
    rating: 4.7, // Copy from host rating
    reviews: 142 // Copy from host reviewCount
  },

  // Chatswood
  {
    id: 'venue-007',
    title: 'Garden Terrace Restaurant', // Changed from 'name' to 'title' for consistency
    location: {
      suburb: 'Chatswood',
      postcode: '2067',
      state: 'NSW',
      address: '234 Victoria Avenue, Chatswood NSW 2067',
      latitude: -33.7967,
      longitude: 151.1800
    },
    pricing: {
      hourlyRate: 95,
      minimumHours: 4,
      cleaningFee: 100,
      securityDeposit: 300,
      totalMinimum: 780
    },
    capacity: {
      standing: 70,
      seated: 50,
      recommended: 60
    },
    venueType: 'Restaurant',
    eventTypes: ['Wedding', 'Social'], // Added missing eventTypes field
    amenities: ['Sound System', 'Kitchen', 'Parking'], // Updated to match filter options
    features: ['Lush Gardens', 'Covered Terrace', 'Intimate Dining', 'Professional Service', 'Seasonal Menu'],
    description: 'Charming restaurant with beautiful garden terrace in Chatswood. Features lush greenery, intimate lighting, and professional catering. Ideal for dinner parties, anniversaries, and small celebrations.',
    images: ['/images/venues/chatswood-garden-1.jpg', '/images/venues/chatswood-garden-2.jpg'],
    availability: {
      daysAvailable: ['Friday', 'Saturday'], // Limited availability for testing
      timeSlots: ['6:00 PM - 11:00 PM', '12:00 PM - 4:00 PM'],
      blackoutDates: ['2024-12-24', '2024-12-25']
    },
    host: {
      id: 'host-007', // Added missing host id
      name: 'Tony Nguyen',
      responseTime: '3 hours',
      rating: 4.4,
      reviewCount: 67,
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=200&q=80' // Added missing host image
    },
    rules: ['Food package required', 'BYO wine allowed', 'Quiet venue', 'Garden care required'],
    partyScore: 8.2, // Fixed partyScore format (should be decimal, not integer)
    verified: true,
    instantBook: true,
    // Frontend-compatible fields
    price: 780, // Use totalMinimum as display price
    rating: 4.4, // Copy from host rating
    reviews: 67 // Copy from host reviewCount
  },

  // Cronulla
  {
    id: 'venue-008',
    name: 'Beachside Event Space',
    location: {
      suburb: 'Cronulla',
      postcode: '2230',
      state: 'NSW',
      address: '67 Kingsway, Cronulla NSW 2230',
      latitude: -34.0581,
      longitude: 151.1543
    },
    pricing: {
      hourlyRate: 75,
      minimumHours: 5,
      cleaningFee: 120,
      securityDeposit: 400,
      totalMinimum: 895
    },
    capacity: {
      standing: 80,
      seated: 60,
      recommended: 70
    },
    venueType: 'Event Space',
    amenities: ['Beach Views', 'BBQ Area', 'Sound System', 'Bar Setup', 'Outdoor Deck', 'Parking'],
    features: ['Ocean Views', 'Casual Atmosphere', 'BBQ Facilities', 'Sunset Location', 'Beach Access'],
    description: 'Relaxed beachside venue with stunning ocean views and casual atmosphere. Features outdoor BBQ area and spacious deck. Perfect for casual celebrations, beach parties, and summer events.',
    images: ['/images/venues/cronulla-beach-1.jpg', '/images/venues/cronulla-beach-2.jpg'],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['2:00 PM - 10:00 PM', '5:00 PM - 11:00 PM'],
      blackoutDates: []
    },
    host: {
      name: 'Lisa Martinez',
      responseTime: '1 hour',
      rating: 4.3,
      reviewCount: 45
    },
    rules: ['BYO permitted', 'Beach cleanup required', 'Music ends at 10 PM', 'No glass on beach'],
    partyScore: 7.9,
    verified: true,
    instantBook: true
  },

  // Ryde (Close to Parramatta - good suggestion)
  {
    id: 'venue-009',
    title: 'Community Hall & Gardens',
    location: {
      suburb: 'Ryde',
      postcode: '2112',
      state: 'NSW',
      address: '45 Blaxland Road, Ryde NSW 2112',
      latitude: -33.8149,
      longitude: 151.1056
    },
    pricing: {
      hourlyRate: 60,
      minimumHours: 4,
      cleaningFee: 80,
      securityDeposit: 250,
      totalMinimum: 570
    },
    capacity: {
      standing: 100,
      seated: 80,
      recommended: 90
    },
    venueType: 'Community Hall',
    amenities: ['Large Hall', 'Kitchen Facilities', 'Sound System', 'Tables & Chairs', 'Garden Area', 'Parking'],
    features: ['Spacious Interior', 'Community Feel', 'Affordable Pricing', 'Flexible Setup', 'Garden Access'],
    description: '🏛️ Spacious community hall with adjoining gardens in Ryde. Features large open space, full kitchen facilities, and beautiful garden areas. Perfect for family celebrations, community events, and budget-friendly parties.',
    images: [
      'https://images.unsplash.com/photo-1497366216548-37526070297c?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1540932239986-30128078f3c5?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['6:00 PM - 12:00 AM', '12:00 PM - 6:00 PM'],
      blackoutDates: []
    },
    host: {
      id: 'host-009',
      name: 'Margaret Chen',
      responseTime: '4 hours',
      rating: 4.2,
      reviewCount: 34,
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=200&q=80'
    },
    rules: ['BYO everything', 'Clean up required', 'No alcohol in garden', 'Community guidelines apply'],
    partyScore: 7.5,
    verified: true,
    instantBook: false,
    // Frontend-compatible fields
    price: 570, // Use totalMinimum as display price
    rating: 4.2, // Copy from host rating
    reviews: 34 // Copy from host reviewCount
  },

  // Westmead (Very close to Parramatta - excellent suggestion)
  {
    id: 'venue-009b',
    name: 'Westmead Sports Club',
    location: {
      suburb: 'Westmead',
      postcode: '2145',
      state: 'NSW',
      address: '12 Darcy Road, Westmead NSW 2145',
      latitude: -33.8067,
      longitude: 150.9875
    },
    pricing: {
      hourlyRate: 75,
      minimumHours: 5,
      cleaningFee: 100,
      securityDeposit: 300,
      totalMinimum: 775
    },
    capacity: {
      standing: 120,
      seated: 90,
      recommended: 100
    },
    venueType: 'Sports Club',
    amenities: ['Full Bar', 'Kitchen', 'Sound System', 'Dance Floor', 'Sports Facilities', 'Parking'],
    features: ['Club Atmosphere', 'Large Space', 'Sports Views', 'Member Rates', 'Flexible Layout'],
    description: '🏆 Modern sports club with spacious function room and full facilities. Features professional bar service, kitchen, and sports viewing areas. Perfect for celebrations, corporate events, and sports-themed parties.',
    images: [
      'https://images.unsplash.com/photo-1571896349842-33c89424de2d?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1566737236500-c8ac43014a67?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['6:00 PM - 1:00 AM', '12:00 PM - 6:00 PM'],
      blackoutDates: []
    },
    host: {
      name: 'Steve Williams',
      responseTime: '2 hours',
      rating: 4.4,
      reviewCount: 67
    },
    rules: ['Club dress code', 'Member rates available', 'Bar service required', 'Sports events priority'],
    partyScore: 8.1,
    verified: true,
    instantBook: true
  },

  // Harris Park (Adjacent to Parramatta - great suggestion)
  {
    id: 'venue-009c',
    name: 'Cultural Centre Banquet Hall',
    location: {
      suburb: 'Harris Park',
      postcode: '2150',
      state: 'NSW',
      address: '78 Station Street, Harris Park NSW 2150',
      latitude: -33.8234,
      longitude: 151.0156
    },
    pricing: {
      hourlyRate: 80,
      minimumHours: 6,
      cleaningFee: 120,
      securityDeposit: 400,
      totalMinimum: 1000
    },
    capacity: {
      standing: 200,
      seated: 150,
      recommended: 170
    },
    venueType: 'Banquet Hall',
    amenities: ['Large Hall', 'Commercial Kitchen', 'Sound System', 'Stage Area', 'Parking', 'Cultural Decor'],
    features: ['Grand Space', 'Cultural Atmosphere', 'Professional Kitchen', 'Stage Performance', 'Traditional Decor'],
    description: '🏛️ Grand cultural centre with traditional architecture and modern facilities. Features large banquet hall, commercial kitchen, and stage area. Perfect for cultural celebrations, weddings, and large gatherings.',
    images: [
      'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['5:00 PM - 12:00 AM', '11:00 AM - 5:00 PM'],
      blackoutDates: []
    },
    host: {
      name: 'Priya Sharma',
      responseTime: '3 hours',
      rating: 4.6,
      reviewCount: 89
    },
    rules: ['Cultural respect required', 'Catering preferred', 'Traditional music welcome', 'Decoration guidelines'],
    partyScore: 8.8,
    verified: true,
    instantBook: false
  },

  // Penrith
  {
    id: 'venue-010',
    name: 'Mountain View Function Room',
    location: {
      suburb: 'Penrith',
      postcode: '2750',
      state: 'NSW',
      address: '123 High Street, Penrith NSW 2750',
      latitude: -33.7506,
      longitude: 150.6944
    },
    pricing: {
      hourlyRate: 70,
      minimumHours: 6,
      cleaningFee: 100,
      securityDeposit: 350,
      totalMinimum: 870
    },
    capacity: {
      standing: 120,
      seated: 100,
      recommended: 110
    },
    venueType: 'Function Room',
    amenities: ['Mountain Views', 'Full Bar', 'Kitchen Access', 'Sound System', 'Dance Floor', 'Free Parking'],
    features: ['Scenic Views', 'Large Space', 'Professional Setup', 'Affordable Rates', 'Easy Access'],
    description: 'Large function room with stunning Blue Mountains views. Features professional setup, full bar facilities, and ample parking. Perfect for weddings, large celebrations, and corporate events in Western Sydney.',
    images: ['/images/venues/penrith-function-1.jpg', '/images/venues/penrith-function-2.jpg'],
    availability: {
      daysAvailable: ['Friday', 'Saturday', 'Sunday'],
      timeSlots: ['6:00 PM - 1:00 AM', '12:00 PM - 10:00 PM'],
      blackoutDates: ['2024-12-31']
    },
    host: {
      name: 'Robert Smith',
      responseTime: '2 hours',
      rating: 4.1,
      reviewCount: 52
    },
    rules: ['Bar package required', 'Security for 80+ guests', 'Music ends at 1 AM', 'Mountain view guaranteed'],
    partyScore: 7.8,
    verified: true,
    instantBook: false
  },

  // Additional Newtown venues
  {
    id: 'venue-011',
    name: 'Newtown Social Club',
    location: {
      suburb: 'Newtown',
      postcode: '2042',
      state: 'NSW',
      address: '387 King Street, Newtown NSW 2042',
      latitude: -33.8988,
      longitude: 151.1810
    },
    pricing: {
      hourlyRate: 90,
      minimumHours: 4,
      cleaningFee: 100,
      securityDeposit: 300,
      totalMinimum: 760
    },
    capacity: {
      standing: 80,
      seated: 50,
      recommended: 65
    },
    venueType: 'Social Club',
    amenities: ['Live Music Stage', 'Full Bar', 'Sound System', 'Vintage Decor', 'Street Art', 'Outdoor Area'],
    features: ['Bohemian Vibes', 'Live Music', 'Craft Cocktails', 'Alternative Scene', 'Instagram Worthy'],
    description: '🎸 Iconic Newtown social club with live music stage & bohemian vibes. Vintage decor meets street art culture - perfect for alternative celebrations & music lovers!',
    images: [
      'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1571266028243-d220c9c3b8c2?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Thursday', 'Friday', 'Saturday', 'Sunday'],
      timeSlots: ['7:00 PM - 1:00 AM', '3:00 PM - 11:00 PM'],
      blackoutDates: []
    },
    host: {
      name: 'Jake Morrison',
      responseTime: '1 hour',
      rating: 4.7,
      reviewCount: 142
    },
    rules: ['Live music encouraged', 'BYO vinyl records', 'Respect the art', 'Alternative dress code welcome'],
    partyScore: 9.2,
    verified: true,
    instantBook: true
  },

  {
    id: 'venue-012',
    name: 'Rooftop Garden Bar',
    location: {
      suburb: 'Newtown',
      postcode: '2042',
      state: 'NSW',
      address: '245 King Street, Newtown NSW 2042',
      latitude: -33.8975,
      longitude: 151.1795
    },
    pricing: {
      hourlyRate: 110,
      minimumHours: 4,
      cleaningFee: 120,
      securityDeposit: 400,
      totalMinimum: 960
    },
    capacity: {
      standing: 60,
      seated: 40,
      recommended: 50
    },
    venueType: 'Rooftop Bar',
    amenities: ['City Views', 'Garden Setting', 'Cocktail Bar', 'Heating', 'Sound System', 'Photo Booth'],
    features: ['Urban Garden', 'Fairy Lights', 'Sunset Views', 'Intimate Setting', 'Craft Cocktails'],
    description: '🌿 Secret rooftop garden bar above King Street. Urban oasis with fairy lights, city views & craft cocktails - perfect for intimate celebrations & date nights!',
    images: [
      'https://images.unsplash.com/photo-1514933651103-005eec06c04b?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1559329007-40df8a9345d8?auto=format&fit=crop&w=800&q=80'
    ],
    availability: {
      daysAvailable: ['Wednesday', 'Thursday', 'Friday', 'Saturday'],
      timeSlots: ['6:00 PM - 12:00 AM', '4:00 PM - 10:00 PM'],
      blackoutDates: []
    },
    host: {
      name: 'Sophie Chen',
      responseTime: '30 minutes',
      rating: 4.8,
      reviewCount: 89
    },
    rules: ['Cocktail packages available', 'Garden care required', 'Quiet after 11 PM', 'Weather dependent'],
    partyScore: 8.9,
    verified: true,
    instantBook: true
  }
];

// Transform venues to ensure compatibility with frontend
export const transformVenueForFrontend = (venue: any): MockVenue => {
  // Map venue types to event types
  const venueTypeToEventTypes: { [key: string]: string[] } = {
    'Rooftop Terrace': ['Corporate', 'Social', 'Birthday'],
    'Beachside Villa': ['Wedding', 'Social', 'Birthday'],
    'Art Gallery': ['Corporate', 'Social'],
    'Function Centre': ['Wedding', 'Corporate', 'Social'],
    'Park Pavilion': ['Wedding', 'Social', 'Birthday'],
    'Restaurant': ['Wedding', 'Social'],
    'Event Space': ['Social', 'Birthday', 'Corporate'],
    'Community Hall': ['Social', 'Birthday'],
    'Sports Club': ['Corporate', 'Social', 'Birthday'],
    'Banquet Hall': ['Wedding', 'Corporate', 'Social'],
    'Function Room': ['Wedding', 'Corporate', 'Social'],
    'Social Club': ['Social', 'Birthday'],
    'Rooftop Bar': ['Social', 'Birthday', 'Corporate']
  };

  // Standardize amenities to match filter options
  const standardizeAmenities = (amenities: string[]): string[] => {
    const standardAmenities: string[] = [];

    amenities.forEach(amenity => {
      const lower = amenity.toLowerCase();
      if (lower.includes('wifi') || lower.includes('internet')) standardAmenities.push('WiFi');
      if (lower.includes('kitchen') || lower.includes('catering')) standardAmenities.push('Kitchen');
      if (lower.includes('parking')) standardAmenities.push('Parking');
      if (lower.includes('sound') || lower.includes('audio')) standardAmenities.push('Sound System');
      if (lower.includes('bar') || lower.includes('drinks')) standardAmenities.push('Catering');
    });

    return [...new Set(standardAmenities)]; // Remove duplicates
  };

  return {
    ...venue,
    title: venue.title || venue.name, // Ensure title exists
    eventTypes: venue.eventTypes || venueTypeToEventTypes[venue.venueType] || ['Social'],
    amenities: standardizeAmenities(venue.amenities || []),
    host: {
      ...venue.host,
      id: venue.host?.id || `host-${venue.id}`,
      image: venue.host?.image || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=200&q=80'
    },
    price: venue.price || venue.pricing?.totalMinimum || 500,
    rating: venue.rating || venue.host?.rating || 4.5,
    reviews: venue.reviews || venue.host?.reviewCount || 50
  };
};

// Export transformed venues for use in the app
export const transformedMockVenues = mockVenues.map(transformVenueForFrontend);

// Helper functions for filtering venues
export const filterVenuesByLocation = (venues: MockVenue[], suburb: string): MockVenue[] => {
  return venues.filter(venue =>
    venue.location.suburb.toLowerCase().includes(suburb.toLowerCase())
  );
};

// Calculate distance between two coordinates using Haversine formula
export const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in kilometers
};

// Sort venues by distance from a given coordinate
export const sortVenuesByDistance = (venues: MockVenue[], searchLat: number, searchLng: number): (MockVenue & { distanceKm: number })[] => {
  return venues.map(venue => ({
    ...venue,
    distanceKm: calculateDistance(searchLat, searchLng, venue.location.latitude, venue.location.longitude)
  })).sort((a, b) => a.distanceKm - b.distanceKm);
};

export const filterVenuesByBudget = (venues: MockVenue[], maxBudget: number): MockVenue[] => {
  return venues.filter(venue => venue.pricing.totalMinimum <= maxBudget);
};

export const filterVenuesByCapacity = (venues: MockVenue[], guestCount: number): MockVenue[] => {
  return venues.filter(venue => venue.capacity.recommended >= guestCount);
};

export const filterVenuesByType = (venues: MockVenue[], venueType: string): MockVenue[] => {
  return venues.filter(venue =>
    venue.venueType.toLowerCase().includes(venueType.toLowerCase()) ||
    venue.eventTypes?.some(type => type.toLowerCase().includes(venueType.toLowerCase()))
  );
};

// Filter by event types (for frontend filters)
export const filterVenuesByEventTypes = (venues: MockVenue[], eventTypes: string[]): MockVenue[] => {
  if (!eventTypes || eventTypes.length === 0) return venues;

  return venues.filter(venue =>
    venue.eventTypes?.some(venueEventType =>
      eventTypes.some(filterEventType =>
        venueEventType.toLowerCase().includes(filterEventType.toLowerCase())
      )
    )
  );
};

// Filter by amenities (for frontend filters)
export const filterVenuesByAmenities = (venues: MockVenue[], amenities: string[]): MockVenue[] => {
  if (!amenities || amenities.length === 0) return venues;

  return venues.filter(venue =>
    amenities.every(amenity =>
      venue.amenities?.some(venueAmenity =>
        venueAmenity.toLowerCase().includes(amenity.toLowerCase())
      )
    )
  );
};

// Filter by price range (for frontend filters)
export const filterVenuesByPriceRange = (venues: MockVenue[], priceRange: string): MockVenue[] => {
  if (!priceRange) return venues;

  const [min, max] = priceRange.split('-').map(p => p.replace('+', ''));
  const minPrice = parseInt(min);
  const maxPrice = max ? parseInt(max) : Infinity;

  return venues.filter(venue => {
    const venuePrice = venue.price || venue.pricing.totalMinimum;
    return venuePrice >= minPrice && venuePrice <= maxPrice;
  });
};

// Filter by guest range (for frontend filters)
export const filterVenuesByGuestRange = (venues: MockVenue[], guestRange: string): MockVenue[] => {
  if (!guestRange) return venues;

  const [min, max] = guestRange.split('-').map(g => g.replace('+', ''));
  const minGuests = parseInt(min);
  const maxGuests = max ? parseInt(max) : Infinity;

  return venues.filter(venue => {
    const capacity = venue.capacity.recommended;
    return capacity >= minGuests && capacity <= maxGuests;
  });
};

// Add date filtering function
export const filterVenuesByDate = (venues: MockVenue[], startDate: string, endDate?: string): MockVenue[] => {
  if (!startDate) return venues;

  const searchDate = new Date(startDate);
  const dayOfWeek = searchDate.toLocaleDateString('en-US', { weekday: 'long' });

  return venues.filter(venue => {
    // Check if venue is available on the requested day
    return venue.availability.daysAvailable.includes(dayOfWeek);
  });
};

// Smart search function with correct priority: Date → Budget → Guests → Location
export const searchVenues = async (
  venues: MockVenue[],
  filters: {
    location?: string;
    startDate?: string;
    endDate?: string;
    maxBudget?: number;
    guestCount?: number;
    venueType?: string;
    eventTypes?: string[];
    amenities?: string[];
    priceRange?: string;
    guestRange?: string;
    instantBook?: boolean;
  }
): Promise<{
  exactMatches: MockVenue[];
  suggestions: MockVenue[];
  searchType: string;
  message?: string;
  suggestionMessage?: string;
  locationSearched?: string;
}> => {

  // Use transformed venues to ensure compatibility
  const transformedVenues = venues.map(transformVenueForFrontend);

  // Priority 1: Date filtering (highest priority)
  let availableVenues = [...transformedVenues];
  if (filters.startDate) {
    availableVenues = filterVenuesByDate(availableVenues, filters.startDate, filters.endDate);
  }

  // Priority 2: Budget filtering (support both old and new formats)
  if (filters.maxBudget) {
    availableVenues = filterVenuesByBudget(availableVenues, filters.maxBudget);
  }
  if (filters.priceRange) {
    availableVenues = filterVenuesByPriceRange(availableVenues, filters.priceRange);
  }

  // Priority 3: Guest count filtering (support both old and new formats)
  if (filters.guestCount) {
    availableVenues = filterVenuesByCapacity(availableVenues, filters.guestCount);
  }
  if (filters.guestRange) {
    availableVenues = filterVenuesByGuestRange(availableVenues, filters.guestRange);
  }

  // Priority 4: Event type filtering (new frontend filters)
  if (filters.eventTypes && filters.eventTypes.length > 0) {
    availableVenues = filterVenuesByEventTypes(availableVenues, filters.eventTypes);
  }

  // Priority 5: Amenities filtering (new frontend filters)
  if (filters.amenities && filters.amenities.length > 0) {
    availableVenues = filterVenuesByAmenities(availableVenues, filters.amenities);
  }

  // Priority 6: Instant book filtering
  if (filters.instantBook) {
    availableVenues = availableVenues.filter(venue => venue.instantBook);
  }

  // Priority 7: Venue type filtering (legacy support)
  if (filters.venueType) {
    availableVenues = filterVenuesByType(availableVenues, filters.venueType);
  }

  // Priority 5: Location filtering with geographic search
  if (filters.location) {
    console.log(`🔍 SEARCH DEBUG: Starting location search for "${filters.location}"`);
    console.log(`📊 SEARCH DEBUG: Available venues before location filter: ${availableVenues.length}`);

    try {
      // Get coordinates for the searched location using NSW suburbs API
      const { searchNSWSuburbs } = await import('../services/nsw-suburbs-search');
      const suburbResults = await searchNSWSuburbs(filters.location, 1);

      if (suburbResults.length > 0 && suburbResults[0].lat && suburbResults[0].lng) {
        const searchCoords = suburbResults[0];
        console.log(`📍 SEARCH DEBUG: Found coordinates for "${filters.location}":`, {
          lat: searchCoords.lat,
          lng: searchCoords.lng
        });

        // Sort all available venues by distance from search location
        const venuesWithDistance = sortVenuesByDistance(availableVenues, searchCoords.lat, searchCoords.lng);
        console.log(`📏 SEARCH DEBUG: Sorted ${venuesWithDistance.length} venues by distance`);

        // Find exact matches (venues in the same suburb - prioritize exact suburb name match over distance)
        const exactMatches = venuesWithDistance.filter(venue => {
          const venueSuburb = venue.location.suburb.toLowerCase().trim();
          const searchLocation = filters.location!.toLowerCase().trim();

          // Check for exact suburb name match (case-insensitive)
          return venueSuburb === searchLocation ||
                 venueSuburb.includes(searchLocation) ||
                 searchLocation.includes(venueSuburb);
        });

        // Get suggestions (other venues sorted by distance)
        const suggestions = venuesWithDistance.filter(venue => {
          const venueSuburb = venue.location.suburb.toLowerCase().trim();
          const searchLocation = filters.location!.toLowerCase().trim();

          // Exclude venues that are exact suburb matches
          return !(venueSuburb === searchLocation ||
                   venueSuburb.includes(searchLocation) ||
                   searchLocation.includes(venueSuburb));
        });

        console.log(`✅ SEARCH DEBUG: Found ${exactMatches.length} exact matches and ${suggestions.length} suggestions`);

        if (exactMatches.length > 0) {
          console.log(`🎯 SEARCH DEBUG: Exact matches:`, exactMatches.map(v => `${v.title} (${v.distanceKm.toFixed(1)}km)`));
        }

        if (suggestions.length > 0) {
          console.log(`📍 SEARCH DEBUG: Closest suggestions:`, suggestions.slice(0, 3).map(v => `${v.title} in ${v.location.suburb} (${v.distanceKm.toFixed(1)}km)`));
        }

        // Keep distance info for API to use (don't remove distanceKm)
        // Return ALL exact matches and suggestions (no artificial limits)
        const cleanExactMatches = exactMatches;
        const cleanSuggestions = suggestions;

        return {
          exactMatches: cleanExactMatches,
          suggestions: cleanSuggestions,
          searchType: exactMatches.length > 0 ? 'exact' : 'nearby',
          locationSearched: filters.location,
          message: exactMatches.length === 0 ? `No venues found in ${filters.location}. Showing nearby alternatives:` : undefined,
          suggestionMessage: suggestions.length > 0
            ? `Areas outside your search that you might like:`
            : undefined
        };
      } else {
        console.log(`❌ SEARCH DEBUG: No coordinates found for "${filters.location}", falling back to text search`);

        // Fallback to text-based search with improved matching
        const exactMatches = availableVenues.filter(venue => {
          const venueSuburb = venue.location.suburb.toLowerCase().trim();
          const searchLocation = filters.location!.toLowerCase().trim();

          return venueSuburb === searchLocation ||
                 venueSuburb.includes(searchLocation) ||
                 searchLocation.includes(venueSuburb);
        });

        const suggestions = availableVenues.filter(venue => {
          const venueSuburb = venue.location.suburb.toLowerCase().trim();
          const searchLocation = filters.location!.toLowerCase().trim();

          return !(venueSuburb === searchLocation ||
                   venueSuburb.includes(searchLocation) ||
                   searchLocation.includes(venueSuburb));
        });

        return {
          exactMatches,
          suggestions: suggestions, // Return ALL suggestions
          searchType: 'text',
          locationSearched: filters.location,
          suggestionMessage: suggestions.length > 0
            ? `Areas outside your search that you might like:`
            : undefined
        };
      }
    } catch (error) {
      console.error(`❌ SEARCH DEBUG: Error in location search:`, error);

      // Fallback to text-based search with improved matching
      const exactMatches = availableVenues.filter(venue => {
        const venueSuburb = venue.location.suburb.toLowerCase().trim();
        const searchLocation = filters.location!.toLowerCase().trim();

        return venueSuburb === searchLocation ||
               venueSuburb.includes(searchLocation) ||
               searchLocation.includes(venueSuburb);
      });

      const suggestions = availableVenues.filter(venue => {
        const venueSuburb = venue.location.suburb.toLowerCase().trim();
        const searchLocation = filters.location!.toLowerCase().trim();

        return !(venueSuburb === searchLocation ||
                 venueSuburb.includes(searchLocation) ||
                 searchLocation.includes(venueSuburb));
      });

      return {
        exactMatches,
        suggestions: suggestions, // Return ALL suggestions
        searchType: 'text_fallback',
        locationSearched: filters.location,
        suggestionMessage: suggestions.length > 0
          ? `Areas outside your search that you might like:`
          : undefined
      };
    }
  } else {
    // No location specified, return all available venues
    return {
      exactMatches: availableVenues,
      suggestions: [],
      searchType: 'all_available',
      message: availableVenues.length === venues.length
        ? undefined
        : `Showing ${availableVenues.length} venues matching your criteria`
    };
  }

  // Fallback strategies when no matches found

  // Fallback 1: Try relaxing date constraints
  if (filters.startDate) {
    let fallbackVenues = [...venues];

    if (filters.maxBudget) {
      fallbackVenues = filterVenuesByBudget(fallbackVenues, filters.maxBudget);
    }
    if (filters.guestCount) {
      fallbackVenues = filterVenuesByCapacity(fallbackVenues, filters.guestCount);
    }
    if (filters.venueType) {
      fallbackVenues = filterVenuesByType(fallbackVenues, filters.venueType);
    }

    if (filters.location) {
      const locationMatches = filterVenuesByLocation(fallbackVenues, filters.location);
      if (locationMatches.length > 0) {
        return {
          exactMatches: [],
          suggestions: locationMatches,
          searchType: 'date_flexible',
          message: `No venues available in ${filters.location} on your selected date.`,
          suggestionMessage: `Try these venues in ${filters.location} on different dates:`
        };
      }
    }

    if (fallbackVenues.length > 0) {
      return {
        exactMatches: [],
        suggestions: fallbackVenues, // Return ALL fallback venues
        searchType: 'date_flexible',
        message: `No venues available on your selected date.`,
        suggestionMessage: `Try these venues on different dates:`
      };
    }
  }

  // Fallback 2: Try relaxing budget constraints
  if (filters.maxBudget) {
    let fallbackVenues = [...venues];

    if (filters.startDate) {
      fallbackVenues = filterVenuesByDate(fallbackVenues, filters.startDate, filters.endDate);
    }
    if (filters.guestCount) {
      fallbackVenues = filterVenuesByCapacity(fallbackVenues, filters.guestCount);
    }
    if (filters.venueType) {
      fallbackVenues = filterVenuesByType(fallbackVenues, filters.venueType);
    }

    if (filters.location) {
      const locationMatches = filterVenuesByLocation(fallbackVenues, filters.location);
      if (locationMatches.length > 0) {
        return {
          exactMatches: [],
          suggestions: locationMatches,
          searchType: 'budget_flexible',
          message: `No venues in ${filters.location} within your $${filters.maxBudget} budget.`,
          suggestionMessage: `Consider these venues in ${filters.location} with flexible pricing:`
        };
      }
    }

    if (fallbackVenues.length > 0) {
      return {
        exactMatches: [],
        suggestions: fallbackVenues, // Return ALL fallback venues
        searchType: 'budget_flexible',
        message: `No venues found within your $${filters.maxBudget} budget.`,
        suggestionMessage: `Consider these venues with flexible pricing:`
      };
    }
  }

  // Fallback 3: Try relaxing guest count
  if (filters.guestCount) {
    let fallbackVenues = [...venues];

    if (filters.startDate) {
      fallbackVenues = filterVenuesByDate(fallbackVenues, filters.startDate, filters.endDate);
    }
    if (filters.maxBudget) {
      fallbackVenues = filterVenuesByBudget(fallbackVenues, filters.maxBudget);
    }
    if (filters.venueType) {
      fallbackVenues = filterVenuesByType(fallbackVenues, filters.venueType);
    }

    if (filters.location) {
      const locationMatches = filterVenuesByLocation(fallbackVenues, filters.location);
      if (locationMatches.length > 0) {
        return {
          exactMatches: [],
          suggestions: locationMatches,
          searchType: 'capacity_flexible',
          message: `No venues in ${filters.location} for ${filters.guestCount} guests.`,
          suggestionMessage: `Consider these venues in ${filters.location} with flexible capacity:`
        };
      }
    }

    if (fallbackVenues.length > 0) {
      return {
        exactMatches: [],
        suggestions: fallbackVenues, // Return ALL fallback venues
        searchType: 'capacity_flexible',
        message: `No venues found for ${filters.guestCount} guests.`,
        suggestionMessage: `Consider these venues with flexible capacity:`
      };
    }
  }

  // Final fallback - show all venues
  return {
    exactMatches: [],
    suggestions: venues, // Return ALL venues as final fallback
    searchType: 'all_venues',
    message: 'No venues match your specific criteria.',
    suggestionMessage: 'Here are all available venues - try adjusting your search:'
  };
};
