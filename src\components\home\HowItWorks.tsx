
import { Search, Clock, MapPin, PartyPopper, HeartHandshake } from "lucide-react";

const steps = [
  {
    icon: Search,
    title: "Find Your Perfect Venue",
    description: "Enter your location, select your dates, and pick the type of celebration—our intuitive search lets you explore party-ready spaces instantly.",
    step: 1
  },
  {
    icon: MapPin,
    title: "Explore and Compare",
    description: "Browse detailed listings with high-quality photos, clear pricing, and a list of amenities. Filter venues by capacity, themes, and party needs.",
    step: 2
  },
  {
    icon: Clock,
    title: "Book Your Venue Instantly",
    description: "Found the one? Secure your spot with transparent pricing and instant confirmation. No back-and-forth, just smooth bookings.",
    step: 3
  },
  {
    icon: PartyPopper,
    title: "Party Your Way",
    description: "Whether it's for weddings, birthdays, or late-night experiences, our venues are entertainment-ready and safe, ensuring every celebration goes off without a hitch.",
    step: 4
  },
  {
    icon: HeartHandshake,
    title: "Dedicated Support",
    description: "Got questions? Need help with planning? Our 24/7 support team is here to guide you every step of the way.",
    step: 5
  }
];

export function HowItWorks() {
  return (
    <div className="bg-gradient-to-b from-white to-primary-light/20 py-gr4">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-2xl mx-auto mb-gr4">
          <h2 className="text-h2 mb-2">How It Works</h2>
          <p className="text-secondary-darker">Your celebration journey made simple</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div 
                key={index}
                className="relative group"
              >
                {/* Connection line between steps */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-12 left-[60%] w-full h-[2px] bg-primary/20" />
                )}
                
                <div className="relative bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-primary/5 group-hover:-translate-y-1">
                  {/* Step number */}
                  <div className="absolute -top-3 -left-3 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-semibold text-sm">
                    {step.step}
                  </div>
                  
                  {/* Icon */}
                  <div className="mb-4 flex justify-center">
                    <div className="w-16 h-16 rounded-full bg-primary-light/30 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                  </div>
                  
                  {/* Content */}
                  <h3 className="text-h3 font-semibold mb-2 text-center text-primary-darker">
                    {step.title}
                  </h3>
                  <p className="text-sm text-center text-secondary-darker">
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
