/**
 * canonical-fix.js
 * 
 * This script scans HTML files for canonical link issues and fixes them.
 * It helps correct the 47 pages with canonical tag problems identified in Google Search Console.
 * 
 * Usage:
 * 1. Run with Node.js: node canonical-fix.js
 * 2. It will scan HTML files in dist/ after building
 * 3. Creates a report of issues found and fixed
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { globSync } from 'glob';
import * as cheerio from 'cheerio';

// Get current file directory with ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SITE_URL = 'https://housegoing.com.au';
const DIST_DIR = path.join(__dirname, '..', 'dist');
const REPORT_FILE = path.join(__dirname, '..', 'canonical-fix-report.md');

// HTML files to check (production build files)
const htmlFiles = globSync(`${DIST_DIR}/**/*.html`);

console.log(`Scanning ${htmlFiles.length} HTML files for canonical tag issues...`);

let report = `# Canonical Tag Fix Report\n\n`;
report += `Generated on: ${new Date().toISOString()}\n\n`;
report += `## Summary of Issues Found\n\n`;

const issuesSummary = {
  missingCanonical: 0,
  multipleCanonicals: 0,
  relativeUrls: 0,
  fixedFiles: 0,
  total: htmlFiles.length
};

htmlFiles.forEach(file => {
  const relPath = path.relative(DIST_DIR, file);
  const html = fs.readFileSync(file, 'utf-8');
  const $ = cheerio.load(html);
  
  const canonicalLinks = $('link[rel="canonical"]');
  
  // Case 1: Missing canonical
  if (canonicalLinks.length === 0) {
    issuesSummary.missingCanonical++;
    
    // Generate correct canonical URL based on file path
    let canonicalUrl = `${SITE_URL}/`;
    if (relPath !== 'index.html') {
      canonicalUrl = `${SITE_URL}/${relPath.replace(/\.html$/, '')}`;
    }
    
    // Add canonical link
    $('head').append(`<link rel="canonical" href="${canonicalUrl}" />`);
    
    // Save changes
    fs.writeFileSync(file, $.html());
    issuesSummary.fixedFiles++;
  }
  // Case 2: Multiple canonicals
  else if (canonicalLinks.length > 1) {
    issuesSummary.multipleCanonicals++;
    
    // Keep only the first canonical and remove others
    const firstCanonical = $(canonicalLinks[0]).attr('href');
    canonicalLinks.slice(1).remove();
    
    // Ensure the remaining canonical has absolute URL
    const updatedCanonical = firstCanonical.startsWith('http') 
      ? firstCanonical 
      : `${SITE_URL}${firstCanonical.startsWith('/') ? '' : '/'}${firstCanonical}`;
    
    $('link[rel="canonical"]').attr('href', updatedCanonical);
    
    // Save changes
    fs.writeFileSync(file, $.html());
    issuesSummary.fixedFiles++;
  }
  // Case 3: Relative URL in canonical
  else if (!canonicalLinks.attr('href').startsWith('http')) {
    issuesSummary.relativeUrls++;
    
    // Convert to absolute URL
    const relativeUrl = canonicalLinks.attr('href');
    const absoluteUrl = `${SITE_URL}${relativeUrl.startsWith('/') ? '' : '/'}${relativeUrl}`;
    
    canonicalLinks.attr('href', absoluteUrl);
    
    // Save changes
    fs.writeFileSync(file, $.html());
    issuesSummary.fixedFiles++;
  }
});

// Complete the report
report += `- Total files scanned: ${issuesSummary.total}\n`;
report += `- Missing canonical tags: ${issuesSummary.missingCanonical}\n`;
report += `- Multiple canonical tags: ${issuesSummary.multipleCanonicals}\n`;
report += `- Relative URL canonicals: ${issuesSummary.relativeUrls}\n`;
report += `- Files fixed: ${issuesSummary.fixedFiles}\n\n`;

report += `## Next Steps\n\n`;
report += `1. Build your site again with \`npm run build:prod\`\n`;
report += `2. Deploy the fixed files\n`;
report += `3. Submit your sitemap to Google Search Console\n`;
report += `4. Request indexing of your main pages\n`;

fs.writeFileSync(REPORT_FILE, report);

console.log(`
✅ Canonical tag scan complete!
   - ${issuesSummary.fixedFiles} files fixed
   - Full report saved to ${REPORT_FILE}
`);
