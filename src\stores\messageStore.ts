// Central Message Store for HouseGoing Platform
// Manages all messages between hosts and guests

export interface Message {
  id: string;
  conversationId: string;
  from: 'host' | 'guest';
  to: 'host' | 'guest';
  message: string;
  timestamp: string;
  read: boolean;
  bookingId?: number;
  guestName?: string;
  hostName?: string;
  propertyName?: string;
}

export interface Conversation {
  id: string;
  bookingId?: number;
  guestName: string;
  hostName: string;
  propertyName: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  messages: Message[];
  guestEmail?: string;
  guestPhone?: string;
  guestImage?: string;
}

class MessageStore {
  private conversations: Map<string, Conversation> = new Map();
  private listeners: Set<() => void> = new Set();

  constructor() {
    // Initialize with some mock conversations
    this.initializeMockData();
  }

  private initializeMockData() {
    // Mock conversation 1 - <PERSON> booking
    const conversation1: Conversation = {
      id: 'conv-1',
      bookingId: 1,
      guestName: '<PERSON>',
      hostName: 'Host User',
      propertyName: 'Beachside Villa',
      lastMessage: 'Hi! Looking forward to our stay. What time is check-in?',
      lastMessageTime: '2023-08-10T10:30:00Z',
      unreadCount: 1,
      guestEmail: '<EMAIL>',
      guestPhone: '+61 412 345 678',
      guestImage: 'https://randomuser.me/api/portraits/men/32.jpg',
      messages: [
        {
          id: 'msg-1',
          conversationId: 'conv-1',
          from: 'guest',
          to: 'host',
          message: 'Hi! Looking forward to our stay. What time is check-in?',
          timestamp: '2023-08-10T10:30:00Z',
          read: false,
          bookingId: 1,
          guestName: 'John Smith',
          hostName: 'Host User',
          propertyName: 'Beachside Villa'
        }
      ]
    };

    // Mock conversation 2 - Sarah Johnson booking
    const conversation2: Conversation = {
      id: 'conv-2',
      bookingId: 2,
      guestName: 'Sarah Johnson',
      hostName: 'Host User',
      propertyName: 'Mountain Cabin',
      lastMessage: 'Thank you for the quick response!',
      lastMessageTime: '2023-07-25T14:20:00Z',
      unreadCount: 0,
      guestEmail: '<EMAIL>',
      guestPhone: '+61 423 456 789',
      guestImage: 'https://randomuser.me/api/portraits/women/44.jpg',
      messages: [
        {
          id: 'msg-2',
          conversationId: 'conv-2',
          from: 'guest',
          to: 'host',
          message: 'Hello! I have some questions about the cabin amenities.',
          timestamp: '2023-07-25T13:15:00Z',
          read: true,
          bookingId: 2,
          guestName: 'Sarah Johnson',
          hostName: 'Host User',
          propertyName: 'Mountain Cabin'
        },
        {
          id: 'msg-3',
          conversationId: 'conv-2',
          from: 'host',
          to: 'guest',
          message: 'Hi Sarah! Happy to help. What would you like to know?',
          timestamp: '2023-07-25T13:45:00Z',
          read: true,
          bookingId: 2,
          guestName: 'Sarah Johnson',
          hostName: 'Host User',
          propertyName: 'Mountain Cabin'
        },
        {
          id: 'msg-4',
          conversationId: 'conv-2',
          from: 'guest',
          to: 'host',
          message: 'Thank you for the quick response!',
          timestamp: '2023-07-25T14:20:00Z',
          read: true,
          bookingId: 2,
          guestName: 'Sarah Johnson',
          hostName: 'Host User',
          propertyName: 'Mountain Cabin'
        }
      ]
    };

    this.conversations.set('conv-1', conversation1);
    this.conversations.set('conv-2', conversation2);
  }

  // Add a new message to a conversation
  addMessage(message: Omit<Message, 'id'>): Message {
    const newMessage: Message = {
      ...message,
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

    const conversation = this.conversations.get(message.conversationId);
    if (conversation) {
      conversation.messages.push(newMessage);
      conversation.lastMessage = message.message;
      conversation.lastMessageTime = message.timestamp;
      
      // Update unread count if message is from guest to host
      if (message.from === 'guest') {
        conversation.unreadCount += 1;
      }
    } else {
      // Create new conversation if it doesn't exist
      const newConversation: Conversation = {
        id: message.conversationId,
        bookingId: message.bookingId,
        guestName: message.guestName || 'Guest',
        hostName: message.hostName || 'Host',
        propertyName: message.propertyName || 'Property',
        lastMessage: message.message,
        lastMessageTime: message.timestamp,
        unreadCount: message.from === 'guest' ? 1 : 0,
        messages: [newMessage]
      };
      this.conversations.set(message.conversationId, newConversation);
    }

    this.notifyListeners();
    return newMessage;
  }

  // Get all conversations
  getConversations(): Conversation[] {
    return Array.from(this.conversations.values())
      .sort((a, b) => new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime());
  }

  // Get a specific conversation
  getConversation(conversationId: string): Conversation | undefined {
    return this.conversations.get(conversationId);
  }

  // Get conversation by booking ID
  getConversationByBookingId(bookingId: number): Conversation | undefined {
    return Array.from(this.conversations.values())
      .find(conv => conv.bookingId === bookingId);
  }

  // Mark messages as read
  markAsRead(conversationId: string): void {
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.unreadCount = 0;
      conversation.messages.forEach(msg => {
        if (msg.to === 'host') {
          msg.read = true;
        }
      });
      this.notifyListeners();
    }
  }

  // Get total unread count
  getTotalUnreadCount(): number {
    return Array.from(this.conversations.values())
      .reduce((total, conv) => total + conv.unreadCount, 0);
  }

  // Subscribe to changes
  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Sync booking messages (for integration with booking system)
  syncBookingMessages(bookingId: number, messages: any[]): void {
    const conversationId = `booking-${bookingId}`;
    const conversation = this.getConversation(conversationId);

    if (conversation) {
      // Update existing conversation
      conversation.messages = messages.map((msg, index) => ({
        id: `msg-${bookingId}-${index}`,
        conversationId,
        from: msg.from,
        to: msg.from === 'host' ? 'guest' : 'host',
        message: msg.message,
        timestamp: msg.time,
        read: true,
        bookingId,
        guestName: conversation.guestName,
        hostName: conversation.hostName,
        propertyName: conversation.propertyName
      }));

      if (messages.length > 0) {
        const lastMsg = messages[messages.length - 1];
        conversation.lastMessage = lastMsg.message;
        conversation.lastMessageTime = lastMsg.time;
      }
    } else if (messages.length > 0) {
      // Create new conversation from booking messages
      const lastMsg = messages[messages.length - 1];
      const newConversation: Conversation = {
        id: conversationId,
        bookingId,
        guestName: 'Guest', // This should come from booking data
        hostName: 'Host User',
        propertyName: 'Property', // This should come from booking data
        lastMessage: lastMsg.message,
        lastMessageTime: lastMsg.time,
        unreadCount: 0,
        messages: messages.map((msg, index) => ({
          id: `msg-${bookingId}-${index}`,
          conversationId,
          from: msg.from,
          to: msg.from === 'host' ? 'guest' : 'host',
          message: msg.message,
          timestamp: msg.time,
          read: true,
          bookingId,
          guestName: 'Guest',
          hostName: 'Host User',
          propertyName: 'Property'
        }))
      };
      this.conversations.set(conversationId, newConversation);
    }

    this.notifyListeners();
  }
}

// Export singleton instance
export const messageStore = new MessageStore();
export default messageStore;
