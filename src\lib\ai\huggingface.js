/**
 * Hugging Face AI integration for HouseGoing
 */

// Default models to use
const DEFAULT_MODEL = "mistralai/Mistral-7B-Instruct-v0.3";
const DEEPSEEK_MODEL = "deepseek-ai/DeepSeek-V3-0324";
const QWEN_MODEL = "Qwen/Qwen2.5-Omni-7B";

/**
 * Generate a response from the Hugging Face API
 * @param {string} prompt - The prompt to send to the model
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - The generated response
 */
export async function generateResponse(prompt, options = {}) {
  const {
    model = DEFAULT_MODEL,
    temperature = 0.7,
    maxTokens = 1024,
    apiKey = process.env.HUGGINGFACE_API_KEY || process.env.NEXT_PUBLIC_HUGGINGFACE_API_KEY
  } = options;

  try {
    const response = await fetch(`https://api-inference.huggingface.co/models/${model}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bear<PERSON> ${apiKey}`
      },
      body: JSON.stringify({
        inputs: prompt,
        parameters: {
          temperature: temperature,
          max_new_tokens: maxTokens,
          return_full_text: false
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Hugging Face API error: ${error.error || 'Unknown error'}`);
    }

    const data = await response.json();
    return data[0]?.generated_text || '';
  } catch (error) {
    console.error('Error generating response from Hugging Face:', error);
    throw error;
  }
}

/**
 * Generate a response using the DeepSeek model
 * @param {string} prompt - The prompt to send to the model
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - The generated response
 */
export async function generateDeepSeekResponse(prompt, options = {}) {
  const {
    temperature = 0.7,
    maxTokens = 1024,
    apiKey = process.env.HUGGINGFACE_API_KEY || process.env.NEXT_PUBLIC_HUGGINGFACE_API_KEY
  } = options;

  try {
    const response = await fetch(`https://api-inference.huggingface.co/models/${DEEPSEEK_MODEL}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        inputs: prompt,
        parameters: {
          temperature: temperature,
          max_new_tokens: maxTokens,
          return_full_text: false,
          trust_remote_code: true
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`DeepSeek API error: ${error.error || 'Unknown error'}`);
    }

    const data = await response.json();
    return data[0]?.generated_text || '';
  } catch (error) {
    console.error('Error generating response from DeepSeek:', error);
    throw error;
  }
}

/**
 * Generate a response using the Qwen model
 * @param {string} prompt - The prompt to send to the model
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - The generated response
 */
export async function generateQwenResponse(prompt, options = {}) {
  const {
    temperature = 0.7,
    maxTokens = 1024,
    apiKey = process.env.HUGGINGFACE_API_KEY || process.env.NEXT_PUBLIC_HUGGINGFACE_API_KEY
  } = options;

  try {
    const response = await fetch(`https://api-inference.huggingface.co/models/${QWEN_MODEL}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        inputs: prompt,
        parameters: {
          temperature: temperature,
          max_new_tokens: maxTokens,
          return_full_text: false
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Qwen API error: ${error.error || 'Unknown error'}`);
    }

    const data = await response.json();
    return data[0]?.generated_text || '';
  } catch (error) {
    console.error('Error generating response from Qwen:', error);
    throw error;
  }
}

/**
 * Generate a host assistant response
 * @param {string} message - The user message
 * @param {string} context - The context of the conversation (e.g., 'properties', 'bookings')
 * @returns {Promise<string>} - The generated response
 */
export async function generateHostAssistantResponse(message, context = 'general') {
  const prompt = `
You are Homie, the AI host assistant for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.
You use Australian English spelling (e.g., 'organise' not 'organize').

Current context: ${context}

Your goal is to help hosts optimize their venue listings, understand Party Scores, set pricing strategies, and maximize bookings.
Provide specific, actionable advice that hosts can implement right away.

User message: ${message}

Your response:`;

  // Use the Mistral model directly since it's working
  return generateResponse(prompt);
}

/**
 * Generate a sales assistant response with proactive venue search
 * @param {string} message - The user message
 * @param {Array} venueData - Optional array of venue data to use for recommendations
 * @returns {Promise<string>} - The generated response
 */
export async function generateSalesAssistantResponse(message, venueData = []) {
  // Mock venue data for recommendations
  const mockVenues = [
    {
      name: 'Harbour View Terrace',
      description: 'Stunning waterfront venue with panoramic harbour views and modern amenities.',
      capacity: 80,
      price: 250,
      features: ['Waterfront location', 'Outdoor deck', 'Sound system', 'Bar area'],
      location: 'Sydney Harbour'
    },
    {
      name: 'The Garden Pavilion',
      description: 'Elegant garden venue surrounded by lush greenery and flowering plants.',
      capacity: 120,
      price: 320,
      features: ['Garden setting', 'Marquee option', 'Catering kitchen', 'Parking'],
      location: 'Eastern Suburbs'
    },
    {
      name: 'Urban Loft Space',
      description: 'Industrial chic warehouse conversion with exposed brick and high ceilings.',
      capacity: 150,
      price: 380,
      features: ['Industrial design', 'DJ booth', 'Lighting rig', 'Rooftop access'],
      location: 'Inner West'
    },
    {
      name: 'Beachside Cabana',
      description: 'Relaxed beach venue with direct sand access and coastal vibes.',
      capacity: 60,
      price: 200,
      features: ['Beach access', 'BBQ facilities', 'Outdoor shower', 'Covered area'],
      location: 'Northern Beaches'
    },
    {
      name: 'Heritage Hall',
      description: 'Stunning restored heritage building with classic architecture and modern facilities.',
      capacity: 200,
      price: 450,
      features: ['Historic building', 'Grand staircase', 'Chandeliers', 'Dance floor'],
      location: 'CBD'
    }
  ];

  // Use provided venue data or fallback to mock data
  const venues = venueData.length > 0 ? venueData : mockVenues;

  // Format venues as a string for the prompt
  const venuesString = venues.map(venue => {
    return `- ${venue.name}: ${venue.description} Capacity: ${venue.capacity}, Price: $${venue.price}/hour, Location: ${venue.location}, Features: ${venue.features.join(', ')}`;
  }).join('\n');

  const prompt = `
You are Homie, the AI sales assistant for HouseGoing, a premium platform for booking party venues in Australia.
Your tone is warm, enthusiastic, and helpful with Australian phrases and spelling.

CORE BEHAVIOR:
Your primary purpose is to help users find and book the perfect venue for their events with minimal friction and maximum satisfaction.

KEY PRINCIPLES:
- Show Before You Ask: When a user provides enough initial information to conduct a search (location, date/timeframe, and event type/size), IMMEDIATELY show venue options before asking additional questions.
- Extract Information Proactively: Parse user messages for key booking criteria (location, date, party size, event type, BYO preferences) without explicitly asking for each piece.
- Progressive Disclosure: Only ask for additional information AFTER showing initial venue options.
- Minimize Conversation Steps: Aim to show relevant venues within 1-2 conversation turns.

SEARCH TRIGGER CONDITIONS:
Immediately search and display venue options when the user has provided AT MINIMUM:
- A location (city, suburb, or area)
AND EITHER
- A timeframe OR event type

RESPONSE STRUCTURE WHEN USER PROVIDES SUFFICIENT INFORMATION:
Great! Based on what you've shared, here are some venues in [LOCATION] that might work for your [EVENT TYPE]:

[3-5 VENUE OPTIONS WITH KEY DETAILS]

Would you like to:
1. See more details about any of these options?
2. Refine your search with more specific requirements?
3. See more venue options?

FOLLOW-UP QUESTIONS (ONLY AFTER showing venue options):
After showing venues, you can then ask 1-2 targeted questions to refine the search, such as:
- "Do you have a specific budget range in mind?"
- "Any particular venue features you're looking for?"
- "Would you prefer indoor or outdoor spaces?"

INITIAL USER ASSESSMENT:
For the FIRST message only, quickly assess what information is provided and respond accordingly:
- If minimal info (just "hi"): Friendly greeting + quick prompt for event details
- If partial info: Acknowledge what's provided + ask for only crucial missing information
- If sufficient info: Immediately show venue options matching criteria

Available venues for recommendations:
${venuesString}

User message: ${message}

Your response:`;

  // Use the Mistral model directly since it's working
  return generateResponse(prompt);
}
