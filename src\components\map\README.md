# NSW Planning Map Integration

This module provides a comprehensive integration of address lookup, mapping, and spatial data layers using Leaflet.js, Mapbox tiles, and the Maps.co Geocoding API.

## Features

- Interactive map with Mapbox basemap tiles
- Address search using Maps.co Geocoding API
- Spatial data retrieval from NSW Planning Portal WFS endpoints
- Display of zoning and LGA (Local Government Area) boundaries
- WMS overlay controls for toggling data layers
- Popup information for zoning and LGA features
- Error handling for geocoding and WFS requests

## Components

### NSWPlanningMap

The main map component that handles:
- Map initialization with Leaflet.js
- Address geocoding via Maps.co API
- Fetching and displaying zoning and LGA data
- Interactive map controls

### NSWPlanningMapPage

A page component that demonstrates the use of the NSWPlanningMap component and displays:
- The interactive map
- Location details (coordinates, council, zoning)
- Noise restrictions based on zoning
- Party planning tips

## Services

### Geocoding Service

Uses the Maps.co API to convert addresses to coordinates and vice versa.

### WFS Service

Provides utility functions for working with Web Feature Service (WFS) endpoints from the NSW Planning Portal.

## Backend Services

### WFS Proxy Server

A proxy server that handles requests to NSW Planning Portal WFS services to avoid CORS issues. It also provides caching to improve performance.

## Usage

### Basic Usage

```jsx
import NSWPlanningMap from '../components/map/NSWPlanningMap';

const MyComponent = () => {
  const handleDataLoaded = (data) => {
    console.log('Spatial data loaded:', data);
    // Process the data as needed
  };

  return (
    <div>
      <h1>NSW Planning Map</h1>
      <NSWPlanningMap onDataLoaded={handleDataLoaded} />
    </div>
  );
};
```

### Data Structure

The `onDataLoaded` callback receives data in the following format:

```js
{
  coordinates: { lat: number, lng: number },
  zoning: {
    code: string,
    name: string
  },
  lga: {
    name: string
  }
}
```

## Setup Requirements

1. Ensure the WFS proxy server is running to handle requests to NSW Planning Portal services
2. Make sure Leaflet.js and its CSS are properly imported
3. Configure the Mapbox access token in the NSWPlanningMap component

## API Endpoints

### NSW Planning Portal WFS Endpoints

- Zoning: `https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer`
- LGA: `https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer`

### NSW Planning Portal WMS Endpoints

- Zoning: `https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer`
- LGA: `https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer`

### Maps.co Geocoding API

- Forward Geocoding: `https://geocode.maps.co/search`
- Reverse Geocoding: `https://geocode.maps.co/reverse`

## Error Handling

The components include comprehensive error handling for:
- Failed geocoding requests
- Failed WFS requests
- Empty or invalid responses

Errors are displayed to the user with appropriate messages and logged to the console for debugging.

## Performance Considerations

- Caching is implemented for both geocoding and WFS requests to reduce API calls
- WMS layers are used for visual overlays to reduce the amount of data transferred
- GeoJSON data is only fetched for the specific features at the selected location
