  // Step 3: Amenities & Features
  const renderAmenitiesFeatures = () => (
    <div className="space-y-6">
      <div>
        <label className="block mb-2 font-medium">Amenities</label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {amenityOptions.map((amenity) => (
            <div key={amenity.id} className="relative flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id={`amenity-${amenity.id}`}
                  type="checkbox"
                  checked={formData.amenities.includes(amenity.id)}
                  onChange={() => handleAmenityToggle(amenity.id)}
                  className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
              </div>
              <div className="ml-3 text-sm flex items-center">
                <span className="mr-2">{amenity.icon}</span>
                <label htmlFor={`amenity-${amenity.id}`} className="font-medium text-gray-700">
                  {amenity.label}
                </label>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block mb-2 font-medium">Parking Details</label>
        <textarea
          value={formData.parkingDetails}
          onChange={(e) => handleChange('parkingDetails', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe parking options (e.g., 2 off-street spots, street parking available, etc.)"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">Public Transportation</label>
        <textarea
          value={formData.transportDetails}
          onChange={(e) => handleChange('transportDetails', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe nearby public transport options (e.g., 5 min walk to Central Station)"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">Nearby Landmarks</label>
        <textarea
          value={formData.nearbyLandmarks}
          onChange={(e) => handleChange('nearbyLandmarks', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="List nearby landmarks or points of interest"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">BYO Alcohol Policy</label>
        <select
          value={formData.byoPolicy}
          onChange={(e) => handleChange('byoPolicy', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="allowed">BYO Allowed - No Restrictions</option>
          <option value="allowed_with_restrictions">BYO Allowed - With Restrictions</option>
          <option value="not_allowed">BYO Not Allowed</option>
          <option value="licensed">Licensed Venue - No BYO</option>
        </select>
        {formData.byoPolicy === 'allowed_with_restrictions' && (
          <textarea
            value={formData.byoRestrictions}
            onChange={(e) => handleChange('byoRestrictions', e.target.value)}
            className="w-full p-2 border rounded mt-2"
            rows={2}
            placeholder="Describe BYO restrictions (e.g., no glass bottles, wine only, etc.)"
          />
        )}
      </div>
    </div>
  );

  // Step 4: House Rules & Policies
  const renderHouseRules = () => (
    <div className="space-y-6">
      <div>
        <label className="block mb-2 font-medium">Noise Restrictions</label>
        <textarea
          value={formData.noiseRestrictions}
          onChange={(e) => handleChange('noiseRestrictions', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe any noise restrictions (e.g., music must be turned down after 10pm)"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">Latest End Time</label>
        <select
          value={formData.endTime}
          onChange={(e) => handleChange('endTime', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="9pm">9:00 PM</option>
          <option value="10pm">10:00 PM</option>
          <option value="11pm">11:00 PM</option>
          <option value="12am">12:00 AM</option>
          <option value="1am">1:00 AM</option>
          <option value="2am">2:00 AM</option>
          <option value="3am">3:00 AM</option>
          <option value="no_restriction">No Restriction</option>
        </select>
      </div>

      <div>
        <label className="block mb-2 font-medium">Decorations Policy</label>
        <textarea
          value={formData.decorationsPolicy}
          onChange={(e) => handleChange('decorationsPolicy', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe what decorations are allowed (e.g., no confetti, wall attachments, etc.)"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">Smoking Policy</label>
        <select
          value={formData.smokingPolicy}
          onChange={(e) => handleChange('smokingPolicy', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="not_allowed">No Smoking Allowed</option>
          <option value="outdoor_only">Outdoor Areas Only</option>
          <option value="designated_areas">Designated Areas Only</option>
          <option value="allowed">Smoking Allowed</option>
        </select>
      </div>

      <div>
        <label className="block mb-2 font-medium">Pet Policy</label>
        <select
          value={formData.petPolicy}
          onChange={(e) => handleChange('petPolicy', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="not_allowed">No Pets Allowed</option>
          <option value="dogs_only">Dogs Only</option>
          <option value="case_by_case">Case by Case Basis</option>
          <option value="allowed">Pets Allowed</option>
        </select>
      </div>

      <div>
        <label className="block mb-2 font-medium">Additional Fees</label>
        <textarea
          value={formData.additionalFees}
          onChange={(e) => handleChange('additionalFees', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="List any additional fees (e.g., cleaning fee, security deposit, etc.)"
        />
      </div>
    </div>
  );

  // Step 5: Photos
  const renderPhotos = () => (
    <div className="space-y-6">
      <div>
        <label className="block mb-2 font-medium">Venue Photos</label>
        <p className="text-sm text-gray-600 mb-4">
          Upload high-quality photos of your venue to attract more bookings.
          You can upload multiple photos to showcase different areas of your venue.
        </p>
        <PhotoUpload
          onImagesUploaded={handleImagesUploaded}
          existingImages={formData.images}
        />
      </div>
    </div>
  );

  // Step 6: Bank Details
  const renderBankDetails = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-blue-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Important Information</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                This information is required for processing your venue booking payments.
                Your details are securely stored and handled according to Australian privacy laws.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block mb-2 font-medium">Account Holder Name*</label>
          <input
            type="text"
            value={formData.bankDetails.accountName}
            onChange={(e) => handleBankDetailsChange('accountName', e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="e.g. John Smith"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Enter the name exactly as it appears on your bank account</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">BSB* (6 digits)</label>
          <input
            type="text"
            value={formData.bankDetails.bsb}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              // Limit to 6 digits
              const bsb = value.slice(0, 6);
              handleBankDetailsChange('bsb', bsb);
            }}
            pattern="\d{6}"
            maxLength={6}
            className="w-full p-2 border rounded"
            placeholder="e.g. 062-000"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Australian BSB number (6 digits)</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Account Number* (6-10 digits)</label>
          <input
            type="text"
            value={formData.bankDetails.accountNumber}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              // Limit to 10 digits
              const accountNumber = value.slice(0, 10);
              handleBankDetailsChange('accountNumber', accountNumber);
            }}
            maxLength={10}
            className="w-full p-2 border rounded"
            placeholder="e.g. ********"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Enter your account number (6-10 digits)</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Bank Name*</label>
          <input
            type="text"
            value={formData.bankDetails.bankName}
            onChange={(e) => handleBankDetailsChange('bankName', e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="e.g. Commonwealth Bank"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Enter the name of your bank</p>
        </div>
      </div>
    </div>
  );
