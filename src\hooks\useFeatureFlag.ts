import { useState, useEffect } from 'react';
import { isFeatureEnabled } from '../lib/feature-flags';
import { useAuth } from '../context/AuthContext';

/**
 * Hook to check if a feature flag is enabled
 * @param key Feature flag key
 * @param defaultValue Default value if flag doesn't exist
 */
export function useFeatureFlag(key: string, defaultValue = false): boolean {
  const [isEnabled, setIsEnabled] = useState<boolean>(defaultValue);
  const { user } = useAuth();
  
  useEffect(() => {
    let isMounted = true;
    
    const checkFeature = async () => {
      try {
        const enabled = await isFeatureEnabled(key, user?.id);
        
        if (isMounted) {
          setIsEnabled(enabled);
        }
      } catch (error) {
        console.error(`Error checking feature flag ${key}:`, error);
        
        if (isMounted) {
          setIsEnabled(defaultValue);
        }
      }
    };
    
    checkFeature();
    
    return () => {
      isMounted = false;
    };
  }, [key, user?.id, defaultValue]);
  
  return isEnabled;
}
