<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple NSW Planning Map Test</title>
  
  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .map-container {
      height: 500px;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 20px;
      border: 1px solid #ccc;
    }
    
    .controls {
      margin-bottom: 20px;
    }
    
    button {
      padding: 10px 15px;
      margin-right: 10px;
      cursor: pointer;
    }
    
    .results {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-top: 20px;
    }
    
    pre {
      background-color: #eee;
      padding: 10px;
      border-radius: 5px;
      overflow: auto;
      max-height: 300px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Simple NSW Planning Map Test</h1>
    
    <div class="controls">
      <button id="testSydney">Test Sydney CBD</button>
      <button id="testParramatta">Test Parramatta</button>
      <button id="testBondi">Test Bondi</button>
    </div>
    
    <div id="mapContainer" class="map-container"></div>
    
    <div class="results">
      <h2>Results:</h2>
      <div id="resultsContainer">Click a button to test a location</div>
    </div>
  </div>
  
  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  
  <script>
    // Mapbox access token
    const MAPBOX_TOKEN = 'pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA';
    
    // Test locations
    const locations = {
      sydney: { name: 'Sydney CBD', lat: -33.8688, lng: 151.2093 },
      parramatta: { name: 'Parramatta', lat: -33.8150, lng: 151.0011 },
      bondi: { name: 'Bondi Beach', lat: -33.8915, lng: 151.2767 }
    };
    
    // NSW Planning Portal WFS/WMS endpoints
    const ZONING_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer';
    const LGA_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer';
    const ZONING_WMS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WMSServer';
    const LGA_WMS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WMSServer';
    
    // DOM elements
    const mapContainer = document.getElementById('mapContainer');
    const resultsContainer = document.getElementById('resultsContainer');
    
    // Map and layer references
    let map;
    let marker;
    let zoningWMS;
    let lgaWMS;
    
    // Initialize map
    function initMap() {
      // Create map instance
      map = L.map(mapContainer).setView([locations.sydney.lat, locations.sydney.lng], 12);
      
      // Add Mapbox tile layer
      L.tileLayer(`https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles/{z}/{x}/{y}?access_token=${MAPBOX_TOKEN}`, {
        attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
        maxZoom: 19
      }).addTo(map);
      
      // Add WMS layers
      zoningWMS = L.tileLayer.wms(ZONING_WMS_URL, {
        layers: '2',
        transparent: true,
        format: 'image/png',
        opacity: 0.7
      }).addTo(map);
      
      lgaWMS = L.tileLayer.wms(LGA_WMS_URL, {
        layers: '1',
        transparent: true,
        format: 'image/png',
        opacity: 0.7
      });
      
      // Add layer control
      L.control.layers(
        {}, // Base layers
        {
          'Zoning': zoningWMS,
          'LGA Boundaries': lgaWMS
        }
      ).addTo(map);
      
      // Add scale control
      L.control.scale().addTo(map);
    }
    
    // Test a location
    async function testLocation(location) {
      // Update map view
      map.setView([location.lat, location.lng], 15);
      
      // Add or update marker
      if (marker) {
        marker.setLatLng([location.lat, location.lng]);
      } else {
        marker = L.marker([location.lat, location.lng])
          .addTo(map)
          .bindPopup(location.name)
          .openPopup();
      }
      
      // Show loading message
      resultsContainer.innerHTML = `<p>Testing ${location.name}...</p>`;
      
      try {
        // Try different approaches to get zoning and LGA data
        const results = await Promise.allSettled([
          // Approach 1: Use CQL_FILTER
          fetchWFSWithCQL(ZONING_WFS_URL, 'EPI_Primary_Planning_Layers:2', location.lng, location.lat),
          fetchWFSWithCQL(LGA_WFS_URL, 'EDP_Administrative_Boundaries:1', location.lng, location.lat),
          
          // Approach 2: Use XML Filter
          fetchWFSWithXML(ZONING_WFS_URL, 'EPI_Primary_Planning_Layers:2', location.lng, location.lat),
          fetchWFSWithXML(LGA_WFS_URL, 'EDP_Administrative_Boundaries:1', location.lng, location.lat),
          
          // Approach 3: Try different layer names
          fetchWFSWithCQL(ZONING_WFS_URL, 'EPI_Primary_Planning_Layers:Layer_2', location.lng, location.lat),
          fetchWFSWithCQL(LGA_WFS_URL, 'EDP_Administrative_Boundaries:Layer_1', location.lng, location.lat)
        ]);
        
        // Display results
        let html = `<h3>Results for ${location.name} (${location.lat}, ${location.lng})</h3>`;
        
        html += '<h4>Approach 1: CQL_FILTER</h4>';
        html += `<p>Zoning: ${results[0].status === 'fulfilled' ? 'Success' : 'Failed'}</p>`;
        html += `<p>LGA: ${results[1].status === 'fulfilled' ? 'Success' : 'Failed'}</p>`;
        
        html += '<h4>Approach 2: XML Filter</h4>';
        html += `<p>Zoning: ${results[2].status === 'fulfilled' ? 'Success' : 'Failed'}</p>`;
        html += `<p>LGA: ${results[3].status === 'fulfilled' ? 'Success' : 'Failed'}</p>`;
        
        html += '<h4>Approach 3: Different Layer Names</h4>';
        html += `<p>Zoning: ${results[4].status === 'fulfilled' ? 'Success' : 'Failed'}</p>`;
        html += `<p>LGA: ${results[5].status === 'fulfilled' ? 'Success' : 'Failed'}</p>`;
        
        // Show successful results if any
        const successfulResults = results.filter(r => r.status === 'fulfilled');
        if (successfulResults.length > 0) {
          html += '<h4>Successful Results:</h4>';
          successfulResults.forEach((result, index) => {
            html += `<pre>${JSON.stringify(result.value, null, 2)}</pre>`;
          });
        } else {
          html += '<p>All approaches failed. Check the console for details.</p>';
        }
        
        resultsContainer.innerHTML = html;
      } catch (error) {
        resultsContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        console.error('Test error:', error);
      }
    }
    
    // Fetch WFS data using CQL_FILTER
    async function fetchWFSWithCQL(serviceUrl, typeName, lng, lat) {
      try {
        console.log(`Fetching WFS with CQL_FILTER for ${typeName} at ${lng}, ${lat}`);
        
        // Use our proxy server to avoid CORS issues
        const response = await fetch('http://localhost:3004/api/wfs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            serviceUrl,
            params: {
              service: 'WFS',
              version: '1.1.0',
              request: 'GetFeature',
              typeNames: typeName,
              outputFormat: 'application/json',
              CQL_FILTER: `INTERSECTS(Shape, POINT(${lng} ${lat}))`
            }
          })
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`WFS request failed: ${errorText}`);
        }
        
        return await response.json();
      } catch (error) {
        console.error(`Error fetching WFS with CQL_FILTER for ${typeName}:`, error);
        throw error;
      }
    }
    
    // Fetch WFS data using XML Filter
    async function fetchWFSWithXML(serviceUrl, typeName, lng, lat) {
      try {
        console.log(`Fetching WFS with XML Filter for ${typeName} at ${lng}, ${lat}`);
        
        // Use our proxy server to avoid CORS issues
        const response = await fetch('http://localhost:3004/api/wfs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            serviceUrl,
            params: {
              service: 'WFS',
              version: '1.1.0',
              request: 'GetFeature',
              typeNames: typeName,
              outputFormat: 'application/json',
              filter: `
                <Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml">
                  <Intersects>
                    <PropertyName>Shape</PropertyName>
                    <gml:Point srsName="EPSG:4326">
                      <gml:coordinates>${lng},${lat}</gml:coordinates>
                    </gml:Point>
                  </Intersects>
                </Filter>
              `
            }
          })
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`WFS request failed: ${errorText}`);
        }
        
        return await response.json();
      } catch (error) {
        console.error(`Error fetching WFS with XML Filter for ${typeName}:`, error);
        throw error;
      }
    }
    
    // Initialize the application
    function init() {
      initMap();
      
      // Add event listeners for test buttons
      document.getElementById('testSydney').addEventListener('click', () => testLocation(locations.sydney));
      document.getElementById('testParramatta').addEventListener('click', () => testLocation(locations.parramatta));
      document.getElementById('testBondi').addEventListener('click', () => testLocation(locations.bondi));
    }
    
    // Start the application when the page loads
    window.addEventListener('load', init);
  </script>
</body>
</html>
