import React, { useState, useEffect } from 'react';
import { MapPin, Loader2 } from 'lucide-react';
import { useNSWSuburbsSearch } from '../../hooks/useNSWSuburbs';
import { searchNSWSuburbs, NSWSuburb } from '../../services/nsw-suburbs';

interface LocationSearchProps {
  onSelect: (location: { lat: number; lng: number; displayName: string }) => void;
  isOpen: boolean;
  searchQuery?: string;
}

export default function LocationSearch({ onSelect, isOpen, searchQuery = '' }: LocationSearchProps) {
  const [searchInput, setSearchInput] = useState(searchQuery);

  // Use NSW suburbs search hook with simplified interface
  const { suburbs, loading } = useNSWSuburbsSearch(searchInput);

  // Get popular NSW suburbs for initial display
  const getPopularSuburbs = (): NSWSuburb[] => {
    return [
      { id: 'pop-1', suburb: 'Sydney', postcode: '2000', state: 'NSW', lat: -33.8688, lng: 151.2093, lga_name: 'City of Sydney', display_name: 'Sydney, NSW 2000' },
      { id: 'pop-2', suburb: 'Bondi Beach', postcode: '2026', state: 'NSW', lat: -33.8915, lng: 151.2767, lga_name: 'Waverley Council', display_name: 'Bondi Beach, NSW 2026' },
      { id: 'pop-3', suburb: 'Newtown', postcode: '2042', state: 'NSW', lat: -33.8978, lng: 151.1795, lga_name: 'Inner West Council', display_name: 'Newtown, NSW 2042' },
      { id: 'pop-4', suburb: 'Parramatta', postcode: '2150', state: 'NSW', lat: -33.8150, lng: 151.0011, lga_name: 'City of Parramatta', display_name: 'Parramatta, NSW 2150' },
      { id: 'pop-5', suburb: 'Manly', postcode: '2095', state: 'NSW', lat: -33.7969, lng: 151.2502, lga_name: 'Northern Beaches Council', display_name: 'Manly, NSW 2095' },
      { id: 'pop-6', suburb: 'Chatswood', postcode: '2067', state: 'NSW', lat: -33.7967, lng: 151.1800, lga_name: 'Willoughby City Council', display_name: 'Chatswood, NSW 2067' },
      { id: 'pop-7', suburb: 'North Sydney', postcode: '2060', state: 'NSW', lat: -33.8389, lng: 151.2071, lga_name: 'North Sydney Council', display_name: 'North Sydney, NSW 2060' },
      { id: 'pop-8', suburb: 'Cronulla', postcode: '2230', state: 'NSW', lat: -34.0581, lng: 151.1543, lga_name: 'Sutherland Shire Council', display_name: 'Cronulla, NSW 2230' }
    ];
  };

  const popularSuburbs = getPopularSuburbs();

  // Update input when searchQuery prop changes
  useEffect(() => {
    setSearchInput(searchQuery);
  }, [searchQuery]);

  if (!isOpen) return null;

  const handleSuburbSelect = (suburb: any) => {
    onSelect({
      lat: suburb.latitude || suburb.lat || -33.8688, // Handle both API formats
      lng: suburb.longitude || suburb.lng || 151.2093,
      displayName: `${suburb.name || suburb.suburb}, NSW ${suburb.postcode}`
    });
  };

  const handleLocationSelect = (location: { lat: number; lng: number; displayName: string }) => {
    onSelect(location);
  };

  return (
    <div
      className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden z-50"
      style={{
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1)',
        maxHeight: '400px'
      }}>
      <div className="p-4">
        {/* NSW Suburbs Search Results */}
        {loading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-purple-600" />
          </div>
        ) : suburbs.length > 0 ? (
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">
              {searchInput.length === 1 ? 'Auto-suggestions' : 'NSW Suburbs'}
              <span className="text-xs text-gray-500 ml-2">({suburbs.length} found)</span>
            </h3>
            <div className="space-y-1 max-h-64 overflow-y-auto">
              {suburbs.map((suburb, index) => (
                <button
                  key={`${suburb.name}-${suburb.postcode}-${index}`}
                  onClick={() => handleSuburbSelect(suburb)}
                  className="w-full flex items-center px-3 py-2 hover:bg-purple-50 rounded-lg text-left transition-colors group"
                >
                  <MapPin className="h-4 w-4 text-purple-500 mr-3 flex-shrink-0 group-hover:text-purple-600" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {/* Highlight matching text */}
                      {searchInput.length > 0 ? (
                        <span>
                          {(suburb.name || suburb.suburb).toLowerCase().includes(searchInput.toLowerCase()) ? (
                            <>
                              {(suburb.name || suburb.suburb).substring(0, (suburb.name || suburb.suburb).toLowerCase().indexOf(searchInput.toLowerCase()))}
                              <span className="bg-yellow-200 text-gray-900">
                                {(suburb.name || suburb.suburb).substring(
                                  (suburb.name || suburb.suburb).toLowerCase().indexOf(searchInput.toLowerCase()),
                                  (suburb.name || suburb.suburb).toLowerCase().indexOf(searchInput.toLowerCase()) + searchInput.length
                                )}
                              </span>
                              {(suburb.name || suburb.suburb).substring((suburb.name || suburb.suburb).toLowerCase().indexOf(searchInput.toLowerCase()) + searchInput.length)}
                            </>
                          ) : (
                            suburb.name || suburb.suburb
                          )}
                        </span>
                      ) : (
                        suburb.name || suburb.suburb
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      NSW {suburb.postcode}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        ) : searchInput.length >= 1 && !loading ? (
          <div className="mb-4 text-center py-6">
            <div className="text-gray-400 mb-2">
              <MapPin className="h-8 w-8 mx-auto" />
            </div>
            <p className="text-sm text-gray-500 mb-1">No NSW suburbs found for "{searchInput}"</p>
            <p className="text-xs text-gray-400">Try a different spelling or nearby suburb</p>
          </div>
        ) : null}

        {/* Popular NSW Suburbs */}
        {!searchInput && (
          <>
            <h3 className="text-sm font-semibold text-gray-700 mb-3">Popular NSW Locations</h3>
            <div className="space-y-1">
              {popularSuburbs.map((suburb, index) => (
                <button
                  key={`${suburb.name}-${suburb.postcode}-${index}`}
                  onClick={() => handleSuburbSelect(suburb)}
                  className="w-full flex items-center px-3 py-2 hover:bg-purple-50 rounded-lg text-left transition-colors"
                >
                  <MapPin className="h-4 w-4 text-purple-500 mr-3" />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {suburb.name || suburb.suburb}
                    </div>
                    <div className="text-xs text-gray-500">
                      NSW {suburb.postcode}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}