/**
 * Clerk OAuth Helper
 * 
 * This utility provides functions to help with OAuth authentication in Clerk,
 * particularly for Google OAuth.
 */

import { User as ClerkUser } from '@clerk/clerk-react';
import { syncClerkUserWithSupabase } from '../services/auth/clerk-supabase-sync';

/**
 * Process a successful OAuth authentication
 * @param user The authenticated Clerk user
 * @param provider The OAuth provider (e.g., 'google')
 * @returns A promise that resolves when processing is complete
 */
export async function processOAuthSuccess(
  user: Clerk<PERSON>ser,
  provider: 'google' | 'github' | 'facebook' | string
): Promise<boolean> {
  try {
    console.log(`Processing successful ${provider} OAuth authentication for user:`, user.id);

    // Store user info in localStorage for persistence
    try {
      localStorage.setItem('clerk_user_email', user.primaryEmailAddress?.emailAddress || '');
      localStorage.setItem('clerk_user_id', user.id);
      localStorage.setItem('clerk_user_name', `${user.firstName || ''} ${user.lastName || ''}`.trim());
      localStorage.setItem('clerk_auth_time', new Date().toISOString());
      localStorage.setItem('clerk_auth_provider', provider);

      // Set a flag in localStorage to indicate successful authentication
      localStorage.setItem('auth_success', 'true');
      localStorage.setItem('auth_success_time', new Date().toISOString());
    } catch (storageError) {
      console.error('Error storing user info in localStorage:', storageError);
    }

    // Sync user with Supabase
    try {
      const syncResult = await syncClerkUserWithSupabase(user);
      if (syncResult) {
        console.log('Successfully synced user with Supabase');
      } else {
        console.warn('Failed to sync user with Supabase');
      }
    } catch (syncError) {
      console.error('Error syncing user with Supabase:', syncError);
    }

    // Dispatch auth complete event
    try {
      window.dispatchEvent(new CustomEvent('auth_complete', {
        detail: { 
          success: true, 
          provider: `clerk-${provider}`,
          user: {
            id: user.id,
            email: user.primaryEmailAddress?.emailAddress
          }
        }
      }));
    } catch (eventError) {
      console.error('Error dispatching auth_complete event:', eventError);
    }

    return true;
  } catch (error) {
    console.error(`Error processing ${provider} OAuth success:`, error);
    return false;
  }
}

/**
 * Handle OAuth callback
 * This should be called when the user is redirected back from the OAuth provider
 * @returns A promise that resolves when processing is complete
 */
export async function handleOAuthCallback(): Promise<boolean> {
  try {
    console.log('Handling OAuth callback');

    // Set a flag to prevent multiple reloads
    if (localStorage.getItem('oauth_callback_processing') === 'true') {
      console.log('OAuth callback already being processed, skipping');
      return false;
    }

    localStorage.setItem('oauth_callback_processing', 'true');

    // Set auth_success flag to ensure the session is checked
    localStorage.setItem('auth_success', 'true');
    localStorage.setItem('auth_success_time', new Date().toISOString());

    // Clear the flag after a timeout
    setTimeout(() => {
      localStorage.removeItem('oauth_callback_processing');
    }, 10000);

    return true;
  } catch (error) {
    console.error('Error handling OAuth callback:', error);
    return false;
  }
}

/**
 * Check if the current page is an OAuth callback
 * @returns True if the current page is an OAuth callback
 */
export function isOAuthCallback(): boolean {
  const url = window.location.href;
  return (
    url.includes('/auth/callback') ||
    url.includes('/oauth/callback') ||
    url.includes('code=') ||
    url.includes('oauth_token=') ||
    url.includes('state=') ||
    url.includes('error=')
  );
}

/**
 * Initialize OAuth helper
 * This should be called when the app starts
 */
export function initOAuthHelper(): void {
  // Check if we're on an OAuth callback page
  if (isOAuthCallback()) {
    console.log('Detected OAuth callback page, initializing helper');
    handleOAuthCallback().catch(error => {
      console.error('Error in OAuth callback handler:', error);
    });
  }

  // Listen for auth_complete events
  window.addEventListener('auth_complete', (event: Event) => {
    const customEvent = event as CustomEvent;
    console.log('Auth complete event detected in OAuth helper:', customEvent.detail);
    
    // If we're on an OAuth callback page, redirect to home
    if (isOAuthCallback()) {
      console.log('Redirecting from OAuth callback to home page');
      window.location.href = '/';
    }
  });
}
