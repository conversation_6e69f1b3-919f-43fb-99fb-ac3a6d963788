# Augment VIP

A utility toolkit for Augment VIP users, providing tools to manage and clean VS Code databases.

## Features

- Database Cleaning: Remove Augment-related entries from VS Code databases
- Telemetry ID Modification: Generate random telemetry IDs for VS Code to enhance privacy
- Cross-Platform Support: Works on macOS, Linux, and Windows
- Safe Operations: Creates backups before making any changes
- User-Friendly: Clear, color-coded output and detailed status messages

## Requirements

- Bash shell environment
- SQLite3
- curl (for future updates)
- jq (for configuration parsing)

## Installation

### One-Line Install

You can install with a single command using curl:

```bash
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/install.sh -o install.sh && chmod +x install.sh && ./install.sh
```

### Repository Install

If you prefer to clone the entire repository:

```bash
git clone https://github.com/azrilaiman2003/augment-vip.git
cd augment-vip
./scripts/install.sh
```

### Automated Installation

You can also run the installation script with options to automatically run the cleaning and ID modification scripts:

```bash
# One-line install with all features
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/scripts/install.sh -o install.sh && chmod +x install.sh && ./install.sh --all

# After installation, you'll have an augment-vip directory
# Navigate to it to run scripts directly:
cd augment-vip

# Or if you've already downloaded the script or cloned the repository:

# Run installation and clean databases
./install.sh --clean
# or
./scripts/install.sh --clean

# Run installation and modify telemetry IDs
./install.sh --modify-ids
# or
./scripts/install.sh --modify-ids

# Run installation, clean databases, and modify telemetry IDs
./install.sh --all
# or
./scripts/install.sh --all

# Show help
./install.sh --help
# or
./scripts/install.sh --help
```

## Usage

### Clean VS Code Databases

To remove Augment-related entries from VS Code databases:

```bash
./scripts/clean_code_db.sh
```

### Modify VS Code Telemetry IDs

To change the telemetry IDs in VS Code's storage.json file:

```bash
./scripts/id_modifier.sh
```

## Project Structure

```
augment-vip/
├── config/             # Configuration files
├── data/               # Data storage
├── logs/               # Log files
├── scripts/            # Utility scripts
│   ├── clean_code_db.sh # Database cleaning script
│   ├── id_modifier.sh  # Telemetry ID modification script
│   └── install.sh      # Installation script
├── temp/               # Temporary files
└── README.md           # This file
```

## How It Works

The database cleaning tool works by:

1. Finding Database Locations: Automatically detects the correct paths for VS Code databases based on your operating system.
2. Creating Backups: Before making any changes, the tool creates a backup of each database file.
3. Cleaning Databases: Uses SQLite commands to remove entries containing "augment" from the databases.
4. Reporting Results: Provides detailed feedback about the operations performed.

## Troubleshooting

### Common Issues

#### Missing Dependencies

```
[ERROR] sqlite3 is not installed
```

Install the required dependencies:

- macOS: `brew install sqlite3 curl jq`
- Ubuntu/Debian: `sudo apt install sqlite3 curl jq`
- Fedora/RHEL: `sudo dnf install sqlite3 curl jq`
- Windows (with Chocolatey): `choco install sqlite curl jq`

#### Permission Denied

```
[ERROR] Permission denied
```

Make sure the scripts are executable:

```bash
chmod +x scripts/*.sh
```

#### No Databases Found

```
[WARNING] No database files found
```

This may occur if you haven't used VS Code on your system, or if it's installed in non-standard locations.
