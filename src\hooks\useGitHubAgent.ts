import { useState, useEffect, useCallback } from 'react';
import { GitHubMarketplaceAgent, Message, ConversationStage } from '../lib/salesgpt/github-agent';

// Define the return type for the hook
interface UseGitHubAgentReturn {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string) => Promise<void>;
  resetConversation: () => void;
  currentStage: ConversationStage | null;
}

export function useGitHubAgent(apiKey?: string): UseGitHubAgentReturn {
  const [agent, setAgent] = useState<GitHubMarketplaceAgent | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStage, setCurrentStage] = useState<ConversationStage | null>(null);

  // Initialize the agent
  useEffect(() => {
    const initializeAgent = async () => {
      try {
        // Use the provided API key or get it from environment variables
        const key = apiKey || import.meta.env.VITE_GITHUB_TOKEN || '****************************************';

        if (!key) {
          setError('GitHub API token is required');
          return;
        }

        const newAgent = new GitHubMarketplaceAgent(key);
        setAgent(newAgent);
        setCurrentStage(newAgent.getCurrentStage());

        // Add initial AI message
        const initialMessage: Message = {
          id: Date.now().toString(),
          role: 'ai',
          content: 'Hi there! 🏠✨ I\'m Homie from HouseGoing! I\'m super excited to help you find the perfect venue for your special event. What kind of celebration are you planning? 🎉',
          timestamp: new Date(),
        };

        setMessages([initialMessage]);
      } catch (err: any) {
        setError(err.message || 'Failed to initialize GitHub agent');
        console.error('Error initializing GitHub agent:', err);
      }
    };

    initializeAgent();
  }, [apiKey]);

  // Send a message to the agent
  const sendMessage = useCallback(async (message: string) => {
    if (!agent) {
      setError('Agent not initialized');
      return;
    }

    try {
      // Show loading state immediately
      setIsLoading(true);

      // Add user message to the list
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'human',
        content: message,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);

      // Add a small delay to show typing indicator (improves perceived responsiveness)
      setTimeout(() => {
        // This keeps the typing indicator visible for at least 500ms
        // which makes the AI seem more responsive even if the actual API call is fast
      }, 500);

      // Process the message with the agent
      const response = await agent.processMessage(message);

      // Add AI response to the list
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'ai',
        content: response,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);

      // Update the current stage
      setCurrentStage(agent.getCurrentStage());
    } catch (err: any) {
      console.error('Error processing message:', err);

      // Provide more user-friendly error messages
      if (err.message?.includes('API key')) {
        setError('Authentication error. Please check your GitHub token.');
      } else if (err.message?.includes('rate limit')) {
        setError('Rate limit exceeded. Please try again later.');
      } else if (err.message?.includes('network')) {
        setError('Network error. Please check your internet connection.');
      } else {
        setError(err.message || 'Failed to process message');
      }

      // Add AI response about the error
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'ai',
        content: 'I apologize, but I encountered an error processing your message. Please try again or contact support if the issue persists.',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [agent]);

  // Reset the conversation
  const resetConversation = useCallback(() => {
    if (!agent) {
      return;
    }

    agent.resetConversation();
    setCurrentStage(agent.getCurrentStage());

    // Add initial AI message
    const initialMessage: Message = {
      id: Date.now().toString(),
      role: 'ai',
      content: 'Hi there! 🏠✨ I\'m Homie from HouseGoing! I\'m super excited to help you find the perfect venue for your special event. What kind of celebration are you planning? 🎉',
      timestamp: new Date(),
    };

    setMessages([initialMessage]);
  }, [agent]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    resetConversation,
    currentStage,
  };
}
