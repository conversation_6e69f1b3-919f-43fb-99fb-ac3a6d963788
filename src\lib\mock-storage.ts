/**
 * Mock storage service for when Supabase storage is unavailable
 * This provides a fallback mechanism for development and testing
 */

// Store uploaded files in memory
const mockStorage: Record<string, string[]> = {
  'property-images': [],
  'venue-images': []
};

// Generate a mock URL for an uploaded file
const generateMockUrl = (bucketName: string, fileName: string): string => {
  return `https://mock-storage.housegoing.com/${bucketName}/${fileName}`;
};

/**
 * Mock implementation of Supabase storage
 */
export const mockStorageService = {
  /**
   * Create a bucket
   */
  createBucket: async (bucketName: string, options?: any) => {
    console.log(`[Mock] Creating bucket: ${bucketName}`, options);
    
    if (!mockStorage[bucketName]) {
      mockStorage[bucketName] = [];
    }
    
    return { data: { name: bucketName }, error: null };
  },
  
  /**
   * List buckets
   */
  listBuckets: async () => {
    console.log('[Mock] Listing buckets');
    
    const buckets = Object.keys(mockStorage).map(name => ({ name }));
    
    return { data: buckets, error: null };
  },
  
  /**
   * Upload a file to a bucket
   */
  uploadToStorage: async (bucketName: string, fileName: string, file: File) => {
    console.log(`[Mock] Uploading file to ${bucketName}: ${fileName}`);
    
    if (!mockStorage[bucketName]) {
      mockStorage[bucketName] = [];
    }
    
    const mockUrl = generateMockUrl(bucketName, fileName);
    mockStorage[bucketName].push(mockUrl);
    
    return {
      data: {
        path: `${bucketName}/${fileName}`,
        fullPath: mockUrl
      },
      error: null
    };
  },
  
  /**
   * Get a public URL for a file
   */
  getPublicUrl: (bucketName: string, filePath: string) => {
    console.log(`[Mock] Getting public URL for ${bucketName}/${filePath}`);
    
    return {
      data: {
        publicUrl: generateMockUrl(bucketName, filePath)
      }
    };
  }
};

/**
 * Check if Supabase storage is available
 */
export const isSupabaseStorageAvailable = async (supabase: any): Promise<boolean> => {
  try {
    const { data, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.warn('Supabase storage not available:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.warn('Error checking Supabase storage:', error);
    return false;
  }
};
