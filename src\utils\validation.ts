// Common validation rules for forms

export const validation = {
  // Required field validation
  required: (message = 'This field is required') => ({
    validate: (value: any) => {
      if (value === undefined || value === null) return false;
      if (typeof value === 'string') return value.trim() !== '';
      if (typeof value === 'number') return true;
      if (Array.isArray(value)) return value.length > 0;
      return !!value;
    },
    message
  }),

  // Email validation
  email: (message = 'Please enter a valid email address') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values, use required for that
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(value);
    },
    message
  }),

  // Minimum length validation
  minLength: (min: number, message = `Must be at least ${min} characters`) => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values, use required for that
      return value.length >= min;
    },
    message
  }),

  // Maximum length validation
  maxLength: (max: number, message = `Must be no more than ${max} characters`) => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      return value.length <= max;
    },
    message
  }),

  // Numeric validation
  numeric: (message = 'Please enter a valid number') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      return !isNaN(Number(value));
    },
    message
  }),

  // Minimum value validation
  min: (min: number, message = `Must be at least ${min}`) => ({
    validate: (value: number | string) => {
      if (value === '' || value === undefined || value === null) return true;
      return Number(value) >= min;
    },
    message
  }),

  // Maximum value validation
  max: (max: number, message = `Must be no more than ${max}`) => ({
    validate: (value: number | string) => {
      if (value === '' || value === undefined || value === null) return true;
      return Number(value) <= max;
    },
    message
  }),

  // Pattern validation
  pattern: (pattern: RegExp, message = 'Invalid format') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      return pattern.test(value);
    },
    message
  }),

  // Password validation (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
  password: (message = 'Password must be at least 8 characters with 1 uppercase letter, 1 lowercase letter, and 1 number') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
      return passwordRegex.test(value);
    },
    message
  }),

  // Match another field (e.g., for password confirmation)
  matches: (field: string, fieldName: string, message?: string) => ({
    validate: (value: string, formValues: Record<string, any>) => {
      if (!value) return true; // Don't validate empty values
      return value === formValues[field];
    },
    message: message || `Must match ${fieldName}`
  }),

  // Australian phone number validation
  australianPhone: (message = 'Please enter a valid Australian phone number') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      // Matches formats like: 0***********, 0412345678, +61 ***********, +***********
      const phoneRegex = /^(\+61|0)[2-478](?:[ -]?[0-9]){8}$/;
      return phoneRegex.test(value.replace(/\s+/g, ''));
    },
    message
  }),

  // Australian postcode validation
  australianPostcode: (message = 'Please enter a valid Australian postcode') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      const postcodeRegex = /^[0-9]{4}$/;
      return postcodeRegex.test(value);
    },
    message
  }),

  // URL validation
  url: (message = 'Please enter a valid URL') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    },
    message
  }),

  // Date validation (checks if it's a valid date)
  date: (message = 'Please enter a valid date') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      const date = new Date(value);
      return !isNaN(date.getTime());
    },
    message
  }),

  // Future date validation
  futureDate: (message = 'Date must be in the future') => ({
    validate: (value: string) => {
      if (!value) return true; // Don't validate empty values
      const date = new Date(value);
      const now = new Date();
      return !isNaN(date.getTime()) && date > now;
    },
    message
  }),

  // Custom validation function
  custom: (validateFn: (value: any) => boolean, message: string) => ({
    validate: validateFn,
    message
  })
};
