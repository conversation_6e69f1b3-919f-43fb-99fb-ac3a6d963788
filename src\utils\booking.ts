export function calculateHours(startTime: string, endTime: string): number {
  if (!startTime || !endTime) return 0;

  const startHour = parseInt(startTime.split(':')[0]);
  const endHour = parseInt(endTime.split(':')[0]);

  // Handle overnight bookings (when end time is earlier than start time)
  if (endHour < startHour) {
    return (24 - startHour) + endHour; // Hours until midnight + hours after midnight
  }

  return endHour - startHour;
}

export function getAvailableStartHours(): number[] {
  return Array.from({ length: 24 }, (_, i) => i);
}

export function getAvailableEndHours(startTime: string): number[] {
  if (!startTime) return [];

  const startHour = parseInt(startTime.split(':')[0]);

  // Include hours after the start time and also early morning hours for overnight
  const afterStartHours = Array.from(
    { length: 24 - startHour },
    (_, i) => startHour + i + 1
  );

  // Add early morning hours (0 to startHour-1) for overnight bookings
  const earlyMorningHours = Array.from(
    { length: startHour },
    (_, i) => i
  );

  return [...afterStartHours, ...earlyMorningHours];
}

/**
 * Check if a date is a weekend (Saturday or Sunday)
 * @param date Date to check
 * @returns True if weekend, false otherwise
 */
export function isWeekend(date: string | Date): boolean {
  const d = typeof date === 'string' ? new Date(date) : date;
  const day = d.getDay();
  return day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
}

/**
 * Check if a date is a public holiday in Australia
 * This is a simplified implementation that only checks for major holidays
 * In a real implementation, this would use a more comprehensive holiday API
 * @param date Date to check
 * @returns True if holiday, false otherwise
 */
export function isHoliday(date: string | Date): boolean {
  const d = typeof date === 'string' ? new Date(date) : date;
  const month = d.getMonth() + 1; // getMonth() returns 0-11
  const day = d.getDate();

  // Check for major Australian holidays
  // New Year's Day
  if (month === 1 && day === 1) return true;

  // Australia Day
  if (month === 1 && day === 26) return true;

  // Christmas Day
  if (month === 12 && day === 25) return true;

  // Boxing Day
  if (month === 12 && day === 26) return true;

  // In a real implementation, we would check for other holidays
  // including Easter, which varies by date

  return false;
}

/**
 * Check if a time is considered "late night" (after 10pm)
 * @param time Time in 24-hour format (HH:MM)
 * @returns True if late night, false otherwise
 */
export function isLateNight(time: string): boolean {
  if (!time) return false;

  const hour = parseInt(time.split(':')[0]);
  return hour >= 22 || hour < 6; // 10pm to 6am
}