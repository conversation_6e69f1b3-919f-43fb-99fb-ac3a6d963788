import { VenueCard } from "@/components/VenueCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const featuredVenues = [
  {
    image: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3",
    title: "Skyline Lounge",
    price: 200,
    location: "Downtown, NYC",
    capacity: 150,
    rating: 4.9,
    reviews: 128,
    amenities: ["Pro Sound System", "Full Bar", "Rooftop Access"],
    tags: ["After Party", "Popular Choice"]
  },
  {
    image: "https://images.unsplash.com/photo-1519750783826-e2420f4d687f",
    title: "The Garden House",
    price: 250,
    location: "Brooklyn, NYC",
    capacity: 200,
    rating: 4.8,
    reviews: 95,
    amenities: ["Indoor/Outdoor Space", "Bar Setup", "Sound System"],
    tags: ["Wedding After Party", "Garden"]
  },
  {
    image: "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3",
    title: "Urban Dance Club",
    price: 300,
    location: "Manhattan, NYC",
    capacity: 180,
    rating: 4.7,
    reviews: 73,
    amenities: ["DJ Booth", "Dance Floor", "VIP Areas"],
    tags: ["Club Venue", "Trending"]
  },
];

export function FeaturedVenues() {
  return (
    <div className="container mx-auto py-16 px-4">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h2 className="text-2xl font-semibold mb-2">Featured Party Venues</h2>
          <p className="text-gray-600">Perfect spaces for your after-party celebration</p>
        </div>
        <Button variant="link" asChild>
          <Link to="/search" className="text-primary hover:text-primary/80">
            View all venues →
          </Link>
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {featuredVenues.map((venue, index) => (
          <VenueCard key={index} {...venue} />
        ))}
      </div>
    </div>
  );
}