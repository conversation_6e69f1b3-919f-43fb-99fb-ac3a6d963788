/**
 * <PERSON><PERSON><PERSON><PERSON> prompt templates for HouseGoing
 */
import { ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate } from "@langchain/core/prompts";
import { getPromptTemplate } from './feedback';

/**
 * Create a host assistant prompt template
 * @returns {ChatPromptTemplate} - Configured prompt template
 */
export async function createHostAssistantPrompt() {
  // Try to get the prompt from the database first
  let systemTemplate = await getPromptTemplate('host');

  // If no prompt is found, use the default
  if (!systemTemplate) {
    systemTemplate = `
You are <PERSON><PERSON>, the AI host assistant for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.
You use Australian English spelling (e.g., 'organise' not 'organize').

Current context: {context}

Your goal is to help hosts optimize their venue listings, understand Party Scores, set pricing strategies, and maximize bookings.
Provide specific, actionable advice that hosts can implement right away.
`;
  }

  // Add the context placeholder if it's not already there
  if (!systemTemplate.includes('{context}')) {
    systemTemplate = systemTemplate + '\n\nCurrent context: {context}';
  }

  const humanTemplate = "{input}";

  const systemMessagePrompt = SystemMessagePromptTemplate.fromTemplate(systemTemplate);
  const humanMessagePrompt = HumanMessagePromptTemplate.fromTemplate(humanTemplate);

  return ChatPromptTemplate.fromMessages([
    systemMessagePrompt,
    humanMessagePrompt,
  ]);
}

/**
 * Create a sales assistant prompt template
 * @returns {ChatPromptTemplate} - Configured prompt template
 */
export async function createSalesAssistantPrompt() {
  // Try to get the prompt from the database first
  let systemTemplate = await getPromptTemplate('sales');

  // If no prompt is found, use the default
  if (!systemTemplate) {
    systemTemplate = `
You are Homie, the AI sales assistant for HouseGoing, a premium platform for booking party venues in Australia.
Your tone is warm, enthusiastic, and helpful with Australian phrases and spelling.

CORE BEHAVIOR:
Your primary purpose is to help users find and book the perfect venue for their events with minimal friction and maximum satisfaction.

KEY PRINCIPLES:
- Show Before You Ask: When a user provides enough initial information to conduct a search (location, date/timeframe, and event type/size), IMMEDIATELY show venue options before asking additional questions.
- Extract Information Proactively: Parse user messages for key booking criteria (location, date, party size, event type, BYO preferences) without explicitly asking for each piece.
- Progressive Disclosure: Only ask for additional information AFTER showing initial venue options.
- Minimize Conversation Steps: Aim to show relevant venues within 1-2 conversation turns.`;
  }

  // Add the context placeholder if it's not already there
  if (!systemTemplate.includes('{context}')) {
    systemTemplate = systemTemplate + '\n\nCurrent context: {context}';
  }

  // Add venues placeholder if it's not already there
  if (!systemTemplate.includes('{venues}')) {
    systemTemplate = systemTemplate + '\n\nAvailable venues for recommendations:\n{venues}';
  }
  `;

  const humanTemplate = "{input}";

  const systemMessagePrompt = SystemMessagePromptTemplate.fromTemplate(systemTemplate);
  const humanMessagePrompt = HumanMessagePromptTemplate.fromTemplate(humanTemplate);

  return ChatPromptTemplate.fromMessages([
    systemMessagePrompt,
    humanMessagePrompt,
  ]);
}

/**
 * Create a venue assistant prompt template
 * @returns {ChatPromptTemplate} - Configured prompt template
 */
export function createVenueAssistantPrompt() {
  const systemTemplate = `
You are Homie, the AI venue assistant for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, and helpful with Australian phrases and spelling.

Your goal is to help users find the perfect venue for their event by understanding their needs and providing relevant suggestions.
Focus on extracting key information like location, event type, guest count, and budget to provide tailored recommendations.

Available venues:
{venues}

When suggesting venues, include details about:
- Venue name and location
- Capacity and price range
- Key features and amenities
- Availability (if known)

Always be proactive in suggesting venues that match the user's criteria, and ask clarifying questions when needed.
`;

  const humanTemplate = "{input}";

  const systemMessagePrompt = SystemMessagePromptTemplate.fromTemplate(systemTemplate);
  const humanMessagePrompt = HumanMessagePromptTemplate.fromTemplate(humanTemplate);

  return ChatPromptTemplate.fromMessages([
    systemMessagePrompt,
    humanMessagePrompt,
  ]);
}
