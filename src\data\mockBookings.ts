import { Database } from '../types/supabase';

type Booking = Database['public']['Tables']['bookings']['Row'];

export const mockBookings: Booking[] = [
  {
    id: '1',
    venue_id: 'venue-001',
    guest_id: 'user-001',
    start_date: '2025-08-15',
    end_date: '2025-08-17',
    status: 'confirmed',
    total_price: 450,
    created_at: '2025-07-01T10:00:00Z',
    updated_at: '2025-07-01T10:00:00Z',
    guests_count: 4
  },
  {
    id: '2',
    venue_id: 'venue-002',
    guest_id: 'user-002',
    start_date: '2025-08-20',
    end_date: '2025-08-22',
    status: 'pending',
    total_price: 600,
    created_at: '2025-07-02T11:30:00Z',
    updated_at: '2025-07-02T11:30:00Z',
    guests_count: 6
  },
  {
    id: '3',
    venue_id: 'venue-003',
    guest_id: 'user-003',
    start_date: '2025-09-01',
    end_date: '2025-09-05',
    status: 'confirmed',
    total_price: 1200,
    created_at: '2025-07-03T09:15:00Z',
    updated_at: '2025-07-03T09:15:00Z',
    guests_count: 8
  }
];
