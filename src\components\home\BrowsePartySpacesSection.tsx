import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

export default function BrowsePartySpacesSection() {
  const venueTypes = [
    {
      title: 'Wedding After-Parties',
      description: 'Keep the celebration going',
      features: ['Dance floor', 'Bar service', 'Late-night access'],
      icon: '🎉'
    },
    {
      title: 'Cocktail Lounges',
      description: 'Sophisticated party spaces',
      features: ['Full bar', 'Lounge seating', 'Ambient lighting'],
      icon: '🍸'
    },
    {
      title: 'Dance Venues',
      description: 'Perfect for dancing the night away',
      features: ['Pro sound system', 'Dance floor', 'DJ booth'],
      icon: '🎵'
    },
    {
      title: 'Party Houses',
      description: 'Private party locations',
      features: ['Kitchen access', 'Indoor/outdoor space', 'Sound system'],
      icon: '🏠'
    }
  ];

  return (
    <section className="pt-6 pb-8 px-4 sm:px-6 bg-white">
      <div className="container-width">
        <div className="flex justify-between items-center mb-4">
          <h2>Browse Party Spaces</h2>
          <Link
            to="/venue-types"
            className="inline-flex items-center text-purple-600 hover:text-purple-800 transition-colors text-small font-medium"
          >
            View all venue types <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </div>

        <p className="text-large text-gray-600 mb-6">
          Find the perfect venue type for your next celebration
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {venueTypes.map((venue) => (
            <div key={venue.title} className="card p-4 group">
              <div className="flex items-center mb-4">
                <div className="bg-purple-100 rounded-lg p-2 mr-3">
                  <span className="text-xl">{venue.icon}</span>
                </div>
                <h3 className="font-semibold text-lg">{venue.title}</h3>
              </div>

              <p className="text-gray-600 mb-4">{venue.description}</p>

              <ul className="space-y-2 mb-4">
                {venue.features.map((feature) => (
                  <li key={feature} className="flex items-center text-sm text-gray-600">
                    <span className="text-purple-500 mr-2">•</span>
                    {feature}
                  </li>
                ))}
              </ul>

              <Link
                to={`/venue-types/${venue.title.toLowerCase().replace(/\s+/g, '-')}`}
                className="text-sm text-purple-600 hover:text-purple-800 inline-flex items-center"
              >
                View details <ArrowRight className="ml-1 h-3 w-3" />
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
