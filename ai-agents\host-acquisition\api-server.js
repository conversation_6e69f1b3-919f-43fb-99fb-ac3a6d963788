/**
 * API Server for the Host Acquisition Agent
 *
 * This file provides an Express server that exposes the Host Acquisition Agent
 * as a REST API that can be integrated with the main HouseGoing application.
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { HuggingFaceInference } from "@langchain/community/llms/huggingface";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "langchain/memory";
import { initializeAgentExecutorWithOptions } from "langchain/agents";
import { hostTools } from './tools.js';
import partyScoreTool from './party-score-tool.js';

// Load environment variables
dotenv.config();

// Configure LangSmith (for tracing)
process.env.LANGCHAIN_TRACING_V2 = process.env.LANGSMITH_TRACING || 'true';
process.env.LANGCHAIN_ENDPOINT = process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com';
process.env.LANGCHAIN_API_KEY = process.env.LANGSMITH_API_KEY;
process.env.LANGCHAIN_PROJECT = process.env.LANGSMITH_PROJECT;

// Initialize Express app
const app = express();
app.use(cors());
app.use(express.json());

// Initialize the Hugging Face model
const model = new HuggingFaceInference({
  model: "mistralai/Mistral-7B-Instruct-v0.3", // Updated to the latest version
  apiKey: process.env.HUGGINGFACE_API_KEY,
  temperature: 0.7,
  maxTokens: 1024,
});

// Combine all tools
const tools = [
  partyScoreTool,
  ...hostTools
];

// Create a prompt template for the agent
const promptTemplate = `
You are Alex, the AI host acquisition specialist for HouseGoing, a premium platform for booking party venues.
Your tone is warm, enthusiastic, knowledgeable about venues, and slightly celebratory.

Your goal is to help potential hosts successfully list their venues on HouseGoing.
The user has signed up to our host enquiry list and needs guidance.

You have access to several tools:
1. PartyScoreCalculator - Calculate a Party Score (1-10) for venues based on local regulations and zoning
2. VenueAnalyzer - Analyze venue potential and estimate earnings
3. ListingEnhancer - Improve venue descriptions and suggest photo improvements
4. PricingOptimizer - Recommend optimal pricing strategies
5. CalendarManager - Help manage venue availability and booking settings
6. RulesGenerator - Create customized house rules
7. CompetitorAnalysis - Analyze similar venues in the area

When a host mentions their venue location, always use the PartyScoreCalculator to determine:
- The venue's Party Score (1-10)
- Local council noise regulations and curfew times
- Zoning restrictions that affect the venue
- Recommendations for time limits to display in the booking calendar

GUIDELINES:
- Be warm and welcoming but not overly casual
- Be enthusiastic about helping hosts find success
- Be confident and knowledgeable about venue options
- Use Australian-friendly language with occasional local phrases
- Keep initial greetings to 1-2 sentences
- Format lists with bullet points for easy scanning
- Bold important information
- Personalize responses by referencing specific venue types
- After identifying key details, share a brief benefit specific to that venue type
- Occasionally mention trending venue types or features in their area

Remember that you are only accessible to hosts, and your primary goal is to help them list their venues successfully.
`;

// Store agent instances for each user
const agentInstances = new Map();

/**
 * Get or create an agent instance for a user
 * @param {string} userId - User ID to create agent for
 * @returns {Promise<Object>} Agent executor
 */
async function getAgentForUser(userId) {
  // Check if agent already exists for this user
  if (agentInstances.has(userId)) {
    return agentInstances.get(userId);
  }

  // Create a new agent
  const memory = new BufferMemory({
    memoryKey: "chat_history",
    returnMessages: true,
  });

  const executor = await initializeAgentExecutorWithOptions(tools, model, {
    agentType: "structured-chat-zero-shot-react-description",
    verbose: true,
    prefix: promptTemplate,
    memory: memory,
  });

  // Store the agent
  agentInstances.set(userId, executor);

  return executor;
}

// Simple authentication middleware - in production, use a proper auth system
function authMiddleware(req, res, next) {
  const apiKey = req.headers['x-api-key'];
  const userId = req.headers['x-user-id'];

  if (!apiKey || !userId) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // In production, validate the API key and user ID
  // For now, we'll just attach the user ID to the request
  req.userId = userId;
  next();
}

// Apply authentication middleware to all routes
app.use(authMiddleware);

/**
 * Chat endpoint - Send a message to the agent
 */
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    const userId = req.userId;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Get or create agent for this user
    const agent = await getAgentForUser(userId);

    // Process the message
    console.log(`Processing message from user ${userId}: ${message}`);
    const response = await agent.call({ input: message });

    res.json({
      response: response.output,
      userId: userId
    });
  } catch (error) {
    console.error("Error processing message:", error);
    res.status(500).json({
      error: "Failed to process message",
      details: error.message
    });
  }
});

/**
 * Reset conversation endpoint - Clear conversation history
 */
app.post('/api/reset', async (req, res) => {
  try {
    const userId = req.userId;

    // Remove the agent instance to reset conversation
    if (agentInstances.has(userId)) {
      agentInstances.delete(userId);
    }

    res.json({
      success: true,
      message: "Conversation reset successfully"
    });
  } catch (error) {
    console.error("Error resetting conversation:", error);
    res.status(500).json({
      error: "Failed to reset conversation",
      details: error.message
    });
  }
});

/**
 * Direct tool access endpoint - Call a specific tool directly
 */
app.post('/api/tools/:toolName', async (req, res) => {
  try {
    const { toolName } = req.params;
    const { input } = req.body;
    const userId = req.userId;

    if (!input) {
      return res.status(400).json({ error: "Input is required" });
    }

    // Find the requested tool
    const tool = tools.find(t => t.name.toLowerCase() === toolName.toLowerCase());

    if (!tool) {
      return res.status(404).json({ error: "Tool not found" });
    }

    // Call the tool directly
    console.log(`User ${userId} calling tool ${toolName} directly`);
    const result = await tool.func(input);

    res.json({
      result: result,
      toolName: toolName,
      userId: userId
    });
  } catch (error) {
    console.error(`Error calling tool ${req.params.toolName}:`, error);
    res.status(500).json({
      error: `Failed to call tool ${req.params.toolName}`,
      details: error.message
    });
  }
});

/**
 * Party Score endpoint - Calculate a Party Score for a venue
 */
app.post('/api/party-score', async (req, res) => {
  try {
    const { address, venueType, isIndoor, hasNeighbors, soundproofing } = req.body;

    if (!address) {
      return res.status(400).json({ error: "Address is required" });
    }

    // Call the Party Score tool directly
    const result = await partyScoreTool.func(`Calculate Party Score for a ${venueType || 'venue'} at ${address}`);

    res.json({
      result: result
    });
  } catch (error) {
    console.error("Error calculating Party Score:", error);
    res.status(500).json({
      error: "Failed to calculate Party Score",
      details: error.message
    });
  }
});

/**
 * Health check endpoint
 */
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    version: '1.0.0',
    userId: req.userId
  });
});

// Start the server
const PORT = process.env.PORT || 3001;

export function startServer() {
  app.listen(PORT, () => {
    console.log(`Host Acquisition Agent API running on port ${PORT}`);
  });
}

// If this file is run directly, start the server
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export default {
  app,
  startServer
};
