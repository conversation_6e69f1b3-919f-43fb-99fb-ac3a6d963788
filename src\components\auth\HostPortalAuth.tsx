import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { LogIn } from 'lucide-react';
import OAuthButton from './OAuthButton';
import GoogleButton from './GoogleButton';
import HeaderAuthButtons from './HeaderAuthButtons';

interface HostPortalAuthProps {
  onSuccess?: () => void;
}

export default function HostPortalAuth({ onSuccess }: HostPortalAuthProps) {
  const [showDirectOAuth, setShowDirectOAuth] = useState(false);

  // Set localStorage flag to indicate host sign-in
  useEffect(() => {
    // Set flag in localStorage
    localStorage.setItem('registering_as_host', 'true');

    // Check if we should show the direct OAuth button
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('direct') === 'true') {
      setShowDirectOAuth(true);
    }
  }, []);

  return (
    <div>
      <div className="text-center mb-6">
        <h2 className="text-xl font-medium text-gray-800 mb-2">Start Hosting Today</h2>
      </div>

      {showDirectOAuth ? (
        <div className="space-y-4 max-w-md mx-auto">
          <GoogleButton
            registrationType="host"
            label="Continue with Google"
          />

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or</span>
            </div>
          </div>

          <button
            onClick={() => setShowDirectOAuth(false)}
            className="w-full flex items-center justify-center py-3 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <LogIn className="w-5 h-5 mr-2" />
            Use Email Instead
          </button>
        </div>
      ) : (
        <div>
          <div className="mb-6">
            <HeaderAuthButtons variant="owner" className="justify-center" />
          </div>

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or</span>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={() => setShowDirectOAuth(true)}
              className="px-6 py-2 flex items-center justify-center mx-auto bg-white hover:bg-gray-50 text-gray-700 rounded-md border border-gray-300 text-sm"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Sign in with Google
            </button>
          </div>
        </div>
      )}

      <div className="mt-6 pt-4 text-center text-xs text-gray-500">
        By continuing, you agree to our <a href="/terms" className="underline">Terms of Service</a> and <a href="/privacy" className="underline">Privacy Policy</a>
      </div>
    </div>
  );
}
