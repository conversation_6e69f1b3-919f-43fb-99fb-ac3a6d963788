/**
 * Mock implementations for AI training functions
 * Used during development to avoid requiring API keys
 */

/**
 * Mock function for analyzing conversations during development
 * @param {Array} conversation - The conversation history
 * @param {string} agentType - The type of agent (sales, host)
 * @returns {Object} - Mock analysis results
 */
export function mockAnalyzeConversation(conversation, agentType) {
  console.log(`Mock analyzing ${conversation.length} messages for ${agentType} assistant`);
  
  // Create a mock analysis based on the conversation
  const isUserAskingForVenue = conversation.some(msg => 
    msg.role === 'user' && 
    (msg.content.toLowerCase().includes('venue') || 
     msg.content.toLowerCase().includes('location') || 
     msg.content.toLowerCase().includes('place'))
  );
  
  const isAssistantShowingOptions = conversation.some(msg => 
    msg.role === 'assistant' && 
    (msg.content.toLowerCase().includes('here are some venues') || 
     msg.content.toLowerCase().includes('options for you'))
  );
  
  // Generate appropriate mock analysis
  if (agentType === 'sales') {
    return {
      strengths: isAssistantShowingOptions 
        ? "The assistant quickly provided venue options when asked, which is excellent. The tone is friendly and helpful." 
        : "The assistant maintains a friendly and professional tone throughout the conversation. Responses are clear and concise.",
      weaknesses: isAssistantShowingOptions 
        ? "The assistant could provide more specific details about each venue option, such as pricing and capacity." 
        : "The assistant didn't proactively show venue options when the user expressed interest in finding a venue.",
      suggestions: isAssistantShowingOptions 
        ? "Include more specific details about venues such as pricing, capacity, and availability. Also, ask follow-up questions about the user's preferences." 
        : "Show venue options more proactively when users express interest in finding a venue. Don't ask too many questions before showing options.",
      rating: isAssistantShowingOptions ? 8 : 6,
      fullAnalysis: "Mock analysis generated for development purposes."
    };
  } else {
    // Host assistant analysis
    return {
      strengths: "The assistant provides helpful guidance on optimizing venue listings. The tone is professional and encouraging.",
      weaknesses: "The assistant could provide more specific, actionable advice with examples. Some responses are too general.",
      suggestions: "Include specific examples and step-by-step instructions. Reference specific features of the HouseGoing platform that hosts can use.",
      rating: 7,
      fullAnalysis: "Mock analysis generated for development purposes."
    };
  }
}

/**
 * Mock function for suggesting prompt improvements during development
 * @param {string} currentPrompt - The current prompt template
 * @param {Array} feedbackItems - Array of feedback items
 * @param {string} agentType - The type of agent (sales, host)
 * @returns {Object} - Mock improvement suggestions
 */
export function mockSuggestPromptImprovements(currentPrompt, feedbackItems, agentType) {
  console.log(`Mock suggesting improvements for ${agentType} assistant prompt based on ${feedbackItems.length} feedback items`);
  
  // Create mock improvements
  const improvements = agentType === 'sales'
    ? "Based on user feedback, I suggest the following improvements:\n\n1. Emphasize showing venue options earlier in the conversation\n2. Include more specific details about venues when presenting options\n3. Ask fewer questions before showing options\n4. Maintain the friendly, helpful tone that users appreciate"
    : "Based on user feedback, I suggest the following improvements:\n\n1. Provide more specific, actionable advice for hosts\n2. Include step-by-step instructions for common hosting tasks\n3. Reference specific HouseGoing platform features\n4. Add more examples of successful venue listings";
  
  // Create a slightly modified version of the current prompt
  const promptLines = currentPrompt.split('\n');
  let newPrompt = currentPrompt;
  
  if (agentType === 'sales') {
    // Add emphasis on showing venues earlier for sales assistant
    newPrompt = currentPrompt.replace(
      'Progressive Disclosure: Only ask for additional information AFTER showing initial venue options.',
      'IMMEDIATE OPTIONS: Show venue options as soon as possible, even with minimal information. Progressive Disclosure: Only ask for additional information AFTER showing initial venue options.'
    );
  } else {
    // Add more specific guidance for host assistant
    newPrompt = currentPrompt.replace(
      'Provide specific, actionable advice that hosts can implement right away.',
      'Provide specific, actionable advice with step-by-step instructions that hosts can implement right away. Include examples of successful listings whenever possible.'
    );
  }
  
  return {
    improvements,
    newPrompt,
    currentPrompt
  };
}
