# Image Optimization Guide for HouseGoing

This guide outlines best practices for optimizing images in the HouseGoing website to improve performance.

## Tools Implemented

1. **vite-plugin-imagemin**: Automatically optimizes images during build
2. **ImageOptimizationService**: Service for generating optimized image URLs
3. **OptimizedImage Component**: React component for displaying optimized images

## Best Practices

### 1. Use the OptimizedImage Component

Always use the \OptimizedImage\ component instead of standard \<img>\ tags:

\\\jsx
import OptimizedImage from '../components/ui/OptimizedImage';

// Instead of <img src="/image.jpg" alt="Description" />
<OptimizedImage 
  src="https://res.cloudinary.com/your-cloud-name/image/upload/v1234/image.jpg"
  alt="Description"
  width={800}
  height={600}
  quality={80}
/>
\\\

### 2. Image Formats

- Use **SVG** for logos, icons, and simple illustrations
- Use **WebP** or **AVIF** for photographs (Cloudinary will serve these automatically)
- Avoid using PNG for photographs

### 3. Image Dimensions

- Never serve images larger than needed
- Use responsive images with appropriate sizes
- Maintain consistent aspect ratios

### 4. Performance Tips

- Set \priority\ to \	rue\ for above-the-fold images
- Use \placeholder="blur"\ for important images
- Lazy load below-the-fold images (default behavior)

### 5. Cloudinary Best Practices

When uploading to Cloudinary:

- Use descriptive filenames
- Organize images in folders by purpose
- Apply appropriate tags for easier management

## Implementation Examples

### Venue Card Example

\\\jsx
<OptimizedImage
  src={venue.images[0]}
  alt={venue.name}
  width={400}
  height={300}
  className="rounded-t-lg"
/>
\\\

### Hero Image Example

\\\jsx
<OptimizedImage
  src={heroImage}
  alt="HouseGoing Hero"
  width={1920}
  height={1080}
  priority={true}
  placeholder="blur"
  className="w-full"
/>
\\\

## Further Optimization

Consider implementing:

1. Next-gen image formats (WebP, AVIF) for all images
2. Content-aware image cropping
3. Responsive art direction for different screen sizes
