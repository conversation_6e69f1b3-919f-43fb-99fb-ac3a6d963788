import { supabase } from '../lib/supabase-client';
import { runMigrations } from '../lib/database/migrations';
import { createNSWTables, insertDefaultZoningRules } from './create-nsw-tables';

/**
 * Execute the SQL migration to create the user_profiles table
 */
export async function runMigration(): Promise<boolean> {
  try {
    console.log('Running migration to create user_profiles table...');
    const result = await runMigrations(supabase);
    return result.success;
  } catch (error) {
    console.error('Migration failed:', error);
    return false;
  }
}

/**
 * Check if the user_profiles table exists
 */
export async function checkTableExists(): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('user_profiles')
      .select('id', { count: 'exact', head: true })
      .limit(1);

    if (error) {
      console.error('Error checking if table exists:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error checking if table exists:', error);
    return false;
  }
}

/**
 * Initialize the database
 */
export async function initializeDatabase(): Promise<boolean> {
  try {
    // Run migrations to ensure all tables exist
    const migrationResult = await runMigration();

    if (!migrationResult) {
      console.warn('Migrations failed, but continuing with initialization');
    }

    // Check if tables exist
    const tablesExist = await checkTableExists();

    if (!tablesExist) {
      console.warn('Tables do not exist, but continuing with initialization');
    }

    // Create NSW Party Planning tables
    console.log('Creating NSW Party Planning tables...');
    const nswTablesCreated = await createNSWTables();
    if (nswTablesCreated) {
      await insertDefaultZoningRules();
      console.log('✅ NSW Party Planning tables created successfully');
    } else {
      console.warn('Failed to create NSW Party Planning tables');
    }

    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
}
