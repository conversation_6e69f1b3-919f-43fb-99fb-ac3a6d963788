/**
 * Test script for WFS integration
 * 
 * This script tests the WFS integration with NSW Planning Portal services.
 * It fetches zoning and LGA data for a set of test coordinates.
 */

import fetch from 'node-fetch';

// Test coordinates (various locations in NSW)
const TEST_COORDINATES = [
  { name: 'Sydney CBD', lat: -33.8688, lng: 151.2093 },
  { name: 'Parramatta', lat: -33.8150, lng: 151.0011 },
  { name: 'Newcastle', lat: -32.9283, lng: 151.7817 },
  { name: 'Wollongong', lat: -34.4278, lng: 150.8931 },
  { name: 'Byron Bay', lat: -28.6474, lng: 153.6020 }
];

// NSW Planning Portal WFS endpoints
const ZONING_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/Planning/EPI_Primary_Planning_Layers/MapServer/WFSServer';
const LGA_WFS_URL = 'https://mapprod3.environment.nsw.gov.au/arcgis/services/EDP/Administrative_Boundaries/MapServer/WFSServer';

/**
 * Fetch WFS data for a location
 */
async function fetchWFSData(serviceUrl, typeName, lat, lng) {
  try {
    console.log(`Fetching ${typeName} data for coordinates: ${lat}, ${lng}`);
    
    const url = new URL(serviceUrl);
    url.searchParams.append('service', 'WFS');
    url.searchParams.append('version', '1.1.0');
    url.searchParams.append('request', 'GetFeature');
    url.searchParams.append('typeNames', typeName);
    url.searchParams.append('outputFormat', 'application/json');
    url.searchParams.append('CQL_FILTER', `INTERSECTS(Shape, POINT(${lng} ${lat}))`);
    
    const response = await fetch(url.toString(), {
      headers: { 'Accept': 'application/json' }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`WFS request failed (${response.status}): ${errorText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching WFS data from ${serviceUrl}:`, error.message);
    return null;
  }
}

/**
 * Test WFS integration for a location
 */
async function testLocation(location) {
  console.log(`\n=== Testing location: ${location.name} ===`);
  
  try {
    // Fetch zoning data
    const zoningData = await fetchWFSData(
      ZONING_WFS_URL,
      'EPI_Primary_Planning_Layers:2',
      location.lat,
      location.lng
    );
    
    // Fetch LGA data
    const lgaData = await fetchWFSData(
      LGA_WFS_URL,
      'EDP_Administrative_Boundaries:1',
      location.lat,
      location.lng
    );
    
    // Process results
    console.log('\nResults:');
    console.log('--------');
    
    if (zoningData && zoningData.features && zoningData.features.length > 0) {
      const zoneCode = zoningData.features[0].properties.ZONE_CODE || 'Unknown';
      const zoneName = zoningData.features[0].properties.ZONE_NAME || 'Unknown Zone';
      console.log(`Zoning: ${zoneCode} - ${zoneName}`);
    } else {
      console.log('Zoning: No data found');
    }
    
    if (lgaData && lgaData.features && lgaData.features.length > 0) {
      const lgaName = lgaData.features[0].properties.LGA_NAME || 'Unknown Council';
      console.log(`Council: ${lgaName}`);
    } else {
      console.log('Council: No data found');
    }
    
    return {
      location: location.name,
      coordinates: { lat: location.lat, lng: location.lng },
      zoning: zoningData?.features?.[0]?.properties ? {
        code: zoningData.features[0].properties.ZONE_CODE,
        name: zoningData.features[0].properties.ZONE_NAME
      } : null,
      lga: lgaData?.features?.[0]?.properties ? {
        name: lgaData.features[0].properties.LGA_NAME
      } : null
    };
  } catch (error) {
    console.error(`Error testing location ${location.name}:`, error);
    return {
      location: location.name,
      coordinates: { lat: location.lat, lng: location.lng },
      error: error.message
    };
  }
}

/**
 * Run tests for all locations
 */
async function runTests() {
  console.log('Starting WFS integration tests...');
  
  const results = [];
  
  for (const location of TEST_COORDINATES) {
    const result = await testLocation(location);
    results.push(result);
  }
  
  console.log('\n=== Summary ===');
  console.log('---------------');
  
  let successCount = 0;
  
  for (const result of results) {
    const success = result.zoning && result.lga && !result.error;
    console.log(`${result.location}: ${success ? 'SUCCESS' : 'FAILED'}`);
    
    if (success) {
      successCount++;
    }
  }
  
  console.log(`\nOverall: ${successCount}/${results.length} locations successfully tested`);
}

// Run the tests
runTests().catch(error => {
  console.error('Test script error:', error);
  process.exit(1);
});
