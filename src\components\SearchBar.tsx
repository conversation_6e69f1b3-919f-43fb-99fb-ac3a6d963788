import React, { useState } from 'react';
import { Search, Users, Calendar, MapPin, DollarSign } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import LocationSearch from './search/LocationSearch';
import DateRangePicker from './search/DateRangePicker';
import GuestPicker from './search/GuestPicker';
import BudgetSearch from './search/BudgetSearch';
import MobileSearchDropdown from './search/MobileSearchDropdown';
import { simplifyLocationName } from '../services/geocoding';
import { trackSearchEvent } from '../api/analytics';
import { useUserSafe } from '../hooks/useClerkSafe';

interface LocationData {
  lat: number;
  lng: number;
  displayName: string;
}

function SearchBarComponent() {
  const { user } = useUserSafe();
  const navigate = useNavigate();
  const location = useLocation();
  const [locationInput, setLocationInput] = useState('');
  const [locationData, setLocationData] = useState<LocationData | null>(null);
  const [dates, setDates] = useState({ start: '', end: '' });
  const [guests, setGuests] = useState(0);
  const [budget, setBudget] = useState(0);
  const [activeDropdown, setActiveDropdown] = useState<'location' | 'dates' | 'guests' | 'budget' | null>(null);

  const handleDropdownClick = (dropdown: 'location' | 'dates' | 'guests' | 'budget') => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const formatBudget = () => {
    if (budget <= 0) return '';
    return `$${budget}`;
  };

  const formatDateRange = () => {
    if (!dates.start || !dates.end) return '';
    return `${new Date(dates.start).toLocaleDateString()} - ${new Date(dates.end).toLocaleDateString()}`;
  };

  const formatGuests = () => {
    if (guests === 0) return '';
    return `${guests} guests`;
  };

  const handleLocationSelect = (location: LocationData) => {
    setLocationData(location);
    setLocationInput(simplifyLocationName(location.displayName));
    setActiveDropdown(null);
  };

  const handleSearch = async () => {
    // Build query parameters
    const params = new URLSearchParams();

    // Add location parameters if available
    if (locationInput) {
      params.append('location', locationInput);

      if (locationData) {
        params.append('lat', locationData.lat.toString());
        params.append('lng', locationData.lng.toString());
      }
    }

    // Add date parameters if available
    if (dates.start && dates.end) {
      params.append('startDate', dates.start);
      params.append('endDate', dates.end);
    }

    // Add guests parameter if available
    if (guests > 0) {
      params.append('guests', guests.toString());
    }

    // Add budget parameter if available
    if (budget > 0) {
      params.append('budget', budget.toString());
    }

    // Track the search event for analytics
    try {
      const searchQuery = [
        locationInput,
        dates.start && dates.end ? `${dates.start} to ${dates.end}` : '',
        guests > 0 ? `${guests} guests` : '',
        budget > 0 ? `$${budget} budget` : ''
      ].filter(Boolean).join(' ');

      if (searchQuery.trim()) {
        await trackSearchEvent({
          user_id: user?.id,
          search_query: searchQuery,
          search_filters: {
            location: locationInput,
            dates: { start: dates.start, end: dates.end },
            guests,
            budget
          }
        });
      }
    } catch (error) {
      console.error('Failed to track search event:', error);
      // Don't prevent search if tracking fails
    }

    // Navigate to find venues page with query parameters
    // If we're already on the find-venues page, just update the URL parameters
    if (location.pathname === '/find-venues') {
      navigate(`/find-venues?${params.toString()}`, { replace: true });
    } else {
      navigate(`/find-venues?${params.toString()}`);
    }
  };

  return (
    <div className="w-full container-width-sm relative search-bar-container">
      {/* Mobile: Use dropdown search */}
      <div className="block md:hidden">
        <MobileSearchDropdown />
      </div>

      {/* Desktop: Use traditional search bar */}
      <div className="hidden md:block">
        <div className="card p-4 relative z-50 shadow-lg hover:shadow-xl transition-all duration-300" style={{ overflow: 'visible' }}>

        <div className="flex flex-col md:flex-row md:items-center gap-3">
          <div className="flex-1 relative search-dropdown-container">
            <div
              onClick={() => handleDropdownClick('location')}
              className="flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-all duration-200 group location-dropdown-trigger"
            >
              <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <MapPin className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <div className="text-small font-semibold text-gray-900 mb-1">Location</div>
                <input
                  type="text"
                  placeholder="Type your Suburb"
                  value={locationInput}
                  onChange={(e) => setLocationInput(e.target.value)}
                  className="w-full bg-transparent border-none p-0 text-base text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-0"
                />
              </div>
            </div>
            <LocationSearch
              isOpen={activeDropdown === 'location'}
              searchQuery={locationInput}
              onSelect={handleLocationSelect}
            />
          </div>

          <div className="h-px md:h-8 md:w-px bg-gray-200" />

          <div className="flex-1 relative search-dropdown-container">
            <div
              onClick={() => handleDropdownClick('dates')}
              className="flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-all duration-200 group dates-dropdown-trigger"
            >
              <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <Calendar className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <div className="text-small font-semibold text-gray-900 mb-1">Dates</div>
                <div className="text-base text-gray-700">{formatDateRange() || 'Any dates'}</div>
              </div>
            </div>
            <DateRangePicker
              isOpen={activeDropdown === 'dates'}
              onClose={() => setActiveDropdown(null)}
              onSelect={setDates}
            />
          </div>

          <div className="h-px md:h-8 md:w-px bg-gray-200" />

          <div className="flex-1 relative search-dropdown-container">
            <div
              onClick={() => handleDropdownClick('guests')}
              className="flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-all duration-200 group guests-dropdown-trigger"
            >
              <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <Users className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <div className="text-small font-semibold text-gray-900 mb-1">Guests</div>
                <div className="text-base text-gray-700">{formatGuests() || 'Any size'}</div>
              </div>
            </div>
            <GuestPicker
              isOpen={activeDropdown === 'guests'}
              onClose={() => setActiveDropdown(null)}
              guests={guests}
              onGuestsChange={setGuests}
            />
          </div>

          <div className="h-px md:h-8 md:w-px bg-gray-200" />

          <div className="flex-1 relative search-dropdown-container">
            <div
              onClick={() => handleDropdownClick('budget')}
              className="flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-all duration-200 group budget-dropdown-trigger"
            >
              <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <div className="text-small font-semibold text-gray-900 mb-1">Budget</div>
                <div className="text-base text-gray-700">{formatBudget() || 'Any budget'}</div>
              </div>
            </div>
            <BudgetSearch
              isOpen={activeDropdown === 'budget'}
              onClose={() => setActiveDropdown(null)}
              value={budget}
              onChange={setBudget}
            />
          </div>

          <button
            onClick={handleSearch}
            className="btn-primary w-full md:w-auto md:min-w-[140px] px-6 py-3 ml-2 text-base font-semibold"
          >
            <Search className="h-5 w-5" />
            <span className="ml-2">Find Venues</span>
          </button>
        </div>
        </div>
      </div>
    </div>
  );
}

// Export both as default and named export
export const SearchBar = SearchBarComponent;
export default SearchBarComponent;