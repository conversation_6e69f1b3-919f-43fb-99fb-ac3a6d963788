import { StrictMode } from 'react';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import { HelmetProvider } from 'react-helmet-async';
import i18n from './i18n';
// Import both apps - we'll conditionally render based on URL parameter
import App from './App';
import SimpleApp from './SimpleApp';
import './index.css';
import { useSimpleApp } from './config/appConfig';

// Import Clerk
import { ClerkProvider } from '@clerk/clerk-react';

// Import custom HMR client for better WebSocket handling
import './hmr-client.js';

// Get Clerk publishable key from environment variables
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!clerkPubKey) {
  throw new Error('VITE_CLERK_PUBLISHABLE_KEY environment variable is required');
}

// Suppress console warnings in production
if (process.env.NODE_ENV === 'production') {
  const originalWarn = console.warn;
  const originalError = console.error;

  console.warn = (...args) => {
    // Filter out common warnings that don't affect functionality
    const message = args.join(' ');
    if (
      message.includes('Tailwind') ||
      message.includes('CDN') ||
      message.includes('deprecated') ||
      message.includes('Warning:')
    ) {
      return; // Suppress these warnings
    }
    originalWarn.apply(console, args);
  };

  console.error = (...args) => {
    // Only log critical errors in production
    const message = args.join(' ');
    if (
      message.includes('Network Error') ||
      message.includes('Failed to fetch') ||
      message.includes('Authentication')
    ) {
      originalError.apply(console, args);
    }
  };
}

// Global error handler
window.addEventListener('error', (event) => {
  if (process.env.NODE_ENV === 'development') {
    console.error('Caught global error:', event.error);
  }
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  if (process.env.NODE_ENV === 'development') {
    console.error('Unhandled promise rejection:', event.reason);
  }
});

// Error boundary component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by error boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
          <p className="mb-4">We're sorry, but there was an error loading the application.</p>
          <button
            onClick={() => window.location.href = '/'}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Go Home
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Error boundary function to catch rendering errors
function renderWithErrorHandling() {
  try {
    const root = createRoot(document.getElementById('root')!);
    root.render(
      <StrictMode>
        <ErrorBoundary>
          <HelmetProvider>
            <ClerkProvider publishableKey={clerkPubKey}>
              <I18nextProvider i18n={i18n}>
                {useSimpleApp ? <SimpleApp /> : <App />}
              </I18nextProvider>
            </ClerkProvider>
          </HelmetProvider>
        </ErrorBoundary>
      </StrictMode>
    );
    console.log('React app rendered successfully with Clerk');
    
    // Dispatch app initialization event
    document.dispatchEvent(new CustomEvent('app-initialized', {
      detail: { success: true, provider: 'clerk' }
    }));
  } catch (error: any) {
    console.error('Failed to render React app:', error, error.stack);
    // Show fallback content
    const fallbackElement = document.getElementById('fallback');
    if (fallbackElement) {
      fallbackElement.style.display = 'block';
    }
    // Hide loading indicator
    const loadingIndicator = document.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }
  }
}

// Execute with a small delay to ensure DOM is ready
setTimeout(renderWithErrorHandling, 0);
