import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Sparkles, Info, HelpCircle } from 'lucide-react';

interface HomeSmartSearchProps {
  className?: string;
}

export default function HomeSmartSearch({ className = '' }: HomeSmartSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showTooltip, setShowTooltip] = useState(false);
  const navigate = useNavigate();

  const handleSearch = () => {
    if (!searchQuery.trim()) return;

    console.log('🏠 HOMEPAGE: Navigating to find venues with smart search query:', searchQuery.trim());

    // Always navigate to find venues page with smart search query
    // The SmartVenueSearch component will handle showing results or "no results found"
    navigate('/find-venues', {
      state: {
        smartSearch: searchQuery.trim()
      }
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const tooltipContent = (
    <div className="bg-white border border-gray-200 shadow-xl rounded-lg p-3 w-96 text-sm">
      <h4 className="font-semibold mb-2 text-gray-900 text-center">Smart Search Examples</h4>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <p className="font-medium text-purple-700 mb-1 text-xs">Event & Capacity:</p>
          <div className="space-y-0.5">
            <p className="text-gray-600 text-xs">• "Ryde birthday 30 people"</p>
            <p className="text-gray-600 text-xs">• "Wedding 120 guests"</p>
          </div>
        </div>

        <div>
          <p className="font-medium text-purple-700 mb-1 text-xs">Location & Features:</p>
          <div className="space-y-0.5">
            <p className="text-gray-600 text-xs">• "Harbour views"</p>
            <p className="text-gray-600 text-xs">• "Budget under $200"</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`w-full container-width-sm relative ${className}`}>
      <div className="card p-4 relative shadow-md hover:shadow-lg transition-all duration-300">
        <div className="flex items-center space-x-3 mb-3">
          <div className="bg-purple-100 p-2 rounded-lg">
            <Sparkles className="h-5 w-5 text-purple-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-base font-semibold text-gray-900">Smart Venue Search</h3>
            <p className="text-xs text-gray-600">Describe what you're looking for in natural language</p>
          </div>
          <div className="relative">
            <button
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              onClick={() => setShowTooltip(!showTooltip)}
              className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
              aria-label="How to use smart search"
            >
              <HelpCircle className="h-5 w-5" />
            </button>

            {showTooltip && (
              <div className="absolute right-0 top-10 z-50">
                <div className="relative">
                  <div className="absolute top-0 right-4 w-3 h-3 bg-white border-l border-t border-gray-200 transform rotate-45 -translate-y-1.5"></div>
                  {tooltipContent}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., 'Ryde birthday party 30 people'"
              className="w-full pl-9 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base min-h-[44px] focus-enhanced"
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={!searchQuery.trim()}
            className="btn btn-reactive px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center text-base font-medium min-h-[44px] touch-target focus-enhanced"
          >
            <Search className="h-4 w-4" />
            <span className="ml-1 hidden sm:inline">Search</span>
          </button>
        </div>
      </div>
    </div>
  );
}
