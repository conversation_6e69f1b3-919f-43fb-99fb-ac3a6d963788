/**
 * Official Clerk-Supabase Native Integration
 * Based on Clerk's current recommended approach (2025)
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

// Environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

/**
 * Create a Supabase client with Clerk authentication
 * Uses Clerk's native integration without JWT templates
 */
export async function createClerkSupabaseClient(session: any): Promise<SupabaseClient<Database>> {
  console.log('Creating Supabase client with Clerk session...');
  
  if (!session) {
    console.warn('No Clerk session provided, using anonymous client');
    return createClient<Database>(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false
      }
    });
  }
  
  try {
    // For the native integration, we just need the auth token from the session
    console.log('Getting token from Clerk session...');
    const token = await session.getToken();
    
    if (!token) {
      console.warn('No token available from Clerk session, using anonymous client');
      return createClient<Database>(supabaseUrl, supabaseKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
          detectSessionInUrl: false
        }
      });
    }
    
    console.log('Got Clerk token, creating authenticated Supabase client...');
    
    // Create client with Authorization header already set
    const client = createClient<Database>(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: true,
        detectSessionInUrl: false
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
          apikey: supabaseKey
        }
      }
    });
    
    // Also explicitly set session for compatibility
    try {
      await client.auth.setSession({
        access_token: token,
        refresh_token: ''
      });
      console.log('Supabase auth session set successfully');
    } catch (sessionError) {
      console.warn('Error setting Supabase session (continuing anyway):', sessionError);
      // Non-fatal error, continue with the client that has the Authorization header
    }
    
    // Verify the authentication worked - use the user_profiles table with clerk_id
    try {
      // Query the user_profiles table which is synced with Clerk IDs
      const { data: testData, error: testError } = await client
        .from('user_profiles')
        .select('*')
        .limit(1);
        
      if (testError) {
        console.warn('Authentication test query failed:', testError);
        // Log more details about the error to help diagnose the issue
        if (testError.code === '22P02' && testError.message.includes('invalid input syntax for type uuid')) {
          console.warn('UUID format error detected. This may indicate a mismatch between UUID and Clerk ID formats.');
        }
        console.log('Error details:', testError);
      } else {
        console.log('Supabase client authenticated successfully, found records:', testData);
      }
    } catch (testErr) {
      console.error('Error running test query:', testErr);
    }
    
    return client;
  } catch (error) {
    console.error('Error in createClerkSupabaseClient:', error);
    // Fall back to anonymous client
    return createClient<Database>(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false
      }
    });
  }
}

/**
 * Server-side Supabase client (for API routes and server components)
 * Uses the official native integration pattern
 */
export function createServerSupabaseClient(getToken: () => Promise<string | null>) {
  return createClient<Database>(
    supabaseUrl, 
    supabaseKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

/**
 * Utility to attach a Clerk token to a Supabase client for server-side use
 */
export async function attachClerkTokenToClient(
  client: SupabaseClient<Database>,
  token: string | null
): Promise<SupabaseClient<Database>> {
  if (!token) {
    return client;
  }
  
  try {
    await client.auth.setSession({
      access_token: token,
      refresh_token: ''
    });
    
    // Update the client's global headers
    // @ts-ignore - accessing internal property
    client.supabaseUrl = client.supabaseUrl || {};
    // @ts-ignore - accessing internal property
    client.headers = client.headers || {};
    // @ts-ignore - accessing internal property
    client.headers.Authorization = `Bearer ${token}`;
  } catch (error) {
    console.error('Error attaching Clerk token to Supabase client:', error);
  }
  
  return client;
}
