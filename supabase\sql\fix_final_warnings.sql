-- Fix Final Security Warnings for HouseGoing
-- This script fixes the last 3 remaining security warnings
-- Run this in the Supabase SQL Editor

-- Fix 1: Last function search path warning
ALTER FUNCTION public.http_detect_property_type SET search_path = public;

-- Fix 2: Auth OTP Long Expiry - Reduce OTP expiry time
-- Check if we can update auth settings via SQL
DO $$
BEGIN
  -- Try to update auth settings if the table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'config') THEN
    -- Set OTP expiry to 10 minutes (600 seconds) instead of default
    UPDATE auth.config SET 
      otp_expiry = 600,
      password_reset_expiry = 3600,
      email_confirmation_expiry = 86400
    WHERE true;
    RAISE NOTICE 'Auth OTP expiry reduced to 10 minutes';
  ELSE
    RAISE NOTICE 'Auth config table not found - will need to configure via dashboard';
  END IF;
END;
$$;

-- Fix 3: Enable Leaked Password Protection
-- This needs to be done via Supabase dashboard, but we can try via SQL
DO $$
BEGIN
  -- Try to enable password breach detection if possible
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'config') THEN
    UPDATE auth.config SET 
      enable_password_breach_detection = true,
      password_min_length = 8
    WHERE true;
    RAISE NOTICE 'Leaked password protection enabled';
  ELSE
    RAISE NOTICE 'Password protection needs to be enabled via Supabase dashboard';
  END IF;
END;
$$;

-- Alternative: Create our own password validation function
CREATE OR REPLACE FUNCTION public.validate_password_strength(
  p_password TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  result JSONB;
  score INTEGER := 0;
  feedback TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Initialize result
  result := jsonb_build_object('valid', false, 'score', 0, 'feedback', '[]'::jsonb);
  
  -- Check minimum length
  IF length(p_password) >= 8 THEN
    score := score + 1;
  ELSE
    feedback := array_append(feedback, 'Password must be at least 8 characters long');
  END IF;
  
  -- Check for uppercase letters
  IF p_password ~ '[A-Z]' THEN
    score := score + 1;
  ELSE
    feedback := array_append(feedback, 'Password should contain uppercase letters');
  END IF;
  
  -- Check for lowercase letters
  IF p_password ~ '[a-z]' THEN
    score := score + 1;
  ELSE
    feedback := array_append(feedback, 'Password should contain lowercase letters');
  END IF;
  
  -- Check for numbers
  IF p_password ~ '[0-9]' THEN
    score := score + 1;
  ELSE
    feedback := array_append(feedback, 'Password should contain numbers');
  END IF;
  
  -- Check for special characters
  IF p_password ~ '[^A-Za-z0-9]' THEN
    score := score + 1;
  ELSE
    feedback := array_append(feedback, 'Password should contain special characters');
  END IF;
  
  -- Check for common weak passwords
  IF lower(p_password) = ANY(ARRAY['password', '123456', 'qwerty', 'admin', 'letmein']) THEN
    score := 0;
    feedback := array_append(feedback, 'Password is too common and easily guessed');
  END IF;
  
  -- Build result
  result := jsonb_build_object(
    'valid', score >= 3,
    'score', score,
    'feedback', to_jsonb(feedback)
  );
  
  RETURN result;
END;
$function$;

-- Grant permission for password validation
GRANT EXECUTE ON FUNCTION public.validate_password_strength TO authenticated, anon;

-- Success message
SELECT 'Final security warnings addressed!' as result;
