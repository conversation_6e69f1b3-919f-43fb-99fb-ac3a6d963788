import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { SupabaseClient } from '@supabase/supabase-js';
import { UserRole } from '../services/auth';
import { initializeDatabase } from '../utils/run-migration';
import { getSupabaseClient, getClerkSupabaseClient } from '../lib/supabase-client'; // Use centralized client

// User profile interface
interface UserProfile {
  clerk_id: string; // Clerk user ID
  email: string;
  role: UserRole;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  bio?: string;
  phone?: string;
  is_host: boolean;
  created_at?: string;
  updated_at?: string;
}

interface SupabaseContextType {
  supabase: SupabaseClient;
  userProfile: UserProfile | null;
  isLoading: boolean;
  isHost: boolean;
  isAuthenticated: boolean;
  setUserRole: (role: UserRole) => Promise<boolean>;
  refreshUserProfile: () => Promise<void>;
  signOut: () => Promise<void>;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
};

interface SupabaseProviderProps {
  children: React.ReactNode;
}

export function SupabaseProvider({ children }: SupabaseProviderProps) {
  const { isSignedIn } = useAuth();
  const { user: clerkUser } = useUser();
  const [supabase, setSupabase] = useState(() => getSupabaseClient());
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const getOrCreateUserProfile = useCallback(async (client: SupabaseClient, clerkUser: any) => {
    console.log('Getting or creating user profile for Clerk ID:', clerkUser.id);
    setIsLoading(true);
    try {
      // First, try to fetch the existing profile
      let { data: profile, error: fetchError } = await client
        .from('user_profiles')
        .select('*')
        .eq('clerk_id', clerkUser.id)
        .single();

      // If the profile exists, we're done
      if (profile) {
        console.log('User profile found:', profile);
        setUserProfile(profile as UserProfile);
        setIsHost(profile.role === 'host' || profile.is_host);
        setIsAuthenticated(true);

        // Clear any lingering host registration flags
        localStorage.removeItem('registering_as_host');
        localStorage.removeItem('user_type');
        localStorage.removeItem('auth_user_type');
        return;
      }

      // If no profile, and the error indicates that, let's create one
      if (fetchError && fetchError.code === 'PGRST116') { // PGRST116: "exact-cardinality-violation" means 0 rows found
        console.log('No profile found, creating a new one...');

        // Check if user is registering as a host
        const isHostRegistration = localStorage.getItem('registering_as_host') === 'true' ||
                                   localStorage.getItem('user_type') === 'host' ||
                                   localStorage.getItem('auth_user_type') === 'host';

        console.log('Host registration detected:', isHostRegistration);

        const { data: newUser, error: insertError } = await client
          .from('user_profiles')
          .insert({
            clerk_id: clerkUser.id,
            email: clerkUser.primaryEmailAddress?.emailAddress,
            first_name: clerkUser.firstName || '',
            last_name: clerkUser.lastName || '',
            role: isHostRegistration ? 'host' : 'customer',
            is_host: isHostRegistration,
          })
          .select()
          .single();

        if (insertError) {
          console.error('Error creating user profile:', insertError);
          throw insertError;
        }

        console.log('New user profile created:', newUser);
        setUserProfile(newUser as UserProfile);
        setIsHost(newUser.is_host || newUser.role === 'host');
        setIsAuthenticated(true);

        // Clear the host registration flags after successful creation
        if (newUser.is_host) {
          localStorage.removeItem('registering_as_host');
          localStorage.removeItem('user_type');
          localStorage.removeItem('auth_user_type');
          console.log('Host registration completed, cleared localStorage flags');
        }
      } else if (fetchError) {
        // For any other errors, log it and fail
        console.error('Error fetching user profile:', fetchError);
        throw fetchError;
      }

    } catch (e) {
      console.error('Error in getOrCreateUserProfile:', e);
      setUserProfile(null);
      setIsHost(false);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    let isMounted = true;
    const manageSession = async () => {
      try {
        if (isSignedIn && clerkUser) {
          console.log('User is signed in. Initializing authenticated session...');
          setIsLoading(true);

          // Always use the centralized client instance
          const clientToUse = await getClerkSupabaseClient();
          
          if (isMounted) {
            setSupabase(clientToUse);
            await getOrCreateUserProfile(clientToUse, clerkUser);
          }
        } else {
          console.log('User is not signed in. Using anonymous Supabase client.');
          if (isMounted) {
            setSupabase(getSupabaseClient());
            setUserProfile(null);
            setIsHost(false);
            setIsAuthenticated(false);
          }
        }
      } catch (error) {
        console.error('Error managing session:', error);
        if (isMounted) {
          setSupabase(getSupabaseClient());
          setUserProfile(null);
          setIsHost(false);
          setIsAuthenticated(false);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    manageSession();

    // Only initialize database once
    // @ts-ignore
    if (!window.__DATABASE_INITIALIZED) {
      // @ts-ignore
      window.__DATABASE_INITIALIZED = true;
      initializeDatabase().catch(console.error);
    }

    return () => {
      isMounted = false;
    };
  }, [isSignedIn, clerkUser?.id]); // Only re-run if clerkUser.id changes

  const setUserRole = async (role: UserRole): Promise<boolean> => {
    if (!userProfile || !clerkUser) {
      console.error('Cannot set user role: no user profile or Clerk user.');
      return false;
    }

    console.log(`Setting role to '${role}' for user:`, clerkUser.id);
    const { error } = await supabase
      .from('user_profiles')
      .update({ role })
      .eq('clerk_id', clerkUser.id);

    if (error) {
      console.error('Error updating user role:', error);
      return false;
    }

    await refreshUserProfile();
    return true;
  };

  const refreshUserProfile = useCallback(async () => {
    if (clerkUser) {
      console.log('Refreshing user profile...');
      await getOrCreateUserProfile(supabase, clerkUser);
    }
  }, [clerkUser, getOrCreateUserProfile, supabase]);

  const signOut = async () => {
    // The session is managed by Clerk, so we just need to clear local state.
    console.log('Signing out from Supabase context.');
    const newClient = getSupabaseClient();
    setSupabase(newClient);
    setUserProfile(null);
    setIsHost(false);
    setIsAuthenticated(false);
  };

  const value = {
    supabase,
    userProfile,
    isLoading,
    isHost,
    isAuthenticated,
    setUserRole,
    refreshUserProfile,
    signOut,
  };

  return <SupabaseContext.Provider value={value}>{children}</SupabaseContext.Provider>;
}
