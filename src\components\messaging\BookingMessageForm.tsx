import React, { useState, useEffect } from 'react';
import { Send } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { getSupabaseClient } from '../../services/api';
import { sendMessageNotification } from '../../api/notifications';

interface BookingMessageFormProps {
  bookingId: string;
  hostId: string;
}

export default function BookingMessageForm({ bookingId, hostId }: BookingMessageFormProps) {
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [notification, setNotification] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const { user } = useAuth();

  const [hostEmail, setHostEmail] = useState<string>('');

  // Fetch host email when component mounts
  useEffect(() => {
    const fetchHostEmail = async () => {
      try {
        const supabase = getSupabaseClient();
        const { data, error } = await supabase
          .from('user_profiles')
          .select('email')
          .eq('user_id', hostId)
          .single();

        if (error) throw error;

        if (data?.email) {
          setHostEmail(data.email);
        } else {
          // Fallback email if not found
          setHostEmail('<EMAIL>');
        }
      } catch (error) {
        console.error('Error fetching host email:', error);
        // Fallback email
        setHostEmail('<EMAIL>');
      }
    };

    fetchHostEmail();
  }, [hostId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) return;
    if (!user) {
      setNotification({ type: 'error', message: 'You need to be signed in to send messages' });
      return;
    }

    setIsSending(true);

    try {
      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      // Create the message
      const { data, error } = await supabase
        .from('messages')
        .insert({
          booking_id: bookingId,
          sender_id: user.id,
          recipient_id: hostId,
          content: message,
          is_read: false,
          created_at: new Date().toISOString()
        })
        .select();

      if (error) throw error;

      // Send email notification
      if (data && data.length > 0) {
        const senderName = user.firstName || user.lastName || user.email || 'Guest';
        await sendMessageNotification(
          hostEmail,
          senderName,
          message,
          bookingId
        );
      }

      setNotification({ type: 'success', message: 'Message sent successfully!' });
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      setNotification({ type: 'error', message: 'Failed to send message. Please try again.' });
    } finally {
      setIsSending(false);
    }
  };

  // Clear notification after 5 seconds
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [notification]);

  return (
    <div>
      {notification && (
        <div
          className={`mb-4 p-3 rounded-md ${
            notification.type === 'success'
              ? 'bg-green-50 text-green-800 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}
        >
          {notification.message}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your message to the host here..."
          className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 min-h-[120px]"
          required
        />

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSending || !message.trim()}
            className={`px-4 py-2 rounded-md flex items-center ${
              isSending || !message.trim()
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
            }`}
          >
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Sending...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Send Message
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
