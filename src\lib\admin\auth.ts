// Mock supabase client for development
const supabase = {
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () => Promise.resolve({ data: null, error: null })
      })
    }),
    update: () => ({
      eq: () => Promise.resolve({ error: null })
    })
  })
};

// Admin roles
export enum AdminRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  SUPPORT = 'support'
}

// Admin user interface
export interface AdminUser {
  id: string;
  email: string;
  role: AdminRole;
  name: string;
  lastActive: Date;
}

// Check if a user is an admin
export async function isAdmin(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('role')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error checking admin status:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Get admin details
export async function getAdminDetails(userId: string): Promise<AdminUser | null> {
  try {
    // In development mode, return mock admin data
    console.log('Using mock admin data for development');
    return {
      id: userId,
      email: '<EMAIL>',
      role: AdminRole.ADMIN,
      name: 'Admin User',
      lastActive: new Date()
    };
  } catch (error) {
    console.error('Error fetching admin details:', error);
    return null;
  }
}

// Update admin last active timestamp
export async function updateAdminActivity(userId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('admin_users')
      .update({ last_active: new Date().toISOString() })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating admin activity:', error);
    }
  } catch (error) {
    console.error('Error updating admin activity:', error);
  }
}

// For development/testing purposes - mock admin check
export function isMockAdmin(email: string): boolean {
  const adminEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>' // Owner email from memories
  ];

  return adminEmails.includes(email.toLowerCase());
}
