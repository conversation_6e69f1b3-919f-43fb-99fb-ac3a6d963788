-- Fix RLS policies for Clerk user IDs
-- First, drop the existing RLS policies that rely on auth.uid()
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;

-- Create new policies that rely on the clerk_user_id column instead
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (true);  -- Allow all authenticated users to select

CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  USING (clerk_user_id = (SELECT current_setting('request.jwt.claims', true)::json->>'sub'));

CREATE POLICY "Users can insert their own profile"
  ON public.user_profiles
  FOR INSERT
  WITH CHECK (clerk_user_id = (SELECT current_setting('request.jwt.claims', true)::json->>'sub'));

-- Ensure the clerk_user_id column exists (if not, add it)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles'
    AND column_name = 'clerk_user_id'
  ) THEN
    ALTER TABLE public.user_profiles ADD COLUMN clerk_user_id TEXT;
    CREATE INDEX idx_user_profiles_clerk_user_id ON public.user_profiles(clerk_user_id);
  END IF;
END
$$;

-- Create function to update Supabase auth schema for Clerk integration
CREATE OR REPLACE FUNCTION update_auth_for_clerk()
RETURNS TEXT AS $$
DECLARE
  result TEXT;
BEGIN
  -- Ensure proper RLS is configured
  EXECUTE 'ALTER TABLE IF EXISTS public.user_profiles ENABLE ROW LEVEL SECURITY';
  
  -- Return success message
  result := 'Auth schema updated for Clerk integration';
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Run the update function
SELECT update_auth_for_clerk();
