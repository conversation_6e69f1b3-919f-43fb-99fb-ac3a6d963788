import React from 'react';
import { Star, Users, MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';
import { mockVenues } from '../data/mockVenues';

// Select the top 3 most popular venues from our mock data
const getFeaturedVenues = () => {
  return mockVenues
    .filter(venue => venue.verified && venue.partyScore >= 8.5) // Only verified venues with high party scores
    .sort((a, b) => b.partyScore - a.partyScore) // Sort by party score descending
    .slice(0, 3) // Take top 3
    .map(venue => ({
      id: venue.id,
      title: venue.title,
      location: `${venue.location.suburb}, NSW`,
      image: venue.images[0] || 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?auto=format&fit=crop&w=800',
      price: venue.pricing.hourlyRate,
      rating: venue.host.rating,
      reviews: venue.host.reviewCount,
      capacity: venue.capacity.recommended,
      partyScore: venue.partyScore,
      instantBook: venue.instantBook
    }));
};

export default function FeaturedVenues() {
  const featuredVenues = getFeaturedVenues();

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {featuredVenues.map((venue) => (
        <Link
          key={venue.id}
          to={`/venue/${venue.id}`}
          className="group"
        >
          <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group">
            <div className="relative h-48">
              <img
                src={venue.image}
                alt={venue.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-semibold shadow-sm">
                <Users className="h-3 w-3 inline mr-1" />
                {venue.capacity}
              </div>
              {venue.instantBook && (
                <div className="absolute top-4 left-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  Instant Book
                </div>
              )}
              <div className="absolute bottom-4 left-4 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                Party Score: {venue.partyScore.toFixed(1)}
              </div>
            </div>

            <div className="p-5">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">{venue.title}</h3>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium text-gray-600">{venue.rating.toFixed(1)}</span>
                </div>
              </div>

              <div className="flex items-center text-gray-600 mb-4">
                <MapPin className="h-4 w-4 mr-1" />
                <span className="text-sm">{venue.location}</span>
              </div>

              <div className="flex justify-between items-center">
                <p className="text-purple-600 font-semibold">
                  {formatPrice(venue.price)} <span className="text-gray-500 font-normal">/ hour</span>
                </p>
                <span className="text-sm text-gray-500">{venue.reviews} reviews</span>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}