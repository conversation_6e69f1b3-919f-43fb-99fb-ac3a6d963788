import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Camera, ArrowRight, Star, MapPin } from 'lucide-react';

// Gallery data
const galleryData: Record<string, {
  name: string;
  description: string;
  seoTitle: string;
  seoDescription: string;
}> = {
  '': {
    name: 'Venue Gallery',
    description: 'Browse our collection of stunning venues across NSW',
    seoTitle: 'Venue Gallery | HouseGoing Venue Photos & Inspiration',
    seoDescription: 'Browse our gallery of stunning venues across NSW. Get inspired by beautiful event spaces, party venues & function rooms for your next celebration.'
  },
  'featured-venues': {
    name: 'Featured Venues',
    description: 'Our handpicked selection of premium venues',
    seoTitle: 'Featured Venues Gallery | Premium Event Spaces NSW',
    seoDescription: 'Discover our featured venues - premium event spaces handpicked for their exceptional quality, amenities & stunning locations across NSW.'
  },
  'sydney-venues': {
    name: 'Sydney Venues',
    description: 'Beautiful venues across Sydney and surrounds',
    seoTitle: 'Sydney Venue Gallery | Event Spaces & Party Venues Sydney',
    seoDescription: 'Browse Sydney venues in our gallery. Stunning event spaces, party venues & function rooms across Sydney CBD, Inner West, Eastern Suburbs & more.'
  },
  'event-inspiration': {
    name: 'Event Inspiration',
    description: 'Get inspired by real events at our venues',
    seoTitle: 'Event Inspiration Gallery | Real Events & Party Ideas NSW',
    seoDescription: 'Get inspired by real events at our venues. Browse party ideas, wedding celebrations, corporate events & celebrations for inspiration.'
  },
  'videos': {
    name: 'Venue Videos',
    description: 'Virtual tours and video showcases of our venues',
    seoTitle: 'Venue Videos | Virtual Tours & Video Showcases NSW',
    seoDescription: 'Watch venue videos and virtual tours of event spaces across NSW. Get a real feel for our venues before booking your celebration.'
  }
};

// Sample venue data for gallery
const sampleVenues = [
  {
    id: 1,
    name: 'Harbour View Terrace',
    location: 'Sydney Harbour',
    image: '/images/venues/harbour-terrace.jpg',
    rating: 4.8,
    price: '$500/hour',
    category: 'Waterfront'
  },
  {
    id: 2,
    name: 'Garden Pavilion',
    location: 'Inner West',
    image: '/images/venues/garden-pavilion.jpg',
    rating: 4.7,
    price: '$300/hour',
    category: 'Garden'
  },
  {
    id: 3,
    name: 'Rooftop Lounge',
    location: 'Sydney CBD',
    image: '/images/venues/rooftop-lounge.jpg',
    rating: 4.9,
    price: '$600/hour',
    category: 'Rooftop'
  },
  {
    id: 4,
    name: 'Beachside Function Room',
    location: 'Bondi',
    image: '/images/venues/beachside-function.jpg',
    rating: 4.6,
    price: '$400/hour',
    category: 'Beachside'
  },
  {
    id: 5,
    name: 'Historic Ballroom',
    location: 'The Rocks',
    image: '/images/venues/historic-ballroom.jpg',
    rating: 4.8,
    price: '$800/hour',
    category: 'Historic'
  },
  {
    id: 6,
    name: 'Modern Event Space',
    location: 'Parramatta',
    image: '/images/venues/modern-space.jpg',
    rating: 4.5,
    price: '$350/hour',
    category: 'Modern'
  }
];

export default function GalleryPage() {
  const { galleryType } = useParams<{ galleryType?: string }>();
  const galleryInfo = galleryData[galleryType || ''];

  if (!galleryInfo) {
    return (
      <div className="pt-32 px-4 text-center">
        <h1 className="text-2xl font-bold mb-4">Gallery Not Found</h1>
        <Link to="/gallery" className="text-purple-600 hover:text-purple-700">
          Browse All Galleries
        </Link>
      </div>
    );
  }

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'ImageGallery',
    name: galleryInfo.name,
    description: galleryInfo.description,
    url: `https://housegoing.com.au/gallery${galleryType ? `/${galleryType}` : ''}`,
    provider: {
      '@type': 'Organization',
      name: 'HouseGoing',
      url: 'https://housegoing.com.au'
    }
  };

  return (
    <>
      <Helmet>
        <title>{galleryInfo.seoTitle}</title>
        <meta name="description" content={galleryInfo.seoDescription} />
        <meta property="og:title" content={galleryInfo.seoTitle} />
        <meta property="og:description" content={galleryInfo.seoDescription} />
        <meta property="og:url" content={`https://housegoing.com.au/gallery${galleryType ? `/${galleryType}` : ''}`} />
        <link rel="canonical" href={`https://housegoing.com.au/gallery${galleryType ? `/${galleryType}` : ''}`} />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      <div className="pt-20 pb-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <Camera className="h-16 w-16 mx-auto mb-4" />
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {galleryInfo.name}
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {galleryInfo.description}
              </p>
            </div>
          </div>
        </div>

        {/* Gallery Navigation */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8 overflow-x-auto py-4">
              <Link
                to="/gallery"
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  !galleryType
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                All Venues
              </Link>
              <Link
                to="/gallery/featured-venues"
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  galleryType === 'featured-venues'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Featured
              </Link>
              <Link
                to="/gallery/sydney-venues"
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  galleryType === 'sydney-venues'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Sydney
              </Link>
              <Link
                to="/gallery/event-inspiration"
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  galleryType === 'event-inspiration'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Inspiration
              </Link>
              <Link
                to="/gallery/videos"
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  galleryType === 'videos'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Videos
              </Link>
            </div>
          </div>
        </div>

        {/* Gallery Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sampleVenues.map((venue) => (
              <div key={venue.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                  <img
                    src={venue.image}
                    alt={venue.name}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder.svg';
                    }}
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-purple-600 font-medium">{venue.category}</span>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="ml-1 text-sm text-gray-600">{venue.rating}</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{venue.name}</h3>
                  <div className="flex items-center text-gray-600 mb-4">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span className="text-sm">{venue.location}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-purple-600">{venue.price}</span>
                    <Link
                      to={`/venue/${venue.id}`}
                      className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium"
                    >
                      View Details
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Book Your Perfect Venue?
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Browse our full collection of venues and find the perfect space for your next event.
            </p>
            <Link
              to="/find-venues"
              className="inline-flex items-center px-8 py-4 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors text-lg"
            >
              Browse All Venues
              <ArrowRight className="ml-2 h-6 w-6" />
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
