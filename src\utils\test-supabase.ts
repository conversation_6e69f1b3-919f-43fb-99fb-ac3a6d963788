import { getSupabaseClient } from '../lib/supabase-client';
import { setUserRole, getUserProfile } from '../lib/supabase';

/**
 * Test the Supabase connection and user profile operations
 */
export async function testSupabaseConnection(): Promise<boolean> {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Test the connection
    const { data, error } = await supabase.from('user_profiles').select('count').single();

    if (error) {
      console.error('Supabase connection test failed:', error);
      return false;
    }

    console.log('Supabase connection test successful:', data);
    return true;
  } catch (error) {
    console.error('Supabase connection test error:', error);
    return false;
  }
}

/**
 * Test creating a user profile
 */
export async function testCreateUserProfile(clerkId: string, email: string): Promise<boolean> {
  try {
    // Create a test user profile
    const success = await setUserRole(clerkId, email, 'host');

    if (!success) {
      console.error('Failed to create test user profile');
      return false;
    }

    // Verify the user profile was created
    const profile = await getUserProfile(clerkId);

    if (!profile) {
      console.error('Failed to retrieve test user profile');
      return false;
    }

    console.log('Test user profile created successfully:', profile);
    return true;
  } catch (error) {
    console.error('Test user profile creation error:', error);
    return false;
  }
}

/**
 * Run all Supabase tests
 */
export async function runSupabaseTests(clerkId: string, email: string): Promise<boolean> {
  console.log('Running Supabase tests...');

  // Test connection
  const connectionSuccess = await testSupabaseConnection();
  if (!connectionSuccess) {
    console.error('Supabase connection test failed');
    return false;
  }

  // Test user profile operations
  const profileSuccess = await testCreateUserProfile(clerkId, email);
  if (!profileSuccess) {
    console.error('Supabase user profile test failed');
    return false;
  }

  console.log('All Supabase tests passed!');
  return true;
}
