import { useState, useEffect } from 'react';
import { useAuth } from '../providers/AuthProvider';
import { getSupabaseClient } from '../lib/supabase-client';

export interface Message {
  id: string;
  content: string;
  sender_id: string;
  recipient_id: string;
  booking_id?: string;
  is_read: boolean;
  created_at: string;
}

interface UseMessagesProps {
  conversationId?: string;
  otherUserId?: string;
}

export function useMessages({ conversationId, otherUserId }: UseMessagesProps = {}) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Function to fetch messages
  const fetchMessages = async () => {
    if (!user) {
      setError('User not authenticated');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      let query;

      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      if (conversationId) {
        // If we have a conversation ID (booking ID), fetch messages for that conversation
        query = supabase
          .from('messages')
          .select('*')
          .eq('booking_id', conversationId)
          .order('created_at', { ascending: true });
      } else if (otherUserId) {
        // If we have another user's ID, fetch direct messages between the current user and that user
        query = supabase
          .from('messages')
          .select('*')
          .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
          .or(`sender_id.eq.${otherUserId},recipient_id.eq.${otherUserId}`)
          .order('created_at', { ascending: true });
      } else {
        // If we don't have either, return an empty array
        setMessages([]);
        setIsLoading(false);
        return;
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // Mark messages as read if they were sent to the current user
      const unreadMessages = data.filter(
        (msg: Message) => !msg.is_read && msg.recipient_id === user.id
      );

      if (unreadMessages.length > 0) {
        await Promise.all(
          unreadMessages.map((msg: Message) =>
            getSupabaseClient()
              .from('messages')
              .update({ is_read: true })
              .eq('id', msg.id)
          )
        );
      }

      setMessages(data || []);
    } catch (err: any) {
      console.error('Error fetching messages:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to send a message
  const sendMessage = async (content: string) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    if (!content.trim()) {
      return;
    }

    if (!conversationId && !otherUserId) {
      setError('No recipient specified');
      return;
    }

    try {
      const newMessage = {
        sender_id: user.id,
        recipient_id: otherUserId,
        booking_id: conversationId,
        content,
        is_read: false,
      };

      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      const { data, error } = await supabase
        .from('messages')
        .insert(newMessage)
        .select();

      if (error) {
        throw error;
      }

      // Add the new message to the state
      if (data && data.length > 0) {
        setMessages((prevMessages) => [...prevMessages, data[0]]);
      }
    } catch (err: any) {
      console.error('Error sending message:', err);
      setError(err.message);
    }
  };

  // Set up real-time subscription
  useEffect(() => {
    if (!user) return;

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Subscribe to new messages
    const subscription = supabase
      .channel('messages-channel')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: conversationId
            ? `booking_id=eq.${conversationId}`
            : `recipient_id=eq.${user.id}`,
        },
        (payload) => {
          // Add the new message to the state if it's relevant to this conversation
          const newMessage = payload.new as Message;

          if (
            (conversationId && newMessage.booking_id === conversationId) ||
            (otherUserId &&
              ((newMessage.sender_id === user.id && newMessage.recipient_id === otherUserId) ||
               (newMessage.sender_id === otherUserId && newMessage.recipient_id === user.id)))
          ) {
            setMessages((prevMessages) => [...prevMessages, newMessage]);

            // Mark the message as read if it was sent to the current user
            if (newMessage.recipient_id === user.id) {
              getSupabaseClient()
                .from('messages')
                .update({ is_read: true })
                .eq('id', newMessage.id)
                .then();
            }
          }
        }
      )
      .subscribe();

    // Fetch initial messages
    fetchMessages();

    // Clean up subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [user, conversationId, otherUserId]);

  return {
    messages,
    sendMessage,
    isLoading,
    error,
    refreshMessages: fetchMessages,
  };
}
