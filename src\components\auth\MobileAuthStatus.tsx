import React from 'react';
import { Link } from 'react-router-dom';
import { User, Settings } from 'lucide-react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { useSupabase } from '../../providers/SupabaseProvider';

export default function MobileAuthStatus() {
  // Use both Clerk and Supabase auth states
  const { isSignedIn, isLoaded: isClerkLoaded } = useAuth();
  const { user } = useUser();
  const { isAuthenticated, userProfile, isLoading: isSupabaseLoading } = useSupabase();

  // Determine the loading state from both providers
  const isLoading = isSupabaseLoading || !isClerkLoaded;

  // Show loading state
  if (isLoading) {
    return (
      <div className="md:hidden flex items-center mr-2">
        <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
      </div>
    );
  }

  // Show authenticated state - prioritize Clerk auth
  if (isSignedIn && user) {
    return (
      <div className="md:hidden flex items-center mr-2">
        <Link to="/my-account" className="flex items-center">
          <div className="bg-purple-100 text-purple-800 rounded-full h-6 w-6 flex items-center justify-center font-medium mr-1">
            {user.firstName ? user.firstName.charAt(0).toUpperCase() :
             user.primaryEmailAddress ? user.primaryEmailAddress.emailAddress.charAt(0).toUpperCase() : 'U'}
          </div>
          <span className="text-sm text-purple-600 font-medium truncate max-w-[80px]">
            {user.firstName || (user.primaryEmailAddress ? user.primaryEmailAddress.emailAddress.split('@')[0] : 'User')}
          </span>
        </Link>
      </div>
    );
  }
  // Fallback to Supabase auth if Clerk auth is not available
  else if (isAuthenticated && userProfile) {
    return (
      <div className="md:hidden flex items-center mr-2">
        <Link to="/my-account" className="flex items-center">
          <div className="bg-purple-100 text-purple-800 rounded-full h-6 w-6 flex items-center justify-center font-medium mr-1">
            {userProfile.first_name ? userProfile.first_name.charAt(0).toUpperCase() :
             userProfile.email ? userProfile.email.charAt(0).toUpperCase() : 'U'}
          </div>
          <span className="text-sm text-purple-600 font-medium truncate max-w-[80px]">
            {userProfile.first_name || userProfile.email?.split('@')[0] || 'User'}
          </span>
        </Link>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className="md:hidden flex items-center mr-2">
      <Link to="/login" className="flex items-center">
        <User className="w-4 h-4 mr-1 text-purple-600" />
        <span className="text-sm text-purple-600 font-medium">
          Sign In
        </span>
      </Link>
    </div>
  );
}
