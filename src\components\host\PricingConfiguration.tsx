/**
 * Pricing Configuration Component for Owner Portal
 * 
 * Allows venue owners to configure:
 * - Progressive pricing tiers
 * - Weekend/holiday/late-night surcharges
 * - Overtime pricing
 */

import React, { useState } from 'react';
import { Plus, Trash2, DollarSign, Clock, Calendar, Moon, AlertCircle } from 'lucide-react';

interface PricingTier {
  minHours: number;
  maxHours?: number;
  discountPercentage: number;
  label: string;
}

interface PricingSurcharges {
  weekend?: number;
  holiday?: number;
  lateNight?: number;
  overtime?: number;
}

interface PricingConfigurationProps {
  initialConfig?: {
    progressivePricing?: {
      enabled: boolean;
      tiers: PricingTier[];
    };
    surcharges?: PricingSurcharges;
  };
  onSave: (config: any) => void;
  onCancel?: () => void;
}

export default function PricingConfiguration({ 
  initialConfig, 
  onSave, 
  onCancel 
}: PricingConfigurationProps) {
  const [progressivePricingEnabled, setProgressivePricingEnabled] = useState(
    initialConfig?.progressivePricing?.enabled ?? false
  );
  
  const [pricingTiers, setPricingTiers] = useState<PricingTier[]>(
    initialConfig?.progressivePricing?.tiers ?? [
      { minHours: 1, maxHours: 3, discountPercentage: 0, label: "Standard Rate" }
    ]
  );

  const [surcharges, setSurcharges] = useState<PricingSurcharges>(
    initialConfig?.surcharges ?? {
      weekend: 20,
      holiday: 30,
      lateNight: 15,
      overtime: 25
    }
  );

  // Add new pricing tier
  const addPricingTier = () => {
    const lastTier = pricingTiers[pricingTiers.length - 1];
    const newMinHours = lastTier?.maxHours ? lastTier.maxHours + 1 : 1;
    
    setPricingTiers([
      ...pricingTiers,
      {
        minHours: newMinHours,
        maxHours: newMinHours + 2,
        discountPercentage: 5,
        label: `${newMinHours}-${newMinHours + 2} Hours`
      }
    ]);
  };

  // Remove pricing tier
  const removePricingTier = (index: number) => {
    if (pricingTiers.length > 1) {
      setPricingTiers(pricingTiers.filter((_, i) => i !== index));
    }
  };

  // Update pricing tier
  const updatePricingTier = (index: number, field: keyof PricingTier, value: any) => {
    const updatedTiers = [...pricingTiers];
    updatedTiers[index] = { ...updatedTiers[index], [field]: value };
    setPricingTiers(updatedTiers);
  };

  // Update surcharge
  const updateSurcharge = (field: keyof PricingSurcharges, value: number) => {
    setSurcharges({ ...surcharges, [field]: value });
  };

  // Save configuration
  const handleSave = () => {
    const config = {
      progressivePricing: {
        enabled: progressivePricingEnabled,
        tiers: progressivePricingEnabled ? pricingTiers : []
      },
      surcharges
    };
    onSave(config);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Pricing Configuration</h2>
        <p className="text-gray-600">
          Configure your venue's pricing strategy including progressive discounts and surcharges.
        </p>
      </div>

      {/* Progressive Pricing Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Clock className="w-5 h-5 mr-2 text-purple-600" />
              Progressive Pricing
            </h3>
            <p className="text-sm text-gray-600">Offer discounts for longer bookings</p>
          </div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={progressivePricingEnabled}
              onChange={(e) => setProgressivePricingEnabled(e.target.checked)}
              className="mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">Enable</span>
          </label>
        </div>

        {progressivePricingEnabled && (
          <div className="space-y-4">
            {pricingTiers.map((tier, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Min Hours
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={tier.minHours}
                      onChange={(e) => updatePricingTier(index, 'minHours', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Max Hours (optional)
                    </label>
                    <input
                      type="number"
                      min={tier.minHours}
                      value={tier.maxHours || ''}
                      onChange={(e) => updatePricingTier(index, 'maxHours', e.target.value ? parseInt(e.target.value) : undefined)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="No limit"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Discount %
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="50"
                      value={tier.discountPercentage}
                      onChange={(e) => updatePricingTier(index, 'discountPercentage', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Label
                    </label>
                    <input
                      type="text"
                      value={tier.label}
                      onChange={(e) => updatePricingTier(index, 'label', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="e.g., Standard Rate"
                    />
                  </div>
                  
                  <div>
                    <button
                      onClick={() => removePricingTier(index)}
                      disabled={pricingTiers.length === 1}
                      className="w-full px-3 py-2 text-red-600 hover:text-red-700 disabled:text-gray-400 disabled:cursor-not-allowed"
                    >
                      <Trash2 className="w-4 h-4 mx-auto" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
            
            <button
              onClick={addPricingTier}
              className="w-full px-4 py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-purple-400 hover:text-purple-600 flex items-center justify-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Pricing Tier
            </button>
          </div>
        )}
      </div>

      {/* Surcharges Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2 text-purple-600" />
          Surcharges
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <Calendar className="w-4 h-4 mr-1 text-blue-500" />
                Weekend Surcharge (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={surcharges.weekend || 0}
                onChange={(e) => updateSurcharge('weekend', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., 20"
              />
              <p className="text-xs text-gray-500 mt-1">Applied to Friday-Sunday bookings</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <Calendar className="w-4 h-4 mr-1 text-red-500" />
                Holiday Surcharge (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={surcharges.holiday || 0}
                onChange={(e) => updateSurcharge('holiday', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., 30"
              />
              <p className="text-xs text-gray-500 mt-1">Applied to public holidays</p>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <Moon className="w-4 h-4 mr-1 text-indigo-500" />
                Late Night Surcharge (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={surcharges.lateNight || 0}
                onChange={(e) => updateSurcharge('lateNight', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., 15"
              />
              <p className="text-xs text-gray-500 mt-1">Applied to bookings after 10 PM</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                <Clock className="w-4 h-4 mr-1 text-orange-500" />
                Overtime Surcharge (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={surcharges.overtime || 0}
                onChange={(e) => updateSurcharge('overtime', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., 25"
              />
              <p className="text-xs text-gray-500 mt-1">Applied to bookings over 12 hours</p>
            </div>
          </div>
        </div>
      </div>

      {/* Preview Section */}
      <div className="mb-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">Pricing Preview</h4>
            <p className="text-sm text-blue-800">
              Your pricing configuration will be applied to all new bookings. Existing bookings will not be affected.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        {onCancel && (
          <button
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            Cancel
          </button>
        )}
        <button
          onClick={handleSave}
          className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          Save Pricing Configuration
        </button>
      </div>
    </div>
  );
}
