import { useState, useEffect } from 'react';

interface RecentlyViewedVenue {
  id: string;
  title: string;
  location: string;
  price: number;
  image: string;
  viewedAt: number;
}

const STORAGE_KEY = 'housegoing_recently_viewed';
const MAX_RECENT_VENUES = 5;

export function useRecentlyViewed() {
  const [recentlyViewed, setRecentlyViewed] = useState<RecentlyViewedVenue[]>([]);

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setRecentlyViewed(parsed);
      }
    } catch (error) {
      console.error('Error loading recently viewed venues:', error);
    }
  }, []);

  // Add a venue to recently viewed
  const addToRecentlyViewed = (venue: Omit<RecentlyViewedVenue, 'viewedAt'>) => {
    const newVenue: RecentlyViewedVenue = {
      ...venue,
      viewedAt: Date.now()
    };

    setRecentlyViewed(prev => {
      // Remove if already exists
      const filtered = prev.filter(v => v.id !== venue.id);
      
      // Add to beginning
      const updated = [newVenue, ...filtered];
      
      // Keep only the most recent MAX_RECENT_VENUES
      const trimmed = updated.slice(0, MAX_RECENT_VENUES);
      
      // Save to localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(trimmed));
      } catch (error) {
        console.error('Error saving recently viewed venues:', error);
      }
      
      return trimmed;
    });
  };

  // Clear all recently viewed
  const clearRecentlyViewed = () => {
    setRecentlyViewed([]);
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing recently viewed venues:', error);
    }
  };

  return {
    recentlyViewed,
    addToRecentlyViewed,
    clearRecentlyViewed
  };
}
