const express = require('express');
const { getCurfewInfo } = require('./src/lib/nsw-party-planning/curfew-api');
const { parseNSWAddress } = require('./src/utils/addressUtils');
const { loadGeoJSON, findZoneForPoint, findLGAForPoint } = require('./src/utils/spatialUtils');
const suburbsApi = require('./server/api/suburbs');

const app = express();
app.use(express.json());

// Add CORS headers for frontend access
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5179');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Register suburbs API routes
suburbsApi(app);

app.post('/api/nsw-zoning', async (req, res) => {
  try {
    const { address, lat, lng } = req.body;

    const [zoningData, lgaData] = await Promise.all([
      loadGeoJSON('./data/zoning.geojson'),
      loadGeoJSON('./data/lga.geojson')
    ]);

    const point = { type: 'Point', coordinates: [parseFloat(lng), parseFloat(lat)] };
    const zoneCode = findZoneForPoint(point, zoningData) || 'R2';
    const lgaName = findLGAForPoint(point, lgaData) || 'Unknown';
    const isApartment = address && /^\d+\//.test(address);

    const curfewInfo = await getCurfewInfo({
      address: parseNSWAddress(address),
      propertyType: isApartment ? 'Apartment/Unit' : null,
      zoneCode,
      lgaName
    });

    res.json({
      address,
      coordinates: { lat, lng },
      zoning: curfewInfo.zone_name,
      lga: curfewInfo.lga_name,
      curfew: `${formatTime(curfewInfo.curfew_start)} to ${formatTime(curfewInfo.curfew_end)}`,
      ...curfewInfo
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Server error' });
  }
});

function formatTime(timeStr) {
  const [hours, minutes] = timeStr.split(':');
  const hourNum = parseInt(hours, 10);
  const period = hourNum >= 12 ? 'PM' : 'AM';
  const displayHour = hourNum % 12 || 12;
  return `${displayHour}:${minutes} ${period}`;
}

app.listen(3001, () => console.log('API server running on port 3001'));
