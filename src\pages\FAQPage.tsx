import React, { useState } from 'react';
import SEO from '../components/seo/SEO';
import { FAQSchema } from '../components/seo/JsonLd';
import { ChevronDown, ChevronUp, Search, MessageCircle, Phone, Mail, Clock, DollarSign, Shield, Volume2, Calendar, Users, Home, Car, Utensils, Music, AlertTriangle, FileText, MapPin, Star } from 'lucide-react';
import Footer from '../components/layout/Footer';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  icon?: React.ReactNode;
  hasTable?: boolean;
  hasDiagram?: boolean;
}

const faqs: FAQ[] = [
  // Booking Process
  {
    id: '1',
    question: 'How do I book a venue on HouseGoing?',
    answer: `Booking a venue is simple and takes just a few minutes:

**Step 1: Search & Browse**
• Use our search bar to find venues in your area
• Filter by date, guest count, and budget
• Browse venue photos and read reviews

**Step 2: Submit Request**
• Select your preferred date and time
• Specify number of guests and event type
• Add any special requirements or questions

**Step 3: Host Review**
• Venue owner reviews your request within 24 hours
• They may ask follow-up questions or suggest alternatives
• You'll receive email notifications about status updates

**Step 4: Confirmation & Payment**
• Once approved, you'll receive booking confirmation
• Pay the required deposit to secure your booking
• Receive venue details and host contact information`,
    category: 'Booking Process',
    icon: <Calendar className="h-5 w-5" />,
    hasDiagram: true
  },
  {
    id: '2',
    question: 'How far in advance should I book?',
    answer: `Booking timelines vary by season and venue popularity:

**Peak Times (Weekends, Holidays, Summer):**
• Book 6-8 weeks in advance
• December bookings: 10-12 weeks ahead
• Popular venues fill up quickly

**Off-Peak Times (Weekdays, Winter):**
• 2-3 weeks is usually sufficient
• More flexibility and better rates
• Last-minute bookings sometimes available

**Pro Tips:**
• Book early for better venue selection
• Consider weekday events for better pricing
• Check venue availability calendar before planning`,
    category: 'Booking Process',
    icon: <Clock className="h-5 w-5" />,
    hasTable: true
  },

  // Pricing & Payments
  {
    id: '3',
    question: 'How does HouseGoing pricing work?',
    answer: `Our transparent pricing includes several components:

**Venue Fee:** Set by the venue owner based on:
• Property size and amenities
• Location and demand
• Day of week and season
• Duration of rental

**Service Fees:** HouseGoing charges:
• Host Service Fee: 10% (deducted from host payout)
• Guest Booking Fee: 5% (added to guest total)
• Covers: Platform maintenance, payment security, customer support, insurance features

**Additional Costs (Optional):**
• Security deposit (typically 20% of booking)
• Extra cleaning fees
• Equipment rentals
• Catering services`,
    category: 'Pricing & Payments',
    icon: <DollarSign className="h-5 w-5" />,
    hasTable: true
  },
  {
    id: '4',
    question: 'What are the payment terms and cancellation policy?',
    answer: `We offer flexible payment options with clear policies:

**Payment Schedule:**
• 50% deposit required to confirm booking
• Remaining balance due 48 hours before event
• All payments processed securely through Stripe

**Cancellation Policy:**
• Free cancellation up to 48 hours before event
• Cancellations within 48 hours: 50% refund
• No-shows: No refund
• Weather cancellations: Full refund with proof

**Security Deposit:**
• 20% of booking value held as pre-authorization
• Released within 48 hours after event if no issues
• Applied toward damages if they occur (not a liability cap)
• You remain liable for full damage costs`,
    category: 'Pricing & Payments',
    icon: <Shield className="h-5 w-5" />
  },

  // Safety & Legal
  {
    id: '5',
    question: 'What are NSW noise restrictions for parties?',
    answer: `NSW has specific noise regulations that all events must follow:

**Standard Quiet Hours:**
• Weekdays: 10 PM - 8 AM
• Weekends: 12 AM (midnight) - 8 AM
• Public holidays: 12 AM - 8 AM

**Noise Level Limits:**
• Residential areas: 45-50 dB during quiet hours
• Mixed-use areas: 50-55 dB during quiet hours
• Measured from nearest neighbor's property

**Council Variations:**
Different councils may have stricter rules:
• Some require permits for 30+ people
• Amplified music may need special approval
• Outdoor events often have earlier cutoffs

**Best Practices:**
• Inform neighbors 48 hours in advance
• Provide your contact details
• Move activities indoors after quiet hours
• Keep council contact info handy`,
    category: 'Safety & Legal',
    icon: <Volume2 className="h-5 w-5" />,
    hasTable: true
  },
  {
    id: '6',
    question: 'What insurance coverage do I need?',
    answer: `HouseGoing provides comprehensive protection for your event:

**Platform Services:**
• 24/7 emergency support
• Dispute resolution facilitation
• Security deposit management
• Host-guest communication tools

**NO Insurance Coverage Provided:**
HouseGoing does NOT provide any insurance coverage or damage protection. We are a booking platform only.

**Your Responsibilities:**
• Obtain your own event insurance if desired
• You are fully liable for any damages
• Security deposit is held toward damages (not coverage)
• Disputes are between you and host directly

**Additional Insurance:**
For larger events (50+ people), consider:
• Extended public liability coverage
• Event cancellation insurance
• Liquor liability coverage
• Equipment protection`,
    category: 'Safety & Legal',
    icon: <Shield className="h-5 w-5" />
  },

  // Guest Guidelines
  {
    id: '7',
    question: 'What are the rules for guests and party size?',
    answer: `Clear guidelines ensure everyone has a great experience:

**Guest Count Limits:**
• Must not exceed venue's maximum capacity
• Based on fire safety regulations
• Includes all attendees (adults and children)
• Additional guests require host approval

**Guest Responsibilities:**
• All guests must be personally known to organizer
• No public advertising or ticket sales allowed
• Organizer responsible for all guest behavior
• Must provide guest list if requested

**Age Restrictions:**
• Primary organizer must be 18+
• Minors require adult supervision
• No alcohol service to under-18s
• Some venues specialize in teen events

**Prohibited Activities:**
• Public events or commercial gatherings
• Illegal drug use
• Excessive noise during quiet hours
• Damage to property or surroundings`,
    category: 'Guest Guidelines',
    icon: <Users className="h-5 w-5" />
  },
  {
    id: '8',
    question: 'Can I bring children to the venue?',
    answer: `Yes, children are welcome at most venues with proper supervision:

**Age Requirements:**
• Children under 12 must be supervised at all times
• Teens 13-17 require adult supervision for alcohol events
• Some venues have age restrictions or curfews
• Pool venues require certified lifeguards for children

**Safety Considerations:**
• Ensure venue is child-proofed if needed
• Check for pool fencing and safety gates
• Verify playground equipment is safe and maintained
• Consider noise levels and neighbor proximity

**Additional Costs:**
• Some venues charge per child for insurance
• Extra cleaning fees may apply for children's parties
• Equipment rentals (high chairs, cribs) available`,
    category: 'Guest Guidelines',
    icon: <Users className="h-5 w-5" />
  },

  // Venue Features & Amenities
  {
    id: '9',
    question: 'What amenities are typically included with venues?',
    answer: `Venue amenities vary, but most include basic essentials:

**Standard Inclusions:**
• Tables and chairs for stated capacity
• Basic lighting (indoor and outdoor)
• Restroom facilities
• Kitchen access (varies by venue)
• Parking spaces (number varies)
• Basic cleaning supplies

**Common Additional Features:**
• Swimming pool and pool equipment
• BBQ facilities and outdoor cooking areas
• Sound system and microphone
• Projector and screen for presentations
• Air conditioning and heating
• Outdoor furniture and umbrellas

**Premium Amenities (Extra Cost):**
• Professional catering kitchen
• Bar setup with glassware
• Dance floor and lighting effects
• Photobooth props and backdrop
• Games room with pool table/arcade
• Spa and wellness facilities`,
    category: 'Venue Features & Amenities',
    icon: <Home className="h-5 w-5" />,
    hasTable: true
  },
  {
    id: '10',
    question: 'Do venues provide catering or can I bring my own food?',
    answer: `Food options depend on the venue's policies and facilities:

**BYO Food Venues:**
• Full kitchen access for preparation
• BBQ facilities for outdoor cooking
• Refrigeration and freezer space
• Basic cooking equipment provided
• You handle all food preparation and cleanup

**Catering-Only Venues:**
• Must use venue's preferred caterers
• Professional kitchen facilities
• Staff to handle food service
• Higher cost but less work for you
• Food safety and licensing handled

**Hybrid Options:**
• Some venues allow both options
• BYO with catering supplement available
• External caterer approval required
• Kitchen access fees may apply

**Food Safety Requirements:**
• All food must be prepared in licensed kitchens
• Temperature control for perishables
• Proper storage and handling procedures
• Alcohol service requires RSA certification`,
    category: 'Venue Features & Amenities',
    icon: <Utensils className="h-5 w-5" />
  },

  // Alcohol & Entertainment
  {
    id: '11',
    question: 'What are the alcohol policies and licensing requirements?',
    answer: `Alcohol policies vary significantly between venues:

**BYO Alcohol Venues:**
• Bring your own alcoholic beverages
• No corkage fees at most venues
• You're responsible for service and cleanup
• Must comply with RSA (Responsible Service of Alcohol) laws
• No service to minors or intoxicated persons

**Licensed Venues:**
• Venue holds liquor license
• Must purchase alcohol through venue
• Professional bar service included
• Higher costs but full compliance guaranteed
• Staff trained in responsible service

**Dry Venues:**
• No alcohol permitted on premises
• Suitable for family events and corporate functions
• Often religious or community venues
• Lower insurance costs and simpler planning

**Legal Requirements:**
• All alcohol service must comply with NSW Liquor Act
• RSA certification required for anyone serving
• ID checking mandatory for anyone appearing under 25
• Intoxicated persons must be refused service
• Venue can lose license for violations`,
    category: 'Alcohol & Entertainment',
    icon: <Utensils className="h-5 w-5" />,
    hasTable: true
  },
  {
    id: '12',
    question: 'Can I hire entertainment and what are the restrictions?',
    answer: `Most venues welcome entertainment with some guidelines:

**Permitted Entertainment:**
• Live bands and acoustic musicians
• DJs with professional equipment
• Magicians and children's entertainers
• Dance instructors and performers
• Photography and videography services

**Sound and Noise Restrictions:**
• Must comply with local noise ordinances
• Sound limiters may be installed at some venues
• Outdoor amplified music often has earlier cutoffs
• Acoustic instruments generally have fewer restrictions
• Some venues require sound engineers

**Equipment and Setup:**
• Most venues provide basic power outlets
• Professional equipment may require additional power
• Setup and breakdown times must be arranged
• Venue access for equipment delivery
• Insurance coverage for entertainment equipment

**Booking Entertainment:**
• Book entertainers well in advance
• Confirm venue suitability with entertainer
• Provide venue contact details and restrictions
• Coordinate load-in and setup times
• Ensure all entertainers have public liability insurance`,
    category: 'Alcohol & Entertainment',
    icon: <Music className="h-5 w-5" />
  },

  // Logistics & Parking
  {
    id: '13',
    question: 'How does parking work and what about guest transportation?',
    answer: `Parking arrangements vary significantly by venue location:

**On-Site Parking:**
• Number of spaces varies (typically 10-50 spaces)
• Free parking included with most venue rentals
• Some venues have valet parking available
• Disability parking spaces required by law
• Overflow parking arrangements may be needed

**Street Parking:**
• Check local council parking restrictions
• Time limits may apply (2-4 hours common)
• Permit parking areas require resident permits
• Weekend restrictions often relaxed
• Inform guests about parking meter requirements

**Alternative Transportation:**
• Public transport accessibility varies
• Uber/taxi pickup and drop-off areas
• Shuttle services for large events
• Designated driver arrangements
• Bicycle parking and storage

**Parking Tips:**
• Inform guests about parking limitations in advance
• Provide alternative parking locations nearby
• Consider carpooling for large events
• Check for special event parking restrictions
• Have backup plans for overflow parking`,
    category: 'Logistics & Parking',
    icon: <Car className="h-5 w-5" />,
    hasTable: true
  },
  {
    id: '14',
    question: 'What about decorations, setup, and cleanup requirements?',
    answer: `Decoration policies help protect venues while allowing creativity:

**Decoration Guidelines:**
• No permanent fixtures or alterations
• Removable decorations only (tape, pins, etc.)
• Some venues prohibit wall attachments entirely
• Outdoor decorations must be weather-secured
• Fire safety regulations apply to all decorations

**Setup and Breakdown:**
• Setup time typically included in rental period
• Early access may be available for additional fee
• Professional decorators welcome with advance notice
• All decorations must be removed by end time
• Venue must be returned to original condition

**Prohibited Items:**
• Glitter, confetti, or difficult-to-clean materials
• Candles or open flames (battery candles OK)
• Nails, screws, or permanent adhesives
• Helium balloons in some indoor venues
• Anything that could damage surfaces

**Cleanup Requirements:**
• Basic cleanup included in most rentals
• Deep cleaning fees for excessive mess
• Garbage removal and recycling sorting
• Return furniture to original positions
• Professional cleaning available for additional cost`,
    category: 'Logistics & Parking',
    icon: <Home className="h-5 w-5" />
  },

  // Weather & Seasonal Considerations
  {
    id: '15',
    question: 'What happens if there\'s bad weather during my event?',
    answer: `Weather planning is crucial for outdoor events:

**Weather Policies:**
• Most venues have covered areas or indoor alternatives
• Outdoor events are subject to weather conditions
• Some venues offer weather guarantees or refunds
• Marquees and tents available for additional cost
• Heating and cooling options for extreme weather

**Seasonal Considerations:**
• Summer: Heat, UV protection, pool safety, bushfire risk
• Winter: Cold, rain, heating requirements, shorter daylight
• Spring/Autumn: Variable weather, wind considerations
• Wet season: Flooding risk, indoor backup plans essential

**Backup Plans:**
• Indoor alternatives should always be considered
• Marquee hire for outdoor protection
• Portable heating and cooling solutions
• Weather monitoring services available
• Flexible timing for weather-dependent activities

**Cancellation Due to Weather:**
• Severe weather warnings may trigger cancellations
• Bushfire danger days restrict outdoor events
• Flooding or storm damage may make venues inaccessible
• Full refunds typically provided for weather cancellations`,
    category: 'Weather & Seasonal',
    icon: <AlertTriangle className="h-5 w-5" />
  },
  {
    id: '16',
    question: 'Are there seasonal pricing differences and peak times?',
    answer: `Venue pricing varies significantly by season and demand:

**Peak Season (Higher Prices):**
• December - January: Holiday and summer events
• March - May: Wedding season and mild weather
• September - November: Spring events and graduations
• School holidays: Increased demand for family events

**Off-Peak Season (Lower Prices):**
• June - August: Winter months, fewer outdoor events
• Weekdays year-round: Business events and smaller gatherings
• January - February: Post-holiday period
• Mid-week bookings: Tuesday - Thursday typically cheapest

**Special Event Pricing:**
• New Year's Eve: Premium pricing (200-300% increase)
• Valentine's Day: Romantic venue premium
• Melbourne Cup: Spring racing carnival events
• Christmas period: Holiday party premium

**Booking Strategy:**
• Book off-peak for significant savings (30-50% less)
• Consider weekday events for better rates
• Avoid public holidays and school holidays
• Early bird discounts often available`,
    category: 'Weather & Seasonal',
    icon: <Calendar className="h-5 w-5" />,
    hasTable: true
  },

  // Reviews & Quality Assurance
  {
    id: '17',
    question: 'How do reviews and ratings work on HouseGoing?',
    answer: `Our review system ensures transparency and quality:

**Review Process:**
• Only verified guests who completed bookings can review
• Reviews must be submitted within 14 days of event
• Both guests and hosts can leave reviews
• Reviews are moderated for inappropriate content
• Response system allows hosts to address concerns

**Rating Categories:**
• Overall experience (1-5 stars)
• Venue cleanliness and condition
• Host communication and responsiveness
• Value for money
• Accuracy of listing description

**Review Guidelines:**
• Honest, constructive feedback encouraged
• No personal attacks or inappropriate language
• Focus on venue and service quality
• Include specific details about experience
• Photos can be included with reviews

**Quality Assurance:**
• Venues with consistently low ratings may be removed
• Host response to reviews monitored
• Regular venue inspections for top-rated properties
• Guest feedback drives platform improvements`,
    category: 'Reviews & Quality',
    icon: <Star className="h-5 w-5" />
  },
  {
    id: '18',
    question: 'What quality standards do venues need to meet?',
    answer: `HouseGoing maintains strict quality standards:

**Safety Requirements:**
• Current fire safety certificates
• Electrical safety compliance
• Pool safety (fencing, gates, equipment)
• First aid kit and emergency procedures
• Public liability insurance (tiered): $5M minimum, $10-20M recommended

**Cleanliness Standards:**
• Professional cleaning between bookings
• Sanitized restroom facilities
• Clean kitchen and food preparation areas
• Pool and spa water quality testing
• Pest control and maintenance programs

**Facility Standards:**
• All advertised amenities must be functional
• Furniture and equipment in good repair
• Adequate lighting and power outlets
• Climate control systems operational
• Internet connectivity as advertised

**Host Requirements:**
• Responsive communication (within 24 hours)
• Accurate listing descriptions and photos
• Clear house rules and policies
• Professional conduct and reliability
• Ongoing property maintenance`,
    category: 'Reviews & Quality',
    icon: <Shield className="h-5 w-5" />
  },

  // Emergency & Support
  {
    id: '19',
    question: 'What support is available during my event?',
    answer: `HouseGoing provides comprehensive support throughout your event:

**24/7 Emergency Support:**
• Emergency hotline for urgent issues
• Direct contact with venue owners
• Emergency services coordination if needed
• Insurance claim assistance
• Dispute resolution services

**Pre-Event Support:**
• Venue selection assistance
• Event planning recommendations
• Vendor and supplier connections
• Permit and licensing guidance
• Weather monitoring and alerts

**During Event Support:**
• Host contact details provided
• Emergency contact numbers
• Technical support for venue systems
• Backup plans for common issues
• Real-time assistance via phone/chat

**Post-Event Support:**
• Damage assessment and claims processing
• Review and feedback systems
• Refund processing if applicable
• Future booking assistance
• Community guidelines enforcement`,
    category: 'Emergency & Support',
    icon: <Phone className="h-5 w-5" />
  },
  {
    id: '20',
    question: 'What should I do in case of emergencies or accidents?',
    answer: `Emergency preparedness is essential for all events:

**Immediate Emergency Response:**
1. Call 000 for life-threatening emergencies
2. Contact venue owner/manager immediately
3. Contact HouseGoing support via email: <EMAIL>
4. Document incident with photos if safe to do so
5. Preserve evidence for insurance claims

**Medical Emergencies:**
• First aid kit locations provided by venue
• Nearest hospital contact details available
• Emergency services access routes clearly marked
• Guest medical information should be collected
• Designated first aid person recommended for large events

**Property Damage:**
• Stop activity causing damage immediately
• Secure area to prevent further damage
• Document with photos and witness statements
• Contact venue owner and HouseGoing
• Do not attempt repairs without approval

**Security Issues:**
• Contact police for criminal activity
• Venue security systems and procedures
• Neighbor contact details for noise complaints
• Evacuation procedures for severe weather
• Emergency lighting and exit procedures`,
    category: 'Emergency & Support',
    icon: <AlertTriangle className="h-5 w-5" />
  },

  // Host Information
  {
    id: '21',
    question: 'How do I become a venue host on HouseGoing?',
    answer: `Becoming a host is straightforward with our comprehensive onboarding:

**Eligibility Requirements:**
• Own or have legal authority to rent the property
• Property must meet safety and quality standards
• Public liability insurance (tiered): $5M minimum, $10-20M recommended
• Compliance with local council regulations
• Age 18+ with valid identification

**Application Process:**
1. Create host account with verification
2. Complete detailed property listing
3. Upload high-quality photos (minimum 10)
4. Set pricing and availability calendar
5. Submit required documentation and certificates

**Required Documentation:**
• Proof of ownership or rental authority
• Public liability insurance certificate
• Fire safety certificate (if required)
• Council permits and approvals
• Pool safety certificate (if applicable)

**Ongoing Requirements:**
• Maintain property condition and cleanliness
• Respond to inquiries within 24 hours
• Keep calendar updated with availability
• Provide excellent guest experience
• Comply with platform policies and local laws`,
    category: 'Host Information',
    icon: <Home className="h-5 w-5" />
  }
];

// Extract unique categories from FAQ data
const categories = Array.from(new Set(faqs.map(faq => faq.category)));

// Category descriptions for the navigation
const categoryDescriptions = {
  'Booking Process': 'How to book venues, timing, and payment terms',
  'Pricing & Payments': 'Costs, fees, seasonal pricing, and payment options',
  'Safety & Legal': 'NSW noise restrictions, insurance, and legal requirements',
  'Guest Guidelines': 'Party rules, guest limits, and age restrictions',
  'Venue Features & Amenities': 'What\'s included, food/alcohol policies, and premium features',
  'Alcohol & Entertainment': 'BYO policies, licensed venues, and entertainment options',
  'Logistics & Parking': 'Parking, decorations, setup, and transportation',
  'Weather & Seasonal': 'Weather planning, seasonal pricing, and backup options',
  'Reviews & Quality': 'Rating system, quality standards, and venue verification',
  'Emergency & Support': '24/7 support, emergency procedures, and assistance',
  'Host Information': 'Becoming a host, requirements, and quality standards'
};

// Helper components for visual elements
const BookingDiagram = () => (
  <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 mt-4">
    <h4 className="font-semibold text-gray-900 mb-4">Booking Process Flow</h4>
    <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
      {[
        { step: '1', title: 'Search', desc: 'Find venues' },
        { step: '2', title: 'Request', desc: 'Submit booking' },
        { step: '3', title: 'Review', desc: 'Host approves' },
        { step: '4', title: 'Confirm', desc: 'Pay & book' }
      ].map((item, index) => (
        <div key={item.step} className="flex items-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">
              {item.step}
            </div>
            <div className="text-center mt-2">
              <div className="font-medium text-gray-900">{item.title}</div>
              <div className="text-sm text-gray-600">{item.desc}</div>
            </div>
          </div>
          {index < 3 && (
            <div className="hidden md:block w-8 h-0.5 bg-purple-300 mx-4"></div>
          )}
        </div>
      ))}
    </div>
  </div>
);

const BookingTimingTable = () => (
  <div className="mt-4 overflow-x-auto">
    <table className="w-full border border-gray-200 rounded-lg">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Event Type</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Recommended Booking Time</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Notes</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        <tr>
          <td className="px-4 py-3 text-gray-900">Weekend Parties</td>
          <td className="px-4 py-3 text-gray-600">6-8 weeks ahead</td>
          <td className="px-4 py-3 text-gray-600">High demand, book early</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Weekday Events</td>
          <td className="px-4 py-3 text-gray-600">2-3 weeks ahead</td>
          <td className="px-4 py-3 text-gray-600">More availability, better rates</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Holiday Parties</td>
          <td className="px-4 py-3 text-gray-600">10-12 weeks ahead</td>
          <td className="px-4 py-3 text-gray-600">Peak season, very limited</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Last Minute</td>
          <td className="px-4 py-3 text-gray-600">1-7 days ahead</td>
          <td className="px-4 py-3 text-gray-600">Limited options, higher prices</td>
        </tr>
      </tbody>
    </table>
  </div>
);

const PricingTable = () => (
  <div className="mt-4 bg-gray-50 rounded-lg p-4">
    <h4 className="font-semibold text-gray-900 mb-3">Sample Pricing Breakdown</h4>
    <div className="space-y-2">
      <div className="flex justify-between">
        <span className="text-gray-600">Venue Fee (8 hours)</span>
        <span className="font-medium">$400</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">Service Fee (5%)</span>
        <span className="font-medium">$20</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">GST (10%)</span>
        <span className="font-medium">$42</span>
      </div>
      <div className="border-t border-gray-300 pt-2 flex justify-between font-bold">
        <span>Total</span>
        <span>$462</span>
      </div>
      <div className="flex justify-between text-sm text-gray-600">
        <span>Security Deposit (refundable)</span>
        <span>$80</span>
      </div>
    </div>
  </div>
);

const NoiseTable = () => (
  <div className="mt-4 overflow-x-auto">
    <table className="w-full border border-gray-200 rounded-lg">
      <thead className="bg-red-50">
        <tr>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Day Type</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Quiet Hours</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Max Noise Level</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        <tr>
          <td className="px-4 py-3 text-gray-900">Monday - Thursday</td>
          <td className="px-4 py-3 text-gray-600">10 PM - 8 AM</td>
          <td className="px-4 py-3 text-gray-600">45-50 dB</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Friday - Saturday</td>
          <td className="px-4 py-3 text-gray-600">12 AM - 8 AM</td>
          <td className="px-4 py-3 text-gray-600">45-50 dB</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Sunday & Public Holidays</td>
          <td className="px-4 py-3 text-gray-600">12 AM - 8 AM</td>
          <td className="px-4 py-3 text-gray-600">45-50 dB</td>
        </tr>
      </tbody>
    </table>
    <p className="text-sm text-gray-600 mt-2">
      * Noise levels measured from nearest neighbor's property boundary
    </p>
  </div>
);

const AmenitiesTable = () => (
  <div className="mt-4 grid md:grid-cols-3 gap-4">
    <div className="bg-green-50 rounded-lg p-4">
      <h5 className="font-semibold text-green-900 mb-2">✅ Standard Inclusions</h5>
      <ul className="text-sm text-green-800 space-y-1">
        <li>• Tables & chairs</li>
        <li>• Basic lighting</li>
        <li>• Restroom facilities</li>
        <li>• Kitchen access</li>
        <li>• Parking spaces</li>
      </ul>
    </div>
    <div className="bg-blue-50 rounded-lg p-4">
      <h5 className="font-semibold text-blue-900 mb-2">🏊 Common Features</h5>
      <ul className="text-sm text-blue-800 space-y-1">
        <li>• Swimming pool</li>
        <li>• BBQ facilities</li>
        <li>• Sound system</li>
        <li>• Air conditioning</li>
        <li>• Outdoor furniture</li>
      </ul>
    </div>
    <div className="bg-purple-50 rounded-lg p-4">
      <h5 className="font-semibold text-purple-900 mb-2">⭐ Premium Add-ons</h5>
      <ul className="text-sm text-purple-800 space-y-1">
        <li>• Professional kitchen</li>
        <li>• Bar setup</li>
        <li>• Dance floor</li>
        <li>• Photobooth</li>
        <li>• Spa facilities</li>
      </ul>
    </div>
  </div>
);

const AlcoholPolicyTable = () => (
  <div className="mt-4 overflow-x-auto">
    <table className="w-full border border-gray-200 rounded-lg">
      <thead className="bg-amber-50">
        <tr>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Venue Type</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Alcohol Policy</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Cost Impact</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Your Responsibility</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        <tr>
          <td className="px-4 py-3 text-gray-900">BYO Venues</td>
          <td className="px-4 py-3 text-gray-600">Bring your own</td>
          <td className="px-4 py-3 text-green-600">Lower cost</td>
          <td className="px-4 py-3 text-gray-600">Service & cleanup</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Licensed Venues</td>
          <td className="px-4 py-3 text-gray-600">Purchase through venue</td>
          <td className="px-4 py-3 text-red-600">Higher cost</td>
          <td className="px-4 py-3 text-gray-600">Minimal</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Dry Venues</td>
          <td className="px-4 py-3 text-gray-600">No alcohol permitted</td>
          <td className="px-4 py-3 text-green-600">Lowest cost</td>
          <td className="px-4 py-3 text-gray-600">None</td>
        </tr>
      </tbody>
    </table>
  </div>
);

const ParkingTable = () => (
  <div className="mt-4 overflow-x-auto">
    <table className="w-full border border-gray-200 rounded-lg">
      <thead className="bg-blue-50">
        <tr>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Parking Type</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Typical Capacity</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Cost</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Considerations</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        <tr>
          <td className="px-4 py-3 text-gray-900">On-site</td>
          <td className="px-4 py-3 text-gray-600">10-50 spaces</td>
          <td className="px-4 py-3 text-green-600">Free</td>
          <td className="px-4 py-3 text-gray-600">Limited availability</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Street parking</td>
          <td className="px-4 py-3 text-gray-600">Variable</td>
          <td className="px-4 py-3 text-amber-600">Meters/permits</td>
          <td className="px-4 py-3 text-gray-600">Time restrictions</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Public lots</td>
          <td className="px-4 py-3 text-gray-600">50-200 spaces</td>
          <td className="px-4 py-3 text-red-600">$5-20/day</td>
          <td className="px-4 py-3 text-gray-600">Walking distance</td>
        </tr>
      </tbody>
    </table>
  </div>
);

const SeasonalPricingTable = () => (
  <div className="mt-4 overflow-x-auto">
    <table className="w-full border border-gray-200 rounded-lg">
      <thead className="bg-gradient-to-r from-orange-50 to-red-50">
        <tr>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Season</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Months</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Price Level</th>
          <th className="px-4 py-3 text-left font-medium text-gray-900">Booking Advice</th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        <tr>
          <td className="px-4 py-3 text-gray-900">Peak Summer</td>
          <td className="px-4 py-3 text-gray-600">Dec - Jan</td>
          <td className="px-4 py-3 text-red-600">Very High</td>
          <td className="px-4 py-3 text-gray-600">Book 10-12 weeks ahead</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Autumn</td>
          <td className="px-4 py-3 text-gray-600">Mar - May</td>
          <td className="px-4 py-3 text-orange-600">High</td>
          <td className="px-4 py-3 text-gray-600">Book 6-8 weeks ahead</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Winter</td>
          <td className="px-4 py-3 text-gray-600">Jun - Aug</td>
          <td className="px-4 py-3 text-green-600">Low</td>
          <td className="px-4 py-3 text-gray-600">Best value, 2-3 weeks</td>
        </tr>
        <tr>
          <td className="px-4 py-3 text-gray-900">Spring</td>
          <td className="px-4 py-3 text-gray-600">Sep - Nov</td>
          <td className="px-4 py-3 text-orange-600">High</td>
          <td className="px-4 py-3 text-gray-600">Wedding season premium</td>
        </tr>
      </tbody>
    </table>
  </div>
);

export default function FAQPage() {
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  // Group FAQs by category for better organization
  const groupedFAQs = filteredFAQs.reduce((acc, faq) => {
    if (!acc[faq.category]) {
      acc[faq.category] = [];
    }
    acc[faq.category].push(faq);
    return acc;
  }, {} as Record<string, FAQ[]>);

  const renderVisualElements = (faq: FAQ) => {
    if (faq.id === '1' && faq.hasDiagram) {
      return <BookingDiagram />;
    }
    if (faq.id === '2' && faq.hasTable) {
      return <BookingTimingTable />;
    }
    if (faq.id === '3' && faq.hasTable) {
      return <PricingTable />;
    }
    if (faq.id === '5' && faq.hasTable) {
      return <NoiseTable />;
    }
    if (faq.id === '9' && faq.hasTable) {
      return <AmenitiesTable />;
    }
    if (faq.id === '11' && faq.hasTable) {
      return <AlcoholPolicyTable />;
    }
    if (faq.id === '13' && faq.hasTable) {
      return <ParkingTable />;
    }
    if (faq.id === '16' && faq.hasTable) {
      return <SeasonalPricingTable />;
    }
    return null;
  };

  return (
    <>
      <SEO
        title="Frequently Asked Questions | Party Venue Rentals NSW | HouseGoing"
        description="Get answers to common questions about party venue rentals in NSW. Learn about noise restrictions, permits, booking policies, insurance, and more."
        keywords="party venue FAQ, NSW party permits, noise restrictions, venue booking questions, party planning help"
        url="https://housegoing.com.au/faq"
      />

      <FAQSchema faqs={filteredFAQs.map(faq => ({ question: faq.question, answer: faq.answer }))} />

      <div className="pt-32 px-4 sm:px-6 max-w-3xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Frequently Asked Questions
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Everything you need to know about renting party venues in NSW
          </p>
        </div>

        {/* Topic Navigation */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Choose Your Topic
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => {
              const categoryFAQs = faqs.filter(faq => faq.category === category);
              const firstFAQ = categoryFAQs[0];

              return (
                <button
                  key={category}
                  onClick={() => {
                    const element = document.getElementById(`category-${category.replace(/\s+/g, '-').toLowerCase()}`);
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                  }}
                  className="bg-white border border-gray-200 rounded-xl p-6 text-left hover:shadow-lg hover:border-purple-300 transition-all duration-200 group"
                >
                  <div className="flex items-start">
                    {firstFAQ?.icon && (
                      <div className="text-purple-600 mr-4 mt-1 group-hover:text-purple-700 transition-colors">
                        {firstFAQ.icon}
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-purple-700 transition-colors">
                        {category}
                      </h3>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {categoryDescriptions[category]}
                      </p>
                      <div className="mt-3 text-xs text-purple-600 font-medium">
                        {categoryFAQs.length} question{categoryFAQs.length !== 1 ? 's' : ''} →
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          <div className="text-center mt-8">
            <p className="text-gray-600 mb-4">
              Can't find what you're looking for?
            </p>
            <button
              onClick={() => {
                // Try multiple methods to open the chat
                const chatWidget = document.querySelector('[data-chat-widget]') as HTMLElement;
                if (chatWidget) {
                  chatWidget.click();
                } else {
                  // Fallback - dispatch custom event
                  window.dispatchEvent(new CustomEvent('openChat'));
                  // Additional fallback - try to find chat button by class
                  const chatButton = document.querySelector('button[aria-label="Chat with venue assistant"]') as HTMLElement;
                  if (chatButton) {
                    chatButton.click();
                  }
                }
              }}
              className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
            >
              <MessageCircle className="h-5 w-5 mr-2" />
              Ask Homie Directly
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-12">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search for answers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-12 pr-4 py-4 text-lg border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-gray-50 hover:bg-white transition-colors"
          />
        </div>

        {/* FAQ Sections */}
        {searchTerm ? (
          // Show filtered results when searching
          <div className="space-y-2">
            {filteredFAQs.map((faq) => (
              <div key={faq.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg text-gray-900 pr-4">{faq.question}</h3>
                  {openFAQ === faq.id ? (
                    <ChevronUp className="h-5 w-5 text-gray-400 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
                  )}
                </button>

                {openFAQ === faq.id && (
                  <div className="px-6 pb-5 border-t border-gray-100">
                    <div className="pt-4">
                      <div className="prose prose-sm max-w-none">
                        <div className="whitespace-pre-line text-gray-700 leading-relaxed">
                          {faq.answer.split('\n').map((line, index) => {
                            // Handle bold text with **
                            if (line.includes('**')) {
                              const parts = line.split('**');
                              return (
                                <p key={index} className="mb-2">
                                  {parts.map((part, partIndex) =>
                                    partIndex % 2 === 1 ? (
                                      <strong key={partIndex} className="font-bold text-gray-900">{part}</strong>
                                    ) : (
                                      <span key={partIndex}>{part}</span>
                                    )
                                  )}
                                </p>
                              );
                            }
                            // Handle bullet points
                            else if (line.trim().startsWith('•')) {
                              return (
                                <div key={index} className="ml-4 mb-1 flex items-start">
                                  <span className="text-purple-600 mr-2 mt-1">•</span>
                                  <span>{line.trim().substring(1).trim()}</span>
                                </div>
                              );
                            }
                            // Handle numbered lists
                            else if (line.trim().match(/^\d+\./)) {
                              return (
                                <div key={index} className="ml-4 mb-1 flex items-start">
                                  <span className="text-purple-600 mr-2 font-medium">{line.trim().match(/^\d+\./)[0]}</span>
                                  <span>{line.trim().replace(/^\d+\./, '').trim()}</span>
                                </div>
                              );
                            }
                            // Regular text
                            else if (line.trim()) {
                              return <p key={index} className="mb-2">{line}</p>;
                            }
                            // Empty line
                            else {
                              return <div key={index} className="mb-2"></div>;
                            }
                          })}
                        </div>
                      </div>
                      {renderVisualElements(faq)}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          // Show grouped results when not searching
          <div className="space-y-12">
            {Object.entries(groupedFAQs).map(([category, categoryFAQs]) => (
              <div key={category} id={`category-${category.replace(/\s+/g, '-').toLowerCase()}`}>
                <h2 className="text-2xl font-bold text-gray-900 mb-8 flex items-center">
                  {categoryFAQs[0]?.icon && (
                    <span className="text-purple-600 mr-3">{categoryFAQs[0].icon}</span>
                  )}
                  {category}
                </h2>
                <div className="space-y-4">
                  {categoryFAQs.map((faq) => (
                    <div key={faq.id} className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <button
                        onClick={() => toggleFAQ(faq.id)}
                        className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          {faq.icon && (
                            <span className="text-purple-600 mr-3 flex-shrink-0">{faq.icon}</span>
                          )}
                          <h3 className="text-lg font-medium text-gray-900 pr-4">{faq.question}</h3>
                        </div>
                        {openFAQ === faq.id ? (
                          <ChevronUp className="h-5 w-5 text-gray-400 flex-shrink-0" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
                        )}
                      </button>

                      {openFAQ === faq.id && (
                        <div className="px-6 pb-6 border-t border-gray-100">
                          <div className="pt-4">
                            <div className="prose prose-sm max-w-none">
                              <div className="whitespace-pre-line text-gray-700 leading-relaxed">
                                {faq.answer.split('\n').map((line, index) => {
                                  // Handle bold text with **
                                  if (line.includes('**')) {
                                    const parts = line.split('**');
                                    return (
                                      <p key={index} className="mb-2">
                                        {parts.map((part, partIndex) =>
                                          partIndex % 2 === 1 ? (
                                            <strong key={partIndex} className="font-bold text-gray-900">{part}</strong>
                                          ) : (
                                            <span key={partIndex}>{part}</span>
                                          )
                                        )}
                                      </p>
                                    );
                                  }
                                  // Handle bullet points
                                  else if (line.trim().startsWith('•')) {
                                    return (
                                      <div key={index} className="ml-4 mb-1 flex items-start">
                                        <span className="text-purple-600 mr-2 mt-1">•</span>
                                        <span>{line.trim().substring(1).trim()}</span>
                                      </div>
                                    );
                                  }
                                  // Handle numbered lists
                                  else if (line.trim().match(/^\d+\./)) {
                                    return (
                                      <div key={index} className="ml-4 mb-1 flex items-start">
                                        <span className="text-purple-600 mr-2 font-medium">{line.trim().match(/^\d+\./)[0]}</span>
                                        <span>{line.trim().replace(/^\d+\./, '').trim()}</span>
                                      </div>
                                    );
                                  }
                                  // Regular text
                                  else if (line.trim()) {
                                    return <p key={index} className="mb-2">{line}</p>;
                                  }
                                  // Empty line
                                  else {
                                    return <div key={index} className="mb-2"></div>;
                                  }
                                })}
                              </div>
                            </div>
                            {renderVisualElements(faq)}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* No Results */}
        {filteredFAQs.length === 0 && searchTerm && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600 mb-6">
              Try searching with different keywords
            </p>
            <button
              onClick={() => setSearchTerm('')}
              className="text-purple-600 hover:text-purple-700 font-medium"
            >
              Clear search
            </button>
          </div>
        )}

        {/* Contact Section */}
        <div className="mt-20 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-8 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Chat with <strong>Homie</strong>, our AI venue assistant! Get instant answers about bookings, pricing, policies, and more.
            </p>

            <div className="bg-white rounded-xl p-6 shadow-sm mb-6">
              <div className="flex items-center justify-center mb-4">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 text-left">
                  <h3 className="font-semibold text-gray-900">Homie - Venue Assistant</h3>
                  <p className="text-sm text-gray-600">Available 24/7 with all FAQ information</p>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div className="text-left">
                  <h4 className="font-medium text-gray-900 mb-2">Homie can help with:</h4>
                  <ul className="space-y-1">
                    <li>• Booking process and timing</li>
                    <li>• Pricing and payment terms</li>
                    <li>• NSW noise regulations</li>
                    <li>• Insurance and safety requirements</li>
                  </ul>
                </div>
                <div className="text-left">
                  <h4 className="font-medium text-gray-900 mb-2">Plus information about:</h4>
                  <ul className="space-y-1">
                    <li>• Venue amenities and features</li>
                    <li>• Alcohol policies and entertainment</li>
                    <li>• Parking and logistics</li>
                    <li>• Weather and seasonal considerations</li>
                  </ul>
                </div>
              </div>
            </div>

            <button
              onClick={() => {
                // Try multiple methods to open the chat
                const chatWidget = document.querySelector('[data-chat-widget]') as HTMLElement;
                if (chatWidget) {
                  chatWidget.click();
                } else {
                  // Fallback - dispatch custom event
                  window.dispatchEvent(new CustomEvent('openChat'));
                  // Additional fallback - try to find chat button by class
                  const chatButton = document.querySelector('button[aria-label="Chat with venue assistant"]') as HTMLElement;
                  if (chatButton) {
                    chatButton.click();
                  }
                }
              }}
              className="inline-flex items-center px-8 py-4 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors font-medium text-lg shadow-lg hover:shadow-xl"
            >
              <MessageCircle className="h-6 w-6 mr-3" />
              Chat with Homie Now
            </button>

            <p className="text-sm text-gray-500 mt-4">
              Or email us at <a href="mailto:<EMAIL>" className="text-purple-600 hover:text-purple-700"><EMAIL></a>
            </p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </>
  );
}
