import React from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { useSupabase } from '../../providers/SupabaseProvider';

export default function HostAuthDebug() {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const { userProfile, isHost, isAuthenticated, isLoading } = useSupabase();

  const debugInfo = {
    clerk: {
      isLoaded,
      isSignedIn,
      userId: user?.id,
      email: user?.primaryEmailAddress?.emailAddress,
      firstName: user?.firstName,
      lastName: user?.lastName,
    },
    supabase: {
      isLoading,
      isAuthenticated,
      isHost,
      userProfile,
    },
    localStorage: {
      registering_as_host: localStorage.getItem('registering_as_host'),
      user_type: localStorage.getItem('user_type'),
      auth_user_type: localStorage.getItem('auth_user_type'),
      auth_redirect_to: localStorage.getItem('auth_redirect_to'),
    },
    url: {
      pathname: window.location.pathname,
      search: window.location.search,
      hostname: window.location.hostname,
    }
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md max-h-96 overflow-auto z-50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">Host Auth Debug</h3>
        <button
          onClick={() => {
            const element = document.getElementById('host-auth-debug');
            if (element) element.style.display = 'none';
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          ×
        </button>
      </div>
      <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
      <div className="mt-2 space-y-1">
        <button
          onClick={() => {
            localStorage.setItem('debug_bypass_host_auth', 'true');
            window.location.reload();
          }}
          className="w-full text-xs bg-yellow-500 text-white px-2 py-1 rounded hover:bg-yellow-600"
        >
          Enable Debug Bypass
        </button>
        <button
          onClick={() => {
            localStorage.removeItem('debug_bypass_host_auth');
            localStorage.removeItem('registering_as_host');
            localStorage.removeItem('user_type');
            localStorage.removeItem('auth_user_type');
            window.location.reload();
          }}
          className="w-full text-xs bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600"
        >
          Clear All & Reload
        </button>
      </div>
    </div>
  );
}
