import { createAgentByType, AgentType } from '../agent-factory';

// Test case interface
export interface AgentTestCase {
  id: string;
  type: AgentType;
  description: string;
  input: string;
  expectedOutputContains?: string[];
  expectedOutputNotContains?: string[];
  maxTokens?: number;
}

// Test result interface
export interface AgentTestResult {
  testCase: AgentTestCase;
  output: string;
  passed: boolean;
  reason?: string;
  executionTime: number;
}

/**
 * Run a single test case against an AI agent
 */
export async function runAgentTest(testCase: AgentTestCase, tools: any[]): Promise<AgentTestResult> {
  const startTime = Date.now();
  
  try {
    // Create the agent
    const agent = await createAgentByType(testCase.type, tools);
    
    // Run the agent with the test input
    const result = await agent.invoke({
      input: testCase.input,
    });
    
    const output = result.output;
    const executionTime = Date.now() - startTime;
    
    // Check if the output contains expected strings
    let passed = true;
    let reason = '';
    
    if (testCase.expectedOutputContains && testCase.expectedOutputContains.length > 0) {
      for (const expectedString of testCase.expectedOutputContains) {
        if (!output.toLowerCase().includes(expectedString.toLowerCase())) {
          passed = false;
          reason = `Output does not contain expected string: "${expectedString}"`;
          break;
        }
      }
    }
    
    if (passed && testCase.expectedOutputNotContains && testCase.expectedOutputNotContains.length > 0) {
      for (const unexpectedString of testCase.expectedOutputNotContains) {
        if (output.toLowerCase().includes(unexpectedString.toLowerCase())) {
          passed = false;
          reason = `Output contains unexpected string: "${unexpectedString}"`;
          break;
        }
      }
    }
    
    return {
      testCase,
      output,
      passed,
      reason: passed ? undefined : reason,
      executionTime
    };
  } catch (error) {
    return {
      testCase,
      output: '',
      passed: false,
      reason: `Error: ${error instanceof Error ? error.message : String(error)}`,
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Run a batch of test cases against AI agents
 */
export async function runAgentTests(testCases: AgentTestCase[], tools: any[]): Promise<AgentTestResult[]> {
  const results: AgentTestResult[] = [];
  
  for (const testCase of testCases) {
    const result = await runAgentTest(testCase, tools);
    results.push(result);
  }
  
  return results;
}

// Example test cases for different agent types
export const exampleTestCases: AgentTestCase[] = [
  {
    id: 'sales-venue-inquiry',
    type: 'sales',
    description: 'User asking about venue options',
    input: 'I need a venue for a birthday party with about 30 people',
    expectedOutputContains: ['birthday', 'venue', '30'],
  },
  {
    id: 'sales-pricing-inquiry',
    type: 'sales',
    description: 'User asking about pricing',
    input: 'How much does it cost to book a venue?',
    expectedOutputContains: ['price', 'cost', 'hour'],
  },
  {
    id: 'host-listing-inquiry',
    type: 'host',
    description: 'Host asking about listing a venue',
    input: 'I have a backyard with a pool that I want to list',
    expectedOutputContains: ['backyard', 'pool', 'list'],
  },
  {
    id: 'host-pricing-advice',
    type: 'host',
    description: 'Host asking for pricing advice',
    input: 'How much should I charge for my venue?',
    expectedOutputContains: ['price', 'charge', 'recommend'],
  },
  {
    id: 'australian-spelling',
    type: 'sales',
    description: 'Check for Australian English spelling',
    input: 'Tell me how to organize a party',
    expectedOutputContains: ['organise'],
    expectedOutputNotContains: ['organize'],
  },
];
