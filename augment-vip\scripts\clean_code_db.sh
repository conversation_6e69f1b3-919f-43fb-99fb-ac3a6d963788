#!/bin/bash

# Function to detect operating system
detect_os() {
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "linux"
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "macos"
  elif [[ "$OSTYPE" == "cygwin"* || "$OSTYPE" == "msys"* || "$OSTYPE" == "win32"* ]]; then
    echo "windows"
  else
    echo "unknown"
  fi
}

# Function to find VS Code database files
find_vscode_databases() {
  local os=$1
  local db_files=()

  case $os in
    linux)
      db_files=($(find ~/.config/Code -name "*.sqlite" -type f))
      ;;
    macos)
      db_files=($(find ~/Library/Application\ Support/Code -name "*.sqlite" -type f))
      ;;
    windows)
      # On Windows, we need to handle paths with backslashes
      db_files=($(find /mnt/c/Users/"$(whoami)"/AppData/Roaming/Code -name "*.sqlite" -type f))
      ;;
    *)
      echo "[WARNING] Unsupported operating system: $os"
      return 1
      ;;
  esac

  if [ ${#db_files[@]} -eq 0 ]; then
    echo "[WARNING] No database files found"
    return 1
  fi

  echo "${db_files[@]}"
}

# Function to create backups
create_backups() {
  local db_files=("$@")
  local backup_dir="data/backups/$(date +%Y%m%d%H%M%S)"

  mkdir -p "$backup_dir"

  for db_file in "${db_files[@]}"; do
    cp "$db_file" "$backup_dir/$(basename "$db_file")"
    echo "[INFO] Created backup of $db_file at $backup_dir/$(basename "$db_file")"
  done
}

# Function to clean databases
clean_databases() {
  local db_files=("$@")

  for db_file in "${db_files[@]}"; do
    echo "[INFO] Cleaning database: $db_file"
    sqlite3 "$db_file" "DELETE FROM KeyValue WHERE value LIKE '%augment%';"
    echo "[INFO] Cleaned database: $db_file"
  done
}

# Main script execution
OS=$(detect_os)
echo "[INFO] Detected operating system: $OS"

DB_FILES=$(find_vscode_databases "$OS")
if [ $? -ne 0 ]; then
  exit 1
fi

echo "[INFO] Found database files: $DB_FILES"

create_backups $DB_FILES
clean_databases $DB_FILES

echo "[INFO] Database cleaning completed successfully."
