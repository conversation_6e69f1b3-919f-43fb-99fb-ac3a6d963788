import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom'; // Import jest-dom matchers
import PreciseNSWAddressLookup from './PreciseNSWAddressLookup';
import { loadGeoJSON } from '../../utils/spatialUtils';
import { getCurfewInfo } from '../../lib/nsw-party-planning/curfew-api';

// Mock the spatialUtils
jest.mock('../../utils/spatialUtils');

// Mock the getCurfewInfo to control API response for testing
jest.mock('../../lib/nsw-party-planning/curfew-api', () => ({
  getCurfewInfo: jest.fn(),
}));

const mockGetCurfewInfo = getCurfewInfo as jest.Mock;

describe('PreciseNSWAddressLookup', () => {
  beforeEach(() => {
    (loadGeoJSON as jest.Mock).mockImplementation((url: string) => {
      return Promise.resolve({
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: {
              type: 'Polygon',
              coordinates: [[[151.2, -33.8], [151.3, -33.8], [151.3, -33.9], [151.2, -33.9]]]
            },
            properties: {
              LGA_NAME: 'Sydney'
            }
          }
        ]
      });
    });

    // Reset mock before each test
    mockGetCurfewInfo.mockReset();
  });

  it('should render without errors', () => {
    render(<PreciseNSWAddressLookup />);
    expect(screen.getByText('NSW Party Planning & Noise Guide')).toBeInTheDocument();
  });

  it('should display address information when an address is entered and API returns data', async () => {
    const mockCurfewData = {
      property_type: 'Apartment/Unit',
      zone_code: 'R3',
      zone_name: 'Medium Density Residential',
      lga_name: 'The Hills Shire Council',
      curfew_start: '23:00:00',
      curfew_end: '07:00:00',
      bass_restriction_start: '21:00:00',
      bass_restriction_end: '08:00:00',
      outdoor_cutoff: '21:00:00',
      special_conditions: 'Strata bylaws may impose stricter rules than council regulations',
      confidence: { level: 'High', score: 4 },
      recommendations: []
    };

    mockGetCurfewInfo.mockResolvedValue(mockCurfewData);

    render(<PreciseNSWAddressLookup />);
    const addressInput = screen.getByLabelText(/Enter Address/i);
    const searchButton = screen.getByRole('button', { name: /Search/i });

    fireEvent.change(addressInput, { target: { value: '9/1 Woodlands Street, Baulkham Hills, NSW 2153' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(mockGetCurfewInfo).toHaveBeenCalledWith({
        address: '9/1 Woodlands Street, Baulkham Hills, NSW 2153',
        propertyType: null, // Assuming property type is not determined by this component
        zoneCode: expect.any(String), // Zone code will be determined by spatialUtils mock
        lgaName: 'Sydney' // LGA will be determined by spatialUtils mock
      });
      expect(screen.getByText('Property Type: Apartment/Unit')).toBeInTheDocument();
      expect(screen.getByText('Zoning: R3 (Medium Density Residential)')).toBeInTheDocument();
      expect(screen.getByText('LGA: The Hills Shire Council')).toBeInTheDocument();
      expect(screen.getByText('Noise curfew: 11:00 PM to 7:00 AM')).toBeInTheDocument();
      expect(screen.getByText('Bass Restriction: 9:00 PM to 8:00 AM')).toBeInTheDocument();
      expect(screen.getByText('Outdoor Cutoff: 9:00 PM')).toBeInTheDocument();
      expect(screen.getByText('Special Conditions: Strata bylaws may impose stricter rules than council regulations')).toBeInTheDocument();
    });
  });

  it('should display an error message if the API call fails', async () => {
    mockGetCurfewInfo.mockRejectedValue(new Error('API Error'));

    render(<PreciseNSWAddressLookup />);
    const addressInput = screen.getByLabelText(/Enter Address/i);
    const searchButton = screen.getByRole('button', { name: /Search/i });

    fireEvent.change(addressInput, { target: { value: 'Invalid Address' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText('Error fetching curfew information.')).toBeInTheDocument();
    });
  });
});
