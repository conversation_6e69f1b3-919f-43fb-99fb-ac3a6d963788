#!/usr/bin/env node

/**
 * Local Page Testing Script
 * 
 * Tests that all critical compliance pages are working correctly on localhost
 * and that our 404 fixes are properly implemented.
 */

import fetch from 'node-fetch';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Test URLs
const testUrls = [
  { path: '/', description: 'Homepage' },
  { path: '/contact', description: 'Contact Page (Australian Compliance)' },
  { path: '/privacy', description: 'Privacy Policy (Comprehensive)' },
  { path: '/help', description: 'Help Center' },
  { path: '/safety', description: 'Safety Guidelines' },
  { path: '/terms', description: 'Terms of Service' },
  { path: '/find-venues', description: 'Find Venues' },
  { path: '/list-space', description: 'List Space' },
  { path: '/venue/venue-001', description: 'Sample Venue Page' },
  { path: '/locations/sydney-cbd', description: 'Sydney CBD Location' }
];

// Check if dev server is running
async function checkDevServer(baseUrl) {
  try {
    const response = await fetch(baseUrl);
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Test a single URL
async function testUrl(baseUrl, urlInfo) {
  try {
    const fullUrl = `${baseUrl}${urlInfo.path}`;
    const response = await fetch(fullUrl);
    
    if (response.ok) {
      const content = await response.text();
      
      // Check if it's a 404 page
      const is404 = content.includes('404 - Page Not Found') || 
                   content.includes('The page you\'re looking for doesn\'t exist');
      
      // Check if it has proper content
      const hasContent = content.length > 1000; // Reasonable content length
      const hasTitle = content.includes('<title>') && !content.includes('<title>HouseGoing - Find Your Perfect Living Space</title>');
      
      return {
        success: true,
        status: response.status,
        is404,
        hasContent,
        hasTitle,
        contentLength: content.length,
        url: fullUrl
      };
    } else {
      return {
        success: false,
        status: response.status,
        url: fullUrl
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      url: `${baseUrl}${urlInfo.path}`
    };
  }
}

// Main testing function
async function testLocalPages() {
  log('🧪 HouseGoing Local Page Testing', 'bold');
  log('================================\n', 'bold');
  
  // Try different possible ports
  const possiblePorts = [5176, 5175, 5173, 3000];
  let baseUrl = null;
  
  log('🔍 Detecting development server...', 'blue');
  
  for (const port of possiblePorts) {
    const testUrl = `http://localhost:${port}`;
    const isRunning = await checkDevServer(testUrl);
    
    if (isRunning) {
      baseUrl = testUrl;
      log(`✅ Found dev server at: ${baseUrl}`, 'green');
      break;
    } else {
      log(`❌ No server at: ${testUrl}`, 'red');
    }
  }
  
  if (!baseUrl) {
    log('\n🚨 ERROR: No development server found!', 'red');
    log('Please start the dev server with: npm run dev', 'yellow');
    return;
  }
  
  log('\n📋 Testing Critical Pages:', 'bold');
  log('─'.repeat(50));
  
  let passedTests = 0;
  let totalTests = testUrls.length;
  const results = [];
  
  for (const urlInfo of testUrls) {
    const result = await testUrl(baseUrl, urlInfo);
    results.push({ ...result, ...urlInfo });
    
    if (result.success) {
      if (result.is404) {
        log(`❌ ${urlInfo.description.padEnd(35)} → 404 ERROR`, 'red');
        log(`   URL: ${result.url}`, 'red');
      } else if (result.hasContent && result.hasTitle) {
        log(`✅ ${urlInfo.description.padEnd(35)} → OK (${(result.contentLength/1024).toFixed(1)} KB)`, 'green');
        passedTests++;
      } else {
        log(`⚠️  ${urlInfo.description.padEnd(35)} → PARTIAL (${(result.contentLength/1024).toFixed(1)} KB)`, 'yellow');
        if (!result.hasTitle) log(`   Missing proper title`, 'yellow');
        if (!result.hasContent) log(`   Content too short`, 'yellow');
        passedTests += 0.5; // Partial credit
      }
    } else {
      log(`❌ ${urlInfo.description.padEnd(35)} → ERROR (${result.status || 'Network Error'})`, 'red');
      if (result.error) log(`   Error: ${result.error}`, 'red');
    }
  }
  
  // Summary
  log('\n📊 TEST SUMMARY:', 'bold');
  log('─'.repeat(50));
  
  const successRate = (passedTests / totalTests) * 100;
  
  log(`📄 Total Pages Tested: ${totalTests}`, 'blue');
  log(`✅ Passed Tests: ${Math.floor(passedTests)}`, passedTests === totalTests ? 'green' : 'yellow');
  log(`📈 Success Rate: ${successRate.toFixed(1)}%`, 
      successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
  
  // Specific compliance checks
  log('\n🏛️ COMPLIANCE PAGE STATUS:', 'bold');
  const compliancePages = results.filter(r => 
    r.path === '/contact' || r.path === '/privacy' || r.path === '/help' || r.path === '/safety'
  );
  
  compliancePages.forEach(page => {
    const status = page.success && !page.is404 && page.hasContent ? '✅' : '❌';
    const statusText = page.success && !page.is404 && page.hasContent ? 'WORKING' : 'FAILED';
    log(`${status} ${page.description.padEnd(35)} → ${statusText}`, 
        statusText === 'WORKING' ? 'green' : 'red');
  });
  
  // Recommendations
  log('\n💡 RECOMMENDATIONS:', 'bold');
  
  if (successRate >= 90) {
    log('🎉 Excellent! All pages are working correctly locally.', 'green');
    log('📤 Ready to deploy to production to eliminate 404 errors.', 'green');
  } else {
    log('⚠️  Some pages need attention:', 'yellow');
    
    const failedPages = results.filter(r => !r.success || r.is404 || !r.hasContent);
    failedPages.forEach(page => {
      if (page.is404) {
        log(`   • ${page.path} returns 404 - check routing configuration`, 'yellow');
      } else if (!page.success) {
        log(`   • ${page.path} failed to load - check for errors`, 'yellow');
      } else if (!page.hasContent) {
        log(`   • ${page.path} has minimal content - check static files`, 'yellow');
      }
    });
  }
  
  log('\n🚀 NEXT STEPS:', 'bold');
  log('1. Verify all compliance pages display proper content', 'blue');
  log('2. Check that pages auto-redirect to SPA after 3 seconds', 'blue');
  log('3. Deploy static HTML files to production server', 'blue');
  log('4. Test production URLs to confirm 404 elimination', 'blue');
  
  return {
    baseUrl,
    totalTests,
    passedTests,
    successRate,
    results
  };
}

// Run the tests
testLocalPages().catch(console.error);
