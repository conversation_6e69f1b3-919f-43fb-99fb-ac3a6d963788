export interface Venue {
  id: string;
  title: string;
  description: string;
  location: string;
  address?: string;
  price: number;
  capacity: number;
  rating: number;
  reviews: number;
  images: string[];
  amenities: string[];
  eventTypes: string[];
  instantBook?: boolean;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  host: {
    id: string;
    name: string;
    image: string;
    rating: number;
    verified?: boolean;
  };
  noiseRestrictions?: {
    curfewTime?: string; // e.g. "22:00"
    councilArea?: string;
    allowsOvernight?: boolean;
    notes?: string;
    windowsClosedAfter?: string; // e.g. "21:00"
    zoning?: 'residential' | 'commercial' | 'industrial' | 'mixed';
    residentialProximity?: 'adjacent' | 'nearby' | 'distant';
    soundproofing?: boolean;
    outdoorMusic?: {
      allowed: boolean;
      until?: string; // e.g. "22:00"
    };
  };
  partyScore?: {
    score: number; // 1-10 scale
    factors: string[];
  };
  // Search result metadata
  isExactMatch?: boolean;
  isSuggestion?: boolean;
  hoursAvailable?: number;
  distanceKm?: number;
  radiusCategory?: 'exact' | 'nearby' | 'extended' | 'distant';
}