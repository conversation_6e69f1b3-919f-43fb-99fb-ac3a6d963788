#!/usr/bin/env node

/**
 * SEO Audit Script for HouseGoing
 * Checks for common SEO issues and validates sitemap URLs
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { DOMParser } = require('xmldom');

const BASE_URL = 'https://housegoing.com.au';
const SITEMAP_PATH = path.join(__dirname, '../public/sitemap_comprehensive.xml');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Parse sitemap and extract URLs
function parseSitemap() {
  try {
    const sitemapContent = fs.readFileSync(SITEMAP_PATH, 'utf8');
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(sitemapContent, 'text/xml');
    
    const urls = [];
    const urlElements = xmlDoc.getElementsByTagName('url');
    
    for (let i = 0; i < urlElements.length; i++) {
      const locElement = urlElements[i].getElementsByTagName('loc')[0];
      const lastmodElement = urlElements[i].getElementsByTagName('lastmod')[0];
      const priorityElement = urlElements[i].getElementsByTagName('priority')[0];
      
      if (locElement) {
        urls.push({
          url: locElement.textContent,
          lastmod: lastmodElement ? lastmodElement.textContent : null,
          priority: priorityElement ? parseFloat(priorityElement.textContent) : null
        });
      }
    }
    
    return urls;
  } catch (error) {
    log(`Error parsing sitemap: ${error.message}`, 'red');
    return [];
  }
}

// Check if URL returns 200 status
function checkUrlStatus(url) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: 443,
      path: urlObj.pathname + urlObj.search,
      method: 'HEAD',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      resolve({
        url,
        status: res.statusCode,
        headers: res.headers
      });
    });

    req.on('error', (error) => {
      resolve({
        url,
        status: 'ERROR',
        error: error.message
      });
    });

    req.on('timeout', () => {
      resolve({
        url,
        status: 'TIMEOUT',
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

// Validate sitemap structure
function validateSitemapStructure(urls) {
  const issues = [];
  
  // Check for duplicate URLs
  const urlSet = new Set();
  const duplicates = [];
  
  urls.forEach(item => {
    if (urlSet.has(item.url)) {
      duplicates.push(item.url);
    } else {
      urlSet.add(item.url);
    }
  });
  
  if (duplicates.length > 0) {
    issues.push({
      type: 'DUPLICATE_URLS',
      message: `Found ${duplicates.length} duplicate URLs`,
      details: duplicates
    });
  }
  
  // Check for URLs with .html extensions
  const htmlUrls = urls.filter(item => item.url.includes('.html'));
  if (htmlUrls.length > 0) {
    issues.push({
      type: 'HTML_EXTENSIONS',
      message: `Found ${htmlUrls.length} URLs with .html extensions`,
      details: htmlUrls.map(item => item.url)
    });
  }
  
  // Check for old lastmod dates
  const oldDates = urls.filter(item => {
    if (!item.lastmod) return false;
    const lastmodDate = new Date(item.lastmod);
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    return lastmodDate < oneMonthAgo;
  });
  
  if (oldDates.length > 0) {
    issues.push({
      type: 'OLD_LASTMOD_DATES',
      message: `Found ${oldDates.length} URLs with lastmod dates older than 1 month`,
      details: oldDates.map(item => `${item.url} (${item.lastmod})`)
    });
  }
  
  // Check priority distribution
  const priorities = urls.map(item => item.priority).filter(p => p !== null);
  const highPriority = priorities.filter(p => p >= 0.8).length;
  const totalUrls = urls.length;
  
  if (highPriority / totalUrls > 0.3) {
    issues.push({
      type: 'TOO_MANY_HIGH_PRIORITY',
      message: `${highPriority} out of ${totalUrls} URLs have priority >= 0.8 (${Math.round(highPriority/totalUrls*100)}%)`,
      details: ['Consider reducing high priority URLs to focus on most important pages']
    });
  }
  
  return issues;
}

// Main audit function
async function runSEOAudit() {
  log('🔍 Starting SEO Audit for HouseGoing', 'blue');
  log('=====================================', 'blue');
  
  // Parse sitemap
  log('\n📄 Parsing sitemap...', 'yellow');
  const urls = parseSitemap();
  log(`Found ${urls.length} URLs in sitemap`, 'green');
  
  // Validate sitemap structure
  log('\n🔧 Validating sitemap structure...', 'yellow');
  const structureIssues = validateSitemapStructure(urls);
  
  if (structureIssues.length === 0) {
    log('✅ No structural issues found', 'green');
  } else {
    log(`❌ Found ${structureIssues.length} structural issues:`, 'red');
    structureIssues.forEach(issue => {
      log(`  • ${issue.type}: ${issue.message}`, 'red');
      if (issue.details.length <= 5) {
        issue.details.forEach(detail => log(`    - ${detail}`, 'red'));
      } else {
        issue.details.slice(0, 3).forEach(detail => log(`    - ${detail}`, 'red'));
        log(`    ... and ${issue.details.length - 3} more`, 'red');
      }
    });
  }
  
  // Check URL status codes (sample first 20 URLs to avoid rate limiting)
  log('\n🌐 Checking URL status codes (sample)...', 'yellow');
  const sampleUrls = urls.slice(0, 20);
  const statusChecks = await Promise.all(sampleUrls.map(item => checkUrlStatus(item.url)));
  
  const successfulUrls = statusChecks.filter(check => check.status === 200);
  const failedUrls = statusChecks.filter(check => check.status !== 200);
  
  log(`✅ ${successfulUrls.length}/${sampleUrls.length} URLs returned 200 status`, 'green');
  
  if (failedUrls.length > 0) {
    log(`❌ ${failedUrls.length} URLs had issues:`, 'red');
    failedUrls.forEach(check => {
      log(`  • ${check.url}: ${check.status} ${check.error || ''}`, 'red');
    });
  }
  
  // Summary
  log('\n📊 SEO Audit Summary', 'blue');
  log('===================', 'blue');
  log(`Total URLs in sitemap: ${urls.length}`, 'blue');
  log(`Structural issues: ${structureIssues.length}`, structureIssues.length > 0 ? 'red' : 'green');
  log(`URL status check: ${successfulUrls.length}/${sampleUrls.length} successful`, failedUrls.length > 0 ? 'red' : 'green');
  
  // Recommendations
  log('\n💡 Recommendations', 'yellow');
  log('==================', 'yellow');
  log('1. Remove duplicate URLs from sitemap', 'yellow');
  log('2. Remove .html extensions from URLs', 'yellow');
  log('3. Update lastmod dates to reflect actual content changes', 'yellow');
  log('4. Ensure all URLs return 200 status codes', 'yellow');
  log('5. Implement proper canonical tags on all pages', 'yellow');
  log('6. Add structured data for better E-E-A-T signals', 'yellow');
  
  log('\n✅ SEO Audit Complete!', 'green');
}

// Run the audit
if (require.main === module) {
  runSEOAudit().catch(error => {
    log(`Error running SEO audit: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runSEOAudit, parseSitemap, validateSitemapStructure };
