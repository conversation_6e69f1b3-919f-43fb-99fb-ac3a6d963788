/**
 * Test Sitemap Script
 *
 * This script serves the sitemap.xml file locally to test it.
 */

import express from 'express';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');
const publicDir = path.join(rootDir, 'public');
const sitemapIndexPath = path.join(publicDir, 'sitemap_index.xml');
const sitemapMainPath = path.join(publicDir, 'sitemap_main.xml');

// Create a simple Express server
const app = express();
const PORT = 3000;

// Helper function to serve XML files with the correct content type
const serveXmlFile = (filePath, res) => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');

    // Set the correct content type for XML
    res.setHeader('Content-Type', 'application/xml');
    res.send(content);
  } else {
    res.status(404).send('File not found');
  }
};

// Serve the sitemap_index.xml file with the correct content type
app.get('/sitemap_index.xml', (req, res) => {
  serveXmlFile(sitemapIndexPath, res);
});

// Serve the sitemap_main.xml file with the correct content type
app.get('/sitemap_main.xml', (req, res) => {
  serveXmlFile(sitemapMainPath, res);
});

// For backward compatibility, serve the sitemap.xml file
app.get('/sitemap.xml', (req, res) => {
  serveXmlFile(sitemapIndexPath, res);
});

// Serve a simple HTML page to test the sitemap
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Sitemap Test</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow: auto; max-height: 300px; }
        .tabs { display: flex; margin-bottom: 20px; }
        .tab { padding: 10px 20px; cursor: pointer; border: 1px solid #ccc; border-bottom: none; }
        .tab.active { background: #f5f5f5; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
      </style>
    </head>
    <body>
      <h1>Sitemap Test</h1>

      <div class="tabs">
        <div class="tab active" onclick="showTab('index')">Sitemap Index</div>
        <div class="tab" onclick="showTab('main')">Main Sitemap</div>
      </div>

      <div id="index-content" class="tab-content active">
        <h2>Sitemap Index</h2>
        <p>Your sitemap index is being served at: <a href="/sitemap_index.xml" target="_blank">/sitemap_index.xml</a></p>
        <p>This is the file you should submit to Google Search Console.</p>

        <h3>Content:</h3>
        <pre>${fs.existsSync(sitemapIndexPath) ?
          fs.readFileSync(sitemapIndexPath, 'utf8')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;') :
          'Sitemap index file not found'}</pre>
      </div>

      <div id="main-content" class="tab-content">
        <h2>Main Sitemap</h2>
        <p>Your main sitemap is being served at: <a href="/sitemap_main.xml" target="_blank">/sitemap_main.xml</a></p>
        <p>This file contains all the URLs and is referenced by the sitemap index.</p>

        <h3>Content:</h3>
        <pre>${fs.existsSync(sitemapMainPath) ?
          fs.readFileSync(sitemapMainPath, 'utf8')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;') :
          'Main sitemap file not found'}</pre>
      </div>

      <h2>How to Test:</h2>
      <ol>
        <li>Open <a href="/sitemap_index.xml" target="_blank">/sitemap_index.xml</a> in a new tab</li>
        <li>Check that it displays as XML, not HTML</li>
        <li>Verify that the Content-Type header is set to "application/xml"</li>
        <li>Use the browser's "View Source" feature to confirm the raw XML</li>
        <li>Repeat the same checks for <a href="/sitemap_main.xml" target="_blank">/sitemap_main.xml</a></li>
      </ol>

      <h2>For Google Search Console:</h2>
      <p>Submit <strong>sitemap_index.xml</strong> to Google Search Console. Google will automatically follow the link to sitemap_main.xml.</p>

      <script>
        function showTab(tab) {
          // Hide all tab contents
          document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
          });

          // Deactivate all tabs
          document.querySelectorAll('.tab').forEach(tabEl => {
            tabEl.classList.remove('active');
          });

          // Show selected tab content
          document.getElementById(tab + '-content').classList.add('active');

          // Activate selected tab
          document.querySelectorAll('.tab').forEach(tabEl => {
            if (tabEl.textContent.toLowerCase().includes(tab)) {
              tabEl.classList.add('active');
            }
          });
        }
      </script>
    </body>
    </html>
  `);
});

// Start the server
app.listen(PORT, () => {
  console.log(`Sitemap test server running at http://localhost:${PORT}`);
  console.log(`View your sitemap at http://localhost:${PORT}/sitemap.xml`);
});
