<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />    <title>HouseGoing - Party Venues & Event Spaces | Find Perfect Venues in Australia</title>
    <meta name="description" content="HouseGoing is Australia's premier venue booking platform. Find and book the perfect party or event venue with noise restriction checks and verified hosts." />
    <meta name="keywords" content="HouseGoing, venue rental, party venues, event spaces, NSW party planning, noise restrictions, venue booking, Australia venues" />
    <meta name="author" content="HouseGoing" />
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://housegoing.com.au/" />
    <meta property="og:title" content="HouseGoing - Australia's Premier Venue Booking Platform" />
    <meta property="og:description" content="HouseGoing makes finding and booking the perfect venue simple. Discover party and event spaces with verified hosts across Australia." />
    <meta property="og:image" content="https://housegoing.com.au/og-image.svg" />    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://housegoing.com.au/" />
    <meta name="twitter:title" content="HouseGoing - Australia's Premier Venue Booking Platform" />
    <meta name="twitter:description" content="HouseGoing makes finding and booking the perfect venue simple. Discover party and event spaces with verified hosts across Australia." />
    <meta name="twitter:image" content="https://housegoing.com.au/og-image.svg" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://housegoing.com.au/" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="HouseGoing" />
    <meta name="application-name" content="HouseGoing" />
    <meta name="msapplication-TileColor" content="#8B5CF6" />
    <meta name="msapplication-config" content="/browserconfig.xml" />    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icons/favicon-16x16.png" />
    <link rel="mask-icon" href="/images/icons/safari-pinned-tab.svg" color="#8B5CF6" />
    
    <!-- Structured data for better SEO and brand visibility -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "HouseGoing",
      "url": "https://housegoing.com.au",
      "logo": "https://housegoing.com.au/images/logo.png",
      "sameAs": [
        "https://facebook.com/housegoinghq",
        "https://instagram.com/housegoinghq",
        "https://linkedin.com/company/housegoing"
      ]
    }
    </script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "HouseGoing",
      "url": "https://housegoing.com.au",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://housegoing.com.au/find-venues?search={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <!-- Splash Screens for iOS -->
    <link rel="apple-touch-startup-image" href="/images/splash/launch-640x1136.png" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/images/splash/launch-750x1334.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/images/splash/launch-1242x2208.png" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/images/splash/launch-1125x2436.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/images/splash/launch-1536x2048.png" media="(min-device-width: 768px) and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) and (orientation: portrait)" />

    <!-- Load critical fonts -->
    <link rel="preload" href="https://rsms.me/inter/inter.css" as="style" onload="this.onload=null;this.rel='stylesheet'" />
    <link rel="preload" href="https://api.fontshare.com/v2/css?f[]=clash-display@600,700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'" />
    <noscript>
      <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
      <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@600,700&display=swap" />
    </noscript>
    <!-- Import map for module resolution -->
    <!-- Add ES module shim for better browser compatibility -->
    <script async src="https://ga.jspm.io/npm:es-module-shims@1.8.2/dist/es-module-shims.js"></script>

    <script type="importmap">
    {
      "imports": {
        "date-fns": "https://cdn.jsdelivr.net/npm/date-fns@2.30.0/esm/index.js",
        "date-fns/": "https://cdn.jsdelivr.net/npm/date-fns@2.30.0/esm/",
        "react": "https://esm.sh/react@18.2.0",
        "react-dom": "https://esm.sh/react-dom@18.2.0",
        "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
        "@babel/runtime/helpers/esm/typeof": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/typeof",
        "@babel/runtime/helpers/esm/objectWithoutPropertiesLoose": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/objectWithoutPropertiesLoose",
        "@babel/runtime/helpers/esm/extends": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/extends",
        "@babel/runtime/helpers/esm/createForOfIteratorHelper": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/createForOfIteratorHelper",
        "@babel/runtime/helpers/esm/arrayLikeToArray": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/arrayLikeToArray",
        "@babel/runtime/helpers/esm/unsupportedIterableToArray": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/unsupportedIterableToArray",
        "@babel/runtime/helpers/esm/assertThisInitialized": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/assertThisInitialized",
        "@babel/runtime/helpers/esm/inherits": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/inherits",
        "@babel/runtime/helpers/esm/createSuper": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/createSuper",
        "@babel/runtime/helpers/esm/classCallCheck": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/classCallCheck",
        "@babel/runtime/helpers/esm/createClass": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/createClass",
        "@babel/runtime/helpers/esm/defineProperty": "https://esm.sh/@babel/runtime@7.23.9/helpers/esm/defineProperty"
      }
    }
    </script>

    <!-- Fallback styles in case of loading issues -->
    <style>
      body {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #f9fafb;
        color: #111827;
        margin: 0;
        padding: 0;
      }
      #root {
        min-height: 100vh;
      }
      .fallback-content {
        display: none;
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
        text-align: center;
      }
      .loading-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-size: 1.25rem;
      }
    </style>
  </head>
  <body>    <div id="root">
      <!-- Fallback loading indicator -->
      <div class="loading-indicator">
        <p>HouseGoing is loading...</p>
      </div>
    </div>
    <!-- Removed fallback content -->
    <div style="display: none;" id="fallback"></div>
    <script>

      // Only show fallback content if there's an actual error
      // We're removing the timeout that was showing the error message prematurely
      function showFallbackIfNeeded() {
        // Check if the app has loaded properly
        if (document.querySelector('#root').children.length > 1 &&
            !document.querySelector('#root').textContent.includes('Loading HouseGoing')) {
          // App has loaded, no need for fallback
          return;
        }
      }

      // Ensure loading indicator is removed when the window loads or app is initialized
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingIndicator = document.querySelector('.loading-indicator');
          if (loadingIndicator && document.querySelector('#root').children.length > 1) {
            loadingIndicator.remove();
          }
        }, 1000); // Give React a second to initialize
      });

      // Listen for app initialization events
      document.addEventListener('app-initialized', function(event) {
        const detail = event.detail || {};
        console.log('App initialization event received:', detail);

        // Remove loading indicator when app is initialized
        setTimeout(function() {
          const loadingIndicator = document.querySelector('.loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.remove();
          }
        }, 500);
      });

      // Set a maximum loading time
      setTimeout(function() {
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
          console.warn('Loading timeout reached - removing loading indicator');
          loadingIndicator.remove();
        }
      }, 15000); // 15 second maximum loading time

      // Catch global errors
      window.addEventListener('error', function(event) {
        console.error(`Global error: ${event.message}`);
      });
    </script>
    <!-- Module error handling -->
    <script>
      // Keep track of all errors
      const errors = new Set();

      // Function to clear cache (removed reload)
      function clearCache() {
        if ('caches' in window) {
          caches.keys().then(function(names) {
            for (let name of names) caches.delete(name);
          });
        }
      }

      // Force cache clear on page load to ensure latest code
      if ('caches' in window) {
        caches.keys().then(function(names) {
          for (let name of names) caches.delete(name);
        });
      }

      window.addEventListener('error', function(event) {
        // Just log errors to console without showing error UI
        console.error('Caught error:', event.message);
        errors.add(event.message);
      }, true);

      // Also catch unhandled promise rejections
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
        errors.add(`Promise error: ${event.reason}`);
      });
    </script>

    <!-- Preload critical modules -->
    <script type="module">
      // List of modules to preload
      const modulesToPreload = [
        '@babel/runtime/helpers/esm/typeof',
        '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose',
        '@babel/runtime/helpers/esm/extends',
        '@babel/runtime/helpers/esm/createForOfIteratorHelper',
        '@babel/runtime/helpers/esm/arrayLikeToArray',
        '@babel/runtime/helpers/esm/unsupportedIterableToArray',
        '@babel/runtime/helpers/esm/assertThisInitialized',
        '@babel/runtime/helpers/esm/inherits',
        '@babel/runtime/helpers/esm/createSuper',
        '@babel/runtime/helpers/esm/classCallCheck',
        '@babel/runtime/helpers/esm/createClass',
        '@babel/runtime/helpers/esm/defineProperty'
      ];

      // Preload all modules
      Promise.all(modulesToPreload.map(module => {
        return import(/* @vite-ignore */ module).catch(err => {
          console.warn(`Failed to preload module ${module}:`, err);
        });
      })).then(() => {
        console.log('Modules preloaded successfully');
      }).catch(err => {
        console.error('Error preloading modules:', err);
      });
    </script>

    <!-- Structured Data for Organization -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "HouseGoing",
        "url": "https://housegoing.com.au",
        "logo": "https://housegoing.com.au/images/housegoing-logo.svg",
        "sameAs": [
          "https://www.facebook.com/housegoing",
          "https://www.instagram.com/housegoing",
          "https://twitter.com/housegoing"
        ],
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Sydney",
          "addressRegion": "NSW",
          "postalCode": "2000",
          "addressCountry": "AU"
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+61-000-000-000",
          "contactType": "customer service",
          "email": "<EMAIL>"
        }
      }
    </script>

    <!-- Structured Data for Website -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "HouseGoing",
        "url": "https://housegoing.com.au",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://housegoing.com.au/find-venues?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>

    <!-- Service Worker Registration -->
    <script>
      // Register service worker for PWA functionality
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);

              // Check for updates
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New content available - just log it
                    console.log('New version available');
                  }
                });
              });
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }      // PWA capability is maintained but without the install prompt
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        // Just prevent the default prompt - no custom banner
        e.preventDefault();
        // Still store the event in case we want to use it programmatically later
        deferredPrompt = e;
        // No banner is shown automatically
      });

      // Install PWA function (kept for potential future manual trigger points)
      window.installPWA = async () => {
        if (deferredPrompt) {
          deferredPrompt.prompt();
          const { outcome } = await deferredPrompt.userChoice;
          console.log(`User response to the install prompt: ${outcome}`);
          deferredPrompt = null;
        }
      };

      // Track PWA usage
      window.addEventListener('appinstalled', (evt) => {
        console.log('PWA was installed');
        // Track installation event
        if (typeof gtag !== 'undefined') {
          gtag('event', 'pwa_install', {
            event_category: 'engagement',
            event_label: 'PWA Installation'
          });
        }
      });
    </script>

    <!-- Load the main application -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>