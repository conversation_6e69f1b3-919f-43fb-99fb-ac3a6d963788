import React from 'react';
import { useAuth, useUser, useClerk } from '@clerk/clerk-react';
import { CLERK_CONFIG } from '../../config/clerk';

export default function ClerkDebugInfo() {
  const { isLoaded: authLoaded, isSignedIn, userId } = useAuth();
  const { isLoaded: userLoaded, user } = useUser();
  const clerk = useClerk();

  // Only show in development or when explicitly requested
  const showDebug = window.location.search.includes('debug=true') || 
                   CLERK_CONFIG.developmentMode;

  if (!showDebug) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Clerk Debug Info</h3>
      
      <div className="space-y-1">
        <div>Auth Loaded: {authLoaded ? '✅' : '❌'}</div>
        <div>User Loaded: {userLoaded ? '✅' : '❌'}</div>
        <div>Signed In: {isSignedIn ? '✅' : '❌'}</div>
        <div>User ID: {userId || 'None'}</div>
        <div>User Email: {user?.primaryEmailAddress?.emailAddress || 'None'}</div>
        
        <hr className="my-2 border-gray-600" />
        
        <div>Publishable Key: {CLERK_CONFIG.publishableKey?.substring(0, 20)}...</div>
        <div>Domain: {CLERK_CONFIG.clerkDomain}</div>
        <div>Dev Mode: {CLERK_CONFIG.developmentMode ? '✅' : '❌'}</div>
        <div>Current URL: {window.location.href}</div>
        
        <hr className="my-2 border-gray-600" />
        
        <div>Clerk Loaded: {clerk.loaded ? '✅' : '❌'}</div>
        <div>Session: {clerk.session ? '✅' : '❌'}</div>
        
        {user && (
          <div className="mt-2">
            <div className="font-semibold">User Details:</div>
            <div>Name: {user.firstName} {user.lastName}</div>
            <div>Created: {new Date(user.createdAt || '').toLocaleString()}</div>
            <div>Role: {user.publicMetadata?.role || 'None'}</div>
          </div>
        )}
      </div>
      
      <button 
        onClick={() => window.location.search = window.location.search.replace('debug=true', '')}
        className="mt-2 text-xs bg-gray-700 px-2 py-1 rounded"
      >
        Hide Debug
      </button>
    </div>
  );
}
