/**
 * Property Lifecycle Service
 * 
 * Handles the complete property submission lifecycle:
 * - Initial submission
 * - Admin review and feedback
 * - Owner edits and resubmission
 * - Final approval and listing
 */

import { getSupabaseClient } from '../lib/supabase-client';

export interface PropertySubmission {
  id: string;
  name: string;
  address: string;
  type: string;
  description: string;
  maxGuests: number;
  price: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  ownerId: string;
  ownerEmail: string;
  ownerName: string;
  rejection_reason?: string;
  admin_notes?: string;
  images?: string[];
  amenities?: string[];
  approved_by?: string;
  approved_at?: string;
  resubmission_count?: number;
}

/**
 * Get all properties for a specific owner
 */
export async function getOwnerProperties(ownerId: string): Promise<PropertySubmission[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('ownerId', ownerId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching owner properties:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception getting owner properties:', error);
    return [];
  }
}

/**
 * Get a specific property by ID for the owner
 */
export async function getOwnerProperty(propertyId: string, ownerId: string): Promise<PropertySubmission | null> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('id', propertyId)
      .eq('ownerId', ownerId)
      .single();

    if (error) {
      console.error('Error fetching owner property:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception getting owner property:', error);
    return null;
  }
}

/**
 * Update property details (for editing rejected properties)
 */
export async function updatePropertySubmission(
  propertyId: string, 
  ownerId: string, 
  updates: Partial<PropertySubmission>
): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    const { error } = await supabase
      .from('property_submissions')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
        // Increment resubmission count if status is changing to pending
        ...(updates.status === 'pending' && {
          resubmission_count: supabase.rpc('increment_resubmission_count', { property_id: propertyId })
        })
      })
      .eq('id', propertyId)
      .eq('ownerId', ownerId);

    if (error) {
      console.error('Error updating property submission:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception updating property submission:', error);
    return false;
  }
}

/**
 * Resubmit a rejected property for review
 */
export async function resubmitProperty(
  propertyId: string, 
  ownerId: string, 
  updates: Partial<PropertySubmission>
): Promise<boolean> {
  try {
    const success = await updatePropertySubmission(propertyId, ownerId, {
      ...updates,
      status: 'pending',
      rejection_reason: undefined, // Clear previous rejection reason
      admin_notes: undefined // Clear previous admin notes
    });

    if (success) {
      // Send notification email to admins about resubmission
      await sendResubmissionNotification(propertyId, ownerId);
    }

    return success;
  } catch (error) {
    console.error('Exception resubmitting property:', error);
    return false;
  }
}

/**
 * Approve a property and move it to live listings
 */
export async function approveProperty(
  propertyId: string, 
  adminEmail: string, 
  adminNotes?: string
): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    // Update property status
    const { error: updateError } = await supabase
      .from('property_submissions')
      .update({
        status: 'approved',
        approved_by: adminEmail,
        approved_at: new Date().toISOString(),
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', propertyId);

    if (updateError) {
      console.error('Error approving property:', updateError);
      return false;
    }

    // Copy to live venues table
    const { data: property, error: fetchError } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('id', propertyId)
      .single();

    if (fetchError || !property) {
      console.error('Error fetching approved property:', fetchError);
      return false;
    }

    // Insert into venues table
    const { error: insertError } = await supabase
      .from('venues')
      .insert({
        id: property.id,
        name: property.name,
        address: property.address,
        type: property.type,
        description: property.description,
        maxGuests: property.maxGuests,
        price: property.price,
        images: property.images,
        amenities: property.amenities,
        ownerId: property.ownerId,
        ownerEmail: property.ownerEmail,
        ownerName: property.ownerName,
        created_at: property.created_at,
        updated_at: new Date().toISOString(),
        status: 'active'
      });

    if (insertError) {
      console.error('Error creating live venue:', insertError);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception approving property:', error);
    return false;
  }
}

/**
 * Get live venues for an owner
 */
export async function getOwnerVenues(ownerId: string): Promise<any[]> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .eq('ownerId', ownerId)
      .eq('status', 'active')
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching owner venues:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception getting owner venues:', error);
    return [];
  }
}

/**
 * Send notification to admins about property resubmission
 */
async function sendResubmissionNotification(propertyId: string, ownerId: string): Promise<void> {
  try {
    // Get property details
    const property = await getOwnerProperty(propertyId, ownerId);
    if (!property) return;

    // Send email notification to admin team
    const emailData = {
      to: ['<EMAIL>', '<EMAIL>'],
      subject: `Property Resubmitted for Review - ${property.name}`,
      template: 'property-resubmission',
      data: {
        propertyName: property.name,
        propertyAddress: property.address,
        ownerName: property.ownerName,
        ownerEmail: property.ownerEmail,
        resubmissionCount: (property.resubmission_count || 0) + 1,
        adminUrl: `https://housegoing.com.au/admin/submissions/${propertyId}`
      }
    };

    // Send via email service
    const response = await fetch('https://housegoing-email-service.onrender.com/send-admin-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    });

    if (!response.ok) {
      console.error('Failed to send resubmission notification');
    }
  } catch (error) {
    console.error('Error sending resubmission notification:', error);
  }
}

/**
 * Get property statistics for owner dashboard
 */
export async function getOwnerPropertyStats(ownerId: string): Promise<{
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  live: number;
}> {
  try {
    const [submissions, venues] = await Promise.all([
      getOwnerProperties(ownerId),
      getOwnerVenues(ownerId)
    ]);

    return {
      total: submissions.length,
      pending: submissions.filter(p => p.status === 'pending').length,
      approved: submissions.filter(p => p.status === 'approved').length,
      rejected: submissions.filter(p => p.status === 'rejected').length,
      live: venues.length
    };
  } catch (error) {
    console.error('Error getting owner property stats:', error);
    return {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      live: 0
    };
  }
}

/**
 * Check if property can be edited (only rejected properties can be edited)
 */
export function canEditProperty(property: PropertySubmission): boolean {
  return property.status === 'rejected';
}

/**
 * Check if property can be managed (only approved properties can be managed)
 */
export function canManageProperty(property: PropertySubmission): boolean {
  return property.status === 'approved';
}

/**
 * Get property action label based on status
 */
export function getPropertyActionLabel(property: PropertySubmission): string {
  switch (property.status) {
    case 'pending':
      return 'View Details';
    case 'approved':
      return 'Manage Property';
    case 'rejected':
      return 'Edit & Resubmit';
    default:
      return 'View';
  }
}

/**
 * Get property status color classes
 */
export function getPropertyStatusColor(status: string): string {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'approved':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}
