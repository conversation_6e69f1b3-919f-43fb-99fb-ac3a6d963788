import React from 'react';
import { Star } from 'lucide-react';
import { Review } from '../../types/review';
import { formatDate } from '../../utils/dates';

interface ReviewListProps {
  reviews: Review[];
}

export default function ReviewList({ reviews }: ReviewListProps) {
  return (
    <div className="space-y-6">
      {reviews.map((review) => (
        <div key={review.id} className="border-b pb-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-3">
              <img
                src={review.user.image}
                alt={review.user.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <h4 className="font-semibold">{review.user.name}</h4>
                <span className="text-sm text-gray-500">
                  {formatDate(review.createdAt)}
                </span>
              </div>
            </div>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400 fill-current" />
              <span className="ml-1 font-medium">{review.rating}</span>
            </div>
          </div>
          <p className="text-gray-600">{review.comment}</p>
        </div>
      ))}
    </div>
  );
}