// Import the centralized Supabase client to prevent multiple GoTrueClient instances
import { supabase } from '../supabase-client'

export interface CurfewInfo {
  propertyType: string
  zoneCode: string
  zoneName: string
  lgaName: string
  weekdayCurfew: {
    start: string
    end: string
  }
  weekendCurfew: {
    start: string
    end: string
  }
  bassRestriction: {
    weekday: string
    weekend: string
  }
  outdoorCutoff: {
    weekday: string
    weekend: string
  }
  specialCondition: string
  isHoliday: boolean
  holidayName?: string
}

export interface CurfewInfoParams {
  address: string;
  propertyType?: string | null;
  date?: string | null;
  zoneCode?: string | null;
  zoneName?: string | null;
  lgaName?: string | null;
}

export const getCurfewInfo = async (params: string | CurfewInfoParams): Promise<CurfewInfo | any> => {
  // Handle both string and object parameters for backward compatibility
  const address = typeof params === 'string' ? params : params.address;
  const propertyType = typeof params === 'string' ? 'Residential' : (params.propertyType || 'Residential');
  const zoneCode = typeof params === 'string' ? null : params.zoneCode;
  const zoneName = typeof params === 'string' ? null : params.zoneName;
  const lgaName = typeof params === 'string' ? null : params.lgaName;

  try {
    console.log('Fetching curfew info for address:', address);

    // Try to get data from Supabase
    const { data, error } = await supabase
      .from('nsw_curfew_zones')
      .select('*')
      .eq('address', address)
      .single();

    if (error) {
      console.warn('Supabase query error:', error);
      // Don't throw, continue to fallback
    }

    // If we have data from Supabase, use it
    if (data) {
      console.log('Found curfew data in database:', data);
      return {
        propertyType: data.property_type,
        zoneCode: data.zone_code,
        zoneName: data.zone_name,
        lgaName: data.lga_name,
        weekdayCurfew: {
          start: data.weekday_curfew_start,
          end: data.weekday_curfew_end
        },
        weekendCurfew: {
          start: data.weekend_curfew_start,
          end: data.weekend_curfew_end
        },
        bassRestriction: {
          weekday: data.weekday_bass_restriction,
          weekend: data.weekend_bass_restriction
        },
        outdoorCutoff: {
          weekday: data.weekday_outdoor_cutoff,
          weekend: data.weekend_outdoor_cutoff
        },
        specialCondition: data.special_condition,
        isHoliday: data.is_holiday,
        holidayName: data.holiday_name
      };
    }

    // If no data from Supabase, use fallback data based on address patterns
    console.log('No data found in database, using fallback data');

    // Extract potential LGA from address
    const lowerAddress = address.toLowerCase();
    let detectedLga = '';
    let detectedZoneCode = '';
    let detectedZoneName = '';

    // If we have zoning information provided, use it as the highest priority
    if (zoneCode && zoneName) {
      console.log('Using provided zoning information:', zoneCode, zoneName);
      detectedZoneCode = zoneCode;
      detectedZoneName = zoneName;
    } else {
      // Special case for James Ruse Drive in Rosehill - highest priority
      if (lowerAddress.includes('james ruse drive') && lowerAddress.includes('rosehill')) {
        console.log('API: Special case detected for James Ruse Drive in Rosehill');
        detectedZoneCode = 'IN1';
        detectedZoneName = 'General Industrial';
      }
      // Detect zone type from address keywords
      else if (lowerAddress.includes('rosehill') || lowerAddress.includes('camellia') ||
          lowerAddress.includes('james ruse drive') || lowerAddress.includes('grand avenue')) {
        detectedZoneCode = 'IN1';
        detectedZoneName = 'General Industrial';
        console.log('Detected industrial area from address:', lowerAddress);
      } else if (lowerAddress.includes('industrial') || lowerAddress.includes('factory')) {
        detectedZoneCode = 'IN1';
        detectedZoneName = 'General Industrial';
      } else if (lowerAddress.includes('commercial') || lowerAddress.includes('business') || lowerAddress.includes('office')) {
        detectedZoneCode = 'B3';
        detectedZoneName = 'Commercial Core';
      } else if (lowerAddress.includes('apartment') || lowerAddress.includes('unit') || lowerAddress.includes('flat')) {
        detectedZoneCode = 'R4';
        detectedZoneName = 'High Density Residential';
      } else if (lowerAddress.includes('parramatta') &&
                (lowerAddress.includes('church street') || lowerAddress.includes('phillip street'))) {
        detectedZoneCode = 'B4';
        detectedZoneName = 'Mixed Use';
      } else {
        detectedZoneCode = 'R2';
        detectedZoneName = 'Low Density Residential';
      }
    }

    // Try to detect LGA from address
    const lgaPatterns = [
      'sydney', 'parramatta', 'blacktown', 'liverpool', 'penrith',
      'campbelltown', 'fairfield', 'bankstown', 'hornsby', 'sutherland',
      'newcastle', 'wollongong', 'gosford', 'wyong', 'lake macquarie',
      'blue mountains', 'hawkesbury', 'camden', 'wollondilly', 'kiama',
      'shellharbour', 'shoalhaven', 'wingecarribee', 'goulburn'
    ];

    for (const lga of lgaPatterns) {
      if (lowerAddress.includes(lga)) {
        detectedLga = lga.charAt(0).toUpperCase() + lga.slice(1);
        break;
      }
    }

    // If we have a provided LGA, use it
    if (lgaName) {
      detectedLga = lgaName;
    }

    // If still no LGA, use a default
    if (!detectedLga) {
      detectedLga = 'Sydney';
    }

    // Return fallback data
    return {
      propertyType: propertyType,
      zoneCode: detectedZoneCode,
      zoneName: detectedZoneName,
      lgaName: detectedLga,
      weekdayCurfew: {
        start: '22:00:00',
        end: '07:00:00'
      },
      weekendCurfew: {
        start: '23:00:00',
        end: '08:00:00'
      },
      bassRestriction: {
        weekday: '20:00:00',
        weekend: '21:00:00'
      },
      outdoorCutoff: {
        weekday: '21:00:00',
        weekend: '22:00:00'
      },
      specialCondition: 'Standard noise restrictions apply',
      isHoliday: false,
      curfew_start: '22:00:00',
      curfew_end: '07:00:00',
      property_type: propertyType,
      zone_code: detectedZoneCode,
      zone_name: detectedZoneName,
      lga_name: detectedLga
    };
  } catch (error) {
    console.error('Error fetching curfew info:', error);

    // Return default fallback data even if everything fails
    return {
      propertyType: 'Residential',
      zoneCode: 'R2',
      zoneName: 'Low Density Residential',
      lgaName: 'Sydney',
      weekdayCurfew: {
        start: '22:00:00',
        end: '07:00:00'
      },
      weekendCurfew: {
        start: '23:00:00',
        end: '08:00:00'
      },
      bassRestriction: {
        weekday: '20:00:00',
        weekend: '21:00:00'
      },
      outdoorCutoff: {
        weekday: '21:00:00',
        weekend: '22:00:00'
      },
      specialCondition: 'Standard noise restrictions apply',
      isHoliday: false,
      curfew_start: '22:00:00',
      curfew_end: '07:00:00',
      property_type: 'Residential',
      zone_code: 'R2',
      zone_name: 'Low Density Residential',
      lga_name: 'Sydney'
    };
  }
}

export const submitCurfewRequest = async (requestData: {
  address: string
  date: string
}): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('curfew_requests')
      .insert({
        ...requestData,
        status: 'pending',
        timestamp: new Date().toISOString()
      })

    if (error) throw error
    return true
  } catch (error) {
    console.error('Error submitting curfew request:', error)
    return false
  }
}

/**
 * Format a time string (HH:MM:SS) to a more readable format (H:MM AM/PM)
 * @param timeStr Time string in format HH:MM:SS
 * @returns Formatted time string in format H:MM AM/PM
 */
export const formatCurfewTime = (timeStr: string): string => {
  if (!timeStr) return 'N/A';

  try {
    const [hours, minutes] = timeStr.split(':');
    const hour = parseInt(hours, 10);
    const minute = parseInt(minutes, 10);

    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    const displayMinute = minute.toString().padStart(2, '0');

    return `${displayHour}:${displayMinute} ${period}`;
  } catch (error) {
    console.error('Error formatting curfew time:', error);
    return timeStr;
  }
}

/**
 * Get a summary of curfew information
 * @param info Curfew information
 * @returns A summary string
 */
export const getCurfewSummary = (info: any): string => {
  if (!info) return 'No curfew information available';

  try {
    const propertyType = info.property_type || info.propertyType || 'property';
    const curfewStart = info.curfew_start || (info.weekdayCurfew && info.weekdayCurfew.start) || '22:00:00';
    const curfewEnd = info.curfew_end || (info.weekdayCurfew && info.weekdayCurfew.end) || '07:00:00';

    return `This ${propertyType} has noise restrictions from ${formatCurfewTime(curfewStart)} to ${formatCurfewTime(curfewEnd)}.`;
  } catch (error) {
    console.error('Error generating curfew summary:', error);
    return 'Curfew information available but could not be summarized';
  }
}

/**
 * Get top recommendations based on curfew information
 * @param info Curfew information
 * @param count Number of recommendations to return
 * @returns Array of recommendation strings
 */
export const getTopRecommendations = (info: any, count: number = 3): string[] => {
  if (!info) return ['No recommendations available'];

  try {
    const recommendations = [
      'Inform neighbors about your event in advance',
      'Keep windows and doors closed during the event',
      'Monitor noise levels throughout the party',
      'Gradually reduce music volume as curfew approaches',
      'Designate a quiet area for guests who need a break',
      'Use sound-absorbing materials for decoration',
      'Position speakers away from walls and windows',
      'Consider hiring a professional sound engineer',
      'Have a dedicated person monitor noise complaints'
    ];

    // If the info object has recommendations, use those instead
    if (info.recommendations && Array.isArray(info.recommendations)) {
      return info.recommendations
        .slice(0, count)
        .map((rec: any) => rec.recommendation || rec);
    }

    // Otherwise return generic recommendations
    return recommendations.slice(0, count);
  } catch (error) {
    console.error('Error generating recommendations:', error);
    return ['Keep noise levels reasonable', 'Respect your neighbors', 'Follow local regulations'];
  }
}
