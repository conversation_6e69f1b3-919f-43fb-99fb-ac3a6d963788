import React, { useState } from 'react';
import { CreditCard, Lock, AlertCircle, CreditCard as CardIcon, Smartphone, Building2, Copy, Check } from 'lucide-react';
import Button from '../ui/Button';
import { paymentService, TEST_CARDS } from '../../services/payment';

interface PaymentFormProps {
  amount: number; // Amount in dollars
  description: string;
  onSuccess: (chargeResponse: any) => void;
  onError?: (error: any) => void;
  showTestCards?: boolean;
  showDigitalWallets?: boolean;
}

export default function PaymentForm({
  amount,
  description,
  onSuccess,
  onError,
  showTestCards = false,
  showDigitalWallets = true
}: PaymentFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'digital-wallet' | 'bank-transfer'>('card');
  const [copySuccess, setCopySuccess] = useState<string | null>(null);

  // Card details state
  const [cardNumber, setCardNumber] = useState('');
  const [expiryMonth, setExpiryMonth] = useState('');
  const [expiryYear, setExpiryYear] = useState('');
  const [cvc, setCvc] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  // Format card number with spaces
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };

  // Use a test card
  const useTestCard = (cardNumber: string) => {
    setCardNumber(formatCardNumber(cardNumber));
    setExpiryMonth('12');
    setExpiryYear('25');
    setCvc('123');
    setName('Test User');
    setEmail('<EMAIL>');
  };

  // Copy text to clipboard
  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopySuccess(field);
        setTimeout(() => setCopySuccess(null), 2000);
      },
      () => {
        setCopySuccess(null);
      }
    );
  };

  // Handle bank transfer confirmation
  const handleBankTransferConfirmation = async () => {
    setLoading(true);
    setError(null);

    try {
      // Simulate processing
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create a mock successful bank transfer response
      const mockTransferResponse = {
        token: `transfer_${Math.random().toString(36).substring(2, 15)}`,
        success: true,
        amount: Math.round(amount * 100),
        currency: 'AUD',
        description,
        email: email || '<EMAIL>',
        created_at: new Date().toISOString(),
        status_message: 'Bank transfer initiated',
        payment_method: 'bank_transfer',
        amount_refunded: 0,
        total_fees: 0, // No fees for bank transfer
        merchant_entitlement: Math.round(amount * 100),
        refund_pending: false,
        captured: true,
        settlement_currency: 'AUD',
        metadata: {
          payment_method: 'bank_transfer'
        }
      };

      // Call the success callback
      onSuccess(mockTransferResponse);
    } catch (err: any) {
      // Set error message
      const errorMessage = err instanceof Error ? err.message : 'Bank transfer confirmation failed';
      setError(errorMessage);

      // Call the error callback if provided
      if (onError) {
        onError(err);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle digital wallet payment
  const handleDigitalWalletPayment = async (walletType: 'apple' | 'google') => {
    setLoading(true);
    setError(null);

    try {
      // Simulate a digital wallet payment
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create a mock successful charge response
      const mockChargeResponse = {
        token: `pin_${Math.random().toString(36).substring(2, 15)}`,
        success: true,
        amount: Math.round(amount * 100),
        currency: 'AUD',
        description,
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        status_message: 'Success',
        card: {
          token: `card_${Math.random().toString(36).substring(2, 15)}`,
          scheme: walletType === 'apple' ? 'apple_pay' : 'google_pay',
          display_number: '•••• 0000',
          expiry_month: 12,
          expiry_year: 25,
          name: walletType === 'apple' ? 'Apple Pay' : 'Google Pay',
          customer_token: null,
          primary: true
        },
        amount_refunded: 0,
        total_fees: Math.round(amount * 100 * 0.029) + 30, // 2.9% + 30c
        merchant_entitlement: Math.round(amount * 100) - (Math.round(amount * 100 * 0.029) + 30),
        refund_pending: false,
        authorisation_expired: false,
        captured: true,
        settlement_currency: 'AUD',
        metadata: {
          payment_method: walletType === 'apple' ? 'apple_pay' : 'google_pay'
        }
      };

      // Call the success callback
      onSuccess(mockChargeResponse);
    } catch (err: any) {
      // Set error message
      const errorMessage = err.response?.data?.error_description ||
                          err.response?.data?.error ||
                          (err instanceof Error ? err.message : 'Digital wallet payment failed');
      setError(errorMessage);

      // Call the error callback if provided
      if (onError) {
        onError(err);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate form
      if (!cardNumber || !expiryMonth || !expiryYear || !cvc || !name || !email) {
        throw new Error('Please fill in all required fields');
      }

      // Create a card token
      const tokenResponse = await paymentService.createToken({
        number: cardNumber.replace(/\s+/g, ''),
        expiry_month: expiryMonth,
        expiry_year: expiryYear,
        cvc,
        name,
      });

      // Create a charge using the card token
      const chargeResponse = await paymentService.createCharge({
        email,
        description,
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'AUD',
        card_token: tokenResponse.token,
      });

      // Call the success callback
      onSuccess(chargeResponse);
    } catch (err: any) {
      // Set error message
      const errorMessage = err.response?.data?.error_description ||
                          err.response?.data?.error ||
                          (err instanceof Error ? err.message : 'Payment failed');
      setError(errorMessage);

      // Call the error callback if provided
      if (onError) {
        onError(err);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <div className="flex flex-wrap justify-center gap-3 mb-4">
          <button
            type="button"
            onClick={() => setPaymentMethod('card')}
            className={`flex items-center px-4 py-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 ${paymentMethod === 'card' ? 'bg-purple-100 text-purple-700 border border-purple-300' : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100 active:bg-gray-200'}`}
          >
            <CardIcon className="w-5 h-5 mr-2" />
            Credit Card
          </button>

          {showDigitalWallets && (
            <button
              type="button"
              onClick={() => setPaymentMethod('digital-wallet')}
              className={`flex items-center px-4 py-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 ${paymentMethod === 'digital-wallet' ? 'bg-purple-100 text-purple-700 border border-purple-300' : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100 active:bg-gray-200'}`}
            >
              <Smartphone className="w-5 h-5 mr-2" />
              Digital Wallet
            </button>
          )}

          <button
            type="button"
            onClick={() => setPaymentMethod('bank-transfer')}
            className={`flex items-center px-4 py-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 ${paymentMethod === 'bank-transfer' ? 'bg-purple-100 text-purple-700 border border-purple-300' : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100 active:bg-gray-200'}`}
          >
            <Building2 className="w-5 h-5 mr-2" />
            Bank Transfer
          </button>
        </div>
      </div>

      {paymentMethod === 'bank-transfer' ? (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4">
            <div className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z" clipRule="evenodd" />
              </svg>
              <div>
                <h3 className="font-medium text-blue-800">Pay via Bank Transfer</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Make a direct bank transfer to complete your booking.
                  Your booking will be confirmed once payment is received.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-5">
            <h3 className="font-medium text-gray-800 mb-4">Bank Transfer Details</h3>

            <div className="space-y-4">
              <div className="flex justify-between items-center pb-3 border-b border-gray-100">
                <span className="text-gray-600">Amount to pay:</span>
                <span className="font-bold text-gray-700">${amount.toFixed(2)} AUD</span>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-700 font-medium">Account Name:</span>
                  <div className="flex items-center">
                    <span className="mr-2">HouseGoing Pty Ltd</span>
                    <button
                      type="button"
                      onClick={() => copyToClipboard('HouseGoing Pty Ltd', 'name')}
                      className="text-purple-600 hover:text-purple-800 p-1 rounded-full hover:bg-purple-50 active:bg-purple-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
                      aria-label="Copy account name"
                    >
                      {copySuccess === 'name' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-700 font-medium">BSB:</span>
                  <div className="flex items-center">
                    <span className="mr-2">062-000</span>
                    <button
                      type="button"
                      onClick={() => copyToClipboard('062-000', 'bsb')}
                      className="text-purple-600 hover:text-purple-800 p-1 rounded-full hover:bg-purple-50 active:bg-purple-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
                      aria-label="Copy BSB number"
                    >
                      {copySuccess === 'bsb' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-700 font-medium">Account Number:</span>
                  <div className="flex items-center">
                    <span className="mr-2">********</span>
                    <button
                      type="button"
                      onClick={() => copyToClipboard('********', 'account')}
                      className="text-purple-600 hover:text-purple-800 p-1 rounded-full hover:bg-purple-50 active:bg-purple-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
                      aria-label="Copy account number"
                    >
                      {copySuccess === 'account' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-700 font-medium">Reference:</span>
                  <div className="flex items-center">
                    <span className="mr-2">BOOKING-{Math.floor(Math.random() * 10000).toString().padStart(4, '0')}</span>
                    <button
                      type="button"
                      onClick={() => copyToClipboard(`BOOKING-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`, 'reference')}
                      className="text-purple-600 hover:text-purple-800 p-1 rounded-full hover:bg-purple-50 active:bg-purple-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
                      aria-label="Copy reference number"
                    >
                      {copySuccess === 'reference' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
            <div className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <div>
                <h3 className="font-medium text-blue-800">Important Information</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Please include the reference number in your bank transfer. Your booking will be confirmed once we receive your payment, which may take 1-2 business days.
                </p>
              </div>
            </div>
          </div>

          <div className="pt-2">
            <Button
              type="button"
              onClick={handleBankTransferConfirmation}
              fullWidth
              size="lg"
              isLoading={loading}
              loadingText="Processing..."
            >
              I Have Made The Bank Transfer
            </Button>

            <p className="text-xs text-center text-gray-500 mt-3">
              Click this button after you have made the bank transfer to confirm your booking.
            </p>
          </div>

          {error && (
            <div className="p-3 rounded-lg bg-red-50 text-red-600 text-sm flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>{error}</span>
            </div>
          )}
        </div>
      ) : paymentMethod === 'digital-wallet' ? (
        <div className="space-y-6">
          <p className="text-center text-gray-600 mb-4">Choose your digital wallet</p>

          <div className="grid grid-cols-2 gap-4">
            <button
              type="button"
              onClick={() => handleDigitalWalletPayment('apple')}
              disabled={loading}
              className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
            >
              <div className="bg-black text-white rounded-md px-3 py-1 mb-2 flex items-center">
                <svg viewBox="0 0 24 24" width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.72 7.22c.6-.8 1.1-1.9 1-3-.9.1-2 .6-2.7 1.3-.6.7-1.1 1.8-1 2.9 1 .1 1.9-.5 2.7-1.2" fill="#fff"/>
                  <path d="M17.3 9.1c-1.5-.1-2.7.8-3.4.8-.7 0-1.8-.8-2.9-.8-1.5 0-2.9.9-3.6 2.2-1.5 2.7-.4 6.6 1.1 8.8.7 1.1 1.6 2.2 2.7 2.2 1.1 0 1.5-.7 2.8-.7 1.3 0 1.7.7 2.8.7 1.2 0 1.9-1.1 2.6-2.1.8-1.2 1.2-2.4 1.2-2.5-.1 0-2.3-.9-2.3-3.5 0-2.2 1.8-3.2 1.9-3.3-.9-1.5-2.4-1.7-2.9-1.8" fill="#fff"/>
                </svg>
                <span className="ml-1 font-medium">Pay</span>
              </div>
              <span className="text-sm">Pay with Apple Pay</span>
            </button>

            <button
              type="button"
              onClick={() => handleDigitalWalletPayment('google')}
              disabled={loading}
              className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50"
            >
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 48 48">
                  <path fill="#4285F4" d="M24.5 21v7.3h10.2c-.4 2.6-2.6 7.8-10.2 7.8-6.2 0-11.2-5.1-11.2-11.4S18.3 13.2 24.5 13.2c3.5 0 5.8 1.5 7.2 2.8l4.9-4.7C32.9 7.9 29.1 6 24.5 6 14.2 6 6 14.2 6 24.5S14.2 43 24.5 43c11.5 0 19.2-8.1 19.2-19.5 0-1.3-.2-2.3-.4-3.3H24.5z"/>
                  <path fill="#EA4335" d="M10.3 14.7l5.7 4.2c1.5-4.2 5.5-7.2 10.2-7.2 3.5 0 5.8 1.5 7.2 2.8l4.9-4.7C32.9 7.9 29.1 6 24.5 6c-7.9 0-14.7 5.1-17.2 12.7"/>
                  <path fill="#FBBC05" d="M6.7 24.5c0-1.8.3-3.5.8-5.1l-5.6-4.3C.7 18.1 0 21.2 0 24.5c0 3.3.7 6.4 1.9 9.2l5.6-4.3c-.5-1.6-.8-3.3-.8-4.9"/>
                  <path fill="#34A853" d="M24.5 43c4.6 0 8.4-1.9 11.2-5.1l-5.4-4.2c-1.5 1.5-3.4 2.5-5.8 2.5-4.7 0-8.7-3-10.2-7.2l-5.7 4.2C11.8 37.9 17.6 43 24.5 43"/>
                  <path fill="none" d="M0 0h48v48H0z"/>
                </svg>
                <span className="ml-1 font-medium text-gray-600">Pay</span>
              </div>
              <span className="text-sm">Pay with Google Pay</span>
            </button>
          </div>

          {loading && (
            <div className="flex justify-center items-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mr-2"></div>
              <span>Processing payment...</span>
            </div>
          )}

          {error && (
            <div className="p-3 rounded-lg bg-red-50 text-red-600 text-sm flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <span>{error}</span>
            </div>
          )}
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Information
          </label>
          <div className="relative">
            <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Card number"
              value={cardNumber}
              onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
              className="pl-10 w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              required
              maxLength={19}
            />
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <select
              value={expiryMonth}
              onChange={(e) => setExpiryMonth(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              required
            >
              <option value="">Month</option>
              {Array.from({ length: 12 }, (_, i) => {
                const month = i + 1;
                return (
                  <option key={month} value={month.toString().padStart(2, '0')}>
                    {month.toString().padStart(2, '0')}
                  </option>
                );
              })}
            </select>
          </div>
          <div>
            <select
              value={expiryYear}
              onChange={(e) => setExpiryYear(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              required
            >
              <option value="">Year</option>
              {Array.from({ length: 10 }, (_, i) => {
                const year = new Date().getFullYear() + i;
                return (
                  <option key={year} value={year.toString().slice(-2)}>
                    {year}
                  </option>
                );
              })}
            </select>
          </div>
          <div>
            <input
              type="text"
              placeholder="CVC"
              value={cvc}
              onChange={(e) => setCvc(e.target.value.replace(/\D/g, ''))}
              className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              required
              maxLength={4}
            />
          </div>
        </div>

        <div>
          <input
            type="text"
            placeholder="Cardholder Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
          />
        </div>

        {error && (
          <div className="p-3 rounded-lg bg-red-50 text-red-600 text-sm flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
            <span>{error}</span>
          </div>
        )}

        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center">
            <Lock className="h-4 w-4 mr-1" />
            Secure payment via Pin Payments
          </div>
          <span>Amount: ${amount.toFixed(2)} AUD</span>
        </div>

        <Button
          type="submit"
          fullWidth
          size="lg"
          isLoading={loading}
          loadingText="Processing..."
        >
          Pay Now
        </Button>
      </form>
      )}

      {showTestCards && paymentMethod === 'card' && (
        <div className="mt-4 border-t pt-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-2">Test Cards</h3>
          <div className="grid grid-cols-1 gap-2">
            <button
              type="button"
              onClick={() => useTestCard(TEST_CARDS.VISA_SUCCESS)}
              className="text-xs text-left px-2 py-1 border border-gray-200 rounded hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-purple-500 focus:ring-opacity-50"
            >
              Success: {formatCardNumber(TEST_CARDS.VISA_SUCCESS)}
            </button>
            <button
              type="button"
              onClick={() => useTestCard(TEST_CARDS.VISA_INSUFFICIENT_FUNDS)}
              className="text-xs text-left px-2 py-1 border border-gray-200 rounded hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-purple-500 focus:ring-opacity-50"
            >
              Insufficient Funds: {formatCardNumber(TEST_CARDS.VISA_INSUFFICIENT_FUNDS)}
            </button>
            <button
              type="button"
              onClick={() => useTestCard(TEST_CARDS.VISA_INVALID_CVC)}
              className="text-xs text-left px-2 py-1 border border-gray-200 rounded hover:bg-gray-50 active:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-purple-500 focus:ring-opacity-50"
            >
              Invalid CVC: {formatCardNumber(TEST_CARDS.VISA_INVALID_CVC)}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}