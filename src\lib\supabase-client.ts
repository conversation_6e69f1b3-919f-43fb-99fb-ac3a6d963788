/**
 * Centralized Supabase client
 *
 * This file provides a single instance of the Supabase client to be used throughout the application.
 * This client is configured to handle both authentication and database operations.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

// Supabase configuration - use environment variables for security
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_URL : '');

const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY : '');

// Validate environment variables
if (!supabaseUrl) {
  throw new Error('VITE_SUPABASE_URL environment variable is required');
}

if (!supabaseKey) {
  throw new Error('VITE_SUPABASE_ANON_KEY environment variable is required');
}

console.log('Supabase config:', {
  url: supabaseUrl,
  keyLength: supabaseKey.length,
  keyPrefix: supabaseKey.substring(0, 10) + '...'
});

// Service role key is only required for admin/service client
const supabaseServiceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY : '');

// Create a singleton instance of the Supabase client
let supabaseInstance: SupabaseClient<Database>;
let clerkSupabaseInstance: SupabaseClient<Database> | null = null;

// Flag to track if we've already warned about multiple instances
let hasWarnedAboutMultipleInstances = false;

/**
 * Get the basic Supabase client instance (without Clerk auth)
 */
export function getSupabaseClient(): SupabaseClient<Database> {
  // Check if we already have an instance in the global scope
  // @ts-ignore
  if (typeof window !== 'undefined' && window.__SUPABASE_CLIENT_INSTANCE) {
    // @ts-ignore
    return window.__SUPABASE_CLIENT_INSTANCE;
  }

  if (!supabaseInstance) {
    // Create a new client with optimized settings
    supabaseInstance = createClient<Database>(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false, // Disable Supabase session persistence - Clerk handles this
        autoRefreshToken: false, // Disable auto-refresh - Clerk manages tokens
        detectSessionInUrl: false, // Disable URL detection to prevent conflicts with Clerk
        storageKey: 'housegoing-supabase-auth', // Unique storage key to avoid conflicts
        flowType: 'pkce', // More secure flow for auth
        debug: false // Disable debug mode to reduce console noise
      },
      global: {
        headers: {
          'apikey': supabaseKey,
          'Content-Type': 'application/json'
        }
      },
      db: {
        schema: 'public'
      },
      // Disable realtime subscriptions to reduce client instances
      realtime: {
        params: {
          eventsPerSecond: 0
        }
      }
    });

    // Store the instance in the global scope for reuse
    if (typeof window !== 'undefined') {
      // @ts-ignore
      window.__SUPABASE_CLIENT_INSTANCE = supabaseInstance;
    }

    console.log('Centralized Supabase client initialized');
  }

  return supabaseInstance;
}

/**
 * Get a Supabase client instance with Clerk authentication
 * This should be used when you need authenticated operations
 */
export async function getClerkSupabaseClient(): Promise<SupabaseClient<Database>> {
  // Check if we already have a global instance
  // @ts-ignore
  if (typeof window !== 'undefined' && window.__CLERK_SUPABASE_CLIENT_INSTANCE) {
    // @ts-ignore
    return window.__CLERK_SUPABASE_CLIENT_INSTANCE;
  }

  // Check if Clerk is available
  if (typeof window !== 'undefined') {
    const maxRetries = 3;
    let retryCount = 0;
    const retryDelay = 500; // ms

    while (retryCount < maxRetries) {
      try {
        // Wait for Clerk to be available
        if (!window.Clerk?.session) {
          console.warn('Clerk session not available');
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          retryCount++;
          continue;
        }

        // Get token from Clerk session - use standard token, not JWT template
        const token = await window.Clerk.session.getToken();
        if (!token) {
          console.warn('No Clerk token available - using anonymous Supabase client');
          throw new Error('No Clerk token available');
        }

        console.log('Got Clerk token, setting up Supabase auth...');

        // Create authenticated client - DO NOT call setSession to avoid multiple GoTrueClient instances
        clerkSupabaseInstance = createClient<Database>(supabaseUrl, supabaseKey, {
          auth: {
            persistSession: false, // Disable persistence to avoid storage conflicts
            autoRefreshToken: false, // Clerk handles token refresh
            detectSessionInUrl: false, // Disable URL detection
            storageKey: 'housegoing-clerk-supabase-auth', // Unique storage key
            flowType: 'pkce',
            debug: false // Disable debug mode
          },
          global: {
            headers: {
              // Set Authorization header with token for authentication
              'Authorization': `Bearer ${token}`,
              'apikey': supabaseKey,
              'Content-Type': 'application/json'
            }
          },
          // Disable realtime to reduce client instances
          realtime: {
            params: {
              eventsPerSecond: 0
            }
          }
        });

        // Store in global scope to prevent multiple instances
        if (typeof window !== 'undefined') {
          // @ts-ignore
          window.__CLERK_SUPABASE_CLIENT_INSTANCE = clerkSupabaseInstance;
        }

        console.log('Clerk-authenticated Supabase client initialized');
        return clerkSupabaseInstance;
      } catch (error) {
        console.error(`Error initializing Clerk-authenticated Supabase client (attempt ${retryCount + 1}):`, error);
        retryCount++;
        if (retryCount >= maxRetries) {
          break;
        }
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  // Fall back to basic client if Clerk isn't available after retries
  console.warn('Clerk session not available after retries - falling back to basic Supabase client');
  return getSupabaseClient();
}

// Patch the createClient function to prevent multiple instances
if (typeof window !== 'undefined') {
  try {
    // @ts-ignore
    const originalCreateClient = window.__ORIGINAL_SUPABASE_CREATE_CLIENT ||
                               // @ts-ignore
                               (window.__ORIGINAL_SUPABASE_CREATE_CLIENT = createClient);

    // @ts-ignore
    window.Supabase = window.Supabase || {};
    // @ts-ignore
    window.Supabase.createClient = function(...args: any[]) {
      if (!hasWarnedAboutMultipleInstances) {
        console.warn('WARNING: Attempted to create a new Supabase client. Using centralized client instead.');
        hasWarnedAboutMultipleInstances = true;
      }
      return getSupabaseClient();
    };
  } catch (error) {
    console.error('Error patching Supabase createClient:', error);
  }
}

// Export the supabase client
export const supabase = getSupabaseClient();

// Export default for compatibility with JavaScript imports
export default supabase;

// Add a warning if someone tries to create another client
export const warnAboutMultipleClients = () => {
  if (!hasWarnedAboutMultipleInstances) {
    console.warn('WARNING: You are creating a new Supabase client. This can cause the "Multiple GoTrueClient instances" warning. Use the centralized client from src/lib/supabase-client.ts instead.');
    hasWarnedAboutMultipleInstances = true;
  }
};

/**
 * Get the Supabase client instance for service role operations
 * This client uses the service role key for elevated permissions
 */
export function getSupabaseServiceClient(): SupabaseClient<Database> {
  if (!supabaseServiceRoleKey) {
    throw new Error('VITE_SUPABASE_SERVICE_ROLE_KEY environment variable is required for service client');
  }
  return createClient<Database>(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    },
    global: {
      headers: {
        'apikey': supabaseServiceRoleKey,
        'Authorization': `Bearer ${supabaseServiceRoleKey}`,
        'Content-Type': 'application/json'
      }
    },
    db: {
      schema: 'public'
    }
  });
}
