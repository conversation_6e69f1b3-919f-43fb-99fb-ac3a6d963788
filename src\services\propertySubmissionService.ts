import { getSupabaseClient } from '../lib/supabase-client';
import { sendPropertySubmissionNotification, sendPropertyApprovalNotification, sendPropertyRejectionNotification } from './notificationService';

export enum PropertyStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface PropertySubmission {
  id: string;
  host_id: string;
  host_name: string;
  host_email: string;
  property_name: string;
  property_address: string;
  property_type: string;
  max_guests: number;
  hourly_rate: number;
  description: string;
  amenities: string[];
  rules: string[];
  photos: string[];
  status: PropertyStatus;
  created_at: string;
  updated_at: string;
  rejection_reason?: string;
}

/**
 * Submits a new property for review
 * @param propertyData The property data to submit
 * @returns The submitted property data with ID
 */
export async function submitProperty(propertyData: Omit<PropertySubmission, 'id' | 'status' | 'created_at' | 'updated_at'>): Promise<PropertySubmission | null> {
  try {
    // Set default values
    const submission = {
      ...propertyData,
      status: PropertyStatus.PENDING,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Insert into Supabase
    const { data, error } = await supabase
      .from('property_submissions')
      .insert(submission)
      .select()
      .single();

    if (error) {
      console.error('Error submitting property:', error);
      return null;
    }

    // Send notification to admin
    await sendPropertySubmissionNotification({
      propertyId: data.id,
      hostId: data.host_id,
      hostName: data.host_name,
      hostEmail: data.host_email,
      propertyName: data.property_name,
      propertyAddress: data.property_address,
      submissionDate: new Date(data.created_at)
    });

    return data as PropertySubmission;
  } catch (error) {
    console.error('Failed to submit property:', error);
    return null;
  }
}

/**
 * Gets all property submissions
 * @param status Optional status filter
 * @returns Array of property submissions
 */
export async function getPropertySubmissions(status?: PropertyStatus): Promise<PropertySubmission[]> {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    let query = supabase
      .from('property_submissions')
      .select('*')
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching property submissions:', error);
      return [];
    }

    return data as PropertySubmission[];
  } catch (error) {
    console.error('Failed to fetch property submissions:', error);
    return [];
  }
}

/**
 * Gets a property submission by ID
 * @param id The property submission ID
 * @returns The property submission or null if not found
 */
export async function getPropertySubmissionById(id: string): Promise<PropertySubmission | null> {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching property submission:', error);
      return null;
    }

    return data as PropertySubmission;
  } catch (error) {
    console.error('Failed to fetch property submission:', error);
    return null;
  }
}

/**
 * Approves a property submission
 * @param id The property submission ID
 * @returns True if approved successfully
 */
export async function approvePropertySubmission(id: string): Promise<boolean> {
  try {
    // Get the property submission first
    const submission = await getPropertySubmissionById(id);
    if (!submission) {
      return false;
    }

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Update the status
    const { error } = await supabase
      .from('property_submissions')
      .update({
        status: PropertyStatus.APPROVED,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      console.error('Error approving property submission:', error);
      return false;
    }

    // Insert into the main properties table
    const { error: insertError } = await supabase
      .from('properties')
      .insert({
        name: submission.property_name,
        address: submission.property_address,
        type: submission.property_type,
        max_guests: submission.max_guests,
        hourly_rate: submission.hourly_rate,
        description: submission.description,
        amenities: submission.amenities,
        rules: submission.rules,
        photos: submission.photos,
        host_id: submission.host_id,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('Error inserting approved property:', insertError);
      return false;
    }

    // Send approval notification to host
    await sendPropertyApprovalNotification(
      submission.host_email,
      submission.property_name
    );

    return true;
  } catch (error) {
    console.error('Failed to approve property submission:', error);
    return false;
  }
}

/**
 * Rejects a property submission
 * @param id The property submission ID
 * @param reason The reason for rejection
 * @returns True if rejected successfully
 */
export async function rejectPropertySubmission(id: string, reason: string): Promise<boolean> {
  try {
    // Get the property submission first
    const submission = await getPropertySubmissionById(id);
    if (!submission) {
      return false;
    }

    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    // Update the status
    const { error } = await supabase
      .from('property_submissions')
      .update({
        status: PropertyStatus.REJECTED,
        rejection_reason: reason,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      console.error('Error rejecting property submission:', error);
      return false;
    }

    // Send rejection notification to host
    await sendPropertyRejectionNotification(
      submission.host_email,
      submission.property_name,
      reason
    );

    return true;
  } catch (error) {
    console.error('Failed to reject property submission:', error);
    return false;
  }
}
