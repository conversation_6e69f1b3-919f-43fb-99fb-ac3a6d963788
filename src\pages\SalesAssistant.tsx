import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import SalesAssistantChat from '../components/chat/SalesAssistantChat';

export default function SalesAssistant() {
  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link to="/" className="flex items-center text-gray-600 hover:text-gray-900 mb-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="text-sm">Back to Home</span>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">HouseGoing Sales Assistant</h1>
          <p className="mt-1 text-gray-600">Chat with Homie to find the perfect venue for your event</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Main content area */}
          <div className="md:col-span-2 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-xl font-semibold mb-4">Find Your Perfect Party Venue</h2>
            <p className="mb-4">Our AI assistant Homie can help you find the ideal venue for your next event. Just ask about:</p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-medium text-purple-800 mb-2">Event Types</h3>
                <ul className="text-sm space-y-1">
                  <li>• Birthday parties</li>
                  <li>• Weddings & engagements</li>
                  <li>• Corporate events</li>
                  <li>• Social gatherings</li>
                </ul>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-medium text-purple-800 mb-2">Venue Features</h3>
                <ul className="text-sm space-y-1">
                  <li>• BYO alcohol options</li>
                  <li>• Indoor/outdoor spaces</li>
                  <li>• Capacity ranges</li>
                  <li>• Amenities & facilities</li>
                </ul>
              </div>
            </div>

            <p className="text-sm text-gray-600 italic">Click the chat button in the bottom right to start a conversation with Homie!</p>
          </div>

          {/* Sidebar */}
          <div className="md:col-span-1 space-y-6">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
              <div className="p-4 border-b border-gray-200 bg-purple-50">
                <h3 className="font-medium text-purple-900">How It Works</h3>
              </div>
              <div className="p-4">
                <p className="text-sm text-gray-600 mb-3">
                  Homie can help you find the perfect venue for your event. Just tell Homie about:
                </p>
                <ul className="text-sm text-gray-600 space-y-2 list-disc pl-5">
                  <li>What type of event you're planning</li>
                  <li>How many guests you'll have</li>
                  <li>Your preferred location</li>
                  <li>Your budget</li>
                  <li>When you need the venue</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
              <div className="p-4 border-b border-gray-200 bg-purple-50">
                <h3 className="font-medium text-purple-900">Need More Help?</h3>
              </div>
              <div className="p-4">
                <p className="text-sm text-gray-600 mb-3">
                  Our team is available to assist you with any questions.
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Email:</span> <EMAIL>
                  </p>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Phone:</span> (02) 1234 5678
                  </p>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Hours:</span> Mon-Fri, 9am-5pm
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat widget */}
      <SalesAssistantChat />
    </div>
  );
}
