import { SearchBar } from "@/components/SearchBar";
import { VenueCard } from "@/components/VenueCard";

const venues = [
  {
    image: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3",
    title: "Luxury Loft Space",
    price: 150,
    location: "Downtown, NYC",
    capacity: 100,
  },
  {
    image: "https://images.unsplash.com/photo-1519750783826-e2420f4d687f",
    title: "Garden Paradise",
    price: 200,
    location: "Brooklyn, NYC",
    capacity: 150,
  },
  {
    image: "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3",
    title: "Rooftop Terrace",
    price: 175,
    location: "Manhattan, NYC",
    capacity: 80,
  },
  {
    image: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3",
    title: "Modern Event Space",
    price: 300,
    location: "Queens, NYC",
    capacity: 200,
  },
  {
    image: "https://images.unsplash.com/photo-1519750783826-e2420f4d687f",
    title: "Cozy Gallery",
    price: 125,
    location: "Brooklyn, NYC",
    capacity: 50,
  },
  {
    image: "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3",
    title: "Urban Warehouse",
    price: 250,
    location: "Bronx, NYC",
    capacity: 300,
  },
];

export default function SearchResults() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <SearchBar />
      </div>
      
      <div className="flex gap-6">
        {/* Filters Section */}
        <div className="hidden md:block w-64 bg-white p-6 rounded-lg shadow-sm h-fit">
          <h3 className="font-semibold text-lg mb-4">Filters</h3>
          
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Price Range</h4>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">$0 - $100</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">$100 - $200</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">$200+</span>
                </label>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Capacity</h4>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">1-50 guests</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">51-100 guests</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">100+ guests</span>
                </label>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Amenities</h4>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">WiFi</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">Parking</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">Kitchen</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox" />
                  <span className="ml-2">Sound System</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        
        {/* Results Grid */}
        <div className="flex-1">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold">Search Results</h2>
            <select className="border rounded-md px-3 py-2">
              <option>Sort by: Recommended</option>
              <option>Price: Low to High</option>
              <option>Price: High to Low</option>
              <option>Capacity: High to Low</option>
            </select>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {venues.map((venue, index) => (
              <VenueCard key={index} {...venue} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}