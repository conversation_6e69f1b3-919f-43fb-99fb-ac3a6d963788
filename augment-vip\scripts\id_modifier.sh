#!/bin/bash

# Function to detect operating system
detect_os() {
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "linux"
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "macos"
  elif [[ "$OSTYPE" == "cygwin"* || "$OSTYPE" == "msys"* || "$OSTYPE" == "win32"* ]]; then
    echo "windows"
  else
    echo "unknown"
  fi
}

# Function to find VS Code storage.json file
find_storage_json() {
  local os=$1
  local storage_file=""

  case $os in
    linux)
      storage_file="$HOME/.config/Code/OSS/storage.json"
      ;;
    macos)
      storage_file="$HOME/Library/Application Support/Code/OSS/storage.json"
      ;;
    windows)
      # On Windows, we need to handle paths with backslashes
      storage_file="/mnt/c/Users/<USER>/AppData/Roaming/Code/OSS/storage.json"
      ;;
    *)
      echo "[WARNING] Unsupported operating system: $os"
      return 1
      ;;
  esac

  if [ ! -f "$storage_file" ]; then
    echo "[WARNING] Storage file not found: $storage_file"
    return 1
  fi

  echo "$storage_file"
}

# Function to generate random values
generate_random_values() {
  local machine_id=$(openssl rand -hex 32)
  local device_id=$(uuidgen)

  echo "$machine_id"
  echo "$device_id"
}

# Function to modify telemetry IDs
modify_telemetry_ids() {
  local storage_file=$1
  local machine_id=$2
  local device_id=$3

  # Create a backup
  cp "$storage_file" "data/backups/$(basename "$storage_file").$(date +%Y%m%d%H%M%S).backup"

  # Modify the file
  jq --arg machine_id "$machine_id" --arg device_id "$device_id" '
    .machineId = $machine_id |
    .devDeviceId = $device_id
  ' "$storage_file" > "$storage_file.tmp" && mv "$storage_file.tmp" "$storage_file"

  echo "[INFO] Modified telemetry IDs in $storage_file"
}

# Main script execution
OS=$(detect_os)
echo "[INFO] Detected operating system: $OS"

STORAGE_FILE=$(find_storage_json "$OS")
if [ $? -ne 0 ]; then
  exit 1
fi

echo "[INFO] Found storage file: $STORAGE_FILE"

read -r MACHINE_ID DEVICE_ID <<< $(generate_random_values)
modify_telemetry_ids "$STORAGE_FILE" "$MACHINE_ID" "$DEVICE_ID"

echo "[INFO] Telemetry ID modification completed successfully."
