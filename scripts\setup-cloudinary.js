/**
 * <PERSON><PERSON><PERSON> to set up Cloudinary upload preset
 *
 * Run this script once to create the upload preset for unsigned uploads
 *
 * Usage: node scripts/setup-cloudinary.js
 */
import https from 'https';
import { stringify } from 'querystring';

// Cloudinary configuration
const cloudName = 'dcdjxfnud';
const apiKey = '565724643666687';
const apiSecret = 'B23KQdOOeZVu-ylR0TQfZXF8YAU';
const uploadPresetName = 'housegoing_uploads';

// Create the upload preset
const createUploadPreset = () => {
  return new Promise((resolve, reject) => {
    // Prepare the request data
    const data = stringify({
      name: uploadPresetName,
      unsigned: true,
      folder: 'property-images',
      allowed_formats: 'jpg,png,jpeg',
      max_file_size: 5000000, // 5MB
    });

    // Prepare the request options
    const options = {
      hostname: 'api.cloudinary.com',
      path: `/v1_1/${cloudName}/upload_presets`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': data.length,
        'Authorization': 'Basic ' + Buffer.from(apiKey + ':' + apiSecret).toString('base64')
      }
    };

    // Send the request
    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log('Upload preset created successfully:', parsedData.name);
            resolve(parsedData);
          } else {
            console.error('Error creating upload preset:', parsedData);
            reject(new Error(`Failed to create upload preset: ${parsedData.error?.message || 'Unknown error'}`));
          }
        } catch (error) {
          console.error('Error parsing response:', error);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error('Error sending request:', error);
      reject(error);
    });

    req.write(data);
    req.end();
  });
};

// Main function
const main = async () => {
  try {
    console.log('Setting up Cloudinary upload preset...');
    await createUploadPreset();
    console.log('Cloudinary setup completed successfully!');
    console.log(`Upload preset "${uploadPresetName}" is ready to use.`);
    console.log('You can now use the Cloudinary upload functionality in your application.');
  } catch (error) {
    console.error('Error setting up Cloudinary:', error);
    process.exit(1);
  }
};

// Run the script
main();
