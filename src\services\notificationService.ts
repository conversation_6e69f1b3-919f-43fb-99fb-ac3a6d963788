import { CLERK_CONFIG } from '../config/clerk';

interface EmailNotification {
  to: string;
  subject: string;
  body: string;
}

interface PropertySubmissionNotification {
  propertyId: string;
  hostId: string;
  hostName: string;
  hostEmail: string;
  propertyName: string;
  propertyAddress: string;
  submissionDate: Date;
}

/**
 * Sends an email notification to the admin when a host submits a new property
 * @param propertyData The property submission data
 * @returns Promise that resolves when the notification is sent
 */
export async function sendPropertySubmissionNotification(propertyData: PropertySubmissionNotification): Promise<boolean> {
  try {
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

    const notification: EmailNotification = {
      to: adminEmail,
      subject: `New Property Submission: ${propertyData.propertyName}`,
      body: `
        <h2>New Property Submission</h2>
        <p>A host has submitted a new property for review.</p>

        <h3>Property Details:</h3>
        <ul>
          <li><strong>Property Name:</strong> ${propertyData.propertyName}</li>
          <li><strong>Property Address:</strong> ${propertyData.propertyAddress}</li>
          <li><strong>Submission Date:</strong> ${propertyData.submissionDate.toLocaleString()}</li>
        </ul>

        <h3>Host Details:</h3>
        <ul>
          <li><strong>Host Name:</strong> ${propertyData.hostName}</li>
          <li><strong>Host Email:</strong> ${propertyData.hostEmail}</li>
        </ul>

        <p>Please review this submission in the <a href="/admin/properties/${propertyData.propertyId}">Admin Portal</a>.</p>
      `
    };

    // In a real implementation, this would call a server API endpoint
    // For now, we'll just log it and return success
    console.log('Sending property submission notification:', notification);

    // In a real implementation, this would be an API call
    // return await sendEmailNotification(notification);
    return true;
  } catch (error) {
    console.error('Failed to send property submission notification:', error);
    return false;
  }
}

/**
 * Sends an approval notification to a host when their property is approved
 * @param hostEmail The host's email address
 * @param propertyName The name of the approved property
 * @returns Promise that resolves when the notification is sent
 */
export async function sendPropertyApprovalNotification(hostEmail: string, propertyName: string): Promise<boolean> {
  try {
    const notification: EmailNotification = {
      to: hostEmail,
      subject: `Your Property "${propertyName}" Has Been Approved!`,
      body: `
        <h2>Congratulations!</h2>
        <p>Your property "${propertyName}" has been approved and is now live on HouseGoing.</p>

        <p>Your property is now visible to potential guests and available for bookings.</p>

        <p>You can manage your property and view bookings in the <a href="/host/dashboard">Host Dashboard</a>.</p>

        <p>Thank you for choosing HouseGoing!</p>
      `
    };

    // In a real implementation, this would call a server API endpoint
    console.log('Sending property approval notification:', notification);

    // In a real implementation, this would be an API call
    // return await sendEmailNotification(notification);
    return true;
  } catch (error) {
    console.error('Failed to send property approval notification:', error);
    return false;
  }
}

/**
 * Sends a rejection notification to a host when their property is rejected
 * @param hostEmail The host's email address
 * @param propertyName The name of the rejected property
 * @param rejectionReason The reason for rejection
 * @returns Promise that resolves when the notification is sent
 */
export async function sendPropertyRejectionNotification(
  hostEmail: string,
  propertyName: string,
  rejectionReason: string
): Promise<boolean> {
  try {
    const notification: EmailNotification = {
      to: hostEmail,
      subject: `Update on Your Property "${propertyName}"`,
      body: `
        <h2>Property Update</h2>
        <p>We've reviewed your property "${propertyName}" and unfortunately, we cannot approve it at this time.</p>

        <h3>Reason:</h3>
        <p>${rejectionReason}</p>

        <p>You can update your property details and resubmit for approval in the <a href="/host/dashboard">Host Dashboard</a>.</p>

        <p>If you have any questions, please contact our support team.</p>
      `
    };

    // In a real implementation, this would call a server API endpoint
    console.log('Sending property rejection notification:', notification);

    // In a real implementation, this would be an API call
    // return await sendEmailNotification(notification);
    return true;
  } catch (error) {
    console.error('Failed to send property rejection notification:', error);
    return false;
  }
}
