import React, { useState, useRef } from 'react';
import { Clock, ChevronDown, Sun, Moon } from 'lucide-react';
import { useClickOutside } from '../../hooks/useClickOutside';
import { useEscapeKey } from '../../hooks/useEscapeKey';

interface TimeSelectorProps {
  label: string;
  value: string;
  onChange: (time: string) => void;
  availableHours: number[];
  disabled?: boolean;
}

export default function TimeSelector({ label, value, onChange, availableHours, disabled }: TimeSelectorProps) {
  const [showDropdown, setShowDropdown] = useState(false);

  // Ref for click outside detection
  const timeRef = useRef<HTMLDivElement>(null);
  useClickOutside(timeRef, () => setShowDropdown(false), showDropdown);
  useEscapeKey(() => setShowDropdown(false), showDropdown);

  // Format time for display
  const formatDisplayTime = (timeString: string) => {
    if (!timeString) return '';

    // Convert 24-hour format to 12-hour format with AM/PM
    const hour = parseInt(timeString.split(':')[0]);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM

    return `${hour12}:00 ${ampm}`;
  };

  // Group hours into 4-hour blocks for easier selection
  const groupHours = (hours: number[]) => {
    // Create groups of hours
    const groups: Record<string, number[]> = {};

    // Early morning (12am-6am)
    groups['earlyMorning'] = hours.filter(h => h >= 0 && h < 6);

    // Morning (6am-12pm)
    groups['morning'] = hours.filter(h => h >= 6 && h < 12);

    // Afternoon (12pm-6pm)
    groups['afternoon'] = hours.filter(h => h >= 12 && h < 18);

    // Evening (6pm-12am)
    groups['evening'] = hours.filter(h => h >= 18 && h < 24);

    return groups;
  };

  const hourGroups = groupHours(availableHours);

  // Get label for time group
  const getGroupLabel = (group: string) => {
    switch(group) {
      case 'earlyMorning': return 'Early Morning (12am-6am)';
      case 'morning': return 'Morning (6am-12pm)';
      case 'afternoon': return 'Afternoon (12pm-6pm)';
      case 'evening': return 'Evening (6pm-12am)';
      default: return '';
    }
  };

  // Check if hour is in next day (for overnight bookings)
  const isNextDay = (hour: number, referenceHour?: number) => {
    if (!referenceHour) return false;
    return hour < referenceHour && referenceHour > 12; // Assuming bookings starting after noon
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      <div className="relative" ref={timeRef}>
        <div
          className={`relative cursor-pointer bg-white rounded-lg border ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'hover:border-purple-400'} ${value ? 'border-gray-300' : 'border-gray-300'} transition-colors`}
          onClick={() => !disabled && setShowDropdown(!showDropdown)}
        >
          <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-500 h-5 w-5" />
          <div className="pl-10 w-full pr-10 py-3 rounded-lg text-gray-700">
            {value ? formatDisplayTime(value) : 'Select time'}
          </div>
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
          </div>
        </div>

        {/* Hidden input for form submission */}
        <input
          type="hidden"
          value={value}
          required
          disabled={disabled}
        />

        {/* Time dropdown */}
        {showDropdown && !disabled && (
          <div className="absolute z-20 mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-200 max-h-72 overflow-y-auto animate-fadeIn">
            {/* Simple time picker */}
            <div className="p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-medium text-gray-700">Select {label.toLowerCase()}</h3>
                <button
                  type="button"
                  onClick={() => setShowDropdown(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>

              {/* Time grid */}
              <div className="space-y-4">
                {Object.keys(hourGroups).map(group => {
                  // Only show groups that have hours
                  if (hourGroups[group].length === 0) return null;

                  // Get reference hour for overnight detection
                  const referenceHour = label === "Start Time" ? undefined :
                    value ? parseInt(value.split(':')[0]) : undefined;

                  return (
                    <div key={group}>
                      {hourGroups[group].length > 0 && (
                        <div className="mb-2">
                          <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">{getGroupLabel(group)}</h4>
                          <div className="grid grid-cols-4 gap-2 mt-1">
                            {hourGroups[group].map(hour => {
                              const timeString = `${hour.toString().padStart(2, '0')}:00`;
                              const isSelected = value === timeString;
                              const isNextDayHour = isNextDay(hour, referenceHour);

                              return (
                                <button
                                  key={hour}
                                  type="button"
                                  onClick={() => {
                                    onChange(timeString);
                                    setShowDropdown(false);
                                  }}
                                  className={`
                                    py-3 px-1 rounded-md text-center relative
                                    ${isSelected ? 'bg-purple-600 text-white' : 'hover:bg-purple-50 text-gray-700 border border-gray-200'}
                                  `}
                                >
                                  <span className="text-sm">{formatDisplayTime(timeString)}</span>
                                  {isNextDayHour && (
                                    <span className="absolute top-0 right-0 bg-purple-500 text-white text-xs px-1 rounded-bl-md rounded-tr-md">
                                      +1
                                    </span>
                                  )}
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}