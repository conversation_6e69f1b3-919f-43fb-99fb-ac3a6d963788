import { useState, useEffect } from 'react';
import { useAuth } from '../providers/AuthProvider';
import { getSupabaseClient } from '../lib/supabase-client';

export interface Conversation {
  id: string;
  otherUser: {
    id: string;
    name: string;
    image: string;
    role: string;
  };
  booking?: {
    id: string;
    venue_name: string;
    start_date: string;
    end_date: string;
    status: string;
  };
  lastMessage: {
    content: string;
    timestamp: string;
    is_read: boolean;
    is_from_current_user: boolean;
  };
  unreadCount: number;
}

export function useConversations() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchConversations = async () => {
    if (!user) {
      setError('User not authenticated');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // In a real implementation, we would fetch this from the API
      // For now, we'll use mock data based on the user's role

      // Get the centralized Supabase client
      const supabase = getSupabaseClient();

      // Determine if the user is a host or guest
      const { data: userProfile, error: userError } = await supabase
        .from('user_profiles')
        .select('role')
        .eq('clerk_id', user.id)
        .single();

      if (userError) throw userError;

      const isHost = userProfile?.role === 'host';

      // For demonstration purposes, we'll create mock conversations
      // In a real app, you would fetch this from your database
      const mockConversations: Conversation[] = [];

      if (isHost) {
        // Host sees conversations with guests
        mockConversations.push({
          id: '1',
          otherUser: {
            id: 'guest-1',
            name: 'John Smith',
            image: 'https://randomuser.me/api/portraits/men/32.jpg',
            role: 'guest'
          },
          booking: {
            id: 'booking-1',
            venue_name: 'Beachside Villa',
            start_date: '2023-07-28T14:30:00Z',
            end_date: '2023-07-29T11:00:00Z',
            status: 'confirmed'
          },
          lastMessage: {
            content: 'Hi, I have a question about the check-in process.',
            timestamp: '2023-07-28T14:30:00Z',
            is_read: false,
            is_from_current_user: false
          },
          unreadCount: 1
        });

        mockConversations.push({
          id: '2',
          otherUser: {
            id: 'guest-2',
            name: 'Sarah Johnson',
            image: 'https://randomuser.me/api/portraits/women/44.jpg',
            role: 'guest'
          },
          booking: {
            id: 'booking-2',
            venue_name: 'Mountain Cabin',
            start_date: '2023-08-15T14:30:00Z',
            end_date: '2023-08-17T11:00:00Z',
            status: 'confirmed'
          },
          lastMessage: {
            content: 'Thanks for accepting my booking!',
            timestamp: '2023-07-27T09:15:00Z',
            is_read: true,
            is_from_current_user: false
          },
          unreadCount: 0
        });
      } else {
        // Guest sees conversations with hosts
        mockConversations.push({
          id: '1',
          otherUser: {
            id: 'host-1',
            name: 'Michael Chen',
            image: 'https://randomuser.me/api/portraits/men/22.jpg',
            role: 'host'
          },
          booking: {
            id: 'booking-1',
            venue_name: 'Luxury Penthouse',
            start_date: '2023-08-10T15:00:00Z',
            end_date: '2023-08-12T11:00:00Z',
            status: 'confirmed'
          },
          lastMessage: {
            content: 'Looking forward to hosting your event!',
            timestamp: '2023-07-29T10:45:00Z',
            is_read: true,
            is_from_current_user: false
          },
          unreadCount: 0
        });

        mockConversations.push({
          id: '3',
          otherUser: {
            id: 'host-3',
            name: 'Emma Wilson',
            image: 'https://randomuser.me/api/portraits/women/29.jpg',
            role: 'host'
          },
          booking: {
            id: 'booking-3',
            venue_name: 'Garden Party Venue',
            start_date: '2023-09-05T16:00:00Z',
            end_date: '2023-09-05T23:00:00Z',
            status: 'pending'
          },
          lastMessage: {
            content: 'I\'ve sent you the booking details. Please let me know if you have any questions!',
            timestamp: '2023-07-26T14:20:00Z',
            is_read: false,
            is_from_current_user: false
          },
          unreadCount: 2
        });
      }

      setConversations(mockConversations);
    } catch (err: any) {
      console.error('Error fetching conversations:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  return {
    conversations,
    isLoading,
    error,
    refreshConversations: fetchConversations,
  };
}
