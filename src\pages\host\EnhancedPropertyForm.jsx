import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Popup } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { submitProperty } from '../../lib/api/propertyApi';
import { useSupabase } from '../../providers/SupabaseProvider';
import {
  Ruler, Building, SquareIcon, Phone, AlertTriangle,
  MapPin, Car, Bus, Landmark, Wine, Music,
  Utensils, Bath, Speaker, Sun, Thermometer, Sofa,
  Clock, Cigarette, PawPrint, DollarSign, ChevronLeft, ChevronRight,
  Calendar
} from 'lucide-react';

// Import components
import AddressLookup from '../../components/host/AddressLookup';
import PhotoUpload from '../../components/host/PhotoUpload';
import SafetyChecklist from '../../components/host/SafetyChecklist';

// Dynamically import Leaflet map to avoid SSR issues
const LeafletMap = React.lazy(() => import('../../components/LeafletMap'));

// Define venue types
const venueTypes = [
  'House', 'Apartment', 'Backyard', 'Rooftop', 'Commercial Space',
  'Function Room', 'Studio', 'Warehouse', 'Bar', 'Restaurant'
];

// Define amenities
const amenityOptions = [
  { id: 'parking', label: 'Parking Available', icon: <Car className="h-4 w-4" /> },
  { id: 'publicTransport', label: 'Near Public Transport', icon: <Bus className="h-4 w-4" /> },
  { id: 'byo', label: 'BYO Alcohol Allowed', icon: <Wine className="h-4 w-4" /> },
  { id: 'kitchen', label: 'Kitchen Facilities', icon: <Utensils className="h-4 w-4" /> },
  { id: 'bathroom', label: 'Private Bathroom', icon: <Bath className="h-4 w-4" /> },
  { id: 'soundSystem', label: 'Sound System', icon: <Speaker className="h-4 w-4" /> },
  { id: 'outdoorSpace', label: 'Outdoor Space', icon: <Sun className="h-4 w-4" /> },
  { id: 'aircon', label: 'Air Conditioning', icon: <Thermometer className="h-4 w-4" /> },
  { id: 'furniture', label: 'Furniture Provided', icon: <Sofa className="h-4 w-4" /> },
];

export default function EnhancedPropertyForm() {
  const { userProfile } = useSupabase();
  const navigate = useNavigate();
  const mapRef = useRef(null);

  // Form steps
  const steps = [
    'Basic Information',
    'Venue Details',
    'Amenities & Features',
    'House Rules & Policies',
    'Safety Requirements',
    'Photos',
    'Bank Details',
    'Review & Submit'
  ];

  const [currentStep, setCurrentStep] = useState(0);
  const [showMap, setShowMap] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [saveStatus, setSaveStatus] = useState('');
  const [isSaving, setIsSaving] = useState(false);

    // Initialize form data
    const [formData, setFormData] = useState({
      // Basic Info
      phoneNumber: '',
      name: '',
      type: 'House',
      address: '',
      location: [-33.8688, 151.2093], // Default to Sydney
      partyAcknowledgment: false,

      // Venue Details
      description: '',
      size: 0,
      functionRooms: 0,
      eventSpaces: 0,
      maxGuests: 1,
      price: 0,
      cleaningFee: 0,
      cleaningFeeIncluded: true,
      securityDepositPercentage: 20,

    // Amenities & Features
    amenities: [],
    customAmenities: '',
    parkingDetails: '',
    transportDetails: '',
    nearbyLandmarks: '',
    byoPolicy: '',

    // House Rules
    noiseRestrictions: '',
    endTime: '', // Legacy field for backward compatibility
    weekdayEndTime: '',
    weekendEndTime: '',
    allowIndoorLate: false,
    decorationsPolicy: '',
    smokingPolicy: '',
    petPolicy: '',
    safetyItems: [],

    // Safety Requirements
    safetyChecklist: [],
    safetyAcknowledgment: false,

    // Photos
    images: [],

    // Bank Details
    bankDetails: {
      accountName: '',
      bsb: '',
      accountNumber: '',
      bankName: ''
    }
  });

  // Handle map click to update location
  const handleMapClick = (e) => {
    const { lat, lng } = e.latlng;
    setFormData(prev => ({
      ...prev,
      location: [lat, lng]
    }));
  };

  // Handle form field changes
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle bank details changes
  const handleBankDetailsChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      bankDetails: {
        ...prev.bankDetails,
        [field]: value
      }
    }));
  };

  // Handle amenity toggle
  const handleAmenityToggle = (amenityId) => {
    setFormData(prev => {
      const amenities = [...prev.amenities];
      if (amenities.includes(amenityId)) {
        return {
          ...prev,
          amenities: amenities.filter(id => id !== amenityId)
        };
      } else {
        return {
          ...prev,
          amenities: [...amenities, amenityId]
        };
      }
    });
  };

  // Handle safety item toggle
  const handleSafetyItemToggle = (itemId) => {
    setFormData(prev => {
      const safetyItems = [...prev.safetyItems];
      if (safetyItems.includes(itemId)) {
        return {
          ...prev,
          safetyItems: safetyItems.filter(id => id !== itemId)
        };
      } else {
        return {
          ...prev,
          safetyItems: [...safetyItems, itemId]
        };
      }
    });
  };

  // Handle safety checklist toggle
  const handleSafetyChecklistToggle = (itemId) => {
    setFormData(prev => {
      const safetyChecklist = [...prev.safetyChecklist];
      if (safetyChecklist.includes(itemId)) {
        return {
          ...prev,
          safetyChecklist: safetyChecklist.filter(id => id !== itemId)
        };
      } else {
        return {
          ...prev,
          safetyChecklist: [...safetyChecklist, itemId]
        };
      }
    });
  };

  // Handle safety acknowledgment
  const handleSafetyAcknowledgment = (acknowledged) => {
    setFormData(prev => ({
      ...prev,
      safetyAcknowledgment: acknowledged
    }));
  };

  // Handle images uploaded
  const handleImagesUploaded = (imageUrls) => {
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...imageUrls]
    }));
  };

  // Validate Australian phone number
  const validatePhoneNumber = (phone) => {
    return /^(?:\+?61|0)[2-478](?:[ -]?[0-9]){8}$/.test(phone);
  };

  // Validate BSB format (6 digits)
  const validateBSB = (bsb) => {
    return /^\d{6}$/.test(bsb);
  };

  // Validate account number (6-10 digits)
  const validateAccountNumber = (accountNumber) => {
    return /^\d{6,10}$/.test(accountNumber);
  };

  // Validate current step
  const validateStep = () => {
    switch (currentStep) {
      case 0: // Basic Information
        if (!formData.phoneNumber || !validatePhoneNumber(formData.phoneNumber)) {
          setError('Please enter a valid Australian phone number');
          return false;
        }
        if (!formData.name) {
          setError('Please enter a venue name');
          return false;
        }
        if (!formData.address) {
          setError('Please enter a venue address');
          return false;
        }
        if (!formData.partyAcknowledgment) {
          setError('Please acknowledge that HouseGoing is a party-focused platform');
          return false;
        }
        break;

      case 1: // Venue Details
        if (!formData.description) {
          setError('Please provide a venue description');
          return false;
        }
        if (formData.size <= 0) {
          setError('Please enter a valid venue size in square metres');
          return false;
        }
        if (formData.eventSpaces <= 0) {
          setError('Please enter at least one event space');
          return false;
        }
        if (formData.maxGuests <= 0) {
          setError('Please enter a valid maximum number of guests');
          return false;
        }
        if (formData.price <= 0) {
          setError('Please enter a valid hourly rate');
          return false;
        }
        break;

      case 2: // Amenities & Features
        if (!formData.byoPolicy) {
          setError('Please select a BYO Alcohol Policy');
          return false;
        }
        break;

      case 3: // House Rules & Policies
        if (formData.safetyItems.length === 0) {
          setError('Please select at least one safety item that your venue has');
          return false;
        }
        break;

      case 4: // Safety Requirements
        const requiredSafetyItems = ['smoke_alarms', 'fire_extinguisher', 'emergency_exits', 'structural_safety', 'public_liability', 'emergency_info'];
        const completedRequired = requiredSafetyItems.filter(item => formData.safetyChecklist.includes(item));

        if (completedRequired.length !== requiredSafetyItems.length) {
          setError('Please complete all required safety items before proceeding');
          return false;
        }

        if (!formData.safetyAcknowledgment) {
          setError('Please acknowledge the safety declaration to proceed');
          return false;
        }
        break;

      case 6: // Bank Details
        if (!formData.bankDetails.accountName) {
          setError('Please enter the account holder name');
          return false;
        }
        if (!validateBSB(formData.bankDetails.bsb)) {
          setError('Please enter a valid 6-digit BSB number');
          return false;
        }
        if (!validateAccountNumber(formData.bankDetails.accountNumber)) {
          setError('Please enter a valid account number (6-10 digits)');
          return false;
        }
        if (!formData.bankDetails.bankName) {
          setError('Please enter the bank name');
          return false;
        }
        break;
    }

    setError('');
    return true;
  };

  // Navigate to next step
  const nextStep = () => {
    if (validateStep()) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateStep()) {
      return;
    }

    // Additional validation for required fields
    if (!formData.byoPolicy) {
      setError('Please select a BYO Alcohol Policy');
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      // Check if user is logged in
      if (!userProfile) {
        setError('You must be logged in to submit a property. Please log in and try again.');
        window.scrollTo({ top: 0, behavior: 'smooth' });
        return;
      }

      console.log('User profile:', userProfile);

      // Ensure location is exactly [number, number] as required by the API
      const location = [formData.location[0], formData.location[1]];

      const submissionData = {
        name: formData.name,
        address: formData.address,
        description: formData.description,
        type: formData.type,
        maxGuests: formData.maxGuests,
        price: formData.price,
        location,
        bankDetails: formData.bankDetails,
        size: formData.size,
        functionRooms: formData.functionRooms,
        eventSpaces: formData.eventSpaces,
        images: formData.images,
        phoneNumber: formData.phoneNumber,
        amenities: formData.amenities,
        customAmenities: formData.customAmenities,
        parkingDetails: formData.parkingDetails,
        transportDetails: formData.transportDetails,
        nearbyLandmarks: formData.nearbyLandmarks,
        byoPolicy: formData.byoPolicy,
        noiseRestrictions: formData.noiseRestrictions,
        endTime: formData.endTime, // Legacy field
        weekdayEndTime: formData.weekdayEndTime,
        weekendEndTime: formData.weekendEndTime,
        allowIndoorLate: formData.allowIndoorLate,
        decorationsPolicy: formData.decorationsPolicy,
        smokingPolicy: formData.smokingPolicy,
        petPolicy: formData.petPolicy,
        safetyItems: formData.safetyItems,
        safetyChecklist: formData.safetyChecklist,
        safetyAcknowledgment: formData.safetyAcknowledgment,
        // Use clerk_id if available, otherwise fall back to id
        ownerId: userProfile?.clerk_id || userProfile?.id || 'guest',
        status: 'pending'
      };

      console.log('Submitting property with data:', submissionData);

      const result = await submitProperty(submissionData);
      console.log('Submission result:', result);

      // Clear draft
      localStorage.removeItem('propertyFormDraft');
      setSuccess(true);

      // Create a toast notification
      const toast = document.createElement('div');
      toast.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded shadow-lg z-50';
      toast.innerHTML = `
        <div class="flex items-center">
          <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span>Property submitted successfully!</span>
      </div>
      `;
      document.body.appendChild(toast);

      // Remove toast after 5 seconds
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 5000);

      // Show success message and redirect after a delay
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setTimeout(() => navigate('/host/properties'), 2000);
    } catch (err) {
      setError(`Failed to submit venue: ${err.message || 'Please try again.'}`);
      console.error('Submission error:', err);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setSubmitting(false);
    }
  };

  // Render form based on current step
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return renderBasicInfo();
      case 1:
        return renderVenueDetails();
      case 2:
        return renderAmenitiesFeatures();
      case 3:
        return renderHouseRules();
      case 4:
        return renderSafetyRequirements();
      case 5:
        return renderPhotos();
      case 6:
        return renderBankDetails();
      case 7:
        return renderReview();
      default:
        return renderBasicInfo();
    }
  };

  // Step 1: Basic Information
  const renderBasicInfo = () => (
          <div className="space-y-6">
            <div className="space-y-4">
              <label className="block mb-2 font-medium">Australian Phone Number*</label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Phone className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="tel"
            value={formData.phoneNumber}
            onChange={(e) => handleChange('phoneNumber', e.target.value)}
            className="pl-10 w-full p-2 border rounded"
            placeholder="e.g. 0412 345 678"
            required
          />
        </div>
        <p className="text-xs text-gray-500 mt-1">Enter your Australian mobile or landline number</p>
      </div>

      <div>
        <label className="block mb-2 font-medium">Venue Name*</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => handleChange('name', e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="e.g. Harbour View Terrace"
          required
        />
      </div>


      <div>
        <label className="block mb-2 font-medium">Address*</label>
        <AddressLookup
          value={formData.address}
          onChange={(address, location) => {
            handleChange('address', address);
            handleChange('location', location);
            setShowMap(true);
          }}
          required
          placeholder="Enter your venue address"
          className="w-full p-2 border rounded"
        />
        <button
          type="button"
          onClick={() => setShowMap(!showMap)}
          className="mt-2 text-sm text-blue-600"
        >
          {showMap ? 'Hide Map' : 'Show Map'}
        </button>
      </div>

      {showMap && (
        <div className="h-64 border rounded overflow-hidden">
          <React.Suspense fallback={<div className="h-64 bg-gray-100 rounded flex items-center justify-center">Loading map...</div>}>
            <LeafletMap
              center={formData.location}
              zoom={15}
              onClick={handleMapClick}
              ref={mapRef}
              className="h-full w-full"
            >
              <Marker position={formData.location}>
                <Popup>Venue Location</Popup>
              </Marker>
            </LeafletMap>
          </React.Suspense>
        </div>
      )}

      <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">Important Notice</h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                HouseGoing is a party-focused platform. By listing your venue, you acknowledge that it may be used for parties and events.
                If your space doesn't allow parties, you can still submit your listing, but it may not be approved for the platform.
              </p>
            </div>
            <div className="mt-4">
              <div className="flex items-center">
                <input
                  id="party-acknowledgment"
                  type="checkbox"
                  checked={formData.partyAcknowledgment}
                  onChange={(e) => handleChange('partyAcknowledgment', e.target.checked)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <label htmlFor="party-acknowledgment" className="ml-2 block text-sm text-amber-700">
                  I understand that HouseGoing is a party-focused platform
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Step 2: Venue Details
  const renderVenueDetails = () => (
      <div className="space-y-6">
        <div>
          <label className="block mb-2 font-medium">Venue Description*</label>
          <textarea
            value={formData.description}
            onChange={(e) => handleChange('description', e.target.value)}
            className="w-full p-2 border rounded"
            rows={4}
            placeholder="Describe your venue in detail. What makes it special? What kind of events is it suitable for?"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block mb-2 font-medium flex items-center">
              <Ruler className="h-4 w-4 mr-1" />
              Size (m²)*
            </label>
            <input
              type="number"
              value={formData.size}
              onChange={(e) => handleChange('size', parseInt(e.target.value) || 0)}
              min="1"
              className="w-full p-2 border rounded"
              placeholder="e.g. 100"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Total area in square metres</p>
          </div>

          <div>
            <label className="block mb-2 font-medium flex items-center">
              <Building className="h-4 w-4 mr-1" />
              Function Rooms
            </label>
            <input
              type="number"
              value={formData.functionRooms}
              onChange={(e) => handleChange('functionRooms', parseInt(e.target.value) || 0)}
              min="0"
              className="w-full p-2 border rounded"
              placeholder="e.g. 2"
            />
            <p className="text-xs text-gray-500 mt-1">Number of separate function rooms</p>
          </div>

          <div>
            <label className="block mb-2 font-medium flex items-center">
              <SquareIcon className="h-4 w-4 mr-1" />
              Event Spaces*
            </label>
            <input
              type="number"
              value={formData.eventSpaces}
              onChange={(e) => handleChange('eventSpaces', parseInt(e.target.value) || 0)}
              min="1"
              className="w-full p-2 border rounded"
              placeholder="e.g. 1"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Total number of usable event spaces</p>
          </div>

          <div>
            <label className="block mb-2 font-medium flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              Hourly Rate (AUD)*
            </label>
            <input
              type="number"
              value={formData.price}
              onChange={(e) => handleChange('price', parseInt(e.target.value) || 0)}
              min="1"
              className="w-full p-2 border rounded"
              placeholder="e.g. 150"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Price per hour in Australian dollars</p>
          </div>

          <div>
            <label className="block mb-2 font-medium">Cleaning Fee</label>
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="cleaningIncluded"
                  name="cleaningFeeOption"
                  checked={formData.cleaningFeeIncluded}
                  onChange={() => handleChange('cleaningFeeIncluded', true)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                />
                <label htmlFor="cleaningIncluded" className="ml-2 block text-sm text-gray-700">
                  No cleaning fee
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="cleaningSeparate"
                  name="cleaningFeeOption"
                  checked={!formData.cleaningFeeIncluded}
                  onChange={() => handleChange('cleaningFeeIncluded', false)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                />
                <label htmlFor="cleaningSeparate" className="ml-2 block text-sm text-gray-700">
                  Add cleaning fee
                </label>
              </div>

              {!formData.cleaningFeeIncluded && (
                <div className="ml-6 mt-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Cleaning Fee Amount (AUD)</label>
                  <div className="relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input
                      type="number"
                      value={formData.cleaningFee}
                      onChange={(e) => handleChange('cleaningFee', parseFloat(e.target.value) || 0)}
                      className="focus:ring-purple-500 focus:border-purple-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                      placeholder="0.00"
                      min="0"
                      step="5"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    This fee will be shown to guests at checkout
                  </p>
                </div>
              )}
            </div>
          </div>

          <div>
            <label className="block mb-2 font-medium flex items-center">
              Security Deposit
            </label>
            <div className="bg-blue-50 p-3 rounded-md flex items-center gap-2">
              <span className="text-sm">20% of the total booking</span>
              <div className="relative group">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-blue-600 cursor-help"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-gray-800 text-white text-xs rounded p-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                  20% is a standard deposit. The security deposit is calculated as 20% of the total booking amount (hourly rate × number of hours booked). This amount is held on the guest's card before the event and released after if there are no issues.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  );

  // Step 3: Amenities & Features
  const renderAmenitiesFeatures = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Amenities & Features</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {amenityOptions.map(amenity => (
            <div key={amenity.id} className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id={`amenity-${amenity.id}`}
                  type="checkbox"
                  checked={formData.amenities.includes(amenity.id)}
                  onChange={() => handleAmenityToggle(amenity.id)}
                  className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor={`amenity-${amenity.id}`} className="font-medium text-gray-700 flex items-center">
                  {amenity.icon}
                  <span className="ml-2">{amenity.label}</span>
                </label>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block mb-2 font-medium">Additional Amenities & Features</label>
        <textarea
          value={formData.customAmenities}
          onChange={(e) => handleChange('customAmenities', e.target.value)}
          className="w-full p-2 border rounded"
          rows={3}
          placeholder="List any additional amenities or features not mentioned above (e.g., BBQ, pool table, projector, etc.)"
        />
        <p className="text-xs text-gray-500 mt-1">Add any custom amenities or features that aren't in the checklist above</p>
      </div>

      <div>
        <label className="block mb-2 font-medium">Parking Details</label>
        <textarea
          value={formData.parkingDetails}
          onChange={(e) => handleChange('parkingDetails', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe parking options (e.g., 2 off-street spots, street parking available, etc.)"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">Public Transportation</label>
        <textarea
          value={formData.transportDetails}
          onChange={(e) => handleChange('transportDetails', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe nearby public transport options (e.g., 5 min walk to Central Station)"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">Nearby Landmarks</label>
        <textarea
          value={formData.nearbyLandmarks}
          onChange={(e) => handleChange('nearbyLandmarks', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="List nearby landmarks or points of interest"
        />
      </div>

      <div>
        <label className="block mb-2 font-medium">BYO Alcohol Policy*</label>
        <select
          value={formData.byoPolicy}
          onChange={(e) => handleChange('byoPolicy', e.target.value)}
          className="w-full p-2 border rounded"
          required
        >
          <option value="">Select an option</option>
          <option value="allowed">BYO Allowed - No Restrictions</option>
          <option value="allowed_with_restrictions">BYO Allowed - With Restrictions</option>
          <option value="not_allowed">BYO Not Allowed</option>
          <option value="licensed">Licensed Venue - No BYO</option>
        </select>
      </div>
    </div>
  );

  // Step 4: House Rules & Policies
  const renderHouseRules = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <h4 className="font-medium">Noise Restrictions</h4>
        <textarea
          value={formData.noiseRestrictions}
          onChange={(e) => handleChange('noiseRestrictions', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe any noise restrictions (e.g., music must be turned down after 10pm)"
        />
      </div>
      <div className="space-y-4">
        <h4 className="font-medium">Operating Hours</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block mb-2 font-medium">Weekday Start Time</label>
            <select
              value={formData.weekdayStartTime}
              onChange={(e) => handleChange('weekdayStartTime', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="">Select an option</option>
              <option value="no_restriction">No Restriction</option>
              <option value="9am">9:00 AM</option>
              <option value="10am">10:00 AM</option>
              <option value="11am">11:00 AM</option>
              <option value="12pm">12:00 PM</option>
              <option value="1pm">1:00 PM</option>
              <option value="2pm">2:00 PM</option>
              <option value="3pm">3:00 PM</option>
              <option value="4pm">4:00 PM</option>
              <option value="5pm">5:00 PM</option>
              <option value="6pm">6:00 PM</option>
              <option value="7pm">7:00 PM</option>
              <option value="8pm">8:00 PM</option>
            </select>
          </div>
          <div>
            <label className="block mb-2 font-medium">Weekday End Time</label>
            <select
              value={formData.weekdayEndTime}
              onChange={(e) => handleChange('weekdayEndTime', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="">Select an option</option>
              <option value="9pm">9:00 PM</option>
              <option value="10pm">10:00 PM</option>
              <option value="11pm">11:00 PM</option>
              <option value="12am">12:00 AM</option>
              <option value="1am">1:00 AM</option>
              <option value="2am">2:00 AM</option>
              <option value="3am">3:00 AM</option>
              <option value="indoor_only">No Outdoor Restriction (Indoor Only)</option>
              <option value="no_restriction">No Restriction</option>
            </select>
          </div>
          <div>
            <label className="block mb-2 font-medium">Weekend Start Time</label>
            <select
              value={formData.weekendStartTime}
              onChange={(e) => handleChange('weekendStartTime', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="">Select an option</option>
              <option value="no_restriction">No Restriction</option>
              <option value="9am">9:00 AM</option>
              <option value="10am">10:00 AM</option>
              <option value="11am">11:00 AM</option>
              <option value="12pm">12:00 PM</option>
              <option value="1pm">1:00 PM</option>
              <option value="2pm">2:00 PM</option>
              <option value="3pm">3:00 PM</option>
              <option value="4pm">4:00 PM</option>
              <option value="5pm">5:00 PM</option>
              <option value="6pm">6:00 PM</option>
              <option value="7pm">7:00 PM</option>
              <option value="8pm">8:00 PM</option>
            </select>
          </div>
          <div>
            <label className="block mb-2 font-medium">Weekend End Time</label>
            <select
              value={formData.weekendEndTime}
              onChange={(e) => handleChange('weekendEndTime', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="">Select an option</option>
              <option value="9pm">9:00 PM</option>
              <option value="10pm">10:00 PM</option>
              <option value="11pm">11:00 PM</option>
              <option value="12am">12:00 AM</option>
              <option value="1am">1:00 AM</option>
              <option value="2am">2:00 AM</option>
              <option value="3am">3:00 AM</option>
              <option value="indoor_only">No Outdoor Restriction (Indoor Only)</option>
              <option value="no_restriction">No Restriction</option>
            </select>
          </div>
        </div>
        <div className="flex items-center mt-2">
          <input
            type="checkbox"
            id="allowIndoorLate"
            checked={formData.allowIndoorLate}
            onChange={(e) => handleChange('allowIndoorLate', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="allowIndoorLate" className="ml-2 block text-sm text-gray-700">
            Allow indoor activities after outdoor cutoff time (reduced noise levels)
          </label>
        </div>
      </div>
      <div>
        <label className="block mb-2 font-medium">Smoking Policy</label>
        <select
          value={formData.smokingPolicy}
          onChange={(e) => handleChange('smokingPolicy', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="not_allowed">No Smoking Allowed</option>
          <option value="outdoor_only">Outdoor Areas Only</option>
          <option value="designated_areas">Designated Areas Only</option>
          <option value="allowed">Smoking Allowed</option>
        </select>
      </div>
      <div>
        <label className="block mb-2 font-medium">Pet Policy</label>
        <select
          value={formData.petPolicy}
          onChange={(e) => handleChange('petPolicy', e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="not_allowed">No Pets Allowed</option>
          <option value="dogs_only">Dogs Only</option>
          <option value="case_by_case">Case by Case Basis</option>
          <option value="allowed">Pets Allowed</option>
        </select>
      </div>
      <div>
        <label className="block mb-2 font-medium">Decorations Policy</label>
        <textarea
          value={formData.decorationsPolicy}
          onChange={(e) => handleChange('decorationsPolicy', e.target.value)}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe any rules or restrictions for decorations (e.g., no confetti, no wall hangings, etc.)"
        />
      </div>
      <div>
        <h3 className="text-lg font-medium mb-4">Safety Items*</h3>
        <p className="text-sm text-gray-600 mb-4">
          Please select at least one safety item that your venue has. This information helps guests understand your venue's safety features.
        </p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Smoke Alarm */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="safety-item-smoke_alarm"
                type="checkbox"
                checked={formData.safetyItems.includes('smoke_alarm')}
                onChange={() => handleSafetyItemToggle('smoke_alarm')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm flex items-center">
              <label htmlFor="safety-item-smoke_alarm" className="font-medium text-gray-700">Smoke Alarm</label>
            </div>
          </div>
          {/* First Aid Kit */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="safety-item-first_aid_kit"
                type="checkbox"
                checked={formData.safetyItems.includes('first_aid_kit')}
                onChange={() => handleSafetyItemToggle('first_aid_kit')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm flex items-center">
              <label htmlFor="safety-item-first_aid_kit" className="font-medium text-gray-700">First Aid Kit</label>
            </div>
          </div>
          {/* Fire Extinguisher */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="safety-item-fire_extinguisher"
                type="checkbox"
                checked={formData.safetyItems.includes('fire_extinguisher')}
                onChange={() => handleSafetyItemToggle('fire_extinguisher')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm flex items-center">
              <label htmlFor="safety-item-fire_extinguisher" className="font-medium text-gray-700">Fire Extinguisher</label>
            </div>
          </div>
          {/* Carbon Monoxide Alarm */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="safety-item-carbon_monoxide_alarm"
                type="checkbox"
                checked={formData.safetyItems.includes('carbon_monoxide_alarm')}
                onChange={() => handleSafetyItemToggle('carbon_monoxide_alarm')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm flex items-center">
              <label htmlFor="safety-item-carbon_monoxide_alarm" className="font-medium text-gray-700">Carbon Monoxide Alarm</label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Step 5: Photos
  const renderPhotos = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Upload Property Photos</h3>
        <p className="text-sm text-gray-600 mb-4">
          High-quality photos help attract more bookings. Show your space at its best!
        </p>

        <div className="bg-gray-50 p-4 rounded-md mb-4">
          <h4 className="font-medium mb-2">Must-have shots:</h4>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            <li>Exterior & entrance</li>
            <li>Main event/party areas</li>
            <li>All function rooms</li>
            <li>Bathrooms</li>
            <li>Special features</li>
          </ul>

          <h4 className="font-medium mt-4 mb-2">Photo tips:</h4>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            <li>Use natural light</li>
            <li>Keep spaces tidy</li>
            <li>Show multiple angles</li>
            <li>Highlight unique features</li>
            <li>Minimum 1920x1080px resolution</li>
          </ul>
        </div>

        <PhotoUpload
          onImagesUploaded={handleImagesUploaded}
          existingImages={formData.images}
        />
      </div>
    </div>
  );

  // Step 6: Bank Details
  const renderBankDetails = () => (
    <div className="space-y-6">
      <p className="text-sm text-gray-600 mb-4">
        This information is required for processing your venue booking payments.
        Your details are securely stored and handled according to Australian privacy laws.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block mb-2 font-medium">Account Holder Name*</label>
          <input
            type="text"
            value={formData.bankDetails.accountName}
            onChange={(e) => handleBankDetailsChange('accountName', e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="e.g. John Smith"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Enter the name exactly as it appears on your bank account</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">BSB* (6 digits)</label>
          <input
            type="text"
            value={formData.bankDetails.bsb}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              // Limit to 6 digits
              const bsb = value.slice(0, 6);
              handleBankDetailsChange('bsb', bsb);
            }}
            pattern="\d{6}"
            maxLength={6}
            className="w-full p-2 border rounded"
            placeholder="e.g. 062000"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Australian BSB number (6 digits)</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Account Number* (6-10 digits)</label>
          <input
            type="text"
            value={formData.bankDetails.accountNumber}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              // Limit to 10 digits
              const accountNumber = value.slice(0, 10);
              handleBankDetailsChange('accountNumber', accountNumber);
            }}
            maxLength={10}
            className="w-full p-2 border rounded"
            placeholder="e.g. ********"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Your bank account number (6-10 digits)</p>
        </div>

        <div>
          <label className="block mb-2 font-medium">Bank Name*</label>
          <input
            type="text"
            value={formData.bankDetails.bankName}
            onChange={(e) => handleBankDetailsChange('bankName', e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="e.g. Commonwealth Bank"
            required
          />
          <p className="text-xs text-gray-500 mt-1">The name of your Australian bank</p>
        </div>
      </div>
    </div>
  );

  // Step 5: Safety Requirements
  const renderSafetyRequirements = () => (
    <div className="space-y-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">Safety Requirements</h2>
        <p className="text-gray-600">
          Please confirm your venue meets these essential safety standards. This helps ensure guest safety and protects you as a host.
        </p>
      </div>

      <SafetyChecklist
        safetyItems={formData.safetyChecklist}
        onSafetyItemToggle={handleSafetyChecklistToggle}
        acknowledgment={formData.safetyAcknowledgment}
        onAcknowledgmentChange={handleSafetyAcknowledgment}
      />
    </div>
  );

  // Step 8: Review & Submit
  const renderReview = () => (
    <div className="space-y-6">
      <p className="text-sm text-gray-600 mb-4">
        Please review your venue details before submitting. Once submitted, our team will review your listing
        and you'll receive a notification when it's approved.
      </p>

      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium mb-2">Basic Information</h3>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Venue Name</p>
            <p>{formData.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Venue Type</p>
            <p>{formData.type}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Phone Number</p>
            <p>{formData.phoneNumber}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Address</p>
            <p>{formData.address}</p>
          </div>
        </div>

        <h3 className="font-medium mb-2 border-t pt-4">Venue Details</h3>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Size</p>
            <p>{formData.size} m²</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Max Guests</p>
            <p>{formData.maxGuests}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Hourly Rate</p>
            <p>${formData.price}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Function Rooms</p>
            <p>{formData.functionRooms}</p>
          </div>
        </div>

        <h3 className="font-medium mb-2 border-t pt-4">Description</h3>
        <p className="mb-4">{formData.description}</p>

        <h3 className="font-medium mb-2 border-t pt-4">House Rules</h3>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Weekday End Time</p>
            <p>{formData.weekdayEndTime === 'indoor_only' ? 'Indoor Only' :
               formData.weekdayEndTime === 'no_restriction' ? 'No Restriction' :
               formData.weekdayEndTime || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Weekend End Time</p>
            <p>{formData.weekendEndTime === 'indoor_only' ? 'Indoor Only' :
               formData.weekendEndTime === 'no_restriction' ? 'No Restriction' :
               formData.weekendEndTime || 'Not specified'}</p>
          </div>
          <div className="col-span-2">
            <p className="text-sm font-medium text-gray-500">Indoor Activities After Cutoff</p>
            <p>{formData.allowIndoorLate ? 'Allowed (with reduced noise)' : 'Not allowed'}</p>
          </div>
          <div className="col-span-2">
            <p className="text-sm font-medium text-gray-500">Latest End Time</p>
            <div className="flex gap-4">
              <div>
                <p className="text-sm text-gray-600">Weekdays: {formData.weekdayEndTime === 'no_restriction' ? 'No restriction' : formData.weekdayEndTime}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Weekends: {formData.weekendEndTime === 'no_restriction' ? 'No restriction' : formData.weekendEndTime}</p>
              </div>
            </div>
          </div>
          <div className="col-span-2">
            <p className="text-sm font-medium text-gray-500">Noise Restrictions</p>
            <p className="whitespace-pre-line">{formData.noiseRestrictions || 'None specified'}</p>
          </div>
        </div>

        <h3 className="font-medium mb-2 border-t pt-4">Amenities</h3>
        <div className="flex flex-wrap gap-2 mb-4">
          {formData.amenities.length > 0 ? (
            formData.amenities.map(amenityId => {
              const amenity = amenityOptions.find(a => a.id === amenityId);
              return amenity ? (
                <span key={amenityId} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {amenity.label}
                </span>
              ) : null;
            })
          ) : (
            <p className="text-sm text-gray-500">No amenities selected</p>
          )}
        </div>

        {formData.customAmenities && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-1">Additional Amenities:</h4>
            <p className="text-sm">{formData.customAmenities}</p>
          </div>
        )}

        <h3 className="font-medium mb-2 border-t pt-4">Safety Items</h3>
        <div className="flex flex-wrap gap-2 mb-4">
          {formData.safetyItems.length > 0 ? (
            formData.safetyItems.map(itemId => {
              const safetyItemLabels = {
                smoke_alarm: 'Smoke alarm',
                first_aid_kit: 'First aid kit',
                fire_extinguisher: 'Fire extinguisher',
                carbon_monoxide_alarm: 'Carbon monoxide alarm'
              };
              return (
                <span key={itemId} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {safetyItemLabels[itemId]}
                </span>
              );
            })
          ) : (
            <p className="text-sm text-gray-500">No safety items selected</p>
          )}
        </div>

        <h3 className="font-medium mb-2 border-t pt-4">Photos</h3>
        <div className="grid grid-cols-3 gap-2 mb-4">
          {formData.images.length > 0 ? (
            formData.images.map((image, index) => (
              <img
                key={index}
                src={image}
                alt={`Venue ${index + 1}`}
                className="h-20 w-full object-cover rounded"
              />
            ))
          ) : (
            <p className="text-sm text-gray-500">No photos uploaded</p>
          )}
        </div>
      </div>

      <div className="flex items-center">
        <input
          id="terms-agreement"
          type="checkbox"
          required
          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
        />
        <label htmlFor="terms-agreement" className="ml-2 block text-sm text-gray-700">
          I agree to HouseGoing's <a href="/terms" className="text-purple-600 hover:text-purple-500">Terms of Service</a> and <a href="/privacy" className="text-purple-600 hover:text-purple-500">Privacy Policy</a>
        </label>
      </div>
    </div>
  );

  // Save draft without redirecting
  const saveDraft = () => {
    try {
      localStorage.setItem('propertyFormDraft', JSON.stringify(formData));

      // Create a toast notification element
      const toast = document.createElement('div');
      toast.className = 'fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded flex items-center shadow-lg z-50';
      toast.innerHTML = `
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>Draft saved successfully!</span>
      `;

      // Add to document
      document.body.appendChild(toast);

      // Remove toast after 3 seconds
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 3000);

      console.log('Draft saved successfully');
    } catch (err) {
      setError('Failed to save draft. Please try again.');
      console.error('Error saving draft:', err);
    }
  };

  // Save draft and continue later
  const saveAndContinueLater = () => {
    try {
      localStorage.setItem('propertyFormDraft', JSON.stringify(formData));

      // Show success message
      setError('');
      setSuccess(true);

      // Create a toast notification element
      const toast = document.createElement('div');
      toast.className = 'fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded flex items-center shadow-lg z-50';
      toast.innerHTML = `
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>Draft saved! Redirecting to properties page...</span>
      `;

      // Add to document
      document.body.appendChild(toast);

      console.log('Draft saved successfully, redirecting...');

      // Show success message and redirect after a delay
      setTimeout(() => {
        navigate('/host/properties');
      }, 1500);
    } catch (err) {
      setError('Failed to save draft. Please try again.');
      console.error('Error saving draft:', err);
    }
  };

  // Auto-save when form data changes
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (Object.keys(formData).length > 0) {
        setIsSaving(true);
        setSaveStatus('Saving...');
        try {
          localStorage.setItem('propertyFormDraft', JSON.stringify(formData));
          setSaveStatus('Draft saved');
          setTimeout(() => setSaveStatus(''), 2000);
        } catch (err) {
          console.error('Error auto-saving draft:', err);
          setSaveStatus('Error saving draft');
        } finally {
          setIsSaving(false);
        }
      }
    }, 1000); // 1 second debounce

    return () => clearTimeout(timer);
  }, [formData]);

  // Load draft from localStorage
  React.useEffect(() => {
    try {
      const savedDraft = localStorage.getItem('propertyFormDraft');
      if (savedDraft) {
        const parsedDraft = JSON.parse(savedDraft);
        setFormData(parsedDraft);
        console.log('Draft loaded successfully');
      }
    } catch (err) {
      console.error('Error loading draft:', err);
    }
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">List Your Venue</h1>

      {error && <div className="text-red-500 mb-4">{error}</div>}
      {success && (
        <div className="bg-green-100 text-green-700 p-4 mb-4 rounded flex flex-col gap-2">
          <p className="font-semibold">Venue submitted successfully!</p>
          <p>Thank you for listing your venue with HouseGoing. Our team will review your submission and get back to you shortly.</p>
          <p>You'll be redirected to your properties dashboard in a moment...</p>
        </div>
      )}

      {/* Autosave status indicator */}
      {saveStatus && (
        <div className={`fixed bottom-4 right-4 px-4 py-2 rounded shadow-lg transition-all duration-300 ${
          saveStatus.includes('Error') ? 'bg-red-100 text-red-800' :
          isSaving ? 'bg-yellow-100 text-yellow-800' :
          saveStatus ? 'bg-green-100 text-green-800' :
          'opacity-0'
        }`}>
          {saveStatus}
        </div>
      )}

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep >= index ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
              <span className="text-xs mt-1 hidden md:block">{step}</span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute inset-0 flex items-center" aria-hidden="true">
            <div className="w-full border-t border-gray-300"></div>
          </div>
        </div>
      </div>

      <form onSubmit={currentStep === steps.length - 1 ? handleSubmit : (e) => e.preventDefault()}>
        {/* Display the current step content */}
        {(currentStep === 0) && renderBasicInfo()}
        {(currentStep === 1) && renderVenueDetails()}
        {(currentStep === 2) && renderAmenitiesFeatures()}
        {(currentStep === 3) && renderHouseRules()}
        {(currentStep === 4) && renderSafetyRequirements()}
        {(currentStep === 5) && renderPhotos()}
        {(currentStep === 6) && renderBankDetails()}
        {(currentStep === 7) && renderReview()}

        <div className="mt-8 flex justify-between">
          <div className="flex space-x-2">
            {currentStep > 0 ? (
              <button
                type="button"
                onClick={prevStep}
                className="flex items-center px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </button>
            ) : (
              <div></div>
            )}

            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => saveDraft()}
                className="flex items-center px-4 py-2 border border-purple-300 text-purple-600 rounded hover:bg-purple-50"
              >
                Save Draft
              </button>

              <button
                type="button"
                onClick={saveAndContinueLater}
                className="flex items-center px-4 py-2 border border-gray-300 text-gray-600 rounded hover:bg-gray-50"
              >
                Save & Exit
              </button>
            </div>
          </div>

          {currentStep < steps.length - 1 ? (
            <button
              type="button"
              onClick={nextStep}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </button>
          ) : (
            <button
              type="submit"
              disabled={submitting}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:bg-purple-300"
            >
              {submitting ? 'Submitting...' : 'Submit Venue for Approval'}
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
