/* Utility classes for HouseGoing */

.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-glow {
  box-shadow: 0 0 15px rgba(124, 58, 237, 0.1);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Button styles */
.btn-primary {
  background-color: #7C3AED; /* purple-600 */
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  display: inline-flex;
  align-items: center;
  transition: all 0.2s;
}

.btn-primary:hover {
  background-color: #6D28D9; /* purple-700 */
}

.btn-primary:active {
  transform: scale(0.95);
}
