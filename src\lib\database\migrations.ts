/**
 * Database migrations for Supabase
 *
 * This file contains functions to create and update database tables
 * for the HouseGoing application.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/supabase';
import { setupAllFunctions } from './setup-functions';

/**
 * Run all migrations
 * @param supabase Supabase client
 */
export async function runMigrations(supabase: SupabaseClient<Database>) {
  console.log('Running database migrations...');

  try {
    // First, set up the required SQL functions
    console.log('Setting up required SQL functions...');
    const functionsSetup = await setupAllFunctions(supabase);
    if (!functionsSetup) {
      console.warn('Failed to set up all SQL functions, but continuing with migrations');
    } else {
      console.log('SQL functions set up successfully');
    }

    // Check if we can connect to the database
    const { data: connectionTest, error: connectionError } = await supabase
      .from('user_profiles')
      .select('id', { count: 'exact', head: true })
      .limit(1)
      .single();

    // If there's a connection error, log it but continue with migrations
    if (connectionError) {
      console.warn('Database connection test failed, but continuing with migrations:', connectionError.message);
    } else {
      console.log('Database connection test successful');
    }

    try {
      // Create user_profiles table if it doesn't exist
      await createUserProfilesTable(supabase);
    } catch (profilesError) {
      console.error('Error creating user_profiles table, but continuing with other migrations:', profilesError);
    }

    try {
      // Create admin_users table if it doesn't exist
      await createAdminUsersTable(supabase);
    } catch (adminError) {
      console.error('Error creating admin_users table:', adminError);
    }

    console.log('Database migrations completed');
    return { success: true };
  } catch (error) {
    console.error('Error running migrations:', error);
    return { success: false, error };
  }
}

/**
 * Create the user_profiles table
 * @param supabase Supabase client
 */
async function createUserProfilesTable(supabase: SupabaseClient<Database>) {
  // Check if the table exists
  const { data: tableExists, error: checkError } = await supabase
    .from('user_profiles')
    .select('id')
    .limit(1);

  // If we get an error that's not "relation does not exist", throw it
  if (checkError && !checkError.message.includes('relation "user_profiles" does not exist')) {
    throw checkError;
  }

  // If the table exists, we're done
  if (tableExists && tableExists.length > 0) {
    console.log('user_profiles table already exists');
    return;
  }

  console.log('Creating user_profiles table...');

  // Create the table using SQL
  const { error } = await supabase.rpc('create_user_profiles_table', {});

  if (error) {
    // If the error is about the function not existing, we need to create it
    if (error.message.includes('function "create_user_profiles_table" does not exist')) {
      // Create the function using raw SQL
      const { error: sqlError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.user_profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id),
            email TEXT NOT NULL,
            role TEXT DEFAULT 'guest',
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            bio TEXT,
            phone TEXT,
            is_host BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
          CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);

          -- Set up RLS (Row Level Security)
          ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

          -- Create policies
          CREATE POLICY "Users can view their own profile"
            ON public.user_profiles
            FOR SELECT
            USING (auth.uid() = id);

          CREATE POLICY "Users can update their own profile"
            ON public.user_profiles
            FOR UPDATE
            USING (auth.uid() = id);

          CREATE POLICY "Users can insert their own profile"
            ON public.user_profiles
            FOR INSERT
            WITH CHECK (auth.uid() = id);

          -- Allow public read access to profiles (for displaying host info, etc.)
          CREATE POLICY "Public read access to profiles"
            ON public.user_profiles
            FOR SELECT
            USING (true);
        `
      });

      if (sqlError) {
        // If the exec_sql function doesn't exist, we need to create it manually
        if (sqlError.message.includes('function "exec_sql" does not exist')) {
          console.error('The exec_sql function does not exist. Please create it in the Supabase dashboard.');
          console.error('Attempting to create table directly...');

          // Try creating the table directly through the REST API
          // This is a fallback and may not work depending on permissions
          await createUserProfilesTableFallback(supabase);
        } else {
          throw sqlError;
        }
      }
    } else {
      throw error;
    }
  }

  console.log('user_profiles table created successfully');
}

/**
 * Fallback method to create user_profiles table
 * This uses the REST API instead of SQL
 * @param supabase Supabase client
 */
async function createUserProfilesTableFallback(supabase: SupabaseClient<Database>) {
  // We can't create tables through the REST API directly
  // Instead, we'll try to insert a record and see if it creates the table
  console.log('Attempting to create user_profiles table through REST API...');

  // Try to create the table directly using SQL
  try {
    // Get the current user's ID if available
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id || '00000000-0000-0000-0000-000000000000';

    // Try to insert a dummy record
    const { error } = await supabase
      .from('user_profiles')
      .insert({
        id: userId,
        email: '<EMAIL>',
        role: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_host: false
      });
  } catch (err) {
    console.error('Error in fallback table creation:', err);
  }

  // Check if the table exists after our attempt
  const { error: checkError } = await supabase
    .from('user_profiles')
    .select('id')
    .limit(1);

  if (checkError) {
    console.error('Failed to create user_profiles table through REST API:', checkError);
    throw new Error('Could not create user_profiles table. Please create it manually in the Supabase dashboard.');
  }

  console.log('user_profiles table created successfully through REST API');
}

/**
 * Create the admin_users table
 * @param supabase Supabase client
 */
async function createAdminUsersTable(supabase: SupabaseClient<Database>) {
  // Check if the table exists
  const { data: tableExists, error: checkError } = await supabase
    .from('admin_users')
    .select('id')
    .limit(1);

  // If we get an error that's not "relation does not exist", throw it
  if (checkError && !checkError.message.includes('relation "admin_users" does not exist')) {
    console.error('Error checking if admin_users table exists:', checkError);
    return; // Continue with other migrations
  }

  // If the table exists, we're done
  if (tableExists && tableExists.length > 0) {
    console.log('admin_users table already exists');
    return;
  }

  console.log('Creating admin_users table...');

  // Create the table using SQL
  const { error } = await supabase.rpc('create_admin_users_table', {});

  if (error) {
    // If the error is about the function not existing, we need to create it
    if (error.message.includes('function "create_admin_users_table" does not exist')) {
      // Create the function using raw SQL
      const { error: sqlError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.admin_users (
            id UUID PRIMARY KEY REFERENCES auth.users(id),
            email TEXT UNIQUE NOT NULL,
            is_super_admin BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_admin_users_email ON public.admin_users(email);

          -- Set up RLS (Row Level Security)
          ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

          -- Create policies
          CREATE POLICY "Admin users can view all admin users"
            ON public.admin_users
            FOR SELECT
            USING (
              EXISTS (
                SELECT 1 FROM public.admin_users
                WHERE id = auth.uid()
              )
            );

          CREATE POLICY "Super admins can update admin users"
            ON public.admin_users
            FOR UPDATE
            USING (
              EXISTS (
                SELECT 1 FROM public.admin_users
                WHERE id = auth.uid() AND is_super_admin = true
              )
            );

          CREATE POLICY "Super admins can insert admin users"
            ON public.admin_users
            FOR INSERT
            WITH CHECK (
              EXISTS (
                SELECT 1 FROM public.admin_users
                WHERE id = auth.uid() AND is_super_admin = true
              )
            );
        `
      });

      if (sqlError) {
        // If the exec_sql function doesn't exist, we need to create it manually
        if (sqlError.message.includes('function "exec_sql" does not exist')) {
          console.error('The exec_sql function does not exist. Please create it in the Supabase dashboard.');
          console.error('Attempting to create table directly...');

          // Try creating the table directly through the REST API
          await createAdminUsersTableFallback(supabase);
        } else {
          console.error('Error creating admin_users table:', sqlError);
          // Continue with other migrations
        }
      }
    } else {
      console.error('Error creating admin_users table:', error);
      // Continue with other migrations
    }
  }

  console.log('admin_users table created successfully');
}

/**
 * Fallback method to create admin_users table
 * This uses the REST API instead of SQL
 * @param supabase Supabase client
 */
async function createAdminUsersTableFallback(supabase: SupabaseClient<Database>) {
  // We can't create tables through the REST API directly
  // Instead, we'll try to insert a record and see if it creates the table
  console.log('Attempting to create admin_users table through REST API...');

  // Try to create the table directly using SQL
  try {
    // Get the current user's ID if available
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id || '00000000-0000-0000-0000-000000000000';

    // Try to insert a dummy record with working admin email
    const { error } = await supabase
      .from('admin_users')
      .insert({
        id: userId,
        email: '<EMAIL>', // Changed to working Gmail address
        is_super_admin: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
  } catch (err) {
    console.error('Error in fallback admin table creation:', err);
  }

  // Check if the table exists after our attempt
  const { error: checkError } = await supabase
    .from('admin_users')
    .select('id')
    .limit(1);

  if (checkError) {
    console.error('Failed to create admin_users table through REST API:', checkError);
    // Continue with other migrations
    return;
  }

  console.log('admin_users table created successfully through REST API');
}
