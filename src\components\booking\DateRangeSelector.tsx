import React, { useState, useEffect, useRef } from 'react';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import { useClickOutside } from '../../hooks/useClickOutside';
import { useEscapeKey } from '../../hooks/useEscapeKey';

interface DateRangeSelectorProps {
  startDate: string;
  endDate: string;
  onChangeStartDate: (date: string) => void;
  onChangeEndDate: (date: string) => void;
  minDate: string;
  allowSameDay?: boolean;
}

export default function DateRangeSelector({
  startDate,
  endDate,
  onChangeStartDate,
  onChangeEndDate,
  minDate,
  allowSameDay = true
}: DateRangeSelectorProps) {
  // Ensure minDate is not in the past using Sydney timezone
  const now = new Date();
  const sydneyOffset = 10 * 60; // Sydney is UTC+10 (in minutes)
  const utcDate = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
  const sydneyDate = new Date(utcDate.getTime() + (sydneyOffset * 60000));

  // Set time to beginning of day for comparison
  const today = new Date(sydneyDate);
  today.setHours(0, 0, 0, 0);

  const todayISO = sydneyDate.toISOString().split('T')[0];
  const effectiveMinDate = minDate < todayISO ? todayISO : minDate;

  // State for calendar popup
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(() => {
    // Initialize with the selected date if available, otherwise today
    return startDate ? new Date(startDate) : today;
  });

  // State for selection mode
  const [selectionMode, setSelectionMode] = useState<'start' | 'end'>(startDate ? 'end' : 'start');

  // Ref for click outside detection
  const calendarRef = useRef<HTMLDivElement>(null);
  useClickOutside(calendarRef, () => setShowCalendar(false), showCalendar);
  useEscapeKey(() => setShowCalendar(false), showCalendar);

  // Update current month when date changes
  useEffect(() => {
    if (startDate && !endDate) {
      setCurrentMonth(new Date(startDate));
      setSelectionMode('end');
    } else if (!startDate && endDate) {
      setCurrentMonth(new Date(endDate));
      setSelectionMode('start');
    } else if (startDate && endDate) {
      // Keep the current month as is
    } else {
      setSelectionMode('start');
    }
  }, [startDate, endDate]);

  // Format date for display
  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return '';

    // Parse the date string manually to avoid timezone issues
    const [year, month, day] = dateString.split('-').map(num => parseInt(num));

    // Create a date object with noon time to avoid timezone shifts
    const date = new Date(Date.UTC(year, month - 1, day, 12, 0, 0));

    return date.toLocaleDateString('en-AU', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };

  // Get days in month
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week for first day of month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  // Check if date is in the past
  const isDateDisabled = (year: number, month: number, day: number) => {
    const date = new Date(year, month, day);
    const minDateObj = new Date(effectiveMinDate);
    // Set time to beginning of day for comparison
    minDateObj.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);

    // If selecting end date, don't allow dates before start date
    if (selectionMode === 'end' && startDate) {
      const startDateObj = new Date(startDate);
      startDateObj.setHours(0, 0, 0, 0);

      if (!allowSameDay && date.getTime() === startDateObj.getTime()) {
        return true; // Disable same day selection if not allowed
      }

      return date < minDateObj || date < startDateObj;
    }

    return date < minDateObj;
  };

  // Check if date is today
  const isToday = (year: number, month: number, day: number) => {
    return (
      today.getFullYear() === year &&
      today.getMonth() === month &&
      today.getDate() === day
    );
  };

  // Check if date is selected
  const isDateSelected = (year: number, month: number, day: number) => {
    if (!startDate && !endDate) return false;

    // For start date check
    if (startDate) {
      const [startYear, startMonth, startDay] = startDate.split('-').map(num => parseInt(num));
      if (year === startYear && month === startMonth - 1 && day === startDay) {
        return true;
      }
    }

    // For end date check
    if (endDate) {
      const [endYear, endMonth, endDay] = endDate.split('-').map(num => parseInt(num));
      if (year === endYear && month === endMonth - 1 && day === endDay) {
        return true;
      }
    }

    return false;
  };

  // Check if date is in the selected range
  const isInRange = (year: number, month: number, day: number) => {
    if (!startDate || !endDate) return false;

    // Parse the date strings manually to avoid timezone issues
    const [startYear, startMonth, startDay] = startDate.split('-').map(num => parseInt(num));
    const [endYear, endMonth, endDay] = endDate.split('-').map(num => parseInt(num));

    // Create date objects with noon time to avoid timezone shifts
    const date = new Date(Date.UTC(year, month, day, 12, 0, 0));
    const startDateObj = new Date(Date.UTC(startYear, startMonth - 1, startDay, 12, 0, 0));
    const endDateObj = new Date(Date.UTC(endYear, endMonth - 1, endDay, 12, 0, 0));

    return date > startDateObj && date < endDateObj;
  };

  // Handle date selection
  const handleDateSelect = (year: number, month: number, day: number) => {
    try {
      // Create a date at noon to avoid timezone issues
      const newDate = new Date(Date.UTC(year, month, day, 12, 0, 0));

      // Format as YYYY-MM-DD, ensuring we get the exact date selected
      // Use UTC methods to avoid timezone shifts
      const dateString = `${newDate.getUTCFullYear()}-${String(newDate.getUTCMonth() + 1).padStart(2, '0')}-${String(newDate.getUTCDate()).padStart(2, '0')}`;

      console.log(`User selected: year=${year}, month=${month}, day=${day}`);
      console.log(`Formatted date string: ${dateString}`);
      console.log(`Current selection mode: ${selectionMode}`);

      if (selectionMode === 'start') {
        console.log('Setting start date:', dateString);
        onChangeStartDate(dateString);
        // If end date exists and is before the new start date, clear it
        if (endDate && new Date(endDate) < newDate) {
          console.log('Clearing end date because it is before the new start date');
          onChangeEndDate('');
        }
        setSelectionMode('end');
      } else {
        console.log('Setting end date:', dateString);
        onChangeEndDate(dateString);
        setSelectionMode('start');
        setShowCalendar(false);
      }
    } catch (error) {
      console.error('Error in handleDateSelect:', error);
    }
  };

  // Generate calendar grid
  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    // Day names
    const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

    // Calculate days from previous month to fill first row
    const prevMonthDays = [];
    if (firstDayOfMonth > 0) {
      const prevMonth = month === 0 ? 11 : month - 1;
      const prevMonthYear = month === 0 ? year - 1 : year;
      const daysInPrevMonth = getDaysInMonth(prevMonthYear, prevMonth);

      for (let i = firstDayOfMonth - 1; i >= 0; i--) {
        prevMonthDays.unshift({
          day: daysInPrevMonth - i,
          month: prevMonth,
          year: prevMonthYear,
          isCurrentMonth: false,
          isDisabled: true
        });
      }
    }

    // Current month days
    const currentMonthDays = [];
    for (let day = 1; day <= daysInMonth; day++) {
      const isPastDate = isDateDisabled(year, month, day);

      // Check if this day is the start date
      let isStart = false;
      if (startDate) {
        const [startYear, startMonth, startDay] = startDate.split('-').map(num => parseInt(num));
        isStart = startYear === year && startMonth - 1 === month && startDay === day;
      }

      // Check if this day is the end date
      let isEnd = false;
      if (endDate) {
        const [endYear, endMonth, endDay] = endDate.split('-').map(num => parseInt(num));
        isEnd = endYear === year && endMonth - 1 === month && endDay === day;
      }

      currentMonthDays.push({
        day,
        month,
        year,
        isCurrentMonth: true,
        isDisabled: isPastDate,
        isPast: isPastDate,
        isSelected: isDateSelected(year, month, day),
        isInRange: isInRange(year, month, day),
        isToday: isToday(year, month, day),
        isStart,
        isEnd
      });
    }

    // Next month days to fill last row
    const nextMonthDays = [];
    const totalDays = prevMonthDays.length + currentMonthDays.length;
    const nextMonthDaysToAdd = 42 - totalDays; // 6 rows x 7 days = 42

    if (nextMonthDaysToAdd > 0) {
      const nextMonth = month === 11 ? 0 : month + 1;
      const nextMonthYear = month === 11 ? year + 1 : year;

      for (let day = 1; day <= nextMonthDaysToAdd; day++) {
        nextMonthDays.push({
          day,
          month: nextMonth,
          year: nextMonthYear,
          isCurrentMonth: false,
          isDisabled: true
        });
      }
    }

    // Combine all days
    const allDays = [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];

    return (
      <div className="bg-white rounded-lg shadow-lg p-4 w-full max-w-xs">
        {/* Selection mode indicator */}
        <div className="mb-3 text-center">
          <span className="text-sm font-medium text-purple-700">
            {selectionMode === 'start' ? 'Select start date' : 'Select end date'}
          </span>
        </div>

        {/* Calendar header */}
        <div className="flex justify-between items-center mb-4">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              prevMonth();
            }}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <ChevronLeft className="h-5 w-5 text-gray-600" />
          </button>

          <h3 className="font-medium text-gray-900">
            {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </h3>

          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              nextMonth();
            }}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <ChevronRight className="h-5 w-5 text-gray-600" />
          </button>
        </div>

        {/* Day names */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map(day => (
            <div key={day} className="text-center text-xs font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">
          {allDays.map((day, index) => (
            <button
              key={index}
              type="button"
              disabled={day.isDisabled}
              onClick={(e) => {
                e.preventDefault();
                handleDateSelect(day.year, day.month, day.day);
              }}
              className={`
                h-9 w-9 flex items-center justify-center rounded-full text-sm relative
                ${!day.isCurrentMonth ? 'text-gray-300' : ''}
                ${day.isPast ? 'text-gray-300 line-through bg-gray-50 cursor-not-allowed' : 'hover:bg-purple-50'}
                ${day.isStart ? 'bg-purple-600 text-white hover:bg-purple-700' : ''}
                ${day.isEnd ? 'bg-purple-600 text-white hover:bg-purple-700' : ''}
                ${day.isInRange ? 'bg-purple-100' : ''}
                ${day.isToday && !day.isSelected ? 'border border-purple-600 font-medium' : ''}
              `}
            >
              {day.day}
              {day.isStart && (
                <span className="absolute -top-1 -right-1 bg-green-500 rounded-full w-3 h-3"></span>
              )}
              {day.isEnd && (
                <span className="absolute -top-1 -right-1 bg-red-500 rounded-full w-3 h-3"></span>
              )}
            </button>
          ))}
        </div>

        {/* Reset button */}
        <div className="mt-4 flex justify-between">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              onChangeStartDate('');
              onChangeEndDate('');
              setSelectionMode('start');
            }}
            className="text-sm text-red-600 hover:text-red-800 font-medium"
          >
            Reset
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              if (!isDateDisabled(today.getFullYear(), today.getMonth(), today.getDate())) {
                handleDateSelect(today.getFullYear(), today.getMonth(), today.getDate());
              }
            }}
            className="text-sm text-purple-600 hover:text-purple-800 font-medium"
          >
            Today
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Date Range
      </label>
      <div className="relative" ref={calendarRef}>
        <div
          className="relative cursor-pointer bg-white rounded-lg border border-gray-300 hover:border-purple-400 transition-colors"
          onClick={(e) => {
            e.preventDefault();
            setShowCalendar(!showCalendar);
          }}
        >
          <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-500 h-5 w-5" />
          <div className="pl-10 w-full px-4 py-3 rounded-lg text-gray-700">
            {startDate && endDate ? (
              <>
                {formatDisplayDate(startDate)}
                {startDate !== endDate && ` - ${formatDisplayDate(endDate)}`}
              </>
            ) : startDate ? (
              `${formatDisplayDate(startDate)} - Select end date`
            ) : (
              'Select date range'
            )}
          </div>
        </div>

        {/* Hidden inputs for form submission */}
        <input
          type="hidden"
          value={startDate}
          name="startDate"
          required
        />
        <input
          type="hidden"
          value={endDate}
          name="endDate"
          required
        />

        {/* Calendar popup */}
        {showCalendar && (
          <div className="absolute z-20 mt-2 left-0 right-0 animate-fadeIn">
            {renderCalendar()}
          </div>
        )}
      </div>
    </div>
  );
}
