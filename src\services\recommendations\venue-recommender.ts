import { supabase } from '../api';

// Types
export interface VenueRecommendation {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  capacity: number;
  images: string[];
  score: number;
  matchReason: string;
}

export interface RecommendationParams {
  userId?: string;
  eventType?: string;
  guestCount?: number;
  location?: string;
  budget?: number;
  date?: string;
  amenities?: string[];
}

/**
 * Get personalized venue recommendations based on user preferences and behavior
 */
export async function getVenueRecommendations(
  params: RecommendationParams
): Promise<VenueRecommendation[]> {
  try {
    // Build query
    let query = supabase
      .from('venues')
      .select(`
        id,
        title,
        description,
        location,
        price,
        capacity,
        amenities,
        images,
        host_id,
        created_at
      `);
    
    // Apply filters
    if (params.location) {
      query = query.ilike('location', `%${params.location}%`);
    }
    
    if (params.guestCount) {
      query = query.gte('capacity', params.guestCount);
    }
    
    if (params.budget) {
      query = query.lte('price', params.budget);
    }
    
    if (params.amenities && params.amenities.length > 0) {
      // Filter venues that have at least one of the requested amenities
      query = query.contains('amenities', params.amenities);
    }
    
    // Execute query
    const { data: venues, error } = await query.limit(20);
    
    if (error) {
      console.error('Error fetching venue recommendations:', error);
      return [];
    }
    
    if (!venues || venues.length === 0) {
      return [];
    }
    
    // Get user's past bookings and favorites if userId is provided
    let userPreferences = {
      favoriteLocations: [] as string[],
      favoriteAmenities: [] as string[],
      typicalGuestCount: 0,
      typicalBudget: 0,
      pastBookings: [] as string[]
    };
    
    if (params.userId) {
      // Get user's past bookings
      const { data: bookings } = await supabase
        .from('bookings')
        .select('venue_id, guests_count, total_price')
        .eq('guest_id', params.userId)
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (bookings && bookings.length > 0) {
        userPreferences.pastBookings = bookings.map(b => b.venue_id);
        userPreferences.typicalGuestCount = bookings.reduce((sum, b) => sum + b.guests_count, 0) / bookings.length;
        userPreferences.typicalBudget = bookings.reduce((sum, b) => sum + b.total_price, 0) / bookings.length;
        
        // Get details of past venues
        const { data: pastVenues } = await supabase
          .from('venues')
          .select('location, amenities')
          .in('id', userPreferences.pastBookings);
        
        if (pastVenues && pastVenues.length > 0) {
          // Extract favorite locations
          userPreferences.favoriteLocations = pastVenues.map(v => v.location);
          
          // Extract favorite amenities
          const amenitiesCounts: Record<string, number> = {};
          pastVenues.forEach(venue => {
            if (venue.amenities) {
              venue.amenities.forEach((amenity: string) => {
                amenitiesCounts[amenity] = (amenitiesCounts[amenity] || 0) + 1;
              });
            }
          });
          
          // Get top amenities
          userPreferences.favoriteAmenities = Object.entries(amenitiesCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([amenity]) => amenity);
        }
      }
      
      // Get user's favorites
      const { data: favorites } = await supabase
        .from('favorites')
        .select('venue_id')
        .eq('user_id', params.userId);
      
      if (favorites && favorites.length > 0) {
        const favoriteIds = favorites.map(f => f.venue_id);
        
        // Get details of favorite venues
        const { data: favoriteVenues } = await supabase
          .from('venues')
          .select('location, amenities')
          .in('id', favoriteIds);
        
        if (favoriteVenues && favoriteVenues.length > 0) {
          // Add to favorite locations
          userPreferences.favoriteLocations = [
            ...userPreferences.favoriteLocations,
            ...favoriteVenues.map(v => v.location)
          ];
          
          // Add to favorite amenities
          const amenitiesCounts: Record<string, number> = {};
          favoriteVenues.forEach(venue => {
            if (venue.amenities) {
              venue.amenities.forEach((amenity: string) => {
                amenitiesCounts[amenity] = (amenitiesCounts[amenity] || 0) + 1;
              });
            }
          });
          
          // Update top amenities
          const additionalAmenities = Object.entries(amenitiesCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([amenity]) => amenity);
          
          userPreferences.favoriteAmenities = [
            ...new Set([...userPreferences.favoriteAmenities, ...additionalAmenities])
          ];
        }
      }
    }
    
    // Score and rank venues
    const scoredVenues = venues.map(venue => {
      let score = 0;
      let matchReasons: string[] = [];
      
      // Base score for matching filters
      if (params.location && venue.location.toLowerCase().includes(params.location.toLowerCase())) {
        score += 20;
        matchReasons.push('Location match');
      }
      
      if (params.guestCount && venue.capacity >= params.guestCount) {
        // Higher score for venues that aren't too much larger than needed
        const capacityRatio = venue.capacity / params.guestCount;
        if (capacityRatio <= 1.5) {
          score += 20;
          matchReasons.push('Perfect size');
        } else if (capacityRatio <= 2) {
          score += 15;
          matchReasons.push('Good size');
        } else {
          score += 10;
          matchReasons.push('Spacious');
        }
      }
      
      if (params.budget && venue.price <= params.budget) {
        // Higher score for venues closer to budget (but not too cheap)
        const priceRatio = venue.price / params.budget;
        if (priceRatio >= 0.8) {
          score += 20;
          matchReasons.push('Great value');
        } else if (priceRatio >= 0.6) {
          score += 15;
          matchReasons.push('Budget-friendly');
        } else {
          score += 10;
          matchReasons.push('Affordable');
        }
      }
      
      // Score based on amenities
      if (params.amenities && params.amenities.length > 0 && venue.amenities) {
        const matchingAmenities = params.amenities.filter(a => 
          venue.amenities.includes(a)
        );
        
        if (matchingAmenities.length > 0) {
          const amenityScore = (matchingAmenities.length / params.amenities.length) * 20;
          score += amenityScore;
          
          if (matchingAmenities.length === params.amenities.length) {
            matchReasons.push('All requested amenities');
          } else if (matchingAmenities.length > 0) {
            matchReasons.push('Some requested amenities');
          }
        }
      }
      
      // Personalization based on user preferences
      if (params.userId) {
        // Location preference
        if (userPreferences.favoriteLocations.some(loc => 
          venue.location.toLowerCase().includes(loc.toLowerCase())
        )) {
          score += 15;
          matchReasons.push('Area you like');
        }
        
        // Amenities preference
        if (venue.amenities) {
          const matchingFavoriteAmenities = userPreferences.favoriteAmenities.filter(a => 
            venue.amenities.includes(a)
          );
          
          if (matchingFavoriteAmenities.length > 0) {
            const amenityScore = (matchingFavoriteAmenities.length / userPreferences.favoriteAmenities.length) * 15;
            score += amenityScore;
            matchReasons.push('Features you like');
          }
        }
        
        // Guest count preference
        if (userPreferences.typicalGuestCount > 0) {
          const guestCountRatio = venue.capacity / userPreferences.typicalGuestCount;
          if (guestCountRatio >= 0.8 && guestCountRatio <= 1.5) {
            score += 10;
            matchReasons.push('Similar to your past bookings');
          }
        }
        
        // Budget preference
        if (userPreferences.typicalBudget > 0) {
          const budgetRatio = venue.price / userPreferences.typicalBudget;
          if (budgetRatio >= 0.8 && budgetRatio <= 1.2) {
            score += 10;
            matchReasons.push('Within your usual budget');
          }
        }
      }
      
      // Event type specific scoring
      if (params.eventType) {
        if (params.eventType.toLowerCase() === 'wedding' && 
            (venue.title.toLowerCase().includes('wedding') || 
             venue.description.toLowerCase().includes('wedding'))) {
          score += 15;
          matchReasons.push('Wedding-friendly');
        } else if (params.eventType.toLowerCase() === 'corporate' && 
                  (venue.title.toLowerCase().includes('corporate') || 
                   venue.description.toLowerCase().includes('corporate') ||
                   venue.description.toLowerCase().includes('business'))) {
          score += 15;
          matchReasons.push('Corporate-friendly');
        } else if (params.eventType.toLowerCase() === 'birthday' && 
                  (venue.title.toLowerCase().includes('party') || 
                   venue.description.toLowerCase().includes('birthday') ||
                   venue.description.toLowerCase().includes('celebration'))) {
          score += 15;
          matchReasons.push('Perfect for birthdays');
        }
      }
      
      return {
        ...venue,
        score,
        matchReason: matchReasons.length > 0 ? matchReasons[0] : 'Recommended venue'
      };
    });
    
    // Sort by score (descending)
    return scoredVenues
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);
  } catch (error) {
    console.error('Error in venue recommendations:', error);
    return [];
  }
}
