/**
 * Supabase Migrations Utility
 * 
 * This file contains functions to create and run database migrations for Supabase.
 * It handles the creation of required SQL functions and tables.
 */

import { supabase } from '../lib/supabase-client';

/**
 * Creates the exec_sql function in Supabase if it doesn't exist
 * This function allows executing arbitrary SQL from the client
 */
export async function createExecSqlFunction(): Promise<boolean> {
  try {
    // First check if the function already exists
    const { data: functionExists, error: checkError } = await supabase.rpc('exec_sql_exists', {});
    
    // If the check function doesn't exist, we need to create both
    if (checkError) {
      console.log('Creating exec_sql_exists function first...');
      
      // Create a function to check if exec_sql exists
      const createCheckFunctionQuery = `
        CREATE OR REPLACE FUNCTION exec_sql_exists()
        RETURNS boolean
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          func_exists boolean;
        BEGIN
          SELECT EXISTS (
            SELECT 1
            FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'public'
            AND p.proname = 'exec_sql'
          ) INTO func_exists;
          
          RETURN func_exists;
        END;
        $$;
      `;
      
      // Execute the query directly using the REST API
      const checkFunctionResponse = await fetch(
        `${supabase.supabaseUrl}/rest/v1/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabase.supabaseKey,
            'Authorization': `Bearer ${supabase.supabaseKey}`,
            'Prefer': 'return=minimal'
          },
          body: JSON.stringify({
            query: createCheckFunctionQuery
          })
        }
      );
      
      if (!checkFunctionResponse.ok) {
        console.error('Failed to create exec_sql_exists function:', await checkFunctionResponse.text());
        return false;
      }
    }
    
    // Now check if exec_sql exists
    const { data: execSqlExists, error: execSqlCheckError } = await supabase.rpc('exec_sql_exists', {});
    
    // If exec_sql doesn't exist or we couldn't check, create it
    if (execSqlCheckError || !execSqlExists) {
      console.log('Creating exec_sql function...');
      
      // Create the exec_sql function
      const createExecSqlQuery = `
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;
      `;
      
      // Execute the query directly using the REST API
      const execSqlResponse = await fetch(
        `${supabase.supabaseUrl}/rest/v1/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabase.supabaseKey,
            'Authorization': `Bearer ${supabase.supabaseKey}`,
            'Prefer': 'return=minimal'
          },
          body: JSON.stringify({
            query: createExecSqlQuery
          })
        }
      );
      
      if (!execSqlResponse.ok) {
        console.error('Failed to create exec_sql function:', await execSqlResponse.text());
        return false;
      }
      
      console.log('exec_sql function created successfully');
    } else {
      console.log('exec_sql function already exists');
    }
    
    return true;
  } catch (error) {
    console.error('Error creating exec_sql function:', error);
    return false;
  }
}

/**
 * Creates the user_profiles table if it doesn't exist
 */
export async function createUserProfilesTable(): Promise<boolean> {
  try {
    // Check if the table exists
    const { data, error } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);
    
    // If the table exists, we're done
    if (!error) {
      console.log('user_profiles table already exists');
      return true;
    }
    
    // Create the table
    console.log('Creating user_profiles table...');
    
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS public.user_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        clerk_id TEXT UNIQUE,
        email TEXT UNIQUE,
        first_name TEXT,
        last_name TEXT,
        role TEXT DEFAULT 'guest',
        is_host BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
    `;
    
    // Try to execute the query using exec_sql if it exists
    try {
      await supabase.rpc('exec_sql', { sql: createTableQuery });
      console.log('user_profiles table created successfully using exec_sql');
      return true;
    } catch (execError) {
      console.warn('Failed to create user_profiles table using exec_sql:', execError);
      
      // Fall back to direct REST API call
      const response = await fetch(
        `${supabase.supabaseUrl}/rest/v1/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabase.supabaseKey,
            'Authorization': `Bearer ${supabase.supabaseKey}`,
            'Prefer': 'return=minimal'
          },
          body: JSON.stringify({
            query: createTableQuery
          })
        }
      );
      
      if (!response.ok) {
        console.error('Failed to create user_profiles table:', await response.text());
        return false;
      }
      
      console.log('user_profiles table created successfully using REST API');
      return true;
    }
  } catch (error) {
    console.error('Error creating user_profiles table:', error);
    return false;
  }
}

/**
 * Creates the admin_users table if it doesn't exist
 */
export async function createAdminUsersTable(): Promise<boolean> {
  try {
    // Check if the table exists
    const { data, error } = await supabase
      .from('admin_users')
      .select('id')
      .limit(1);
    
    // If the table exists, we're done
    if (!error) {
      console.log('admin_users table already exists');
      return true;
    }
    
    // Create the table
    console.log('Creating admin_users table...');
    
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS public.admin_users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        clerk_id TEXT UNIQUE,
        email TEXT UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
    `;
    
    // Try to execute the query using exec_sql if it exists
    try {
      await supabase.rpc('exec_sql', { sql: createTableQuery });
      console.log('admin_users table created successfully using exec_sql');
      return true;
    } catch (execError) {
      console.warn('Failed to create admin_users table using exec_sql:', execError);
      
      // Fall back to direct REST API call
      const response = await fetch(
        `${supabase.supabaseUrl}/rest/v1/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabase.supabaseKey,
            'Authorization': `Bearer ${supabase.supabaseKey}`,
            'Prefer': 'return=minimal'
          },
          body: JSON.stringify({
            query: createTableQuery
          })
        }
      );
      
      if (!response.ok) {
        console.error('Failed to create admin_users table:', await response.text());
        return false;
      }
      
      console.log('admin_users table created successfully using REST API');
      return true;
    }
  } catch (error) {
    console.error('Error creating admin_users table:', error);
    return false;
  }
}

/**
 * Run all migrations
 */
export async function runMigrations(): Promise<boolean> {
  try {
    console.log('Running Supabase migrations...');
    
    // Create the exec_sql function first
    const execSqlCreated = await createExecSqlFunction();
    
    // Create tables
    const userProfilesCreated = await createUserProfilesTable();
    const adminUsersCreated = await createAdminUsersTable();
    
    console.log('Migration results:', {
      execSqlCreated,
      userProfilesCreated,
      adminUsersCreated
    });
    
    return execSqlCreated && userProfilesCreated && adminUsersCreated;
  } catch (error) {
    console.error('Error running migrations:', error);
    return false;
  }
}
