# Stripe Payment Integration - HouseGoing

## Overview

This document outlines the complete Stripe payment integration for the HouseGoing platform. The integration supports secure payment processing for venue bookings with proper error handling, webhook support, and test card functionality.

## Architecture

### Frontend Components
- **StripePaymentForm**: Main payment form component with card input
- **BookingPayment**: Booking-specific payment wrapper
- **PriceSummary**: Price calculation and display component

### Backend Services
- **Netlify Functions**: Serverless functions for secure Stripe API calls
- **Webhook Handler**: Processes Stripe webhook events
- **Payment Service**: Client-side service for payment operations

### Configuration
- **Stripe Config**: Centralized configuration for API keys and settings
- **Environment Variables**: Secure storage of sensitive keys

## Setup Instructions

### 1. Environment Variables

Add these variables to your Netlify environment settings:

```bash
# Stripe Configuration (Test Keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### 2. Netlify Functions

The following functions are deployed automatically:

- `/.netlify/functions/create-payment-intent` - Creates payment intents
- `/.netlify/functions/create-customer` - Manages Stripe customers
- `/.netlify/functions/stripe-webhook` - Handles webhook events

### 3. Webhook Configuration

Configure webhooks in your Stripe dashboard:

1. Go to Stripe Dashboard > Webhooks
2. Add endpoint: `https://your-domain.netlify.app/.netlify/functions/stripe-webhook`
3. Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
4. Copy webhook secret to environment variables

## Usage

### Basic Payment Flow

```typescript
import StripePaymentForm from '../components/payment/StripePaymentForm';

<StripePaymentForm
  amount={100} // Amount in AUD
  description="Venue booking payment"
  customerEmail="<EMAIL>"
  metadata={{
    booking_id: "booking_123",
    venue_name: "Test Venue"
  }}
  onSuccess={(paymentId) => console.log('Payment successful:', paymentId)}
  onError={(error) => console.error('Payment failed:', error)}
/>
```

### Price Calculation

```typescript
import { stripePaymentService } from '../services/stripe-payment';

const calculation = stripePaymentService.calculatePrice(50, 4, {
  isWeekend: true,
  isHoliday: false,
  isLateNight: false
});
```

## Test Cards

Use these test card numbers for development:

- **Success**: 4242 4242 4242 4242
- **Authentication Required**: 4000 0025 0000 3155
- **Declined**: 4000 0000 0000 0002

Use any future expiry date, any 3-digit CVC, and any postal code.

## API Endpoints

### Create Payment Intent
```
POST /.netlify/functions/create-payment-intent
Content-Type: application/json

{
  "amount": 10000, // Amount in cents
  "currency": "aud",
  "description": "Venue booking",
  "customer_email": "<EMAIL>",
  "metadata": {
    "booking_id": "booking_123"
  }
}
```

### Create Customer
```
POST /.netlify/functions/create-customer
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "John Doe",
  "phone": "+61400000000"
}
```

## Security Features

- **Server-side API calls**: All sensitive operations use Netlify functions
- **Webhook verification**: Stripe signature verification for webhooks
- **Environment variables**: Secure storage of API keys
- **HTTPS only**: All communications encrypted
- **PCI compliance**: Stripe handles card data securely

## Error Handling

The integration includes comprehensive error handling:

- **Card errors**: Declined, expired, incorrect details
- **Network errors**: Timeout, connection issues
- **Validation errors**: Invalid input data
- **Authentication errors**: 3D Secure requirements

## Testing

### Test Page
Visit `/test-stripe-payment` to test the integration with various scenarios.

### Manual Testing
1. Use test card numbers
2. Test different amounts
3. Verify webhook events
4. Check error scenarios

## Monitoring

### Stripe Dashboard
- Monitor payments in real-time
- View webhook delivery status
- Check for failed payments

### Application Logs
- Netlify function logs
- Browser console for client-side errors
- Webhook event processing logs

## Production Deployment

### Before Going Live
1. Replace test API keys with live keys
2. Update webhook endpoints
3. Test with real bank cards (small amounts)
4. Verify webhook delivery
5. Set up monitoring alerts

### Live Configuration
```bash
# Production Stripe Keys
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret
```

## Troubleshooting

### Common Issues
1. **Payment intent creation fails**: Check API keys and network
2. **Webhooks not received**: Verify endpoint URL and secret
3. **Card declined**: Use valid test cards or check real card
4. **CORS errors**: Ensure proper headers in Netlify functions

### Debug Steps
1. Check browser console for errors
2. Verify Netlify function logs
3. Check Stripe dashboard for events
4. Test with curl commands

## Support

For issues with the Stripe integration:
1. Check this documentation
2. Review Stripe documentation
3. Test with the `/test-stripe-payment` page
4. Check Netlify function logs
