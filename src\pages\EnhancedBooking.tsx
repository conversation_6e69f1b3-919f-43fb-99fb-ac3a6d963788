/**
 * Enhanced Booking Page
 *
 * This page provides the complete enhanced booking flow for a venue.
 * It fetches venue data and renders the EnhancedBookingFlow component.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import EnhancedBookingFlow from '../components/booking/EnhancedBookingFlow';
import { getVenueById } from '../api/venues';
import { mockVenues } from '../data/mockVenues';
import SEO from '../components/seo/SEO';
import { ArrowLeft } from 'lucide-react';

export default function EnhancedBooking() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useUser();

  const [venue, setVenue] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch venue data
  useEffect(() => {
    const fetchVenue = async () => {
      if (!id) {
        setError('No venue ID provided');
        setLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching venue for enhanced booking:', id);
        setLoading(true);

        // Try to get venue from API first
        let venueData = await getVenueById(id);

        // If not found in API, try to find in mock data
        if (!venueData) {
          console.log('🔍 Venue not found in API, searching mock data...');
          const mockVenue = mockVenues.find(v => v.id === id);
          if (mockVenue) {
            // Convert mock venue to the expected format
            venueData = {
              id: mockVenue.id,
              title: mockVenue.title || mockVenue.name,
              description: mockVenue.description,
              location: `${mockVenue.location.suburb}, ${mockVenue.location.state}`,
              price: mockVenue.pricing.hourlyRate,
              capacity: mockVenue.capacity.recommended,
              rating: mockVenue.host.rating,
              reviews: mockVenue.host.reviewCount,
              images: mockVenue.images,
              amenities: mockVenue.amenities,
              eventTypes: mockVenue.eventTypes || [mockVenue.venueType],
              host: {
                id: mockVenue.host.id || mockVenue.id + '_host',
                name: mockVenue.host.name,
                image: mockVenue.host.image || "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=200",
                rating: mockVenue.host.rating
              },
              noiseRestrictions: {
                curfewTime: "22:00",
                councilArea: `${mockVenue.location.suburb} Council`,
                allowsOvernight: true,
                notes: "Please be mindful of neighbors after 10 PM. All loud music must move indoors after curfew.",
                windowsClosedAfter: "21:00",
                zoning: "residential",
                residentialProximity: "nearby",
                soundproofing: true,
                outdoorMusic: {
                  allowed: true,
                  until: "21:00"
                }
              },
              partyScore: {
                score: Math.round(mockVenue.partyScore / 10) || 8,
                factors: [
                  "Residential area with noise restrictions",
                  "Good soundproofing",
                  "Outdoor space available",
                  "Council permits in place"
                ]
              }
            };
            console.log('✅ Found venue in mock data:', venueData.title);
          }
        } else {
          console.log('✅ Found venue in API:', venueData.title);
        }

        if (venueData) {
          setVenue(venueData);
        } else {
          setError('Venue not found');
        }
      } catch (err) {
        console.error('❌ Error fetching venue:', err);
        setError('Failed to load venue details');
      } finally {
        setLoading(false);
      }
    };

    fetchVenue();
  }, [id]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      navigate(`/login?redirect=${encodeURIComponent(window.location.pathname)}`);
    }
  }, [user, loading, navigate]);

  // Handle booking completion
  const handleBookingComplete = (bookingId: string) => {
    navigate(`/booking-confirmation/${bookingId}`);
  };

  // Handle booking cancellation
  const handleBookingCancel = () => {
    navigate(`/venue/${id}`);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading venue details...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Venue Not Found</h1>
          <p className="text-gray-600 mb-8">{error}</p>
          <div className="space-y-3">
            <button
              onClick={() => navigate(`/venue/${id}`)}
              className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Back to Venue Details
            </button>
            <button
              onClick={() => navigate('/find-venues')}
              className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Browse Other Venues
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show venue not found state
  if (!venue) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Venue Not Found</h1>
          <p className="text-gray-600 mb-8">The venue you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/find-venues')}
            className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Browse Other Venues
          </button>
        </div>
      </div>
    );
  }

  // Show authentication required state
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Login Required</h1>
          <p className="text-gray-600 mb-8">You need to be logged in to make a booking.</p>
          <button
            onClick={() => navigate(`/login?redirect=${encodeURIComponent(window.location.pathname)}`)}
            className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Login to Continue
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEO
        title={`Book ${venue.title} - Enhanced Booking Flow | HouseGoing`}
        description={`Complete your booking for ${venue.title} with our streamlined booking process. Secure payment and instant confirmation.`}
        image={venue.images[0]}
        url={`https://housegoing.com.au/enhanced-booking/${id}`}
        type="product"
      />

      <div className="min-h-screen bg-gray-50">
        {/* Venue Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-4xl mx-auto px-4 py-6">
            <button
              onClick={() => navigate(`/venue/${id}`)}
              className="flex items-center text-purple-600 hover:text-purple-700 mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Venue Details
            </button>

            <div className="flex items-center space-x-4">
              <img
                src={venue.images[0]}
                alt={venue.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{venue.title}</h1>
                <p className="text-gray-600">{venue.location} • Up to {venue.capacity} guests</p>
                <p className="text-lg font-semibold text-purple-600">${venue.price}/hour</p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Booking Flow */}
        <EnhancedBookingFlow
          venue={venue}
          onComplete={handleBookingComplete}
          onCancel={handleBookingCancel}
        />
      </div>
    </>
  );
}
