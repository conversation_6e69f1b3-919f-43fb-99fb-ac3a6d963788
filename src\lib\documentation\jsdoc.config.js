{"source": {"include": ["src"], "includePattern": ".+\\.(js|jsx|ts|tsx)$", "excludePattern": "(node_modules|dist|build)"}, "plugins": ["plugins/markdown", "better-docs/component", "better-docs/typescript"], "opts": {"destination": "docs", "recurse": true, "readme": "README.md", "template": "node_modules/better-docs"}, "templates": {"better-docs": {"name": "HouseGoing Documentation", "logo": "src/assets/logo.png", "navigation": [{"label": "GitHub", "href": "https://github.com/housegoingmate/housegoing-"}, {"label": "Website", "href": "https://housegoing.com.au"}]}}, "tags": {"allowUnknownTags": ["component"]}}