import React, { useState } from 'react';
import { calculatePartyScore, getPartyScoreRecommendations } from '../../lib/nsw-party-planning/party-score';
import { ChevronDown, ChevronUp, Info, AlertTriangle, CheckCircle, PartyPopper, Clock, Volume2, Music } from 'lucide-react';

interface PartyScoreCardProps {
  zoneCode: string;
  zoneName: string;
  propertyType: string;
  curfewStart: string;
  outdoorCutoff: string | null;
  specialConditions?: string;
  isWeekend: boolean;
  confidenceLevel: 'High' | 'Medium' | 'Low';
}

const PartyScoreCard: React.FC<PartyScoreCardProps> = ({
  zoneCode,
  zoneName,
  propertyType,
  curfewStart,
  outdoorCutoff,
  specialConditions,
  isWeekend,
  confidenceLevel
}) => {
  const [showBreakdown, setShowBreakdown] = useState(false);

  // Calculate party score
  const partyScoreResult = calculatePartyScore({
    zoneCode,
    zoneName,
    propertyType,
    curfewStart,
    outdoorAllowed: outdoorCutoff !== null,
    specialLocalRule: specialConditions,
    isWeekend,
    dataConfidence: confidenceLevel
  });

  // Get recommendations
  const recommendations = getPartyScoreRecommendations(
    partyScoreResult.score,
    {
      zoneCode,
      zoneName,
      propertyType,
      curfewStart,
      outdoorAllowed: outdoorCutoff !== null,
      specialLocalRule: specialConditions,
      isWeekend,
      dataConfidence: confidenceLevel
    }
  );

  // Get color classes based on score
  const getColorClasses = () => {
    switch (partyScoreResult.color) {
      case 'green':
        return {
          bg: 'bg-green-100',
          border: 'border-green-200',
          text: 'text-green-800',
          gauge: 'from-green-300 to-green-500',
          glow: 'shadow-green-200'
        };
      case 'yellow':
        return {
          bg: 'bg-yellow-100',
          border: 'border-yellow-200',
          text: 'text-yellow-800',
          gauge: 'from-yellow-300 to-yellow-500',
          glow: 'shadow-yellow-200'
        };
      case 'red':
        return {
          bg: 'bg-red-100',
          border: 'border-red-200',
          text: 'text-red-800',
          gauge: 'from-red-300 to-red-500',
          glow: 'shadow-red-200'
        };
      default:
        return {
          bg: 'bg-gray-100',
          border: 'border-gray-200',
          text: 'text-gray-800',
          gauge: 'from-gray-300 to-gray-500',
          glow: 'shadow-gray-200'
        };
    }
  };

  const colorClasses = getColorClasses();

  // Get zone color
  const getZoneColor = () => {
    if (zoneCode.startsWith('R')) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (zoneCode.startsWith('B')) return 'bg-purple-100 text-purple-800 border-purple-200';
    if (zoneCode.startsWith('IN')) return 'bg-orange-100 text-orange-800 border-orange-200';
    if (zoneCode.startsWith('RU')) return 'bg-green-100 text-green-800 border-green-200';
    if (zoneCode.startsWith('SP')) return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    if (zoneCode.startsWith('RE')) return 'bg-teal-100 text-teal-800 border-teal-200';
    if (zoneCode.startsWith('E')) return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    if (zoneCode.startsWith('W')) return 'bg-cyan-100 text-cyan-800 border-cyan-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  return (
    <div className="mb-6 transition-all duration-300">
      {/* Main card with white background and subtle border */}
      <div className="p-6 pt-10 sm:p-8 sm:pt-10 rounded-2xl border border-gray-200 bg-white shadow-md relative overflow-visible mt-8">
        {/* Score circle - positioned inside the card at the top */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 translate-y-[-40%] w-16 sm:w-20 h-16 sm:h-20 flex items-center justify-center z-10">
          <div className="relative w-full h-full bg-white rounded-full shadow-lg flex items-center justify-center border-4 border-white">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              {/* Background circle */}
              <circle
                cx="50"
                cy="50"
                r="42"
                fill="white"
                stroke="#f0f0f0"
                strokeWidth="8"
                strokeLinecap="round"
              />
              {/* Foreground circle - score indicator */}
              <circle
                cx="50"
                cy="50"
                r="42"
                fill="none"
                stroke={`url(#${partyScoreResult.color}Gradient)`}
                strokeWidth="8"
                strokeLinecap="round"
                strokeDasharray="264"
                strokeDashoffset={`${264 - (264 * partyScoreResult.score) / 10}`}
                transform="rotate(-90 50 50)"
                className="transition-all duration-1000 ease-out"
                style={{ filter: 'drop-shadow(0 0 3px rgba(0,0,0,0.1))' }}
              />
              {/* Gradient definition */}
              <defs>
                <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#86efac" />
                  <stop offset="100%" stopColor="#22c55e" />
                </linearGradient>
                <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#fde047" />
                  <stop offset="100%" stopColor="#eab308" />
                </linearGradient>
                <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#fca5a5" />
                  <stop offset="100%" stopColor="#ef4444" />
                </linearGradient>
              </defs>
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <span className="text-2xl font-bold text-gray-800">{partyScoreResult.score}</span>
              <span className="text-[10px] text-gray-500">out of 10</span>
            </div>
          </div>
        </div>

        {/* Status badge - positioned below score circle */}
        <div className="flex justify-center mb-2 mt-2">
          <div className={`inline-flex items-center px-3 py-1 rounded-full shadow-sm ${
            partyScoreResult.color === 'green'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : partyScoreResult.color === 'yellow'
                ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            {partyScoreResult.color === 'green' ? (
              <CheckCircle className="h-4 w-4 mr-1.5" />
            ) : partyScoreResult.color === 'yellow' ? (
              <Info className="h-4 w-4 mr-1.5" />
            ) : (
              <AlertTriangle className="h-4 w-4 mr-1.5" />
            )}
            <span className="text-sm font-medium">{partyScoreResult.band}</span>
          </div>
        </div>

        {/* Party Score header with zone info */}
        <div className="flex items-center justify-center mb-3">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <PartyPopper className="h-4 w-4 text-purple-600 mr-1" />
              <h2 className="text-lg font-bold text-gray-800">Party Score</h2>
            </div>
            <div className="flex items-center justify-center">
              <span className={`text-sm font-medium ${getZoneColor()} px-2 py-0.5 rounded-full border mr-2`}>
                {zoneCode}
              </span>
              <span className="text-sm text-gray-600">{zoneName}</span>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-6"></div>

        {/* Recommendations */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <CheckCircle className="h-5 w-5 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">Key Recommendations</h3>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg border border-purple-100 mb-4">
            <p className="text-sm text-purple-800">
              Follow these recommendations to ensure your event complies with local noise regulations and respects the neighborhood.
            </p>
          </div>

          <ul className="space-y-3">
            {recommendations.slice(0, 3).map((recommendation, index) => (
              <li key={index} className="flex items-start bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <div className={`p-2 rounded-full mr-3 flex-shrink-0 ${
                  partyScoreResult.color === 'green'
                    ? 'bg-green-100 text-green-600 border border-green-200'
                    : partyScoreResult.color === 'yellow'
                      ? 'bg-yellow-100 text-yellow-600 border border-yellow-200'
                      : 'bg-red-100 text-red-600 border border-red-200'
                }`}>
                  {index === 0 ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : index === 1 ? (
                    <Volume2 className="h-5 w-5" />
                  ) : (
                    <Clock className="h-5 w-5" />
                  )}
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-800">{recommendation}</span>
                  {index === 0 && (
                    <p className="text-xs text-gray-500 mt-1">
                      This is the most important recommendation based on your location's zoning and regulations.
                    </p>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-6"></div>

        {/* Noise Restriction Timeline - Completely redesigned for clarity */}
        <div>
          <div className="flex items-center mb-4">
            <Clock className="h-5 w-5 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">Noise Restriction Timeline</h3>
          </div>

          {/* Timeline explanation */}
          <div className="mb-4 bg-blue-50 p-3 rounded-lg border border-blue-100">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
              <p className="text-sm text-blue-800">
                This timeline shows when noise restrictions apply at this location. Plan your event accordingly to avoid disturbing neighbors.
              </p>
            </div>
          </div>

          {/* Simplified timeline with clear time blocks */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            {/* Time blocks in a more readable format */}
            <div className="grid grid-cols-3 gap-0">
              {/* Morning Block (7am-7pm) */}
              <div className="border-r border-gray-200">
                <div className="bg-green-50 p-3 border-b border-green-100">
                  <div className="flex items-center justify-center mb-1">
                    <Volume2 className="h-4 w-4 text-green-600 mr-1.5" />
                    <h4 className="font-medium text-green-800">Daytime</h4>
                  </div>
                  <p className="text-xs text-center text-green-700">7:00 AM - 7:00 PM</p>
                </div>
                <div className="p-3">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 rounded-full bg-green-100 border border-green-200 flex items-center justify-center mb-2">
                      <Volume2 className="h-5 w-5 text-green-600" />
                    </div>
                    <p className="text-sm font-medium text-gray-800 text-center">Normal Activity</p>
                    <p className="text-xs text-gray-600 text-center mt-1">Regular conversation and music allowed</p>
                  </div>
                </div>
              </div>

              {/* Evening Block (7pm-10pm) */}
              <div className="border-r border-gray-200">
                <div className="bg-yellow-50 p-3 border-b border-yellow-100">
                  <div className="flex items-center justify-center mb-1">
                    <Volume2 className="h-4 w-4 text-yellow-600 mr-1.5" />
                    <h4 className="font-medium text-yellow-800">Evening</h4>
                  </div>
                  <p className="text-xs text-center text-yellow-700">7:00 PM - {curfewStart ? curfewStart.split(':')[0] + ':00 PM' : '10:00 PM'}</p>
                </div>
                <div className="p-3">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 rounded-full bg-yellow-100 border border-yellow-200 flex items-center justify-center mb-2">
                      <Volume2 className="h-5 w-5 text-yellow-600" />
                    </div>
                    <p className="text-sm font-medium text-gray-800 text-center">Reduced Volume</p>
                    <p className="text-xs text-gray-600 text-center mt-1">Lower music, keep windows closed</p>
                  </div>
                </div>
              </div>

              {/* Night Block (10pm-7am) */}
              <div>
                <div className="bg-red-50 p-3 border-b border-red-100">
                  <div className="flex items-center justify-center mb-1">
                    <Volume2 className="h-4 w-4 text-red-600 mr-1.5" />
                    <h4 className="font-medium text-red-800">Night</h4>
                  </div>
                  <p className="text-xs text-center text-red-700">{curfewStart ? curfewStart.split(':')[0] + ':00 PM' : '10:00 PM'} - 7:00 AM</p>
                </div>
                <div className="p-3">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 rounded-full bg-red-100 border border-red-200 flex items-center justify-center mb-2">
                      <Volume2 className="h-5 w-5 text-red-600" />
                    </div>
                    <p className="text-sm font-medium text-gray-800 text-center">Quiet Hours</p>
                    <p className="text-xs text-gray-600 text-center mt-1">Minimal noise, no loud music</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Current time indicator */}
            <div className="bg-gray-50 p-2 border-t border-gray-200">
              <div className="flex items-center justify-center">
                <Clock className="h-4 w-4 text-blue-600 mr-1.5" />
                <span className="text-xs text-gray-700">
                  Current time: <span className="font-medium">{new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span> -
                  <span className="ml-1 font-medium">
                    {(() => {
                      const hour = new Date().getHours();
                      if (hour >= 7 && hour < 19) return "Normal Activity Period";
                      if (hour >= 19 && hour < parseInt(curfewStart?.split(':')[0] || '22', 10)) return "Reduced Volume Period";
                      return "Quiet Hours Period";
                    })()}
                  </span>
                </span>
              </div>
            </div>
          </div>

          {/* Special notes for this location */}
          {outdoorCutoff && (
            <div className="mt-3 bg-orange-50 p-3 rounded-lg border border-orange-100">
              <div className="flex items-start">
                <Music className="h-5 w-5 text-orange-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-orange-800">Outdoor Music Restriction</p>
                  <p className="text-xs text-orange-700 mt-1">
                    Outdoor music must be turned off by {outdoorCutoff.split(':')[0]}:00 {parseInt(outdoorCutoff.split(':')[0]) >= 12 ? 'PM' : 'AM'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Local council regulations */}
          <div className="mt-3 bg-purple-50 p-3 rounded-lg border border-purple-100">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-purple-800">Local Council Regulations</p>
                <p className="text-xs text-purple-700 mt-1">
                  {zoneCode.startsWith('R') ? 'Residential zones have strict noise restrictions after hours.' :
                   zoneCode.startsWith('B') ? 'Business zones may have special provisions for entertainment venues.' :
                   zoneCode.startsWith('IN') ? 'Industrial zones typically have fewer noise restrictions.' :
                   'Check with local council for specific noise regulations in this zone.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-6"></div>

        {/* Score breakdown toggle */}
        <button
          onClick={() => setShowBreakdown(!showBreakdown)}
          className="flex items-center justify-center w-full text-sm font-medium text-gray-500 hover:text-gray-800 focus:outline-none"
        >
          {showBreakdown ? (
            <>
              <ChevronUp className="h-4 w-4 mr-1" />
              Hide score breakdown
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4 mr-1" />
              Show score breakdown
            </>
          )}
        </button>

        {/* Score breakdown */}
        {showBreakdown && (
          <div className="mt-4 pt-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Score Breakdown</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Zoning</span>
                <span className="font-medium">{partyScoreResult.breakdown.zoning.toFixed(1)} / 4.0</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Property Type</span>
                <span className="font-medium">{partyScoreResult.breakdown.propertyType.toFixed(1)} / 1.5</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Curfew Time</span>
                <span className="font-medium">{partyScoreResult.breakdown.curfewTime.toFixed(1)} / 2.0</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Outdoor Allowance</span>
                <span className="font-medium">{partyScoreResult.breakdown.outdoorAllowance.toFixed(1)} / 1.0</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Special Local Rules</span>
                <span className="font-medium">{partyScoreResult.breakdown.specialLocalRule.toFixed(1)} / 1.0</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Weekend Bonus</span>
                <span className="font-medium">{partyScoreResult.breakdown.dayFactor.toFixed(1)} / 0.5</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
                <span className="text-sm text-gray-600">Data Confidence</span>
                <span className="font-medium">{partyScoreResult.breakdown.dataConfidence.toFixed(2)} / 0.5</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-100 shadow-sm font-bold">
                <span className="text-sm">TOTAL SCORE</span>
                <span>{partyScoreResult.breakdown.total.toFixed(0)} / 10</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PartyScoreCard;
