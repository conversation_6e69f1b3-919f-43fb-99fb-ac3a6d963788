import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { MapPin, Users, Clock, Star, ArrowRight } from 'lucide-react';

// Category data
const categoryData: Record<string, {
  name: string;
  description: string;
  longDescription: string;
  features: string[];
  averagePrice: string;
  popularLocations: string[];
  seoTitle: string;
  seoDescription: string;
}> = {
  garden: {
    name: 'Garden Venues',
    description: 'Beautiful outdoor garden spaces perfect for intimate gatherings and celebrations',
    longDescription: 'Garden venues offer a natural, serene setting for your special events. These outdoor spaces feature lush greenery, beautiful landscaping, and often include gazebos, pergolas, or outdoor pavilions. Perfect for weddings, birthday parties, corporate events, and intimate gatherings.',
    features: ['Outdoor setting', 'Natural ambiance', 'Photo opportunities', 'Fresh air', 'Flexible layouts'],
    averagePrice: '$150-400/hour',
    popularLocations: ['Sydney CBD', 'Inner West', 'Eastern Suburbs', 'North Shore'],
    seoTitle: 'Garden Venue Hire Sydney NSW | Outdoor Garden Party Spaces',
    seoDescription: 'Book beautiful garden venues in Sydney for your next event. Outdoor garden party spaces with natural settings, perfect for weddings, birthdays & corporate events.'
  },
  outdoor: {
    name: 'Outdoor Venues',
    description: 'Open-air venues perfect for large gatherings and outdoor celebrations',
    longDescription: 'Outdoor venues provide spacious, open-air environments ideal for larger events and celebrations. These venues often feature expansive grounds, outdoor stages, and flexible seating arrangements. Great for festivals, large parties, corporate events, and community gatherings.',
    features: ['Spacious areas', 'Open-air setting', 'Flexible capacity', 'Natural lighting', 'Weather contingency'],
    averagePrice: '$200-600/hour',
    popularLocations: ['Parramatta', 'Penrith', 'Central Coast', 'Blue Mountains'],
    seoTitle: 'Outdoor Venue Hire Sydney NSW | Open Air Event Spaces',
    seoDescription: 'Hire outdoor venues in Sydney for your event. Spacious open-air venues perfect for large parties, festivals, corporate events & celebrations.'
  },
  waterfront: {
    name: 'Waterfront Venues',
    description: 'Stunning waterfront locations with harbor and ocean views',
    longDescription: 'Waterfront venues offer breathtaking views and a sophisticated atmosphere for your events. Located along Sydney\'s beautiful harbors, rivers, and coastline, these venues provide stunning backdrops and often include outdoor terraces, private jetties, and panoramic water views.',
    features: ['Water views', 'Premium locations', 'Photo opportunities', 'Sophisticated atmosphere', 'Outdoor terraces'],
    averagePrice: '$300-800/hour',
    popularLocations: ['Sydney Harbour', 'Manly', 'Cronulla', 'Parramatta River'],
    seoTitle: 'Waterfront Venue Hire Sydney | Harbor View Event Spaces',
    seoDescription: 'Book premium waterfront venues in Sydney with stunning harbor views. Perfect for weddings, corporate events & special celebrations with water views.'
  },
  beachside: {
    name: 'Beachside Venues',
    description: 'Coastal venues with beach access and ocean views',
    longDescription: 'Beachside venues combine the beauty of coastal settings with convenient event facilities. These venues offer direct beach access, ocean views, and often include beachfront pavilions, surf clubs, or coastal function centers. Perfect for relaxed, coastal-themed celebrations.',
    features: ['Beach access', 'Ocean views', 'Coastal atmosphere', 'Relaxed setting', 'Surf club facilities'],
    averagePrice: '$250-500/hour',
    popularLocations: ['Bondi', 'Manly', 'Cronulla', 'Newcastle'],
    seoTitle: 'Beachside Venue Hire Sydney | Coastal Event Spaces NSW',
    seoDescription: 'Hire beachside venues in Sydney with ocean views and beach access. Perfect coastal event spaces for weddings, parties & celebrations by the beach.'
  }
};

export default function CategoryPage() {
  const { category } = useParams<{ category: string }>();
  const categoryInfo = category ? categoryData[category] : null;

  if (!categoryInfo) {
    return <Navigate to="/find-venues" replace />;
  }

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: categoryInfo.name,
    description: categoryInfo.description,
    provider: {
      '@type': 'Organization',
      name: 'HouseGoing',
      url: 'https://housegoing.com.au'
    },
    areaServed: {
      '@type': 'State',
      name: 'New South Wales',
      addressCountry: 'AU'
    },
    priceRange: categoryInfo.averagePrice,
    serviceType: 'Venue Rental'
  };

  return (
    <>
      <Helmet>
        <title>{categoryInfo.seoTitle}</title>
        <meta name="description" content={categoryInfo.seoDescription} />
        <meta property="og:title" content={categoryInfo.seoTitle} />
        <meta property="og:description" content={categoryInfo.seoDescription} />
        <meta property="og:url" content={`https://housegoing.com.au/categories/${category}`} />
        <link rel="canonical" href={`https://housegoing.com.au/categories/${category}`} />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>

      <div className="pt-20 pb-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {categoryInfo.name} in NSW
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                {categoryInfo.description}
              </p>
              <Link
                to="/find-venues"
                className="inline-flex items-center px-8 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
              >
                Browse All Venues
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="prose prose-lg max-w-none">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  About {categoryInfo.name}
                </h2>
                <p className="text-gray-600 mb-8">
                  {categoryInfo.longDescription}
                </p>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Key Features
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  {categoryInfo.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Star className="h-5 w-5 text-purple-600 mr-3" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Popular Locations
                </h3>
                <div className="grid md:grid-cols-2 gap-4 mb-8">
                  {categoryInfo.popularLocations.map((location, index) => (
                    <Link
                      key={index}
                      to={`/find-venues?location=${encodeURIComponent(location)}`}
                      className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                    >
                      <MapPin className="h-5 w-5 text-purple-600 mr-3" />
                      <span className="text-gray-700 hover:text-purple-600">
                        {location}
                      </span>
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Quick Facts
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Average Price</div>
                      <div className="text-gray-600">{categoryInfo.averagePrice}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Capacity</div>
                      <div className="text-gray-600">Varies by venue</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">Locations</div>
                      <div className="text-gray-600">Across NSW</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Need Help Choosing?
                </h3>
                <p className="text-gray-600 mb-4">
                  Our venue experts can help you find the perfect {categoryInfo.name.toLowerCase()} for your event.
                </p>
                <Link
                  to="/contact"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Get Expert Help
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Book Your {categoryInfo.name}?
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Browse our selection of {categoryInfo.name.toLowerCase()} and find the perfect space for your next event.
            </p>
            <Link
              to={`/find-venues?category=${category}`}
              className="inline-flex items-center px-8 py-4 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors text-lg"
            >
              Browse {categoryInfo.name}
              <ArrowRight className="ml-2 h-6 w-6" />
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
