// File: src/components/search/NSWSuburbsSearchBar.tsx
import React, { useState } from 'react';
import { useNSWSuburbsSearch } from '../../hooks/useNSWSuburbs';

export const NSWSuburbsSearchBar = ({ onSelect }) => {
  const [query, setQuery] = useState('');
  const { suburbs, loading } = useNSWSuburbsSearch(query);

  return (
    <div className="relative">
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search NSW suburbs..."
        className="w-full p-3 border rounded-lg"
      />
      {suburbs.length > 0 && (
        <div className="absolute z-10 w-full bg-white border rounded-lg shadow-lg">
          {suburbs.map((suburb) => (
            <div
              key={`${suburb.name}-${suburb.postcode}`}
              onClick={() => onSelect(suburb)}
              className="p-2 hover:bg-gray-100 cursor-pointer"
            >
              {suburb.name}, {suburb.postcode}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
