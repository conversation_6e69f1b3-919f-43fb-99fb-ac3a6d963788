/**
 * Business Analytics Service - <PERSON> Inspired Data Management
 *
 * Essential metrics for business growth based on <PERSON>'s principles:
 * 1. Customer Acquisition Cost (CAC)
 * 2. Lifetime Value (LTV)
 * 3. Revenue Growth Rate
 * 4. Churn Rate
 * 5. Unit Economics
 * 6. Operational Efficiency
 */

import { getSupabaseClient } from '../lib/supabase-client';

// Core Business Metrics Types
export interface BusinessMetrics {
  // Revenue Metrics
  totalRevenue: number;
  monthlyRecurringRevenue: number;
  averageOrderValue: number;
  revenueGrowthRate: number;

  // Customer Metrics
  totalCustomers: number;
  newCustomers: number;
  activeCustomers: number;
  customerAcquisitionCost: number;
  customerLifetimeValue: number;
  churnRate: number;

  // Host/Supply Metrics
  totalHosts: number;
  activeHosts: number;
  averageHostRevenue: number;
  hostRetentionRate: number;

  // Booking Metrics
  totalBookings: number;
  bookingConversionRate: number;
  averageBookingValue: number;
  repeatBookingRate: number;

  // Operational Metrics
  platformCommission: number;
  operationalCosts: number;
  profitMargin: number;

  // Growth Metrics
  monthOverMonthGrowth: number;
  yearOverYearGrowth: number;

  // Calculated at
  calculatedAt: string;
}

export interface CustomerSegment {
  segment: string;
  count: number;
  revenue: number;
  averageValue: number;
  retentionRate: number;
}

export interface HostPerformance {
  hostId: string;
  hostName: string;
  totalBookings: number;
  totalRevenue: number;
  averageRating: number;
  responseRate: number;
  listingCount: number;
}

/**
 * Calculate Customer Acquisition Cost (CAC)
 * Total marketing spend / Number of new customers acquired
 */
export async function calculateCAC(timeframe: 'month' | 'quarter' | 'year' = 'month'): Promise<number> {
  try {
    const supabase = getSupabaseClient();

    // Get marketing spend (placeholder - would integrate with actual marketing data)
    const marketingSpend = await getMarketingSpend(timeframe);

    // Get new customers in timeframe
    const { data: newCustomers } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('role', 'customer')
      .gte('created_at', getTimeframeStart(timeframe));

    const customerCount = newCustomers?.length || 0;
    return customerCount > 0 ? marketingSpend / customerCount : 0;
  } catch (error) {
    console.error('Error calculating CAC:', error);
    return 0;
  }
}

/**
 * Calculate Customer Lifetime Value (LTV)
 * Average order value × Purchase frequency × Customer lifespan
 */
export async function calculateLTV(): Promise<number> {
  try {
    const supabase = getSupabaseClient();

    // Get average booking value
    const { data: bookings } = await supabase
      .from('bookings')
      .select('total_amount, customer_id, created_at');

    if (!bookings || bookings.length === 0) return 0;

    const totalRevenue = bookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
    const averageOrderValue = totalRevenue / bookings.length;

    // Calculate average customer lifespan and frequency
    const customerMetrics = calculateCustomerMetrics(bookings);

    return averageOrderValue * customerMetrics.averageFrequency * customerMetrics.averageLifespan;
  } catch (error) {
    console.error('Error calculating LTV:', error);
    return 0;
  }
}

/**
 * Calculate comprehensive business metrics
 */
export async function getBusinessMetrics(): Promise<BusinessMetrics> {
  try {
    console.log('📊 Calculating business metrics...');
    const supabase = getSupabaseClient();

    // Add timeout to prevent hanging
    const timeout = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database query timeout')), 8000)
    );

    // Get all necessary data with timeout
    const dataPromise = Promise.all([
      supabase.from('bookings').select('*'),
      supabase.from('user_profiles').select('*'),
      supabase.from('venues').select('*'),
      supabase.from('user_profiles').select('*').eq('role', 'host')
    ]);

    const [
      bookingsData,
      usersData,
      venuesData,
      hostData
    ] = await Promise.race([dataPromise, timeout]) as any;

    const bookings = bookingsData.data || [];
    const users = usersData.data || [];
    const venues = venuesData.data || [];
    const hosts = hostData.data || [];

    // Calculate revenue metrics
    const totalRevenue = bookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
    const averageOrderValue = bookings.length > 0 ? totalRevenue / bookings.length : 0;

    // Calculate customer metrics
    const customers = users.filter(user => user.role === 'customer');
    const cac = await calculateCAC();
    const ltv = await calculateLTV();

    // Calculate growth rates
    const currentMonth = new Date().getMonth();
    const lastMonth = currentMonth - 1;

    const currentMonthBookings = bookings.filter(b =>
      new Date(b.created_at).getMonth() === currentMonth
    );
    const lastMonthBookings = bookings.filter(b =>
      new Date(b.created_at).getMonth() === lastMonth
    );

    const monthOverMonthGrowth = lastMonthBookings.length > 0
      ? ((currentMonthBookings.length - lastMonthBookings.length) / lastMonthBookings.length) * 100
      : 0;

    // Platform commission (10% default)
    const platformCommission = totalRevenue * 0.10;

    return {
      // Revenue Metrics
      totalRevenue,
      monthlyRecurringRevenue: calculateMRR(bookings),
      averageOrderValue,
      revenueGrowthRate: monthOverMonthGrowth,

      // Customer Metrics
      totalCustomers: customers.length,
      newCustomers: getNewCustomersThisMonth(customers),
      activeCustomers: getActiveCustomers(bookings),
      customerAcquisitionCost: cac,
      customerLifetimeValue: ltv,
      churnRate: calculateChurnRate(customers, bookings),

      // Host/Supply Metrics
      totalHosts: hosts.length,
      activeHosts: getActiveHosts(bookings, hosts),
      averageHostRevenue: calculateAverageHostRevenue(bookings, hosts),
      hostRetentionRate: calculateHostRetentionRate(hosts, bookings),

      // Booking Metrics
      totalBookings: bookings.length,
      bookingConversionRate: calculateConversionRate(users, bookings),
      averageBookingValue: averageOrderValue,
      repeatBookingRate: calculateRepeatBookingRate(bookings),

      // Operational Metrics
      platformCommission,
      operationalCosts: estimateOperationalCosts(totalRevenue),
      profitMargin: ((platformCommission - estimateOperationalCosts(totalRevenue)) / totalRevenue) * 100,

      // Growth Metrics
      monthOverMonthGrowth,
      yearOverYearGrowth: calculateYearOverYearGrowth(bookings),

      calculatedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error calculating business metrics:', error);
    return getDefaultMetrics();
  }
}

/**
 * Get customer segments for analysis
 */
export async function getCustomerSegments(): Promise<CustomerSegment[]> {
  try {
    console.log('👥 Getting customer segments...');
    const supabase = getSupabaseClient();

    // Add timeout
    const timeout = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Customer segments query timeout')), 5000)
    );

    const queryPromise = supabase
      .from('bookings')
      .select('customer_id, total_amount, created_at');

    const { data: bookings } = await Promise.race([queryPromise, timeout]) as any;

    if (!bookings) {
      console.log('📝 No bookings data, returning empty segments');
      return [];
    }

    // Segment customers by booking frequency
    const customerBookings = groupBy(bookings, 'customer_id');

    const segments = [
      { name: 'High Value', min: 5, max: Infinity },
      { name: 'Regular', min: 2, max: 4 },
      { name: 'One-time', min: 1, max: 1 }
    ];

    return segments.map(segment => {
      const segmentCustomers = Object.entries(customerBookings).filter(
        ([_, bookings]) => bookings.length >= segment.min && bookings.length <= segment.max
      );

      const totalRevenue = segmentCustomers.reduce((sum, [_, bookings]) =>
        sum + bookings.reduce((bookingSum, booking) => bookingSum + (booking.total_amount || 0), 0), 0
      );

      return {
        segment: segment.name,
        count: segmentCustomers.length,
        revenue: totalRevenue,
        averageValue: segmentCustomers.length > 0 ? totalRevenue / segmentCustomers.length : 0,
        retentionRate: calculateSegmentRetention(segmentCustomers)
      };
    });
  } catch (error) {
    console.error('Error getting customer segments:', error);
    return [];
  }
}

/**
 * Get top performing hosts
 */
export async function getTopHosts(limit: number = 10): Promise<HostPerformance[]> {
  try {
    console.log('🏆 Getting top hosts...');
    const supabase = getSupabaseClient();

    // Add timeout
    const timeout = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Top hosts query timeout')), 5000)
    );

    const dataPromise = Promise.all([
      supabase.from('bookings').select('host_id, total_amount, rating'),
      supabase.from('venues').select('host_id, id'),
      supabase.from('user_profiles').select('id, first_name, last_name').eq('role', 'host')
    ]);

    const [
      { data: bookings },
      { data: venues },
      { data: hosts }
    ] = await Promise.race([dataPromise, timeout]) as any;

    if (!bookings || !hosts) {
      console.log('📝 No host data, returning empty list');
      return [];
    }

    const hostMetrics = hosts.map(host => {
      const hostBookings = bookings.filter(b => b.host_id === host.id);
      const hostVenues = venues?.filter(v => v.host_id === host.id) || [];

      const totalRevenue = hostBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
      const averageRating = hostBookings.length > 0
        ? hostBookings.reduce((sum, booking) => sum + (booking.rating || 0), 0) / hostBookings.length
        : 0;

      return {
        hostId: host.id,
        hostName: `${host.first_name || ''} ${host.last_name || ''}`.trim() || 'Unknown Host',
        totalBookings: hostBookings.length,
        totalRevenue,
        averageRating,
        responseRate: 95, // Placeholder - would track actual response times
        listingCount: hostVenues.length
      };
    });

    return hostMetrics
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting top hosts:', error);
    return [];
  }
}

// Helper functions
function getTimeframeStart(timeframe: string): string {
  const now = new Date();
  switch (timeframe) {
    case 'month':
      return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3);
      return new Date(now.getFullYear(), quarter * 3, 1).toISOString();
    case 'year':
      return new Date(now.getFullYear(), 0, 1).toISOString();
    default:
      return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
  }
}

function getMarketingSpend(timeframe: string): number {
  // Placeholder - would integrate with actual marketing spend tracking
  const monthlySpend = 5000; // $5k monthly marketing budget
  switch (timeframe) {
    case 'month': return monthlySpend;
    case 'quarter': return monthlySpend * 3;
    case 'year': return monthlySpend * 12;
    default: return monthlySpend;
  }
}

function calculateCustomerMetrics(bookings: any[]) {
  // Calculate average purchase frequency and customer lifespan
  const customerBookings = groupBy(bookings, 'customer_id');

  const frequencies = Object.values(customerBookings).map(bookings => bookings.length);
  const averageFrequency = frequencies.reduce((sum, freq) => sum + freq, 0) / frequencies.length || 0;

  // Estimate average customer lifespan (in months)
  const averageLifespan = 12; // Placeholder - would calculate from actual data

  return { averageFrequency, averageLifespan };
}

function calculateMRR(bookings: any[]): number {
  const currentMonth = new Date().getMonth();
  const currentMonthBookings = bookings.filter(b =>
    new Date(b.created_at).getMonth() === currentMonth
  );
  return currentMonthBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
}

function getNewCustomersThisMonth(customers: any[]): number {
  const currentMonth = new Date().getMonth();
  return customers.filter(customer =>
    new Date(customer.created_at).getMonth() === currentMonth
  ).length;
}

function getActiveCustomers(bookings: any[]): number {
  const last30Days = new Date();
  last30Days.setDate(last30Days.getDate() - 30);

  const recentBookings = bookings.filter(booking =>
    new Date(booking.created_at) >= last30Days
  );

  return new Set(recentBookings.map(booking => booking.customer_id)).size;
}

function calculateChurnRate(customers: any[], bookings: any[]): number {
  // Simplified churn calculation - customers who haven't booked in 90 days
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  const recentBookings = bookings.filter(booking =>
    new Date(booking.created_at) >= ninetyDaysAgo
  );

  const activeCustomers = new Set(recentBookings.map(booking => booking.customer_id)).size;
  const totalCustomers = customers.filter(c => c.role === 'customer').length;

  return totalCustomers > 0 ? ((totalCustomers - activeCustomers) / totalCustomers) * 100 : 0;
}

function getActiveHosts(bookings: any[], hosts: any[]): number {
  const last30Days = new Date();
  last30Days.setDate(last30Days.getDate() - 30);

  const recentBookings = bookings.filter(booking =>
    new Date(booking.created_at) >= last30Days
  );

  return new Set(recentBookings.map(booking => booking.host_id)).size;
}

function calculateAverageHostRevenue(bookings: any[], hosts: any[]): number {
  const hostRevenues = hosts.map(host => {
    const hostBookings = bookings.filter(b => b.host_id === host.id);
    return hostBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
  });

  return hostRevenues.length > 0
    ? hostRevenues.reduce((sum, revenue) => sum + revenue, 0) / hostRevenues.length
    : 0;
}

function calculateHostRetentionRate(hosts: any[], bookings: any[]): number {
  // Hosts who have had bookings in the last 90 days
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  const recentBookings = bookings.filter(booking =>
    new Date(booking.created_at) >= ninetyDaysAgo
  );

  const activeHosts = new Set(recentBookings.map(booking => booking.host_id)).size;

  return hosts.length > 0 ? (activeHosts / hosts.length) * 100 : 0;
}

function calculateConversionRate(users: any[], bookings: any[]): number {
  const customers = users.filter(user => user.role === 'customer');
  const customersWithBookings = new Set(bookings.map(booking => booking.customer_id)).size;

  return customers.length > 0 ? (customersWithBookings / customers.length) * 100 : 0;
}

function calculateRepeatBookingRate(bookings: any[]): number {
  const customerBookings = groupBy(bookings, 'customer_id');
  const repeatCustomers = Object.values(customerBookings).filter(bookings => bookings.length > 1).length;
  const totalCustomers = Object.keys(customerBookings).length;

  return totalCustomers > 0 ? (repeatCustomers / totalCustomers) * 100 : 0;
}

function estimateOperationalCosts(revenue: number): number {
  // Estimate operational costs as 30% of revenue
  return revenue * 0.30;
}

function calculateYearOverYearGrowth(bookings: any[]): number {
  const currentYear = new Date().getFullYear();
  const lastYear = currentYear - 1;

  const currentYearBookings = bookings.filter(b =>
    new Date(b.created_at).getFullYear() === currentYear
  );
  const lastYearBookings = bookings.filter(b =>
    new Date(b.created_at).getFullYear() === lastYear
  );

  return lastYearBookings.length > 0
    ? ((currentYearBookings.length - lastYearBookings.length) / lastYearBookings.length) * 100
    : 0;
}

function calculateSegmentRetention(segmentCustomers: any[]): number {
  // Simplified retention calculation
  return 75; // Placeholder
}

function groupBy(array: any[], key: string): { [key: string]: any[] } {
  return array.reduce((groups, item) => {
    const group = item[key] || 'unknown';
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {});
}

function getDefaultMetrics(): BusinessMetrics {
  return {
    totalRevenue: 0,
    monthlyRecurringRevenue: 0,
    averageOrderValue: 0,
    revenueGrowthRate: 0,
    totalCustomers: 0,
    newCustomers: 0,
    activeCustomers: 0,
    customerAcquisitionCost: 0,
    customerLifetimeValue: 0,
    churnRate: 0,
    totalHosts: 0,
    activeHosts: 0,
    averageHostRevenue: 0,
    hostRetentionRate: 0,
    totalBookings: 0,
    bookingConversionRate: 0,
    averageBookingValue: 0,
    repeatBookingRate: 0,
    platformCommission: 0,
    operationalCosts: 0,
    profitMargin: 0,
    monthOverMonthGrowth: 0,
    yearOverYearGrowth: 0,
    calculatedAt: new Date().toISOString()
  };
}
