import UserProfile from './UserProfile';
import ClerkSupabaseInitializer from '../components/auth/ClerkSupabaseInitializer.simple';
import AccountPageFix from '../components/account/AccountPageFix';
import { useEffect } from 'react';

export default function MyAccount() {  
  // Add some client-side debugging to help track down issues
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('MyAccount component mounted');
      
      // Check if we have a stuck loading state
      const checkForStuckState = () => {
        const loadingElements = document.querySelectorAll('.animate-spin');
        if (loadingElements.length > 0) {
          console.warn('Loading spinners detected after 10 seconds, possible stuck state');
          
          // Add debugging info to help
          console.log('Auth state:', {
            clerk: window.Clerk?.user ? 'Available' : 'Not available',
            session: window.Clerk?.session ? 'Available' : 'Not available',
            signedIn: window.Clerk?.user ? 'Yes' : 'No'
          });
        }
      };
      
      // Check after 10 seconds
      const timeoutId = setTimeout(checkForStuckState, 10000);
      return () => clearTimeout(timeoutId);
    }
  }, []);
  
  return (
    <ClerkSupabaseInitializer>
      <AccountPageFix>
        <UserProfile />
      </AccountPageFix>
    </ClerkSupabaseInitializer>
  );
}