// File: src/hooks/useNSWSuburbsSearch.ts
import { useState, useEffect } from 'react';
import { searchNSWSuburbs } from '../services/nsw-suburbs';

export const useNSWSuburbsSearch = (query: string) => {
  const [suburbs, setSuburbs] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Auto-suggestions start from 1 character for better UX
    if (query.length >= 1) {
      setLoading(true);
      console.log(`🔍 Auto-suggesting for: "${query}"`);

      searchNSWSuburbs(query)
        .then(results => {
          console.log(`✅ Found ${results.length} auto-suggestions for "${query}"`);
          setSuburbs(results);
        })
        .catch(error => {
          console.error('❌ Auto-suggestion error:', error);
          setSuburbs([]);
        })
        .finally(() => setLoading(false));
    } else {
      // Clear results when query is empty
      setSuburbs([]);
      setLoading(false);
    }
  }, [query]);

  return { suburbs, loading };
};
