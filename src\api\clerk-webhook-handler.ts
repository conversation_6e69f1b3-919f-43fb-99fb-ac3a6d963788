import { <PERSON>LERK_CONFIG } from '../config/clerk';

// Types for Clerk webhook events
type ClerkEvent = {
  type: string;
  data: {
    id: string;
    email_addresses?: Array<{ email_address: string }>;
    public_metadata?: Record<string, any>;
    private_metadata?: Record<string, any>;
    [key: string]: any;
  };
};

/**
 * Process Clerk webhook events
 * This would normally be a server-side function, but we're implementing a client-side version
 * for demonstration purposes
 */
export async function processClerkWebhook(event: ClerkEvent): Promise<void> {
  console.log('Processing Clerk webhook event:', event.type);
  
  try {
    switch (event.type) {
      case 'user.created':
        await handleUserCreated(event.data);
        break;
      
      case 'user.updated':
        await handleUserUpdated(event.data);
        break;
        
      case 'session.created':
        await handleSessionCreated(event.data);
        break;
        
      default:
        console.log('Unhandled event type:', event.type);
    }
  } catch (error) {
    console.error('Error processing webhook:', error);
  }
}

/**
 * Handle user.created event
 */
async function handleUserCreated(userData: ClerkEvent['data']): Promise<void> {
  console.log('User created:', userData.id);
  
  // Check if this is a host registration
  const isHostRegistration = localStorage.getItem('registering_as_host') === 'true';
  
  if (isHostRegistration) {
    console.log('Host registration detected for new user');
    
    // Store the user as a host in localStorage
    try {
      // Get user email
      const userEmail = userData.email_addresses?.[0]?.email_address;
      
      if (userEmail) {
        // Add to registered hosts in localStorage
        const registeredHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
        if (!registeredHosts.includes(userEmail)) {
          registeredHosts.push(userEmail);
          localStorage.setItem('registeredHosts', JSON.stringify(registeredHosts));
          console.log('Added user to registeredHosts in localStorage:', userEmail);
        }
      }
    } catch (err) {
      console.error('Failed to update localStorage:', err);
    }
    
    // In a real implementation, we would update the user's metadata on the server
    // For now, we'll rely on the client-side update in the OAuth callback
  }
}

/**
 * Handle user.updated event
 */
async function handleUserUpdated(userData: ClerkEvent['data']): Promise<void> {
  console.log('User updated:', userData.id);
  
  // Check if the user's role was updated to host
  const isHost = userData.public_metadata?.role === 'host';
  
  if (isHost) {
    console.log('User role updated to host');
    
    // Store the user as a host in localStorage
    try {
      // Get user email
      const userEmail = userData.email_addresses?.[0]?.email_address;
      
      if (userEmail) {
        // Add to registered hosts in localStorage
        const registeredHosts = JSON.parse(localStorage.getItem('registeredHosts') || '[]');
        if (!registeredHosts.includes(userEmail)) {
          registeredHosts.push(userEmail);
          localStorage.setItem('registeredHosts', JSON.stringify(registeredHosts));
          console.log('Added user to registeredHosts in localStorage:', userEmail);
        }
      }
    } catch (err) {
      console.error('Failed to update localStorage:', err);
    }
  }
}

/**
 * Handle session.created event
 */
async function handleSessionCreated(sessionData: ClerkEvent['data']): Promise<void> {
  console.log('Session created:', sessionData.id);
  
  // Check if this is a host login
  const isHostLogin = localStorage.getItem('registering_as_host') === 'true';
  
  if (isHostLogin) {
    console.log('Host login detected');
    // In a real implementation, we would verify the user's host status on the server
  }
}

/**
 * Simulate webhook events for client-side testing
 */
export function simulateWebhookEvent(eventType: string, userData: any): void {
  const event: ClerkEvent = {
    type: eventType,
    data: {
      id: userData.id,
      ...userData
    }
  };
  
  processClerkWebhook(event);
}
