/**
 * Example frontend integration for the Sales Assistant
 * 
 * This file demonstrates how to integrate the Sales Assistant
 * with your website's frontend.
 */

// Store the session ID in localStorage
function getSessionId() {
  let sessionId = localStorage.getItem('salesAssistantSessionId');
  
  if (!sessionId) {
    // Generate a random session ID if one doesn't exist
    sessionId = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('salesAssistantSessionId', sessionId);
  }
  
  return sessionId;
}

// Send a message to the Sales Assistant
async function sendMessage(message) {
  const sessionId = getSessionId();
  
  try {
    const response = await fetch('http://localhost:3002/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message,
        sessionId
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data.response;
  } catch (error) {
    console.error('Error sending message:', error);
    return 'Sorry, I encountered an error. Please try again.';
  }
}

// Reset the conversation
async function resetConversation() {
  const sessionId = getSessionId();
  
  try {
    const response = await fetch('http://localhost:3002/api/reset', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error resetting conversation:', error);
    return false;
  }
}

// Example usage in a chat interface
document.addEventListener('DOMContentLoaded', () => {
  // Get DOM elements
  const chatForm = document.getElementById('chat-form');
  const messageInput = document.getElementById('message-input');
  const chatMessages = document.getElementById('chat-messages');
  const resetButton = document.getElementById('reset-button');
  
  // Add event listener for form submission
  chatForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const message = messageInput.value.trim();
    if (!message) return;
    
    // Clear input
    messageInput.value = '';
    
    // Add user message to chat
    addMessage('user', message);
    
    // Show loading indicator
    const loadingMessage = addMessage('assistant', '...', 'loading');
    
    // Get response from Sales Assistant
    const response = await sendMessage(message);
    
    // Remove loading indicator and add assistant response
    loadingMessage.remove();
    addMessage('assistant', response);
  });
  
  // Add event listener for reset button
  resetButton.addEventListener('click', async () => {
    const success = await resetConversation();
    
    if (success) {
      // Clear chat messages
      chatMessages.innerHTML = '';
      
      // Add welcome message
      addMessage('assistant', 'Hi there! I\'m Alex, your HouseGoing assistant. How can I help you find the perfect venue for your event?');
    }
  });
  
  // Function to add a message to the chat
  function addMessage(role, content, className = '') {
    const messageElement = document.createElement('div');
    messageElement.classList.add('message', role, className);
    
    const contentElement = document.createElement('div');
    contentElement.classList.add('content');
    contentElement.innerHTML = formatMessage(content);
    
    messageElement.appendChild(contentElement);
    chatMessages.appendChild(messageElement);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    return messageElement;
  }
  
  // Function to format message with markdown-like syntax
  function formatMessage(message) {
    // Replace **text** with <strong>text</strong>
    message = message.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Replace newlines with <br>
    message = message.replace(/\n/g, '<br>');
    
    return message;
  }
  
  // Add initial welcome message
  addMessage('assistant', 'Hi there! I\'m Alex, your HouseGoing assistant. How can I help you find the perfect venue for your event?');
});

// Example HTML structure:
/*
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing Chat</title>
  <style>
    /* Chat styles */
    #chat-container {
      max-width: 400px;
      margin: 0 auto;
      border: 1px solid #ccc;
      border-radius: 8px;
      overflow: hidden;
    }
    
    #chat-messages {
      height: 400px;
      overflow-y: auto;
      padding: 10px;
      background-color: #f9f9f9;
    }
    
    .message {
      margin-bottom: 10px;
      max-width: 80%;
      clear: both;
    }
    
    .user {
      float: right;
    }
    
    .assistant {
      float: left;
    }
    
    .content {
      padding: 8px 12px;
      border-radius: 18px;
    }
    
    .user .content {
      background-color: #0084ff;
      color: white;
    }
    
    .assistant .content {
      background-color: #e5e5ea;
      color: black;
    }
    
    .loading .content {
      opacity: 0.5;
    }
    
    #chat-form {
      display: flex;
      padding: 10px;
      background-color: white;
      border-top: 1px solid #ccc;
    }
    
    #message-input {
      flex-grow: 1;
      padding: 8px 12px;
      border: 1px solid #ccc;
      border-radius: 20px;
      margin-right: 8px;
    }
    
    button {
      padding: 8px 16px;
      background-color: #0084ff;
      color: white;
      border: none;
      border-radius: 20px;
      cursor: pointer;
    }
    
    #reset-button {
      background-color: #ff3b30;
      margin-left: 8px;
    }
  </style>
</head>
<body>
  <div id="chat-container">
    <div id="chat-messages"></div>
    <form id="chat-form">
      <input type="text" id="message-input" placeholder="Type your message...">
      <button type="submit">Send</button>
      <button type="button" id="reset-button">Reset</button>
    </form>
  </div>
  
  <script src="frontend-example.js"></script>
</body>
</html>
*/
