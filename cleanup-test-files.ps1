# Clean up unused test files
$filesToRemove = @(
    "test-huggingface.js",
    "test-langchain.js",
    "test-wfs.js",
    "test-api.js",
    "test-address.js",
    "test-glendenning-address.js",
    "testGlendenning.js",
    "testGlendenningMaps.js",
    "testGlendenningMapbox.js",
    "testMarrickville.js",
    "testMarrickvilleMaps.js",
    "testMarrickvilleNSW.js",
    "testNewtown.js",
    "testNewtownDirect.js",
    "testRuralNSW.js",
    "test-nsw-api-endpoints.js",
    "test-nsw-party-planning.js",
    "test-specific-addresses.js",
    "test-nsw-party-planning-fixed.js",
    "test-component-accuracy.js",
    "test-specific-lga-methods.js",
    "test-zoning.js"
)

# Create backups directory if it doesn't exist
$backupDir = ".\test-files-backup"
if (-not (Test-Path -Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir | Out-Null
    Write-Host "Created backup directory: $backupDir"
}

# Move files to backup directory
foreach ($file in $filesToRemove) {
    if (Test-Path -Path ".\$file") {
        Move-Item -Path ".\$file" -Destination "$backupDir\$file" -Force
        Write-Host "Moved $file to backup directory"
    } else {
        Write-Host "File $file not found, skipping"
    }
}

Write-Host "`nAll unused test files have been moved to $backupDir"
Write-Host "If you need to restore these files, you can find them in the backup directory."
