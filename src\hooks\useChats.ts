import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

interface Chat {
  id: string;
  otherUser: {
    name: string;
    image: string;
  };
  lastMessage: {
    content: string;
    timestamp: string;
  };
}

export function useChats() {
  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    // Replace with actual API call
    const fetchChats = async () => {
      setIsLoading(true);
      try {
        // Mock data
        setChats([
          {
            id: '1',
            otherUser: {
              name: '<PERSON>',
              image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e'
            },
            lastMessage: {
              content: 'Looking forward to hosting your event!',
              timestamp: new Date().toISOString()
            }
          }
        ]);
      } catch (error) {
        console.error('Error fetching chats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchChats();
    }
  }, [user]);

  return { chats, isLoading };
}