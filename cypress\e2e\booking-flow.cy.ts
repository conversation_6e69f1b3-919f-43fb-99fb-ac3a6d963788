describe('Booking Flow', () => {
  beforeEach(() => {
    // Mock user authentication
    cy.intercept('POST', '/api/auth/session', { fixture: 'user-session.json' }).as('session');
    
    // Mock venue data
    cy.intercept('GET', '/api/venues*', { fixture: 'venues.json' }).as('getVenues');
    cy.intercept('GET', '/api/venues/1', { fixture: 'venue-detail.json' }).as('getVenueDetail');
    
    // Mock booking endpoints
    cy.intercept('POST', '/api/bookings', { statusCode: 201, body: { id: '123', status: 'pending' } }).as('createBooking');
    
    // Visit the home page
    cy.visit('/');
  });

  it('should allow a user to search for venues', () => {
    // Fill in search form
    cy.get('[data-testid=location-input]').type('Sydney');
    cy.get('[data-testid=date-input]').type('2023-12-15');
    cy.get('[data-testid=guests-input]').type('30');
    cy.get('[data-testid=search-button]').click();
    
    // Wait for venues to load
    cy.wait('@getVenues');
    
    // Verify search results
    cy.get('[data-testid=venue-card]').should('have.length.at.least', 1);
    cy.get('[data-testid=venue-title]').first().should('be.visible');
  });

  it('should display venue details when a venue is selected', () => {
    // Search for venues
    cy.get('[data-testid=location-input]').type('Sydney');
    cy.get('[data-testid=search-button]').click();
    
    // Wait for venues to load
    cy.wait('@getVenues');
    
    // Click on the first venue
    cy.get('[data-testid=venue-card]').first().click();
    
    // Wait for venue details to load
    cy.wait('@getVenueDetail');
    
    // Verify venue details page
    cy.url().should('include', '/venue/');
    cy.get('[data-testid=venue-title]').should('be.visible');
    cy.get('[data-testid=venue-description]').should('be.visible');
    cy.get('[data-testid=venue-price]').should('be.visible');
    cy.get('[data-testid=booking-form]').should('be.visible');
  });

  it('should allow a logged-in user to book a venue', () => {
    // Visit venue details page directly
    cy.visit('/venue/1');
    
    // Wait for venue details to load
    cy.wait('@getVenueDetail');
    
    // Fill in booking form
    cy.get('[data-testid=booking-date]').type('2023-12-15');
    cy.get('[data-testid=booking-time]').select('18:00');
    cy.get('[data-testid=booking-duration]').select('4');
    cy.get('[data-testid=booking-guests]').type('25');
    
    // Submit booking
    cy.get('[data-testid=book-now-button]').click();
    
    // Confirm booking
    cy.get('[data-testid=confirm-booking-button]').click();
    
    // Wait for booking to be created
    cy.wait('@createBooking');
    
    // Verify success message
    cy.get('[data-testid=booking-success-message]').should('be.visible');
    cy.url().should('include', '/bookings/');
  });

  it('should show AI assistant when requested', () => {
    // Open AI assistant
    cy.get('[data-testid=ai-assistant-button]').click();
    
    // Verify AI assistant is visible
    cy.get('[data-testid=ai-assistant-chat]').should('be.visible');
    
    // Type a message
    cy.get('[data-testid=chat-input]').type('I need a venue for 30 people{enter}');
    
    // Verify response
    cy.get('[data-testid=assistant-message]').should('have.length.at.least', 2);
  });
});
