import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  active?: boolean;
}

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  isLoading = false,
  loadingText,
  leftIcon,
  rightIcon,
  active = false,
  className = '',
  disabled,
  ...props
}: ButtonProps) {
  // Base styles for all buttons with enhanced mobile support
  const baseStyles = 'font-semibold transition-all duration-200 relative inline-flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 btn-reactive focus-enhanced touch-target';

  // Size variations with mobile-optimized touch targets
  const sizeStyles = {
    sm: 'px-4 py-2 text-sm rounded-md min-h-[40px]',
    md: 'px-6 py-3 text-base rounded-lg min-h-[44px]',
    lg: 'px-8 py-4 text-lg rounded-xl min-h-[48px]'
  };

  // Variant styles with enhanced mobile feedback
  const variantStyles = {
    primary: 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:opacity-90 hover:shadow-lg active:opacity-100 active:scale-[0.98] active:shadow-md',
    secondary: 'bg-white text-gray-800 border border-gray-200 hover:border-purple-600 hover:text-purple-600 hover:shadow-md active:bg-gray-50 active:scale-[0.98]',
    outline: 'bg-transparent border-2 border-purple-600 text-purple-600 hover:bg-purple-50 hover:shadow-md active:bg-purple-100 active:scale-[0.98]',
    ghost: 'bg-transparent text-purple-600 hover:bg-purple-50 hover:shadow-sm active:bg-purple-100 active:scale-[0.98]',
    link: 'bg-transparent text-purple-600 hover:underline p-0 active:text-purple-800 min-h-[44px] flex items-center'
  };

  // State styles
  const stateStyles = {
    disabled: 'opacity-50 cursor-not-allowed pointer-events-none',
    loading: 'cursor-wait',
    active: active ? 'ring-2 ring-purple-500 ring-offset-2' : ''
  };

  // Determine if the button should be disabled
  const isDisabled = disabled || isLoading;

  return (
    <button
      className={`
        ${baseStyles}
        ${sizeStyles[size]}
        ${variantStyles[variant]}
        ${isDisabled ? stateStyles.disabled : ''}
        ${isLoading ? stateStyles.loading : ''}
        ${stateStyles.active}
        ${fullWidth ? 'w-full' : ''}
        ${className}
      `}
      disabled={isDisabled}
      {...props}
    >
      {/* Loading spinner */}
      {isLoading && (
        <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <svg className="animate-spin h-5 w-5 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
      )}

      {/* Button content with opacity controlled for loading state */}
      <span className={`flex items-center justify-center ${isLoading ? 'opacity-0' : 'opacity-100'}`}>
        {leftIcon && <span className="mr-2">{leftIcon}</span>}
        {isLoading && loadingText ? loadingText : children}
        {rightIcon && <span className="ml-2">{rightIcon}</span>}
      </span>
    </button>
  );
}