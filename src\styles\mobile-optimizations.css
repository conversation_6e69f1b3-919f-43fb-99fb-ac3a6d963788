/* Mobile-first optimizations for HouseGoing */

/* Mobile animations */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-up {
  animation: slide-in-up 0.3s ease-out;
}

/* Mobile Typography Scale - Optimized for readability */
@media (max-width: 768px) {
  /* Base typography improvements */
  body {
    font-size: 16px; /* Prevents iOS zoom */
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
  }

  /* Heading scale optimized for mobile */
  h1 {
    font-size: 2rem; /* 32px */
    line-height: 1.2;
    font-weight: 800;
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
  }

  h2 {
    font-size: 1.75rem; /* 28px */
    line-height: 1.3;
    font-weight: 700;
    letter-spacing: -0.02em;
    margin-bottom: 0.875rem;
  }

  h3 {
    font-size: 1.5rem; /* 24px */
    line-height: 1.4;
    font-weight: 600;
    margin-bottom: 0.75rem;
  }

  h4 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.4;
    font-weight: 600;
    margin-bottom: 0.625rem;
  }

  /* Body text improvements */
  p {
    font-size: 1rem; /* 16px */
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #374151;
  }

  .text-large {
    font-size: 1.125rem; /* 18px */
    line-height: 1.6;
  }

  .text-small {
    font-size: 0.875rem; /* 14px */
    line-height: 1.5;
  }

  /* Hero section mobile optimizations */
  .hero-section {
    min-height: 75vh !important;
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  /* Mobile search dropdown optimizations */
  .mobile-search-dropdown {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
  }

  .mobile-search-dropdown .search-field {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
  }

  /* Booking form mobile optimizations */
  .booking-form-mobile {
    position: sticky;
    top: 80px;
    z-index: 40;
    margin: 0 -1rem;
    border-radius: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

/* Touch target improvements - Mobile First */
  /* Ensure all interactive elements meet 48px minimum touch target for better accessibility */
  button,
  a,
  input[type="button"],
  input[type="submit"],
  .btn,
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }

  /* Enhanced button interactions for mobile */
  .btn {
    padding: 14px 20px;
    font-size: 16px; /* Prevents iOS zoom */
    border-radius: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  .btn:active {
    transform: scale(0.96);
    transition-duration: 0.1s;
  }

  .btn:focus {
    outline: 3px solid #8B5CF6;
    outline-offset: 2px;
    border-color: transparent;
  }

  /* Improved touch feedback */
  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .btn:active::before {
    opacity: 1;
  }

  /* Input improvements */
  input, 
  select, 
  textarea {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 8px;
    transition: border-color 0.15s ease, box-shadow 0.15s ease;
  }

  input:focus,
  select:focus,
  textarea:focus {
    border-color: #8B5CF6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    outline: none;
  }

  /* Enhanced reactive buttons for mobile */
  .btn-reactive {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    transform-origin: center;
  }

  /* Remove hover effects on touch devices */
  @media (hover: hover) {
    .btn-reactive:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }

  .btn-reactive:active {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition-duration: 0.1s;
  }

  /* Improved focus states for accessibility */
  .btn-reactive:focus-visible {
    outline: 3px solid #8B5CF6;
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(139, 92, 246, 0.1);
  }

  /* Navigation improvements */
  .nav-link {
    padding: 16px 20px;
    display: block;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    font-weight: 500;
  }

  .nav-link:active {
    background-color: rgba(139, 92, 246, 0.15);
    transform: scale(0.98);
  }

  /* Enhanced dropdown positioning */
  .dropdown-content,
  .search-dropdown {
    position: fixed !important;
    left: 8px !important;
    right: 8px !important;
    width: auto !important;
    max-height: 60vh;
    overflow-y: auto;
    z-index: 9999 !important;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Mobile menu enhancements */
  .mobile-menu {
    width: 100vw !important;
    max-width: none !important;
  }

  .mobile-menu-content {
    padding: 20px;
    max-height: 100vh;
    overflow-y: auto;
  }

  /* Search interface mobile optimization */
  .search-container {
    padding: 16px;
    gap: 12px;
  }

  .search-input {
    width: 100%;
    margin-bottom: 12px;
  }

  /* Mobile Layout Improvements */
  .container-width {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }

  .container-width-sm {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }

  /* Card spacing improvements */
  .card {
    margin: 0.5rem;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  /* Section spacing for mobile */
  section {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  /* Improved spacing between elements */
  .space-y-4 > * + * {
    margin-top: 1.5rem;
  }

  .space-y-6 > * + * {
    margin-top: 2rem;
  }

  /* Mobile Touch Interactions */
  /* Prevent text selection on interactive elements */
  button, .btn, .touch-target {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve touch scrolling */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Touch-friendly link styling */
  a {
    -webkit-tap-highlight-color: rgba(139, 92, 246, 0.2);
    tap-highlight-color: rgba(139, 92, 246, 0.2);
  }

  /* Swipe gesture indicators */
  .swipe-indicator {
    position: relative;
  }

  .swipe-indicator::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: rgba(139, 92, 246, 0.3);
    border-radius: 2px;
  }

  /* Pull-to-refresh indicator */
  .pull-to-refresh {
    padding-top: 60px;
    margin-top: -60px;
    transition: margin-top 0.3s ease;
  }

  /* Mobile-specific focus states */
  .focus-enhanced:focus-visible {
    outline: 3px solid #8B5CF6;
    outline-offset: 3px;
    border-radius: 8px;
  }

  /* Mobile Performance Optimizations */
  /* Hardware acceleration for smooth animations */
  .btn-reactive,
  .card,
  .mobile-menu,
  .search-dropdown {
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    will-change: transform;
  }

  /* Z-index management for mobile overlays */
  .mobile-menu {
    z-index: 9998;
  }

  .mobile-search-modal {
    z-index: 9999;
  }

  /* Ensure mobile dropdowns are above other content */
  .mobile-dropdown {
    z-index: 9997;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Optimize font loading */
  body {
    font-display: swap;
  }

  /* Lazy loading optimization */
  .lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .lazy-load.loaded {
    opacity: 1;
  }

  /* Mobile Modal and Overlay Fixes */
  /* Prevent body scroll when modal is open */
  body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Mobile-specific fixes for dropdowns and modals */
  .mobile-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
  }

  .mobile-modal-content {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 9999;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Safe area handling for mobile devices */
  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-specific button improvements */
  .mobile-button {
    min-height: 48px;
    min-width: 48px;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .mobile-button:active {
    transform: scale(0.98);
  }

  /* Grid improvements */
  .grid-mobile {
    grid-template-columns: 1fr !important;
    gap: 16px;
  }

  /* Typography scaling */
  h1 { font-size: 2rem; line-height: 1.2; }
  h2 { font-size: 1.75rem; line-height: 1.3; }
  h3 { font-size: 1.5rem; line-height: 1.4; }
  
  /* Spacing improvements */
  .section-mobile {
    padding: 24px 16px;
  }

  /* Loading states */
  .loading-mobile {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .btn {
    padding: 10px 14px;
    font-size: 15px;
  }

  input, select, textarea {
    min-height: 40px;
    font-size: 15px;
    padding: 10px 14px;
  }
}

/* Enhanced button reactivity for all screen sizes */
.btn-reactive {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.btn-reactive:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-reactive:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Improved focus states */
.focus-enhanced:focus {
  outline: 2px solid #8B5CF6;
  outline-offset: 2px;
  border-color: transparent;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent horizontal scroll */
body {
  overflow-x: hidden;
}

/* Enhanced mobile viewport handling */
@viewport {
  width: device-width;
  initial-scale: 1;
  maximum-scale: 1;
  user-scalable: no;
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  input, select, textarea {
    font-size: 16px !important; /* Prevents zoom */
  }
  
  .btn {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }
}

/* Android specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select {
    background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    padding-right: 40px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .btn {
    border-width: 0.5px;
  }
  
  input, select, textarea {
    border-width: 0.5px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .btn:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .nav-link:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .nav-link,
  input,
  select,
  textarea {
    transition: none;
  }
  
  .btn-reactive:hover,
  .btn-reactive:active {
    transform: none;
  }
}

/* Print styles */
@media print {
  .btn,
  .mobile-menu,
  .dropdown-content {
    display: none;
  }
}
