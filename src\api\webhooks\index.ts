import { WebhookEvent } from '@clerk/clerk-react';
import { handleClerkWebhook } from './clerk';

// Webhook secret from environment variable
const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

// Verify the webhook signature
function verifyWebhookSignature(payload: string, headers: Headers): boolean {
  if (!WEBHOOK_SECRET) {
    console.warn('CLERK_WEBHOOK_SECRET is not set. Skipping signature verification.');
    return true;
  }

  // In a real implementation, you would verify the signature here
  // using the svix library or a similar approach
  // For now, we'll just return true
  return true;
}

// Main webhook handler
export async function handleWebhook(request: Request): Promise<Response> {
  // Only allow POST requests
  if (request.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // Get the request body
    const payload = await request.text();
    
    // Verify the webhook signature
    if (!verifyWebhookSignature(payload, request.headers)) {
      return new Response('Invalid signature', { status: 401 });
    }

    // Parse the webhook event
    const event = JSON.parse(payload) as WebhookEvent;

    // Handle the webhook event
    return await handleClerkWebhook(event);
  } catch (error) {
    console.error('Error handling webhook:', error);
    return new Response('Internal server error', { status: 500 });
  }
}
