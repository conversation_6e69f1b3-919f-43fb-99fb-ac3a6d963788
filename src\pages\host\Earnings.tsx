import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';
import { AIAssistantButton } from '../../components/host/AIAssistantButton.jsx';
import {
  Calendar,
  Download,
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  AlertCircle
} from 'lucide-react';

interface Booking {
  id: string;
  venue_name: string;
  guest_name: string;
  booking_date: string;
  total_amount: number;
  host_payout: number;
  status: string;
  created_at: string;
}

interface EarningsSummary {
  totalEarnings: number;
  pendingPayouts: number;
  completedPayouts: number;
  thisMonth: number;
  lastMonth: number;
  monthlyChange: number;
}

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  type: 'earning' | 'payout';
  status: 'completed' | 'pending' | 'cancelled';
}

export default function Earnings() {
  const { user } = useUser();
  const [timeframe, setTimeframe] = useState('year');
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [earningsSummary, setEarningsSummary] = useState<EarningsSummary>({
    totalEarnings: 0,
    pendingPayouts: 0,
    completedPayouts: 0,
    thisMonth: 0,
    lastMonth: 0,
    monthlyChange: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchEarningsData();
    }
  }, [user]);

  const fetchEarningsData = async () => {
    try {
      setLoading(true);

      // Fetch host's bookings with earnings
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          total_amount,
          status,
          booking_date,
          created_at,
          venues!inner(name, host_id),
          user_profiles!inner(first_name, last_name)
        `)
        .eq('venues.host_id', user?.id)
        .in('status', ['confirmed', 'completed', 'cancelled']);

      if (bookingsError) throw bookingsError;

      // Calculate host payout (90% of total amount - 10% platform fee)
      const processedBookings: Booking[] = (bookingsData || []).map(booking => ({
        id: booking.id,
        venue_name: booking.venues?.name || 'Unknown Venue',
        guest_name: `${booking.user_profiles?.first_name || ''} ${booking.user_profiles?.last_name || ''}`.trim() || 'Unknown Guest',
        booking_date: booking.booking_date,
        total_amount: booking.total_amount,
        host_payout: booking.total_amount * 0.9, // 90% to host, 10% platform fee
        status: booking.status,
        created_at: booking.created_at
      }));

      setBookings(processedBookings);

      // Calculate earnings summary
      const now = new Date();
      const thisMonth = now.getMonth();
      const thisYear = now.getFullYear();
      const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
      const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

      const completedBookings = processedBookings.filter(b => b.status === 'completed');
      const pendingBookings = processedBookings.filter(b => b.status === 'confirmed');

      const totalEarnings = completedBookings.reduce((sum, b) => sum + b.host_payout, 0);
      const pendingPayouts = pendingBookings.reduce((sum, b) => sum + b.host_payout, 0);

      const thisMonthEarnings = completedBookings
        .filter(b => {
          const bookingDate = new Date(b.booking_date);
          return bookingDate.getMonth() === thisMonth && bookingDate.getFullYear() === thisYear;
        })
        .reduce((sum, b) => sum + b.host_payout, 0);

      const lastMonthEarnings = completedBookings
        .filter(b => {
          const bookingDate = new Date(b.booking_date);
          return bookingDate.getMonth() === lastMonth && bookingDate.getFullYear() === lastMonthYear;
        })
        .reduce((sum, b) => sum + b.host_payout, 0);

      const monthlyChange = lastMonthEarnings > 0
        ? ((thisMonthEarnings - lastMonthEarnings) / lastMonthEarnings) * 100
        : thisMonthEarnings > 0 ? 100 : 0;

      setEarningsSummary({
        totalEarnings,
        pendingPayouts,
        completedPayouts: totalEarnings,
        thisMonth: thisMonthEarnings,
        lastMonth: lastMonthEarnings,
        monthlyChange
      });

      // Create transactions from bookings
      const transactionList: Transaction[] = processedBookings.map(booking => ({
        id: booking.id,
        date: booking.booking_date,
        description: `${booking.status === 'completed' ? 'Payout' : 'Booking'} - ${booking.venue_name}`,
        amount: booking.host_payout,
        type: booking.status === 'completed' ? 'payout' : 'earning',
        status: booking.status === 'completed' ? 'completed' : 'pending'
      }));

      setTransactions(transactionList.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get transaction status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get transaction type badge color
  const getTypeBadgeClass = (type: string) => {
    switch (type) {
      case 'earning':
        return 'bg-blue-100 text-blue-800';
      case 'payout':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Generate monthly earnings for chart
  const generateMonthlyEarnings = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentYear = new Date().getFullYear();

    return months.map(month => {
      const monthIndex = months.indexOf(month);
      const monthlyTotal = bookings
        .filter(b => {
          const bookingDate = new Date(b.booking_date);
          return bookingDate.getMonth() === monthIndex &&
                 bookingDate.getFullYear() === currentYear &&
                 b.status === 'completed';
        })
        .reduce((sum, b) => sum + b.host_payout, 0);

      return { month, amount: monthlyTotal };
    });
  };

  const monthlyEarnings = generateMonthlyEarnings();
  const maxEarnings = Math.max(...monthlyEarnings.map(item => item.amount));
  const chartScale = maxEarnings > 0 ? maxEarnings : 1000;

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Earnings</h1>
          <p className="mt-1 text-gray-600">Track your earnings and payouts</p>
        </div>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-white rounded-lg shadow-sm p-6">
                <div className="h-16 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Earnings</h1>
          <p className="mt-1 text-gray-600">Track your earnings and payouts</p>
        </div>

        {/* No earnings message */}
        {earningsSummary.totalEarnings === 0 && !loading && (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center mb-8">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No earnings yet</h3>
            <p className="text-gray-600 mb-4">
              You haven't received any bookings yet. Once guests book your venues, your earnings will appear here.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">How earnings work:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• You receive 90% of each booking amount</li>
                <li>• HouseGoing keeps 10% as a platform fee</li>
                <li>• Payouts are processed after successful events</li>
                <li>• All amounts shown are in AUD</li>
              </ul>
            </div>
          </div>
        )}

        {/* Earnings summary */}
        {earningsSummary.totalEarnings > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                  <DollarSign className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Earnings</p>
                  <h3 className="text-xl font-bold text-gray-900">{formatCurrency(earningsSummary.totalEarnings)}</h3>
                </div>
              </div>
            </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <CreditCard className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Payouts</p>
              <h3 className="text-xl font-bold text-gray-900">{formatCurrency(earningsSummary.pendingPayouts)}</h3>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              {earningsSummary.monthlyChange >= 0 ? (
                <TrendingUp className="h-6 w-6" />
              ) : (
                <TrendingDown className="h-6 w-6" />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">This Month</p>
              <div className="flex items-center">
                <h3 className="text-xl font-bold text-gray-900">{formatCurrency(earningsSummary.thisMonth)}</h3>
                {earningsSummary.lastMonth > 0 && (
                  <span className={`ml-2 text-xs font-medium ${
                    earningsSummary.monthlyChange >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {earningsSummary.monthlyChange >= 0 ? '+' : ''}
                    {earningsSummary.monthlyChange.toFixed(1)}%
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Earnings chart */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-medium text-gray-900">Earnings Overview</h2>
          <div className="flex space-x-2">
            <button
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === 'year' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
              }`}
              onClick={() => setTimeframe('year')}
            >
              Year
            </button>
            <button
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === 'month' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
              }`}
              onClick={() => setTimeframe('month')}
            >
              Month
            </button>
          </div>
        </div>

        <div className="h-64">
          <div className="flex h-full items-end">
            {monthlyEarnings.map((item, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full mx-1 bg-purple-200 rounded-t-sm"
                  style={{
                    height: `${(item.amount / chartScale) * 100}%`,
                    minHeight: item.amount > 0 ? '4px' : '0'
                  }}
                ></div>
                <div className="mt-2 text-xs text-gray-600">{item.month}</div>
                {item.amount > 0 && (
                  <div className="text-xs font-medium text-gray-900">{formatCurrency(item.amount)}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Transactions */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Recent Transactions</h2>
          <button className="flex items-center text-sm text-purple-600 hover:text-purple-800">
            <Download className="h-4 w-4 mr-1" />
            Export
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.length > 0 ? transactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(transaction.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeBadgeClass(transaction.type)}`}>
                      {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(transaction.status)}`}>
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                    <span className={transaction.type === 'earning' ? 'text-green-600' : 'text-gray-900'}>
                      {transaction.type === 'earning' ? '+' : '-'}{formatCurrency(transaction.amount)}
                    </span>
                  </td>
                </tr>
              )) : (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
                    <p className="text-gray-600">
                      You don't have any transactions yet. Once guests book your venues, transactions will appear here.
                    </p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
      <AIAssistantButton context="earnings" position="bottom-right" />
    </>
  );
}
