import React, { useState } from 'react';
import { AIAssistantButton } from '../../components/host/AIAssistantButton.jsx';
import {
  Calendar,
  Download,
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard
} from 'lucide-react';

// Mock data for earnings
const mockEarningsSummary = {
  totalEarnings: 8465,
  pendingPayouts: 1750,
  completedPayouts: 6715,
  thisMonth: 2450,
  lastMonth: 2100,
  monthlyChange: 16.7, // percentage
};

// Mock data for monthly earnings
const mockMonthlyEarnings = [
  { month: 'Jan', amount: 0 },
  { month: 'Feb', amount: 0 },
  { month: 'Mar', amount: 0 },
  { month: 'Apr', amount: 1200 },
  { month: 'May', amount: 1500 },
  { month: 'Jun', amount: 1215 },
  { month: 'Jul', amount: 2100 },
  { month: 'Aug', amount: 2450 },
  { month: 'Sep', amount: 0 },
  { month: 'Oct', amount: 0 },
  { month: 'Nov', amount: 0 },
  { month: 'Dec', amount: 0 },
];

// Mock data for transactions
const mockTransactions = [
  {
    id: 1,
    date: '2023-08-10',
    description: 'Payout - Beachside Villa',
    amount: 1050,
    type: 'payout',
    status: 'completed'
  },
  {
    id: 2,
    date: '2023-08-05',
    description: 'Booking - Downtown Loft',
    amount: 840,
    type: 'earning',
    status: 'completed'
  },
  {
    id: 3,
    date: '2023-07-28',
    description: 'Payout - Mountain Cabin',
    amount: 1375,
    type: 'payout',
    status: 'completed'
  },
  {
    id: 4,
    date: '2023-07-25',
    description: 'Booking - Beachside Villa',
    amount: 1750,
    type: 'earning',
    status: 'pending'
  },
  {
    id: 5,
    date: '2023-07-20',
    description: 'Booking - Mountain Cabin',
    amount: 1375,
    type: 'earning',
    status: 'completed'
  }
];

export default function Earnings() {
  const [timeframe, setTimeframe] = useState('year');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get transaction status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get transaction type badge color
  const getTypeBadgeClass = (type: string) => {
    switch (type) {
      case 'earning':
        return 'bg-blue-100 text-blue-800';
      case 'payout':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate the max value for the chart
  const maxEarnings = Math.max(...mockMonthlyEarnings.map(item => item.amount));
  const chartScale = maxEarnings > 0 ? maxEarnings : 1000;

  return (
    <>
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Earnings</h1>
        <p className="mt-1 text-gray-600">Track your earnings and payouts</p>
      </div>

      {/* Earnings summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <DollarSign className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <h3 className="text-xl font-bold text-gray-900">{formatCurrency(mockEarningsSummary.totalEarnings)}</h3>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <CreditCard className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending Payouts</p>
              <h3 className="text-xl font-bold text-gray-900">{formatCurrency(mockEarningsSummary.pendingPayouts)}</h3>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              {mockEarningsSummary.monthlyChange >= 0 ? (
                <TrendingUp className="h-6 w-6" />
              ) : (
                <TrendingDown className="h-6 w-6" />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">This Month</p>
              <div className="flex items-center">
                <h3 className="text-xl font-bold text-gray-900">{formatCurrency(mockEarningsSummary.thisMonth)}</h3>
                <span className={`ml-2 text-xs font-medium ${
                  mockEarningsSummary.monthlyChange >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {mockEarningsSummary.monthlyChange >= 0 ? '+' : ''}
                  {mockEarningsSummary.monthlyChange}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Earnings chart */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-medium text-gray-900">Earnings Overview</h2>
          <div className="flex space-x-2">
            <button
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === 'year' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
              }`}
              onClick={() => setTimeframe('year')}
            >
              Year
            </button>
            <button
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === 'month' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
              }`}
              onClick={() => setTimeframe('month')}
            >
              Month
            </button>
          </div>
        </div>

        <div className="h-64">
          <div className="flex h-full items-end">
            {mockMonthlyEarnings.map((item, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full mx-1 bg-purple-200 rounded-t-sm"
                  style={{
                    height: `${(item.amount / chartScale) * 100}%`,
                    minHeight: item.amount > 0 ? '4px' : '0'
                  }}
                ></div>
                <div className="mt-2 text-xs text-gray-600">{item.month}</div>
                {item.amount > 0 && (
                  <div className="text-xs font-medium text-gray-900">{formatCurrency(item.amount)}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Transactions */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Recent Transactions</h2>
          <button className="flex items-center text-sm text-purple-600 hover:text-purple-800">
            <Download className="h-4 w-4 mr-1" />
            Export
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mockTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(transaction.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeBadgeClass(transaction.type)}`}>
                      {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(transaction.status)}`}>
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                    <span className={transaction.type === 'earning' ? 'text-green-600' : 'text-gray-900'}>
                      {transaction.type === 'earning' ? '+' : '-'}{formatCurrency(transaction.amount)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {mockTransactions.length === 0 && (
          <div className="px-6 py-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
            <p className="text-gray-600">
              You don't have any transactions yet
            </p>
          </div>
        )}
      </div>
    </div>
      <AIAssistantButton context="earnings" position="bottom-right" />
    </>
  );
}
