import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useUser } from '@clerk/clerk-react';
import { useSupabase } from '../../providers/SupabaseProvider';

export default function AuthDebug() {
  const navigate = useNavigate();
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const { userProfile, isHost, isAuthenticated, isLoading } = useSupabase();

  const debugInfo = {
    clerk: {
      isLoaded,
      isSignedIn,
      userId: user?.id,
      email: user?.primaryEmailAddress?.emailAddress,
      firstName: user?.firstName,
      lastName: user?.lastName,
    },
    supabase: {
      isLoading,
      isAuthenticated,
      isHost,
      userProfile,
    },
    localStorage: {
      registering_as_host: localStorage.getItem('registering_as_host'),
      user_type: localStorage.getItem('user_type'),
      auth_user_type: localStorage.getItem('auth_user_type'),
      auth_redirect_to: localStorage.getItem('auth_redirect_to'),
      auth_success: localStorage.getItem('auth_success'),
    },
    url: {
      pathname: window.location.pathname,
      search: window.location.search,
      hostname: window.location.hostname,
    }
  };

  const handleForceHostRedirect = () => {
    // Set host flags
    localStorage.setItem('registering_as_host', 'true');
    localStorage.setItem('user_type', 'host');
    localStorage.setItem('auth_user_type', 'host');
    localStorage.setItem('auth_success', 'true');
    
    // Clear processed flags
    if (user?.id) {
      localStorage.removeItem(`oauth_processed_${user.id}`);
    }
    
    // Navigate to host dashboard
    navigate('/host/dashboard');
  };

  const handleForceCustomerRedirect = () => {
    // Clear host flags
    localStorage.removeItem('registering_as_host');
    localStorage.removeItem('user_type');
    localStorage.removeItem('auth_user_type');
    localStorage.setItem('auth_success', 'true');
    
    // Clear processed flags
    if (user?.id) {
      localStorage.removeItem(`oauth_processed_${user.id}`);
    }
    
    // Navigate to customer account
    navigate('/my-account');
  };

  const handleClearAll = () => {
    // Clear all auth-related localStorage
    const keysToRemove = [
      'registering_as_host',
      'user_type', 
      'auth_user_type',
      'auth_redirect_to',
      'auth_success',
      'auth_success_time'
    ];
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Clear processed flags
    if (user?.id) {
      localStorage.removeItem(`oauth_processed_${user.id}`);
    }
    
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-semibold mb-4">Debug Information</h2>
              <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
            
            <div>
              <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <button
                  onClick={handleForceHostRedirect}
                  className="w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
                >
                  Force Host Dashboard Redirect
                </button>
                
                <button
                  onClick={handleForceCustomerRedirect}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                  Force Customer Account Redirect
                </button>
                
                <button
                  onClick={handleClearAll}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                >
                  Clear All & Reload
                </button>
                
                <button
                  onClick={() => navigate('/')}
                  className="w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
                >
                  Go to Homepage
                </button>
              </div>
              
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
                <h3 className="font-semibold text-yellow-800 mb-2">Instructions:</h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Use "Force Host Dashboard" if you're trying to access host features</li>
                  <li>• Use "Force Customer Account" for regular user features</li>
                  <li>• Use "Clear All" if authentication is completely stuck</li>
                  <li>• Check the debug info to see current authentication state</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
