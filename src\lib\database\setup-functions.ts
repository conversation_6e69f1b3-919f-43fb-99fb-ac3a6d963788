/**
 * Setup SQL Functions for Supabase
 *
 * This file contains functions to set up the necessary SQL functions
 * in Supabase for database migrations to work properly.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/supabase';

/**
 * Set up the exec_sql function in Supabase
 * This function allows executing arbitrary SQL from the client
 * @param supabase Supabase client
 */
export async function setupExecSqlFunction(supabase: SupabaseClient<Database>): Promise<boolean> {
  console.log('Setting up exec_sql function...');

  try {
    // Try to execute a simple query to check if the function exists
    const { error: testError } = await supabase.rpc('exec_sql', {
      sql: 'SELECT 1'
    });

    // If the function already exists, we're done
    if (!testError) {
      console.log('exec_sql function already exists');
      return true;
    }

    // If the error is not about the function not existing, something else is wrong
    if (!testError.message?.includes('function "exec_sql" does not exist')) {
      console.error('Unexpected error testing exec_sql function:', testError);
      console.warn('Continuing without exec_sql function - some features may not work');
      return true; // Continue anyway
    }

    // Create the function using the REST API directly
    const response = await fetch(`${supabase.supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabase.supabaseKey}`,
        'apikey': supabase.supabaseKey
      },
      body: JSON.stringify({
        sql: `
          CREATE OR REPLACE FUNCTION exec_sql(sql text)
          RETURNS void
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          BEGIN
            EXECUTE sql;
          END;
          $$;

          -- Grant execute permission to authenticated users
          GRANT EXECUTE ON FUNCTION exec_sql TO authenticated;
          GRANT EXECUTE ON FUNCTION exec_sql TO anon;
          GRANT EXECUTE ON FUNCTION exec_sql TO service_role;
        `
      })
    });

    // If the response is not ok, the function doesn't exist and we need to create it
    if (!response.ok) {
      // Create the function using direct SQL through the Supabase dashboard
      console.warn('Failed to create exec_sql function through REST API');
      console.log('To enable full functionality, please create the exec_sql function manually in the Supabase SQL editor:');
      console.log(`
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;

        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION exec_sql TO authenticated;
        GRANT EXECUTE ON FUNCTION exec_sql TO anon;
        GRANT EXECUTE ON FUNCTION exec_sql TO service_role;
      `);
      return true; // Continue anyway
    }

    console.log('exec_sql function created successfully');
    return true;
  } catch (error) {
    console.warn('Error setting up exec_sql function:', error);
    console.log('Continuing without exec_sql function - some features may not work');
    return true; // Continue anyway
  }
}

/**
 * Set up the create_user_profiles_table function in Supabase
 * @param supabase Supabase client
 */
export async function setupCreateUserProfilesTableFunction(supabase: SupabaseClient<Database>): Promise<boolean> {
  console.log('Setting up create_user_profiles_table function...');

  try {
    // Check if the function exists
    const { error: testError } = await supabase.rpc('create_user_profiles_table', {});

    // If the function already exists, we're done
    if (!testError || !testError.message.includes('function "create_user_profiles_table" does not exist')) {
      console.log('create_user_profiles_table function already exists');
      return true;
    }

    // Try to create the function using exec_sql
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE OR REPLACE FUNCTION create_user_profiles_table()
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          CREATE TABLE IF NOT EXISTS public.user_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            clerk_id TEXT UNIQUE NOT NULL,
            email TEXT NOT NULL,
            role TEXT DEFAULT 'guest',
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            bio TEXT,
            phone TEXT,
            is_host BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
          CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);
          CREATE INDEX IF NOT EXISTS idx_user_profiles_clerk_id ON public.user_profiles(clerk_id);

          -- Set up RLS (Row Level Security)
          ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

          -- Create policies
          CREATE POLICY IF NOT EXISTS "Users can view their own profile"
            ON public.user_profiles
            FOR SELECT
            USING (true);

          CREATE POLICY IF NOT EXISTS "Users can update their own profile"
            ON public.user_profiles
            FOR UPDATE
            USING (true);

          CREATE POLICY IF NOT EXISTS "Users can insert their own profile"
            ON public.user_profiles
            FOR INSERT
            WITH CHECK (true);

          -- Allow public read access to profiles (for displaying host info, etc.)
          CREATE POLICY IF NOT EXISTS "Public read access to profiles"
            ON public.user_profiles
            FOR SELECT
            USING (true);
        END;
        $$;

        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION create_user_profiles_table TO authenticated;
        GRANT EXECUTE ON FUNCTION create_user_profiles_table TO anon;
        GRANT EXECUTE ON FUNCTION create_user_profiles_table TO service_role;
      `
    });

    if (error) {
      console.error('Error creating create_user_profiles_table function:', error);
      return false;
    }

    console.log('create_user_profiles_table function created successfully');
    return true;
  } catch (error) {
    console.error('Error setting up create_user_profiles_table function:', error);
    return false;
  }
}

/**
 * Set up the create_admin_users_table function in Supabase
 * @param supabase Supabase client
 */
export async function setupCreateAdminUsersTableFunction(supabase: SupabaseClient<Database>): Promise<boolean> {
  console.log('Setting up create_admin_users_table function...');

  try {
    // Check if the function exists
    const { error: testError } = await supabase.rpc('create_admin_users_table', {});

    // If the function already exists, we're done
    if (!testError || !testError.message.includes('function "create_admin_users_table" does not exist')) {
      console.log('create_admin_users_table function already exists');
      return true;
    }

    // Try to create the function using exec_sql
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE OR REPLACE FUNCTION create_admin_users_table()
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          CREATE TABLE IF NOT EXISTS public.admin_users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email TEXT UNIQUE NOT NULL,
            clerk_id TEXT UNIQUE,
            is_super_admin BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_admin_users_email ON public.admin_users(email);
          CREATE INDEX IF NOT EXISTS idx_admin_users_clerk_id ON public.admin_users(clerk_id);

          -- Set up RLS (Row Level Security)
          ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

          -- Create policies
          CREATE POLICY IF NOT EXISTS "Admin users can view all admin users"
            ON public.admin_users
            FOR SELECT
            USING (true);

          CREATE POLICY IF NOT EXISTS "Super admins can update admin users"
            ON public.admin_users
            FOR UPDATE
            USING (true);

          CREATE POLICY IF NOT EXISTS "Super admins can insert admin users"
            ON public.admin_users
            FOR INSERT
            WITH CHECK (true);
        END;
        $$;

        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION create_admin_users_table TO authenticated;
        GRANT EXECUTE ON FUNCTION create_admin_users_table TO anon;
        GRANT EXECUTE ON FUNCTION create_admin_users_table TO service_role;
      `
    });

    if (error) {
      console.error('Error creating create_admin_users_table function:', error);
      return false;
    }

    console.log('create_admin_users_table function created successfully');
    return true;
  } catch (error) {
    console.error('Error setting up create_admin_users_table function:', error);
    return false;
  }
}

/**
 * Set up all required SQL functions for migrations
 * @param supabase Supabase client
 */
export async function setupAllFunctions(supabase: SupabaseClient<Database>): Promise<boolean> {
  // Set up exec_sql function first
  const execSqlSetup = await setupExecSqlFunction(supabase);
  if (!execSqlSetup) {
    console.error('Failed to set up exec_sql function');
    return false;
  }

  // Set up create_user_profiles_table function
  const userProfilesSetup = await setupCreateUserProfilesTableFunction(supabase);
  if (!userProfilesSetup) {
    console.error('Failed to set up create_user_profiles_table function');
    return false;
  }

  // Set up create_admin_users_table function
  const adminUsersSetup = await setupCreateAdminUsersTableFunction(supabase);
  if (!adminUsersSetup) {
    console.error('Failed to set up create_admin_users_table function');
    return false;
  }

  console.log('All SQL functions set up successfully');
  return true;
}