import React, { createContext, useContext } from 'react';
import { User, AuthContextType } from '../types/auth';
import { useNavigate } from 'react-router-dom';
import { useSupabase } from '../providers/SupabaseProvider';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { userProfile, isLoading, isAuthenticated, signOut } = useSupabase();
  const navigate = useNavigate();

  // Determine the effective user
  const user: User | null = (isAuthenticated && userProfile) ? {
    id: userProfile.id,
    email: userProfile.email || '',
    name: userProfile.email.split('@')[0] || '',
  } : null;

  // Authentication methods
  const login = async () => {
    navigate('/login');
  };

  const signup = async () => {
    navigate('/signup');
  };

  const logout = async () => {
    await signOut();
    navigate('/');
  };

  return (
    <AuthContext.Provider value={{ user, loading: isLoading, login, signup, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}