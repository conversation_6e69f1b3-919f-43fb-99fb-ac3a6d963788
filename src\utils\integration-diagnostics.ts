/**
 * Clerk-Supabase Integration Diagnostics
 * 
 * This utility provides comprehensive diagnostic information about the Clerk-Supabase integration
 * to help troubleshoot authentication and authorization issues.
 */

import { createClient } from '@supabase/supabase-js';

// Environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_URL : '');
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY : '');

interface TokenInfo {
  hasToken: boolean;
  tokenType: string | null;
  tokenLength: number | null;
  tokenPrefix: string | null;
  error: string | null;
}

interface SessionInfo {
  id: string;
  status: string;
  expireAt: string;
  lastActiveAt: string;
  hasGetToken: boolean;
  hasReload: boolean;
}

interface TestResult {
  status: 'pending' | 'success' | 'failed' | 'error';
  error: string | null;
  count?: number | null;
}

/**
 * Collects diagnostic information about the Clerk session
 */
export const getClerkDiagnostics = async (session: any) => {
  if (!session) {
    return {
      status: 'error',
      message: 'No session provided',
      session: null,
      sessionInfo: null,
      tokenInfo: null
    };
  }

  try {
    const sessionInfo: SessionInfo = {
      id: session.id || 'missing',
      status: session.status || 'unknown',
      expireAt: session.expireAt || 'unknown',
      lastActiveAt: session.lastActiveAt || 'unknown',
      hasGetToken: typeof session.getToken === 'function',
      hasReload: typeof session.reload === 'function',
    };

    let tokenInfo: TokenInfo = { 
      hasToken: false,
      tokenType: null,
      tokenLength: null,
      tokenPrefix: null,
      error: null
    };

    // Try to get a token
    if (sessionInfo.hasGetToken) {
      try {
        const token = await session.getToken({ template: "supabase" });
        tokenInfo = {
          hasToken: !!token,
          tokenType: typeof token,
          tokenLength: token ? token.length : null,
          tokenPrefix: token ? `${token.substring(0, 10)}...` : null,
          error: null
        };
      } catch (err) {
        tokenInfo.error = err instanceof Error ? err.message : String(err);
      }
    }

    return {
      status: 'success',
      message: 'Clerk diagnostics collected',
      session: session,
      sessionInfo,
      tokenInfo
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'Error collecting Clerk diagnostics',
      error: error instanceof Error ? error.message : String(error),
      session: null,
      sessionInfo: null,
      tokenInfo: null
    };
  }
};

/**
 * Tests Supabase connectivity using various methods
 */
export const testSupabaseConnectivity = async (session: any) => {
  const results: {
    anonClientTest: TestResult;
    authenticatedClientTest: TestResult;
    userProfilesQuery: TestResult & { count: number | null };
    directTokenTest: TestResult;
    setSessionTest: TestResult;
  } = {
    anonClientTest: { status: 'pending', error: null },
    authenticatedClientTest: { status: 'pending', error: null },
    userProfilesQuery: { status: 'pending', error: null, count: null },
    directTokenTest: { status: 'pending', error: null },
    setSessionTest: { status: 'pending', error: null }
  };

  try {
    // Test 1: Anonymous client basic connectivity
    try {
      const anonClient = createClient(supabaseUrl, supabaseKey);
      const { error } = await anonClient.from('user_profiles').select('count').limit(1);
      results.anonClientTest = { 
        status: error ? 'failed' : 'success',
        error: error ? error.message : null
      };
    } catch (err) {
      results.anonClientTest = { 
        status: 'error', 
        error: err instanceof Error ? err.message : String(err)
      };
    }

    // Only proceed with authenticated tests if we have a session
    if (!session || typeof session.getToken !== 'function') {
      return {
        status: 'partial',
        message: 'Only anonymous client test completed - no valid session provided',
        results
      };
    }

    // Test 2: Direct token fetch and manual authentication
    try {
      const token = await session.getToken({ template: "supabase" });
      const directClient = createClient(supabaseUrl, supabaseKey, {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      });
      const { error } = await directClient.from('user_profiles').select('count').limit(1);
      results.directTokenTest = { 
        status: error ? 'failed' : 'success',
        error: error ? error.message : null
      };
    } catch (err) {
      results.directTokenTest = { 
        status: 'error', 
        error: err instanceof Error ? err.message : String(err)
      };
    }

    // Test 3: Using setSession method
    try {
      const token = await session.getToken({ template: "supabase" });
      const sessionClient = createClient(supabaseUrl, supabaseKey);
      await sessionClient.auth.setSession({
        access_token: token,
        refresh_token: ''
      });
      const { error } = await sessionClient.from('user_profiles').select('count').limit(1);
      results.setSessionTest = { 
        status: error ? 'failed' : 'success',
        error: error ? error.message : null
      };
    } catch (err) {
      results.setSessionTest = { 
        status: 'error', 
        error: err instanceof Error ? err.message : String(err)
      };
    }

    // Test 4: User Profiles Query
    try {
      const token = await session.getToken({ template: "supabase" });
      const queryClient = createClient(supabaseUrl, supabaseKey, {
        global: {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      });
      
      const { data, error } = await queryClient
        .from('user_profiles')
        .select('count');
      
      results.userProfilesQuery = { 
        status: error ? 'failed' : 'success',
        error: error ? error.message : null,
        count: data ? data.length : null
      };
    } catch (err) {
      results.userProfilesQuery = { 
        status: 'error', 
        error: err instanceof Error ? err.message : String(err),
        count: null
      };
    }

    return {
      status: 'success',
      message: 'All Supabase connectivity tests completed',
      results
    };
  } catch (error) {
    return {
      status: 'error',
      message: 'Error during Supabase connectivity tests',
      error: error instanceof Error ? error.message : String(error),
      results
    };
  }
};

/**
 * Get environment diagnostic information
 */
export const getEnvironmentInfo = () => {
  const hasSupabaseUrl = !!supabaseUrl;
  const hasSupabaseKey = !!supabaseKey;
  const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : 'Not in browser';
  const hostname = typeof window !== 'undefined' ? window.location.hostname : 'Not in browser';
  const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
  const isDevelopment = isLocalhost || import.meta.env.DEV;
  
  return {
    environment: import.meta.env.MODE || 'unknown',
    isDevelopment,
    isProduction: !isDevelopment,
    isLocalhost,
    hostname,
    userAgent,
    supabaseConfigured: hasSupabaseUrl && hasSupabaseKey,
    hasSupabaseUrl,
    hasSupabaseKey,
    supabaseUrlPrefix: hasSupabaseUrl ? `${supabaseUrl.substring(0, 15)}...` : null,
  };
};

/**
 * Run comprehensive diagnostics
 * @param session The Clerk session object
 * @returns Complete diagnostic information
 */
export const runComprehensiveDiagnostics = async (session: any) => {
  console.log('🔍 Running comprehensive integration diagnostics...');
  
  const timestamp = new Date().toISOString();
  const environment = getEnvironmentInfo();
  const clerkDiagnostics = await getClerkDiagnostics(session);
  const supabaseTests = await testSupabaseConnectivity(session);
  
  const results = {
    timestamp,
    environment,
    clerk: clerkDiagnostics,
    supabase: supabaseTests,
  };
  
  console.log('📊 Diagnostics results:', results);
  return results;
};

/**
 * Helper utility to log diagnostic information to console
 * with proper formatting and clarity
 */
export const logDiagnostics = async (session: any) => {
  const results = await runComprehensiveDiagnostics(session);
  
  console.group('🔍 CLERK-SUPABASE INTEGRATION DIAGNOSTICS');
  console.log('Timestamp:', results.timestamp);
  
  console.group('Environment');
  console.table({
    environment: results.environment.environment,
    isDevelopment: results.environment.isDevelopment,
    isProduction: results.environment.isProduction,
    hostname: results.environment.hostname,
    supabaseConfigured: results.environment.supabaseConfigured
  });
  console.groupEnd();
  
  console.group('Clerk Session');
  if (results.clerk.status === 'success') {
    console.table(results.clerk.sessionInfo);
    console.group('Token Information');
    console.table(results.clerk.tokenInfo);
    console.groupEnd();
  } else {
    console.error('Error getting Clerk diagnostics:', results.clerk.message);
  }
  console.groupEnd();
  
  console.group('Supabase Connectivity Tests');
  if (results.supabase.status === 'success' || results.supabase.status === 'partial') {
    console.table({
      anonClient: results.supabase.results.anonClientTest.status,
      directToken: results.supabase.results.directTokenTest.status,
      setSession: results.supabase.results.setSessionTest.status,
      userProfiles: results.supabase.results.userProfilesQuery.status
    });
    
    // Log errors if any
    const errors = {
      anonClient: results.supabase.results.anonClientTest.error,
      directToken: results.supabase.results.directTokenTest.error,
      setSession: results.supabase.results.setSessionTest.error,
      userProfiles: results.supabase.results.userProfilesQuery.error
    };
    
    if (Object.values(errors).some(e => e !== null)) {
      console.group('Test Errors');
      console.table(errors);
      console.groupEnd();
    }
  } else {
    console.error('Error running Supabase tests:', results.supabase.message);
  }
  console.groupEnd();
  
  // Check for Google OAuth information
  const isGoogleOAuth = 
    localStorage.getItem('google_oauth_flow') === 'true' ||
    (session?.user?.primaryEmailAddress?.emailAddress || '').endsWith('@gmail.com') ||
    document.referrer.includes('google') ||
    document.referrer.includes('accounts.google.com');
  
  if (isGoogleOAuth) {
    console.log('==== Google OAuth User Detected ====');
    try {
      // Import the Google OAuth diagnostics dynamically to avoid circular references
      const { logGoogleOAuthDiagnostics } = await import('./google-oauth-debug');
      if (logGoogleOAuthDiagnostics) {
        logGoogleOAuthDiagnostics();
      } else {
        console.log('Google OAuth diagnostics not available');
      }
    } catch (err) {
      console.error('Error loading Google OAuth diagnostics:', err);
    }
  }
  
  console.groupEnd(); // End main group
  
  return results;
};
