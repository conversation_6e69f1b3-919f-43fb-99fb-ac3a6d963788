import { Search, Calendar, MessageSquare, PartyPopper } from "lucide-react";

const steps = [
  {
    icon: Search,
    title: "Find",
    description: "Search for the perfect venue",
  },
  {
    icon: Calendar,
    title: "Book",
    description: "Select your date and time",
  },
  {
    icon: MessageSquare,
    title: "Connect",
    description: "Chat with the host",
  },
  {
    icon: PartyPopper,
    title: "Enjoy",
    description: "Have an amazing celebration",
  },
];

export function HowItWorks() {
  return (
    <div className="py-16 bg-gray-50">
      <div className="container">
        <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-primary-light flex items-center justify-center mb-4">
                <step.icon className="w-8 h-8 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">{step.title}</h3>
              <p className="text-gray-600">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}