/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Define font-inter utility class */
.font-inter {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* Set Inter as default font for the entire app */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced button styles for better reactivity */
.btn-primary {
  @apply px-4 py-2 bg-purple-600 hover:bg-purple-700 active:bg-purple-800 text-white rounded-md font-medium transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-100 hover:bg-gray-200 active:bg-gray-300 text-gray-900 rounded-md font-medium transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md;
}

.btn-outline {
  @apply px-4 py-2 border border-gray-300 hover:border-gray-400 active:border-gray-500 text-gray-700 hover:text-gray-900 rounded-md font-medium transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md;
}

.btn-danger {
  @apply px-4 py-2 bg-red-600 hover:bg-red-700 active:bg-red-800 text-white rounded-md font-medium transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md;
}

/* Enhanced input styles */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 font-inter;
}

.input-field:focus {
  @apply shadow-md;
}

/* Enhanced card styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-200 hover:shadow-md;
}

.card-interactive {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-200 hover:shadow-lg hover:border-gray-300 cursor-pointer;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation improvements */
.loading-spinner {
  @apply animate-spin rounded-full border-t-2 border-b-2 border-purple-500;
}

/* Text selection styling */
::selection {
  background-color: rgba(147, 51, 234, 0.2);
  color: inherit;
}

/* Focus styles for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
}

/* Responsive text sizes */
.text-responsive-sm {
  @apply text-sm md:text-base;
}

.text-responsive-base {
  @apply text-base md:text-lg;
}

.text-responsive-lg {
  @apply text-lg md:text-xl;
}

.text-responsive-xl {
  @apply text-xl md:text-2xl;
}

/* Mobile-first responsive utilities */
.mobile-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.mobile-margin {
  @apply mx-4 sm:mx-6 lg:mx-8;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced hover effects */
.hover-lift {
  @apply transition-transform duration-200 hover:transform hover:-translate-y-1;
}

.hover-glow {
  @apply transition-shadow duration-200 hover:shadow-lg;
}

/* Better spacing utilities */
.space-y-responsive > * + * {
  @apply mt-4 md:mt-6;
}

.gap-responsive {
  @apply gap-4 md:gap-6 lg:gap-8;
}
