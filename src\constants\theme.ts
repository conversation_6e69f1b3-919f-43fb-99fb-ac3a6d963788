// Centralized theme configuration
export const theme = {
  colors: {
    primary: {
      main: '#7C3AED', // Purple-600
      light: '#A78BFA', // Purple-400
      dark: '#5B21B6', // Purple-800
    },
    secondary: {
      main: '#EC4899', // Pink-600
      light: '#F472B6', // Pink-400
      dark: '#BE185D', // Pink-800
    },
    gray: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280',
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827',
    }
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
  },
  gradients: {
    primary: 'linear-gradient(to right, #7C3AED, #EC4899)',
    hover: 'linear-gradient(to right, #6D28D9, #DB2777)',
  }
}