/**
 * IMPORTANT: This file is deprecated and should not be used directly.
 * Import from src/lib/supabase-client.ts instead to prevent multiple GoTrueClient instances.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';
import { supabase as centralizedClient, getSupabaseClient } from './supabase-client';

// Log a warning when this file is imported
console.warn('WARNING: src/lib/supabase.ts is deprecated. Import from src/lib/supabase-client.ts instead.');

// Re-export the centralized client
export const supabase = centralizedClient;

// Re-export the getSupabaseClient function from the centralized client
export function getSupabaseClient(): SupabaseClient<Database> {
  console.warn('WARNING: Using deprecated getSupabaseClient from src/lib/supabase.ts. Import from src/lib/supabase-client.ts instead.');
  return centralizedClient;
}

// User roles
export type UserRole = 'guest' | 'host' | 'admin';

// User profile interface
export interface UserProfile {
  id: string;
  clerk_id: string;
  email: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

/**
 * Get user profile from Supabase by Clerk ID
 */
export async function getUserProfile(clerkId: string): Promise<UserProfile | null> {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('clerk_id', clerkId)
    .single();

  if (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }

  return data as UserProfile;
}

/**
 * Create or update user profile in Supabase
 */
export async function upsertUserProfile(profile: Partial<UserProfile>): Promise<UserProfile | null> {
  // Ensure clerk_id is provided
  if (!profile.clerk_id) {
    console.error('clerk_id is required for upsertUserProfile');
    return null;
  }

  // Check if user already exists
  const existingUser = await getUserProfile(profile.clerk_id);

  if (existingUser) {
    // Update existing user
    const { data, error } = await supabase
      .from('user_profiles')
      .update({
        ...profile,
        updated_at: new Date().toISOString()
      })
      .eq('clerk_id', profile.clerk_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating user profile:', error);
      return null;
    }

    return data as UserProfile;
  } else {
    // Create new user
    const { data, error } = await supabase
      .from('user_profiles')
      .insert({
        ...profile,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      return null;
    }

    return data as UserProfile;
  }
}

/**
 * Set user role in Supabase
 */
export async function setUserRole(clerkId: string, email: string, role: UserRole): Promise<boolean> {
  const { data, error } = await supabase
    .from('user_profiles')
    .upsert({
      clerk_id: clerkId,
      email: email,
      role: role,
      updated_at: new Date().toISOString()
    }, {
      onConflict: 'clerk_id'
    });

  if (error) {
    console.error('Error setting user role:', error);
    return false;
  }

  return true;
}

/**
 * Check if user is a host
 */
export async function isUserHost(clerkId: string): Promise<boolean> {
  const profile = await getUserProfile(clerkId);
  return profile?.role === 'host';
}

/**
 * Sync Clerk user with Supabase
 */
export async function syncClerkUser(clerkId: string, email: string, role?: UserRole): Promise<UserProfile | null> {
  // Get existing profile if any
  const existingProfile = await getUserProfile(clerkId);

  // Determine role (keep existing role if not provided)
  const userRole = role || existingProfile?.role || 'guest';

  // Upsert user profile
  return upsertUserProfile({
    clerk_id: clerkId,
    email: email,
    role: userRole
  });
}