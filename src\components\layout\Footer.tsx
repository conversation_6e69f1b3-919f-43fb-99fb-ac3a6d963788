import React from 'react';
import { Link } from 'react-router-dom';
import { Instagram, Mail } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="text-xl font-bold text-purple-600 mb-2">HouseGoing</div>
            <p className="text-sm font-medium text-purple-600 mb-3">
              "The only house we going, is a party house"
            </p>
            <p className="text-gray-600 text-xs leading-relaxed mb-4">
              Australia's premier party venue rental platform.
            </p>

            {/* Social Links */}
            <div className="flex space-x-3">
              <a href="https://www.instagram.com/housegoing.com.au/" className="text-gray-500 hover:text-purple-600 transition-colors" target="_blank" rel="noopener noreferrer">
                <Instagram className="h-4 w-4" />
              </a>
              <a href="https://www.tiktok.com/@housegoing_au?lang=en-GB" className="text-gray-500 hover:text-purple-600 transition-colors" target="_blank" rel="noopener noreferrer">
                <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.321 5.562a5.122 5.122 0 0 1-.443-.258 5.65 5.65 0 0 1-1.686-1.722 5.474 5.474 0 0 1-.657-1.756h.003l-.001-.36c-.001-.041 0-.082 0-.124h-3.761v11.648a2.914 2.914 0 0 1-3.988 2.695A2.913 2.913 0 0 1 7.65 12.36c0-.286.042-.562.12-.824.394-1.317 1.58-2.278 3.018-2.278.267 0 .525.035.772.1V6.135a7.212 7.212 0 0 0-1.113-.088c-1.885 0-3.637.713-4.953 1.99a6.927 6.927 0 0 0-2.076 4.904c.001 1.163.286 2.257.788 3.219a7.062 7.062 0 0 0 5.114 3.585c.6.088 1.216.093 1.84.013 2.026-.26 3.755-1.396 4.825-3.057.771-1.196 1.22-2.612 1.22-4.135V8.254s1.425.344 2.292.302a5.82 5.82 0 0 0 1.597-.242V5.505a5.95 5.95 0 0 1-.773.057"></path>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-sm font-semibold mb-3 text-gray-900">Quick Links</h4>
            <ul className="space-y-1">
              <li><Link to="/find-venues" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Find Venues</Link></li>
              <li><Link to="/venue-guide" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Venue Guide</Link></li>
              <li><Link to="/how-it-works" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">How It Works</Link></li>
              <li><Link to="/nsw-party-planning" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">NSW Planning</Link></li>
              <li><Link to="/host/submit-property" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">List Your Venue</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-sm font-semibold mb-3 text-gray-900">Support</h4>
            <ul className="space-y-1">
              <li><Link to="/help" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Help Center</Link></li>
              <li><Link to="/about-housegoing" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">About HouseGoing</Link></li>
              <li><Link to="/contact" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Contact Us</Link></li>
              <li><Link to="/safety" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Safety Information</Link></li>
              <li><Link to="/terms" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Terms & Conditions</Link></li>
              <li><Link to="/privacy" className="text-xs text-gray-600 hover:text-purple-600 transition-colors">Privacy Policy</Link></li>
            </ul>
          </div>

          {/* Stay Connected */}
          <div>
            <h4 className="text-sm font-semibold mb-3 text-gray-900">Stay Connected</h4>
            <div className="flex items-center text-gray-600">
              <Mail className="h-3 w-3 mr-1" />
              <a href="mailto:<EMAIL>" className="text-xs hover:text-purple-600 transition-colors">
                <EMAIL>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 mt-6 pt-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-600 text-xs">
              © 2025 HouseGoing Pty Ltd. All rights reserved. ABN **************
            </div>
            <div className="flex space-x-4 mt-2 md:mt-0">
              <Link to="/terms" className="text-gray-600 hover:text-purple-600 text-xs transition-colors">Terms</Link>
              <Link to="/privacy" className="text-gray-600 hover:text-purple-600 text-xs transition-colors">Privacy</Link>
              <Link to="/cookies" className="text-gray-600 hover:text-purple-600 text-xs transition-colors">Cookies</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
