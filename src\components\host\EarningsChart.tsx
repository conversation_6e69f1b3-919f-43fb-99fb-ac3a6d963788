import React from 'react';
import { useEarnings } from '../../hooks/useEarnings';

export default function EarningsChart() {
  const { earnings, isLoading } = useEarnings();

  if (isLoading) {
    return <div>Loading earnings data...</div>;
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-md">
      <h2 className="text-xl font-semibold mb-4">Earnings Overview</h2>
      <div className="h-64 flex items-end justify-between space-x-2">
        {earnings?.map((month) => (
          <div key={month.name} className="flex-1">
            <div 
              className="bg-purple-600 rounded-t-lg" 
              style={{ height: `${(month.amount / 1000) * 100}%` }}
            />
            <div className="text-xs text-gray-600 mt-2 text-center">
              {month.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}