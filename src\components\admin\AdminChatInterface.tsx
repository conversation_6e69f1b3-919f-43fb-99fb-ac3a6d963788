import React, { useState, useEffect, useRef } from 'react';
import { Send, AlertCircle, CheckCircle, User, Bot, Edit, Save, X, MessageSquare, Clock, ThumbsUp, ThumbsDown } from 'lucide-react';
import { ChatSession, getChatSessions, getChatSession, adminTakeoverChat, submitChatFeedback } from '../../lib/admin/chat-storage';
import { useAuth } from '../../context/AuthContext';
import { AdminUser, getAdminDetails } from '../../lib/admin/auth';

interface AdminChatInterfaceProps {
  onClose?: () => void;
}

export default function AdminChatInterface({ onClose }: AdminChatInterfaceProps) {
  const { user } = useAuth();
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [activeSessions, setActiveSessions] = useState<ChatSession[]>([]);
  const [closedSessions, setClosedSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);
  const [adminMessage, setAdminMessage] = useState('');
  const [isInTakeoverMode, setIsInTakeoverMode] = useState(false);
  const [isEditingMessage, setIsEditingMessage] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');
  const [activeTab, setActiveTab] = useState<'active' | 'closed'>('active');
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
  const [feedback, setFeedback] = useState({
    rating: 3,
    text: '',
    categories: [] as string[],
    improvements: {
      tone: '',
      length: '',
      accuracy: '',
      helpfulness: ''
    }
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Load admin details
  useEffect(() => {
    const loadAdminDetails = async () => {
      if (user) {
        const details = await getAdminDetails(user.id);
        setAdminUser(details);
      }
    };
    
    loadAdminDetails();
  }, [user]);
  
  // Load chat sessions
  useEffect(() => {
    const loadSessions = async () => {
      const { sessions: activeSessions } = await getChatSessions('active');
      const { sessions: interventionSessions } = await getChatSessions('admin_intervention');
      const { sessions: closedSessions } = await getChatSessions('closed', 1, 50);
      
      setActiveSessions([...activeSessions, ...interventionSessions]);
      setClosedSessions(closedSessions);
    };
    
    loadSessions();
    
    // Refresh active sessions every 30 seconds
    const interval = setInterval(loadSessions, 30000);
    return () => clearInterval(interval);
  }, []);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [selectedSession?.messages]);
  
  // Handle session selection
  const handleSelectSession = async (sessionId: string) => {
    const session = await getChatSession(sessionId);
    setSelectedSession(session);
    setIsInTakeoverMode(false);
    setIsEditingMessage(null);
    setFeedback({
      rating: 3,
      text: '',
      categories: [],
      improvements: {
        tone: '',
        length: '',
        accuracy: '',
        helpfulness: ''
      }
    });
  };
  
  // Handle admin takeover
  const handleTakeover = async () => {
    if (!selectedSession || !adminUser) return;
    
    const success = await adminTakeoverChat(selectedSession.id, adminUser.id);
    if (success) {
      setIsInTakeoverMode(true);
      
      // Refresh the session
      const updatedSession = await getChatSession(selectedSession.id);
      setSelectedSession(updatedSession);
    }
  };
  
  // Handle sending admin message
  const handleSendAdminMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!adminMessage.trim() || !selectedSession || !adminUser) return;
    
    // In a real implementation, this would send the message to the user
    // and update the database
    console.log('Admin message:', adminMessage);
    
    // For now, just update the local state
    const newMessage = {
      id: `admin-${Date.now()}`,
      role: 'ai',
      content: adminMessage,
      timestamp: new Date()
    };
    
    setSelectedSession({
      ...selectedSession,
      messages: [...selectedSession.messages, newMessage]
    });
    
    setAdminMessage('');
  };
  
  // Handle editing a message
  const handleEditMessage = (messageId: string, content: string) => {
    setIsEditingMessage(messageId);
    setEditedContent(content);
  };
  
  // Handle saving edited message
  const handleSaveEdit = async () => {
    if (!selectedSession || !isEditingMessage) return;
    
    // In a real implementation, this would update the message in the database
    // For now, just update the local state
    const updatedMessages = selectedSession.messages.map(msg => 
      msg.id === isEditingMessage ? { ...msg, content: editedContent } : msg
    );
    
    setSelectedSession({
      ...selectedSession,
      messages: updatedMessages
    });
    
    setIsEditingMessage(null);
    setEditedContent('');
  };
  
  // Handle submitting feedback
  const handleSubmitFeedback = async () => {
    if (!selectedSession || !adminUser) return;
    
    setIsSubmittingFeedback(true);
    
    const success = await submitChatFeedback(
      selectedSession.id,
      adminUser.id,
      feedback.rating as 1 | 2 | 3 | 4 | 5,
      feedback.text,
      feedback.categories,
      feedback.improvements
    );
    
    if (success) {
      // Refresh the session
      const updatedSession = await getChatSession(selectedSession.id);
      setSelectedSession(updatedSession);
    }
    
    setIsSubmittingFeedback(false);
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  if (!adminUser) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-purple-600 text-white p-4 flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">Admin Chat Interface</h2>
          <p className="text-sm text-purple-200">Logged in as {adminUser.name} ({adminUser.role})</p>
        </div>
        {onClose && (
          <button 
            onClick={onClose}
            className="text-white hover:text-purple-200 p-1 rounded"
          >
            <X size={20} />
          </button>
        )}
      </div>
      
      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sessions list */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              className={`flex-1 py-3 text-sm font-medium ${activeTab === 'active' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('active')}
            >
              Active Sessions ({activeSessions.length})
            </button>
            <button
              className={`flex-1 py-3 text-sm font-medium ${activeTab === 'closed' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('closed')}
            >
              Closed Sessions
            </button>
          </div>
          
          {/* Sessions */}
          <div className="flex-1 overflow-y-auto">
            {activeTab === 'active' ? (
              activeSessions.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  No active sessions
                </div>
              ) : (
                activeSessions.map(session => (
                  <button
                    key={session.id}
                    className={`w-full text-left px-4 py-3 border-b border-gray-200 hover:bg-gray-50 flex items-start ${
                      selectedSession?.id === session.id ? 'bg-purple-50' : ''
                    }`}
                    onClick={() => handleSelectSession(session.id)}
                  >
                    <div className="flex-shrink-0 mr-3">
                      {session.status === 'admin_intervention' ? (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      ) : (
                        <MessageSquare className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {session.userEmail || 'Anonymous User'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTimestamp(session.startTime)}
                        </p>
                      </div>
                      <p className="text-xs text-gray-500 truncate">
                        {session.messages.length} messages
                      </p>
                      {session.status === 'admin_intervention' && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                          Needs attention
                        </span>
                      )}
                    </div>
                  </button>
                ))
              )
            ) : (
              closedSessions.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  No closed sessions
                </div>
              ) : (
                closedSessions.map(session => (
                  <button
                    key={session.id}
                    className={`w-full text-left px-4 py-3 border-b border-gray-200 hover:bg-gray-50 flex items-start ${
                      selectedSession?.id === session.id ? 'bg-purple-50' : ''
                    }`}
                    onClick={() => handleSelectSession(session.id)}
                  >
                    <div className="flex-shrink-0 mr-3">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {session.userEmail || 'Anonymous User'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTimestamp(session.startTime)}
                        </p>
                      </div>
                      <p className="text-xs text-gray-500 truncate">
                        {session.messages.length} messages • {formatTimestamp(session.endTime!)}
                      </p>
                      {session.feedback && (
                        <div className="flex items-center mt-1">
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map(star => (
                              <span key={star} className={`text-xs ${star <= session.feedback!.rating ? 'text-yellow-500' : 'text-gray-300'}`}>★</span>
                            ))}
                          </div>
                          <span className="text-xs text-gray-500 ml-1">
                            {session.feedback.categories.join(', ')}
                          </span>
                        </div>
                      )}
                    </div>
                  </button>
                ))
              )
            )}
          </div>
        </div>
        
        {/* Chat view */}
        <div className="w-2/3 flex flex-col">
          {selectedSession ? (
            <>
              {/* Chat header */}
              <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                <div>
                  <h3 className="font-medium text-gray-900">
                    {selectedSession.userEmail || 'Anonymous User'}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Started {selectedSession.startTime.toLocaleString()} • 
                    {selectedSession.status === 'closed' 
                      ? ` Ended ${selectedSession.endTime?.toLocaleString()}` 
                      : ' Active'}
                  </p>
                </div>
                {selectedSession.status === 'active' && !isInTakeoverMode && (
                  <button
                    onClick={handleTakeover}
                    className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                  >
                    Take Over Chat
                  </button>
                )}
              </div>
              
              {/* Messages */}
              <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                <div className="space-y-4">
                  {selectedSession.messages.map((msg) => (
                    <div key={msg.id} className="relative group">
                      <div
                        className={`flex ${msg.role === 'human' ? 'justify-start' : 'justify-end'}`}
                      >
                        <div className="flex items-start max-w-[80%]">
                          <div className="flex-shrink-0 mr-2">
                            {msg.role === 'human' ? (
                              <User className="h-5 w-5 text-gray-500" />
                            ) : (
                              <Bot className="h-5 w-5 text-purple-500" />
                            )}
                          </div>
                          <div
                            className={`px-4 py-2 rounded-lg ${
                              msg.role === 'human'
                                ? 'bg-white text-gray-800 border border-gray-200'
                                : 'bg-purple-600 text-white'
                            }`}
                          >
                            {isEditingMessage === msg.id ? (
                              <div>
                                <textarea
                                  value={editedContent}
                                  onChange={(e) => setEditedContent(e.target.value)}
                                  className="w-full p-2 text-gray-800 border border-gray-300 rounded"
                                  rows={3}
                                />
                                <div className="flex justify-end mt-2 space-x-2">
                                  <button
                                    onClick={() => setIsEditingMessage(null)}
                                    className="px-2 py-1 text-xs text-gray-600 hover:text-gray-800"
                                  >
                                    Cancel
                                  </button>
                                  <button
                                    onClick={handleSaveEdit}
                                    className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                                  >
                                    Save
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                                <p className={`text-xs mt-1 ${msg.role === 'human' ? 'text-gray-500' : 'text-purple-200'}`}>
                                  {formatTimestamp(msg.timestamp)}
                                </p>
                              </>
                            )}
                          </div>
                        </div>
                        
                        {/* Edit button (only for AI messages) */}
                        {msg.role === 'ai' && !isEditingMessage && (
                          <button
                            onClick={() => handleEditMessage(msg.id, msg.content)}
                            className="absolute right-0 top-0 p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </div>
              
              {/* Admin input or feedback */}
              {selectedSession.status === 'active' && isInTakeoverMode ? (
                <form onSubmit={handleSendAdminMessage} className="p-4 border-t border-gray-200 bg-white">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={adminMessage}
                      onChange={(e) => setAdminMessage(e.target.value)}
                      placeholder="Type your message as AI assistant..."
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                    <button
                      type="submit"
                      className="px-4 py-2 bg-purple-600 text-white rounded-r-lg hover:bg-purple-700 transition-colors disabled:bg-purple-400"
                      disabled={!adminMessage.trim()}
                    >
                      <Send className="h-5 w-5" />
                    </button>
                  </div>
                </form>
              ) : selectedSession.status === 'closed' && !selectedSession.feedback ? (
                <div className="p-4 border-t border-gray-200 bg-white">
                  <h4 className="font-medium text-gray-900 mb-2">Provide Feedback</h4>
                  
                  {/* Rating */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Rating</label>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map(rating => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => setFeedback(prev => ({ ...prev, rating }))}
                          className={`p-1 rounded-full ${feedback.rating >= rating ? 'text-yellow-500' : 'text-gray-300'}`}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                          </svg>
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  {/* Categories */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Categories</label>
                    <div className="flex flex-wrap gap-2">
                      {['Accurate', 'Helpful', 'Too Long', 'Too Short', 'Too Technical', 'Too Casual'].map(category => (
                        <button
                          key={category}
                          type="button"
                          onClick={() => {
                            setFeedback(prev => {
                              const categories = prev.categories.includes(category)
                                ? prev.categories.filter(c => c !== category)
                                : [...prev.categories, category];
                              return { ...prev, categories };
                            });
                          }}
                          className={`px-2 py-1 text-xs rounded-full ${
                            feedback.categories.includes(category)
                              ? 'bg-purple-100 text-purple-800 border border-purple-300'
                              : 'bg-gray-100 text-gray-800 border border-gray-200'
                          }`}
                        >
                          {category}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  {/* Improvements */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Improvement Notes</label>
                    <div className="space-y-2">
                      <div>
                        <label className="block text-xs text-gray-600">Tone</label>
                        <input
                          type="text"
                          value={feedback.improvements.tone || ''}
                          onChange={(e) => setFeedback(prev => ({
                            ...prev,
                            improvements: { ...prev.improvements, tone: e.target.value }
                          }))}
                          placeholder="e.g., More professional, less technical"
                          className="w-full px-3 py-1 text-sm border border-gray-300 rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600">Length</label>
                        <input
                          type="text"
                          value={feedback.improvements.length || ''}
                          onChange={(e) => setFeedback(prev => ({
                            ...prev,
                            improvements: { ...prev.improvements, length: e.target.value }
                          }))}
                          placeholder="e.g., More concise, provide more details"
                          className="w-full px-3 py-1 text-sm border border-gray-300 rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600">Accuracy</label>
                        <input
                          type="text"
                          value={feedback.improvements.accuracy || ''}
                          onChange={(e) => setFeedback(prev => ({
                            ...prev,
                            improvements: { ...prev.improvements, accuracy: e.target.value }
                          }))}
                          placeholder="e.g., Incorrect information about pricing"
                          className="w-full px-3 py-1 text-sm border border-gray-300 rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600">Helpfulness</label>
                        <input
                          type="text"
                          value={feedback.improvements.helpfulness || ''}
                          onChange={(e) => setFeedback(prev => ({
                            ...prev,
                            improvements: { ...prev.improvements, helpfulness: e.target.value }
                          }))}
                          placeholder="e.g., Should have asked clarifying questions"
                          className="w-full px-3 py-1 text-sm border border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Feedback text */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Additional Comments</label>
                    <textarea
                      value={feedback.text}
                      onChange={(e) => setFeedback(prev => ({ ...prev, text: e.target.value }))}
                      placeholder="Any other feedback about this conversation..."
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded"
                      rows={3}
                    />
                  </div>
                  
                  <button
                    type="button"
                    onClick={handleSubmitFeedback}
                    disabled={isSubmittingFeedback}
                    className="w-full py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors disabled:bg-purple-400"
                  >
                    {isSubmittingFeedback ? 'Submitting...' : 'Submit Feedback'}
                  </button>
                </div>
              ) : selectedSession.feedback ? (
                <div className="p-4 border-t border-gray-200 bg-white">
                  <h4 className="font-medium text-gray-900 mb-2">Feedback</h4>
                  <div className="bg-gray-50 p-3 rounded border border-gray-200">
                    <div className="flex items-center mb-2">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map(star => (
                          <span key={star} className={`text-lg ${star <= selectedSession.feedback!.rating ? 'text-yellow-500' : 'text-gray-300'}`}>★</span>
                        ))}
                      </div>
                      <span className="text-sm text-gray-500 ml-2">
                        by {selectedSession.feedback.adminId === adminUser.id ? 'you' : 'another admin'}
                      </span>
                    </div>
                    
                    {selectedSession.feedback.categories.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {selectedSession.feedback.categories.map(category => (
                          <span key={category} className="px-2 py-0.5 text-xs bg-purple-100 text-purple-800 rounded-full">
                            {category}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {Object.entries(selectedSession.feedback.improvements).filter(([_, value]) => value).length > 0 && (
                      <div className="mb-2 space-y-1">
                        {Object.entries(selectedSession.feedback.improvements)
                          .filter(([_, value]) => value)
                          .map(([key, value]) => (
                            <div key={key} className="flex items-start">
                              <span className="text-xs font-medium text-gray-700 mr-1 capitalize">{key}:</span>
                              <span className="text-xs text-gray-600">{value}</span>
                            </div>
                          ))}
                      </div>
                    )}
                    
                    {selectedSession.feedback.feedbackText && (
                      <p className="text-sm text-gray-700">{selectedSession.feedback.feedbackText}</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="p-4 border-t border-gray-200 bg-gray-50 text-center text-gray-500">
                  {selectedSession.status === 'active' ? (
                    <p>Take over the chat to respond as the AI assistant</p>
                  ) : (
                    <p>This chat session has ended</p>
                  )}
                </div>
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              Select a chat session to view
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
