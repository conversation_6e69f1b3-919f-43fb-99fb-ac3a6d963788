/**
 * API route for the Sales Assistant
 */

// This is a mock implementation since we don't have the actual AI model running in the browser
// In production, this would connect to a backend service

// Store sessions in memory (in production, use a database)
const sessions = new Map();

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, sessionId } = req.body;

    // Validate message
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get or create session
    let session;
    if (sessionId && sessions.has(sessionId)) {
      session = sessions.get(sessionId);
    } else {
      // Create a new session
      session = {
        id: Math.random().toString(36).substring(2, 15),
        history: []
      };
      sessions.set(session.id, session);
    }

    // Add message to history
    session.history.push({ role: 'user', content: message });

    // Generate a mock response based on the message
    let response = '';
    
    if (message.toLowerCase().includes('venue') || message.toLowerCase().includes('location')) {
      response = "I'd be happy to help you find the perfect venue! Could you tell me more about your event? What type of event are you planning, how many guests will you have, and do you have a specific location in mind?";
    } else if (message.toLowerCase().includes('price') || message.toLowerCase().includes('cost')) {
      response = "Venues on HouseGoing are priced by the hour, typically ranging from $100-$500 per hour depending on the size, location, and amenities. If you tell me more about your event, I can help find options that fit your budget.";
    } else if (message.toLowerCase().includes('party') || message.toLowerCase().includes('birthday')) {
      response = "Birthday parties are one of our most popular event types! We have many venues perfect for celebrations. How many guests are you expecting, and what area are you looking in?";
    } else if (message.toLowerCase().includes('book') || message.toLowerCase().includes('reservation')) {
      response = "Booking a venue on HouseGoing is easy! Once you find a venue you like, you can check availability on the calendar, select your date and time, and submit a booking request. The host will respond within 24 hours.";
    } else {
      response = "Thanks for your message! I'm Alex, your HouseGoing assistant. I can help you find the perfect venue for your event, answer questions about pricing, booking, or venue features. What kind of event are you planning?";
    }

    // Add response to history
    session.history.push({ role: 'assistant', content: response });

    // Return response
    return res.status(200).json({
      response,
      sessionId: session.id
    });
  } catch (error) {
    console.error('Error processing sales assistant message:', error);
    return res.status(500).json({ 
      error: 'An error occurred while processing your message',
      details: error.message
    });
  }
}
