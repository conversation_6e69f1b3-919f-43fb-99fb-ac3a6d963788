import React, { useEffect, useState } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';

export default function ClerkDebug() {
  const { isSignedIn, isLoaded, getToken } = useAuth();
  const { user } = useUser();
  const [token, setToken] = useState<string | null>(null);
  const [tokenError, setTokenError] = useState<string | null>(null);

  useEffect(() => {
    if (isSignedIn && isLoaded) {
      // Try to get the Supabase token
      getToken({ template: 'supabase' })
        .then((token) => {
          if (token) {
            setToken(token.substring(0, 50) + '...');
            setTokenError(null);
          } else {
            setTokenError('No token returned - JWT template may not be configured');
          }
        })
        .catch((error) => {
          setTokenError(`Error getting token: ${error.message}`);
        });
    }
  }, [isSignedIn, isLoaded, getToken]);

  if (!isLoaded) {
    return <div className="p-4 bg-yellow-100 rounded">Loading Clerk...</div>;
  }

  return (
    <div className="fixed bottom-4 right-4 p-4 bg-white border rounded-lg shadow-lg max-w-md">
      <h3 className="font-bold mb-2">🔧 Clerk Debug Info</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Signed In:</strong> {isSignedIn ? '✅ Yes' : '❌ No'}
        </div>
        
        {isSignedIn && user && (
          <>
            <div>
              <strong>User Email:</strong> {user.primaryEmailAddress?.emailAddress}
            </div>
            <div>
              <strong>User ID:</strong> {user.id}
            </div>
          </>
        )}
        
        <div>
          <strong>Supabase Token:</strong> 
          {token ? (
            <span className="text-green-600"> ✅ Available</span>
          ) : tokenError ? (
            <span className="text-red-600"> ❌ {tokenError}</span>
          ) : (
            <span className="text-gray-500"> Not checked</span>
          )}
        </div>
        
        {tokenError && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
            <strong>Fix:</strong> Configure JWT template named 'supabase' in Clerk Dashboard
          </div>
        )}
      </div>
    </div>
  );
}
