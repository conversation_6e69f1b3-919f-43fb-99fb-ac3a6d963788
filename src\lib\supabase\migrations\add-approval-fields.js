/**
 * Migration script to add approval fields to venues table
 */
import { supabase } from '../../supabase-client';

export async function addApprovalFieldsToVenues() {
  console.log('Adding approval fields to venues table...');
  
  try {
    // Add approval_status field
    const { error: statusError } = await supabase.rpc('execute_sql', {
      sql: `
        ALTER TABLE IF EXISTS venues
        ADD COLUMN IF NOT EXISTS approval_status VARCHAR(20) DEFAULT 'pending' NOT NULL,
        ADD COLUMN IF NOT EXISTS is_published BOOLEAN DEFAULT false NOT NULL,
        ADD COLUMN IF NOT EXISTS admin_notes TEXT,
        ADD COLUMN IF NOT EXISTS approved_by TEXT,
        ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS inspection_scheduled BOOLEAN DEFAULT false,
        ADD COLUMN IF NOT EXISTS inspection_date TIMESTAMP WITH TIME ZONE;
        
        -- Add constraint to ensure valid approval status
        ALTER TABLE IF EXISTS venues
        ADD CONSTRAINT valid_approval_status 
        CHECK (approval_status IN ('pending', 'approved', 'rejected', 'revision_requested'));
      `
    });
    
    if (statusError) {
      console.error('Error adding approval fields to venues table:', statusError);
      return false;
    }
    
    console.log('Successfully added approval fields to venues table');
    return true;
  } catch (error) {
    console.error('Exception adding approval fields to venues table:', error);
    return false;
  }
}

// Run the migration if this file is executed directly
if (import.meta.url === import.meta.main) {
  addApprovalFieldsToVenues()
    .then(success => {
      console.log('Migration completed:', success ? 'SUCCESS' : 'FAILED');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Migration failed with exception:', error);
      process.exit(1);
    });
}
