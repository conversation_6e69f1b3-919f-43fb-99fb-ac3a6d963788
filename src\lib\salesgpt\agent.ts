import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { HumanMessage, AIMessage, SystemMessage } from "@langchain/core/messages";
import { 
  SALES_AGENT_SYSTEM_PROMPT, 
  SALES_AGENT_TOOLS_PROMPT,
  SALES_CONVERSATION_STAGES,
  SALES_AGENT_INCEPTION_PROMPT,
  MOCK_PROPERTIES
} from "./config";

// Define the conversation stage type
export type ConversationStage = {
  name: string;
  description: string;
};

// Define the message type
export type Message = {
  id: string;
  role: "human" | "ai";
  content: string;
  timestamp: Date;
};

// Define the SalesGPT agent class
export class SalesGPTAgent {
  private model: ChatOpenAI;
  private conversationHistory: (HumanMessage | AIMessage | SystemMessage)[] = [];
  private currentStage: ConversationStage;
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.model = new ChatOpenAI({
      openAIApiKey: apiKey,
      modelName: "gpt-4o",
      temperature: 0.7,
    });
    
    // Initialize with system prompts
    this.conversationHistory.push(new SystemMessage(SALES_AGENT_SYSTEM_PROMPT));
    this.conversationHistory.push(new SystemMessage(SALES_AGENT_TOOLS_PROMPT));
    
    // Set initial conversation stage
    this.currentStage = SALES_CONVERSATION_STAGES[0];
    
    // Add stage information to the conversation
    const stagePrompt = `Current conversation stage: ${this.currentStage.name} - ${this.currentStage.description}`;
    this.conversationHistory.push(new SystemMessage(stagePrompt));
    
    // Add inception prompt
    this.conversationHistory.push(new AIMessage(SALES_AGENT_INCEPTION_PROMPT));
  }

  // Method to get the current conversation stage
  public getCurrentStage(): ConversationStage {
    return this.currentStage;
  }

  // Method to update the conversation stage
  private updateStage(stageName: string): void {
    const newStage = SALES_CONVERSATION_STAGES.find(stage => stage.name === stageName);
    if (newStage) {
      this.currentStage = newStage;
      const stagePrompt = `Current conversation stage: ${this.currentStage.name} - ${this.currentStage.description}`;
      this.conversationHistory.push(new SystemMessage(stagePrompt));
    }
  }

  // Method to determine the next conversation stage based on the conversation history
  private async determineNextStage(): Promise<string> {
    const stageAnalysisPrompt = `
    Based on the conversation so far, what should be the next conversation stage?
    Current stage: ${this.currentStage.name}
    
    Available stages:
    ${SALES_CONVERSATION_STAGES.map(stage => `- ${stage.name}: ${stage.description}`).join('\n')}
    
    Respond with just the name of the next appropriate stage.
    `;
    
    const stageAnalysisMessages = [
      new SystemMessage("You are a conversation stage analyzer. Your job is to determine the appropriate next stage in a sales conversation."),
      ...this.conversationHistory,
      new HumanMessage(stageAnalysisPrompt)
    ];
    
    const response = await this.model.invoke(stageAnalysisMessages);
    return response.content.toString().trim();
  }

  // Method to process user input and generate a response
  public async processMessage(userMessage: string): Promise<string> {
    // Add user message to conversation history
    this.conversationHistory.push(new HumanMessage(userMessage));
    
    // Generate AI response
    const response = await this.model.invoke(this.conversationHistory);
    
    // Add AI response to conversation history
    this.conversationHistory.push(response);
    
    // Determine and update the next conversation stage
    const nextStage = await this.determineNextStage();
    if (nextStage !== this.currentStage.name) {
      this.updateStage(nextStage);
    }
    
    return response.content.toString();
  }

  // Method to search for properties based on criteria
  public async searchProperties(criteria: {
    location?: string;
    minCapacity?: number;
    maxPrice?: number;
    amenities?: string[];
    eventType?: string;
  }): Promise<any[]> {
    // In a real implementation, this would query a database
    // For now, we'll filter the mock properties
    
    let results = [...MOCK_PROPERTIES];
    
    if (criteria.location) {
      results = results.filter(property => 
        property.location.toLowerCase().includes(criteria.location!.toLowerCase())
      );
    }
    
    if (criteria.minCapacity) {
      results = results.filter(property => property.capacity >= criteria.minCapacity!);
    }
    
    if (criteria.maxPrice) {
      results = results.filter(property => property.price <= criteria.maxPrice!);
    }
    
    if (criteria.amenities && criteria.amenities.length > 0) {
      results = results.filter(property => 
        criteria.amenities!.some(amenity => 
          property.amenities.some(a => a.toLowerCase().includes(amenity.toLowerCase()))
        )
      );
    }
    
    if (criteria.eventType) {
      results = results.filter(property => 
        property.eventTypes.some(type => 
          type.toLowerCase().includes(criteria.eventType!.toLowerCase())
        )
      );
    }
    
    return results;
  }

  // Method to get property details
  public async getPropertyDetails(propertyId: string): Promise<any> {
    // In a real implementation, this would query a database
    const property = MOCK_PROPERTIES.find(p => p.id === propertyId);
    return property || null;
  }

  // Method to check property availability
  public async checkAvailability(params: {
    propertyId: string;
    date: string;
    startTime: string;
    endTime: string;
  }): Promise<{ available: boolean; conflicts?: string[] }> {
    // In a real implementation, this would check a booking database
    // For now, we'll return mock data
    const property = MOCK_PROPERTIES.find(p => p.id === params.propertyId);
    
    if (!property) {
      return { available: false, conflicts: ["Property not found"] };
    }
    
    // Mock availability check - 80% chance of being available
    const isAvailable = Math.random() > 0.2;
    
    return {
      available: isAvailable,
      conflicts: isAvailable ? [] : ["Time slot already booked"]
    };
  }

  // Method to schedule a viewing
  public async scheduleViewing(params: {
    propertyId: string;
    date: string;
    time: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
  }): Promise<{ success: boolean; bookingId?: string; message: string }> {
    // In a real implementation, this would create a booking in the database
    // For now, we'll return mock data
    const property = MOCK_PROPERTIES.find(p => p.id === params.propertyId);
    
    if (!property) {
      return { 
        success: false, 
        message: "Property not found" 
      };
    }
    
    // Generate a mock booking ID
    const bookingId = `VIEW-${Date.now().toString(36).toUpperCase()}`;
    
    return {
      success: true,
      bookingId,
      message: `Viewing scheduled for ${property.title} on ${params.date} at ${params.time}`
    };
  }

  // Method to create a lead
  public async createLead(params: {
    name: string;
    email: string;
    phone?: string;
    interests?: string[];
    notes?: string;
  }): Promise<{ success: boolean; leadId?: string; message: string }> {
    // In a real implementation, this would create a lead in the CRM
    // For now, we'll return mock data
    
    // Generate a mock lead ID
    const leadId = `LEAD-${Date.now().toString(36).toUpperCase()}`;
    
    return {
      success: true,
      leadId,
      message: `Lead created for ${params.name} (${params.email})`
    };
  }

  // Method to reset the conversation
  public resetConversation(): void {
    this.conversationHistory = [];
    this.conversationHistory.push(new SystemMessage(SALES_AGENT_SYSTEM_PROMPT));
    this.conversationHistory.push(new SystemMessage(SALES_AGENT_TOOLS_PROMPT));
    this.currentStage = SALES_CONVERSATION_STAGES[0];
    const stagePrompt = `Current conversation stage: ${this.currentStage.name} - ${this.currentStage.description}`;
    this.conversationHistory.push(new SystemMessage(stagePrompt));
    this.conversationHistory.push(new AIMessage(SALES_AGENT_INCEPTION_PROMPT));
  }
}
