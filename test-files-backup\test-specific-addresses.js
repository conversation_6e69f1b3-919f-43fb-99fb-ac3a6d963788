/**
 * Test Specific NSW Addresses with Layer 8
 * 
 * This script tests specific addresses to determine which council they belong to
 * using layer 8 of the NSW Spatial Services API.
 */

// Test addresses
const testAddresses = [
  '23 John Radley Avenue, Dural, NSW 2158',
  '36 Earle St, Doonside NSW 2767',
  '10 Carlingford Road, Carlingford'
];

// Geocode an address using Mapbox
async function geocodeAddress(address) {
  try {
    const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=pk.eyJ1IjoiaG91c2Vnb2luZ21hdGUiLCJhIjoiY205bnFoc2M2MHNqMjJrcHZqajRuenNxdyJ9.SQZC2H1UZYeXydRwC13biA&country=AU&limit=1`;
    
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    if (geocodeData.features && geocodeData.features.length > 0) {
      const [lng, lat] = geocodeData.features[0].center;
      return { lat, lng };
    } else {
      console.error('Geocoding failed:', geocodeData);
      return null;
    }
  } catch (error) {
    console.error('Geocoding error:', error);
    return null;
  }
}

// Test the NSW Spatial Services API Layer 8 for LGA detection
async function testLGADetection(address, lat, lng) {
  try {
    console.log(`Testing address: ${address} (${lat}, ${lng})`);
    
    // NSW Spatial Services API endpoint for LGA (Layer 8)
    const lgaUrl = `https://portal.spatial.nsw.gov.au/server/rest/services/NSW_Administrative_Boundaries_Theme/MapServer/8/query`;
    
    const lgaParams = new URLSearchParams({
      geometry: `${lng},${lat}`, // longitude first, then latitude
      geometryType: 'esriGeometryPoint',
      inSR: '4326', // WGS84 coordinate system
      outFields: '*', // Get all fields to see what's available
      f: 'json'
    });
    
    const lgaResponse = await fetch(`${lgaUrl}?${lgaParams.toString()}`);
    
    if (lgaResponse.ok) {
      const lgaData = await lgaResponse.json();
      
      if (lgaData.features && lgaData.features.length > 0) {
        const attributes = lgaData.features[0].attributes;
        console.log('LGA attributes:', attributes);
        
        // Check for different possible field names
        let lgaName = null;
        if (attributes.lganame) {
          lgaName = attributes.lganame;
          console.log('LGA from lganame:', lgaName);
        } else if (attributes.LGANAME) {
          lgaName = attributes.LGANAME;
          console.log('LGA from LGANAME:', lgaName);
        } else if (attributes.NAME) {
          lgaName = attributes.NAME;
          console.log('LGA from NAME:', lgaName);
        } else if (attributes.name) {
          lgaName = attributes.name;
          console.log('LGA from name:', lgaName);
        } else {
          console.log('LGA field not found in attributes');
        }
        
        return lgaName;
      } else {
        console.log('No LGA information found');
        return null;
      }
    } else {
      console.error('NSW Spatial Services API response not OK:', lgaResponse.statusText);
      return null;
    }
  } catch (error) {
    console.error('Error fetching LGA information:', error);
    return null;
  }
}

// Run the tests
async function runTests() {
  console.log('Testing specific NSW addresses with Layer 8...\n');
  
  for (const address of testAddresses) {
    console.log(`\n=== Testing address: ${address} ===`);
    
    // Geocode the address
    const coords = await geocodeAddress(address);
    
    if (coords) {
      // Test LGA detection
      const lgaName = await testLGADetection(address, coords.lat, coords.lng);
      
      if (lgaName) {
        console.log(`\nResult: "${address}" belongs to "${lgaName}"\n`);
      } else {
        console.log(`\nResult: Could not determine council for "${address}"\n`);
      }
    } else {
      console.log(`\nResult: Could not geocode "${address}"\n`);
    }
    
    console.log('='.repeat(50));
  }
}

// Run the tests
runTests().catch(console.error);
