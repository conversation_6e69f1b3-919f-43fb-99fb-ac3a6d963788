import { SignUp as ClerkSignUp, useUser } from '@clerk/clerk-react';
import { clerkAppearance } from '../../utils/clerk-theme';
import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import OAuthButton from '../../components/auth/OAuthButton';
import { CLERK_CONFIG } from '../../config/clerk';
import GoogleButton from '../../components/auth/GoogleButton';
import EmailPasswordSignUp from '../../components/auth/EmailPasswordSignUp';
import { simulateWebhookEvent } from '../../api/clerk-webhook-handler';

export default function SignUp() {
  const { isLoaded, isSignedIn, user } = useUser();
  const [showDirectOAuth, setShowDirectOAuth] = useState(false);
  const [showEmailPasswordForm, setShowEmailPasswordForm] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const navigate = useNavigate();

  // Wait for Clerk to load
  if (!isLoaded) {
    return <div className="min-h-screen flex items-center justify-center">Loading authentication...</div>;
  }

  // Only redirect if user is truly signed in
  useEffect(() => {
    if (isSignedIn && !isRedirecting) {
      setIsRedirecting(true);
      // Check if user is admin
      const userEmail = user?.primaryEmailAddress?.emailAddress;
      if (userEmail === '<EMAIL>') {
        navigate('/admin/dashboard');
      } else {
        navigate('/my-account');
      }
    }
    // Remove any custom localStorage auth flags
    localStorage.removeItem('auth_success');
    localStorage.removeItem('registering_as_host');
    // Simulate a webhook event for analytics
    simulateWebhookEvent('page.viewed', {
      id: 'customer-signup-page',
      page: 'customer-signup'
    });
    // Check if we should show the direct OAuth button
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('direct') === 'true') {
      setShowDirectOAuth(true);
    }
  }, [navigate, isSignedIn, isRedirecting, user]);

  if (isSignedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="mt-4 p-3 bg-yellow-50 text-yellow-700 rounded-lg text-sm">
          You're already signed in. Redirecting to home page...
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <img
            className="mx-auto h-16 w-16 mb-2"
            src="/images/housegoing-logo.svg"
            alt="HouseGoing"
          />
        </div>
        <h2 className="text-center text-3xl font-bold text-gray-900">
          Join{' '}
          <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            HouseGoing
          </span>
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Create an account to book the perfect party venue
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-xl">
        <div className="bg-white py-10 px-6 shadow-xl sm:rounded-xl sm:px-12 border border-gray-100">
          {showDirectOAuth ? (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Join HouseGoing</h1>
                <p className="text-lg text-gray-600">
                  Create an account to find the perfect party venue
                </p>
                <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <p className="text-sm text-purple-800">
                    <strong>Customer Portal</strong> - Book amazing venues for your events
                  </p>
                </div>
              </div>

              <div className="mb-8">
                <p className="text-center text-base text-gray-600 mb-4">Sign up with:</p>
                <GoogleButton registrationType="guest" label="Continue with Google" />
              </div>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center">
                  <span className="px-4 py-1 bg-white text-gray-500 text-base">Or sign up with email</span>
                </div>
              </div>

              <button
                onClick={() => setShowEmailPasswordForm(true)}
                className="w-full bg-white border border-gray-300 rounded-full shadow-md hover:shadow-lg text-lg font-medium text-gray-700 py-4 px-8 transition-all duration-200 transform hover:-translate-y-0.5 flex items-center justify-center mb-4 relative overflow-hidden"
              >
                <div className="absolute inset-0 opacity-0 hover:opacity-5 bg-gradient-to-r from-purple-500 to-purple-700 transition-opacity duration-200"></div>
                <div className="absolute right-0 top-0 w-16 h-16 opacity-5">
                  <div className="w-full h-full bg-gradient-to-bl from-purple-500 to-transparent rounded-tr-full"></div>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-4 text-gray-500 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span className="relative z-10">Continue with Email</span>
              </button>

              <div className="mt-6 text-center">
                <button
                  onClick={() => setShowDirectOAuth(false)}
                  className="text-sm text-purple-600 hover:text-purple-800 font-medium"
                >
                  Back to standard sign up
                </button>
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  Already have an account?{' '}
                  <Link to="/login" className="text-purple-600 hover:text-purple-800 font-medium">
                    Sign in
                  </Link>
                </p>

                <p className="mt-2 text-sm text-gray-600">
                  Are you a venue owner?{' '}
                  <Link to="/host/signup" className="text-purple-600 hover:text-purple-800 font-medium">
                    Create Owner Portal Account
                  </Link>
                </p>
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Join HouseGoing</h1>
                <p className="text-lg text-gray-600">
                  Create an account to find the perfect party venue
                </p>
              </div>

              <div className="mb-8">
                <p className="text-center text-base text-gray-600 mb-4">Sign up with:</p>
                <GoogleButton registrationType="guest" label="Continue with Google" />
              </div>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center">
                  <span className="px-4 py-1 bg-white text-gray-500 text-base">Or sign up with email</span>
                </div>
              </div>

              {showEmailPasswordForm ? (
                <>
                  <EmailPasswordSignUp
                    role="guest"
                    onError={(error) => console.error('Sign up error:', error)}
                  />
                  <div className="mt-4 text-center">
                    <button
                      onClick={() => setShowEmailPasswordForm(false)}
                      className="text-sm text-purple-600 hover:text-purple-800"
                    >
                      Back to standard sign up
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setShowEmailPasswordForm(true)}
                    className="w-full bg-white border border-gray-300 rounded-full shadow-md hover:shadow-lg text-lg font-medium text-gray-700 py-4 px-8 transition-all duration-200 transform hover:-translate-y-0.5 flex items-center justify-center mb-4 relative overflow-hidden"
                  >
                    <div className="absolute inset-0 opacity-0 hover:opacity-5 bg-gradient-to-r from-purple-500 to-purple-700 transition-opacity duration-200"></div>
                    <div className="absolute right-0 top-0 w-16 h-16 opacity-5">
                      <div className="w-full h-full bg-gradient-to-bl from-purple-500 to-transparent rounded-tr-full"></div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-4 text-gray-500 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="relative z-10">Continue with Email</span>
                  </button>

                  <div className="hidden">
                    <ClerkSignUp
                      appearance={clerkAppearance}
                      routing="path"
                      path="/signup"
                      forceRedirectUrl={CLERK_CONFIG.redirectUrls.afterSignUp}
                      signInUrl="/sign-in"
                    />
                  </div>
                </>
              )}

              <div className="mt-6 text-center">
                {CLERK_CONFIG.publishableKey.includes('test_') && (
                  <p className="mb-2 text-xs text-gray-500">
                    Note: Using test keys. Social login may not work in development mode.
                  </p>
                )}

                <button
                  onClick={() => setShowDirectOAuth(true)}
                  className="text-sm text-purple-600 hover:text-purple-800"
                >
                  Having trouble? Try direct sign up
                </button>
              </div>

              {/* Host Portal Link */}
              <div className="mt-8 text-center border-t border-gray-200 pt-6">
                <p className="text-sm text-gray-600 mb-2">
                  Do you have a venue to share?
                </p>
                <Link
                  to="/host/signup"
                  className="inline-flex items-center px-4 py-2 border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors"
                >
                  Join as a Host
                </Link>
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  Already have an account?{' '}
                  <Link to="/login" className="text-purple-600 hover:text-purple-800 font-medium">
                    Sign in
                  </Link>
                </p>

                <p className="mt-2 text-sm text-gray-600">
                  Are you a venue owner?{' '}
                  <Link to="/host/signup" className="text-purple-600 hover:text-purple-800 font-medium">
                    Create Owner Portal Account
                  </Link>
                </p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
