/**
 * Clerk + Supabase Integration Test Component
 * 
 * This component tests the new native integration between Clerk and Supa<PERSON>.
 * It should be temporarily added to verify the authentication is working.
 */

import React, { useEffect, useState } from 'react';
import { useClerkSupabase } from '../../hooks/useClerkSupabase';

export default function ClerkSupabaseTest() {
  const { supabase, isReady, isAuthenticated, user, isLoading } = useClerkSupabase();
  const [testResult, setTestResult] = useState<any>(null);
  const [testing, setTesting] = useState(false);

  // Test the Supabase connection when ready
  useEffect(() => {
    if (isReady && supabase && !testing) {
      testSupabaseConnection();
    }
  }, [isReady, supabase]);

  const testSupabaseConnection = async () => {
    if (!supabase) return;
    
    setTesting(true);
    console.log('🧪 Testing Clerk + Supabase integration...');

    try {
      // Test 1: Simple query to check connection
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count(*)')
        .limit(1);

      if (error) {
        console.error('❌ Supabase test failed:', error);
        setTestResult({
          success: false,
          error: error.message,
          details: error
        });
      } else {
        console.log('✅ Supabase test successful:', data);
        setTestResult({
          success: true,
          data,
          message: 'Clerk + Supabase integration working!'
        });
      }
    } catch (error) {
      console.error('❌ Test error:', error);
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      });
    } finally {
      setTesting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800">🔄 Loading Clerk + Supabase...</h3>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800">⚠️ Not Authenticated</h3>
        <p className="text-yellow-700">Please sign in to test the integration.</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
      <h3 className="font-semibold text-gray-800 mb-3">🧪 Clerk + Supabase Integration Test</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>User:</strong> {user?.firstName || user?.email || 'Unknown'}
        </div>
        <div>
          <strong>Integration Ready:</strong> {isReady ? '✅ Yes' : '❌ No'}
        </div>
        <div>
          <strong>Supabase Client:</strong> {supabase ? '✅ Created' : '❌ Not Available'}
        </div>
      </div>

      {testing && (
        <div className="mt-3 p-2 bg-blue-100 rounded">
          <span className="text-blue-800">🔄 Testing connection...</span>
        </div>
      )}

      {testResult && (
        <div className={`mt-3 p-3 rounded ${
          testResult.success 
            ? 'bg-green-100 border border-green-200' 
            : 'bg-red-100 border border-red-200'
        }`}>
          <div className={`font-semibold ${
            testResult.success ? 'text-green-800' : 'text-red-800'
          }`}>
            {testResult.success ? '✅ Success!' : '❌ Failed'}
          </div>
          <div className={`text-sm mt-1 ${
            testResult.success ? 'text-green-700' : 'text-red-700'
          }`}>
            {testResult.message || testResult.error}
          </div>
          {testResult.details && (
            <details className="mt-2">
              <summary className="cursor-pointer text-xs opacity-75">
                Show Details
              </summary>
              <pre className="text-xs mt-1 p-2 bg-white rounded overflow-auto">
                {JSON.stringify(testResult.details, null, 2)}
              </pre>
            </details>
          )}
        </div>
      )}

      <button
        onClick={testSupabaseConnection}
        disabled={!isReady || testing}
        className="mt-3 px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 disabled:opacity-50"
      >
        {testing ? 'Testing...' : 'Test Again'}
      </button>
    </div>
  );
}
