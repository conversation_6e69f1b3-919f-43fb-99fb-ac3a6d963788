#!/usr/bin/env node

/**
 * Security Validation Script for HouseGoing
 * Validates that the application is secure and ready for production
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ERROR: ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  WARNING: ${message}`, 'yellow');
}

function logSuccess(message) {
  log(`✅ SUCCESS: ${message}`, 'green');
}

function logInfo(message) {
  log(`ℹ️  INFO: ${message}`, 'blue');
}

// Security validation functions
const validations = {
  // Check for hardcoded secrets
  checkHardcodedSecrets() {
    logInfo('Checking for hardcoded secrets...');
    
    const secretPatterns = [
      /sk_live_[a-zA-Z0-9]+/g,
      /sk_test_[a-zA-Z0-9]+/g,
      /rk_live_[a-zA-Z0-9]+/g,
      /whsec_[a-zA-Z0-9]+/g,
      /xkeysib-[a-zA-Z0-9\-]+/g,
      /sk-or-v1-[a-zA-Z0-9]+/g
    ];

    const srcDir = path.join(process.cwd(), 'src');
    let foundSecrets = false;

    function scanDirectory(dir) {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && file !== 'node_modules') {
          scanDirectory(filePath);
        } else if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) {
          const content = fs.readFileSync(filePath, 'utf8');
          
          for (const pattern of secretPatterns) {
            const matches = content.match(pattern);
            if (matches) {
              logError(`Hardcoded secret found in ${filePath}: ${matches[0].substring(0, 20)}...`);
              foundSecrets = true;
            }
          }
        }
      }
    }

    scanDirectory(srcDir);
    
    if (!foundSecrets) {
      logSuccess('No hardcoded secrets found');
    }
    
    return !foundSecrets;
  },

  // Check environment configuration
  checkEnvironmentConfig() {
    logInfo('Checking environment configuration...');
    
    const envFile = path.join(process.cwd(), '.env');
    const envServerFile = path.join(process.cwd(), '.env.server');
    
    let isValid = true;
    
    // Check if .env exists and contains only public keys
    if (fs.existsSync(envFile)) {
      const envContent = fs.readFileSync(envFile, 'utf8');
      
      // Check for secret keys in .env (should not be there)
      if (envContent.includes('sk_live_') || envContent.includes('sk_test_')) {
        logError('.env file contains secret keys - move them to .env.server');
        isValid = false;
      } else {
        logSuccess('.env file contains only public keys');
      }
    } else {
      logWarning('.env file not found');
    }
    
    // Check if .env.server exists (for production secrets)
    if (fs.existsSync(envServerFile)) {
      logSuccess('.env.server file exists for production secrets');
    } else {
      logWarning('.env.server file not found - create it for production deployment');
    }
    
    return isValid;
  },

  // Check for development artifacts
  checkDevelopmentArtifacts() {
    logInfo('Checking for development artifacts...');
    
    const srcDir = path.join(process.cwd(), 'src');
    let foundArtifacts = false;
    
    function scanForArtifacts(dir) {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && file !== 'node_modules') {
          scanForArtifacts(filePath);
        } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
          const content = fs.readFileSync(filePath, 'utf8');
          
          // Check for development bypasses
          if (content.includes('isDevelopment') && content.includes('bypass')) {
            logWarning(`Development bypass found in ${filePath}`);
            foundArtifacts = true;
          }
          
          // Check for localhost checks
          if (content.includes('localhost') && content.includes('127.0.0.1')) {
            logWarning(`Localhost checks found in ${filePath}`);
          }
          
          // Check for console.log statements
          const consoleMatches = content.match(/console\.log/g);
          if (consoleMatches && consoleMatches.length > 2) {
            logWarning(`Multiple console.log statements in ${filePath} (${consoleMatches.length})`);
          }
        }
      }
    }
    
    scanForArtifacts(srcDir);
    
    if (!foundArtifacts) {
      logSuccess('No critical development artifacts found');
    }
    
    return true; // Non-blocking
  },

  // Check gitignore configuration
  checkGitignore() {
    logInfo('Checking .gitignore configuration...');
    
    const gitignoreFile = path.join(process.cwd(), '.gitignore');
    
    if (!fs.existsSync(gitignoreFile)) {
      logError('.gitignore file not found');
      return false;
    }
    
    const gitignoreContent = fs.readFileSync(gitignoreFile, 'utf8');
    
    const requiredEntries = [
      '.env',
      '.env.server',
      '.env.secrets',
      'node_modules',
      'dist'
    ];
    
    let isValid = true;
    
    for (const entry of requiredEntries) {
      if (!gitignoreContent.includes(entry)) {
        logError(`Missing entry in .gitignore: ${entry}`);
        isValid = false;
      }
    }
    
    if (isValid) {
      logSuccess('.gitignore properly configured');
    }
    
    return isValid;
  },

  // Check build configuration
  checkBuildConfig() {
    logInfo('Checking build configuration...');
    
    const viteConfigFile = path.join(process.cwd(), 'vite.config.ts');
    
    if (!fs.existsSync(viteConfigFile)) {
      logError('vite.config.ts not found');
      return false;
    }
    
    const viteConfig = fs.readFileSync(viteConfigFile, 'utf8');
    
    let isValid = true;
    
    // Check if source maps are disabled
    if (!viteConfig.includes('sourcemap: false')) {
      logWarning('Source maps not explicitly disabled - consider disabling for production');
    }
    
    // Check if secret keys are in define section
    if (viteConfig.includes('STRIPE_SECRET_KEY') || viteConfig.includes('CLERK_SECRET_KEY')) {
      logError('Secret keys found in vite.config.ts define section');
      isValid = false;
    } else {
      logSuccess('Build configuration secure');
    }
    
    return isValid;
  },

  // Check package.json security
  checkPackageSecurity() {
    logInfo('Checking package.json security...');
    
    const packageFile = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
    
    let isValid = true;
    
    // Check if security scripts exist
    const securityScripts = ['security:check', 'security:audit', 'deploy:prod'];
    
    for (const script of securityScripts) {
      if (!packageJson.scripts[script]) {
        logWarning(`Missing security script: ${script}`);
      }
    }
    
    // Check if private is set to true
    if (!packageJson.private) {
      logWarning('Package should be marked as private');
    }
    
    logSuccess('Package.json security check completed');
    return isValid;
  }
};

// Main validation function
async function runSecurityValidation() {
  log('\n🔒 HouseGoing Security Validation\n', 'blue');
  
  const results = [];
  
  for (const [name, validation] of Object.entries(validations)) {
    try {
      const result = await validation();
      results.push({ name, result });
    } catch (error) {
      logError(`Validation ${name} failed: ${error.message}`);
      results.push({ name, result: false });
    }
  }
  
  // Summary
  log('\n📊 Validation Summary\n', 'blue');
  
  const passed = results.filter(r => r.result).length;
  const total = results.length;
  
  for (const { name, result } of results) {
    const status = result ? '✅ PASS' : '❌ FAIL';
    log(`${status} ${name}`);
  }
  
  log(`\nOverall: ${passed}/${total} validations passed\n`);
  
  if (passed === total) {
    logSuccess('🎉 All security validations passed! Ready for production deployment.');
    process.exit(0);
  } else {
    logError('❌ Some security validations failed. Please fix the issues before deploying.');
    process.exit(1);
  }
}

// Run the validation
runSecurityValidation().catch(error => {
  logError(`Validation failed: ${error.message}`);
  process.exit(1);
});

export { validations, runSecurityValidation };
