import { <PERSON><PERSON><PERSON><PERSON> as Clerk<PERSON>roviderBase, useUser } from '@clerk/clerk-react';
import { ReactNode, useEffect, useState } from 'react';
import { clerkAppearance } from '../utils/clerk-theme';
import { CLERK_CONFIG } from '../config/clerk';
import { syncClerkUserWithSupabase, initClerkSupabaseSync } from '../services/auth/clerk-supabase-sync';

interface ClerkProviderProps {
  children: ReactNode;
}

export function ClerkProvider({ children }: ClerkProviderProps) {
  const [isLoaded, setIsLoaded] = useState(false);

  // Log Clerk configuration on mount for debugging
  useEffect(() => {
    console.log('Clerk configuration loaded:', {
      publishableKey: CLERK_CONFIG.publishableKey.substring(0, 10) + '...',
      redirectUrl: CLERK_CONFIG.oauthCallbackURL,
      developmentMode: CLERK_CONFIG.developmentMode,
      domain: CLERK_CONFIG.clerkDomain
    });

    // Log a warning if using development keys
    if (CLERK_CONFIG.publishableKey.includes('test_')) {
      console.warn('Using Clerk test keys. For production, use live keys.');
    }

    // Initialize Clerk-Supabase synchronization
    initClerkSupabaseSync();

    // Set a timeout to ensure the app continues even if Clerk fails
    const timeout = setTimeout(() => {
      if (!isLoaded) {
        console.warn('Clerk initialization timeout, continuing anyway');
        setIsLoaded(true);
      }
    }, 3000);

    return () => clearTimeout(timeout);
  }, [isLoaded]);

  // Add a user change listener to handle Clerk authentication
  const UserSynchronizer = () => {
    const { user, isLoaded: isUserLoaded } = useUser();

    useEffect(() => {
      if (isUserLoaded && user) {
        console.log('User authenticated with Clerk:', user);

        // Synchronize Clerk user with Supabase using clerk_id
        syncClerkUserWithSupabase(user.id);
      }
    }, [isUserLoaded, user]);

    return null;
  };

  // Remove unused setError state and unsupported navigate property
  return (
    <ClerkProviderBase
      publishableKey={CLERK_CONFIG.publishableKey}
      appearance={clerkAppearance}
      signInFallbackRedirectUrl={CLERK_CONFIG.fallbackRedirectUrl}
      signInUrl="/login"
      signUpUrl="/signup"
    >
      <UserSynchronizer />
      {children}
    </ClerkProviderBase>
  );
}
