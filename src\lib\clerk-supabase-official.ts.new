/**
 * Official Clerk-Supabase Native Integration
 * Based on the official Clerk documentation (native integration, not JWT template)
 *
 * This is the NEW recommended way to integrate <PERSON> with Supabase as of April 2025.
 * The old JWT template method is deprecated.
 */

import { createClient } from '@supabase/supabase-js';

// Environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_URL : '');

const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY ||
  (typeof process !== 'undefined' ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY : '');

/**
 * Create a Supabase client using the official native integration pattern
 * Uses a simplified approach that works reliably with Clerk
 *
 * @param session - Clerk session object from useSession() hook
 * @returns Supabase client with Clerk authentication
 */
export function createClerkSupabaseClient(session: any) {
  console.log('🔗 Creating Clerk-Supabase client with native integration');

  // Create a basic client
  const client = createClient(supabaseUrl, supabaseKey);
  
  // Immediately try to set the auth token if we have a valid session
  if (session && typeof session.getToken === 'function') {
    try {
      // Get token and set it
      session.getToken({ template: "supabase" })
        .then((token: string) => {
          if (token) {
            client.auth.setSession({ 
              access_token: token,
              refresh_token: ''  // We don't need refresh token for Clerk
            });
            console.log('✅ Auth token set on Supabase client');
          }
        })
        .catch((err: any) => {
          console.error('❌ Error getting token from Clerk:', err);
        });
    } catch (error) {
      console.error('❌ Exception in createClerkSupabaseClient:', error);
    }
  } else {
    console.warn('⚠️ No valid session or getToken method');
  }

  return client;
}

/**
 * Server-side Supabase client (for API routes and server components)
 * Uses the official native integration pattern
 */
export function createServerSupabaseClient(getToken: () => Promise<string | null>) {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

/**
 * React hook to get an authenticated Supabase client
 * Use this in React components with useSession()
 */
export function useClerkSupabase(session: any) {
  return createClerkSupabaseClient(session);
}
