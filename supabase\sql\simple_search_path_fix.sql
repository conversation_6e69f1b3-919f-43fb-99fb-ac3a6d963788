-- Simple Search Path Fix for HouseGoing
-- This script adds secure search paths to existing functions without changing signatures
-- Run this in the Supabase SQL Editor

-- Use ALTER FUNCTION to add search_path to existing functions
-- This avoids the "cannot change return type" error

DO $$
DECLARE
    func_record RECORD;
    func_signature TEXT;
BEGIN
    -- Get all functions that need search_path fixes
    FOR func_record IN 
        SELECT 
            n.nspname as schema_name,
            p.proname as function_name,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname IN (
            'create_notification',
            'get_unread_message_count', 
            'calculate_confidence_score',
            'get_user_average_rating',
            'detect_property_type',
            'get_curfew_info',
            'api_get_curfew_info',
            'api_detect_property_type',
            'requesting_user_id',
            'check_venue_availability',
            'get_venue_availability_range',
            'block_venue_slot',
            'update_venue_operating_hours',
            'set_venue_day_availability',
            'http_get_curfew_info',
            'get_available_venues',
            'exec_sql_exists',
            'create_user_profiles_table',
            'create_admin_users_table',
            'update_updated_at_column',
            'check_booking_conflicts',
            'create_booking_with_availability_check'
        )
    LOOP
        -- Build function signature
        func_signature := func_record.schema_name || '.' || func_record.function_name;
        IF func_record.args != '' THEN
            func_signature := func_signature || '(' || func_record.args || ')';
        ELSE
            func_signature := func_signature || '()';
        END IF;
        
        -- Try to alter the function to add search_path
        BEGIN
            EXECUTE 'ALTER FUNCTION ' || func_signature || ' SET search_path = public';
            RAISE NOTICE 'Fixed search_path for function: %', func_signature;
        EXCEPTION 
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not fix function: % - Error: %', func_signature, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'Search path fix completed!';
END;
$$;
