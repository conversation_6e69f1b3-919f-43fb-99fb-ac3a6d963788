// Import the centralized Supabase client
import { getSupabaseClient } from '../../lib/supabase-client'

export interface AdminNotification {
  propertyId: string
  propertyName: string
  propertyAddress?: string
  propertyType?: string
  ownerDetails?: {
    ownerId: string
    phoneNumber?: string
    bankName: string
  }
  venueDetails?: {
    size: number
    maxGuests: number
    price: number
    amenities: string[]
  }
  timestamp: string
  type: 'new_property'
  read: boolean
}

export const notifyAdmin = async (notification: AdminNotification) => {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { error } = await supabase
      .from('admin_notifications')
      .insert({
        ...notification,
        type: 'new_property',
        read: false
      })

    if (error) throw error
    return true
  } catch (error) {
    console.error('Error notifying admin:', error)
    throw error
  }
}
