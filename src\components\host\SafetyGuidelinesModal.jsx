import React from 'react';
import { X, Shield, AlertTriangle, FileText, Phone, Mail } from 'lucide-react';

const SafetyGuidelinesModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      ></div>
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-blue-50">
            <div className="flex items-center">
              <Shield className="h-6 w-6 text-blue-600 mr-3" />
              <h2 className="text-xl font-bold text-blue-900">
                HouseGoing Venue Safety Guidelines
              </h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <div className="prose prose-sm max-w-none">
              
              {/* Introduction */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-1 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-900 mb-2">Important Notice</h3>
                    <p className="text-yellow-800 text-sm">
                      These safety guidelines ensure your venue meets essential safety standards for hosting private gatherings. 
                      Following these requirements helps protect your guests and provides you with liability protection as a host.
                    </p>
                  </div>
                </div>
              </div>

              {/* Essential Safety Requirements */}
              <section className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <Shield className="h-5 w-5 text-green-600 mr-2" />
                  Essential Safety Requirements
                </h3>

                <div className="space-y-6">
                  {/* Fire Safety */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">🔥 Fire Safety & Emergency Preparedness</h4>
                    
                    <div className="space-y-3 text-sm">
                      <div>
                        <strong>Smoke Alarms:</strong>
                        <ul className="list-disc list-inside ml-4 mt-1 text-gray-700">
                          <li>Working smoke alarms must be installed on each level of the venue</li>
                          <li>Test smoke alarms monthly and replace batteries annually</li>
                          <li>Smoke alarms must be located in/near all sleeping areas (if overnight stays permitted)</li>
                        </ul>
                      </div>

                      <div>
                        <strong>Fire Extinguishers:</strong>
                        <ul className="list-disc list-inside ml-4 mt-1 text-gray-700">
                          <li>At least one fire extinguisher accessible to guests</li>
                          <li>Fire extinguisher must be fully charged with valid inspection tag</li>
                          <li>Minimum 2.5kg ABE powder type suitable for all fire types</li>
                          <li>Fire blanket required in kitchen areas (if kitchen facilities provided)</li>
                        </ul>
                      </div>

                      <div>
                        <strong>Emergency Exits:</strong>
                        <ul className="list-disc list-inside ml-4 mt-1 text-gray-700">
                          <li>All emergency exits clearly marked with illuminated signs</li>
                          <li>Emergency exit paths must be free from obstruction at all times</li>
                          <li>Emergency exit doors must open easily from inside without keys</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Structural Safety */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">🏗️ Basic Structural Safety</h4>
                    
                    <div className="space-y-2 text-sm text-gray-700">
                      <div>• Building structure in good condition with no obvious hazards</div>
                      <div>• Staircases have secure handrails and are well-maintained</div>
                      <div>• Balconies and elevated areas have secure railings (minimum 1m height)</div>
                      <div>• No evidence of water damage, mold, or pest infestation</div>
                      <div>• Adequate lighting throughout all guest-accessible areas</div>
                      <div>• No exposed wiring or damaged power outlets</div>
                    </div>
                  </div>

                  {/* Pool Safety */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">🏊 Pool Safety (If Applicable)</h4>
                    
                    <div className="space-y-2 text-sm text-gray-700">
                      <div>• Pool fence minimum 1.2m height compliant with NSW regulations</div>
                      <div>• Self-closing, self-latching gate that opens away from pool</div>
                      <div>• CPR sign displayed prominently in pool area</div>
                      <div>• Pool chemicals stored securely away from guest access</div>
                    </div>
                  </div>

                  {/* Insurance */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">🛡️ Insurance Requirements (Tiered)</h4>

                    <div className="space-y-2 text-sm text-gray-700">
                      <div>• <strong>Basic venues:</strong> Minimum $5 million (small private parties)</div>
                      <div>• <strong>Standard venues:</strong> $10 million recommended (most bookings)</div>
                      <div>• <strong>Premium venues:</strong> $20 million required (large events, food service)</div>
                      <div>• Insurance must specifically cover venue hire activities</div>
                      <div>• Certificate of Currency must be current and available upon request</div>
                    </div>
                  </div>

                  {/* Emergency Information */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">📞 Emergency Information & Support</h4>
                    
                    <div className="space-y-2 text-sm text-gray-700">
                      <div>• Emergency contact numbers (000, local police, fire, ambulance)</div>
                      <div>• Venue address and emergency information card for guests</div>
                      <div>• Host contact details accessible 24/7</div>
                      <div>• First aid kit available and stocked with basic supplies</div>
                    </div>
                  </div>
                </div>
              </section>

              {/* NSW Legal Compliance */}
              <section className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 mb-4">⚖️ NSW Legal Compliance</h3>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="space-y-3 text-sm">
                    <div>
                      <strong>Noise Regulations:</strong>
                      <div className="ml-4 mt-1 text-gray-700">
                        Standard quiet hours: 10 PM - 8 AM weekdays, 12 AM - 8 AM weekends
                      </div>
                    </div>
                    <div>
                      <strong>Capacity Limits:</strong>
                      <div className="ml-4 mt-1 text-gray-700">
                        Maximum occupancy based on 1 person per 2 sqm for indoor areas
                      </div>
                    </div>
                    <div>
                      <strong>Alcohol Service:</strong>
                      <div className="ml-4 mt-1 text-gray-700">
                        Must comply with NSW Liquor Act requirements if serving alcohol
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Host Responsibilities */}
              <section className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 mb-4">👤 Host Responsibilities</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Before Each Event</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• Conduct safety walkthrough of venue</li>
                      <li>• Ensure all safety equipment is functional</li>
                      <li>• Verify emergency exits are clear</li>
                      <li>• Provide safety briefing to guests</li>
                    </ul>
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">During Events</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• Monitor guest behavior and safety</li>
                      <li>• Respond promptly to safety concerns</li>
                      <li>• Maintain contact availability</li>
                      <li>• Enforce venue rules and capacity limits</li>
                    </ul>
                  </div>
                </div>
              </section>

              {/* Contact Information */}
              <section className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-bold text-gray-900 mb-4">📞 Need Help?</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-blue-600 mr-2" />
                    <span>Email: <EMAIL></span>
                  </div>
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
                    <span>Emergency: 000</span>
                  </div>
                </div>
              </section>

            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-600">
                HouseGoing Pty Ltd - ABN **************
              </p>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Close Guidelines
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SafetyGuidelinesModal;
