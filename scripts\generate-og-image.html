<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HouseGoing OG Image Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .og-image {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .og-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .logo-container {
            margin-bottom: 40px;
            z-index: 2;
        }
        
        .logo {
            width: 200px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        }
        
        .title {
            font-size: 64px;
            font-weight: 800;
            color: white;
            text-align: center;
            margin: 0 0 20px 0;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 2;
        }
        
        .subtitle {
            font-size: 32px;
            font-weight: 400;
            color: rgba(255,255,255,0.9);
            text-align: center;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            z-index: 2;
        }
        
        .tagline {
            font-size: 24px;
            font-weight: 300;
            color: rgba(255,255,255,0.8);
            text-align: center;
            margin: 20px 0 0 0;
            font-style: italic;
            z-index: 2;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }
        
        .party-icon {
            position: absolute;
            font-size: 60px;
            opacity: 0.2;
            color: white;
        }
        
        .party-icon:nth-child(1) { top: 50px; left: 100px; }
        .party-icon:nth-child(2) { top: 100px; right: 150px; }
        .party-icon:nth-child(3) { bottom: 80px; left: 200px; }
        .party-icon:nth-child(4) { bottom: 120px; right: 100px; }
    </style>
</head>
<body>
    <div class="og-image">
        <div class="decorative-elements">
            <div class="party-icon">🎉</div>
            <div class="party-icon">🏠</div>
            <div class="party-icon">🎊</div>
            <div class="party-icon">🎈</div>
        </div>
        
        <div class="logo-container">
            <!-- HouseGoing logo would go here -->
            <div style="width: 200px; height: 60px; background: white; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 24px; color: #667eea;">
                HouseGoing
            </div>
        </div>
        
        <h1 class="title">Find Your Perfect Venue</h1>
        <p class="subtitle">Party Venues & Event Spaces in NSW</p>
        <p class="tagline">"the only house we going, is a party house"</p>
    </div>

    <script>
        // Instructions for generating the actual image:
        console.log(`
🎨 HouseGoing OG Image Generator

To create the actual OG image:

1. Open this HTML file in a browser
2. Take a screenshot of the .og-image div (1200x630px)
3. Save as 'og-image.png' in the public folder

Or use a tool like:
- Puppeteer to automate screenshot
- Figma/Canva with these dimensions
- Any image editor with 1200x630px canvas

The image should include:
✅ HouseGoing branding
✅ Clear value proposition
✅ Your tagline
✅ Professional gradient background
✅ Party/venue themed elements
        `);
    </script>
</body>
</html>
