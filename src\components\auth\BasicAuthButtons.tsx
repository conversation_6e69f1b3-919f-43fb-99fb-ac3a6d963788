import React from 'react';
import { Link } from 'react-router-dom';
import { User, LogOut } from 'lucide-react';
import { useAuth, useClerk } from '@clerk/clerk-react';

interface BasicAuthButtonsProps {
  className?: string;
}

export default function BasicAuthButtons({ className = '' }: BasicAuthButtonsProps) {
  const { isSignedIn, isLoaded } = useAuth();
  const { signOut } = useClerk();

  // Show loading state
  if (!isLoaded) {
    return null;
  }

  // Show authenticated state
  if (isSignedIn) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <Link to="/my-account">
          <button className="bg-white hover:bg-gray-50 text-gray-800 font-medium py-2 px-4 rounded border border-gray-200">
            My Account
          </button>
        </Link>
        <button
          onClick={() => signOut()}
          className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded"
        >
          <LogOut className="w-4 h-4 inline mr-1" />
          <span>Sign Out</span>
        </button>
      </div>
    );
  }

  // Show unauthenticated state
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <Link to="/login">
        <button className="bg-white hover:bg-gray-50 text-gray-800 font-medium py-2 px-4 rounded border border-gray-200">
          <User className="w-4 h-4 inline mr-1" />
          <span>Sign In</span>
        </button>
      </Link>
      <Link to="/signup">
        <button className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded">
          <span>Sign Up</span>
        </button>
      </Link>
    </div>
  );
}
