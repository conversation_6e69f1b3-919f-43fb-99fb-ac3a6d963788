# HouseGoing Email Server

Email notification server for the HouseGoing platform using <PERSON><PERSON>mail<PERSON> and Gmail SMTP.

## Features

- ✅ **Property submission notifications** to admin
- ✅ **User message notifications** between guests and hosts  
- ✅ **Review notifications** for new reviews
- ✅ **Beautiful HTML email templates** with HouseGoing branding
- ✅ **Gmail SMTP integration** for reliable delivery
- ✅ **CORS enabled** for frontend integration
- ✅ **Environment variable support** for secure deployment

## Endpoints

### `GET /`
Health check endpoint showing server status and available endpoints.

### `GET /health`
Simple health check returning `{ status: 'OK' }`.

### `POST /api/test-email`
Send a test email to verify the email service is working.

### `POST /api/property-submission-email`
Send notification when a new property is submitted for review.

**Body:**
```json
{
  "name": "Beautiful Venue",
  "id": "venue-123",
  "address": "123 Party Street, Sydney NSW",
  "type": "House",
  "ownerId": "user-456"
}
```

### `POST /api/send-notification-email`
Send notification emails for messages, reviews, and other user interactions.

**Body:**
```json
{
  "to": "<EMAIL>",
  "subject": "New message from John - <PERSON>Going",
  "html": "<h1>Beautiful HTML email</h1>",
  "text": "Plain text version",
  "from": "<EMAIL>",
  "fromName": "HouseGoing"
}
```

## Environment Variables

Set these in your deployment platform (Render, Railway, etc.):

```env
PORT=3001
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=ztjj vkeu foty iyeg
```

## Local Development

```bash
# Install dependencies
npm install

# Start server
npm start

# Server will run on http://localhost:3001
```

## Deployment to Render

1. **Create new Web Service** on Render
2. **Connect GitHub repository**
3. **Set build command**: `npm install`
4. **Set start command**: `npm start`
5. **Add environment variables** (Gmail credentials)
6. **Deploy**

## CORS Configuration

The server allows requests from:
- `https://housegoing.com.au` (production)
- `http://localhost:5173` (Vite dev server)
- `http://localhost:3000` (React dev server)

## Gmail Setup

Uses Gmail SMTP with App Password authentication:
- **Service**: Gmail
- **User**: <EMAIL>
- **Password**: App Password (not regular password)
- **Security**: 2FA enabled, App Password generated

## Error Handling

All endpoints include comprehensive error handling:
- ✅ **Validation** of required fields
- ✅ **Detailed error messages** for debugging
- ✅ **Console logging** for monitoring
- ✅ **Graceful failures** with proper HTTP status codes

## Integration with Frontend

The frontend calls this server for email notifications:

```javascript
// Example: Send notification email
const response = await fetch(`${BACKEND_URL}/api/send-notification-email`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    to: '<EMAIL>',
    subject: 'New message - HouseGoing',
    html: '<h1>You have a new message!</h1>'
  })
});
```

## Monitoring

Check server health:
- **Health endpoint**: `GET /health`
- **Console logs**: All email attempts logged
- **Error tracking**: Failed emails logged with details
- **Success tracking**: Message IDs logged for successful sends
