  // Render Amenities & Features section
  const renderAmenitiesFeatures = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Amenities & Features</h2>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Amenities</label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-parking"
                type="checkbox"
                checked={formData.amenities.includes('parking')}
                onChange={() => handleAmenityToggle('parking')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-parking" className="font-medium text-gray-700">
                Parking Available
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-publicTransport"
                type="checkbox"
                checked={formData.amenities.includes('publicTransport')}
                onChange={() => handleAmenityToggle('publicTransport')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-publicTransport" className="font-medium text-gray-700">
                Near Public Transport
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-kitchen"
                type="checkbox"
                checked={formData.amenities.includes('kitchen')}
                onChange={() => handleAmenityToggle('kitchen')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-kitchen" className="font-medium text-gray-700">
                Kitchen Facilities
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-bathroom"
                type="checkbox"
                checked={formData.amenities.includes('bathroom')}
                onChange={() => handleAmenityToggle('bathroom')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-bathroom" className="font-medium text-gray-700">
                Private Bathroom
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-soundSystem"
                type="checkbox"
                checked={formData.amenities.includes('soundSystem')}
                onChange={() => handleAmenityToggle('soundSystem')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-soundSystem" className="font-medium text-gray-700">
                Sound System
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-outdoorSpace"
                type="checkbox"
                checked={formData.amenities.includes('outdoorSpace')}
                onChange={() => handleAmenityToggle('outdoorSpace')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-outdoorSpace" className="font-medium text-gray-700">
                Outdoor Space
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-aircon"
                type="checkbox"
                checked={formData.amenities.includes('aircon')}
                onChange={() => handleAmenityToggle('aircon')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-aircon" className="font-medium text-gray-700">
                Air Conditioning
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="amenity-furniture"
                type="checkbox"
                checked={formData.amenities.includes('furniture')}
                onChange={() => handleAmenityToggle('furniture')}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="amenity-furniture" className="font-medium text-gray-700">
                Furniture Provided
              </label>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <label className="block mb-2 font-medium">Parking Details</label>
        <textarea
          value={formData.parkingDetails}
          onChange={(e) => setFormData({...formData, parkingDetails: e.target.value})}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe parking options (e.g., 2 off-street spots, street parking available, etc.)"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Public Transportation</label>
        <textarea
          value={formData.transportDetails}
          onChange={(e) => setFormData({...formData, transportDetails: e.target.value})}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe nearby public transport options (e.g., 5 min walk to Central Station)"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Nearby Landmarks</label>
        <textarea
          value={formData.nearbyLandmarks}
          onChange={(e) => setFormData({...formData, nearbyLandmarks: e.target.value})}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="List nearby landmarks or points of interest"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">BYO Alcohol Policy</label>
        <select
          value={formData.byoPolicy}
          onChange={(e) => setFormData({...formData, byoPolicy: e.target.value})}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="allowed">BYO Allowed - No Restrictions</option>
          <option value="allowed_with_restrictions">BYO Allowed - With Restrictions</option>
          <option value="not_allowed">BYO Not Allowed</option>
          <option value="licensed">Licensed Venue - No BYO</option>
        </select>
        {formData.byoPolicy === 'allowed_with_restrictions' && (
          <textarea
            value={formData.byoPolicy === 'allowed_with_restrictions' ? formData.byoPolicy : ''}
            onChange={(e) => setFormData({...formData, byoPolicy: e.target.value})}
            className="w-full p-2 border rounded mt-2"
            rows={2}
            placeholder="Describe BYO restrictions (e.g., no glass bottles, wine only, etc.)"
          />
        )}
      </div>
    </div>
  );
  
  // Render House Rules section
  const renderHouseRules = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">House Rules & Policies</h2>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Noise Restrictions</label>
        <textarea
          value={formData.noiseRestrictions}
          onChange={(e) => setFormData({...formData, noiseRestrictions: e.target.value})}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe any noise restrictions (e.g., music must be turned down after 10pm)"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Latest End Time</label>
        <select
          value={formData.endTime}
          onChange={(e) => setFormData({...formData, endTime: e.target.value})}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="9pm">9:00 PM</option>
          <option value="10pm">10:00 PM</option>
          <option value="11pm">11:00 PM</option>
          <option value="12am">12:00 AM</option>
          <option value="1am">1:00 AM</option>
          <option value="2am">2:00 AM</option>
          <option value="3am">3:00 AM</option>
          <option value="no_restriction">No Restriction</option>
        </select>
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Decorations Policy</label>
        <textarea
          value={formData.decorationsPolicy}
          onChange={(e) => setFormData({...formData, decorationsPolicy: e.target.value})}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="Describe what decorations are allowed (e.g., no confetti, wall attachments, etc.)"
        />
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Smoking Policy</label>
        <select
          value={formData.smokingPolicy}
          onChange={(e) => setFormData({...formData, smokingPolicy: e.target.value})}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="not_allowed">No Smoking Allowed</option>
          <option value="outdoor_only">Outdoor Areas Only</option>
          <option value="designated_areas">Designated Areas Only</option>
          <option value="allowed">Smoking Allowed</option>
        </select>
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Pet Policy</label>
        <select
          value={formData.petPolicy}
          onChange={(e) => setFormData({...formData, petPolicy: e.target.value})}
          className="w-full p-2 border rounded"
        >
          <option value="">Select an option</option>
          <option value="not_allowed">No Pets Allowed</option>
          <option value="dogs_only">Dogs Only</option>
          <option value="case_by_case">Case by Case Basis</option>
          <option value="allowed">Pets Allowed</option>
        </select>
      </div>

      <div className="mb-6">
        <label className="block mb-2 font-medium">Additional Fees</label>
        <textarea
          value={formData.additionalFees}
          onChange={(e) => setFormData({...formData, additionalFees: e.target.value})}
          className="w-full p-2 border rounded"
          rows={2}
          placeholder="List any additional fees (e.g., cleaning fee, security deposit, etc.)"
        />
      </div>
    </div>
  );
