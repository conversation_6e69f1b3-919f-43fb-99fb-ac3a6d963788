import React from 'react';
import { Users } from 'lucide-react';

interface GuestCounterProps {
  onSelect: (range: string) => void;
  isOpen: boolean;
}

export default function GuestCounter({ onSelect, isOpen }: GuestCounterProps) {
  if (!isOpen) return null;

  const guestRanges = [
    '1-5 guests',
    '6-10 guests',
    '11-20 guests',
    '20-40 guests',
    '41+ guests'
  ];

  return (
    <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg overflow-hidden z-50">
      {guestRanges.map((range) => (
        <button
          key={range}
          onClick={() => onSelect(range)}
          className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
        >
          {range}
        </button>
      ))}
    </div>
  );
}