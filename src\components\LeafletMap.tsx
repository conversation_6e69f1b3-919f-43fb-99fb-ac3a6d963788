import React, { forwardRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

interface LeafletMapProps extends L.MapOptions {
  center: L.LatLngExpression;
  zoom: number;
  children?: React.ReactNode;
  className?: string;
  onClick?: (e: L.LeafletMouseEvent) => void;
}

const LeafletMap = forwardRef<L.Map, LeafletMapProps>(({
  center,
  zoom,
  children,
  className,
  onClick,
  ...options
}, ref) => {
  return (
    <MapContainer
      center={center}
      zoom={zoom}
      className={className}
      ref={ref}
      {...options}
    >
      <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      />
      {children}
    </MapContainer>
  );
});

LeafletMap.displayName = 'LeafletMap';

export default LeafletMap;
