import { getCurfewInfo } from './src/lib/nsw-party-planning/curfew-api';
import { parseNSWAddress } from './src/utils/addressUtils.js';
import { loadGeoJSON, findZoneForPoint, findLGAForPoint } from './src/utils/spatialUtils.js';

async function testZoning() {
  try {
    const address = '10 Darvall Road, Eastwood, NSW 2122';
    const lat = -33.791;
    const lng = 151.080;

    const [zoningData, lgaData] = await Promise.all([
      loadGeoJSON('./src/data/nswZoningData.json'),
      loadGeoJSON('./src/data/lga.geojson')
    ]);

    const point = { type: 'Point', coordinates: [lng, lat] };
    const zoneCode = findZoneForPoint(point, zoningData) || 'R2';
    const lgaName = findLGAForPoint(point, lgaData) || 'Unknown';
    const isApartment = address && /^\d+\//.test(address);

    const curfewInfo = await getCurfewInfo({
      address: parseNSWAddress(address),
      propertyType: isApartment ? 'Apartment/Unit' : null,
      zoneCode,
      lgaName
    });

    console.log('Zoning Analysis Results:');
    console.log('Address:', address);
    console.log('Coordinates:', { lat, lng });
    console.log('Zoning:', zoneCode);
    console.log('LGA:', lgaName);
    console.log('Curfew:', {
      start: curfewInfo.curfew_start,
      end: curfewInfo.curfew_end,
      rules: curfewInfo.rules
    });
  } catch (error) {
    console.error('Error:', error);
  }
}

testZoning();
