import React from 'react';
import { Home, Star, TrendingUp } from 'lucide-react';
import { useListingStats } from '../../hooks/useListingStats';

export default function ListingStats() {
  const { stats, isLoading } = useListingStats();

  if (isLoading) {
    return <div>Loading stats...</div>;
  }

  const statItems = [
    {
      icon: Home,
      label: 'Active Listings',
      value: stats?.activeListings || 0
    },
    {
      icon: Star,
      label: 'Average Rating',
      value: stats?.averageRating?.toFixed(1) || '0.0'
    },
    {
      icon: TrendingUp,
      label: 'Total Bookings',
      value: stats?.totalBookings || 0
    }
  ];

  return (
    <div className="grid grid-cols-3 gap-4">
      {statItems.map((item) => (
        <div key={item.label} className="bg-white p-4 rounded-xl shadow-md">
          <div className="flex items-center justify-between mb-2">
            <item.icon className="h-5 w-5 text-purple-600" />
            <span className="text-sm text-gray-600">{item.label}</span>
          </div>
          <div className="text-2xl font-bold">{item.value}</div>
        </div>
      ))}
    </div>
  );
}