/**
 * Analytics API Service
 * Handles suburb-wise search and booking analytics
 */

import { supabase } from '../lib/supabase-client';

// Types for analytics data
export interface SuburbSearchAnalytics {
  suburb: string;
  state: string;
  search_count: number;
  unique_users: number;
  unique_ips: number;
  last_search: string;
}

export interface SuburbBookingAnalytics {
  suburb: string;
  state: string;
  booking_count: number;
  total_revenue: number;
  unique_users: number;
  avg_booking_amount: number;
  last_booking: string;
}

export interface SuburbConversionAnalytics {
  suburb: string;
  state: string;
  search_count: number;
  booking_count: number;
  conversion_rate: number;
  revenue_per_search: number;
}

export interface SearchTrackingData {
  user_id?: string;
  search_query: string;
  suburb?: string;
  state?: string;
  search_filters?: Record<string, any>;
  results_count?: number;
}

export interface BookingTrackingData {
  booking_id: string;
  user_id: string;
  venue_id: string;
  venue_suburb: string;
  venue_state: string;
  booking_date: string;
  booking_amount: number;
  guest_count: number;
  booking_status?: string;
}

// Utility function to get user's IP address
async function getUserIP(): Promise<string> {
  try {
    // In development, return a mock IP
    if (typeof window !== 'undefined' && 
        (window.location.hostname === 'localhost' || 
         window.location.hostname === '127.0.0.1')) {
      return '127.0.0.1';
    }

    // In production, get real IP from a service
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip || '0.0.0.0';
  } catch (error) {
    console.warn('Failed to get IP address:', error);
    return '0.0.0.0';
  }
}

// Utility function to generate session ID
function getSessionId(): string {
  let sessionId = sessionStorage.getItem('housegoing_session_id');
  if (!sessionId) {
    sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('housegoing_session_id', sessionId);
  }
  return sessionId;
}

// Utility function to extract suburb from search query
function extractSuburbFromQuery(query: string): { suburb?: string; state?: string } {
  const cleanQuery = query.toLowerCase().trim();
  
  // Common Australian suburbs and states
  const statePatterns = {
    'nsw': ['nsw', 'new south wales', 'sydney'],
    'vic': ['vic', 'victoria', 'melbourne'],
    'qld': ['qld', 'queensland', 'brisbane'],
    'wa': ['wa', 'western australia', 'perth'],
    'sa': ['sa', 'south australia', 'adelaide'],
    'tas': ['tas', 'tasmania', 'hobart'],
    'act': ['act', 'canberra'],
    'nt': ['nt', 'northern territory', 'darwin']
  };

  // Try to extract state
  let detectedState: string | undefined;
  for (const [state, patterns] of Object.entries(statePatterns)) {
    if (patterns.some(pattern => cleanQuery.includes(pattern))) {
      detectedState = state.toUpperCase();
      break;
    }
  }

  // For now, return the query as suburb if it's not too long
  const suburb = cleanQuery.length <= 50 ? cleanQuery : undefined;

  return { suburb, state: detectedState };
}

/**
 * Track a search event
 */
export async function trackSearchEvent(data: SearchTrackingData): Promise<void> {
  try {
    const ip = await getUserIP();
    const sessionId = getSessionId();
    const userAgent = navigator.userAgent;
    const referrer = document.referrer;

    // Extract suburb/state if not provided
    const { suburb, state } = data.suburb && data.state 
      ? { suburb: data.suburb, state: data.state }
      : extractSuburbFromQuery(data.search_query);

    const { error } = await supabase.rpc('track_search_event', {
      p_user_id: data.user_id || null,
      p_ip_address: ip,
      p_search_query: data.search_query,
      p_suburb: suburb || null,
      p_state: state || null,
      p_search_filters: data.search_filters || {},
      p_results_count: data.results_count || 0,
      p_user_agent: userAgent,
      p_referrer: referrer,
      p_session_id: sessionId
    });

    if (error) {
      console.error('Error tracking search event:', error);
    } else {
      console.log('✅ Search event tracked:', { suburb, state, query: data.search_query });
    }
  } catch (error) {
    console.error('Failed to track search event:', error);
  }
}

/**
 * Track a booking event
 */
export async function trackBookingEvent(data: BookingTrackingData): Promise<void> {
  try {
    const ip = await getUserIP();
    const sessionId = getSessionId();
    const userAgent = navigator.userAgent;

    const { error } = await supabase.rpc('track_booking_event', {
      p_booking_id: data.booking_id,
      p_user_id: data.user_id,
      p_ip_address: ip,
      p_venue_id: data.venue_id,
      p_venue_suburb: data.venue_suburb,
      p_venue_state: data.venue_state,
      p_booking_date: data.booking_date,
      p_booking_amount: data.booking_amount,
      p_guest_count: data.guest_count,
      p_booking_status: data.booking_status || 'pending',
      p_user_agent: userAgent,
      p_session_id: sessionId
    });

    if (error) {
      console.error('Error tracking booking event:', error);
    } else {
      console.log('✅ Booking event tracked:', { suburb: data.venue_suburb, amount: data.booking_amount });
    }
  } catch (error) {
    console.error('Failed to track booking event:', error);
  }
}

/**
 * Get top searched suburbs
 */
export async function getTopSearchedSuburbs(
  limit: number = 10, 
  daysBack: number = 30
): Promise<SuburbSearchAnalytics[]> {
  try {
    const { data, error } = await supabase.rpc('get_top_searched_suburbs', {
      limit_count: limit,
      days_back: daysBack
    });

    if (error) {
      console.error('Error fetching top searched suburbs:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch top searched suburbs:', error);
    return [];
  }
}

/**
 * Get top booked suburbs
 */
export async function getTopBookedSuburbs(
  limit: number = 10, 
  daysBack: number = 30
): Promise<SuburbBookingAnalytics[]> {
  try {
    const { data, error } = await supabase.rpc('get_top_booked_suburbs', {
      limit_count: limit,
      days_back: daysBack
    });

    if (error) {
      console.error('Error fetching top booked suburbs:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch top booked suburbs:', error);
    return [];
  }
}

/**
 * Get suburb conversion rates
 */
export async function getSuburbConversionRates(
  limit: number = 10, 
  daysBack: number = 30
): Promise<SuburbConversionAnalytics[]> {
  try {
    const { data, error } = await supabase.rpc('get_suburb_conversion_rates', {
      limit_count: limit,
      days_back: daysBack
    });

    if (error) {
      console.error('Error fetching suburb conversion rates:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch suburb conversion rates:', error);
    return [];
  }
}
