import React from 'react';
import { Link } from 'react-router-dom';
import { User } from 'lucide-react';
import { useAuth } from '@clerk/clerk-react';

export default function BasicMobileAuthStatus() {
  const { isSignedIn, isLoaded } = useAuth();
  
  // Show nothing while loading
  if (!isLoaded) {
    return null;
  }
  
  // Show authenticated state
  if (isSignedIn) {
    return (
      <div className="md:hidden flex items-center mr-2">
        <Link to="/my-account" className="flex items-center">
          <div className="bg-purple-100 text-purple-800 rounded-full h-6 w-6 flex items-center justify-center font-medium mr-1">
            <User className="w-3 h-3" />
          </div>
          <span className="text-sm text-purple-600 font-medium">
            Account
          </span>
        </Link>
      </div>
    );
  }
  
  // Show unauthenticated state
  return (
    <div className="md:hidden flex items-center mr-2">
      <Link to="/login" className="flex items-center">
        <User className="w-4 h-4 mr-1 text-purple-600" />
        <span className="text-sm text-purple-600 font-medium">
          Sign In
        </span>
      </Link>
    </div>
  );
}
