# Supabase Setup Guide for HouseGoing

This guide will help you set up the necessary database tables and functions in Supabase for the HouseGoing application.

## Overview

The HouseGoing application requires several database tables and SQL functions to work properly. These need to be created manually in the Supabase SQL Editor.

## Step 1: Access the Supabase SQL Editor

1. Go to the [Supabase Dashboard](https://app.supabase.io/)
2. Select your project (fxqoowlruissctsgbljk)
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Create the Required SQL Functions

Copy and paste the following SQL into the SQL Editor and click "Run":

```sql
-- Create exec_sql function to execute dynamic SQL
CREATE OR REPLACE FUNCTION public.exec_sql(sql_query text)
RETURNS void AS $$
BEGIN
  EXECUTE sql_query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_sql TO authenticated;
GRANT EXECUTE ON FUNCTION exec_sql TO anon;
GRANT EXECUTE ON FUNCTION exec_sql TO service_role;

-- Create exec_sql_exists function to check if a table exists
CREATE OR REPLACE FUNCTION public.exec_sql_exists(table_name text)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = table_name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_sql_exists TO authenticated;
GRANT EXECUTE ON FUNCTION exec_sql_exists TO anon;
GRANT EXECUTE ON FUNCTION exec_sql_exists TO service_role;

-- Create function to create user_profiles table
CREATE OR REPLACE FUNCTION public.create_user_profiles_table()
RETURNS void AS $$
BEGIN
  -- Check if table already exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_profiles'
  ) THEN
    -- Create the table
    CREATE TABLE public.user_profiles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      clerk_id TEXT UNIQUE NOT NULL,
      email TEXT NOT NULL,
      role TEXT DEFAULT 'guest',
      first_name TEXT,
      last_name TEXT,
      avatar_url TEXT,
      bio TEXT,
      phone TEXT,
      is_host BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Set up RLS (Row Level Security)
    ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Users can view their own profile"
      ON public.user_profiles
      FOR SELECT
      USING (auth.uid() = id);
      
    CREATE POLICY "Users can update their own profile"
      ON public.user_profiles
      FOR UPDATE
      USING (auth.uid() = id);
      
    -- Allow public read access for basic user info
    CREATE POLICY "Public read access for basic user info"
      ON public.user_profiles
      FOR SELECT
      USING (true);
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_user_profiles_table TO authenticated;
GRANT EXECUTE ON FUNCTION create_user_profiles_table TO anon;
GRANT EXECUTE ON FUNCTION create_user_profiles_table TO service_role;

-- Create function to create admin_users table
CREATE OR REPLACE FUNCTION public.create_admin_users_table()
RETURNS void AS $$
BEGIN
  -- Check if table already exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'admin_users'
  ) THEN
    -- Create the table
    CREATE TABLE public.admin_users (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      email TEXT UNIQUE NOT NULL,
      clerk_id TEXT UNIQUE,
      is_super_admin BOOLEAN DEFAULT false,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Set up RLS (Row Level Security)
    ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Admins can view all admin users"
      ON public.admin_users
      FOR SELECT
      USING (
        auth.uid() IN (SELECT id FROM public.admin_users)
      );
      
    CREATE POLICY "Only super admins can insert/update admin users"
      ON public.admin_users
      FOR ALL
      USING (
        auth.uid() IN (SELECT id FROM public.admin_users WHERE is_super_admin = true)
      );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_admin_users_table TO authenticated;
GRANT EXECUTE ON FUNCTION create_admin_users_table TO anon;
GRANT EXECUTE ON FUNCTION create_admin_users_table TO service_role;
```

## Step 3: Create the Required Tables

Now run the following SQL to create the tables:

```sql
-- Create user_profiles table
SELECT create_user_profiles_table();

-- Create admin_users table
SELECT create_admin_users_table();
```

## Step 4: Verify the Setup

Run the following SQL to verify that the tables were created:

```sql
-- Check if user_profiles table exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public'
  AND table_name = 'user_profiles'
);

-- Check if admin_users table exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public'
  AND table_name = 'admin_users'
);
```

Both queries should return `true` if the tables were created successfully.

## Troubleshooting

If you encounter any issues:

1. Check the Supabase logs for error messages
2. Make sure you have the correct permissions to create tables and functions
3. Try running each SQL statement separately to identify which one is failing

## Next Steps

After setting up the database, you should be able to use the HouseGoing application without getting stuck on the authentication screen. If you still encounter issues, please check the browser console for error messages and refer to the error handling section in this guide.
