{"name": "housegoing-email-server", "version": "1.0.0", "description": "Email notification server for HouseGoing platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node server.js"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "node-fetch": "^3.3.2", "nodemailer": "^6.9.7"}, "engines": {"node": ">=18.0.0"}, "keywords": ["email", "notifications", "nodemailer", "housegoing"], "author": "HouseGoing", "license": "MIT"}