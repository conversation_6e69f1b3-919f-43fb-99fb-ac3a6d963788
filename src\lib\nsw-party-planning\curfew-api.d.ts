export interface CurfewRecommendation {
  time_period: string;
  recommendation: string;
  priority: number;
}

export interface CurfewConfidence {
  level: string;
  score: number;
  factors?: string[];
}

export interface CurfewInfo {
  property_type: string;
  zone_code: string;
  zone_name: string;
  lga_name: string;
  event_date?: string;
  is_weekend?: boolean;
  is_holiday?: boolean;
  holiday_name?: string | null;
  curfew_start: string;
  curfew_end: string;
  bass_restriction_start?: string | null;
  bass_restriction_end?: string | null;
  outdoor_cutoff?: string | null;
  special_conditions?: string;
  confidence: CurfewConfidence;
  recommendations: CurfewRecommendation[];
}

export interface CurfewInfoParams {
  address: string;
  propertyType?: string | null;
  date?: string | null;
  zoneCode?: string | null;
  zoneName?: string | null;
  lgaName?: string | null;
}

export declare function detectPropertyType(address: string): Promise<string>;

export declare function getCurfewInfo(params: CurfewInfoParams): Promise<CurfewInfo>;

export declare function formatCurfewTime(timeStr: string): string;

export declare function getCurfewSummary(info: CurfewInfo): string;

export declare function getTopRecommendations(info: CurfewInfo, count?: number): string[];
