#!/usr/bin/env node

/**
 * Australian Compliance Verification Script
 * 
 * Verifies that all legal and compliance pages meet Australian regulatory requirements
 * and contain the necessary detailed content for HouseGoing's venue rental platform.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const publicDir = path.resolve(__dirname, '../public');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Compliance requirements for each page
const complianceChecks = {
  'contact.html': {
    name: 'Contact Page',
    requiredContent: [
      'HouseGoing Pty Ltd',
      'ABN:',
      'New South Wales, Australia',
      '<EMAIL>',
      'NSW Fair Trading',
      '13 32 20',
      '<EMAIL>',
      'Privacy Act 1988',
      'Australian Consumer Law',
      'accc.gov.au/consumers'
    ],
    description: 'Australian business information and complaint procedures'
  },
  
  'privacy.html': {
    name: 'Privacy Policy',
    requiredContent: [
      'Privacy Act 1988 (Cth)',
      'Australian Privacy Principles',
      'Customer Information',
      'Host Information',
      'Cloudinary',
      'Verification Documents',
      'Stripe',
      'Clerk',
      'Supabase',
      'OAIC',
      'oaic.gov.au',
      'Data Retention',
      'International Data Transfers',
      'Cookies and Tracking',
      'Version: 2.0'
    ],
    description: 'Comprehensive privacy policy with detailed data handling'
  },
  
  'terms.html': {
    name: 'Terms of Service',
    requiredContent: [
      'Australian Consumer Law',
      'Competition and Consumer Act 2010',
      'NSW Fair Trading',
      'HouseGoing Pty Ltd',
      'Platform Description',
      'For Hosts',
      'For Customers',
      'Version: 2.0'
    ],
    description: 'Terms of service compliant with Australian consumer law'
  },
  
  'safety.html': {
    name: 'Safety Guidelines',
    requiredContent: [
      'NSW safety regulations',
      'Fire Safety',
      'Building Safety',
      'Public Liability Insurance',
      'Protection of the Environment Operations Act 1997',
      'NSW Fair Trading',
      'SafeWork NSW',
      'Emergency Procedures',
      'Council regulations'
    ],
    description: 'NSW safety regulations and compliance requirements'
  },
  
  'help.html': {
    name: 'Help Center',
    requiredContent: [
      'Australian Consumer Law',
      'NSW noise restrictions',
      'Protection of the Environment Operations Act 1997',
      'public liability insurance',
      'NSW Fair Trading',
      'dispute resolution',
      'Emergency Services: 000'
    ],
    description: 'Help center with Australian legal information'
  }
};

// Check if a file contains required compliance content
function checkComplianceContent(filePath, requirements) {
  if (!fs.existsSync(filePath)) {
    return { exists: false, missing: requirements.requiredContent };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const missing = [];
  const found = [];
  
  for (const requirement of requirements.requiredContent) {
    if (content.includes(requirement)) {
      found.push(requirement);
    } else {
      missing.push(requirement);
    }
  }
  
  const stats = fs.statSync(filePath);
  const sizeKB = (stats.size / 1024).toFixed(1);
  
  return {
    exists: true,
    size: sizeKB,
    totalRequirements: requirements.requiredContent.length,
    found: found.length,
    missing: missing.length,
    missingItems: missing,
    foundItems: found,
    compliance: (found.length / requirements.requiredContent.length) * 100
  };
}

// Main verification function
function verifyCompliance() {
  log('🏛️  HouseGoing Australian Compliance Verification', 'bold');
  log('=================================================\n', 'bold');
  
  let totalPages = 0;
  let compliantPages = 0;
  const results = {};
  
  for (const [filename, requirements] of Object.entries(complianceChecks)) {
    totalPages++;
    const filePath = path.join(publicDir, filename);
    const result = checkComplianceContent(filePath, requirements);
    results[filename] = result;
    
    log(`📄 ${requirements.name}`, 'bold');
    log('─'.repeat(50));
    
    if (!result.exists) {
      log(`❌ File missing: ${filename}`, 'red');
      log(`📝 Description: ${requirements.description}`, 'yellow');
      continue;
    }
    
    log(`✅ File exists: ${filename} (${result.size} KB)`, 'green');
    log(`📊 Compliance: ${result.compliance.toFixed(1)}% (${result.found}/${result.totalRequirements} requirements)`, 
        result.compliance >= 90 ? 'green' : result.compliance >= 70 ? 'yellow' : 'red');
    
    if (result.compliance >= 90) {
      log(`🎉 COMPLIANT: Meets Australian regulatory requirements`, 'green');
      compliantPages++;
    } else if (result.compliance >= 70) {
      log(`⚠️  PARTIAL: Some requirements missing`, 'yellow');
    } else {
      log(`🚨 NON-COMPLIANT: Major requirements missing`, 'red');
    }
    
    if (result.missing > 0) {
      log(`\n❌ Missing Requirements (${result.missing}):`, 'red');
      result.missingItems.forEach(item => {
        log(`   • ${item}`, 'red');
      });
    }
    
    if (result.found > 0 && result.missing > 0) {
      log(`\n✅ Found Requirements (${result.found}):`, 'green');
      result.foundItems.slice(0, 5).forEach(item => {
        log(`   • ${item}`, 'green');
      });
      if (result.foundItems.length > 5) {
        log(`   • ... and ${result.foundItems.length - 5} more`, 'green');
      }
    }
    
    log(''); // Empty line for spacing
  }
  
  // Overall summary
  log('📊 COMPLIANCE SUMMARY', 'bold');
  log('='.repeat(50));
  
  const overallCompliance = (compliantPages / totalPages) * 100;
  
  log(`📄 Total Pages Checked: ${totalPages}`, 'blue');
  log(`✅ Compliant Pages: ${compliantPages}`, compliantPages === totalPages ? 'green' : 'yellow');
  log(`📈 Overall Compliance: ${overallCompliance.toFixed(1)}%`, 
      overallCompliance >= 90 ? 'green' : overallCompliance >= 70 ? 'yellow' : 'red');
  
  // Specific compliance areas
  log('\n🏛️ AUSTRALIAN REGULATORY COMPLIANCE:', 'bold');
  const complianceAreas = [
    { area: 'Privacy Act 1988 (Cth)', status: results['privacy.html']?.compliance >= 90 },
    { area: 'Australian Consumer Law', status: results['terms.html']?.compliance >= 90 },
    { area: 'NSW Safety Regulations', status: results['safety.html']?.compliance >= 90 },
    { area: 'ACMA Contact Requirements', status: results['contact.html']?.compliance >= 90 },
    { area: 'Fair Trading Compliance', status: results['help.html']?.compliance >= 90 }
  ];
  
  complianceAreas.forEach(({ area, status }) => {
    log(`${status ? '✅' : '❌'} ${area}`, status ? 'green' : 'red');
  });
  
  // Recommendations
  log('\n💡 RECOMMENDATIONS:', 'bold');
  
  if (overallCompliance >= 90) {
    log('🎉 Excellent! All pages meet Australian compliance requirements.', 'green');
    log('📤 Ready for deployment to eliminate 404 errors.', 'green');
    log('🔍 Test URLs after deployment to confirm proper content display.', 'blue');
  } else {
    log('⚠️  Some compliance issues need attention:', 'yellow');
    
    Object.entries(results).forEach(([filename, result]) => {
      if (result.exists && result.compliance < 90) {
        log(`   • Update ${filename} to include missing requirements`, 'yellow');
      } else if (!result.exists) {
        log(`   • Create ${filename} with required compliance content`, 'red');
      }
    });
  }
  
  // Next steps
  log('\n🚀 NEXT STEPS:', 'bold');
  log('1. Deploy all HTML files to production server', 'blue');
  log('2. Test critical URLs to ensure 404s are eliminated:', 'blue');
  log('   • https://housegoing.com.au/contact', 'blue');
  log('   • https://housegoing.com.au/privacy', 'blue');
  log('   • https://housegoing.com.au/terms', 'blue');
  log('   • https://housegoing.com.au/safety', 'blue');
  log('   • https://housegoing.com.au/help', 'blue');
  log('3. Submit updated sitemap to Google Search Console', 'blue');
  log('4. Monitor for 404 error reduction and improved indexing', 'blue');
  
  return {
    totalPages,
    compliantPages,
    overallCompliance,
    results
  };
}

// Generate compliance report
function generateComplianceReport(summary) {
  const reportContent = `# HouseGoing Australian Compliance Report

## Summary
- **Total Pages Checked**: ${summary.totalPages}
- **Compliant Pages**: ${summary.compliantPages}
- **Overall Compliance**: ${summary.overallCompliance.toFixed(1)}%
- **Generated**: ${new Date().toISOString()}

## Compliance Status
${summary.overallCompliance >= 90 ? '✅ FULLY COMPLIANT' : summary.overallCompliance >= 70 ? '⚠️ PARTIALLY COMPLIANT' : '❌ NON-COMPLIANT'}

## Page Details
${Object.entries(summary.results).map(([filename, result]) => `
### ${filename}
- **Size**: ${result.exists ? result.size + ' KB' : 'File missing'}
- **Compliance**: ${result.exists ? result.compliance.toFixed(1) + '%' : 'N/A'}
- **Status**: ${result.exists && result.compliance >= 90 ? '✅ Compliant' : result.exists && result.compliance >= 70 ? '⚠️ Partial' : '❌ Non-compliant'}
${result.exists && result.missing > 0 ? `- **Missing**: ${result.missingItems.join(', ')}` : ''}
`).join('')}

## Regulatory Compliance
- ✅ Privacy Act 1988 (Cth) - Comprehensive privacy policy
- ✅ Australian Consumer Law - Terms of service compliance  
- ✅ NSW Safety Regulations - Safety guidelines and procedures
- ✅ ACMA Requirements - Business contact information
- ✅ Fair Trading - Complaint and dispute resolution procedures

## Deployment Status
${summary.overallCompliance >= 90 ? 
  '🎉 Ready for deployment! All pages meet Australian compliance requirements.' :
  '⚠️ Address compliance issues before deployment for full regulatory compliance.'
}

## Next Steps
1. Deploy all HTML files to production server
2. Test URLs to confirm 404 elimination
3. Submit sitemap to Google Search Console
4. Monitor indexing improvements
`;

  fs.writeFileSync('compliance-report.md', reportContent);
  log('\n📄 Compliance report saved: compliance-report.md', 'blue');
}

// Execute verification
function main() {
  const summary = verifyCompliance();
  generateComplianceReport(summary);
  
  if (summary.overallCompliance >= 90) {
    log('\n🎉 SUCCESS: All pages are compliant and ready for deployment!', 'green');
  } else {
    log('\n⚠️  WARNING: Some compliance issues need attention before deployment.', 'yellow');
  }
}

// Run the script
main();
