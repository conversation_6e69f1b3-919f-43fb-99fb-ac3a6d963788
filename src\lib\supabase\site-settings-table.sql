-- Site settings table for HouseGoing

-- Table for storing site settings
CREATE TABLE IF NOT EXISTS site_settings (
  id INTEGER PRIMARY KEY CHECK (id = 1), -- Only one row allowed
  general JSONB NOT NULL DEFAULT '{}',
  appearance JSONB NOT NULL DEFAULT '{}',
  email JSONB NOT NULL DEFAULT '{}',
  integrations JSONB NOT NULL DEFAULT '{}',
  advanced JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings if not exists
INSERT INTO site_settings (id, general, appearance, email, integrations, advanced)
VALUES (
  1,
  '{
    "siteName": "HouseGoing",
    "siteDescription": "Find the perfect party venue",
    "contactEmail": "<EMAIL>",
    "supportPhone": "",
    "defaultCurrency": "AUD"
  }',
  '{
    "primaryColor": "#7c3aed",
    "secondaryColor": "#a78bfa",
    "logoUrl": "",
    "faviconUrl": "",
    "enableDarkMode": false
  }',
  '{
    "senderName": "HouseGoing",
    "senderEmail": "<EMAIL>",
    "enableEmailNotifications": true,
    "bookingConfirmationTemplate": "",
    "hostNotificationTemplate": ""
  }',
  '{
    "googleMapsApiKey": "",
    "stripePublicKey": "",
    "stripeSecretKey": "",
    "huggingFaceApiKey": "",
    "langChainApiKey": ""
  }',
  '{
    "enableCaching": true,
    "cacheLifetime": 3600,
    "debugMode": false,
    "maintenanceMode": false,
    "maintenanceMessage": "We are currently performing maintenance. Please check back soon."
  }'
)
ON CONFLICT (id) DO NOTHING;
