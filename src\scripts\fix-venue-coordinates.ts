/**
 * Migration script to fix coordinates for existing venues
 * Run this to ensure all existing venues have proper coordinates
 */

import { mockVenues } from '../data/mockVenues';
import { assignVenueCoordinates, validateVenueCoordinates, batchUpdateVenueCoordinates } from '../services/venue-coordinates';

/**
 * Audit all mock venues for coordinate accuracy
 */
export async function auditMockVenues() {
  console.log('🔍 Auditing mock venue coordinates...');
  
  const issues: Array<{
    venue: string;
    issue: string;
    current?: { lat: number; lng: number };
    suggested?: { lat: number; lng: number };
  }> = [];

  for (const venue of mockVenues) {
    console.log(`\n📍 Checking ${venue.name} (${venue.location.suburb})`);
    
    const venueLocation = {
      address: venue.location.address,
      suburb: venue.location.suburb,
      postcode: venue.location.postcode,
      state: venue.location.state,
      coordinates: {
        latitude: venue.location.latitude,
        longitude: venue.location.longitude,
        source: 'manual' as const,
        accuracy: 'exact' as const
      }
    };

    try {
      const validation = await validateVenueCoordinates({
        id: venue.id,
        location: venueLocation
      });

      if (!validation.isValid || validation.needsUpdate) {
        const issue = {
          venue: `${venue.name} (${venue.location.suburb})`,
          issue: validation.isValid ? 'Needs coordinate update' : 'Invalid coordinates',
          current: { lat: venue.location.latitude, lng: venue.location.longitude },
          suggested: validation.suggestedCoordinates ? {
            lat: validation.suggestedCoordinates.latitude,
            lng: validation.suggestedCoordinates.longitude
          } : undefined
        };
        
        issues.push(issue);
        console.log(`⚠️ Issue found: ${issue.issue}`);
        if (issue.suggested) {
          console.log(`   Current: ${issue.current?.lat}, ${issue.current?.lng}`);
          console.log(`   Suggested: ${issue.suggested.lat}, ${issue.suggested.lng}`);
        }
      } else {
        console.log('✅ Coordinates are valid');
      }
    } catch (error) {
      console.error(`❌ Error validating ${venue.name}:`, error);
      issues.push({
        venue: `${venue.name} (${venue.location.suburb})`,
        issue: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  console.log('\n📊 AUDIT SUMMARY');
  console.log(`Total venues checked: ${mockVenues.length}`);
  console.log(`Issues found: ${issues.length}`);
  
  if (issues.length > 0) {
    console.log('\n🚨 ISSUES FOUND:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.venue}: ${issue.issue}`);
      if (issue.current && issue.suggested) {
        console.log(`   Current: ${issue.current.lat}, ${issue.current.lng}`);
        console.log(`   Suggested: ${issue.suggested.lat}, ${issue.suggested.lng}`);
      }
    });
  } else {
    console.log('✅ All venues have valid coordinates!');
  }

  return issues;
}

/**
 * Generate coordinate fixes for mock venues
 */
export async function generateMockVenueFixes() {
  console.log('🔧 Generating coordinate fixes for mock venues...');
  
  const fixes: Array<{
    venueId: string;
    venueName: string;
    suburb: string;
    currentCoords: { lat: number; lng: number };
    newCoords: { lat: number; lng: number };
    source: string;
  }> = [];

  for (const venue of mockVenues) {
    try {
      const newCoords = await assignVenueCoordinates({
        address: venue.location.address,
        suburb: venue.location.suburb,
        postcode: venue.location.postcode,
        state: venue.location.state
      });

      const currentLat = venue.location.latitude;
      const currentLng = venue.location.longitude;
      const newLat = newCoords.latitude;
      const newLng = newCoords.longitude;

      // Check if coordinates are different (more than 0.001 degrees difference)
      const latDiff = Math.abs(currentLat - newLat);
      const lngDiff = Math.abs(currentLng - newLng);

      if (latDiff > 0.001 || lngDiff > 0.001) {
        fixes.push({
          venueId: venue.id,
          venueName: venue.name,
          suburb: venue.location.suburb,
          currentCoords: { lat: currentLat, lng: currentLng },
          newCoords: { lat: newLat, lng: newLng },
          source: newCoords.source
        });

        console.log(`🔧 ${venue.name} (${venue.location.suburb})`);
        console.log(`   Current: ${currentLat}, ${currentLng}`);
        console.log(`   New: ${newLat}, ${newLng} (${newCoords.source})`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${venue.name}:`, error);
    }
  }

  console.log(`\n📊 Found ${fixes.length} venues that need coordinate updates`);
  return fixes;
}

/**
 * Generate TypeScript code to update mock venue coordinates
 */
export async function generateMockVenueUpdateCode() {
  console.log('📝 Generating TypeScript update code...');
  
  const fixes = await generateMockVenueFixes();
  
  if (fixes.length === 0) {
    console.log('✅ No coordinate fixes needed!');
    return '';
  }

  let updateCode = '// Coordinate fixes for mock venues\n';
  updateCode += '// Generated by fix-venue-coordinates.ts\n\n';

  fixes.forEach(fix => {
    updateCode += `// ${fix.venueName} (${fix.suburb})\n`;
    updateCode += `// Current: ${fix.currentCoords.lat}, ${fix.currentCoords.lng}\n`;
    updateCode += `// New: ${fix.newCoords.lat}, ${fix.newCoords.lng} (${fix.source})\n`;
    updateCode += `venues.find(v => v.id === '${fix.venueId}')!.location.latitude = ${fix.newCoords.lat};\n`;
    updateCode += `venues.find(v => v.id === '${fix.venueId}')!.location.longitude = ${fix.newCoords.lng};\n\n`;
  });

  console.log('📋 Update code generated:');
  console.log(updateCode);
  
  return updateCode;
}

/**
 * Test coordinate assignment for all suburbs in mock data
 */
export async function testAllSuburbCoordinates() {
  console.log('🧪 Testing coordinate assignment for all suburbs...');
  
  const suburbs = [...new Set(mockVenues.map(v => v.location.suburb))];
  console.log(`Found ${suburbs.length} unique suburbs: ${suburbs.join(', ')}`);

  const results: Array<{
    suburb: string;
    success: boolean;
    coordinates?: { lat: number; lng: number };
    source?: string;
    error?: string;
  }> = [];

  for (const suburb of suburbs) {
    console.log(`\n🔍 Testing ${suburb}...`);
    
    try {
      const coords = await assignVenueCoordinates({
        address: `Test Address, ${suburb} NSW 2000`,
        suburb,
        postcode: '2000',
        state: 'NSW'
      });

      results.push({
        suburb,
        success: true,
        coordinates: { lat: coords.latitude, lng: coords.longitude },
        source: coords.source
      });

      console.log(`✅ ${suburb}: ${coords.latitude}, ${coords.longitude} (${coords.source})`);
    } catch (error) {
      results.push({
        suburb,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      console.log(`❌ ${suburb}: Failed - ${error}`);
    }
  }

  console.log('\n📊 SUBURB COORDINATE TEST SUMMARY');
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log(`✅ Successful: ${successful.length}/${suburbs.length}`);
  console.log(`❌ Failed: ${failed.length}/${suburbs.length}`);

  if (failed.length > 0) {
    console.log('\n🚨 FAILED SUBURBS:');
    failed.forEach(result => {
      console.log(`- ${result.suburb}: ${result.error}`);
    });
  }

  return results;
}

/**
 * Main function to run all coordinate fixes
 */
export async function runCoordinateFixes() {
  console.log('🚀 Running comprehensive coordinate fixes...\n');

  try {
    // Step 1: Audit current state
    console.log('STEP 1: Auditing current venue coordinates');
    const auditResults = await auditMockVenues();

    // Step 2: Test suburb coordinate assignment
    console.log('\nSTEP 2: Testing suburb coordinate assignment');
    const suburbResults = await testAllSuburbCoordinates();

    // Step 3: Generate fixes
    console.log('\nSTEP 3: Generating coordinate fixes');
    const updateCode = await generateMockVenueUpdateCode();

    console.log('\n✅ COORDINATE FIX ANALYSIS COMPLETE');
    console.log(`- Venues audited: ${mockVenues.length}`);
    console.log(`- Issues found: ${auditResults.length}`);
    console.log(`- Suburbs tested: ${suburbResults.length}`);
    console.log(`- Successful suburb lookups: ${suburbResults.filter(r => r.success).length}`);

    if (updateCode) {
      console.log('\n📝 Update code has been generated. Apply these fixes to mockVenues.ts');
    }

  } catch (error) {
    console.error('❌ Error running coordinate fixes:', error);
  }
}

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).auditMockVenues = auditMockVenues;
  (window as any).testAllSuburbCoordinates = testAllSuburbCoordinates;
  (window as any).generateMockVenueFixes = generateMockVenueFixes;
  (window as any).runCoordinateFixes = runCoordinateFixes;
}
