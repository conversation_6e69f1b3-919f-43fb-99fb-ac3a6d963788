@echo off
echo HouseGoing Sitemap Update Script
echo =================================

echo Generating fresh sitemap...
node scripts/generate-sitemap.js

if %errorlevel% neq 0 (
    echo ERROR: Failed to generate sitemap
    pause
    exit /b 1
)

echo.
echo Running SEO analysis...
node scripts/seo-monitor.js

echo.
echo ========================================
echo Sitemap update completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Submit updated sitemap to Google Search Console
echo 2. Submit updated sitemap to Bing Webmaster Tools
echo 3. Monitor indexing status
echo.
echo Sitemap URLs:
echo - https://housegoing.com.au/sitemap.xml
echo - https://housegoing.com.au/sitemap_index.xml
echo.
pause
