/**
 * <PERSON><PERSON><PERSON><PERSON> integration for HouseGoing
 */
import { createHuggingFaceModel, create<PERSON>hain, createTracer } from './models';
import { createMemoryWithHistory } from './memory';
import { createHostAssistantPrompt, createSalesAssistantPrompt, createVenueAssistantPrompt } from './prompts';

// Mock venue data for recommendations
const mockVenues = [
  {
    name: 'Harbour View Terrace',
    description: 'Stunning waterfront venue with panoramic harbour views and modern amenities.',
    capacity: 80,
    price: 250,
    features: ['Waterfront location', 'Outdoor deck', 'Sound system', 'Bar area'],
    location: 'Sydney Harbour',
    byo: true
  },
  {
    name: 'The Garden Pavilion',
    description: 'Elegant garden venue surrounded by lush greenery and flowering plants.',
    capacity: 120,
    price: 320,
    features: ['Garden setting', 'Marquee option', 'Catering kitchen', 'Parking'],
    location: 'Eastern Suburbs',
    byo: true
  },
  {
    name: 'Urban Loft Space',
    description: 'Industrial chic warehouse conversion with exposed brick and high ceilings.',
    capacity: 150,
    price: 380,
    features: ['Industrial design', 'DJ booth', 'Lighting rig', 'Rooftop access'],
    location: 'Inner West',
    byo: false
  },
  {
    name: 'Beachside Cabana',
    description: 'Relaxed beach venue with direct sand access and coastal vibes.',
    capacity: 60,
    price: 200,
    features: ['Beach access', 'BBQ facilities', 'Outdoor shower', 'Covered area'],
    location: 'Northern Beaches',
    byo: true
  },
  {
    name: 'Heritage Hall',
    description: 'Stunning restored heritage building with classic architecture and modern facilities.',
    capacity: 200,
    price: 450,
    features: ['Historic building', 'Grand staircase', 'Chandeliers', 'Dance floor'],
    location: 'CBD',
    byo: false
  }
];

/**
 * Format venues as a string for the prompt
 * @param {Array} venues - Array of venue objects
 * @returns {string} - Formatted venue string
 */
function formatVenues(venues) {
  return venues.map(venue => {
    return `- ${venue.name}: ${venue.description} Capacity: ${venue.capacity}, Price: $${venue.price}/hour, Location: ${venue.location}, Features: ${venue.features.join(', ')}`;
  }).join('\n');
}

/**
 * Generate a host assistant response using LangChain
 * @param {string} message - The user message
 * @param {Array} history - The conversation history
 * @param {string} context - The context of the conversation
 * @returns {Promise<string>} - The generated response
 */
export async function generateHostAssistantResponse(message, history = [], context = 'general') {
  try {
    // Create the model
    const model = createHuggingFaceModel();

    // Create the prompt template - now async
    const promptTemplate = await createHostAssistantPrompt();

    // Create the chain
    const chain = createChain(promptTemplate, model);

    // Create memory with history
    const memory = createMemoryWithHistory(history);

    // Create tracer for monitoring
    const tracer = createTracer();

    // Generate the response
    const response = await chain.invoke(
      {
        input: message,
        context: context
      },
      {
        callbacks: [tracer]
      }
    );

    return response;
  } catch (error) {
    console.error('Error generating host assistant response:', error);
    return "I'm sorry, I encountered an error. Please try again in a moment.";
  }
}

/**
 * Generate a sales assistant response using LangChain
 * @param {string} message - The user message
 * @param {Array} history - The conversation history
 * @param {Array} venueData - Optional array of venue data
 * @returns {Promise<string>} - The generated response
 */
export async function generateSalesAssistantResponse(message, history = [], venueData = []) {
  try {
    // Create the model
    const model = createHuggingFaceModel();

    // Create the prompt template - now async
    const promptTemplate = await createSalesAssistantPrompt();

    // Create the chain
    const chain = createChain(promptTemplate, model);

    // Create memory with history
    const memory = createMemoryWithHistory(history);

    // Create tracer for monitoring
    const tracer = createTracer();

    // Use provided venue data or fallback to mock data
    const venues = venueData.length > 0 ? venueData : mockVenues;

    // Generate the response
    const response = await chain.invoke(
      {
        input: message,
        venues: formatVenues(venues)
      },
      {
        callbacks: [tracer]
      }
    );

    return response;
  } catch (error) {
    console.error('Error generating sales assistant response:', error);
    return "I'm sorry, I encountered an error. Please try again in a moment.";
  }
}

/**
 * Generate a venue assistant response using LangChain
 * @param {string} message - The user message
 * @param {Array} history - The conversation history
 * @param {Array} venueData - Optional array of venue data
 * @returns {Promise<string>} - The generated response
 */
export async function generateVenueAssistantResponse(message, history = [], venueData = []) {
  try {
    // Create the model
    const model = createHuggingFaceModel();

    // Create the prompt template
    const promptTemplate = createVenueAssistantPrompt();

    // Create the chain
    const chain = createChain(promptTemplate, model);

    // Create memory with history
    const memory = createMemoryWithHistory(history);

    // Create tracer for monitoring
    const tracer = createTracer();

    // Use provided venue data or fallback to mock data
    const venues = venueData.length > 0 ? venueData : mockVenues;

    // Generate the response
    const response = await chain.invoke(
      {
        input: message,
        venues: formatVenues(venues)
      },
      {
        callbacks: [tracer]
      }
    );

    return response;
  } catch (error) {
    console.error('Error generating venue assistant response:', error);
    return "I'm sorry, I encountered an error. Please try again in a moment.";
  }
}
