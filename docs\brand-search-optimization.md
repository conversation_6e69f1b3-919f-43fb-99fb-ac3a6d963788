# HouseGoing Brand Search Optimization

This document outlines steps to improve HouseGoing's visibility when users search for "housegoing" without "au".

## 1. Meta Title and Description Optimization

Update your main page title to emphasize "HouseGoing" as a standalone brand:

```html
<title>HouseGoing - Party Venues & Event Spaces | Find Perfect Venues in Australia</title>
<meta name="description" content="HouseGoing is Australia's premier venue booking platform. Find and book the perfect party or event venue with noise restriction checks and verified hosts.">
```

## 2. Fix Canonical Issues

The Search Console shows 47 pages with canonical tag problems. This is likely causing Google to index the wrong version of your pages.

1. Use absolute URLs for all canonical tags
2. Ensure every page has exactly ONE canonical tag pointing to the preferred version
3. Remove any conflicting canonical tags
4. Use consistent URL patterns (with or without trailing slash)

## 3. Add Structured Data Markup

Add organization and website schema to increase brand authority:

```html
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "HouseGoing",
  "url": "https://housegoing.com.au",
  "logo": "https://housegoing.com.au/images/housegoing-logo.png",
  "sameAs": [
    "https://facebook.com/housegoinghq",
    "https://instagram.com/housegoinghq",
    "https://linkedin.com/company/housegoing",
    "https://twitter.com/housegoinghq"
  ]
}
</script>
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "HouseGoing",
  "url": "https://housegoing.com.au",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://housegoing.com.au/find-venues?search={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>
```

## 4. Consolidate Similar Pages

Having multiple similar pages can dilute your SEO strength. Review and consolidate:

1. Redirect test/duplicate pages to their main versions
2. Update all internal links to point to canonical URLs
3. Set up 301 redirects for any outdated URLs

## 5. Improve Internal Linking

1. Make sure "HouseGoing" appears in anchor text across the site
2. Link to your homepage with "HouseGoing" as the anchor text (not just "Home")
3. Create a brand page at /about-housegoing that explains your brand story and links to key pages

## 6. Fix Soft 404s

For the 30 soft 404 pages:
1. Add substantial content to make them valuable pages
2. Implement proper 404 status codes for genuinely missing pages
3. Set up 301 redirects to relevant existing pages

## 7. External Brand Mentions

1. Create social media profiles with the name "HouseGoing"
2. Set up a Google Business Profile for "HouseGoing"
3. Submit your site to relevant Australian business directories

## 8. Submit Request for Reconsideration

After implementing these changes:
1. Go to Google Search Console
2. Submit the main site for indexing
3. Request indexing for key pages
4. Monitor results over the next 2-4 weeks
