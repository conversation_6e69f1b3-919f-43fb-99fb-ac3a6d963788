  // Render Additional Information section
  const renderAdditionalInfo = () => (
    <div className="border-t pt-6">
      <h2 className="text-xl font-bold mb-4">Additional Information</h2>
      
      <div className="mb-6">
        <label className="block mb-2 font-medium">Accessibility Features</label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="accessibility-wheelchair"
                type="checkbox"
                checked={formData.accessibilityFeatures.includes('wheelchair')}
                onChange={() => {
                  const features = [...formData.accessibilityFeatures];
                  if (features.includes('wheelchair')) {
                    setFormData({...formData, accessibilityFeatures: features.filter(f => f !== 'wheelchair')});
                  } else {
                    setFormData({...formData, accessibilityFeatures: [...features, 'wheelchair']});
                  }
                }}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="accessibility-wheelchair" className="font-medium text-gray-700">
                Wheelchair Accessible
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="accessibility-elevator"
                type="checkbox"
                checked={formData.accessibilityFeatures.includes('elevator')}
                onChange={() => {
                  const features = [...formData.accessibilityFeatures];
                  if (features.includes('elevator')) {
                    setFormData({...formData, accessibilityFeatures: features.filter(f => f !== 'elevator')});
                  } else {
                    setFormData({...formData, accessibilityFeatures: [...features, 'elevator']});
                  }
                }}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="accessibility-elevator" className="font-medium text-gray-700">
                Elevator Access
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="accessibility-bathroom"
                type="checkbox"
                checked={formData.accessibilityFeatures.includes('accessible-bathroom')}
                onChange={() => {
                  const features = [...formData.accessibilityFeatures];
                  if (features.includes('accessible-bathroom')) {
                    setFormData({...formData, accessibilityFeatures: features.filter(f => f !== 'accessible-bathroom')});
                  } else {
                    setFormData({...formData, accessibilityFeatures: [...features, 'accessible-bathroom']});
                  }
                }}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="accessibility-bathroom" className="font-medium text-gray-700">
                Accessible Bathroom
              </label>
            </div>
          </div>
          
          <div className="relative flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="accessibility-parking"
                type="checkbox"
                checked={formData.accessibilityFeatures.includes('accessible-parking')}
                onChange={() => {
                  const features = [...formData.accessibilityFeatures];
                  if (features.includes('accessible-parking')) {
                    setFormData({...formData, accessibilityFeatures: features.filter(f => f !== 'accessible-parking')});
                  } else {
                    setFormData({...formData, accessibilityFeatures: [...features, 'accessible-parking']});
                  }
                }}
                className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="accessibility-parking" className="font-medium text-gray-700">
                Accessible Parking
              </label>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <label className="block mb-2 font-medium">Catering Options</label>
        <textarea
          value={formData.cateringOptions}
          onChange={(e) => setFormData({...formData, cateringOptions: e.target.value})}
          className="w-full p-2 border rounded"
          rows={3}
          placeholder="Describe catering options (e.g., in-house catering available, external caterers allowed, etc.)"
        />
      </div>
      
      <div className="mb-6">
        <label className="block mb-2 font-medium">Equipment Provided</label>
        <textarea
          value={formData.equipmentProvided}
          onChange={(e) => setFormData({...formData, equipmentProvided: e.target.value})}
          className="w-full p-2 border rounded"
          rows={3}
          placeholder="List equipment provided (e.g., tables, chairs, AV equipment, etc.)"
        />
      </div>
      
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <input
            id="staff-available"
            type="checkbox"
            checked={formData.staffAvailable}
            onChange={(e) => setFormData({...formData, staffAvailable: e.target.checked})}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="staff-available" className="ml-2 block text-sm font-medium text-gray-700">
            Staff available during events
          </label>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block mb-2 font-medium">Setup Time (hours)</label>
          <input
            type="number"
            min="0"
            step="0.5"
            value={formData.setupTime || ''}
            onChange={(e) => setFormData({...formData, setupTime: parseFloat(e.target.value) || 0})}
            className="w-full p-2 border rounded"
            placeholder="e.g. 1.5"
          />
          <p className="text-xs text-gray-500 mt-1">Time allowed for setup before event (in hours)</p>
        </div>
        
        <div>
          <label className="block mb-2 font-medium">Cleanup Time (hours)</label>
          <input
            type="number"
            min="0"
            step="0.5"
            value={formData.cleanupTime || ''}
            onChange={(e) => setFormData({...formData, cleanupTime: parseFloat(e.target.value) || 0})}
            className="w-full p-2 border rounded"
            placeholder="e.g. 1"
          />
          <p className="text-xs text-gray-500 mt-1">Time allowed for cleanup after event (in hours)</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">
        {isEditing ? 'Edit Venue' : 'Add New Venue'}
      </h1>

      {error && <div className="text-red-500 mb-4">{error}</div>}
      {success && (
        <div className="bg-green-100 text-green-700 p-4 mb-4 rounded flex flex-col gap-2">
          <p className="font-semibold">Venue submitted successfully!</p>
          <p>Thank you for listing your venue with HouseGoing. Our team will review your submission and get back to you shortly.</p>
          <p>You'll be redirected to your properties dashboard in a moment...</p>
        </div>
      )}

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {formSteps.map((step, index) => (
            <div 
              key={step.id} 
              className="flex flex-col items-center cursor-pointer"
              onClick={() => goToStep(index)}
            >
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  formData.currentStep >= index ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
              <span className="text-xs mt-1 hidden md:block">{step.title}</span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute inset-0 flex items-center" aria-hidden="true">
            <div className="w-full border-t border-gray-300"></div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Render current step content based on formData.currentStep */}
        {formData.currentStep === 0 && renderBasicInfo()}
        {formData.currentStep === 1 && renderVenueDetails()}
        {formData.currentStep === 2 && renderAmenitiesFeatures()}
        {formData.currentStep === 3 && renderHouseRules()}
        {formData.currentStep === 4 && renderInsuranceCompliance()}
        {formData.currentStep === 5 && renderLicensesPermits()}
        {formData.currentStep === 6 && renderAdditionalInfo()}
        {formData.currentStep === 7 && renderPhotos()}
        {formData.currentStep === 8 && renderBankDetails()}
        {formData.currentStep === 9 && renderReview()}
        
        {/* Step Navigation */}
        {renderStepNavigation()}
      </form>
    </div>
  );
}
