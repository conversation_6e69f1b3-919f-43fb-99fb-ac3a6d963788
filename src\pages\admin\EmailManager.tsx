/**
 * Email Manager
 * 
 * Manages pending emails and provides manual sending options
 */

import React, { useState, useEffect } from 'react';
import { 
  Mail, 
  Send, 
  Copy, 
  ExternalLink, 
  Trash2, 
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface PendingEmail {
  timestamp: string;
  type: 'approval' | 'rejection';
  to: string;
  subject: string;
  content: string;
  property: string;
  status: 'pending_manual_send' | 'sent' | 'failed';
}

export default function EmailManager() {
  const [pendingEmails, setPendingEmails] = useState<PendingEmail[]>([]);
  const [selectedEmail, setSelectedEmail] = useState<PendingEmail | null>(null);

  useEffect(() => {
    loadPendingEmails();
  }, []);

  const loadPendingEmails = () => {
    const emails = JSON.parse(localStorage.getItem('pendingEmails') || '[]');
    setPendingEmails(emails);
  };

  const markEmailAsSent = (index: number) => {
    const updatedEmails = [...pendingEmails];
    updatedEmails[index].status = 'sent';
    setPendingEmails(updatedEmails);
    localStorage.setItem('pendingEmails', JSON.stringify(updatedEmails));
  };

  const deleteEmail = (index: number) => {
    const updatedEmails = pendingEmails.filter((_, i) => i !== index);
    setPendingEmails(updatedEmails);
    localStorage.setItem('pendingEmails', JSON.stringify(updatedEmails));
  };

  const copyEmailContent = (email: PendingEmail) => {
    navigator.clipboard.writeText(email.content);
    alert('Email content copied to clipboard!');
  };

  const openMailtoLink = (email: PendingEmail) => {
    const mailtoLink = `mailto:${email.to}?subject=${encodeURIComponent(email.subject)}&body=${encodeURIComponent(email.content)}`;
    window.open(mailtoLink, '_blank');
  };

  const clearAllEmails = () => {
    if (confirm('Are you sure you want to clear all email logs?')) {
      setPendingEmails([]);
      localStorage.removeItem('pendingEmails');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default: return <Clock className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800 border-green-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Email Manager</h1>
            <p className="text-gray-600 mt-1">Manage pending property notification emails</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={loadPendingEmails}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
            >
              <Mail className="w-4 h-4" />
              Refresh
            </button>
            <button
              onClick={clearAllEmails}
              className="inline-flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
            >
              <Trash2 className="w-4 h-4" />
              Clear All
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {pendingEmails.filter(e => e.status === 'pending_manual_send').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Sent</p>
                <p className="text-2xl font-bold text-green-600">
                  {pendingEmails.filter(e => e.status === 'sent').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Mail className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total</p>
                <p className="text-2xl font-bold text-blue-600">{pendingEmails.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Email List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Pending Emails</h3>
            <p className="text-sm text-gray-600 mt-1">
              These emails need to be sent manually to property owners
            </p>
          </div>

          {pendingEmails.length === 0 ? (
            <div className="p-12 text-center">
              <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No pending emails</h3>
              <p className="text-gray-500">
                When property approvals/rejections are processed, emails will appear here for manual sending.
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {pendingEmails.map((email, index) => (
                <div key={index} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">{email.subject}</h4>
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(email.status)}`}>
                          {getStatusIcon(email.status)}
                          {email.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">
                            <strong>To:</strong> {email.to}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Property:</strong> {email.property}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">
                            <strong>Type:</strong> {email.type.charAt(0).toUpperCase() + email.type.slice(1)}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Created:</strong> {new Date(email.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>

                      {selectedEmail === email && (
                        <div className="bg-gray-50 rounded-lg p-4 mb-4">
                          <h5 className="font-medium text-gray-900 mb-2">Email Content:</h5>
                          <pre className="text-sm text-gray-700 whitespace-pre-wrap font-sans">
                            {email.content}
                          </pre>
                        </div>
                      )}
                    </div>

                    <div className="ml-6 flex flex-col gap-2">
                      <button
                        onClick={() => setSelectedEmail(selectedEmail === email ? null : email)}
                        className="inline-flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 rounded-md transition-colors"
                      >
                        {selectedEmail === email ? 'Hide' : 'Preview'}
                      </button>

                      <button
                        onClick={() => openMailtoLink(email)}
                        className="inline-flex items-center gap-2 px-3 py-1 text-sm text-blue-600 hover:text-blue-700 border border-blue-300 hover:border-blue-400 rounded-md transition-colors"
                      >
                        <ExternalLink className="w-3 h-3" />
                        Send Email
                      </button>

                      <button
                        onClick={() => copyEmailContent(email)}
                        className="inline-flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-700 border border-gray-300 hover:border-gray-400 rounded-md transition-colors"
                      >
                        <Copy className="w-3 h-3" />
                        Copy
                      </button>

                      {email.status === 'pending_manual_send' && (
                        <button
                          onClick={() => markEmailAsSent(index)}
                          className="inline-flex items-center gap-2 px-3 py-1 text-sm text-green-600 hover:text-green-700 border border-green-300 hover:border-green-400 rounded-md transition-colors"
                        >
                          <CheckCircle className="w-3 h-3" />
                          Mark Sent
                        </button>
                      )}

                      <button
                        onClick={() => deleteEmail(index)}
                        className="inline-flex items-center gap-2 px-3 py-1 text-sm text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 rounded-md transition-colors"
                      >
                        <Trash2 className="w-3 h-3" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">📧 How to Send Emails Manually</h3>
          <div className="space-y-2 text-sm text-blue-800">
            <p><strong>1. Click "Send Email"</strong> - Opens your default email client with pre-filled content</p>
            <p><strong>2. Review and send</strong> - Check the email content and send from your email client</p>
            <p><strong>3. Mark as sent</strong> - Click "Mark Sent" to update the status</p>
            <p><strong>Alternative:</strong> Click "Copy" to copy the email content and paste it into any email client</p>
          </div>
        </div>
      </div>
    </div>
  );
}
