import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth, useUser } from '@clerk/clerk-react';
import { getSupabaseClient } from '../../lib/supabase-client';

export default function SimpleOAuthCallback() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [status, setStatus] = useState('Processing authentication...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      console.log('SimpleOAuthCallback: Starting process');
      console.log('SimpleOAuthCallback: Auth state:', { isLoaded, isSignedIn, hasUser: !!user });
      console.log('SimpleOAuthCallback: URL:', window.location.href);

      // Wait for Clerk to load
      if (!isLoaded) {
        console.log('SimpleOAuthCallback: Waiting for Clerk to load...');
        setStatus('Loading...');
        return;
      }

      // Check for OAuth errors
      const urlParams = new URLSearchParams(location.search);
      const oauthError = urlParams.get('error');
      if (oauthError) {
        console.error('SimpleOAuthCallback: OAuth error:', oauthError);
        setError(`Authentication failed: ${oauthError}`);
        setTimeout(() => navigate('/sign-in'), 3000);
        return;
      }

      // If not signed in, redirect to sign in
      if (!isSignedIn || !user) {
        console.log('SimpleOAuthCallback: Not signed in, redirecting to sign in');
        setError('Authentication failed. Please try again.');
        setTimeout(() => navigate('/sign-in'), 2000);
        return;
      }

      // User is signed in, determine redirect path
      console.log('SimpleOAuthCallback: User is signed in, processing...');
      setStatus('Setting up your account...');

      const userTypeFromUrl = urlParams.get('userType');
      const isHostUser = userTypeFromUrl === 'host';
      const redirectPath = isHostUser ? '/host/dashboard' : '/my-account';

      console.log('SimpleOAuthCallback: User type:', { userTypeFromUrl, isHostUser });
      console.log('SimpleOAuthCallback: Redirecting to:', redirectPath);

      // Sync with Supabase (non-blocking)
      try {
        const supabase = getSupabaseClient();
        const userData = {
          clerk_id: user.id,
          email: user.primaryEmailAddress?.emailAddress || user.emailAddresses[0]?.emailAddress,
          first_name: user.firstName,
          last_name: user.lastName,
          avatar_url: user.imageUrl,
          role: isHostUser ? 'host' : 'guest',
          is_host: isHostUser,
          updated_at: new Date().toISOString()
        };

        const { error: supabaseError } = await supabase
          .from('user_profiles')
          .upsert(userData, { onConflict: 'clerk_id' });

        if (supabaseError) {
          console.warn('SimpleOAuthCallback: Supabase sync failed:', supabaseError);
        } else {
          console.log('SimpleOAuthCallback: Supabase sync successful');
        }
      } catch (syncError) {
        console.warn('SimpleOAuthCallback: Supabase sync error:', syncError);
      }

      // Clear any localStorage flags
      localStorage.removeItem('registering_as_host');
      localStorage.removeItem('user_type');
      localStorage.removeItem('auth_user_type');

      // Redirect
      setStatus('Redirecting...');
      setTimeout(() => {
        navigate(redirectPath, { replace: true });
      }, 1000);
    };

    processCallback();
  }, [isLoaded, isSignedIn, user, navigate, location.search]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <div className="text-red-600 text-xl mb-4">❌</div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Authentication Failed</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <p className="text-sm text-gray-500">Redirecting to sign in...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold text-gray-900 mb-2">Almost There!</h1>
        <p className="text-gray-600 mb-4">{status}</p>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
        </div>
      </div>
    </div>
  );
}
