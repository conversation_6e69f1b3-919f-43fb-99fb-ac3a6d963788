-- Add clerk_id column to user_profiles if not exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles'
    AND column_name = 'clerk_id'
  ) THEN
    ALTER TABLE public.user_profiles ADD COLUMN clerk_id TEXT;
    CREATE INDEX idx_user_profiles_clerk_id ON public.user_profiles(clerk_id);
  END IF;
END
$$;

-- Update RLS policies for user_profiles table to use clerk_id
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;

-- Create more permissive policies for development/testing
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (true);  -- Allow all authenticated users to select profiles

CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  USING (true);  -- For testing, allow all updates (tighten this later)

CREATE POLICY "Users can insert their own profile"
  ON public.user_profiles
  FOR INSERT
  WITH CHECK (true);  -- For testing, allow all inserts (tighten this later)
