# Clean up redundant email server files
$filesToBackup = @(
    "email-server.js",
    "run-email-server.js",
    "start-email-server.js"
)

# Create backups directory if it doesn't exist
$backupDir = ".\email-server-backup"
if (-not (Test-Path -Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir | Out-Null
    Write-Host "Created backup directory: $backupDir"
}

# Move files to backup directory
foreach ($file in $filesToBackup) {
    if (Test-Path -Path ".\$file") {
        Move-Item -Path ".\$file" -Destination "$backupDir\$file" -Force
        Write-Host "Moved $file to backup directory"
    } else {
        Write-Host "File $file not found, skipping"
    }
}

Write-Host "`nAll redundant email server files have been moved to $backupDir"
Write-Host "The active email implementation in server/api/send-email.js has been preserved."
