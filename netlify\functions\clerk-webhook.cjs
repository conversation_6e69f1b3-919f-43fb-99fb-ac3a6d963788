const { Webhook } = require('svix')
const { createClient } = require('@supabase/supabase-js')

// Ensure Supabase URL and Service Role Key are correctly referenced
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Debug logs for environment variables
console.log('Supabase URL:', supabaseUrl);
console.log('Service Role Key:', supabaseKey);

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

exports.handler = async (event, context) => {
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET
  const wh = new Webhook(WEBHOOK_SECRET)

  let evt

  try {
    evt = wh.verify(event.body, {
      'svix-id': event.headers['svix-id'],
      'svix-timestamp': event.headers['svix-timestamp'],
      'svix-signature': event.headers['svix-signature'],
    })
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Invalid signature' })
    }
  }

  const { type, data } = evt

  // Debug log for incoming payload
  console.log('Received payload:', evt)

  if (type === 'user.created' || type === 'user.updated') {
    const clerk_id = data.id;
    const email = data.email_addresses?.[0]?.email_address || null;
    const first_name = data.first_name || null;
    const last_name = data.last_name || null;
    const avatar_url = data.image_url || null;
    // Extract data from unsafe_metadata instead of public/private metadata
    // Also check if the sign-up URL indicates host registration
    const signUpUrl = data.last_sign_in_url || '';
    const isHostFromUrl = signUpUrl.includes('/host/') || signUpUrl.includes('host');

    const role = data.unsafe_metadata?.role || (isHostFromUrl ? 'host' : 'guest');
    const is_host = data.unsafe_metadata?.isHost || isHostFromUrl || false;

    console.log('Webhook processing user:', {
      clerk_id,
      email,
      signUpUrl,
      isHostFromUrl,
      role,
      is_host,
      unsafe_metadata: data.unsafe_metadata
    });

    if (!clerk_id || !email) {
      console.error('Missing required fields in Clerk payload!')
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Invalid payload' })
      }
    }

    // Check if user exists in Clerk
    try {
      const response = await fetch(`https://api.clerk.dev/v1/users/${clerk_id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.CLERK_SECRET_KEY}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        console.error('User not found in Clerk:', clerk_id)
        return {
          statusCode: 404,
          body: JSON.stringify({ error: 'User not found in Clerk' })
        }
      }
    } catch (error) {
      console.error('Error querying Clerk API:', error)
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Failed to query Clerk API' })
      }
    }

    try {
      console.log('Upserting user data:', {
        clerk_id,
        email,
        first_name,
        last_name,
        avatar_url,
        role,
        is_host
      });

      // Map Clerk user data to the user_profiles table schema in Supabase
      const userData = {
        clerk_id, // Use clerk_id as the primary key for the user
        email,
        first_name,
        last_name,
        avatar_url,
        role,
        is_host,
        updated_at: new Date().toISOString()
      };
      
      console.log('Mapped user data for Supabase:', userData);
      
      const { data: upsertedData, error } = await supabase
        .from('user_profiles')
        .upsert(
          userData,
          { onConflict: 'clerk_id' }
        );

      console.log('Supabase response for upsert operation:', { upsertedData, error });

      if (error) {
        console.error('Failed to sync user with Supabase:', error.message);
        return {
          statusCode: 500,
          body: JSON.stringify({ error: 'Supabase sync error' })
        }
      }

      console.log(`✅ Successfully synced user ${clerk_id} with Supabase`)
    } catch (error) {
      console.error('Error interacting with Supabase:', error)
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Failed to interact with Supabase' })
      }
    }
  } else if (type === 'user.deleted') {
    const clerk_id = data.id;

    try {
      // Use the clerk_id field for deletion
      const { error } = await supabase
        .from('user_profiles')
        .delete()
        .eq('clerk_id', clerk_id);

      if (error) {
        console.error('Failed to delete user from Supabase:', error.message);
        return {
          statusCode: 500,
          body: JSON.stringify({ error: 'Supabase deletion error' })
        };
      }

      console.log(`✅ Successfully deleted user ${clerk_id} from Supabase`);
    } catch (error) {
      console.error('Error interacting with Supabase:', error);
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Failed to interact with Supabase' })
      };
    }
  }  // This section is now handled by the combined user.created and user.updated logic above
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Webhook handled successfully' })
  }
}
