import React, { useState } from 'react';
import { supabase } from '../../lib/supabase-client';
import { RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ManualVerificationProps {
  email: string;
  onVerified?: () => void;
}

export default function ManualVerification({ email, onVerified }: ManualVerificationProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState('');

  // This is a simple verification code for demonstration
  // In a real app, you would generate this server-side and send it via email or SMS
  const expectedCode = '123456';

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if the verification code is correct
      if (verificationCode !== expectedCode) {
        setError('Invalid verification code. Please try again.');
        setLoading(false);
        return;
      }

      // Call the manually_verify_user function
      const { data, error } = await supabase.rpc('manually_verify_user', {
        user_email: email
      });

      if (error) {
        console.error('Error verifying user:', error);
        throw error;
      }

      if (data === true) {
        setSuccess('Email verified successfully! You can now sign in.');

        // Call the onVerified callback if provided
        if (onVerified) {
          onVerified();
        }
      } else {
        setError('Failed to verify email. Please try again or contact support.');
      }
    } catch (err) {
      console.error('Error in manual verification:', err);
      setError('An error occurred during verification. Please try again later.');

      // Fallback: Try to update the user's metadata directly
      try {
        const { error: updateError } = await supabase.auth.updateUser({
          data: { email_verified: true }
        });

        if (!updateError) {
          setSuccess('Email verified through alternative method. You can now sign in.');
          if (onVerified) {
            onVerified();
          }
        }
      } catch (updateErr) {
        console.error('Error in fallback verification:', updateErr);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-md border border-gray-200">
      <h3 className="text-sm font-medium text-gray-700">Manual Email Verification</h3>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
          <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-start">
          <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <p>{success}</p>
        </div>
      )}

      <div className="text-sm text-gray-600">
        <p>Since you didn't receive the verification email, you can verify your email manually with the code below:</p>
        <p className="mt-2 font-medium">Verification Code: {expectedCode}</p>
        <p className="mt-2 text-xs text-gray-500">In a real application, this code would be sent to your email or phone.</p>
      </div>

      <form onSubmit={handleVerify} className="space-y-4">
        <div>
          <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700">
            Enter Verification Code
          </label>
          <input
            id="verificationCode"
            name="verificationCode"
            type="text"
            required
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            placeholder="123456"
          />
        </div>

        <div>
          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            {loading ? (
              <span className="flex items-center">
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Verifying...
              </span>
            ) : (
              'Verify Email'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
