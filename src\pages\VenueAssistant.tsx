import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import VenueAssistant from '../components/chat/VenueAssistant';

export default function VenueAssistantPage() {
  return (
    <div className="pt-32 px-4 sm:px-6 pb-16">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link to="/" className="flex items-center text-gray-600 hover:text-gray-900 mb-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="text-sm">Back to Home</span>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">HouseGoing Venue Assistant</h1>
          <p className="mt-1 text-gray-600">Chat with Homie to find the perfect venue for your event</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">How It Works</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-purple-600 font-bold text-lg mb-2">1. Tell Homie what you need</div>
              <p className="text-gray-700">Describe your event, guest count, location preferences, or any other requirements.</p>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-purple-600 font-bold text-lg mb-2">2. Get venue suggestions</div>
              <p className="text-gray-700">Homie will immediately show you venues that match your criteria.</p>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-purple-600 font-bold text-lg mb-2">3. Refine your search</div>
              <p className="text-gray-700">Answer follow-up questions to find the perfect venue for your event.</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Example Queries</h2>

          <div className="space-y-4">
            <div className="border-l-4 border-purple-500 pl-4 py-1">
              "I need a venue for 50 people in the Eastern Suburbs"
            </div>

            <div className="border-l-4 border-purple-500 pl-4 py-1">
              "Looking for a waterfront venue for a corporate event"
            </div>

            <div className="border-l-4 border-purple-500 pl-4 py-1">
              "I want a venue with a pool for a birthday party"
            </div>

            <div className="border-l-4 border-purple-500 pl-4 py-1">
              "Need a rustic venue for a wedding with 100 guests"
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Ready to Find Your Perfect Venue?</h2>
          <p className="text-gray-700 mb-4">Click the chat button in the bottom right corner to start a conversation with Homie, our venue assistant.</p>

          <div className="flex items-center p-4 bg-purple-50 rounded-lg">
            <div className="flex-shrink-0 bg-purple-600 text-white p-3 rounded-full mr-4">
              <MessageSquare className="h-6 w-6" />
            </div>
            <div>
              <p className="font-medium">Homie is ready to help you find the perfect venue for your event.</p>
              <p className="text-sm text-gray-600">Available 24/7 to answer your questions and provide venue recommendations.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Venue Assistant Chat Widget */}
      <VenueAssistant />
    </div>
  );
}

// Import the MessageSquare icon
function MessageSquare(props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
    </svg>
  );
}
