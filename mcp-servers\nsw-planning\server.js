whatimport express from 'express';
import axios from 'axios';
import xml2js from 'xml2js';

const app = express();
const PORT = process.env.NSW_PLANNING_PORT || 3004;

app.use(express.json());

// Cache for zoning and council data
const cache = new Map();

// Parse XML error responses
async function parseXmlError(xml) {
  try {
    const result = await xml2js.parseStringPromise(xml);
    return result['ows:ExceptionReport']?.['ows:Exception']?.[0]?.['ows:ExceptionText']?.[0] ||
           'Unknown WFS service error';
  } catch {
    return 'Failed to parse WFS error response';
  }
}

// Get zoning data from NSW WFS service
async function getZoningData(lat, lng) {
  const cacheKey = `zoning-${lat}-${lng}`;
  if (cache.has(cacheKey)) return cache.get(cacheKey);

  try {
    const url = new URL('https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/Planning/EPI_Primary_Planning_Layers/MapServer/2/query');
    url.searchParams.append('f', 'json');
    url.searchParams.append('geometryType', 'esriGeometryPoint');
    url.searchParams.append('geometry', `${lng},${lat}`);
    url.searchParams.append('inSR', '4326');
    url.searchParams.append('spatialRel', 'esriSpatialRelIntersects');
    url.searchParams.append('returnGeometry', 'false');
    url.searchParams.append('outFields', 'ZONE_CODE,ZONE_NAME');

    const response = await axios.get(url.toString(), {
      headers: { 'Accept': 'application/json' },
      validateStatus: () => true // Don't throw on HTTP errors
    });

    if (response.headers['content-type'].includes('xml')) {
      const error = await parseXmlError(response.data);
      throw new Error(`WFS Error: ${error}`);
    }

    const data = response.data;
    if (!data?.features?.length) {
      throw new Error('No zoning data found for location');
    }

    const result = {
      code: data.features[0].properties.ZONE_CODE,
      name: data.features[0].properties.ZONE_NAME
    };

    cache.set(cacheKey, result);
    return result;
  } catch (error) {
    console.error('Zoning lookup failed:', error.message);
    throw error;
  }
}

// Get council data from NSW WFS service
async function getCouncilData(lat, lng) {
  const cacheKey = `council-${lat}-${lng}`;
  if (cache.has(cacheKey)) return cache.get(cacheKey);

  try {
    const url = new URL('https://mapprod3.environment.nsw.gov.au/arcgis/rest/services/EDP/Administrative_Boundaries/MapServer/1/query');
    url.searchParams.append('f', 'json');
    url.searchParams.append('geometryType', 'esriGeometryPoint');
    url.searchParams.append('geometry', `${lng},${lat}`);
    url.searchParams.append('inSR', '4326');
    url.searchParams.append('spatialRel', 'esriSpatialRelIntersects');
    url.searchParams.append('returnGeometry', 'false');
    url.searchParams.append('outFields', 'LGA_NAME');

    const response = await axios.get(url.toString(), {
      headers: { 'Accept': 'application/json' },
      validateStatus: () => true
    });

    if (response.headers['content-type'].includes('xml')) {
      const error = await parseXmlError(response.data);
      throw new Error(`WFS Error: ${error}`);
    }

    const data = response.data;
    if (!data?.features?.length) {
      throw new Error('No council data found for location');
    }

    const result = data.features[0].properties.LGA_NAME;
    cache.set(cacheKey, result);
    return result;
  } catch (error) {
    console.error('Council lookup failed:', error.message);
    throw error;
  }
}

// MCP Tool: Get zoning and council info
app.post('/api/nsw-planning/lookup', async (req, res) => {
  try {
    const { lat, lng } = req.body;
    if (!lat || !lng) throw new Error('Latitude and longitude are required');

    const [zoning, council] = await Promise.all([
      getZoningData(lat, lng),
      getCouncilData(lat, lng)
    ]);

    res.json({ zoning, council });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`NSW Planning MCP Server running on port ${PORT}`);
});
