import React, { useState } from 'react';
import { useSignUp } from '@clerk/clerk-react';
import { CLERK_CONFIG } from '../../config/clerk';
import { notifyAdminOfSignup } from '../../hooks/useSignupNotifications';

interface EmailPasswordSignUpProps {
  role: 'host' | 'guest';
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export default function EmailPasswordSignUp({ role, onSuccess, onError }: EmailPasswordSignUpProps) {
  const isOwnerPortal = role === 'host';
  const { isLoaded, signUp, setActive } = useSignUp();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoaded) {
      setError('Authentication system is not ready yet. Please try again in a moment.');
      return;
    }

    if (!firstName || !lastName || !email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Start the sign-up process with email - omit username to let Clerk generate one
      // Add skipPasswordChecks to bypass the password breach check
      await signUp.create({
        emailAddress: email,
        password,
        firstName,
        lastName,
        unsafeMetadata: {
          role
        },
        skipPasswordChecks: true
      });

      // Send the email verification code
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });

      // Move to verification step
      setVerifying(true);
      setLoading(false);
    } catch (err) {
      console.error('Error during sign up:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during sign up');
      setLoading(false);
      if (onError && err instanceof Error) {
        onError(err);
      }
    }
  };

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!verificationCode) {
      setError('Please enter the verification code sent to your email');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Attempt to verify the email
      const completeSignUp = await signUp.attemptEmailAddressVerification({
        code: verificationCode
      });

      if (completeSignUp.status !== 'complete') {
        // Handle incomplete sign up
        setError('Verification failed. Please try again.');
        setLoading(false);
        return;
      }

      // Set the user session as active
      await setActive({ session: completeSignUp.createdSessionId });

      // Store user info in localStorage for immediate access
      localStorage.setItem('first_name', firstName);
      localStorage.setItem('last_name', lastName);
      localStorage.setItem('user_full_name', `${firstName} ${lastName}`);
      localStorage.setItem('user_display_name', firstName);
      localStorage.setItem('clerk_user_email', email);
      localStorage.setItem('auth_success', 'true');
      localStorage.setItem('auth_success_time', new Date().toISOString());
      localStorage.setItem('user_role', role);
      localStorage.setItem('clerk_user_id', completeSignUp.createdUserId || '');

      // Send admin signup notification
      try {
        const userType = role === 'host' ? 'host' : 'customer';
        const fullName = `${firstName} ${lastName}`;
        await notifyAdminOfSignup(email, fullName, userType);
        console.log(`Admin notified of new ${userType} signup: ${fullName}`);
      } catch (error) {
        console.error('Failed to send signup notification:', error);
        // Don't block the signup process if notification fails
      }

      // Redirect to the appropriate page
      const redirectUrl = role === 'host'
        ? CLERK_CONFIG.hostSignUpRedirectURL
        : CLERK_CONFIG.signUpRedirectURL;

      if (onSuccess) {
        onSuccess();
      } else {
        window.location.href = redirectUrl;
      }
    } catch (err) {
      console.error('Error during verification:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during verification');
      setLoading(false);
      if (onError && err instanceof Error) {
        onError(err);
      }
    }
  };

  if (!isLoaded) {
    return <div className="text-center py-4">Loading...</div>;
  }

  if (verifying) {
    return (
      <div className="w-full max-w-md mx-auto">
        <h3 className="text-xl font-semibold text-center mb-4">
          {isOwnerPortal ? 'Verify your Owner Portal email' : 'Verify your email'}
        </h3>
        <p className="text-gray-600 text-center mb-6">
          We've sent a verification code to {email}. Please enter it below.
        </p>

        {error && (
          <div className="bg-red-50 text-red-600 p-3 rounded-lg mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleVerify} className="space-y-4">
          <div>
            <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
              Verification Code
            </label>
            <input
              id="verificationCode"
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
              placeholder="Enter code"
              disabled={loading}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-800 text-white py-3 rounded-lg font-medium hover:from-purple-700 hover:to-purple-900 transition-all duration-200 flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Verifying...
              </>
            ) : (
              'Verify Email'
            )}
          </button>
        </form>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {error && (
        <div className="bg-red-50 text-red-600 p-3 rounded-lg mb-4 text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
              First Name
            </label>
            <input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
              placeholder="First name"
              disabled={loading}
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
              Last Name
            </label>
            <input
              id="lastName"
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
              placeholder="Last name"
              disabled={loading}
            />
          </div>
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
            placeholder="Your email"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Password
          </label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
            placeholder="Create a password"
            disabled={loading}
          />
          <p className="mt-1 text-xs text-gray-500">
            Password must be at least 8 characters
          </p>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-gradient-to-r from-purple-600 to-purple-800 text-white py-3 rounded-lg font-medium hover:from-purple-700 hover:to-purple-900 transition-all duration-200 flex items-center justify-center"
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Account...
            </>
          ) : (
            isOwnerPortal ? 'Create Owner Portal Account' : 'Create Account'
          )}
        </button>
      </form>
    </div>
  );
}
