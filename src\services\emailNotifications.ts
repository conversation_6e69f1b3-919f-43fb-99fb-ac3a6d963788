import { getSupabaseClient } from './api';

// Email service configuration for <PERSON><PERSON>mailer
const FROM_EMAIL = '<EMAIL>'; // Professional email address
const FROM_NAME = 'HouseGoing';
const BACKEND_API_URL = process.env.REACT_APP_BACKEND_URL || 'https://housegoing.onrender.com';

interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

interface NotificationData {
  recipientEmail: string;
  recipientName: string;
  senderName?: string;
  venueName?: string;
  messageContent?: string;
  rating?: number;
  reviewComment?: string;
  bookingId?: string;
  messageId?: string;
  reviewId?: string;
  // Booking-specific data
  bookingDate?: string;
  bookingTime?: string;
  guestCount?: number;
  totalPrice?: string;
  hostName?: string;
  guestName?: string;
  checkInTime?: string;
  checkOutTime?: string;
  specialRequests?: string;
}

// Import clean email templates
import { CLEAN_EMAIL_TEMPLATES } from './cleanEmailTemplates';

// Use clean email templates (replacing the old colorful ones)
const EMAIL_TEMPLATES = CLEAN_EMAIL_TEMPLATES;

// Send email using email server API
async function sendEmail(to: string, template: EmailTemplate, emailType: 'customer' | 'admin'): Promise<boolean> {
  try {
    const response = await fetch(`${BACKEND_API_URL}/api/send-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: to,
        from: FROM_EMAIL,
        fromName: FROM_NAME,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        emailType
      })
    });

    if (response.ok) {
      console.log('Email sent successfully to:', to);
      return true;
    } else {
      console.error('Failed to send email:', await response.text());
      return false;
    }
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

// Check if user wants email notifications
async function shouldSendEmailNotification(userId: string, type: 'messages' | 'reviews'): Promise<boolean> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('user_preferences')
    .select('email_notifications')
    .eq('user_id', userId)
    .single();

  if (error || !data) {
    // Default to true if no preferences set
    return true;
  }

  return data.email_notifications;
}

// Get user email and name
async function getUserInfo(userId: string): Promise<{ email: string; name: string } | null> {
  // This would typically fetch from your user profiles table
  // For now, we'll use Clerk's user data
  try {
    // You'll need to implement this based on your user storage
    // This is a placeholder - replace with actual user lookup
    return {
      email: '<EMAIL>', // Get from Clerk or user_profiles table
      name: 'User Name' // Get from Clerk or user_profiles table
    };
  } catch (error) {
    console.error('Error getting user info:', error);
    return null;
  }
}

// Main notification functions
export async function sendMessageNotification(
  senderId: string,
  receiverId: string,
  messageContent: string,
  senderName: string,
  bookingId?: string,
  venueName?: string
): Promise<void> {
  try {
    // Check if receiver wants email notifications
    const wantsNotifications = await shouldSendEmailNotification(receiverId, 'messages');
    if (!wantsNotifications) return;

    // Get receiver's email and name
    const receiverInfo = await getUserInfo(receiverId);
    if (!receiverInfo) return;

    // Choose template based on whether it's booking-related
    const templateType = bookingId ? 'BOOKING_MESSAGE' : 'NEW_MESSAGE';
    const template = EMAIL_TEMPLATES[templateType]({
      recipientEmail: receiverInfo.email,
      recipientName: receiverInfo.name,
      senderName,
      messageContent,
      venueName
    });

    // Send email (customer-facing message)
    await sendEmail(receiverInfo.email, template, 'customer');

    // Create in-app notification
    const supabase = getSupabaseClient();
    await supabase.rpc('create_notification', {
      p_user_id: receiverId,
      p_type: 'message',
      p_title: `New message from ${senderName}`,
      p_message: messageContent.substring(0, 100) + (messageContent.length > 100 ? '...' : ''),
      p_data: { senderId, messageContent, bookingId, venueName }
    });

  } catch (error) {
    console.error('Error sending message notification:', error);
  }
}

export async function sendReviewNotification(
  reviewerId: string,
  revieweeId: string,
  rating: number,
  comment: string,
  reviewerName: string,
  venueName?: string
): Promise<void> {
  try {
    // Check if reviewee wants email notifications
    const wantsNotifications = await shouldSendEmailNotification(revieweeId, 'reviews');
    if (!wantsNotifications) return;

    // Get reviewee's email and name
    const revieweeInfo = await getUserInfo(revieweeId);
    if (!revieweeInfo) return;

    // Generate email template
    const template = EMAIL_TEMPLATES.NEW_REVIEW({
      recipientEmail: revieweeInfo.email,
      recipientName: revieweeInfo.name,
      senderName: reviewerName,
      rating,
      reviewComment: comment,
      venueName
    });

    // Send email (customer-facing review)
    await sendEmail(revieweeInfo.email, template, 'customer');

    // Create in-app notification
    const supabase = getSupabaseClient();
    await supabase.rpc('create_notification', {
      p_user_id: revieweeId,
      p_type: 'review',
      p_title: `New ${rating}-star review from ${reviewerName}`,
      p_message: comment.substring(0, 100) + (comment.length > 100 ? '...' : ''),
      p_data: { reviewerId, rating, comment, venueName }
    });

  } catch (error) {
    console.error('Error sending review notification:', error);
  }
}

// Send booking confirmation to guest
export async function sendBookingConfirmationToGuest(
  guestId: string,
  hostId: string,
  bookingData: {
    venueName: string;
    bookingDate: string;
    bookingTime: string;
    guestCount: number;
    totalPrice: string;
    hostName: string;
    bookingId: string;
  }
): Promise<void> {
  try {
    // Get guest's email and name
    const guestInfo = await getUserInfo(guestId);
    if (!guestInfo) return;

    // Generate email template
    const template = EMAIL_TEMPLATES.BOOKING_CONFIRMATION_GUEST({
      recipientEmail: guestInfo.email,
      recipientName: guestInfo.name,
      venueName: bookingData.venueName,
      bookingDate: bookingData.bookingDate,
      bookingTime: bookingData.bookingTime,
      guestCount: bookingData.guestCount,
      totalPrice: bookingData.totalPrice,
      hostName: bookingData.hostName,
      bookingId: bookingData.bookingId
    });

    // Send email (customer-facing booking confirmation)
    await sendEmail(guestInfo.email, template, 'customer');

    // Create in-app notification
    const supabase = getSupabaseClient();
    await supabase.rpc('create_notification', {
      p_user_id: guestId,
      p_type: 'booking_confirmed',
      p_title: `Booking confirmed at ${bookingData.venueName}`,
      p_message: `Your party is set for ${bookingData.bookingDate} at ${bookingData.bookingTime}`,
      p_data: bookingData
    });

  } catch (error) {
    console.error('Error sending booking confirmation to guest:', error);
  }
}

// Send booking notification to host
export async function sendBookingNotificationToHost(
  hostId: string,
  guestId: string,
  bookingData: {
    venueName: string;
    bookingDate: string;
    bookingTime: string;
    guestCount: number;
    totalPrice: string;
    guestName: string;
    bookingId: string;
    specialRequests?: string;
  }
): Promise<void> {
  try {
    // Get host's email and name
    const hostInfo = await getUserInfo(hostId);
    if (!hostInfo) return;

    // Generate email template
    const template = EMAIL_TEMPLATES.BOOKING_NOTIFICATION_HOST({
      recipientEmail: hostInfo.email,
      recipientName: hostInfo.name,
      venueName: bookingData.venueName,
      bookingDate: bookingData.bookingDate,
      bookingTime: bookingData.bookingTime,
      guestCount: bookingData.guestCount,
      totalPrice: bookingData.totalPrice,
      guestName: bookingData.guestName,
      bookingId: bookingData.bookingId,
      specialRequests: bookingData.specialRequests
    });

    // Send email (admin notification to host)
    await sendEmail(hostInfo.email, template, 'admin');

    // Create in-app notification
    const supabase = getSupabaseClient();
    await supabase.rpc('create_notification', {
      p_user_id: hostId,
      p_type: 'new_booking',
      p_title: `New booking from ${bookingData.guestName}`,
      p_message: `${bookingData.guestName} booked ${bookingData.venueName} for ${bookingData.bookingDate}`,
      p_data: bookingData
    });

  } catch (error) {
    console.error('Error sending booking notification to host:', error);
  }
}
