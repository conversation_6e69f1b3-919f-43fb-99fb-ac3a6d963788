<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In | HouseGoing - Access Your Account</title>
    <meta name="description" content="Sign in to your HouseGoing account to manage bookings, list venues, and access exclusive features. Secure login for guests and hosts.">
    <meta name="keywords" content="sign in HouseGoing, login account, access dashboard, manage bookings, host portal">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Sign In | HouseGoing - Access Your Account">
    <meta property="og:description" content="Sign in to your HouseGoing account to manage bookings, list venues, and access exclusive features.">
    <meta property="og:url" content="https://housegoing.com.au/sign-in">
    <meta property="og:type" content="website">
    
    <!-- Canonical -->
    <link rel="canonical" href="https://housegoing.com.au/sign-in">
    
    <!-- Redirect to React app -->
    <script>
        // Redirect to the React app login page
        window.location.replace('/login');
    </script>
    
    <!-- Fallback content for crawlers -->
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .cta-button { background: #8B5CF6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Sign In to HouseGoing</h1>
    <p>Access your account to manage bookings, list venues, and enjoy exclusive features.</p>
    
    <h2>Account Features</h2>
    <ul>
        <li><strong>Guest Dashboard:</strong> View and manage your venue bookings</li>
        <li><strong>Host Portal:</strong> List venues and track earnings</li>
        <li><strong>Secure Messaging:</strong> Communicate with hosts and guests</li>
        <li><strong>Booking History:</strong> Access all your past and upcoming events</li>
    </ul>
    
    <a href="/login" class="cta-button">Sign In Now</a>
    <a href="/signup" class="cta-button">Don't have an account? Sign Up</a>
    
    <h2>Quick Links</h2>
    <ul>
        <li><a href="/find-venues">Browse Venues</a></li>
        <li><a href="/list-space">List Your Space</a></li>
        <li><a href="/help">Help & Support</a></li>
        <li><a href="/contact">Contact Us</a></li>
    </ul>
    
    <noscript>
        <p>Please enable JavaScript to access the full HouseGoing experience, or <a href="/login">click here to sign in</a>.</p>
    </noscript>
</body>
</html>
