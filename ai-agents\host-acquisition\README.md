# HouseGoing AI Host Acquisition Agent

This is an AI agent designed to help potential hosts list their venues on the HouseGoing platform. The agent uses LangChain with Hugging Face models to provide personalized guidance and assistance to hosts.

## Features

- Conversational AI agent that helps potential hosts list their venues
- Personalized responses based on venue type, location, and host experience
- Venue analysis tool that estimates earning potential and suggests improvements
- Listing enhancement tool that improves venue descriptions and suggests photo improvements
- **Party Score Calculator** that evaluates venues based on local regulations, zoning, and noise restrictions
- Pricing optimization tool for setting competitive rates
- Booking calendar management assistance
- Host rules generator for creating customized house rules
- Competitor analysis for market positioning
- Integration with LangSmith for tracing and monitoring

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create a `.env` file with your API keys (you can copy from `.env.example`):
   ```
   HUGGINGFACE_API_KEY=your_huggingface_api_key
   LANGSMITH_API_KEY=your_langsmith_api_key
   LANGSMITH_PROJECT=your_langsmith_project
   LANGSMITH_TRACING=true
   LANGSMITH_ENDPOINT=https://api.smith.langchain.com
   ```

## Usage

### Basic Agent

Run the basic agent with:

```bash
npm start
```

This starts a simple conversational agent that helps potential hosts list their venues.

### Advanced Agent

Run the advanced agent with:

```bash
npm run advanced
```

The advanced agent includes additional tools for venue analysis, listing enhancement, pricing optimization, and the Party Score calculator.

### API Server

Run the API server with:

```bash
npm run api
```

This starts an Express server that exposes the Host Acquisition Agent as a REST API on port 3001. The API server includes the following endpoints:

- `POST /api/chat` - Send a message to the agent
- `POST /api/reset` - Reset the conversation history
- `POST /api/tools/:toolName` - Call a specific tool directly
- `POST /api/party-score` - Calculate a Party Score for a venue
- `GET /api/health` - Check the health of the API server

All endpoints require authentication headers:
- `x-api-key` - API key for authentication
- `x-user-id` - User ID for conversation tracking

## Example Conversation

```
You: Hi, I'm thinking about listing my backyard with a pool on HouseGoing.

AI: G'day! That's fantastic to hear you're considering listing your backyard with a pool on HouseGoing! 🏊

A backyard with a pool is actually one of our most sought-after venue types, especially during the warmer months in Australia. Hosts with similar spaces are seeing great booking rates for everything from birthday celebrations to casual weekend gatherings.

Could you tell me a bit more about your space? Things like:
• Approximate size of the area
• Location (suburb/city)
• Any additional features (BBQ, seating, shade structures)

This will help me give you more specific guidance on getting started!

You: It's about 500 square meters in Sydney's Eastern Suburbs. We have a BBQ area and outdoor seating.

AI: Thanks for sharing those details! Your backyard in Sydney's Eastern Suburbs sounds like it has great potential as a venue.

Before we go further, let me calculate a Party Score for your venue to help you understand its potential for hosting events and any time restrictions that might apply.

[Party Score Analysis: 7.5/10]

Great news! Your venue has scored 7.5/10 on our Party Score scale, which means it's well-suited for hosting events. Here are the key details:

• Council Area: Eastern Suburbs Council
• Time Limits: Events must end by 9:30 PM on weekdays and 10:30 PM on weekends
• Zoning: Residential with moderate noise restrictions

I recommend displaying these time limits clearly in your listing and booking calendar to set proper expectations with guests. With your pool, BBQ area, and outdoor seating, you have excellent amenities for daytime and early evening events!

Would you like me to help you optimize your pricing based on similar venues in your area?
```

## Party Score Feature

The Party Score Calculator is a unique tool that helps hosts understand their venue's potential for hosting events based on local regulations and restrictions. It provides:

- A numerical score from 1-10 indicating how party-friendly a venue is
- Information about local council noise regulations and curfew times
- Details about zoning restrictions that affect the venue
- Recommendations for time limits to display in the booking calendar
- Suggestions for transparent communication with guests

This feature helps hosts set appropriate expectations and avoid potential issues with neighbors or local authorities. It also provides implementation suggestions for displaying this information in the venue listing and booking calendar.

## Integration with HouseGoing Platform

To integrate this agent with the HouseGoing platform:

1. Create an API endpoint that accepts user messages and returns agent responses
2. Store conversation history in your database
3. Add a chat interface to your host onboarding flow

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
