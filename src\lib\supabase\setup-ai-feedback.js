/**
 * <PERSON><PERSON><PERSON> to set up AI feedback tables in Supabase
 */
import { supabase } from '../supabase';
import fs from 'fs';
import path from 'path';

async function setupAIFeedbackTables() {
  try {
    console.log('Setting up AI feedback tables in Supabase...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'ai-feedback-tables.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error setting up AI feedback tables:', error);
      return false;
    }
    
    console.log('AI feedback tables set up successfully!');
    return true;
  } catch (error) {
    console.error('Error setting up AI feedback tables:', error);
    return false;
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupAIFeedbackTables()
    .then(success => {
      if (success) {
        console.log('Setup completed successfully.');
      } else {
        console.error('Setup failed.');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unhandled error during setup:', error);
      process.exit(1);
    });
}

export default setupAIFeedbackTables;
