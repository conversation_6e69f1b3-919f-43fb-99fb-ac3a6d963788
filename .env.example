# GitHub Marketplace API Token for AI Assistant
VITE_GITHUB_TOKEN=your_github_token_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Service Role Key (commented for security)
# SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Stripe API Keys (Test Keys - Replace with Live Keys for Production)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key_here
CLERK_SECRET_KEY=your_clerk_secret_key_here
VITE_CLERK_DOMAIN=your_clerk_domain_here

# Mapbox Configuration
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here

# Email Service (Brevo)
BREVO_API_KEY=your_brevo_api_key_here

# Application Configuration
NODE_ENV=development
VITE_APP_URL=http://localhost:5173

# Email Server (Render)
VITE_BACKEND_URL=your_backend_url_here
