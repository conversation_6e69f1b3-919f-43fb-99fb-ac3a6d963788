import { useState, useEffect } from 'react';
import {
  getCurfewInfo,
  formatCurfewTime,
  getCurfewSummary,
  getTopRecommendations
} from '../../lib/nsw-party-planning/curfew-api';
import { extractLGAFromAddress } from '../../utils/addressUtils';
import {
  Clock,
  Calendar,
  Home,
  MapPin,
  Music,
  Volume2,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

/**
 * NSW Party Curfew Lookup Component
 *
 * This component provides a user interface for looking up curfew and noise
 * restriction information for a specific address in NSW.
 */
export default function NSWPartyCurfewLookup() {
  const [address, setAddress] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!address.trim()) {
      setError('Please enter an address');
      return;
    }

    setLoading(true);
    setError('');

    // Submit to backend
    try {
      const response = await fetch('/api/submit-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address,
          date,
          timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit request');
      }

      // Notify admin
      await fetch('/api/notify-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address,
          date,
          timestamp: new Date().toISOString(),
          type: 'curfew_lookup'
        }),
      });
    } catch (submitError) {
      console.error('Error submitting request:', submitError);
      // Continue with curfew lookup even if submission fails
    }

    try {
      // First try to geocode the address using Nominatim
      let geocodeSuccess = false;
      let zoneCode = null;
      let lgaName = null;
      let propertyType = null;

      // Detect if this is an apartment/unit address
      const isApartment = /^\d+\//.test(address);
      if (isApartment) {
        propertyType = 'Apartment/Unit';
        console.log('Detected apartment/unit address');
      }

      // Try to extract LGA from address first
      const extractedLGA = extractLGAFromAddress(address);
      if (extractedLGA) {
        lgaName = extractedLGA;
        console.log('Extracted LGA from address:', lgaName);
      }

      // Detect if this is a Sydney CBD address
      const isSydneyCBD =
        address.toLowerCase().includes('sydney') ||
        address.toLowerCase().includes('bathurst street') ||
        address.toLowerCase().includes('pitt street') ||
        address.toLowerCase().includes('george street');

      if (isSydneyCBD) {
        zoneCode = 'B8';
        // Only set lgaName if not already extracted
        if (!lgaName) {
          lgaName = 'City of Sydney';
        }
        console.log('Detected Sydney CBD address');
      }

      try {
        // Add NSW, Australia to the address if not already present
        let searchAddress = address;
        if (!searchAddress.toLowerCase().includes('nsw') && !searchAddress.toLowerCase().includes('new south wales')) {
          searchAddress += ', NSW, Australia';
        }

        // For apartment/unit addresses, create an alternative search format
        let alternativeAddress = null;
        const unitMatch = searchAddress.match(/^(\d+)\/(\d+(-\d+)?\s+.+)/i);
        if (unitMatch) {
          // Format as "Unit X, Y Street" which works better with some geocoding services
          alternativeAddress = `Unit ${unitMatch[1]}, ${unitMatch[2]}, NSW, Australia`;
          console.log('Created alternative address format:', alternativeAddress);
        }

        // Try with original address format
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(searchAddress)}&format=json&addressdetails=1&limit=1`
        );

        if (response.ok) {
          const data = await response.json();
          if (data && data.length > 0) {
            geocodeSuccess = true;

            // If not already set, use data from geocoding
            if (!zoneCode) zoneCode = 'R2';
            // Only set lgaName from geocoding if we didn't extract it from the address
            if (!lgaName) {
              // For Epping addresses, use City of Ryde
              if (data[0].display_name.toLowerCase().includes('epping')) {
                lgaName = 'City of Ryde';
              } else {
                lgaName = data[0].address?.city || data[0].address?.county || 'City of Sydney';
              }
            }
          }
        }

        // If original format failed and we have an alternative format, try that
        if (!geocodeSuccess && alternativeAddress) {
          console.log('Trying alternative address format with Nominatim');
          const altResponse = await fetch(
            `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(alternativeAddress)}&format=json&addressdetails=1&limit=1`
          );

          if (altResponse.ok) {
            const altData = await altResponse.json();
            if (altData && altData.length > 0) {
              geocodeSuccess = true;

              // If not already set, use data from geocoding
              if (!zoneCode) zoneCode = 'R2';
              // Only set lgaName from geocoding if we didn't extract it from the address
              if (!lgaName) {
                // For Epping addresses, use City of Ryde
                if (altData[0].display_name.toLowerCase().includes('epping')) {
                  lgaName = 'City of Ryde';
                } else {
                  lgaName = altData[0].address?.city || altData[0].address?.county || 'City of Sydney';
                }
              }
            }
          }
        }

        // If both formats failed and we have a unit match, try with just the street address
        if (!geocodeSuccess && unitMatch) {
          // Extract just the street name without unit number
          const streetAddress = `${unitMatch[2]}, NSW, Australia`;
          console.log('Trying street-only address with Nominatim:', streetAddress);

          const streetResponse = await fetch(
            `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(streetAddress)}&format=json&addressdetails=1&limit=1`
          );

          if (streetResponse.ok) {
            const streetData = await streetResponse.json();
            if (streetData && streetData.length > 0) {
              geocodeSuccess = true;

              // If not already set, use data from geocoding
              if (!zoneCode) zoneCode = 'R2';
              // Only set lgaName from geocoding if we didn't extract it from the address
              if (!lgaName) {
                // For Epping addresses, use City of Ryde
                if (streetData[0].display_name.toLowerCase().includes('epping')) {
                  lgaName = 'City of Ryde';
                } else {
                  lgaName = streetData[0].address?.city || streetData[0].address?.county || 'City of Sydney';
                }
              }
            }
          }
        }
      } catch (geocodeError) {
        console.error('Geocoding error:', geocodeError);
        // Continue with the process even if geocoding fails
      }

      // If geocoding failed, we'll still try to get curfew info with default values
      if (!geocodeSuccess) {
        console.log('Geocoding failed, using default values');

        // If we haven't set these values yet, use defaults
        if (!zoneCode) {
          zoneCode = isSydneyCBD ? 'B8' : 'R2';
        }
        if (!lgaName) {
          lgaName = 'City of Sydney';
        }
      }

      // Get curfew info with the address and any zone/LGA info we found
      const curfewInfo = await getCurfewInfo({
        address,
        date,
        propertyType,
        zoneCode,
        lgaName,
      });

      if (!curfewInfo) {
        // If the API call failed, try a direct database call with hardcoded values
        // This is a fallback to ensure users always get some information
        const defaultCurfewInfo = {
          property_type: propertyType || "House",
          zone_code: zoneCode || "R2",
          zone_name: (zoneCode === 'B8') ? "Metropolitan Centre" : "Low Density Residential",
          lga_name: lgaName || "City of Sydney",
          event_date: date,
          is_weekend: new Date(date).getDay() === 0 || new Date(date).getDay() === 6,
          is_holiday: false,
          holiday_name: null,
          curfew_start: (zoneCode === 'B8') ? "00:00:00" : "22:00:00",
          curfew_end: "07:00:00",
          bass_restriction_start: isApartment ? "21:00:00" : null,
          bass_restriction_end: isApartment ? "08:00:00" : null,
          outdoor_cutoff: isApartment ? "21:00:00" : "22:00:00",
          special_conditions: isApartment
            ? "Strata bylaws may impose stricter rules than council regulations. Noise complaints can be lodged with strata management AND council."
            : (zoneCode === 'B8'
              ? "Commercial spaces may have 24/7 operations. Residential areas have stricter enforcement."
              : "Standard residential noise restrictions apply"),
          confidence: {
            level: "Moderate",
            score: 3,
            factors: []
          },
          recommendations: isApartment ? [
            {
              time_period: "Before 8:00 PM",
              recommendation: "Regular conversation and moderate music acceptable",
              priority: 1
            },
            {
              time_period: "8:00 PM - 10:00 PM",
              recommendation: "Reduce bass, keep windows closed",
              priority: 2
            },
            {
              time_period: "After 10:00 PM",
              recommendation: "Low background music only, focus on conversation",
              priority: 3
            },
            {
              time_period: "After 11:00 PM",
              recommendation: "No music, quiet conversation only",
              priority: 4
            }
          ] : [
            {
              time_period: "Before 9:00 PM",
              recommendation: "Outdoor activities acceptable",
              priority: 1
            },
            {
              time_period: "9:00 PM - 10:00 PM",
              recommendation: "Begin transitioning activities indoors",
              priority: 2
            },
            {
              time_period: "After 10:00 PM",
              recommendation: "Indoor activities only, moderate volume",
              priority: 3
            }
          ]
        };

        setResult(defaultCurfewInfo);
        setError('');
      } else {
        setResult(curfewInfo);
        setError('');
      }
    } catch (err) {
      console.error('Error looking up curfew info:', err);
      setError('An error occurred while looking up curfew information.');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  // Format today's date as YYYY-MM-DD
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    setDate(today);
  }, []);

  return (
    <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-6 bg-purple-700 text-white">
        <h2 className="text-2xl font-bold mb-2">NSW Party Planning & Noise Regulations</h2>
        <p className="text-purple-100">
          Check NSW-specific noise restrictions and curfew times for any address
        </p>
      </div>

      <form onSubmit={handleSubmit} className="p-6 border-b">
        <div className="mb-4">
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
            Address
          </label>
          <div className="relative">
            <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
              type="text"
              id="address"
              placeholder="e.g. 123 George St, Sydney NSW 2000"
              className="pl-10 w-full p-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              Enter a full NSW address including suburb and postcode for best results
            </p>
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
            Event Date
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            <input
              type="date"
              id="date"
              className="pl-10 w-full p-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              required
            />
          </div>
        </div>

        <button
          type="submit"
          className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50"
          disabled={loading}
        >
          {loading ? 'Looking up...' : 'Check Noise Restrictions'}
        </button>

        {error && (
          <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md flex items-start">
            <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
            <span>{error}</span>
          </div>
        )}
      </form>

      {result && (
        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2 text-gray-800">Curfew Information</h3>
            <div className="bg-purple-50 p-4 rounded-md">
              <div className="flex items-start mb-4">
                <CheckCircle className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                <p className="text-purple-800">{getCurfewSummary(result)}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <Home className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Property Type</p>
                    <p className="text-sm text-gray-900">{result.property_type}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Zoning</p>
                    <p className="text-sm text-gray-900">
                      {result.zone_name || 'Unknown'}
                      {result.zone_code ? ` (${result.zone_code})` : ''}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Council</p>
                    <p className="text-sm text-gray-900">
                      {result.lga_name || 'Unknown Council'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Noise Curfew</p>
                    <p className="text-sm text-gray-900">
                      {formatCurfewTime(result.curfew_start)} to {formatCurfewTime(result.curfew_end)}
                    </p>
                  </div>
                </div>

                {result.bass_restriction_start && (
                  <div className="flex items-start">
                    <Volume2 className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Bass Music Restriction</p>
                      <p className="text-sm text-gray-900">
                        {formatCurfewTime(result.bass_restriction_start)} to {formatCurfewTime(result.bass_restriction_end)}
                      </p>
                    </div>
                  </div>
                )}

                {result.outdoor_cutoff && (
                  <div className="flex items-start">
                    <Music className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Outdoor Music Cutoff</p>
                      <p className="text-sm text-gray-900">
                        {formatCurfewTime(result.outdoor_cutoff)}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-start md:col-span-2">
                  <Info className="h-5 w-5 text-purple-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Special Conditions</p>
                    <p className="text-sm text-gray-900">
                      {result.special_conditions || 'No special conditions'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2 text-gray-800">Top Recommendations</h3>
            <ul className="bg-green-50 p-4 rounded-md space-y-3">
              {getTopRecommendations(result, 5).map((recommendation, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                  <span className="text-green-800">{recommendation}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="mt-6 text-sm text-gray-500">
            <p className="flex items-center">
              <Info className="h-4 w-4 mr-1" />
              Confidence Level: {result.confidence?.level || 'Moderate'}
            </p>
            <p className="mt-1">
              This information is provided as a guide only. Always check with your local council for specific regulations.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
