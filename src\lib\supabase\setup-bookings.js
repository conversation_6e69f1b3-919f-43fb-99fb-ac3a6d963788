/**
 * Setup script for bookings table in Supabase
 */
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupBookingsTable() {
  try {
    console.log('Setting up bookings table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'src', 'lib', 'supabase', 'bookings-table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      throw error;
    }
    
    console.log('Bookings table created successfully!');
    
    // Verify the table was created
    const { data, error: fetchError } = await supabase
      .from('bookings')
      .select('id')
      .limit(1);
    
    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
      throw fetchError;
    }
    
    console.log('Bookings table verified!');
    console.log('Setup complete!');
  } catch (error) {
    console.error('Error setting up bookings table:', error);
  }
}

// Run the setup
setupBookingsTable();
