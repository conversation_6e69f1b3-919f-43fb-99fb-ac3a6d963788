import React, { useState, useEffect } from 'react';
import { getCurfewInfo, formatCurfewTime, getTopRecommendations } from '../../lib/nsw-party-planning/curfew-api';
import { calculatePartyScore, getPartyScoreRecommendations } from '../../lib/nsw-party-planning/party-score';
import { isHoliday } from '../../utils/booking';
import {
  Clock,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Info,
  Loader,
  PartyPopper,
  Music,
  Volume2,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';

interface NoiseRestrictionSuggestionsProps {
  address: string;
  coordinates: [number, number];
  onApplySuggestions: (suggestions: NoiseRestrictionData) => void;
}

export interface NoiseRestrictionData {
  noiseRestrictions: string;
  weekdaysEndTime: string;
  weekendEndTime: string;
  holidayEndTime?: string; // New field for holiday end time
  curfew: {
    weekday: {
      start: string;
      end: string;
    };
    weekend: {
      start: string;
      end: string;
    };
    holiday?: { // New field for holiday curfew
      start: string;
      end: string;
    };
  };
  bassRestriction: {
    weekday: string;
    weekend: string;
    holiday?: string; // New field for holiday bass restriction
  };
  outdoorCutoff: {
    weekday: string;
    weekend: string;
    holiday?: string; // New field for holiday outdoor cutoff
  };
  specialCondition: string;
  isHoliday?: boolean; // New field to indicate if a date is a holiday
  holidayName?: string; // New field for holiday name
  partyScore?: { // New field for party score
    score: number;
    band: string;
    color: string;
  };
}

const NoiseRestrictionSuggestions: React.FC<NoiseRestrictionSuggestionsProps> = ({
  address,
  coordinates,
  onApplySuggestions
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [curfewInfo, setCurfewInfo] = useState<any>(null);
  const [propertyType, setPropertyType] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [activeTab, setActiveTab] = useState<'weekday' | 'weekend' | 'holiday'>('weekday');
  const [showPartyScore, setShowPartyScore] = useState<boolean>(false);

  // Detect if the property is an apartment based on the address
  useEffect(() => {
    if (address) {
      const isApartment = /^\d+\//.test(address);
      setPropertyType(isApartment ? 'Apartment/Unit' : 'House');
    }
  }, [address]);

  // Determine if the selected date is a weekend or holiday
  useEffect(() => {
    if (selectedDate && curfewInfo) {
      const date = new Date(selectedDate);
      const isWeekendDay = date.getDay() === 0 || date.getDay() === 6;
      const isHolidayDay = isHoliday(date);

      if (isHolidayDay) {
        setActiveTab('holiday');
      } else if (isWeekendDay) {
        setActiveTab('weekend');
      } else {
        setActiveTab('weekday');
      }
    }
  }, [selectedDate, curfewInfo]);

  // Fetch curfew info when the button is clicked
  const fetchCurfewInfo = async () => {
    if (!address) {
      setError('Please enter a property address first');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get curfew info for the selected date
      const selectedDateCurfewInfo = await getCurfewInfo({
        address,
        propertyType,
        date: selectedDate,
        zoneCode: null,
        zoneName: null,
        lgaName: null,
      });

      // Get curfew info for weekdays (Monday)
      const weekdayDate = getNextWeekdayDate();
      const weekdayCurfewInfo = await getCurfewInfo({
        address,
        propertyType,
        date: weekdayDate,
        zoneCode: selectedDateCurfewInfo.zone_code,
        zoneName: selectedDateCurfewInfo.zone_name,
        lgaName: selectedDateCurfewInfo.lga_name,
      });

      // Get curfew info for weekends (Saturday)
      const weekendDate = getNextWeekendDate();
      const weekendCurfewInfo = await getCurfewInfo({
        address,
        propertyType,
        date: weekendDate,
        zoneCode: selectedDateCurfewInfo.zone_code,
        zoneName: selectedDateCurfewInfo.zone_name,
        lgaName: selectedDateCurfewInfo.lga_name,
      });

      // Get curfew info for holidays (New Year's Day)
      const holidayDate = "2024-01-01"; // New Year's Day
      const holidayCurfewInfo = await getCurfewInfo({
        address,
        propertyType,
        date: holidayDate,
        zoneCode: selectedDateCurfewInfo.zone_code,
        zoneName: selectedDateCurfewInfo.zone_name,
        lgaName: selectedDateCurfewInfo.lga_name,
      });

      // Check if the selected date is a holiday
      const isHolidayDate = isHoliday(selectedDate);
      let holidayName = null;

      if (isHolidayDate) {
        // Determine which holiday it is
        const date = new Date(selectedDate);
        const month = date.getMonth() + 1;
        const day = date.getDate();

        if (month === 1 && day === 1) holidayName = "New Year's Day";
        else if (month === 1 && day === 26) holidayName = "Australia Day";
        else if (month === 12 && day === 25) holidayName = "Christmas Day";
        else if (month === 12 && day === 26) holidayName = "Boxing Day";
      }

      // Set the active tab based on the selected date
      const date = new Date(selectedDate);
      const isWeekendDay = date.getDay() === 0 || date.getDay() === 6;

      if (isHolidayDate) {
        setActiveTab('holiday');
      } else if (isWeekendDay) {
        setActiveTab('weekend');
      } else {
        setActiveTab('weekday');
      }

      setCurfewInfo({
        selected: selectedDateCurfewInfo,
        weekday: weekdayCurfewInfo,
        weekend: weekendCurfewInfo,
        holiday: holidayCurfewInfo,
        isHoliday: isHolidayDate,
        holidayName: holidayName
      });
    } catch (err) {
      console.error('Error fetching curfew info:', err);
      setError('Failed to fetch noise restrictions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get the next weekend date (Saturday)
  const getNextWeekendDate = (): string => {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 6 = Saturday
    const daysUntilSaturday = dayOfWeek === 6 ? 0 : (6 - dayOfWeek);

    const nextSaturday = new Date(today);
    nextSaturday.setDate(today.getDate() + daysUntilSaturday);

    return nextSaturday.toISOString().split('T')[0];
  };

  // Get the next weekday date (Monday)
  const getNextWeekdayDate = (): string => {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 6 = Saturday

    // If today is a weekend, get the next Monday
    // If today is a weekday, use today or the next Monday
    const daysUntilMonday = dayOfWeek === 0 ? 1 : dayOfWeek === 6 ? 2 : 0;

    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + daysUntilMonday);

    return nextMonday.toISOString().split('T')[0];
  };

  // Apply the suggested noise restrictions
  const applySuggestions = () => {
    if (!curfewInfo) return;

    const { weekday, weekend, holiday, isHoliday: isHolidayDay, holidayName } = curfewInfo;

    // Format the noise restrictions text
    let noiseRestrictionsText = `
Based on NSW regulations for ${weekday.property_type} in ${weekday.zone_name} zone (${weekday.lga_name}):

Weekdays: Quiet hours from ${formatCurfewTime(weekday.curfew_start)} to ${formatCurfewTime(weekday.curfew_end)}
Weekends: Quiet hours from ${formatCurfewTime(weekend.curfew_start)} to ${formatCurfewTime(weekend.curfew_end)}
`;

    // Add holiday information if available
    if (holiday) {
      noiseRestrictionsText += `
Public Holidays: Quiet hours from ${formatCurfewTime(holiday.curfew_start)} to ${formatCurfewTime(holiday.curfew_end)}`;
    }

    // Add special conditions
    noiseRestrictionsText += `

${weekday.special_conditions}
`;

    // Add party score information
    if (curfewInfo.selected) {
      const partyScoreResult = calculatePartyScore({
        zoneCode: curfewInfo.selected.zone_code,
        zoneName: curfewInfo.selected.zone_name,
        propertyType: curfewInfo.selected.property_type,
        curfewStart: curfewInfo.selected.curfew_start,
        outdoorAllowed: !!curfewInfo.selected.outdoor_cutoff,
        specialLocalRule: curfewInfo.selected.special_conditions,
        isWeekend: curfewInfo.selected.is_weekend,
        dataConfidence: curfewInfo.selected.confidence?.level || 'Medium'
      });

      noiseRestrictionsText += `
Party Score: ${partyScoreResult.score}/10 - ${partyScoreResult.band}
`;
    }

    noiseRestrictionsText = noiseRestrictionsText.trim();

    // Convert 24-hour time format to 12-hour format for input fields
    const convertTo12HourFormat = (time24: string): string => {
      if (!time24) return '';
      const [hours] = time24.split(':');
      return `${hours}:00`;
    };

    // Calculate party score
    const partyScoreResult = calculatePartyScore({
      zoneCode: weekday.zone_code,
      zoneName: weekday.zone_name,
      propertyType: weekday.property_type,
      curfewStart: weekday.curfew_start,
      outdoorAllowed: !!weekday.outdoor_cutoff,
      specialLocalRule: weekday.special_conditions,
      isWeekend: false,
      dataConfidence: weekday.confidence?.level || 'Medium'
    });

    // Create the noise restriction data
    const noiseRestrictionData: NoiseRestrictionData = {
      noiseRestrictions: noiseRestrictionsText,
      weekdaysEndTime: convertTo12HourFormat(weekday.curfew_start),
      weekendEndTime: convertTo12HourFormat(weekend.curfew_start),
      holidayEndTime: holiday ? convertTo12HourFormat(holiday.curfew_start) : undefined,
      curfew: {
        weekday: {
          start: weekday.curfew_start,
          end: weekday.curfew_end
        },
        weekend: {
          start: weekend.curfew_start,
          end: weekend.curfew_end
        },
        holiday: holiday ? {
          start: holiday.curfew_start,
          end: holiday.curfew_end
        } : undefined
      },
      bassRestriction: {
        weekday: weekday.bass_restriction_start || '',
        weekend: weekend.bass_restriction_start || '',
        holiday: holiday ? holiday.bass_restriction_start || '' : undefined
      },
      outdoorCutoff: {
        weekday: weekday.outdoor_cutoff || '',
        weekend: weekend.outdoor_cutoff || '',
        holiday: holiday ? holiday.outdoor_cutoff || '' : undefined
      },
      specialCondition: weekday.special_conditions || '',
      isHoliday: isHolidayDay,
      holidayName: holidayName,
      partyScore: {
        score: partyScoreResult.score,
        band: partyScoreResult.band,
        color: partyScoreResult.color
      }
    };

    // Call the callback function with the data
    onApplySuggestions(noiseRestrictionData);
  };

  return (
    <div className="mt-4 mb-6">
      <div className="flex flex-col md:flex-row md:items-center mb-4 gap-4">
        <div className="flex-1">
          <label htmlFor="event-date" className="block text-sm font-medium text-gray-700 mb-1">
            Check restrictions for a specific date:
          </label>
          <input
            id="event-date"
            type="date"
            className="w-full border border-gray-300 p-2 rounded-md focus:ring-purple-500 focus:border-purple-500"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
          />
        </div>

        <div className="flex-none">
          <button
            type="button"
            onClick={fetchCurfewInfo}
            disabled={loading || !address}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center justify-center"
          >
            {loading ? (
              <>
                <Loader className="animate-spin mr-2 h-4 w-4" />
                Fetching suggestions...
              </>
            ) : (
              <>
                <Info className="mr-2 h-4 w-4" />
                Get Noise Restriction Suggestions
              </>
            )}
          </button>
          <div className="text-center mt-1">
            <a
              href="/nsw-party-planning"
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-purple-600 hover:text-purple-800 flex items-center justify-center"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Open full NSW Party Planning Tool
            </a>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-start">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {curfewInfo && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden mb-4">
          {/* Header with property info and party score toggle */}
          <div className="bg-purple-600 text-white p-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium text-white flex items-center text-lg">
                <Clock className="mr-2 h-5 w-5" />
                Noise Restrictions
              </h3>
              <button
                type="button"
                onClick={() => setShowPartyScore(!showPartyScore)}
                className="text-white text-sm flex items-center hover:underline"
              >
                {showPartyScore ? 'Hide Party Score' : 'Show Party Score'}
                {showPartyScore ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
              </button>
            </div>
            <p className="text-purple-100 text-sm mt-1">
              {curfewInfo.weekday.property_type} in {curfewInfo.weekday.zone_name} zone ({curfewInfo.weekday.lga_name})
            </p>
          </div>

          {/* Party Score Card (conditionally shown) */}
          {showPartyScore && curfewInfo.selected && (
            <div className="p-4 bg-purple-50 border-b border-purple-100">
              {/* Simplified Party Score Card */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <PartyPopper className="h-5 w-5 text-purple-600 mr-2" />
                  <h4 className="font-medium text-purple-800">Party Score</h4>
                </div>

                {(() => {
                  // Calculate party score
                  const partyScoreResult = calculatePartyScore({
                    zoneCode: curfewInfo.selected.zone_code,
                    zoneName: curfewInfo.selected.zone_name,
                    propertyType: curfewInfo.selected.property_type,
                    curfewStart: curfewInfo.selected.curfew_start,
                    outdoorAllowed: !!curfewInfo.selected.outdoor_cutoff,
                    specialLocalRule: curfewInfo.selected.special_conditions,
                    isWeekend: curfewInfo.selected.is_weekend,
                    dataConfidence: curfewInfo.selected.confidence?.level || 'Medium'
                  });

                  const scoreColor =
                    partyScoreResult.color === 'green' ? 'bg-green-100 text-green-800 border-green-200' :
                    partyScoreResult.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                    'bg-red-100 text-red-800 border-red-200';

                  return (
                    <div className={`px-3 py-1 rounded-full ${scoreColor} border flex items-center`}>
                      <span className="font-bold mr-1">{partyScoreResult.score}/10</span>
                      <span className="text-sm">{partyScoreResult.band}</span>
                    </div>
                  );
                })()}
              </div>

              {/* Top recommendations */}
              <div className="bg-white p-3 rounded border border-gray-200 mb-2">
                <p className="text-sm text-gray-700">
                  {getPartyScoreRecommendations(
                    calculatePartyScore({
                      zoneCode: curfewInfo.selected.zone_code,
                      zoneName: curfewInfo.selected.zone_name,
                      propertyType: curfewInfo.selected.property_type,
                      curfewStart: curfewInfo.selected.curfew_start,
                      outdoorAllowed: !!curfewInfo.selected.outdoor_cutoff,
                      specialLocalRule: curfewInfo.selected.special_conditions,
                      isWeekend: curfewInfo.selected.is_weekend,
                      dataConfidence: curfewInfo.selected.confidence?.level || 'Medium'
                    }).score,
                    {
                      zoneCode: curfewInfo.selected.zone_code,
                      zoneName: curfewInfo.selected.zone_name,
                      propertyType: curfewInfo.selected.property_type,
                      curfewStart: curfewInfo.selected.curfew_start,
                      outdoorAllowed: !!curfewInfo.selected.outdoor_cutoff,
                      specialLocalRule: curfewInfo.selected.special_conditions,
                      isWeekend: curfewInfo.selected.is_weekend,
                      dataConfidence: curfewInfo.selected.confidence?.level || 'Medium'
                    }
                  )[0]}
                </p>
              </div>
            </div>
          )}

          {/* Tabs for weekday/weekend/holiday */}
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px">
              <button
                className={`py-3 px-4 text-sm font-medium border-b-2 ${
                  activeTab === 'weekday'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('weekday')}
              >
                Weekdays
              </button>
              <button
                className={`py-3 px-4 text-sm font-medium border-b-2 ${
                  activeTab === 'weekend'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('weekend')}
              >
                Weekends
              </button>
              <button
                className={`py-3 px-4 text-sm font-medium border-b-2 ${
                  activeTab === 'holiday'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('holiday')}
              >
                Public Holidays
              </button>
            </nav>
          </div>

          {/* Tab content */}
          <div className="p-4">
            {/* Weekday tab */}
            {activeTab === 'weekday' && (
              <div>
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Clock className="h-5 w-5 text-purple-600 mr-2" />
                    <h4 className="font-medium text-gray-800">Weekday Noise Restrictions</h4>
                  </div>

                  <div className="bg-white p-3 rounded border border-gray-200 mb-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Quiet Hours:</p>
                        <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.weekday.curfew_start)} to {formatCurfewTime(curfewInfo.weekday.curfew_end)}</p>
                      </div>

                      {curfewInfo.weekday.outdoor_cutoff && (
                        <div>
                          <p className="text-sm font-medium text-gray-700">Outdoor Activities End:</p>
                          <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.weekday.outdoor_cutoff)}</p>
                        </div>
                      )}

                      {curfewInfo.weekday.bass_restriction_start && (
                        <div>
                          <p className="text-sm font-medium text-gray-700">Bass Restrictions:</p>
                          <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.weekday.bass_restriction_start)} to {formatCurfewTime(curfewInfo.weekday.bass_restriction_end)}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-blue-50 p-3 rounded border border-blue-100">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-blue-800">
                        Weekday noise restrictions are typically stricter as most people need to work the next day. Plan your event accordingly.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Weekend tab */}
            {activeTab === 'weekend' && (
              <div>
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Clock className="h-5 w-5 text-purple-600 mr-2" />
                    <h4 className="font-medium text-gray-800">Weekend Noise Restrictions</h4>
                  </div>

                  <div className="bg-white p-3 rounded border border-gray-200 mb-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Quiet Hours:</p>
                        <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.weekend.curfew_start)} to {formatCurfewTime(curfewInfo.weekend.curfew_end)}</p>
                      </div>

                      {curfewInfo.weekend.outdoor_cutoff && (
                        <div>
                          <p className="text-sm font-medium text-gray-700">Outdoor Activities End:</p>
                          <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.weekend.outdoor_cutoff)}</p>
                        </div>
                      )}

                      {curfewInfo.weekend.bass_restriction_start && (
                        <div>
                          <p className="text-sm font-medium text-gray-700">Bass Restrictions:</p>
                          <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.weekend.bass_restriction_start)} to {formatCurfewTime(curfewInfo.weekend.bass_restriction_end)}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-blue-50 p-3 rounded border border-blue-100">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-blue-800">
                        Weekend noise restrictions are typically more lenient, but you should still be considerate of neighbors.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Holiday tab */}
            {activeTab === 'holiday' && (
              <div>
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Clock className="h-5 w-5 text-purple-600 mr-2" />
                    <h4 className="font-medium text-gray-800">Public Holiday Noise Restrictions</h4>
                  </div>

                  {curfewInfo.holiday ? (
                    <div className="bg-white p-3 rounded border border-gray-200 mb-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Quiet Hours:</p>
                          <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.holiday.curfew_start)} to {formatCurfewTime(curfewInfo.holiday.curfew_end)}</p>
                        </div>

                        {curfewInfo.holiday.outdoor_cutoff && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">Outdoor Activities End:</p>
                            <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.holiday.outdoor_cutoff)}</p>
                          </div>
                        )}

                        {curfewInfo.holiday.bass_restriction_start && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">Bass Restrictions:</p>
                            <p className="text-sm text-gray-900">{formatCurfewTime(curfewInfo.holiday.bass_restriction_start)} to {formatCurfewTime(curfewInfo.holiday.bass_restriction_end)}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-yellow-50 p-3 rounded border border-yellow-100 mb-3">
                      <div className="flex items-start">
                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                        <p className="text-sm text-yellow-800">
                          Public holiday information is not available. Please check with the local council for specific holiday noise restrictions.
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="bg-blue-50 p-3 rounded border border-blue-100">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-blue-800">
                        Public holidays may have special noise exemptions, especially for major holidays like New Year's Eve. Check with the local council for specific details.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Special conditions */}
            {curfewInfo.weekday.special_conditions && (
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                  <h4 className="font-medium text-gray-800">Special Conditions</h4>
                </div>
                <div className="bg-yellow-50 p-3 rounded border border-yellow-100">
                  <p className="text-sm text-gray-700">{curfewInfo.weekday.special_conditions}</p>
                </div>
              </div>
            )}

            {/* Recommendations */}
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <h4 className="font-medium text-gray-800">Recommendations</h4>
              </div>
              <div className="bg-green-50 p-3 rounded border border-green-100">
                <ul className="space-y-2">
                  {getTopRecommendations(curfewInfo.selected || curfewInfo.weekday).map((rec, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Apply button */}
            <div className="flex justify-end">
              <button
                type="button"
                onClick={applySuggestions}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Apply These Suggestions
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NoiseRestrictionSuggestions;
