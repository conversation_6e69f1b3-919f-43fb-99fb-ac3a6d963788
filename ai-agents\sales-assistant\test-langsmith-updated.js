import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const apiKey = process.env.LANGSMITH_API_KEY;
const projectName = process.env.LANGSMITH_PROJECT;

console.log('Using API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');
console.log('Project Name:', projectName);

async function testLangSmithAPI() {
  try {
    // Test API connection by fetching projects
    const response = await fetch('https://api.smith.langchain.com/api/projects', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${await response.text()}`);
    }
    
    const data = await response.json();
    console.log('\nProjects found:', data.length);
    
    // List all projects
    console.log('\nAvailable projects:');
    data.forEach(project => {
      console.log(`- ${project.name} (${project.id})`);
    });
    
    // Check if our project exists
    const ourProject = data.find(project => project.name === projectName);
    if (ourProject) {
      console.log(`\nOur project "${projectName}" found with ID: ${ourProject.id}`);
    } else {
      console.log(`\nOur project "${projectName}" not found in the list of available projects.`);
    }
    
  } catch (error) {
    console.error('Error testing LangSmith API:', error);
  }
}

testLangSmithAPI();
