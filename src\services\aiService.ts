// AI Service for HouseGoing Platform
// Uses OpenRouter API with Mistral Nemo Free model

const OPENROUTER_API_KEY = import.meta.env.VITE_OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const MODEL = 'mistralai/mistral-nemo:free';

interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIResponse {
  success: boolean;
  message?: string;
  error?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class AIService {
  private async makeRequest(messages: AIMessage[]): Promise<AIResponse> {
    try {
      const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'HouseGoing Platform'
        },
        body: JSON.stringify({
          model: MODEL,
          messages,
          max_tokens: 500,
          temperature: 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        success: true,
        message: data.choices[0]?.message?.content || 'No response generated',
        usage: data.usage
      };
    } catch (error) {
      console.error('AI Service Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Customer AI Assistant (Homie)
  async getCustomerAssistance(userMessage: string, context?: {
    currentPage?: string;
    searchQuery?: string;
    userLocation?: string;
  }): Promise<AIResponse> {
    const systemPrompt = `You are Homie, the friendly AI assistant for HouseGoing - Australia's premier party venue rental platform. You help customers find perfect venues for their events and answer questions about bookings, pricing, and policies.

PERSONALITY: Friendly, helpful, and knowledgeable. Use Australian expressions naturally. Be enthusiastic about helping people create amazing events.

COMPREHENSIVE FAQ KNOWLEDGE:

BOOKING PROCESS:
- How to book: 1) Search & browse venues, 2) Submit booking request with event details, 3) Host reviews within 24 hours, 4) Pay deposit to confirm
- Booking timing: Peak times (weekends, holidays, summer) book 6-8 weeks ahead. December: 10-12 weeks. Off-peak: 2-3 weeks sufficient
- Payment terms: 50% deposit to confirm, balance due 48 hours before event. Secure Stripe processing. Security deposit (20%) held as pre-auth
- Cancellation: Free cancellation 48+ hours before. Within 48 hours: 50% refund. No-shows: no refund. Weather cancellations: full refund

PRICING & PAYMENTS:
- Pricing structure: Venue fee (host-set) + Service fee (3-5%) + GST (10%) + optional security deposit (20%)
- Seasonal pricing: Peak (Dec-Jan, Mar-May, Sep-Nov) higher prices. Off-peak (Jun-Aug, weekdays) 30-50% savings
- Special events: NYE premium 200-300% increase. Valentine's Day, Melbourne Cup premiums

SAFETY & LEGAL (NSW):
- Noise restrictions: Weekdays 10PM-8AM quiet hours. Weekends/holidays 12AM-8AM. 45-50dB limits during quiet hours
- Insurance: HouseGoing does NOT provide any insurance coverage. Guests must obtain their own event insurance. Hosts need public liability insurance.
- Guest rules: Must not exceed venue capacity. All guests known to organizer. No public events. 18+ organizer required

VENUE AMENITIES:
- Standard: Tables & chairs, basic lighting, restrooms, kitchen access, parking
- Common features: Pool, BBQ, sound system, air conditioning, outdoor furniture
- Premium add-ons: Professional kitchen, bar setup, dance floor, photobooth, spa facilities
- Food/alcohol: BYO venues (kitchen access), catering-only venues (approved caterers), licensed venues (purchase through venue), dry venues (no alcohol)

LOGISTICS:
- Parking: On-site (10-50 spaces, free), street parking (check restrictions), public lots ($5-20/day)
- Decorations: No permanent fixtures, removable only, fire safety compliance. Prohibited: glitter, confetti, candles, nails
- Entertainment: Live bands, DJs, entertainers welcome. Must comply with noise ordinances. Book in advance, ensure insurance

WEATHER & SEASONAL:
- Weather backup: Most venues have covered areas. Marquees available. Severe weather = full refund
- Seasonal considerations: Summer (heat, UV, bushfire risk), Winter (cold, rain, heating), variable spring/autumn

SUPPORT:
- 24/7 emergency hotline, direct host contact, insurance claims assistance
- Emergency response: Call 000 for life-threatening, contact venue owner, call HouseGoing 1800-HOUSE-GO
- Pre-event: venue selection help, planning recommendations, vendor connections

HOST INFORMATION:
- Becoming host: Own property, $20M liability insurance, 18+, local compliance
- Quality standards: Fire safety certificates, electrical compliance, pool safety, professional cleaning

Current context: ${context ? JSON.stringify(context) : 'General inquiry'}

When users ask about venues, always ask clarifying questions:
- How many guests?
- What type of event?
- Preferred location/suburb?
- Date and time?
- Budget range?
- Special requirements?

Provide helpful, specific advice using the comprehensive FAQ knowledge above. Always encourage users to browse our platform for latest availability and pricing.`;

    const messages: AIMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage }
    ];

    return this.makeRequest(messages);
  }

  // Host AI Assistant
  async getHostAssistance(userMessage: string, context?: {
    propertyCount?: number;
    recentBookings?: number;
    earnings?: string;
    currentTask?: string;
  }): Promise<AIResponse> {
    const systemPrompt = `You are the AI assistant for HouseGoing hosts. You help property owners manage their listings and maximize their success on the platform.

Your role:
- Help hosts optimize their property listings
- Provide pricing and booking management advice
- Answer questions about host policies and best practices
- Suggest improvements for better guest experience
- Help with property photography and description tips

Platform context:
- HouseGoing is a venue booking platform for parties and events
- Hosts list properties for hourly rentals
- Focus on maximizing bookings and guest satisfaction
- Australian market with emphasis on NSW

Host context: ${context ? JSON.stringify(context) : 'General inquiry'}

Provide practical, actionable advice to help hosts succeed. Be professional but friendly.`;

    const messages: AIMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage }
    ];

    return this.makeRequest(messages);
  }

  // Admin Analytics AI (Optional - only when requested)
  async getAdminAnalytics(query: string, websiteData?: {
    totalUsers?: number;
    totalBookings?: number;
    totalRevenue?: string;
    topVenues?: string[];
    userGrowth?: string;
    recentActivity?: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `You are an AI analytics assistant for HouseGoing platform administrators. You analyze website data and provide insights to help improve the platform.

Your role:
- Analyze user behavior and booking patterns
- Identify growth opportunities and potential issues
- Provide data-driven recommendations for platform improvements
- Help interpret metrics and KPIs
- Suggest marketing and operational strategies

Platform data: ${websiteData ? JSON.stringify(websiteData, null, 2) : 'No data provided'}

Provide clear, actionable insights based on the data. Focus on practical recommendations that can drive business growth and improve user experience.`;

    const messages: AIMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: query }
    ];

    return this.makeRequest(messages);
  }

  // General chat for testing
  async chat(message: string): Promise<AIResponse> {
    const messages: AIMessage[] = [
      { role: 'user', content: message }
    ];

    return this.makeRequest(messages);
  }
}

// Export singleton instance
export const aiService = new AIService();
export default aiService;

// Usage examples:
/*
// Customer assistance
const response = await aiService.getCustomerAssistance(
  "I'm looking for a venue for a birthday party in Sydney for 20 people",
  { currentPage: 'find-venues', userLocation: 'Sydney, NSW' }
);

// Host assistance  
const hostResponse = await aiService.getHostAssistance(
  "How can I increase my booking rate?",
  { propertyCount: 2, recentBookings: 5, earnings: '$1,200' }
);

// Admin analytics (optional)
const analyticsResponse = await aiService.getAdminAnalytics(
  "What trends do you see in our booking data?",
  { totalUsers: 1250, totalBookings: 450, totalRevenue: '$125,000' }
);
*/
