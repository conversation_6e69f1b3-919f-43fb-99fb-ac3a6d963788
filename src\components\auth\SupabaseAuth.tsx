import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase-client';
import { UserRole } from '../../services/auth';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import ManualVerification from './ManualVerification';
import { getAuthCallbackUrl } from '../../utils/site-url';

interface SupabaseAuthProps {
  mode: 'signin' | 'signup';
  redirectTo: string;
  userType: 'host' | 'guest';
}

export default function SupabaseAuth({ mode, redirectTo, userType }: SupabaseAuthProps) {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [showResendOption, setShowResendOption] = useState(false);
  const [showManualVerification, setShowManualVerification] = useState(false);
  const [resendEmail, setResendEmail] = useState('');

  const isSignUp = mode === 'signup';
  const buttonText = isSignUp ? 'Sign Up' : 'Sign In';
  const isHost = userType === 'host';

  // Function to handle resending confirmation email
  const handleResendConfirmation = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setMessage(null);
    setResendLoading(true);

    try {
      const emailToUse = resendEmail || email;

      if (!emailToUse) {
        setError('Please enter your email address');
        setResendLoading(false);
        return;
      }

      console.log('Resending confirmation email to:', emailToUse);

      // Try to resend the confirmation email
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: emailToUse,
        options: {
          emailRedirectTo: getAuthCallbackUrl()
        }
      });

      if (error) {
        console.error('Error from Supabase when resending confirmation:', error);
        throw error;
      }

      setMessage(`Confirmation email resent to ${emailToUse}. Please check your inbox and spam folder.`);
      setShowResendOption(false);

      // Log success for debugging
      console.log('Confirmation email resent successfully');
    } catch (err) {
      console.error('Error resending confirmation:', err);
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while resending the confirmation email';
      setError(errorMessage);

      // If we get a specific error about the email not being in the system, suggest signing up
      if (errorMessage.includes('Email not found') || errorMessage.includes('No user found')) {
        setError(`Email ${resendEmail || email} not found. Please sign up first.`);
      }
    } finally {
      setResendLoading(false);
    }
  };

  // Function to handle email authentication (sign in or sign up)
  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setMessage(null);
    setLoading(true);

    try {
      if (isSignUp) {
        // Sign up with email and password
        console.log('Signing up with email:', email);

        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              first_name: firstName,
              last_name: lastName,
              role: userType,
            },
            emailRedirectTo: getAuthCallbackUrl()
          },
        });

        if (error) {
          console.error('Error during signup:', error);
          throw error;
        }

        console.log('Signup successful, user data:', data);

        if (data.user) {
          // Create user profile in Supabase
          try {
            console.log('Creating user profile for user ID:', data.user.id);

            // First, check if the user_profiles table exists
            const { error: tableCheckError } = await supabase
              .from('user_profiles')
              .select('id')
              .limit(1);

            if (tableCheckError) {
              console.error('Error checking user_profiles table:', tableCheckError);
              console.log('Attempting to create user profile directly...');

              // Try direct insert without upsert
              const { error: insertError } = await supabase
                .from('user_profiles')
                .insert({
                  id: data.user.id,
                  email: data.user.email || '',
                  role: userType,
                  first_name: firstName,
                  last_name: lastName,
                  is_host: isHost,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                });

              if (insertError) {
                console.error('Error inserting user profile:', insertError);
              } else {
                console.log('User profile created successfully via direct insert');
              }
            } else {
              // Table exists, use upsert
              const { error: profileError } = await supabase
                .from('user_profiles')
                .upsert({
                  id: data.user.id,
                  email: data.user.email || '',
                  role: userType,
                  first_name: firstName,
                  last_name: lastName,
                  is_host: isHost,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                }, { onConflict: 'id' });

              if (profileError) {
                console.error('Error creating user profile:', profileError);

                // If there's an error, try again with a delay
                setTimeout(async () => {
                  try {
                    const { error: retryError } = await supabase
                      .from('user_profiles')
                      .upsert({
                        id: data.user?.id || '',
                        email: data.user?.email || '',
                        role: userType,
                        first_name: firstName,
                        last_name: lastName,
                        is_host: isHost,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                      }, { onConflict: 'id' });

                    if (retryError) {
                      console.error('Error on retry creating user profile:', retryError);
                    } else {
                      console.log('User profile created successfully on retry');
                    }
                  } catch (e) {
                    console.error('Exception on retry creating user profile:', e);
                  }
                }, 2000);
              } else {
                console.log('User profile created successfully via upsert');
              }
            }
          } catch (profileException) {
            console.error('Exception creating user profile:', profileException);
          }

          // Check if email confirmation is needed
          if (data.session) {
            // User is already confirmed and logged in
            setMessage('Registration successful! You are now logged in.');

            // Set auth success flag
            localStorage.setItem('auth_success', 'true');
            localStorage.setItem('auth_success_time', new Date().toISOString());
            localStorage.setItem('auth_user_type', userType);
            localStorage.setItem('first_name', firstName);
            localStorage.setItem('last_name', lastName);

            // Redirect after successful sign-up with a small delay
            setTimeout(() => {
              navigate(redirectTo);
            }, 1000);
          } else {
            // User needs to confirm email
            setMessage(`Registration successful! Please check your email (${email}) for verification. If you don't see it, check your spam folder.`);
            // Store email for potential resend
            setResendEmail(email);
            // Show manual verification option after a delay if this is a development environment
            setTimeout(() => {
              setShowManualVerification(true);
            }, 5000);
          }
        }
      } else {
        // Sign in with email and password
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          // Check for specific error messages
          if (error.message.includes('Email not confirmed')) {
            setError('Your email has not been confirmed. Please check your inbox or request a new confirmation email.');
            setShowResendOption(true);
            setResendEmail(email);
            throw error;
          } else if (error.message.includes('Invalid login credentials')) {
            setError('Invalid email or password. Please try again.');
            throw error;
          } else if (error.message.includes('failed to load image data')) {
            setError('Authentication error. Please try again or use a different browser.');
            throw error;
          } else {
            throw error;
          }
        }

        // Set auth success flag
        localStorage.setItem('auth_success', 'true');
        localStorage.setItem('auth_success_time', new Date().toISOString());
        localStorage.setItem('auth_user_type', userType);
        localStorage.setItem('first_name', data.user?.user_metadata?.first_name || '');
        localStorage.setItem('last_name', data.user?.user_metadata?.last_name || '');

        // Redirect after successful sign-in with a small delay to ensure auth state is updated
        setTimeout(() => {
          navigate(redirectTo);
        }, 500);
      }
    } catch (err) {
      console.error('Authentication error:', err);
      const errorMessage = err instanceof Error ? err.message : 'An error occurred during authentication';

      // Don't overwrite specific error messages
      if (!error) {
        setError(errorMessage);
      }

      // Check for specific error messages to show resend option
      if (errorMessage.includes('Email not confirmed') ||
          errorMessage.includes('not verified') ||
          errorMessage.includes('verification')) {
        setShowResendOption(true);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Error and Success Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
          <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p>{error}</p>
            {showResendOption && (
              <button
                onClick={handleResendConfirmation}
                disabled={resendLoading}
                className="mt-2 inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-800"
              >
                {resendLoading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Resend confirmation email
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      )}

      {message && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-start">
          <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p>{message}</p>
            {isSignUp && (
              <p className="mt-2 text-sm">
                Didn't receive the email? Check your spam folder or{' '}
                <button
                  onClick={handleResendConfirmation}
                  disabled={resendLoading}
                  className="inline-flex items-center font-medium text-green-700 hover:text-green-900"
                >
                  {resendLoading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                      sending...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-1" />
                      resend it
                    </>
                  )}
                </button>
                {' or '}
                <button
                  onClick={() => setShowManualVerification(!showManualVerification)}
                  className="inline-flex items-center font-medium text-green-700 hover:text-green-900"
                >
                  verify manually
                </button>
              </p>
            )}
          </div>
        </div>
      )}

      {/* Standalone Resend Confirmation Form */}
      {!isSignUp && (
        <div className="text-center mb-4">
          <button
            onClick={() => setShowResendOption(!showResendOption)}
            className="text-sm text-purple-600 hover:text-purple-800"
          >
            Need to resend confirmation email?
          </button>
        </div>
      )}

      {showResendOption && !isSignUp && (
        <form onSubmit={handleResendConfirmation} className="space-y-4 mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
          <h3 className="text-sm font-medium text-gray-700">Resend Confirmation Email</h3>
          <div>
            <label htmlFor="resendEmail" className="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <input
              id="resendEmail"
              name="resendEmail"
              type="email"
              required
              value={resendEmail}
              onChange={(e) => setResendEmail(e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            />
          </div>
          <div>
            <button
              type="submit"
              disabled={resendLoading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              {resendLoading ? (
                <span className="flex items-center">
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Sending...
                </span>
              ) : (
                'Resend Confirmation'
              )}
            </button>
          </div>
          <div className="text-center mt-2">
            <button
              type="button"
              onClick={() => {
                setShowManualVerification(!showManualVerification);
                setShowResendOption(false);
              }}
              className="text-sm text-purple-600 hover:text-purple-800"
            >
              Verify manually instead
            </button>
          </div>
        </form>
      )}

      {/* Manual Verification Form */}
      {showManualVerification && (
        <ManualVerification
          email={resendEmail || email}
          onVerified={() => {
            setShowManualVerification(false);
            setMessage('Email verified successfully! You can now sign in.');

            // If we're in sign-up mode, we're done
            if (isSignUp) {
              return;
            }

            // If we're in sign-in mode, try to sign in automatically
            setTimeout(async () => {
              try {
                if (!password) {
                  return; // Can't sign in without password
                }

                const { data, error } = await supabase.auth.signInWithPassword({
                  email: resendEmail || email,
                  password,
                });

                if (error) {
                  console.error('Error signing in after verification:', error);
                  return;
                }

                // Set auth success flag
                localStorage.setItem('auth_success', 'true');
                localStorage.setItem('auth_success_time', new Date().toISOString());
                localStorage.setItem('auth_user_type', userType);

                // Redirect after successful sign-in
                navigate(redirectTo);
              } catch (err) {
                console.error('Error auto-signing in after verification:', err);
              }
            }, 1500);
          }}
        />
      )}

      {/* Email/Password Form */}
      <form onSubmit={handleEmailAuth} className="space-y-4">
        {isSignUp && (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                First Name
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                required
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
              />
            </div>
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                Last Name
              </label>
              <input
                id="lastName"
                name="lastName"
                type="text"
                required
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
              />
            </div>
          </div>
        )}

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete={isSignUp ? 'new-password' : 'current-password'}
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
          />
        </div>

        <div>
          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </span>
            ) : (
              buttonText
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
