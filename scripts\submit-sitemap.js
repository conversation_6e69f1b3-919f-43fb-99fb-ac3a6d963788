// submit-sitemap.js
// A utility script to help submit sitemaps to Google Search Console
// Node.js version

const readline = require('readline');
const https = require('https');
const url = require('url');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('============================================');
console.log('Google Search Console Sitemap Submission Guide');
console.log('============================================');
console.log('\nThis script will help you submit your updated sitemaps to Google Search Console.\n');

// Step 1: Ask for the sitemap URL
rl.question('Enter your sitemap URL (e.g., https://housegoing.com.au/sitemap.xml): ', (sitemapUrl) => {
  if (!sitemapUrl) {
    sitemapUrl = 'https://housegoing.com.au/sitemap.xml';
    console.log(`Using default sitemap URL: ${sitemapUrl}`);
  }

  // Parse the URL to get domain info
  const parsedUrl = url.parse(sitemapUrl);
  const domain = parsedUrl.hostname;

  // Step 2: Verify the URL
  console.log(`\nVerifying sitemap URL: ${sitemapUrl}`);
  
  // Make a HEAD request to check if the sitemap exists
  const req = https.request(
    {
      method: 'HEAD',
      hostname: parsedUrl.hostname,
      path: parsedUrl.path,
    },
    (res) => {
      console.log(`Sitemap status: ${res.statusCode} ${res.statusMessage}`);
      
      if (res.statusCode >= 200 && res.statusCode < 300) {
        console.log('✅ Sitemap URL is valid and accessible');
        showInstructions(domain, sitemapUrl);
      } else {
        console.log('❌ Sitemap URL returned an error. Please check the URL and try again.');
        rl.close();
      }
    }
  );
  
  req.on('error', (error) => {
    console.error('❌ Error checking sitemap:', error.message);
    rl.close();
  });
  
  req.end();
});

function showInstructions(domain, sitemapUrl) {
  console.log('\n============================================');
  console.log('Submission Instructions:');
  console.log('============================================');
  console.log('\n1. Log in to Google Search Console at https://search.google.com/search-console');
  console.log(`2. Select your property for "${domain}"`);
  console.log('3. In the left sidebar, click on "Sitemaps"');
  console.log(`4. Enter the URL of your sitemap: ${sitemapUrl.replace(`https://${domain}/`, '')}`);
  console.log('5. Click "Submit"');
  console.log('\nAlso submit individual brand page URLs for indexing:');
  console.log('\n1. In Google Search Console, go to "URL Inspection" in the left sidebar');
  console.log('2. Enter these URLs one by one:');
  console.log(`   - https://${domain}/about-housegoing`);
  console.log(`   - https://${domain}/housegoing-brand.html`);
  console.log('3. Click "Request Indexing" for each URL');
  
  console.log('\n============================================');
  console.log('Brand Search Monitoring:');
  console.log('============================================');
  console.log('\n1. Use incognito/private browsing to search for "housegoing" (without quotes)');
  console.log('2. Check weekly to see if your site appears in the results');
  console.log('3. Monitor Google Search Console for brand queries in Performance reports');
  console.log('\nNote: It typically takes 2-8 weeks for brand search visibility to improve.');
  
  rl.close();
}
