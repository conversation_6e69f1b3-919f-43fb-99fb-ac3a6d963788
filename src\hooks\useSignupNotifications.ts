// Hook for handling signup notifications
import { useUser } from '@clerk/clerk-react';
import { sendCustomerSignupNotification, sendHostSignupNotification } from '../services/cleanEmailTemplates';

export interface SignupData {
  email: string;
  firstName?: string;
  lastName?: string;
  userType: 'customer' | 'host';
}

export function useSignupNotifications() {
  const { user } = useUser();

  const sendSignupNotification = async (signupData: SignupData) => {
    try {
      const fullName = signupData.firstName && signupData.lastName 
        ? `${signupData.firstName} ${signupData.lastName}`
        : signupData.firstName || signupData.lastName || undefined;

      if (signupData.userType === 'customer') {
        // Send <NAME_EMAIL>
        await sendCustomerSignupNotification(signupData.email, fullName);
        console.log('Customer signup notification sent');
      } else if (signupData.userType === 'host') {
        // Send notification to hui<PERSON><EMAIL>
        await sendHostSignupNotification(signupData.email, fullName);
        console.log('Host signup notification sent');
      }
    } catch (error) {
      console.error('Failed to send signup notification:', error);
    }
  };

  // Auto-detect signup from Clerk user creation
  const handleClerkSignup = async (userType: 'customer' | 'host') => {
    if (user) {
      await sendSignupNotification({
        email: user.primaryEmailAddress?.emailAddress || '',
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        userType
      });
    }
  };

  return {
    sendSignupNotification,
    handleClerkSignup
  };
}

// Utility function to call from anywhere in your app
export async function notifyAdminOfSignup(
  email: string,
  name: string | undefined,
  userType: 'customer' | 'host'
) {
  try {
    if (userType === 'customer') {
      await sendCustomerSignupNotification(email, name);
    } else {
      await sendHostSignupNotification(email, name);
    }
  } catch (error) {
    console.error('Failed to notify admin of signup:', error);
  }
}
