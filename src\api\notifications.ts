import { getSupabaseClient } from '../services/api';

/**
 * Send an email notification for a new message
 * @param recipientEmail The email address of the recipient
 * @param senderName The name of the sender
 * @param messageContent The content of the message
 * @param bookingId The ID of the booking related to the message
 */
export async function sendMessageNotification(
  recipientEmail: string,
  senderName: string,
  messageContent: string,
  bookingId: string
) {
  try {
    // In a production environment, this would call a serverless function
    // or use a service like SendGrid, Mailgun, etc.
    
    // For now, we'll just log the notification and store it in Supabase
    console.log('Sending message notification:', {
      to: recipientEmail,
      from: '<EMAIL>',
      subject: `New message from ${senderName} regarding booking #${bookingId}`,
      body: `
        You have received a new message from ${senderName} regarding booking #${bookingId}:
        
        "${messageContent.substring(0, 100)}${messageContent.length > 100 ? '...' : ''}"
        
        Log in to your HouseGoing account to respond.
      `
    });
    
    // Store the notification in Supabase
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('notifications')
      .insert({
        recipient_email: recipientEmail,
        sender_name: senderName,
        message_preview: messageContent.substring(0, 100),
        booking_id: bookingId,
        created_at: new Date().toISOString(),
        type: 'message',
        is_read: false
      });
      
    if (error) {
      console.error('Error storing notification:', error);
    }
    
    return true;
  } catch (error) {
    console.error('Error sending message notification:', error);
    return false;
  }
}

/**
 * Mark a notification as read
 * @param notificationId The ID of the notification to mark as read
 */
export async function markNotificationAsRead(notificationId: string) {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);
      
    if (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
}

/**
 * Get all notifications for a user
 * @param userEmail The email address of the user
 */
export async function getUserNotifications(userEmail: string) {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('recipient_email', userEmail)
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return [];
  }
}
