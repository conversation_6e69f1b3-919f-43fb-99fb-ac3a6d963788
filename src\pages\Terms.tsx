import React from 'react';

export default function Terms() {
  return (
    <div className="pt-32 px-4 max-w-4xl mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6">
        <h1 className="text-3xl font-bold mb-6">Terms of Service</h1>
        
        <div className="prose max-w-none">
          <p className="mb-4">
            Welcome to HouseGoing. By using our platform, you agree to these Terms of Service.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">1. Acceptance of Terms</h2>
          <p className="mb-4">
            By accessing or using the HouseGoing platform, you agree to be bound by these Terms of Service. 
            If you do not agree to all the terms and conditions, you may not access or use our services.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">2. User Accounts</h2>
          <p className="mb-4">
            To use certain features of our platform, you must register for an account. You agree to provide accurate, 
            current, and complete information during the registration process and to update such information to keep it 
            accurate, current, and complete.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">3. User Conduct</h2>
          <p className="mb-4">
            You agree not to use the HouseGoing platform for any illegal or unauthorized purpose. You agree to comply 
            with all local laws regarding online conduct and acceptable content.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">4. Bookings and Payments</h2>
          <p className="mb-4">
            When you book a venue through our platform, you agree to pay all fees and applicable taxes associated with 
            your booking. Cancellation policies vary by venue and are specified on the venue listing.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">5. Host Obligations</h2>
          <p className="mb-4">
            If you are a host, you agree to provide accurate information about your venue, maintain your venue in a safe 
            and clean condition, and honor all bookings made through our platform.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">6. Intellectual Property</h2>
          <p className="mb-4">
            The HouseGoing platform and its original content, features, and functionality are owned by HouseGoing and 
            are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">7. Termination</h2>
          <p className="mb-4">
            We may terminate or suspend your account and bar access to the platform immediately, without prior notice or 
            liability, for any reason whatsoever, including without limitation if you breach the Terms.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">8. Changes to Terms</h2>
          <p className="mb-4">
            We reserve the right to modify or replace these Terms at any time. It is your responsibility to check our 
            Terms periodically for changes.
          </p>

          <h2 className="text-xl font-semibold mt-6 mb-3">9. Contact Us</h2>
          <p className="mb-4">
            If you have any questions about these Terms, please contact <NAME_EMAIL>.
          </p>

          <p className="mt-8 text-sm text-gray-500">
            Last updated: June 15, 2023
          </p>
        </div>
      </div>
    </div>
  );
}
