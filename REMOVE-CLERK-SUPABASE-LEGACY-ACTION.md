# Clerk-Supabase Integration Cleanup

**Remove these files:**
- `src/lib/clerk-supabase.ts`
- `src/lib/auth-manager.ts`
- `src/utils/clerk-supabase-template.ts`

**Update all imports and usages to:**
- Use `createClerkSupabaseClient(session)` from `src/lib/clerk-supabase-official.ts` for authenticated Supabase access.
- Use `supabase` from `src/lib/supabase-client.ts` for unauthenticated/global access.

**Reference:**
- https://supabase.com/docs/guides/auth/third-party/clerk
- https://clerk.com/docs/integrations/databases/supabase
