/**
 * Message Notifications Component
 *
 * Displays unread message count and provides quick access to messages
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../providers/AuthProvider';
import { getUnreadMessageCount, getConversations } from '../../api/userAccount';
import { getSupabaseClient } from '../../services/api';
import { MessageCircle, Bell, User, Clock } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';

interface MessageNotificationsProps {
  className?: string;
  showDropdown?: boolean;
}

interface Conversation {
  otherUser: {
    id: string;
    name: string;
    email: string;
  };
  booking?: {
    id: string;
    venue_id: string;
    start_date: string;
    end_date: string;
    status: string;
  };
  lastMessage: {
    id: string;
    content: string;
    created_at: string;
    sender_id: string;
  };
  unreadCount: number;
}

export default function MessageNotifications({
  className = '',
  showDropdown = true
}: MessageNotificationsProps) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [unreadCount, setUnreadCount] = useState(0);
  const [recentConversations, setRecentConversations] = useState<Conversation[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [loading, setLoading] = useState(false);

  // Load unread count and recent conversations
  useEffect(() => {
    if (!user) return;

    const loadNotifications = async () => {
      try {
        setLoading(true);

        // Get unread count
        const count = await getUnreadMessageCount(user.id);
        setUnreadCount(count);

        // Get recent conversations with unread messages
        const conversations = await getConversations(user.id);
        const unreadConversations = conversations
          .filter(conv => conv.unreadCount > 0)
          .slice(0, 5); // Show max 5 recent unread conversations

        setRecentConversations(unreadConversations);
      } catch (error) {
        console.error('Error loading message notifications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadNotifications();
  }, [user]);

  // Real-time updates for new messages
  useEffect(() => {
    if (!user) return;

    const supabase = getSupabaseClient();
    const subscription = supabase
      .channel('message-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `receiver_id.eq.${user.id}`
        },
        () => {
          // Refresh notifications when new message received
          setUnreadCount(prev => prev + 1);
          // Could also update recent conversations here
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `receiver_id.eq.${user.id}`
        },
        (payload) => {
          // Update count when messages are marked as read
          if (payload.new.read && !payload.old.read) {
            setUnreadCount(prev => Math.max(0, prev - 1));
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user]);

  if (!user) return null;

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell */}
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative p-2 text-gray-600 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-lg"
      >
        <MessageCircle className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notifications Dropdown */}
      {showDropdown && showNotifications && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Messages</h3>
              <button
                onClick={() => navigate('/messages')}
                className="text-purple-600 hover:text-purple-700 text-sm font-medium"
              >
                View All
              </button>
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                {unreadCount} unread message{unreadCount !== 1 ? 's' : ''}
              </p>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500 mx-auto mb-2"></div>
                Loading...
              </div>
            ) : recentConversations.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No unread messages</p>
              </div>
            ) : (
              recentConversations.map((conversation, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setShowNotifications(false);
                    navigate('/messages', {
                      state: {
                        selectedConversation: conversation
                      }
                    });
                  }}
                  className="w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="w-5 h-5 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {conversation.otherUser.name}
                        </p>
                        <div className="flex items-center space-x-2">
                          <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                            {conversation.unreadCount}
                          </span>
                          <div className="flex items-center text-xs text-gray-500">
                            <Clock className="w-3 h-3 mr-1" />
                            {formatDistanceToNow(new Date(conversation.lastMessage.created_at), { addSuffix: true })}
                          </div>
                        </div>
                      </div>

                      {conversation.booking && (
                        <p className="text-xs text-gray-500 mb-1">
                          Booking #{conversation.booking.id.slice(0, 8)}
                        </p>
                      )}

                      <p className="text-sm text-gray-600 truncate">
                        {conversation.lastMessage.sender_id === user.id ? 'You: ' : ''}
                        {conversation.lastMessage.content}
                      </p>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>

          {/* Footer */}
          {recentConversations.length > 0 && (
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => {
                  setShowNotifications(false);
                  navigate('/messages');
                }}
                className="w-full text-center text-purple-600 hover:text-purple-700 text-sm font-medium"
              >
                View All Messages
              </button>
            </div>
          )}
        </div>
      )}

      {/* Click outside to close */}
      {showNotifications && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowNotifications(false)}
        />
      )}
    </div>
  );
}

// Simple badge component for showing unread count only
export function MessageBadge({ className = '' }: { className?: string }) {
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (!user) return;

    const loadUnreadCount = async () => {
      try {
        const count = await getUnreadMessageCount(user.id);
        setUnreadCount(count);
      } catch (error) {
        console.error('Error loading unread count:', error);
      }
    };

    loadUnreadCount();

    // Real-time updates
    const supabase = getSupabaseClient();
    const subscription = supabase
      .channel('message-badge')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `receiver_id.eq.${user.id}`
        },
        () => setUnreadCount(prev => prev + 1)
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `receiver_id.eq.${user.id}`
        },
        (payload) => {
          if (payload.new.read && !payload.old.read) {
            setUnreadCount(prev => Math.max(0, prev - 1));
          }
        }
      )
      .subscribe();

    return () => subscription.unsubscribe();
  }, [user]);

  if (!user || unreadCount === 0) return null;

  return (
    <span className={`bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ${className}`}>
      {unreadCount > 9 ? '9+' : unreadCount}
    </span>
  );
}
