/**
 * Unified API route for AI chat assistants
 */

import { generateHostAssistantResponse, generateSalesAssistantResponse } from '../../lib/ai/huggingface';

// Mock venue data for recommendations
const mockVenues = [
  {
    name: 'Harbour View Terrace',
    description: 'Stunning waterfront venue with panoramic harbour views and modern amenities.',
    capacity: 80,
    price: 250,
    features: ['Waterfront location', 'Outdoor deck', 'Sound system', 'Bar area'],
    location: 'Sydney Harbour',
    byo: true
  },
  {
    name: 'The Garden Pavilion',
    description: 'Elegant garden venue surrounded by lush greenery and flowering plants.',
    capacity: 120,
    price: 320,
    features: ['Garden setting', 'Marquee option', 'Catering kitchen', 'Parking'],
    location: 'Eastern Suburbs',
    byo: true
  },
  {
    name: 'Urban Loft Space',
    description: 'Industrial chic warehouse conversion with exposed brick and high ceilings.',
    capacity: 150,
    price: 380,
    features: ['Industrial design', 'DJ booth', 'Lighting rig', 'Rooftop access'],
    location: 'Inner West',
    byo: false
  },
  {
    name: 'Beachside Cabana',
    description: 'Relaxed beach venue with direct sand access and coastal vibes.',
    capacity: 60,
    price: 200,
    features: ['Beach access', 'BBQ facilities', 'Outdoor shower', 'Covered area'],
    location: 'Northern Beaches',
    byo: true
  },
  {
    name: 'Heritage Hall',
    description: 'Stunning restored heritage building with classic architecture and modern facilities.',
    capacity: 200,
    price: 450,
    features: ['Historic building', 'Grand staircase', 'Chandeliers', 'Dance floor'],
    location: 'CBD',
    byo: false
  },
  {
    name: 'Poolside Paradise',
    description: 'Modern venue with a large swimming pool and entertainment deck.',
    capacity: 80,
    price: 300,
    features: ['Swimming pool', 'Cabanas', 'Outdoor speakers', 'BBQ area'],
    location: 'Eastern Suburbs',
    byo: true
  },
  {
    name: 'Skyline Penthouse',
    description: 'Luxurious penthouse venue with 360-degree city views and upscale amenities.',
    capacity: 50,
    price: 500,
    features: ['City views', 'Private elevator', 'Premium sound system', 'Full bar'],
    location: 'CBD',
    byo: false
  },
  {
    name: 'Rustic Barn',
    description: 'Charming countryside barn with rustic features and open space.',
    capacity: 100,
    price: 280,
    features: ['Rustic setting', 'Fire pit', 'String lights', 'Country views'],
    location: 'Western Sydney',
    byo: true
  }
];

/**
 * Extract venue search criteria from user message and conversation history
 * @param {string} message - Current user message
 * @param {Array} history - Conversation history
 * @returns {Object} - Extracted search criteria
 */
function extractVenueSearchCriteria(message, history) {
  // Initialize criteria object
  const criteria = {
    location: null,
    eventType: null,
    guestCount: null,
    budget: null,
    timeframe: null,
    features: [],
    byo: null
  };

  // Combine current message with history for context
  const fullContext = history.map(msg => msg.content).join(' ') + ' ' + message;
  const lowerContext = fullContext.toLowerCase();

  // Extract location
  const locationPatterns = [
    { regex: /\b(sydney|melbourne|brisbane|perth|adelaide|gold coast|canberra)\b/gi, group: 1 },
    { regex: /\b(eastern suburbs|inner west|northern beaches|western sydney|cbd|harbour)\b/gi, group: 1 },
    { regex: /\bin\s+([\w\s]+?)\b(?:,|\.|for|with|and|\s|$)/i, group: 1 },
    { regex: /\blocation\s*(?:in|:)?\s*([\w\s]+?)\b(?:,|\.|for|with|and|\s|$)/i, group: 1 }
  ];

  for (const pattern of locationPatterns) {
    const match = lowerContext.match(pattern.regex);
    if (match) {
      criteria.location = match[pattern.group];
      break;
    }
  }

  // Extract event type
  const eventTypePatterns = [
    { regex: /\b(wedding|birthday|party|corporate|business|meeting|conference|dinner|celebration)\b/gi, group: 1 },
    { regex: /\bfor\s+(?:a|my)?\s*([\w\s]+?)\b(?:party|event|celebration|gathering)/i, group: 1 }
  ];

  for (const pattern of eventTypePatterns) {
    const match = lowerContext.match(pattern.regex);
    if (match) {
      criteria.eventType = match[pattern.group];
      break;
    }
  }

  // Extract guest count
  const guestCountPattern = /(\d+)\s*(?:people|guests|persons|capacity)/i;
  const guestMatch = lowerContext.match(guestCountPattern);
  if (guestMatch) {
    criteria.guestCount = parseInt(guestMatch[1]);
  }

  // Extract budget
  const budgetPattern = /(\d+)\s*(?:dollars|aud|\$)/i;
  const budgetMatch = lowerContext.match(budgetPattern);
  if (budgetMatch) {
    criteria.budget = parseInt(budgetMatch[1]);
  }

  // Extract timeframe
  const timeframePatterns = [
    { regex: /\b(january|february|march|april|may|june|july|august|september|october|november|december)\b/gi, group: 1 },
    { regex: /\b(next week|next month|this weekend|next weekend)\b/gi, group: 1 },
    { regex: /\b(\d{1,2})(?:st|nd|rd|th)?\s+(?:of\s+)?(january|february|march|april|may|june|july|august|september|october|november|december)\b/gi, group: 0 }
  ];

  for (const pattern of timeframePatterns) {
    const match = lowerContext.match(pattern.regex);
    if (match) {
      criteria.timeframe = match[pattern.group];
      break;
    }
  }

  // Extract features
  const featureKeywords = [
    'outdoor', 'indoor', 'pool', 'beach', 'waterfront', 'garden', 'rooftop', 'view',
    'catering', 'kitchen', 'bar', 'sound system', 'dj', 'dance floor', 'parking'
  ];

  featureKeywords.forEach(feature => {
    if (lowerContext.includes(feature.toLowerCase())) {
      criteria.features.push(feature);
    }
  });

  // Extract BYO preference
  if (lowerContext.includes('byo') || lowerContext.includes('bring your own') ||
      lowerContext.includes('bring my own') || lowerContext.includes('bring our own')) {
    criteria.byo = true;
  }

  return criteria;
}

/**
 * Filter venues based on extracted criteria
 * @param {Object} criteria - Search criteria
 * @returns {Array} - Filtered venues
 */
function getFilteredVenues(criteria) {
  let filteredVenues = [...mockVenues];

  // Filter by location if provided
  if (criteria.location) {
    const locationTerms = criteria.location.toLowerCase().split(/\s+/);
    filteredVenues = filteredVenues.filter(venue => {
      const venueLoc = venue.location.toLowerCase();
      return locationTerms.some(term => venueLoc.includes(term));
    });
  }

  // Filter by capacity if provided
  if (criteria.guestCount) {
    filteredVenues = filteredVenues.filter(venue => venue.capacity >= criteria.guestCount);
  }

  // Filter by budget if provided
  if (criteria.budget) {
    filteredVenues = filteredVenues.filter(venue => venue.price <= criteria.budget);
  }

  // Filter by BYO if specified
  if (criteria.byo === true) {
    filteredVenues = filteredVenues.filter(venue => venue.byo === true);
  }

  // Filter by features if provided
  if (criteria.features.length > 0) {
    filteredVenues = filteredVenues.filter(venue => {
      const venueFeatures = venue.features.map(f => f.toLowerCase());
      return criteria.features.some(feature =>
        venueFeatures.some(f => f.includes(feature.toLowerCase()))
      );
    });
  }

  // If we have too few results, relax the criteria
  if (filteredVenues.length < 3 && criteria.features.length > 0) {
    // Try without feature filtering
    let relaxedVenues = [...mockVenues];

    if (criteria.location) {
      const locationTerms = criteria.location.toLowerCase().split(/\s+/);
      relaxedVenues = relaxedVenues.filter(venue => {
        const venueLoc = venue.location.toLowerCase();
        return locationTerms.some(term => venueLoc.includes(term));
      });
    }

    if (criteria.guestCount) {
      relaxedVenues = relaxedVenues.filter(venue => venue.capacity >= criteria.guestCount);
    }

    if (relaxedVenues.length >= 3) {
      filteredVenues = relaxedVenues;
    }
  }

  // If still no results, return a diverse selection
  if (filteredVenues.length === 0) {
    // Return a diverse selection of venues
    filteredVenues = mockVenues.slice(0, 5);
  }

  // Limit to 5 venues
  return filteredVenues.slice(0, 5);
}

// Store sessions in memory (in production, use a database)
const sessions = new Map();

// Fallback responses for different agent types (used when AI API fails)
const fallbackResponses = {
  sales: {
    default: "Thanks for your message! I'm Homie, your HouseGoing assistant. I can help you find the perfect venue for your event, answer questions about pricing, booking, or venue features. What kind of event are you planning?"
  },
  host: {
    default: "G'day! I'm Homie, your HouseGoing Host Assistant. I can help with venue optimization, Party Score calculations, pricing strategies, and more. How can I help you make the most of your venue today?"
  },
  booking: {
    default: "Hello! I'm Homie, your HouseGoing Booking Assistant. I can help with checking availability, processing payments, and managing your bookings. How can I assist you today?"
  },
  support: {
    default: "Welcome to HouseGoing Support. I'm Homie, and I'm here to help with any issues or questions you have about our platform. How can I assist you today?"
  }
};

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, agentType, sessionId } = req.body;

    // Validate message and agent type
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    if (!agentType || !['sales', 'host', 'booking', 'support'].includes(agentType)) {
      return res.status(400).json({ error: 'Valid agent type is required' });
    }

    // Get or create session
    let session;
    const sessionKey = `${agentType}:${sessionId || 'new'}`;

    if (sessionId && sessions.has(sessionKey)) {
      session = sessions.get(sessionKey);
    } else {
      // Create a new session
      session = {
        id: Math.random().toString(36).substring(2, 15),
        agentType,
        history: []
      };
      sessions.set(`${agentType}:${session.id}`, session);
    }

    // Add message to history
    session.history.push({ role: 'user', content: message });

    // Generate a response using the appropriate AI model based on agent type
    let response = '';
    try {
      if (agentType === 'host') {
        // Get context from request if available
        const context = req.body.context || 'general';
        response = await generateHostAssistantResponse(message, context);
      } else if (agentType === 'sales') {
        // Extract venue search criteria from message
        const criteria = extractVenueSearchCriteria(message, session.history);

        // Get filtered venues based on criteria
        const filteredVenues = getFilteredVenues(criteria);

        // Generate response with venue recommendations
        response = await generateSalesAssistantResponse(message, filteredVenues);
      } else {
        // For other agent types, use fallback responses for now
        response = fallbackResponses[agentType].default;
      }
    } catch (error) {
      console.error(`Error generating AI response for ${agentType}:`, error);
      // Use fallback response if AI generation fails
      response = fallbackResponses[agentType].default;
    }

    // Add response to history
    session.history.push({ role: 'assistant', content: response });

    // Return response
    return res.status(200).json({
      response,
      sessionId: session.id
    });
  } catch (error) {
    console.error('Error processing AI chat message:', error);
    return res.status(500).json({
      error: 'An error occurred while processing your message',
      details: error.message
    });
  }
}
