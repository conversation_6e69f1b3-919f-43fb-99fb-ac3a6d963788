import React, { useState, useEffect } from 'react';
import { MessageCircle, Send, Clock, CheckCircle2 } from 'lucide-react';
import { getMessages, sendMessage, markMessageAsRead, Message } from '../../api/userAccount';

interface MessagesSectionProps {
  userId: string;
}

export default function MessagesSection({ userId }: MessagesSectionProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);

  useEffect(() => {
    fetchMessages();
  }, [userId]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const data = await getMessages(userId);
      setMessages(data);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (receiverId: string) => {
    if (!newMessage.trim()) return;

    try {
      setSending(true);
      await sendMessage(userId, receiverId, newMessage.trim());
      setNewMessage('');
      await fetchMessages(); // Refresh messages
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleMarkAsRead = async (messageId: string) => {
    try {
      await markMessageAsRead(messageId);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId ? { ...msg, read: true } : msg
        )
      );
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  // Group messages by conversation
  const conversations = messages.reduce((acc, message) => {
    const otherUserId = message.sender_id === userId ? message.receiver_id : message.sender_id;
    if (!acc[otherUserId]) {
      acc[otherUserId] = [];
    }
    acc[otherUserId].push(message);
    return acc;
  }, {} as Record<string, Message[]>);

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (Object.keys(conversations).length === 0) {
    return (
      <div className="text-center py-12">
        <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2 font-inter">No messages yet</h3>
        <p className="text-gray-600 font-inter">Your conversations with hosts will appear here</p>
      </div>
    );
  }

  return (
    <div className="flex h-96 border border-gray-200 rounded-lg overflow-hidden">
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200 bg-gray-50">
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 font-inter">Conversations</h3>
        </div>
        <div className="overflow-y-auto h-full">
          {Object.entries(conversations).map(([otherUserId, msgs]) => {
            const latestMessage = msgs[0];
            const unreadCount = msgs.filter(m => !m.read && m.receiver_id === userId).length;
            const otherUserName = latestMessage.sender_id === userId 
              ? latestMessage.receiver_name 
              : latestMessage.sender_name;

            return (
              <button
                key={otherUserId}
                onClick={() => setSelectedConversation(otherUserId)}
                className={`w-full p-4 text-left border-b border-gray-200 hover:bg-white transition-colors ${
                  selectedConversation === otherUserId ? 'bg-white border-l-4 border-l-purple-600' : ''
                }`}
              >
                <div className="flex justify-between items-start mb-1">
                  <span className="font-medium text-gray-900 font-inter">{otherUserName}</span>
                  {unreadCount > 0 && (
                    <span className="bg-purple-600 text-white text-xs rounded-full px-2 py-1 font-inter">
                      {unreadCount}
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 truncate font-inter">{latestMessage.content}</p>
                <p className="text-xs text-gray-500 mt-1 font-inter">
                  {new Date(latestMessage.created_at).toLocaleDateString()}
                </p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Message Thread */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {conversations[selectedConversation]
                .slice()
                .reverse()
                .map((message) => {
                  const isOwn = message.sender_id === userId;
                  return (
                    <div
                      key={message.id}
                      className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
                      onClick={() => !message.read && !isOwn && handleMarkAsRead(message.id)}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          isOwn
                            ? 'bg-purple-600 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <p className="font-inter">{message.content}</p>
                        <div className={`flex items-center gap-1 mt-1 text-xs ${
                          isOwn ? 'text-purple-200' : 'text-gray-500'
                        }`}>
                          <Clock className="w-3 h-3" />
                          <span className="font-inter">
                            {new Date(message.created_at).toLocaleTimeString([], { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </span>
                          {isOwn && message.read && (
                            <CheckCircle2 className="w-3 h-3 ml-1" />
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>

            {/* Message Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-inter"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(selectedConversation);
                    }
                  }}
                />
                <button
                  onClick={() => handleSendMessage(selectedConversation)}
                  disabled={!newMessage.trim() || sending}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-md transition-colors font-inter font-medium"
                >
                  {sending ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 font-inter">Select a conversation to start messaging</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
