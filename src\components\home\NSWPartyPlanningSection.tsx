import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { MapPin, Calendar, ArrowRight, CheckCircle, Clock, AlertTriangle, Search, X, Info, Home } from 'lucide-react';
import { getAddressSuggestions, geocodeAddress } from '../nsw-curfew/AddressSuggestionService';
import { getCurfewInfo, formatCurfewTime } from '../../lib/nsw-party-planning/curfew-api';

export default function NSWPartyPlanningSection() {
  const [address, setAddress] = useState('');
  const [addressInput, setAddressInput] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const [selectedCoordinates, setSelectedCoordinates] = useState<{lat: number, lng: number} | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // Function to fetch address suggestions
  const fetchAddressSuggestions = async (query: string) => {
    if (query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const results = await getAddressSuggestions(query);
      setSuggestions(results);
      setShowSuggestions(results.length > 0);
    } catch (err) {
      console.error('Error fetching address suggestions:', err);
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle address input change with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (addressInput) {
        fetchAddressSuggestions(addressInput);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [addressInput]);

  // Handle clicking outside suggestions to close them
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchInputRef.current && !searchInputRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearch = async () => {
    if (!address) {
      setError('Please enter an address');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // If we have stored coordinates from a suggestion, use those
      if (selectedCoordinates) {
        const { lat, lng } = selectedCoordinates;

        // Get curfew info
        const curfewInfo = await getCurfewInfo({
          address,
          date
        });

        setResult({
          address,
          coordinates: { lat, lng },
          council: curfewInfo.lga_name,
          zoning: curfewInfo.zone_code ? `${curfewInfo.zone_code} - ${curfewInfo.zone_name}` : curfewInfo.zone_name,
          curfew: `${formatCurfewTime(curfewInfo.curfew_start)} to ${formatCurfewTime(curfewInfo.curfew_end)}`,
          curfewInfo
        });
      } else {
        // Otherwise geocode the address
        const geocodeResult = await geocodeAddress(addressInput);
        setAddress(geocodeResult.displayName);

        // Get curfew info
        const curfewInfo = await getCurfewInfo({
          address: geocodeResult.displayName,
          date
        });

        setResult({
          address: geocodeResult.displayName,
          coordinates: { lat: geocodeResult.lat, lng: geocodeResult.lng },
          council: curfewInfo.lga_name,
          zoning: curfewInfo.zone_code ? `${curfewInfo.zone_code} - ${curfewInfo.zone_name}` : curfewInfo.zone_name,
          curfew: `${formatCurfewTime(curfewInfo.curfew_start)} to ${formatCurfewTime(curfewInfo.curfew_end)}`,
          curfewInfo
        });
      }
    } catch (err: any) {
      console.error('Search error:', err);
      setError(err.message || 'Failed to search address. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (result) {
      // If we already have results, navigate to the full tool with the address and date
      navigate(`/nsw-party-planning?address=${encodeURIComponent(address)}&date=${encodeURIComponent(date)}`);
    } else {
      // Otherwise perform the search first
      handleSearch();
    }
  };

  return (
    <section className="pt-8 pb-12 px-4 sm:px-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">NSW Party Planning & Noise Guide</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find out noise curfew times and party-friendly zones for any address in NSW
          </p>
        </div>

        <div className="bg-purple-50 rounded-2xl p-6 shadow-sm mt-8 max-w-4xl mx-auto">
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <MapPin className="h-5 w-5 text-purple-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-800">Check Your Address</h3>
            </div>
            <p className="text-gray-600 ml-7">
              Enter any NSW address to get instant party planning advice
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <div className="relative" ref={searchInputRef}>
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <MapPin className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="address"
                      placeholder="Enter NSW address"
                      className="pl-10 block w-full rounded-md border border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                      value={addressInput}
                      onChange={(e) => setAddressInput(e.target.value)}
                      required
                    />
                    {addressInput && (
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => {
                          setAddressInput('');
                          setAddress('');
                          setSuggestions([]);
                          setShowSuggestions(false);
                        }}
                      >
                        <X className="h-4 w-4 text-gray-400" />
                      </button>
                    )}

                    {/* Address suggestions dropdown */}
                    {showSuggestions && (
                      <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-auto">
                        {suggestions.length > 0 ? (
                          suggestions.map((suggestion, index) => (
                            <div
                              key={index}
                              className="px-4 py-2 hover:bg-purple-50 cursor-pointer flex items-start"
                              onClick={() => {
                                setAddressInput(suggestion.displayName);
                                setAddress(suggestion.displayName);
                                setSelectedCoordinates({
                                  lat: suggestion.lat,
                                  lng: suggestion.lon
                                });
                                setShowSuggestions(false);
                              }}
                            >
                              <MapPin className="h-4 w-4 text-gray-400 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{suggestion.displayName}</span>
                            </div>
                          ))
                        ) : (
                          <div className="px-4 py-2 text-sm text-gray-500">No suggestions found</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                    Event Date
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Calendar className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="date"
                      id="date"
                      className="pl-10 block w-full rounded-md border border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                      value={date}
                      onChange={(e) => setDate(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Error message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md flex items-start">
                  <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                  <span>{error}</span>
                </div>
              )}

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Searching...
                    </>
                  ) : (
                    'Search'
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Results section */}
          {result && (
            <div className="mt-6 bg-white rounded-xl p-6 shadow-sm border border-purple-100">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Party Planning Results</h3>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <MapPin className="h-5 w-5 text-purple-600 mr-2" />
                      <h4 className="font-medium text-gray-700">Location</h4>
                    </div>
                    <p className="text-sm text-gray-600 ml-7">{result.address}</p>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Home className="h-5 w-5 text-purple-600 mr-2" />
                      <h4 className="font-medium text-gray-700">Property Type</h4>
                    </div>
                    <p className="text-sm text-gray-600 ml-7">{result.curfewInfo.property_type}</p>
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Clock className="h-5 w-5 text-amber-500 mr-2" />
                    <h4 className="font-medium text-gray-700">Noise Curfew</h4>
                  </div>
                  <p className="text-sm text-gray-600 ml-7">
                    Quiet hours from <span className="font-medium">{formatCurfewTime(result.curfewInfo.curfew_start)}</span> to <span className="font-medium">{formatCurfewTime(result.curfewInfo.curfew_end)}</span>
                  </p>
                </div>

                {result.curfewInfo.recommendations && result.curfewInfo.recommendations.length > 0 && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Info className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="font-medium text-gray-700">Recommendations</h4>
                    </div>
                    <ul className="text-sm text-gray-600 ml-7 space-y-1">
                      {result.curfewInfo.recommendations.slice(0, 2).map((rec: any, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2">•</span>
                          <span>{rec.recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex justify-end">
                  <Link
                    to={`/nsw-party-planning?address=${encodeURIComponent(result.address)}&date=${encodeURIComponent(date)}`}
                    className="inline-flex items-center px-4 py-2 border border-purple-300 text-sm font-medium rounded-md shadow-sm text-purple-700 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    View detailed results <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-wrap gap-4 mt-4 justify-start">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-sm text-gray-700">Party-friendly</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-amber-500 mr-2" />
              <span className="text-sm text-gray-700">Time restrictions</span>
            </div>
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-sm text-gray-700">Not recommended</span>
            </div>
            <div className="ml-auto">
              <Link
                to="/nsw-party-planning"
                className="inline-flex items-center text-sm text-purple-600 hover:text-purple-800 transition-colors"
              >
                View full guide <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
