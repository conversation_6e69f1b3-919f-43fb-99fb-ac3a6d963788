# Simple fix script for HouseGoing indexing issues

Write-Host "Running simple indexing fix for HouseGoing..."

# Create the verification file
$content = @"
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HouseGoing Indexing Verification</title>
  <meta name="description" content="This page helps verify that HouseGoing is properly indexed by Google and other search engines.">
  <link rel="canonical" href="https://housegoing.com.au/indexing-verification.html">
</head>
<body>
  <h1>HouseGoing Indexing Verification</h1>
  <p>This page confirms that HouseGoing should be indexed by Google and other search engines.</p>
  <p>HouseGoing is Australia's premier venue booking platform for party and event spaces.</p>
  <p>Visit our site at <a href="https://housegoing.com.au">https://housegoing.com.au</a></p>
</body>
</html>
"@

# Make sure the public directory exists
if (-not (Test-Path "public")) {
    New-Item -ItemType Directory -Path "public"
}

# Save the verification file
Set-Content -Path "public/indexing-verification.html" -Value $content
Write-Host "Created indexing verification file: public/indexing-verification.html"

# Now run manual update of sitemap (simplified)
Write-Host "Please manually deploy these changes to Netlify."
Write-Host "Then go to Google Search Console and request indexing for:"
Write-Host "- https://housegoing.com.au/"
Write-Host "- https://housegoing.com.au/about-housegoing"
Write-Host "- https://housegoing.com.au/housegoing-brand.html"
Write-Host "- https://housegoing.com.au/housegoing-faq.html"
Write-Host "- https://housegoing.com.au/indexing-verification.html"
