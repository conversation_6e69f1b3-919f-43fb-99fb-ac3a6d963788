CREATE TABLE property_type_rules (
  id SERIAL PRIMARY KEY,
  property_type VARCHAR(50) NOT NULL UNIQUE,
  weekday_curfew_start TIME,
  weekday_curfew_end TIME,
  weekend_curfew_start TIME,
  weekend_curfew_end TIME,
  bass_music_restriction_start TIME,
  bass_music_restriction_end TIME,
  outdoor_cutoff TIME,
  special_considerations TEXT
);

INSERT INTO property_type_rules (
  property_type, weekday_curfew_start, weekday_curfew_end, 
  weekend_curfew_start, weekend_curfew_end, bass_music_restriction_start,
  bass_music_restriction_end, outdoor_cutoff, special_considerations
) VALUES
  ('Apartment/Unit', '22:00:00', '07:00:00', '22:00:00', '08:00:00', '21:00:00', '08:00:00', '21:00:00', 'Strata bylaws may impose stricter rules'),
  ('House', '22:00:00', '07:00:00', '22:00:00', '08:00:00', NULL, NULL, '22:00:00', 'More tolerance for outdoor gatherings'),
  ('Townhouse', '22:00:00', '07:00:00', '22:00:00', '08:00:00', '21:30:00', '08:00:00', '21:30:00', 'Shared walls require additional consideration'),
  ('Commercial', '23:00:00', '07:00:00', '00:00:00', '08:00:00', NULL, NULL, '23:00:00', 'Must adhere to specific development approval conditions');
