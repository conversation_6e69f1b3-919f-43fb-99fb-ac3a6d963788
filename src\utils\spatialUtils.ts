import * as turf from '@turf/turf';
import { FeatureCollection, Point, Polygon } from 'geojson';

export async function loadGeoJSON(url: string): Promise<FeatureCollection<Polygon>> {
  try {
    // Handle both relative and absolute URLs
    const isAbsoluteUrl = url.startsWith('http://') || url.startsWith('https://');

    // For local file paths in development, convert to public URL
    const fetchUrl = isAbsoluteUrl ? url : url.replace(/^\.\/src\/data\//, '/data/');

    console.log(`Loading GeoJSON from: ${fetchUrl}`);
    const response = await fetch(fetchUrl);

    if (!response.ok) {
      throw new Error(`Failed to load GeoJSON from ${fetchUrl}: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error loading GeoJSON from ${url}:`, error);
    // Return empty feature collection as fallback
    return { type: 'FeatureCollection', features: [] };
  }
}

export function findZoneForPoint(point: Point, zonesGeoJSON: FeatureCollection<Polygon>): string | null {
  if (!zonesGeoJSON.features || zonesGeoJSON.features.length === 0) {
    return null;
  }

  // First try exact point-in-polygon match
  for (const feature of zonesGeoJSON.features) {
    if (feature && turf.booleanPointInPolygon(point, feature as any)) {
      return feature.properties?.ZONE_CODE || null;
    }
  }

  // Fallback to nearest polygon within 500m for edge cases
  try {
    const buffer = turf.buffer(turf.point(point.coordinates), 0.005); // ~500m
    for (const feature of zonesGeoJSON.features) {
      if (feature && turf.booleanIntersects(buffer, feature as any)) {
        return feature.properties?.ZONE_CODE || null;
      }
    }
  } catch (error) {
    console.error('Error in buffer intersection check:', error);
  }

  return null;
}

// Note: We use type assertions to handle GeoJSON compatibility issues

export function findLGAForPoint(point: Point, lgaGeoJSON: FeatureCollection<Polygon>): string | null {
  if (!lgaGeoJSON.features || lgaGeoJSON.features.length === 0) {
    return null;
  }

  // First try exact point-in-polygon match
  for (const feature of lgaGeoJSON.features) {
    if (feature && turf.booleanPointInPolygon(point, feature as any)) {
      return feature.properties?.LGA_NAME || null;
    }
  }

  // Fallback to buffered search (500m radius)
  try {
    const buffer = turf.buffer(turf.point(point.coordinates), 0.005);
    for (const feature of lgaGeoJSON.features) {
      if (feature && turf.booleanIntersects(buffer, feature as any)) {
        return feature.properties?.LGA_NAME || null;
      }
    }
  } catch (error) {
    console.error('Error in buffer intersection check:', error);
  }

  // Final fallback to address pattern matching
  return null;
}
