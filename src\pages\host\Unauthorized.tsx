import React from 'react';
import { Link } from 'react-router-dom';
import RegisterAsHost from '../../components/host/RegisterAsHost';

export default function Unauthorized() {
  return (
    <div className="min-h-screen pt-32 px-4 flex flex-col items-center">
      <div className="max-w-md mx-auto text-center">
        <h1 className="text-3xl font-bold mb-4">Property Owner Registration Required</h1>
        <p className="text-gray-600 mb-8">
          You need to register as a property owner to access this area.
          Please complete the property owner registration process first.
        </p>
        <div className="flex flex-col space-y-4">
          <RegisterAsHost />
          <Link to="/host/portal" className="btn-secondary mt-4">
            Go to Owner Portal
          </Link>
        </div>
      </div>
    </div>
  );
}
