/**
 * redirect-chain-analyzer.js
 * 
 * This script identifies and helps fix redirect chain issues found in Google Search Console.
 * It analyzes the netlify.toml file and checks for redirect chains and unnecessary redirects.
 * 
 * Usage:
 * 1. Run with Node.js: node redirect-chain-analyzer.js
 * 2. Review the generated report and implement suggested fixes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { parse as parseToml } from 'toml';
import axios from 'axios';

// Get current file directory with ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const NETLIFY_CONFIG = path.join(__dirname, '..', 'netlify.toml');
const REPORT_FILE = path.join(__dirname, '..', 'redirect-chain-report.md');
const SITE_URL = 'https://housegoing.com.au';

// Load the netlify.toml file
console.log(`Analyzing redirects in netlify.toml...`);

const netlifyConfig = fs.readFileSync(NETLIFY_CONFIG, 'utf-8');
const config = parseToml(netlifyConfig);

let report = `# Redirect Chain Analysis Report\n\n`;
report += `Generated on: ${new Date().toISOString()}\n\n`;
report += `## Current Redirects Configuration\n\n`;

// Extract all redirects
const redirects = config.redirects || [];

// Map of redirects: from -> to
const redirectMap = new Map();

// Add all redirects to the map
redirects.forEach(redirect => {
  redirectMap.set(redirect.from, {
    to: redirect.to,
    status: redirect.status,
    force: redirect.force,
  });
});

// Analyze for chains
report += `Found ${redirects.length} redirects in the configuration.\n\n`;
report += `### Potential Redirect Chains\n\n`;

const chains = [];
const visited = new Set();

// Find chains
redirectMap.forEach((value, from) => {
  if (visited.has(from)) return;
  
  const chain = [from];
  visited.add(from);
  
  let current = from;
  let next = redirectMap.get(current)?.to;
  
  while (next && redirectMap.has(next) && !chain.includes(next)) {
    chain.push(next);
    visited.add(next);
    current = next;
    next = redirectMap.get(current)?.to;
  }
  
  if (next) {
    chain.push(next);
  }
  
  if (chain.length > 2) {
    chains.push(chain);
  }
});

if (chains.length === 0) {
  report += `No redirect chains found. Good job!\n\n`;
} else {
  chains.forEach((chain, index) => {
    report += `#### Chain ${index + 1}\n\n`;
    report += `- Original: \`${chain[0]}\`\n`;
    
    for (let i = 1; i < chain.length - 1; i++) {
      report += `- Intermediate: \`${chain[i]}\`\n`;
    }
    
    report += `- Final destination: \`${chain[chain.length - 1]}\`\n\n`;
    
    report += `**Suggested Fix:**\n`;
    report += `\`\`\`toml\n[[redirects]]\n  from = "${chain[0]}"\n  to = "${chain[chain.length - 1]}"\n  status = 301\n  force = true\n\`\`\`\n\n`;
  });
}

// Check for redirects to the index.html catch-all
report += `### Redirects to index.html\n\n`;

const indexRedirects = redirects.filter(r => r.to === '/index.html' || r.to === 'index.html');

if (indexRedirects.length === 0) {
  report += `No redirects to index.html found.\n\n`;
} else {
  report += `Found ${indexRedirects.length} redirects to index.html. This is generally fine for SPA routing but make sure they're actually intended:\n\n`;
  
  indexRedirects.forEach(redirect => {
    report += `- \`${redirect.from}\` → \`${redirect.to}\` (status: ${redirect.status})\n`;
  });
  
  report += `\nRecommendation: Ensure these redirects are intended for client-side routing and not accidental.\n\n`;
}

// Generate optimized redirect configuration
report += `## Suggested Optimized Configuration\n\n`;
report += `Replace your current redirects with this optimized configuration:\n\n`;
report += `\`\`\`toml\n`;

// Keep track of processed redirects to avoid duplicates
const processedRedirects = new Set();

// Add all non-chain redirects
redirectMap.forEach((value, from) => {
  if (processedRedirects.has(from)) return;
  
  const isInChain = chains.some(chain => chain.includes(from) && chain[0] !== from);
  
  if (!isInChain) {
    report += `[[redirects]]\n  from = "${from}"\n  to = "${value.to}"\n  status = ${value.status || 301}\n`;
    if (value.force) report += `  force = ${value.force}\n`;
    report += `\n`;
  }
  
  processedRedirects.add(from);
});

// Add optimized chains
chains.forEach(chain => {
  report += `[[redirects]]\n  from = "${chain[0]}"\n  to = "${chain[chain.length - 1]}"\n  status = 301\n  force = true\n\n`;
  
  // Mark all redirects in this chain as processed
  chain.forEach(path => processedRedirects.add(path));
});

report += `\`\`\`\n\n`;

// Add next steps
report += `## Next Steps\n\n`;
report += `1. Review the suggested optimized configuration\n`;
report += `2. Update your \`netlify.toml\` file with the optimized redirects\n`;
report += `3. Deploy the changes\n`;
report += `4. Test the fixed redirects\n`;
report += `5. Submit affected URLs for reindexing in Google Search Console\n`;

fs.writeFileSync(REPORT_FILE, report);

console.log(`
✅ Redirect analysis complete!
   - ${chains.length} redirect chains identified
   - ${indexRedirects.length} redirects to index.html found
   - Full report saved to ${REPORT_FILE}
`);
