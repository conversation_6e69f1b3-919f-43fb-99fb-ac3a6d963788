import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { useSupabase } from '../providers/SupabaseProvider';
import { isPreregisteredHost } from '../data/preregisteredHosts';

interface HostProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export default function HostProtectedRoute({ children, redirectTo = '/host/login' }: HostProtectedRouteProps) {
  // Check if we're in development mode first
  const isDevelopmentMode = typeof window !== 'undefined' &&
                           (window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('local'));

  // In development mode, bypass all authentication checks immediately
  if (isDevelopmentMode) {
    console.log('🔧 HostProtectedRoute: Development mode - bypassing all authentication checks');
    return <>{children}</>;
  }

  // Production mode: use normal authentication flow
  const { isAuthenticated, isHost, userProfile, isLoading: supabaseLoading } = useSupabase();
  const [isLoading, setIsLoading] = useState(true);
  const [userEmail, setUserEmail] = useState<string>('');

  useEffect(() => {
    if (!supabaseLoading) {
      // Get user email from profile
      if (userProfile?.email) {
        setUserEmail(userProfile.email);
      }

      // Check if user is a pre-registered host
      if (userProfile?.email && !isHost) {
        const isPreregistered = isPreregisteredHost(userProfile.email);
        if (isPreregistered) {
          console.log('User is a pre-registered host:', userProfile.email);
        }
      }

      setIsLoading(false);
    }
  }, [supabaseLoading, userProfile, isHost]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="pt-32 flex justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
          <p className="text-gray-600">Loading authentication...</p>
        </div>
      </div>
    );
  }

  // Check if user is signed in
  if (!isAuthenticated) {
    // Store the current path to redirect back after login
    try {
      localStorage.setItem('auth_redirect_to', window.location.pathname);
      localStorage.setItem('auth_user_type', 'host');
    } catch (e) {
      console.error('Error storing redirect path:', e);
    }
    return <Navigate to="/host/login?userType=host" />;
  }

  // Check if user is a host
  if (!isHost) {
    return <Navigate to="/host/unauthorized" />;
  }

  return <>{children}</>;
}
