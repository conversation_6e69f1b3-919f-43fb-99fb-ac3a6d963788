-- Ultra Simple Security Fix for HouseGoing
-- This script only removes the dangerous functions causing security warnings
-- Run this in the Supabase SQL Editor

-- Remove all dangerous functions that allow arbitrary SQL execution
DROP FUNCTION IF EXISTS public.exec_sql(text);
DROP FUNCTION IF EXISTS public.pg_query(text);
DROP FUNCTION IF EXISTS public.execute_sql(text);
DROP FUNCTION IF EXISTS public.exec_sql_query(text);

-- Remove any other variations of these dangerous functions
DROP FUNCTION IF EXISTS public.exec_sql;
DROP FUNCTION IF EXISTS public.pg_query;
DROP FUNCTION IF EXISTS public.execute_sql;

-- Success message
SELECT 'Dangerous SQL execution functions removed successfully!' as result;
