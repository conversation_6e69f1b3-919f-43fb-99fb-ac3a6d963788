import { getSupabaseClient } from '../../lib/supabase-client';
import { sendPropertySubmissionEmail } from '../../services/emailService';
import { notifyAdmin } from './adminApi';

export interface PropertySubmission {
  // Basic Info
  name: string
  address: string
  type: string
  location: [number, number]
  phoneNumber?: string

  // Venue Details
  description: string
  size: number
  functionRooms: number
  eventSpaces: number
  maxGuests: number
  price: number

  // Amenities & Features
  amenities?: string[]
  parkingDetails?: string
  transportDetails?: string
  nearbyLandmarks?: string
  byoPolicy?: string

  // House Rules
  noiseRestrictions?: string
  endTime?: {
    weekday: string
    weekend: string
  }
  decorationsPolicy?: string
  smokingPolicy?: string
  petPolicy?: string
  additionalFees?: string
  curfew?: {
    weekday: {
      start: string
      end: string
    }
    weekend: {
      start: string
      end: string
    }
  }
  bassRestriction?: {
    weekday: string
    weekend: string
  }
  outdoorCutoff?: {
    weekday: string
    weekend: string
  }

  // Photos
  images?: string[]

  // Bank Details
  bankDetails: {
    accountName: string
    bsb: string
    accountNumber: string
    bankName: string
  }

  // System Fields
  ownerId: string
  status: 'pending' | 'approved' | 'rejected'
}

export interface SubmissionResult {
  success: boolean
  data?: any
  error?: string
}

export const submitProperty = async (property: PropertySubmission): Promise<SubmissionResult> => {
  try {
    // Get the centralized Supabase client
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('property_submissions')
      .insert({
        ...property,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Supabase error submitting property:', error)
      return {
        success: false,
        error: error.message || 'Failed to submit property to database'
      }
    }

    // Send email notification to admin
    try {
      await sendPropertySubmissionEmail({
        name: property.name,
        id: data.id,
        address: property.address,
        type: property.type,
        ownerId: property.ownerId,
        phoneNumber: property.phoneNumber || 'Not provided'
      })
      console.log('Email notification sent successfully')
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError)
    }

    // Also notify through the admin notification system
    try {
      await notifyAdmin({
        propertyId: data.id,
        propertyName: property.name,
        propertyAddress: property.address,
        propertyType: property.type,
        ownerDetails: {
          ownerId: property.ownerId,
          phoneNumber: property.phoneNumber || 'Not provided',
          bankName: property.bankDetails.bankName
        },
        venueDetails: {
          size: property.size,
          maxGuests: property.maxGuests,
          price: property.price,
          amenities: property.amenities || []
        },
        timestamp: new Date().toISOString(),
        type: 'new_property',
        read: false
      })
    } catch (notifyError) {
      console.error('Failed to send admin notification:', notifyError)
    }

    return {
      success: true,
      data
    }
  } catch (error: any) {
    console.error('Error submitting property:', error)
    return {
      success: false,
      error: error.message || 'An unexpected error occurred'
    }
  }
}
