import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

export function HostCTA() {
  return (
    <div className="bg-primary-light py-16">
      <div className="container mx-auto text-center px-4">
        <Badge className="mb-4 bg-primary/20 text-primary">Venue Owners</Badge>
        <h2 className="text-2xl font-semibold mb-6">Have a Space to Share?</h2>
        <p className="text-lg mb-8 text-gray-600 max-w-2xl mx-auto">
          Join our community of hosts and start earning from your venue.
          We'll help you reach party planners looking for spaces just like yours.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg" className="btn-gradient px-8">
            <Link to="/host/portal">Owner Portal</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}