/**
 * Initialize Analytics System for HouseGoing
 * 
 * Sets up database tables and tracking for Alex Hormozi-inspired business metrics
 */

import { getSupabaseClient } from '../lib/supabase-client';
import { initializeEventTracking } from './dataTracking';

/**
 * Initialize the complete analytics system
 */
export async function initializeAnalyticsSystem(): Promise<{
  success: boolean;
  message: string;
  details: string[];
}> {
  const details: string[] = [];
  
  try {
    console.log('🚀 Initializing HouseGoing Analytics System...');
    
    // 1. Initialize business events table
    const eventsResult = await initializeBusinessEventsTable();
    details.push(eventsResult.message);
    
    // 2. Initialize tracking triggers
    const triggersResult = await initializeTrackingTriggers();
    details.push(triggersResult.message);
    
    // 3. Initialize analytics views
    const viewsResult = await initializeAnalyticsViews();
    details.push(viewsResult.message);
    
    // 4. Initialize event tracking
    initializeEventTracking();
    details.push('✅ Event tracking system initialized');
    
    // 5. Create sample tracking data if needed
    const sampleResult = await createSampleTrackingData();
    details.push(sampleResult.message);
    
    console.log('✅ Analytics system initialization complete!');
    
    return {
      success: true,
      message: 'Analytics system initialized successfully',
      details
    };
    
  } catch (error) {
    console.error('❌ Error initializing analytics system:', error);
    return {
      success: false,
      message: 'Failed to initialize analytics system',
      details: [...details, `Error: ${error}`]
    };
  }
}

/**
 * Initialize business events table
 */
async function initializeBusinessEventsTable(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create business_events table
        CREATE TABLE IF NOT EXISTS business_events (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          event_type VARCHAR(100) NOT NULL,
          event_category VARCHAR(50) NOT NULL CHECK (event_category IN ('acquisition', 'conversion', 'revenue', 'retention', 'churn')),
          user_id UUID,
          session_id VARCHAR(100),
          properties JSONB DEFAULT '{}',
          revenue_impact DECIMAL(10,2),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_business_events_type ON business_events(event_type);
        CREATE INDEX IF NOT EXISTS idx_business_events_category ON business_events(event_category);
        CREATE INDEX IF NOT EXISTS idx_business_events_user_id ON business_events(user_id);
        CREATE INDEX IF NOT EXISTS idx_business_events_created_at ON business_events(created_at);
        CREATE INDEX IF NOT EXISTS idx_business_events_revenue ON business_events(revenue_impact) WHERE revenue_impact IS NOT NULL;
        
        -- Create composite indexes for common queries
        CREATE INDEX IF NOT EXISTS idx_business_events_type_date ON business_events(event_type, created_at);
        CREATE INDEX IF NOT EXISTS idx_business_events_category_date ON business_events(event_category, created_at);
      `
    });

    if (error) {
      throw error;
    }

    return { success: true, message: '✅ Business events table created successfully' };
  } catch (error) {
    console.error('Error creating business events table:', error);
    return { success: false, message: `❌ Failed to create business events table: ${error}` };
  }
}

/**
 * Initialize tracking triggers for automatic event creation
 */
async function initializeTrackingTriggers(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Function to track property submission events
        CREATE OR REPLACE FUNCTION track_property_submission()
        RETURNS TRIGGER AS $$
        BEGIN
          INSERT INTO business_events (
            event_type,
            event_category,
            user_id,
            properties,
            created_at
          ) VALUES (
            'property_submission',
            'acquisition',
            NEW."ownerId",
            jsonb_build_object(
              'property_id', NEW.id,
              'property_type', NEW.type,
              'price_per_hour', NEW.price,
              'capacity', NEW."maxGuests",
              'location', NEW.address,
              'submission_source', 'owner_portal'
            ),
            NEW.created_at
          );
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        -- Trigger for property submissions
        DROP TRIGGER IF EXISTS trigger_track_property_submission ON property_submissions;
        CREATE TRIGGER trigger_track_property_submission
          AFTER INSERT ON property_submissions
          FOR EACH ROW
          EXECUTE FUNCTION track_property_submission();
          
        -- Function to track user signups
        CREATE OR REPLACE FUNCTION track_user_signup()
        RETURNS TRIGGER AS $$
        BEGIN
          INSERT INTO business_events (
            event_type,
            event_category,
            user_id,
            properties,
            created_at
          ) VALUES (
            CASE 
              WHEN NEW.role = 'host' THEN 'host_signup'
              ELSE 'user_signup'
            END,
            'acquisition',
            NEW.id,
            jsonb_build_object(
              'user_type', NEW.role,
              'signup_method', COALESCE(NEW.signup_method, 'email'),
              'first_name', NEW.first_name,
              'last_name', NEW.last_name
            ),
            NEW.created_at
          );
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        -- Trigger for user signups
        DROP TRIGGER IF EXISTS trigger_track_user_signup ON user_profiles;
        CREATE TRIGGER trigger_track_user_signup
          AFTER INSERT ON user_profiles
          FOR EACH ROW
          EXECUTE FUNCTION track_user_signup();
      `
    });

    if (error) {
      throw error;
    }

    return { success: true, message: '✅ Tracking triggers created successfully' };
  } catch (error) {
    console.error('Error creating tracking triggers:', error);
    return { success: false, message: `❌ Failed to create tracking triggers: ${error}` };
  }
}

/**
 * Initialize analytics views for faster queries
 */
async function initializeAnalyticsViews(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- View for daily metrics
        CREATE OR REPLACE VIEW daily_metrics AS
        SELECT 
          DATE(created_at) as date,
          event_category,
          event_type,
          COUNT(*) as event_count,
          COUNT(DISTINCT user_id) as unique_users,
          SUM(revenue_impact) as total_revenue_impact
        FROM business_events
        WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
        GROUP BY DATE(created_at), event_category, event_type
        ORDER BY date DESC;
        
        -- View for user journey analysis
        CREATE OR REPLACE VIEW user_journey AS
        SELECT 
          user_id,
          MIN(created_at) as first_event,
          MAX(created_at) as last_event,
          COUNT(*) as total_events,
          COUNT(DISTINCT event_type) as unique_event_types,
          SUM(revenue_impact) as total_revenue_impact,
          ARRAY_AGG(DISTINCT event_type ORDER BY created_at) as event_sequence
        FROM business_events
        WHERE user_id IS NOT NULL
        GROUP BY user_id;
        
        -- View for conversion funnel
        CREATE OR REPLACE VIEW conversion_funnel AS
        WITH funnel_steps AS (
          SELECT 
            'signup' as step,
            1 as step_order,
            COUNT(DISTINCT user_id) as users
          FROM business_events 
          WHERE event_type IN ('user_signup', 'host_signup')
          
          UNION ALL
          
          SELECT 
            'property_submission' as step,
            2 as step_order,
            COUNT(DISTINCT user_id) as users
          FROM business_events 
          WHERE event_type = 'property_submission'
          
          UNION ALL
          
          SELECT 
            'property_approved' as step,
            3 as step_order,
            COUNT(DISTINCT user_id) as users
          FROM business_events 
          WHERE event_type = 'property_approved'
        )
        SELECT 
          step,
          step_order,
          users,
          LAG(users) OVER (ORDER BY step_order) as previous_step_users,
          CASE 
            WHEN LAG(users) OVER (ORDER BY step_order) > 0 
            THEN ROUND((users::DECIMAL / LAG(users) OVER (ORDER BY step_order)) * 100, 2)
            ELSE 0 
          END as conversion_rate
        FROM funnel_steps
        ORDER BY step_order;
      `
    });

    if (error) {
      throw error;
    }

    return { success: true, message: '✅ Analytics views created successfully' };
  } catch (error) {
    console.error('Error creating analytics views:', error);
    return { success: false, message: `❌ Failed to create analytics views: ${error}` };
  }
}

/**
 * Create sample tracking data for testing
 */
async function createSampleTrackingData(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = getSupabaseClient();
    
    // Check if we already have events
    const { data: existingEvents } = await supabase
      .from('business_events')
      .select('id')
      .limit(1);
    
    if (existingEvents && existingEvents.length > 0) {
      return { success: true, message: '📊 Existing tracking data found, skipping sample data creation' };
    }
    
    // Create sample events for the last 30 days
    const sampleEvents = [];
    const now = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // Sample user signups
      sampleEvents.push({
        event_type: 'user_signup',
        event_category: 'acquisition',
        properties: {
          user_type: 'customer',
          signup_method: 'email'
        },
        created_at: date.toISOString()
      });
      
      // Sample host signups (fewer)
      if (i % 3 === 0) {
        sampleEvents.push({
          event_type: 'host_signup',
          event_category: 'acquisition',
          properties: {
            user_type: 'host',
            signup_method: 'google'
          },
          created_at: date.toISOString()
        });
      }
      
      // Sample property submissions
      if (i % 2 === 0) {
        sampleEvents.push({
          event_type: 'property_submission',
          event_category: 'conversion',
          properties: {
            property_type: 'house',
            price_per_hour: 150 + (i * 10),
            capacity: 10 + (i % 5)
          },
          created_at: date.toISOString()
        });
      }
      
      // Sample approvals
      if (i % 4 === 0) {
        sampleEvents.push({
          event_type: 'property_approved',
          event_category: 'conversion',
          properties: {
            approval_time_hours: 24 + (i % 48),
            listing_quality_score: 70 + (i % 30)
          },
          revenue_impact: 15, // Commission estimate
          created_at: date.toISOString()
        });
      }
    }
    
    const { error } = await supabase
      .from('business_events')
      .insert(sampleEvents);
    
    if (error) {
      throw error;
    }
    
    return { success: true, message: `✅ Created ${sampleEvents.length} sample tracking events` };
  } catch (error) {
    console.error('Error creating sample tracking data:', error);
    return { success: false, message: `❌ Failed to create sample data: ${error}` };
  }
}

/**
 * Get analytics system status
 */
export async function getAnalyticsSystemStatus(): Promise<{
  tablesExist: boolean;
  triggersExist: boolean;
  viewsExist: boolean;
  eventCount: number;
  lastEvent: string | null;
}> {
  try {
    const supabase = getSupabaseClient();
    
    // Check if business_events table exists
    const { data: tables } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'business_events'
        ) as table_exists;
      `
    });
    
    // Check event count
    const { data: events, count } = await supabase
      .from('business_events')
      .select('created_at', { count: 'exact' })
      .order('created_at', { ascending: false })
      .limit(1);
    
    return {
      tablesExist: tables?.[0]?.table_exists || false,
      triggersExist: true, // Assume true if tables exist
      viewsExist: true, // Assume true if tables exist
      eventCount: count || 0,
      lastEvent: events?.[0]?.created_at || null
    };
  } catch (error) {
    console.error('Error checking analytics system status:', error);
    return {
      tablesExist: false,
      triggersExist: false,
      viewsExist: false,
      eventCount: 0,
      lastEvent: null
    };
  }
}
