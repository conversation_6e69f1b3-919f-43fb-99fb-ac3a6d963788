import React, { useState, useEffect } from 'react';
import { getSupabaseClient } from '../../services/api';
import { useAuth } from '../../providers/AuthProvider';
import { formatDistanceToNow } from 'date-fns';

interface Message {
  id: string;
  booking_id: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  is_read: boolean;
  created_at: string;
  sender?: {
    first_name?: string;
    last_name?: string;
    email?: string;
  };
}

interface BookingMessagesProps {
  bookingId: string;
}

export default function BookingMessages({ bookingId }: BookingMessagesProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const fetchMessages = async () => {
      if (!bookingId) return;

      try {
        setLoading(true);
        const supabase = getSupabaseClient();

        const { data, error } = await supabase
          .from('messages')
          .select(`
            *,
            sender:sender_id (
              first_name,
              last_name,
              email
            )
          `)
          .eq('booking_id', bookingId)
          .order('created_at', { ascending: true });

        if (error) throw error;

        setMessages(data || []);
      } catch (err) {
        console.error('Error fetching messages:', err);
        setError('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Set up real-time subscription for new messages
    const supabase = getSupabaseClient();
    const subscription = supabase
      .channel('messages-channel')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `booking_id=eq.${bookingId}`,
        },
        (payload) => {
          // Add the new message to the state
          const newMessage = payload.new as Message;
          setMessages((prevMessages) => [...prevMessages, newMessage]);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [bookingId]);

  if (loading) {
    return <div className="py-4 text-center text-gray-500">Loading messages...</div>;
  }

  if (error) {
    return <div className="py-4 text-center text-red-500">{error}</div>;
  }

  if (messages.length === 0) {
    return <div className="py-4 text-center text-gray-500">No messages yet</div>;
  }

  return (
    <div className="space-y-4 max-h-[300px] overflow-y-auto p-2">
      {messages.map((message) => {
        const isCurrentUser = user && message.sender_id === user.id;
        const senderName = message.sender?.first_name || message.sender?.last_name || message.sender?.email || 'Unknown';

        return (
          <div
            key={message.id}
            className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                isCurrentUser
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              {!isCurrentUser && (
                <div className="font-medium text-sm mb-1">
                  {senderName}
                </div>
              )}
              <p className="whitespace-pre-wrap break-words">{message.content}</p>
              <div className="text-xs opacity-75 mt-1">
                {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
