# HouseGoing Sitemap Submission Guide

This guide explains how to submit your sitemap to Google Search Console to improve your website's indexing and visibility in search results.

## About Your Sitemap

HouseGoing has a sitemap index located at:
```
https://housegoing.com.au/sitemap_index.xml
```

This sitemap index points to the main sitemap:
```
https://housegoing.com.au/sitemap_main.xml
```

This sitemap is automatically generated and includes:
- All static pages (homepage, find venues, how it works, etc.)
- Dynamic venue pages from your database
- Each URL includes metadata like last modification date, change frequency, and priority

## Submitting Your Sitemap to Google Search Console

### Step 1: Access Google Search Console

1. Go to [Google Search Console](https://search.google.com/search-console)
2. Sign in with the Google account that has access to your HouseGoing property

### Step 2: Select Your Property

- If you haven't added your site yet, click "Add Property"
- Choose "URL prefix" and enter `https://housegoing.com.au/`
- Follow the verification steps (you may need to upload an HTML file, add a meta tag, or use your Google Analytics connection)

### Step 3: Submit Your Sitemap

1. In the left sidebar, click on "Sitemaps"
2. In the "Add a new sitemap" field, enter `sitemap_index.xml`
3. Click "Submit"

Google will process your sitemap and begin crawling the URLs it contains. This process may take several days.

### Step 4: Monitor Indexing Status

- Return to the Sitemaps section regularly to check the status
- You'll see how many URLs were submitted and how many were indexed
- If there are issues, Google will provide warnings or errors to help you fix them

## Troubleshooting

If your sitemap submission has issues:

1. **Verify the sitemap index is accessible**: Visit `https://housegoing.com.au/sitemap_index.xml` directly to ensure it loads properly
2. **Verify the main sitemap is accessible**: Visit `https://housegoing.com.au/sitemap_main.xml` directly to ensure it loads properly
2. **Check for XML errors**: Ensure your sitemap follows the proper XML format
3. **Confirm URLs are accessible**: Make sure the URLs in your sitemap don't return 404 errors
4. **Review robots.txt**: Ensure your robots.txt file doesn't block the sitemap or important pages

## Updating Your Sitemap

Your sitemap is automatically updated when:
- You run the build process with `npm run build:prod`
- The script `scripts/generate-sitemap.js` is executed

To manually update your sitemap, run:
```bash
npm run generate-sitemap
```

## Additional Resources

- [Google's Official Sitemap Documentation](https://developers.google.com/search/docs/advanced/sitemaps/overview)
- [Sitemap XML Format Specification](https://www.sitemaps.org/protocol.html)
- [Google Search Console Help](https://support.google.com/webmasters/)
