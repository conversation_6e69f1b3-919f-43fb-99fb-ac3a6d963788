{"name": "housegoing-ai-agents", "version": "1.0.0", "description": "AI Agents for the HouseGoing platform", "main": "index.js", "type": "module", "scripts": {"host-agent": "cd host-acquisition && npm start", "host-agent:advanced": "cd host-acquisition && npm run advanced", "host-agent:api": "cd host-acquisition && npm run api", "sales-assistant": "cd sales-assistant && npm start", "sales-assistant:api": "cd sales-assistant && npm run api", "install-all": "npm install && cd host-acquisition && npm install && cd ../sales-assistant && npm install"}, "dependencies": {"dotenv": "^16.4.1"}}