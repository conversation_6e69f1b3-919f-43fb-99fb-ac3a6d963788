#!/bin/bash

# Function to display help
show_help() {
  echo "Usage: $0 [--clean] [--modify-ids] [--all] [--help]"
  echo
  echo "Options:"
  echo "  --clean         Run installation and clean databases"
  echo "  --modify-ids    Run installation and modify telemetry IDs"
  echo "  --all           Run installation, clean databases, and modify telemetry IDs"
  echo "  --help          Display this help message"
}

# Function to check for required dependencies
check_dependencies() {
  local missing_deps=()

  for cmd in sqlite3 curl jq; do
    if ! command -v $cmd &> /dev/null; then
      missing_deps+=($cmd)
    fi
  done

  if [ ${#missing_deps[@]} -ne 0 ]; then
    echo "[ERROR] The following required dependencies are not installed: ${missing_deps[*]}"
    echo "Please install them and try again."
    exit 1
  fi
}

# Function to make scripts executable
make_scripts_executable() {
  chmod +x scripts/*.sh
}

# Function to run database cleaning
clean_databases() {
  echo "Running database cleaning..."
  ./scripts/clean_code_db.sh
}

# Function to modify telemetry IDs
modify_telemetry_ids() {
  echo "Modifying telemetry IDs..."
  ./scripts/id_modifier.sh
}

# Parse command line arguments
clean=false
modify_ids=false

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --clean) clean=true ;;
    --modify-ids) modify_ids=true ;;
    --all) clean=true; modify_ids=true ;;
    --help) show_help; exit 0 ;;
    *) echo "[ERROR] Unknown parameter passed: $1"; show_help; exit 1 ;;
  esac
  shift
done

# Check for required dependencies
check_dependencies

# Make scripts executable
make_scripts_executable

# Run requested operations
if $clean; then
  clean_databases
fi

if $modify_ids; then
  modify_telemetry_ids
fi

echo "Installation completed successfully."
