import React, { useState } from 'react';
import { Search, MapPin, Calendar, Users, ChevronDown, ChevronUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import LocationSearch from './LocationSearch';
import DateRangePicker from './DateRangePicker';
import GuestPicker from './GuestPicker';

interface LocationData {
  lat: number;
  lng: number;
  displayName: string;
}

interface MobileSearchDropdownProps {
  className?: string;
}

export default function MobileSearchDropdown({ className = '' }: MobileSearchDropdownProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [location, setLocation] = useState<LocationData | null>(null);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [guests, setGuests] = useState(1);
  const navigate = useNavigate();

  const handleSearch = () => {
    const searchParams = new URLSearchParams();
    
    if (location) {
      searchParams.set('location', location.displayName);
      searchParams.set('lat', location.lat.toString());
      searchParams.set('lng', location.lng.toString());
    }
    
    if (startDate) searchParams.set('startDate', startDate);
    if (endDate) searchParams.set('endDate', endDate);
    if (guests > 1) searchParams.set('guests', guests.toString());

    console.log('🔍 MOBILE SEARCH: Navigating with params:', Object.fromEntries(searchParams));
    navigate(`/find-venues?${searchParams.toString()}`);
  };

  const getLocationDisplay = () => {
    if (location) return location.displayName;
    return 'Where?';
  };

  const getDateDisplay = () => {
    if (startDate && endDate) {
      const start = new Date(startDate).toLocaleDateString('en-AU', { day: 'numeric', month: 'short' });
      const end = new Date(endDate).toLocaleDateString('en-AU', { day: 'numeric', month: 'short' });
      return `${start} - ${end}`;
    }
    if (startDate) {
      return new Date(startDate).toLocaleDateString('en-AU', { day: 'numeric', month: 'short' });
    }
    return 'When?';
  };

  const getGuestDisplay = () => {
    return guests === 1 ? '1 guest' : `${guests} guests`;
  };

  return (
    <>
      <div className={`w-full ${className}`}>
        {/* Mobile-First Search Bar - Optimized for Touch */}
        <div
          className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden cursor-pointer transform transition-all duration-300 hover:shadow-2xl active:scale-[0.99]"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="p-5 min-h-[72px] flex items-center">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center space-x-4 flex-1">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                  <Search className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-base font-semibold text-gray-900 truncate">
                    {location ? location.displayName : 'Find your perfect venue'}
                  </div>
                  <div className="text-sm text-gray-600 truncate mt-0.5">
                    {startDate || endDate ? getDateDisplay() : 'Any date'} • {getGuestDisplay()}
                  </div>
                </div>
              </div>
              <div className="ml-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                {isExpanded ? (
                  <ChevronUp className="h-6 w-6 text-gray-500" />
                ) : (
                  <ChevronDown className="h-6 w-6 text-gray-500" />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Full-Screen Mobile Search Modal */}
      {isExpanded && (
        <div className="fixed inset-0 bg-white z-[9999] overflow-y-auto">
          <div className="min-h-screen">
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-4 flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900">Find Venues</h2>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-2 rounded-xl hover:bg-gray-100 active:bg-gray-200 touch-target transition-colors min-h-[48px] min-w-[48px] flex items-center justify-center"
              >
                <X className="h-6 w-6 text-gray-600" />
              </button>
            </div>

            {/* Search Form */}
            <div className="p-4 space-y-6">
              {/* Location */}
              <div className="space-y-3">
                <label className="flex items-center text-base font-semibold text-gray-800">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3">
                    <MapPin className="h-5 w-5 text-purple-600" />
                  </div>
                  Where do you want to party?
                </label>
                <div className="relative">
                  <LocationSearch
                    onLocationSelect={(locationData) => {
                      setLocation(locationData);
                    }}
                    placeholder="Type suburb or city name..."
                    className="w-full min-h-[52px] text-base px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-colors"
                  />
                </div>
              </div>

              {/* Date Range */}
              <div className="space-y-3">
                <label className="flex items-center text-base font-semibold text-gray-800">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3">
                    <Calendar className="h-5 w-5 text-purple-600" />
                  </div>
                  When's the party?
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="min-h-[52px] text-base px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-colors"
                    placeholder="Start date"
                  />
                  <input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="min-h-[52px] text-base px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-colors"
                    placeholder="End date"
                  />
                </div>
              </div>

              {/* Guests */}
              <div className="space-y-3">
                <label className="flex items-center text-base font-semibold text-gray-800">
                  <div className="p-2 bg-purple-100 rounded-lg mr-3">
                    <Users className="h-5 w-5 text-purple-600" />
                  </div>
                  How many guests?
                </label>
                <div className="flex items-center space-x-4 bg-white rounded-xl border-2 border-gray-200 p-4">
                  <button
                    type="button"
                    onClick={() => setGuests(Math.max(1, guests - 1))}
                    className="w-12 h-12 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-xl font-bold text-gray-600 transition-colors touch-target"
                  >
                    −
                  </button>
                  <div className="flex-1 text-center">
                    <div className="text-2xl font-bold text-gray-900">{guests}</div>
                    <div className="text-sm text-gray-500">{guests === 1 ? 'guest' : 'guests'}</div>
                  </div>
                  <button
                    type="button"
                    onClick={() => setGuests(guests + 1)}
                    className="w-12 h-12 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-xl font-bold text-gray-600 transition-colors touch-target"
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Search Button - Mobile Optimized */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleSearch();
                  setIsExpanded(false); // Close modal after search
                }}
                className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-4 px-6 rounded-2xl font-bold text-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] min-h-[56px] touch-target"
              >
                <Search className="h-6 w-6" />
                <span>Find Perfect Venues</span>
              </button>
            </div>
          </div>
        </div>
      )}

    </>
  );
}
